/**
 * Verification Script for Subscription Management Dashboard
 * Quick verification of subscription management functionality
 */

async function verifySubscriptionManagement() {
    console.log('🔍 Verifying Subscription Management Dashboard...');
    
    try {
        // Check if required dependencies are available
        if (typeof SubscriptionManagementDashboard === 'undefined') {
            throw new Error('SubscriptionManagementDashboard class not found');
        }
        
        if (typeof SubscriptionTierConfig === 'undefined') {
            throw new Error('SubscriptionTierConfig class not found');
        }
        
        if (typeof StripeManager === 'undefined') {
            throw new Error('StripeManager class not found');
        }
        
        console.log('✅ All required classes are available');
        
        // Initialize components
        const tierConfig = new SubscriptionTierConfig();
        const stripeManager = new StripeManager();
        
        // Initialize Stripe manager
        await stripeManager.initialize();
        console.log('✅ Stripe manager initialized');
        
        // Create subscription dashboard
        const dashboard = new SubscriptionManagementDashboard(
            stripeManager,
            tierConfig,
            null // No usage manager for basic verification
        );
        
        console.log('✅ Subscription dashboard created');
        
        // Get dashboard container
        const container = dashboard.getDashboardContainer();
        if (!container) {
            throw new Error('Dashboard container not created');
        }
        
        console.log('✅ Dashboard container created');
        
        // Verify dashboard sections
        const requiredSections = [
            'current-plan-section',
            'usage-tracking-section',
            'billing-history-section',
            'payment-methods-section',
            'subscription-modification-section'
        ];
        
        requiredSections.forEach(sectionId => {
            const section = container.querySelector(`#${sectionId}`);
            if (!section) {
                throw new Error(`Required section ${sectionId} not found`);
            }
            console.log(`✅ Section ${sectionId} verified`);
        });
        
        // Verify dashboard data
        if (!dashboard.currentUser) {
            throw new Error('Current user data not loaded');
        }
        console.log('✅ Current user data loaded');
        
        if (!dashboard.currentSubscription) {
            throw new Error('Current subscription data not loaded');
        }
        console.log('✅ Current subscription data loaded');
        
        if (!Array.isArray(dashboard.billingHistory)) {
            throw new Error('Billing history not loaded');
        }
        console.log('✅ Billing history loaded');
        
        if (!Array.isArray(dashboard.paymentMethods)) {
            throw new Error('Payment methods not loaded');
        }
        console.log('✅ Payment methods loaded');
        
        // Test dashboard rendering
        dashboard.renderDashboard();
        console.log('✅ Dashboard rendered successfully');
        
        // Verify rendered content
        const statusBadge = container.querySelector('#subscription-status-badge');
        if (!statusBadge || !statusBadge.textContent.trim()) {
            throw new Error('Subscription status badge not rendered');
        }
        console.log('✅ Subscription status badge rendered');
        
        const currentPlanContent = container.querySelector('#current-plan-content');
        if (!currentPlanContent || !currentPlanContent.innerHTML.trim()) {
            throw new Error('Current plan content not rendered');
        }
        console.log('✅ Current plan content rendered');
        
        const usageContent = container.querySelector('#usage-tracking-content');
        if (!usageContent || !usageContent.innerHTML.trim()) {
            throw new Error('Usage tracking content not rendered');
        }
        console.log('✅ Usage tracking content rendered');
        
        const billingContent = container.querySelector('#billing-history-content');
        if (!billingContent || !billingContent.innerHTML.trim()) {
            throw new Error('Billing history content not rendered');
        }
        console.log('✅ Billing history content rendered');
        
        const paymentContent = container.querySelector('#payment-methods-content');
        if (!paymentContent || !paymentContent.innerHTML.trim()) {
            throw new Error('Payment methods content not rendered');
        }
        console.log('✅ Payment methods content rendered');
        
        const modificationContent = container.querySelector('#subscription-modification-content');
        if (!modificationContent || !modificationContent.innerHTML.trim()) {
            throw new Error('Subscription modification content not rendered');
        }
        console.log('✅ Subscription modification content rendered');
        
        // Test interactive elements
        const buttons = container.querySelectorAll('.btn');
        if (buttons.length === 0) {
            throw new Error('No interactive buttons found');
        }
        console.log(`✅ Found ${buttons.length} interactive buttons`);
        
        // Test progress bars
        const progressBars = container.querySelectorAll('.progress-bar');
        if (progressBars.length === 0) {
            throw new Error('No progress bars found');
        }
        console.log(`✅ Found ${progressBars.length} progress bars`);
        
        // Test feature list
        const featureItems = container.querySelectorAll('.feature-item');
        if (featureItems.length === 0) {
            throw new Error('No feature items found');
        }
        console.log(`✅ Found ${featureItems.length} feature items`);
        
        // Test utility functions
        const formattedDate = dashboard.formatDate(new Date());
        if (!formattedDate || typeof formattedDate !== 'string') {
            throw new Error('Date formatting function not working');
        }
        console.log('✅ Date formatting function working');
        
        const totalPaid = dashboard.calculateTotalPaid();
        if (typeof totalPaid !== 'number' || totalPaid < 0) {
            throw new Error('Total paid calculation not working');
        }
        console.log('✅ Total paid calculation working');
        
        // Test notification system
        dashboard.showSuccess('Test success message');
        setTimeout(() => {
            const notification = document.querySelector('.notification.success');
            if (notification) {
                console.log('✅ Success notification system working');
            }
        }, 100);
        
        // Test loading system
        dashboard.showLoading('Test loading...');
        const loadingOverlay = document.getElementById('dashboard-loading');
        if (!loadingOverlay) {
            throw new Error('Loading overlay not created');
        }
        console.log('✅ Loading system working');
        
        dashboard.hideLoading();
        
        // Test localStorage integration
        const savedSubscription = localStorage.getItem('demo_subscription');
        if (!savedSubscription) {
            throw new Error('Subscription not saved to localStorage');
        }
        console.log('✅ localStorage integration working');
        
        // Test tier configuration integration
        const allTiers = tierConfig.getAllTiers();
        if (!Array.isArray(allTiers) || allTiers.length === 0) {
            throw new Error('Tier configuration not working');
        }
        console.log(`✅ Tier configuration working (${allTiers.length} tiers)`);
        
        // Test upgrade path calculation
        const upgradePath = tierConfig.getUpgradePath(dashboard.currentSubscription.tierId);
        if (!Array.isArray(upgradePath)) {
            throw new Error('Upgrade path calculation not working');
        }
        console.log(`✅ Upgrade path calculation working (${upgradePath.length} options)`);
        
        // Cleanup
        dashboard.cleanup();
        console.log('✅ Dashboard cleanup completed');
        
        console.log('\n🎉 Subscription Management Dashboard verification completed successfully!');
        
        return {
            success: true,
            message: 'All verification checks passed',
            dashboard: dashboard,
            container: container
        };
        
    } catch (error) {
        console.error('❌ Verification failed:', error.message);
        return {
            success: false,
            message: error.message,
            error: error
        };
    }
}

/**
 * Quick test of subscription management functionality
 */
async function quickTestSubscriptionManagement() {
    console.log('🚀 Running quick test of subscription management...');
    
    try {
        const result = await verifySubscriptionManagement();
        
        if (!result.success) {
            throw new Error(result.message);
        }
        
        const { dashboard, container } = result;
        
        // Mount dashboard to page for visual inspection
        const testContainer = document.createElement('div');
        testContainer.id = 'subscription-test-container';
        testContainer.style.cssText = `
            margin: 20px;
            padding: 20px;
            border: 2px solid #4facfe;
            border-radius: 10px;
            background: #f8f9fa;
        `;
        
        const header = document.createElement('h2');
        header.textContent = 'Subscription Management Dashboard Test';
        header.style.cssText = `
            color: #4facfe;
            margin-bottom: 20px;
            text-align: center;
        `;
        
        testContainer.appendChild(header);
        testContainer.appendChild(container);
        
        // Add test controls
        const controls = document.createElement('div');
        controls.style.cssText = `
            margin-top: 20px;
            text-align: center;
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        `;
        
        const refreshButton = document.createElement('button');
        refreshButton.textContent = '🔄 Refresh Dashboard';
        refreshButton.className = 'btn';
        refreshButton.onclick = () => dashboard.refreshDashboard();
        
        const upgradeButton = document.createElement('button');
        upgradeButton.textContent = '⬆️ Test Upgrade';
        upgradeButton.className = 'btn';
        upgradeButton.onclick = () => {
            const upgradePath = dashboard.tierConfig.getUpgradePath(dashboard.currentSubscription.tierId);
            if (upgradePath.length > 0) {
                dashboard.upgradePlan(upgradePath[0].id);
            } else {
                alert('No upgrade options available');
            }
        };
        
        const addPaymentButton = document.createElement('button');
        addPaymentButton.textContent = '💳 Test Add Payment';
        addPaymentButton.className = 'btn secondary';
        addPaymentButton.onclick = () => dashboard.addPaymentMethod();
        
        const cleanupButton = document.createElement('button');
        cleanupButton.textContent = '🧹 Cleanup Test';
        cleanupButton.className = 'btn danger';
        cleanupButton.onclick = () => {
            dashboard.cleanup();
            testContainer.remove();
        };
        
        controls.appendChild(refreshButton);
        controls.appendChild(upgradeButton);
        controls.appendChild(addPaymentButton);
        controls.appendChild(cleanupButton);
        
        testContainer.appendChild(controls);
        document.body.appendChild(testContainer);
        
        console.log('✅ Quick test completed - dashboard mounted to page');
        console.log('Use the test controls to interact with the dashboard');
        
        return {
            success: true,
            dashboard: dashboard,
            testContainer: testContainer
        };
        
    } catch (error) {
        console.error('❌ Quick test failed:', error);
        return {
            success: false,
            error: error
        };
    }
}

/**
 * Run comprehensive test suite
 */
async function runComprehensiveTest() {
    console.log('🧪 Running comprehensive subscription management test...');
    
    try {
        // Check if test class is available
        if (typeof SubscriptionManagementTest === 'undefined') {
            console.warn('⚠️ SubscriptionManagementTest class not found, running basic verification only');
            return await quickTestSubscriptionManagement();
        }
        
        // Run comprehensive test suite
        const testSuite = new SubscriptionManagementTest();
        await testSuite.runAllTests();
        
        console.log('✅ Comprehensive test completed');
        
        return {
            success: true,
            testSuite: testSuite
        };
        
    } catch (error) {
        console.error('❌ Comprehensive test failed:', error);
        return {
            success: false,
            error: error
        };
    }
}

// Export functions for use in web demo
if (typeof window !== 'undefined') {
    window.verifySubscriptionManagement = verifySubscriptionManagement;
    window.quickTestSubscriptionManagement = quickTestSubscriptionManagement;
    window.runComprehensiveTest = runComprehensiveTest;
}

// Auto-run verification if this script is loaded directly
if (typeof window !== 'undefined' && window.location.search.includes('verify-subscription')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(quickTestSubscriptionManagement, 1000);
    });
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        verifySubscriptionManagement,
        quickTestSubscriptionManagement,
        runComprehensiveTest
    };
}