<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced UI Test - SafeKeep Demo</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4facfe">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SafeKeep Demo">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Enhanced UI Framework -->
    <link rel="stylesheet" href="enhanced-ui-framework.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <div class="container">
        <header class="header" role="banner">
            <h1>Enhanced UI Test</h1>
            <p>Testing responsive design, dark/light theme, accessibility, and PWA features</p>
        </header>
        
        <main class="main-content" id="main-content" role="main">
            <!-- Theme Testing Section -->
            <section class="demo-section" aria-labelledby="theme-heading">
                <h2 id="theme-heading">Theme System</h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Theme Controls</h3>
                            </div>
                            <div class="card-body">
                                <p>The theme should automatically switch based on your system preference, or you can use the theme toggle button.</p>
                                <div class="alert alert-info">
                                    <strong>Info:</strong> Theme changes are announced to screen readers and persist across sessions.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Color Samples</h3>
                            </div>
                            <div class="card-body">
                                <div class="d-flex gap-md flex-wrap">
                                    <div class="badge badge-primary">Primary</div>
                                    <div class="badge badge-success">Success</div>
                                    <div class="badge badge-warning">Warning</div>
                                    <div class="badge badge-danger">Danger</div>
                                    <div class="badge badge-info">Info</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Responsive Design Testing -->
            <section class="demo-section" aria-labelledby="responsive-heading">
                <h2 id="responsive-heading">Responsive Design</h2>
                <div class="demo-grid">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="card-title">Mobile First</h3>
                            <p>This layout adapts to different screen sizes using CSS Grid and Flexbox.</p>
                            <button class="btn btn-primary btn-block">Full Width Button</button>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <h3 class="card-title">Breakpoints</h3>
                            <p>Current breakpoint is detected and announced to assistive technologies.</p>
                            <div class="d-flex gap-sm">
                                <button class="btn btn-sm btn-outline">Small</button>
                                <button class="btn btn-sm btn-secondary">Medium</button>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <h3 class="card-title">Touch Targets</h3>
                            <p>All interactive elements meet minimum 44px touch target requirements.</p>
                            <button class="btn btn-success">Touch Friendly</button>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Accessibility Testing -->
            <section class="demo-section" aria-labelledby="accessibility-heading">
                <h2 id="accessibility-heading">Accessibility Features</h2>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Form Accessibility</h3>
                            </div>
                            <div class="card-body">
                                <form>
                                    <div class="form-group">
                                        <label for="test-email" class="form-label">Email Address</label>
                                        <input type="email" id="test-email" class="form-control" required 
                                               aria-describedby="email-help">
                                        <div id="email-help" class="form-text">We'll never share your email.</div>
                                    </div>
                                    <div class="form-group">
                                        <label for="test-password" class="form-label">Password</label>
                                        <input type="password" id="test-password" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" required> I agree to the terms
                                        </label>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Submit Form</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Progress & Status</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>Upload Progress</label>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: 65%" 
                                             aria-valuenow="65" 
                                             aria-valuemin="0" 
                                             aria-valuemax="100"
                                             aria-label="Upload progress">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-info" onclick="updateProgress()">Update Progress</button>
                                    <button class="btn btn-warning" onclick="showAlert()">Show Alert</button>
                                </div>
                                <div id="status-area" role="status" aria-live="polite"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Button System Testing -->
            <section class="demo-section" aria-labelledby="buttons-heading">
                <h2 id="buttons-heading">Button System</h2>
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title">Button Variants</h3>
                        <div class="d-flex gap-md flex-wrap align-center">
                            <button class="btn btn-primary">Primary</button>
                            <button class="btn btn-secondary">Secondary</button>
                            <button class="btn btn-success">Success</button>
                            <button class="btn btn-warning">Warning</button>
                            <button class="btn btn-danger">Danger</button>
                            <button class="btn btn-outline">Outline</button>
                        </div>
                        
                        <h4>Button Sizes</h4>
                        <div class="d-flex gap-md flex-wrap align-center">
                            <button class="btn btn-primary btn-sm">Small</button>
                            <button class="btn btn-primary">Default</button>
                            <button class="btn btn-primary btn-lg">Large</button>
                        </div>
                        
                        <h4>Button States</h4>
                        <div class="d-flex gap-md flex-wrap align-center">
                            <button class="btn btn-primary" onclick="testLoading(this)">Test Loading</button>
                            <button class="btn btn-secondary" disabled>Disabled</button>
                            <button class="btn btn-info" onclick="testRipple(this)">Ripple Effect</button>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- PWA Features Testing -->
            <section class="demo-section" aria-labelledby="pwa-heading">
                <h2 id="pwa-heading">PWA Features</h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Installation</h3>
                            </div>
                            <div class="card-body">
                                <p>This app can be installed as a Progressive Web App on supported devices.</p>
                                <button class="btn btn-primary" onclick="testInstall()">Test Install Prompt</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Offline Support</h3>
                            </div>
                            <div class="card-body">
                                <p>The app works offline with cached content and background sync.</p>
                                <button class="btn btn-secondary" onclick="testOffline()">Simulate Offline</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Data Table Testing -->
            <section class="demo-section" aria-labelledby="table-heading">
                <h2 id="table-heading">Data Tables</h2>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Accessible Data Table</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" aria-label="Sample data table">
                                <caption>Sample backup sessions data</caption>
                                <thead>
                                    <tr>
                                        <th scope="col">Date</th>
                                        <th scope="col">Type</th>
                                        <th scope="col">Size</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-01-15</td>
                                        <td>Photos</td>
                                        <td>2.3 GB</td>
                                        <td><span class="badge badge-success">Complete</span></td>
                                        <td><button class="btn btn-sm btn-outline">View</button></td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-14</td>
                                        <td>Messages</td>
                                        <td>45 MB</td>
                                        <td><span class="badge badge-warning">Pending</span></td>
                                        <td><button class="btn btn-sm btn-outline">View</button></td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-13</td>
                                        <td>Contacts</td>
                                        <td>1.2 MB</td>
                                        <td><span class="badge badge-danger">Failed</span></td>
                                        <td><button class="btn btn-sm btn-outline">Retry</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
    
    <!-- Enhanced UI Components -->
    <script src="theme-manager.js"></script>
    <script src="responsive-layout-manager.js"></script>
    <script src="accessibility-manager.js"></script>
    <script src="pwa-manager.js"></script>
    <script src="enhanced-ui-integration.js"></script>
    
    <!-- Test Scripts -->
    <script>
        // Test functions for interactive elements
        function updateProgress() {
            const progressBar = document.querySelector('.progress-bar');
            const currentValue = parseInt(progressBar.getAttribute('aria-valuenow'));
            const newValue = Math.min(100, currentValue + 15);
            
            if (progressBar.updateProgress) {
                progressBar.updateProgress(newValue);
            } else {
                progressBar.style.width = newValue + '%';
                progressBar.setAttribute('aria-valuenow', newValue);
            }
            
            document.getElementById('status-area').textContent = `Progress updated to ${newValue}%`;
        }
        
        function showAlert() {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success';
            alertDiv.innerHTML = '<strong>Success!</strong> This is a test alert message.';
            
            const statusArea = document.getElementById('status-area');
            statusArea.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }
        
        function testLoading(button) {
            if (button.setLoading) {
                button.setLoading(true);
                setTimeout(() => {
                    button.setLoading(false);
                }, 2000);
            }
        }
        
        function testRipple(button) {
            // Ripple effect is automatically added by the enhanced UI system
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('Ripple effect triggered', 'polite');
            }
        }
        
        function testInstall() {
            if (window.pwaManager && window.pwaManager.canInstall()) {
                window.pwaManager.promptInstall();
            } else {
                alert('Install prompt not available. Try on a supported device/browser.');
            }
        }
        
        function testOffline() {
            // Simulate offline mode
            document.body.classList.toggle('offline-mode');
            
            if (window.accessibilityManager) {
                const isOffline = document.body.classList.contains('offline-mode');
                window.accessibilityManager.announce(
                    isOffline ? 'Offline mode simulated' : 'Online mode restored',
                    'assertive'
                );
            }
        }
        
        // Wait for enhanced UI to be ready
        window.addEventListener('enhanced-ui-ready', () => {
            console.log('Enhanced UI system is ready!');
            
            // Test announcements
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('Enhanced UI test page loaded', 'polite');
            }
        });
        
        // Test keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.altKey && e.key === 't') {
                e.preventDefault();
                if (window.themeManager) {
                    window.themeManager.toggleTheme();
                }
            }
        });
    </script>
</body>
</html>