/**
 * ServiceDetails Component
 * Expandable service details with features and information
 */

class ServiceDetails {
    /**
     * @param {Object} props - Component properties
     * @param {ServiceOption} props.service - Service configuration
     * @param {boolean} props.isExpanded - Whether details are expanded
     * @param {Function} props.onToggle - Callback when details are toggled
     * @param {boolean} props.showFeatures - Whether to show feature list
     */
    constructor(props) {
        this.props = props;
        this.element = null;
        this.contentElement = null;
        this.isAnimating = false;
        
        this.render();
        this.attachEventListeners();
    }

    /**
     * Render the service details component
     */
    render() {
        const { service, isExpanded } = this.props;
        
        this.element = window.ModularPricingUtils.createElement('div', {
            className: `service-details ${isExpanded ? 'expanded' : 'collapsed'}`,
            dataset: { serviceId: service.id }
        });

        // Create toggle button
        const toggleButton = window.ModularPricingUtils.createElement('button', {
            className: 'service-details-toggle',
            type: 'button',
            'aria-expanded': isExpanded.toString(),
            'aria-controls': `service-details-content-${service.id}`
        });

        const toggleIcon = window.ModularPricingUtils.createElement('span', {
            className: 'toggle-icon'
        }, isExpanded ? '▼' : '▶');

        const toggleText = window.ModularPricingUtils.createElement('span', {
            className: 'toggle-text'
        }, isExpanded ? 'Hide Details' : 'Show Details');

        toggleButton.appendChild(toggleIcon);
        toggleButton.appendChild(toggleText);

        // Create content container
        this.contentElement = window.ModularPricingUtils.createElement('div', {
            className: 'service-details-content',
            id: `service-details-content-${service.id}`,
            'aria-hidden': (!isExpanded).toString()
        });

        // Create features section
        const featuresSection = this.createFeaturesSection(service);
        
        // Create additional info section
        const infoSection = this.createInfoSection(service);

        this.contentElement.appendChild(featuresSection);
        this.contentElement.appendChild(infoSection);

        this.element.appendChild(toggleButton);
        this.element.appendChild(this.contentElement);

        // Set initial height for animation
        this.updateContentHeight();
    }

    /**
     * Create features section
     * @param {ServiceOption} service - Service configuration
     * @returns {HTMLElement} Features section element
     */
    createFeaturesSection(service) {
        const section = window.ModularPricingUtils.createElement('div', {
            className: 'features-section'
        });

        const title = window.ModularPricingUtils.createElement('h4', {
            className: 'features-title'
        }, 'What\'s Included');

        const featuresList = window.ModularPricingUtils.createElement('ul', {
            className: 'features-list'
        });

        service.features.forEach(feature => {
            const listItem = window.ModularPricingUtils.createElement('li', {
                className: 'feature-item'
            });

            const checkIcon = window.ModularPricingUtils.createElement('span', {
                className: 'feature-check'
            }, '✓');

            const featureText = window.ModularPricingUtils.createElement('span', {
                className: 'feature-text'
            }, feature);

            listItem.appendChild(checkIcon);
            listItem.appendChild(featureText);
            featuresList.appendChild(listItem);
        });

        section.appendChild(title);
        section.appendChild(featuresList);

        return section;
    }

    /**
     * Create additional info section
     * @param {ServiceOption} service - Service configuration
     * @returns {HTMLElement} Info section element
     */
    createInfoSection(service) {
        const section = window.ModularPricingUtils.createElement('div', {
            className: 'info-section'
        });

        // Service-specific information
        const infoData = this.getServiceInfo(service.id);
        
        if (infoData.length > 0) {
            const title = window.ModularPricingUtils.createElement('h4', {
                className: 'info-title'
            }, 'Additional Information');

            const infoList = window.ModularPricingUtils.createElement('div', {
                className: 'info-list'
            });

            infoData.forEach(info => {
                const infoItem = window.ModularPricingUtils.createElement('div', {
                    className: 'info-item'
                });

                const infoLabel = window.ModularPricingUtils.createElement('span', {
                    className: 'info-label'
                }, info.label);

                const infoValue = window.ModularPricingUtils.createElement('span', {
                    className: 'info-value'
                }, info.value);

                infoItem.appendChild(infoLabel);
                infoItem.appendChild(infoValue);
                infoList.appendChild(infoItem);
            });

            section.appendChild(title);
            section.appendChild(infoList);
        }

        return section;
    }

    /**
     * Get service-specific information
     * @param {string} serviceId - Service ID
     * @returns {Array} Array of info objects
     */
    getServiceInfo(serviceId) {
        const infoMap = {
            contacts: [
                { label: 'Storage Format', value: 'Encrypted JSON' },
                { label: 'Sync Frequency', value: 'Real-time' },
                { label: 'Backup Retention', value: 'Unlimited' },
                { label: 'Export Formats', value: 'CSV, vCard' }
            ],
            messages: [
                { label: 'Message Types', value: 'SMS, MMS, iMessage' },
                { label: 'Attachment Support', value: 'Photos, Videos, Files' },
                { label: 'Search Capability', value: 'Full-text search' },
                { label: 'Export Formats', value: 'PDF, HTML, JSON' }
            ],
            photos: [
                { label: 'Storage Quality', value: 'Original resolution' },
                { label: 'File Formats', value: 'JPEG, PNG, HEIC, RAW' },
                { label: 'Organization', value: 'Smart albums & tags' },
                { label: 'Sharing', value: 'Secure links & galleries' }
            ]
        };

        return infoMap[serviceId] || [];
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        const toggleButton = this.element.querySelector('.service-details-toggle');
        
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.handleToggle();
            });

            // Keyboard support
            toggleButton.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleToggle();
                }
            });
        }
    }

    /**
     * Handle toggle action
     */
    handleToggle() {
        if (this.isAnimating) return;

        const newExpandedState = !this.props.isExpanded;
        
        if (this.props.onToggle) {
            this.props.onToggle(this.props.service.id, newExpandedState);
        }
    }

    /**
     * Update content height for smooth animation
     */
    updateContentHeight() {
        if (!this.contentElement) return;

        if (this.props.isExpanded) {
            // Get natural height
            this.contentElement.style.height = 'auto';
            const height = this.contentElement.scrollHeight;
            this.contentElement.style.height = height + 'px';
        } else {
            this.contentElement.style.height = '0px';
        }
    }

    /**
     * Animate expansion/collapse
     * @param {boolean} expand - Whether to expand or collapse
     */
    animateToggle(expand) {
        if (this.isAnimating) return;

        this.isAnimating = true;
        
        const toggleButton = this.element.querySelector('.service-details-toggle');
        const toggleIcon = this.element.querySelector('.toggle-icon');
        const toggleText = this.element.querySelector('.toggle-text');

        if (expand) {
            this.element.classList.add('expanded');
            this.element.classList.remove('collapsed');
            this.contentElement.setAttribute('aria-hidden', 'false');
            toggleButton.setAttribute('aria-expanded', 'true');
            
            // Update button content
            toggleIcon.textContent = '▼';
            toggleText.textContent = 'Hide Details';
            
            // Animate height
            this.contentElement.style.height = '0px';
            requestAnimationFrame(() => {
                const height = this.contentElement.scrollHeight;
                this.contentElement.style.height = height + 'px';
            });
        } else {
            this.element.classList.add('collapsed');
            this.element.classList.remove('expanded');
            this.contentElement.setAttribute('aria-hidden', 'true');
            toggleButton.setAttribute('aria-expanded', 'false');
            
            // Update button content
            toggleIcon.textContent = '▶';
            toggleText.textContent = 'Show Details';
            
            // Animate height
            this.contentElement.style.height = '0px';
        }

        // Reset animation flag after transition
        setTimeout(() => {
            this.isAnimating = false;
            if (expand) {
                this.contentElement.style.height = 'auto';
            }
        }, 300);
    }

    /**
     * Update component props
     * @param {Object} newProps - New properties
     */
    updateProps(newProps) {
        const oldProps = this.props;
        this.props = { ...this.props, ...newProps };

        // Handle expansion state change
        if (oldProps.isExpanded !== this.props.isExpanded) {
            this.animateToggle(this.props.isExpanded);
        }
    }

    /**
     * Get the DOM element
     * @returns {HTMLElement} The component's DOM element
     */
    getElement() {
        return this.element;
    }

    /**
     * Destroy the component and clean up
     */
    destroy() {
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        this.element = null;
        this.contentElement = null;
    }
}

// Export component
if (typeof window !== 'undefined') {
    window.ServiceDetails = ServiceDetails;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ServiceDetails;
}