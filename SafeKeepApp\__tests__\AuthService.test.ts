import { supabase } from '../src/config/supabase';
import AuthService from '../src/services/AuthService';

jest.mock('../src/config/supabase');

const mockUpdate = jest.fn();
supabase.from = jest.fn(() => ({
  update: mockUpdate,
}));

mockUpdate.mockReturnValue({
  eq: jest.fn().mockReturnValue({
    then: jest.fn().mockImplementation((callback) => callback({ error: null })),
  }),
});

describe('AuthService', () => {
  describe('updateLastLoginTime', () => {
    it('should update the last login time for the user', async () => {
      const userId = 'test-user-id';
      await AuthService.updateLastLoginTime(userId);

      expect(supabase.from).toHaveBeenCalledWith('users');
      expect(mockUpdate).toHaveBeenCalledWith({
        last_login_at: expect.any(String),
      });
    });

    it('should handle errors gracefully', async () => {
      mockUpdate.mockReturnValueOnce({
        eq: jest.fn().mockReturnValue({
          then: jest.fn().mockImplementation((callback) => callback({ error: new Error('Update failed') })),
        }),
      });

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const userId = 'test-user-id';
      await AuthService.updateLastLoginTime(userId);

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to update last login time:', expect.any(Error));

      consoleErrorSpy.mockRestore();
    });
  });
});
