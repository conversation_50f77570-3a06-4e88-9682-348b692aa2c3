import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, TABLES } from '../config/supabase';
import AuthService from './AuthService';

export interface BackupPerformanceLog {
  id: string;
  sessionId: string;
  userId: string;
  timestamp: Date;
  eventType: 'session_start' | 'session_end' | 'data_type_start' | 'data_type_end' | 'item_processed' | 'error' | 'pause' | 'resume';
  dataType?: 'contacts' | 'messages' | 'photos';
  itemId?: string;
  duration?: number; // milliseconds
  itemSize?: number; // bytes
  throughput?: number; // items per second or bytes per second
  memoryUsage?: number; // bytes
  batteryLevel?: number; // percentage
  networkType?: string;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export interface PerformanceMetrics {
  sessionId: string;
  totalDuration: number;
  itemsProcessed: number;
  totalBytes: number;
  averageThroughput: number;
  errorCount: number;
  pauseCount: number;
  memoryPeakUsage: number;
  batteryConsumption: number;
  networkEfficiency: number;
}

export interface PerformanceReport {
  sessionMetrics: PerformanceMetrics;
  dataTypeBreakdown: {
    contacts: PerformanceMetrics;
    messages: PerformanceMetrics;
    photos: PerformanceMetrics;
  };
  timelineEvents: BackupPerformanceLog[];
  recommendations: string[];
}

class BackupPerformanceLogger {
  private logs: BackupPerformanceLog[] = [];
  private currentSessionId?: string;
  private sessionStartTime?: Date;
  private dataTypeStartTimes = new Map<string, Date>();
  private readonly STORAGE_KEY = 'backup_performance_logs';
  private readonly MAX_LOGS_STORED = 1000;

  // Start logging for a backup session
  async startSession(sessionId: string): Promise<void> {
    this.currentSessionId = sessionId;
    this.sessionStartTime = new Date();
    
    console.log(`📊 Starting performance logging for session: ${sessionId}`);

    await this.logEvent({
      eventType: 'session_start',
      sessionId,
      metadata: {
        platform: 'react-native',
        timestamp: this.sessionStartTime.toISOString()
      }
    });
  }

  // End logging for a backup session
  async endSession(success: boolean, totalItems: number, totalErrors: number): Promise<void> {
    if (!this.currentSessionId || !this.sessionStartTime) {
      return;
    }

    const duration = Date.now() - this.sessionStartTime.getTime();

    await this.logEvent({
      eventType: 'session_end',
      sessionId: this.currentSessionId,
      duration,
      metadata: {
        success,
        totalItems,
        totalErrors,
        endTime: new Date().toISOString()
      }
    });

    // Generate and save performance report
    const report = await this.generatePerformanceReport(this.currentSessionId);
    await this.savePerformanceReport(report);

    console.log(`📊 Performance logging ended for session: ${this.currentSessionId}`);
    
    this.currentSessionId = undefined;
    this.sessionStartTime = undefined;
    this.dataTypeStartTimes.clear();
  }

  // Log start of data type processing
  async logDataTypeStart(dataType: 'contacts' | 'messages' | 'photos', totalItems: number): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    const startTime = new Date();
    this.dataTypeStartTimes.set(dataType, startTime);

    await this.logEvent({
      eventType: 'data_type_start',
      sessionId: this.currentSessionId,
      dataType,
      metadata: {
        totalItems,
        startTime: startTime.toISOString()
      }
    });
  }

  // Log end of data type processing
  async logDataTypeEnd(
    dataType: 'contacts' | 'messages' | 'photos',
    processedItems: number,
    failedItems: number,
    totalBytes: number
  ): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    const startTime = this.dataTypeStartTimes.get(dataType);
    const duration = startTime ? Date.now() - startTime.getTime() : 0;
    const throughput = duration > 0 ? (processedItems / duration) * 1000 : 0; // items per second

    await this.logEvent({
      eventType: 'data_type_end',
      sessionId: this.currentSessionId,
      dataType,
      duration,
      throughput,
      metadata: {
        processedItems,
        failedItems,
        totalBytes,
        endTime: new Date().toISOString()
      }
    });

    this.dataTypeStartTimes.delete(dataType);
  }

  // Log individual item processing
  async logItemProcessed(
    dataType: 'contacts' | 'messages' | 'photos',
    itemId: string,
    itemSize: number,
    processingTime: number,
    success: boolean
  ): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    const throughput = processingTime > 0 ? itemSize / (processingTime / 1000) : 0; // bytes per second

    await this.logEvent({
      eventType: 'item_processed',
      sessionId: this.currentSessionId,
      dataType,
      itemId,
      duration: processingTime,
      itemSize,
      throughput,
      metadata: {
        success,
        timestamp: new Date().toISOString()
      }
    });
  }

  // Log error occurrence
  async logError(
    dataType: 'contacts' | 'messages' | 'photos' | undefined,
    errorMessage: string,
    itemId?: string,
    retryable: boolean = true
  ): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    await this.logEvent({
      eventType: 'error',
      sessionId: this.currentSessionId,
      dataType,
      itemId,
      errorMessage,
      metadata: {
        retryable,
        timestamp: new Date().toISOString()
      }
    });
  }

  // Log backup pause
  async logPause(reason: string): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    await this.logEvent({
      eventType: 'pause',
      sessionId: this.currentSessionId,
      metadata: {
        reason,
        timestamp: new Date().toISOString()
      }
    });
  }

  // Log backup resume
  async logResume(): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    await this.logEvent({
      eventType: 'resume',
      sessionId: this.currentSessionId,
      metadata: {
        timestamp: new Date().toISOString()
      }
    });
  }

  // Log performance metrics
  async logPerformanceMetrics(
    memoryUsage: number,
    batteryLevel: number,
    networkType: string
  ): Promise<void> {
    if (!this.currentSessionId) {
      return;
    }

    // Find the most recent log to update with performance data
    const recentLog = this.logs[this.logs.length - 1];
    if (recentLog && recentLog.sessionId === this.currentSessionId) {
      recentLog.memoryUsage = memoryUsage;
      recentLog.batteryLevel = batteryLevel;
      recentLog.networkType = networkType;
    }
  }

  // Internal method to log events
  private async logEvent(eventData: Partial<BackupPerformanceLog>): Promise<void> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return;
      }

      const log: BackupPerformanceLog = {
        id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        sessionId: eventData.sessionId || this.currentSessionId || '',
        userId: user.id,
        timestamp: new Date(),
        eventType: eventData.eventType!,
        dataType: eventData.dataType,
        itemId: eventData.itemId,
        duration: eventData.duration,
        itemSize: eventData.itemSize,
        throughput: eventData.throughput,
        memoryUsage: eventData.memoryUsage,
        batteryLevel: eventData.batteryLevel,
        networkType: eventData.networkType,
        errorMessage: eventData.errorMessage,
        metadata: eventData.metadata
      };

      this.logs.push(log);

      // Keep only recent logs to prevent memory bloat
      if (this.logs.length > this.MAX_LOGS_STORED) {
        this.logs = this.logs.slice(-this.MAX_LOGS_STORED);
      }

      // Save to local storage periodically
      if (this.logs.length % 10 === 0) {
        await this.saveLogs();
      }

    } catch (error) {
      console.error('Error logging performance event:', error);
    }
  }

  // Generate comprehensive performance report
  async generatePerformanceReport(sessionId: string): Promise<PerformanceReport> {
    const sessionLogs = this.logs.filter(log => log.sessionId === sessionId);
    
    if (sessionLogs.length === 0) {
      return this.createEmptyReport(sessionId);
    }

    // Calculate session metrics
    const sessionMetrics = this.calculateSessionMetrics(sessionLogs);
    
    // Calculate data type breakdown
    const dataTypeBreakdown = {
      contacts: this.calculateDataTypeMetrics(sessionLogs, 'contacts'),
      messages: this.calculateDataTypeMetrics(sessionLogs, 'messages'),
      photos: this.calculateDataTypeMetrics(sessionLogs, 'photos')
    };

    // Generate recommendations
    const recommendations = this.generateRecommendations(sessionMetrics, dataTypeBreakdown);

    return {
      sessionMetrics,
      dataTypeBreakdown,
      timelineEvents: sessionLogs.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime()),
      recommendations
    };
  }

  // Calculate session-level metrics
  private calculateSessionMetrics(logs: BackupPerformanceLog[]): PerformanceMetrics {
    const sessionStart = logs.find(log => log.eventType === 'session_start');
    const sessionEnd = logs.find(log => log.eventType === 'session_end');
    
    const totalDuration = sessionStart && sessionEnd 
      ? sessionEnd.timestamp.getTime() - sessionStart.timestamp.getTime()
      : 0;

    const itemLogs = logs.filter(log => log.eventType === 'item_processed');
    const errorLogs = logs.filter(log => log.eventType === 'error');
    const pauseLogs = logs.filter(log => log.eventType === 'pause');

    const itemsProcessed = itemLogs.length;
    const totalBytes = itemLogs.reduce((sum, log) => sum + (log.itemSize || 0), 0);
    const averageThroughput = itemLogs.length > 0 
      ? itemLogs.reduce((sum, log) => sum + (log.throughput || 0), 0) / itemLogs.length
      : 0;

    const memoryUsages = logs.map(log => log.memoryUsage).filter(Boolean) as number[];
    const memoryPeakUsage = memoryUsages.length > 0 ? Math.max(...memoryUsages) : 0;

    const batteryLevels = logs.map(log => log.batteryLevel).filter(Boolean) as number[];
    const batteryStart = batteryLevels[0] || 100;
    const batteryEnd = batteryLevels[batteryLevels.length - 1] || 100;
    const batteryConsumption = batteryStart - batteryEnd;

    return {
      sessionId: logs[0]?.sessionId || '',
      totalDuration,
      itemsProcessed,
      totalBytes,
      averageThroughput,
      errorCount: errorLogs.length,
      pauseCount: pauseLogs.length,
      memoryPeakUsage,
      batteryConsumption,
      networkEfficiency: this.calculateNetworkEfficiency(logs)
    };
  }

  // Calculate data type specific metrics
  private calculateDataTypeMetrics(logs: BackupPerformanceLog[], dataType: 'contacts' | 'messages' | 'photos'): PerformanceMetrics {
    const dataTypeLogs = logs.filter(log => log.dataType === dataType);
    
    const startLog = dataTypeLogs.find(log => log.eventType === 'data_type_start');
    const endLog = dataTypeLogs.find(log => log.eventType === 'data_type_end');
    
    const totalDuration = startLog && endLog 
      ? endLog.timestamp.getTime() - startLog.timestamp.getTime()
      : 0;

    const itemLogs = dataTypeLogs.filter(log => log.eventType === 'item_processed');
    const errorLogs = dataTypeLogs.filter(log => log.eventType === 'error');

    const itemsProcessed = itemLogs.length;
    const totalBytes = itemLogs.reduce((sum, log) => sum + (log.itemSize || 0), 0);
    const averageThroughput = itemLogs.length > 0 
      ? itemLogs.reduce((sum, log) => sum + (log.throughput || 0), 0) / itemLogs.length
      : 0;

    return {
      sessionId: logs[0]?.sessionId || '',
      totalDuration,
      itemsProcessed,
      totalBytes,
      averageThroughput,
      errorCount: errorLogs.length,
      pauseCount: 0,
      memoryPeakUsage: 0,
      batteryConsumption: 0,
      networkEfficiency: 0
    };
  }

  // Calculate network efficiency score
  private calculateNetworkEfficiency(logs: BackupPerformanceLog[]): number {
    const throughputValues = logs
      .map(log => log.throughput)
      .filter(Boolean) as number[];

    if (throughputValues.length === 0) {
      return 0;
    }

    const averageThroughput = throughputValues.reduce((sum, val) => sum + val, 0) / throughputValues.length;
    
    // Normalize to 0-100 scale (assuming 1MB/s is 100% efficiency)
    return Math.min(100, (averageThroughput / (1024 * 1024)) * 100);
  }

  // Generate performance recommendations
  private generateRecommendations(
    sessionMetrics: PerformanceMetrics,
    dataTypeBreakdown: any
  ): string[] {
    const recommendations: string[] = [];

    // Battery recommendations
    if (sessionMetrics.batteryConsumption > 20) {
      recommendations.push('High battery consumption detected. Consider enabling WiFi-only backup or connecting to power during backup.');
    }

    // Memory recommendations
    if (sessionMetrics.memoryPeakUsage > 80) {
      recommendations.push('High memory usage detected. Consider reducing concurrent operations or clearing app cache.');
    }

    // Error rate recommendations
    const errorRate = sessionMetrics.itemsProcessed > 0 
      ? (sessionMetrics.errorCount / sessionMetrics.itemsProcessed) * 100 
      : 0;
    
    if (errorRate > 5) {
      recommendations.push('High error rate detected. Check network connection stability and storage availability.');
    }

    // Throughput recommendations
    if (sessionMetrics.averageThroughput < 100000) { // Less than 100KB/s
      recommendations.push('Low backup throughput detected. Consider using WiFi connection or reducing concurrent apps.');
    }

    // Pause recommendations
    if (sessionMetrics.pauseCount > 3) {
      recommendations.push('Frequent backup pauses detected. Check battery level and network stability.');
    }

    // Data type specific recommendations
    if (dataTypeBreakdown.photos.errorCount > dataTypeBreakdown.contacts.errorCount + dataTypeBreakdown.messages.errorCount) {
      recommendations.push('Photo backup has higher error rate. Consider reducing photo quality or using chunked upload.');
    }

    return recommendations;
  }

  // Create empty report structure
  private createEmptyReport(sessionId: string): PerformanceReport {
    const emptyMetrics: PerformanceMetrics = {
      sessionId,
      totalDuration: 0,
      itemsProcessed: 0,
      totalBytes: 0,
      averageThroughput: 0,
      errorCount: 0,
      pauseCount: 0,
      memoryPeakUsage: 0,
      batteryConsumption: 0,
      networkEfficiency: 0
    };

    return {
      sessionMetrics: emptyMetrics,
      dataTypeBreakdown: {
        contacts: emptyMetrics,
        messages: emptyMetrics,
        photos: emptyMetrics
      },
      timelineEvents: [],
      recommendations: []
    };
  }

  // Save performance report to database
  private async savePerformanceReport(report: PerformanceReport): Promise<void> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return;
      }

      // Save to Supabase (would need to create performance_reports table)
      const { error } = await supabase
        .from('performance_reports')
        .insert({
          session_id: report.sessionMetrics.sessionId,
          user_id: user.id,
          report_data: report,
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to save performance report:', error);
      } else {
        console.log(`📊 Performance report saved for session: ${report.sessionMetrics.sessionId}`);
      }
    } catch (error) {
      console.error('Error saving performance report:', error);
    }
  }

  // Save logs to local storage
  private async saveLogs(): Promise<void> {
    try {
      const logsData = {
        logs: this.logs.slice(-500), // Keep only recent logs
        lastUpdated: new Date().toISOString()
      };
      
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(logsData));
    } catch (error) {
      console.error('Error saving performance logs:', error);
    }
  }

  // Load logs from local storage
  async loadLogs(): Promise<void> {
    try {
      const logsJson = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (logsJson) {
        const logsData = JSON.parse(logsJson);
        this.logs = logsData.logs.map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }));
      }
    } catch (error) {
      console.error('Error loading performance logs:', error);
    }
  }

  // Get recent performance reports
  async getRecentReports(limit: number = 10): Promise<PerformanceReport[]> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return [];
      }

      const { data, error } = await supabase
        .from('performance_reports')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      return (data || []).map(row => row.report_data);
    } catch (error) {
      console.error('Error getting recent reports:', error);
      return [];
    }
  }

  // Clear all logs and reports
  async clearLogs(): Promise<void> {
    try {
      this.logs = [];
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      console.log('📊 Performance logs cleared');
    } catch (error) {
      console.error('Error clearing performance logs:', error);
    }
  }

  // Get current session logs
  getCurrentSessionLogs(): BackupPerformanceLog[] {
    if (!this.currentSessionId) {
      return [];
    }
    
    return this.logs.filter(log => log.sessionId === this.currentSessionId);
  }
}

export default new BackupPerformanceLogger();