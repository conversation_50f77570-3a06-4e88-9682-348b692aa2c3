import { configureStore } from '@reduxjs/toolkit';
import backupReducer, {
  startBackupSession,
  updateBackupSession,
  completeBackupSession,
  cancelBackupSession,
  updateDataTypeProgress,
  addProgressError,
  updateConfiguration,
  calculateStatistics,
  clearErrors,
  removeError,
  setLoading,
  resetBackupState,
} from '../../src/store/slices/backupSlice';
import { BackupSession, BackupError, BackupConfiguration } from '../../src/types/backup';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('backupSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        backup: backupReducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          serializableCheck: {
            ignoredActions: [
              'backup/startBackupSession',
              'backup/completeBackupSession',
              'backup/addProgressError',
            ],
            ignoredPaths: [
              'backup.currentSession.startTime',
              'backup.currentSession.endTime',
              'backup.backupHistory',
              'backup.realTimeProgress.contacts.errors',
              'backup.realTimeProgress.messages.errors',
              'backup.realTimeProgress.photos.errors',
              'backup.errors',
            ],
          },
        }),
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().backup;
      
      expect(state.currentSession).toBeNull();
      expect(state.isBackupInProgress).toBe(false);
      expect(state.backupHistory).toEqual([]);
      expect(state.errors).toEqual([]);
      expect(state.loading).toBe(false);
      expect(state.configuration).toEqual({
        autoBackup: false,
        wifiOnly: true,
        includeContacts: true,
        includeMessages: true,
        includePhotos: true,
        compressionLevel: 'medium',
      });
    });
  });

  describe('session management', () => {
    const mockSession: BackupSession = {
      id: 'test-session-1',
      userId: 'user-123',
      startTime: new Date('2024-01-01T10:00:00Z'),
      status: 'in_progress',
      totalItems: 100,
      completedItems: 0,
      configuration: {
        autoBackup: false,
        wifiOnly: true,
        includeContacts: true,
        includeMessages: true,
        includePhotos: true,
        compressionLevel: 'medium',
      },
      progress: {
        contacts: {
          total: 50,
          completed: 0,
          failed: 0,
          status: 'pending',
          errors: [],
        },
        messages: {
          total: 30,
          completed: 0,
          failed: 0,
          status: 'pending',
          errors: [],
        },
        photos: {
          total: 20,
          completed: 0,
          failed: 0,
          status: 'pending',
          errors: [],
        },
      },
    };

    it('should start backup session', () => {
      store.dispatch(startBackupSession(mockSession));
      
      const state = store.getState().backup;
      expect(state.currentSession).toEqual(mockSession);
      expect(state.isBackupInProgress).toBe(true);
      expect(state.errors).toEqual([]);
    });

    it('should update backup session', () => {
      store.dispatch(startBackupSession(mockSession));
      
      const updates = {
        completedItems: 25,
        status: 'in_progress' as const,
      };
      
      store.dispatch(updateBackupSession(updates));
      
      const state = store.getState().backup;
      expect(state.currentSession?.completedItems).toBe(25);
      expect(state.currentSession?.status).toBe('in_progress');
    });

    it('should complete backup session', () => {
      store.dispatch(startBackupSession(mockSession));
      
      const endTime = new Date('2024-01-01T10:30:00Z');
      store.dispatch(completeBackupSession({ endTime, status: 'completed' }));
      
      const state = store.getState().backup;
      expect(state.currentSession?.endTime).toEqual(endTime);
      expect(state.currentSession?.status).toBe('completed');
      expect(state.isBackupInProgress).toBe(false);
      expect(state.backupHistory).toHaveLength(1);
      expect(state.backupHistory[0]).toEqual(state.currentSession);
    });

    it('should cancel backup session', () => {
      store.dispatch(startBackupSession(mockSession));
      store.dispatch(cancelBackupSession());
      
      const state = store.getState().backup;
      expect(state.currentSession?.status).toBe('cancelled');
      expect(state.isBackupInProgress).toBe(false);
    });
  });

  describe('progress tracking', () => {
    beforeEach(() => {
      const mockSession: BackupSession = {
        id: 'test-session-1',
        userId: 'user-123',
        startTime: new Date(),
        status: 'in_progress',
        totalItems: 100,
        completedItems: 0,
        configuration: {
          autoBackup: false,
          wifiOnly: true,
          includeContacts: true,
          includeMessages: true,
          includePhotos: true,
          compressionLevel: 'medium',
        },
        progress: {
          contacts: { total: 50, completed: 0, failed: 0, status: 'pending', errors: [] },
          messages: { total: 30, completed: 0, failed: 0, status: 'pending', errors: [] },
          photos: { total: 20, completed: 0, failed: 0, status: 'pending', errors: [] },
        },
      };
      store.dispatch(startBackupSession(mockSession));
    });

    it('should update data type progress', () => {
      store.dispatch(updateDataTypeProgress({
        dataType: 'contacts',
        progress: { completed: 25, status: 'in_progress' }
      }));
      
      const state = store.getState().backup;
      expect(state.realTimeProgress.contacts.completed).toBe(25);
      expect(state.realTimeProgress.contacts.status).toBe('in_progress');
      expect(state.currentSession?.progress.contacts.completed).toBe(25);
      expect(state.currentSession?.completedItems).toBe(25);
    });

    it('should add progress error', () => {
      const error: BackupError = {
        id: 'error-1',
        type: 'network',
        message: 'Connection failed',
        timestamp: new Date(),
        retryable: true,
      };

      store.dispatch(addProgressError({ dataType: 'photos', error }));
      
      const state = store.getState().backup;
      expect(state.realTimeProgress.photos.errors).toContain(error);
      expect(state.errors).toContain(error);
    });

    it('should update total progress when individual progress changes', () => {
      // Update contacts progress
      store.dispatch(updateDataTypeProgress({
        dataType: 'contacts',
        progress: { completed: 25 }
      }));
      
      // Update messages progress
      store.dispatch(updateDataTypeProgress({
        dataType: 'messages',
        progress: { completed: 15 }
      }));
      
      const state = store.getState().backup;
      expect(state.currentSession?.completedItems).toBe(40); // 25 + 15
    });
  });

  describe('configuration management', () => {
    it('should update configuration', () => {
      const configUpdate: Partial<BackupConfiguration> = {
        autoBackup: true,
        wifiOnly: false,
        compressionLevel: 'high',
      };

      store.dispatch(updateConfiguration(configUpdate));
      
      const state = store.getState().backup;
      expect(state.configuration.autoBackup).toBe(true);
      expect(state.configuration.wifiOnly).toBe(false);
      expect(state.configuration.compressionLevel).toBe('high');
      expect(state.configuration.includeContacts).toBe(true); // Should preserve existing values
    });
  });

  describe('statistics calculation', () => {
    beforeEach(() => {
      // Add some completed sessions to history
      const completedSession1: BackupSession = {
        id: 'session-1',
        userId: 'user-123',
        startTime: new Date('2024-01-01T10:00:00Z'),
        endTime: new Date('2024-01-01T10:15:00Z'),
        status: 'completed',
        totalItems: 100,
        completedItems: 95,
        configuration: {
          autoBackup: false,
          wifiOnly: true,
          includeContacts: true,
          includeMessages: true,
          includePhotos: true,
          compressionLevel: 'medium',
        },
        progress: {
          contacts: { total: 50, completed: 45, failed: 5, status: 'completed', errors: [] },
          messages: { total: 30, completed: 30, failed: 0, status: 'completed', errors: [] },
          photos: { total: 20, completed: 20, failed: 0, status: 'completed', errors: [] },
        },
      };

      const completedSession2: BackupSession = {
        id: 'session-2',
        userId: 'user-123',
        startTime: new Date('2024-01-02T10:00:00Z'),
        endTime: new Date('2024-01-02T10:10:00Z'),
        status: 'completed',
        totalItems: 80,
        completedItems: 80,
        configuration: {
          autoBackup: false,
          wifiOnly: true,
          includeContacts: true,
          includeMessages: true,
          includePhotos: true,
          compressionLevel: 'medium',
        },
        progress: {
          contacts: { total: 40, completed: 40, failed: 0, status: 'completed', errors: [] },
          messages: { total: 25, completed: 25, failed: 0, status: 'completed', errors: [] },
          photos: { total: 15, completed: 15, failed: 0, status: 'completed', errors: [] },
        },
      };

      const failedSession: BackupSession = {
        id: 'session-3',
        userId: 'user-123',
        startTime: new Date('2024-01-03T10:00:00Z'),
        endTime: new Date('2024-01-03T10:05:00Z'),
        status: 'failed',
        totalItems: 60,
        completedItems: 20,
        configuration: {
          autoBackup: false,
          wifiOnly: true,
          includeContacts: true,
          includeMessages: true,
          includePhotos: true,
          compressionLevel: 'medium',
        },
        progress: {
          contacts: { total: 30, completed: 10, failed: 20, status: 'failed', errors: [] },
          messages: { total: 20, completed: 10, failed: 10, status: 'failed', errors: [] },
          photos: { total: 10, completed: 0, failed: 10, status: 'failed', errors: [] },
        },
      };

      // Manually set the history
      store.dispatch(startBackupSession(completedSession1));
      store.dispatch(completeBackupSession({ endTime: completedSession1.endTime!, status: 'completed' }));
      
      store.dispatch(startBackupSession(completedSession2));
      store.dispatch(completeBackupSession({ endTime: completedSession2.endTime!, status: 'completed' }));
      
      store.dispatch(startBackupSession(failedSession));
      store.dispatch(completeBackupSession({ endTime: failedSession.endTime!, status: 'failed' }));
    });

    it('should calculate statistics correctly', () => {
      store.dispatch(calculateStatistics());
      
      const state = store.getState().backup;
      const stats = state.statistics;
      
      expect(stats.totalBackups).toBe(2); // Only completed sessions
      expect(stats.totalItems).toBe(175); // 95 + 80
      expect(stats.successRate).toBeCloseTo(66.67, 1); // 2 successful out of 3 total
      
      // Check data type breakdown
      expect(stats.dataTypeBreakdown.contacts.count).toBe(85); // 45 + 40
      expect(stats.dataTypeBreakdown.messages.count).toBe(55); // 30 + 25
      expect(stats.dataTypeBreakdown.photos.count).toBe(35); // 20 + 15
    });
  });

  describe('error management', () => {
    beforeEach(() => {
      const error1: BackupError = {
        id: 'error-1',
        type: 'network',
        message: 'Connection failed',
        timestamp: new Date(),
        retryable: true,
      };

      const error2: BackupError = {
        id: 'error-2',
        type: 'permission',
        message: 'Access denied',
        timestamp: new Date(),
        retryable: false,
      };

      store.dispatch(addProgressError({ dataType: 'contacts', error: error1 }));
      store.dispatch(addProgressError({ dataType: 'photos', error: error2 }));
    });

    it('should clear all errors', () => {
      store.dispatch(clearErrors());
      
      const state = store.getState().backup;
      expect(state.errors).toEqual([]);
      expect(state.realTimeProgress.contacts.errors).toEqual([]);
      expect(state.realTimeProgress.photos.errors).toEqual([]);
    });

    it('should remove specific error', () => {
      store.dispatch(removeError('error-1'));
      
      const state = store.getState().backup;
      expect(state.errors).toHaveLength(1);
      expect(state.errors[0].id).toBe('error-2');
      expect(state.realTimeProgress.contacts.errors).toEqual([]);
      expect(state.realTimeProgress.photos.errors).toHaveLength(1);
    });
  });

  describe('state management', () => {
    it('should set loading state', () => {
      store.dispatch(setLoading(true));
      expect(store.getState().backup.loading).toBe(true);
      
      store.dispatch(setLoading(false));
      expect(store.getState().backup.loading).toBe(false);
    });

    it('should reset backup state', () => {
      // First, set some state
      const mockSession: BackupSession = {
        id: 'test-session',
        userId: 'user-123',
        startTime: new Date(),
        status: 'in_progress',
        totalItems: 100,
        completedItems: 50,
        configuration: {
          autoBackup: true,
          wifiOnly: false,
          includeContacts: true,
          includeMessages: true,
          includePhotos: true,
          compressionLevel: 'high',
        },
        progress: {
          contacts: { total: 50, completed: 25, failed: 0, status: 'in_progress', errors: [] },
          messages: { total: 30, completed: 15, failed: 0, status: 'in_progress', errors: [] },
          photos: { total: 20, completed: 10, failed: 0, status: 'in_progress', errors: [] },
        },
      };

      // Update configuration first
      store.dispatch(updateConfiguration(mockSession.configuration));
      store.dispatch(startBackupSession(mockSession));
      store.dispatch(setLoading(true));
      
      // Reset state
      store.dispatch(resetBackupState());
      
      const state = store.getState().backup;
      expect(state.currentSession).toBeNull();
      expect(state.isBackupInProgress).toBe(false);
      expect(state.backupHistory).toEqual([]);
      expect(state.errors).toEqual([]);
      expect(state.loading).toBe(false);
      // Configuration should be preserved
      expect(state.configuration).toEqual(mockSession.configuration);
    });
  });
});