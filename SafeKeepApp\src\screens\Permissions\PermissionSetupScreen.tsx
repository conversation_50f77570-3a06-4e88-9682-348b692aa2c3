import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { <PERSON>ton, Card, Text, ProgressBar, List, Chip } from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

import PermissionService, { PermissionStatus } from '../../services/PermissionService';
import { COLORS, FONTS, SPACING } from '../../utils/constants';

interface PermissionItem {
  key: string;
  title: string;
  description: string;
  icon: string;
  status: PermissionStatus;
  required: boolean;
}

const PermissionSetupScreen = () => {
  const navigation = useNavigation();
  const [permissions, setPermissions] = useState<Record<string, PermissionStatus>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const permissionItems: PermissionItem[] = [
    {
      key: 'photos',
      title: 'Photo Access',
      description: 'Backup your precious family photos safely',
      icon: 'camera',
      status: permissions.photos || { granted: false, denied: false, blocked: false, unavailable: false },
      required: true,
    },
    {
      key: 'contacts',
      title: 'Contact Access', 
      description: 'Save important phone numbers and contacts',
      icon: 'contacts',
      status: permissions.contacts || { granted: false, denied: false, blocked: false, unavailable: false },
      required: true,
    },
    {
      key: 'sms',
      title: 'Message Access',
      description: 'Backup important text conversations',
      icon: 'message-text',
      status: permissions.sms || { granted: false, denied: false, blocked: false, unavailable: false },
      required: false,
    },
  ];

  useEffect(() => {
    checkCurrentPermissions();
  }, []);

  const checkCurrentPermissions = async () => {
    try {
      const currentPermissions = await PermissionService.checkAllPermissions();
      setPermissions(currentPermissions);
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const requestSinglePermission = async (permissionType: string) => {
    setIsLoading(true);
    try {
      const result = await PermissionService.requestPermission(permissionType as any);
      setPermissions(prev => ({
        ...prev,
        [permissionType]: result
      }));
    } catch (error) {
      console.error(`Error requesting ${permissionType} permission:`, error);
      Alert.alert(
        'Permission Error',
        'There was a problem requesting permission. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const requestAllPermissions = async () => {
    setIsLoading(true);
    try {
      const results = await PermissionService.requestAllPermissions();
      setPermissions(results);
      
      const summary = PermissionService.getPermissionSummary(results);
      
      if (summary.allGranted) {
        Alert.alert(
          '🎉 All Set!',
          'Great! SafeKeep now has all the permissions needed to keep your data safe. You can start backing up right away!',
          [
            {
              text: 'Start Using SafeKeep',
              onPress: () => navigation.navigate('Main' as never)
            }
          ]
        );
      } else {
        Alert.alert(
          'Almost There!',
          `We have ${summary.grantedCount} out of ${summary.totalCount} permissions. You can still use SafeKeep, but some features may be limited.`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting all permissions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusChip = (status: PermissionStatus) => {
    if (status.unavailable) {
      return (
        <Chip 
          icon="information" 
          style={[styles.statusChip, { backgroundColor: COLORS.textSecondary }]}
          textStyle={{ color: '#fff' }}
        >
          Not Available
        </Chip>
      );
    }
    
    if (status.granted) {
      return (
        <Chip 
          icon="check" 
          style={[styles.statusChip, { backgroundColor: COLORS.success }]}
          textStyle={{ color: '#fff' }}
        >
          Allowed
        </Chip>
      );
    }
    
    if (status.blocked) {
      return (
        <Chip 
          icon="block-helper" 
          style={[styles.statusChip, { backgroundColor: COLORS.error }]}
          textStyle={{ color: '#fff' }}
        >
          Blocked
        </Chip>
      );
    }
    
    return (
      <Chip 
        icon="help" 
        style={[styles.statusChip, { backgroundColor: COLORS.warning }]}
        textStyle={{ color: '#fff' }}
      >
        Not Asked
      </Chip>
    );
  };

  const getOverallProgress = () => {
    const summary = PermissionService.getPermissionSummary(permissions);
    return summary.totalCount > 0 ? summary.grantedCount / summary.totalCount : 0;
  };

  const canProceed = () => {
    const summary = PermissionService.getPermissionSummary(permissions);
    // Allow proceeding if at least photos permission is granted
    return permissions.photos?.granted || false;
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Setup Permissions',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        <Card style={styles.introCard}>
          <Card.Content>
            <View style={styles.introHeader}>
              <Icon name="shield-check" size={48} color={COLORS.primary} />
              <Text variant="headlineSmall" style={styles.introTitle}>
                Let's Keep Your Data Safe
              </Text>
            </View>
            
            <Text variant="bodyLarge" style={styles.introText}>
              SafeKeep needs a few permissions to backup your important data. 
              Don't worry - we'll explain exactly what each one does and why it helps you!
            </Text>
            
            <View style={styles.progressContainer}>
              <Text variant="bodyMedium" style={styles.progressText}>
                Setup Progress
              </Text>
              <ProgressBar 
                progress={getOverallProgress()} 
                color={COLORS.primary}
                style={styles.progressBar}
              />
              <Text variant="bodySmall" style={styles.progressLabel}>
                {Math.round(getOverallProgress() * 100)}% Complete
              </Text>
            </View>
          </Card.Content>
        </Card>

        <Card style={styles.permissionsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Permissions Needed
            </Text>
            
            {permissionItems.map((item) => (
              <List.Item
                key={item.key}
                title={item.title}
                description={item.description}
                left={() => <Icon name={item.icon} size={32} color={COLORS.primary} />}
                right={() => (
                  <View style={styles.permissionRight}>
                    {getStatusChip(item.status)}
                    {!item.status.granted && !item.status.unavailable && (
                      <Button
                        mode="outlined"
                        compact
                        onPress={() => requestSinglePermission(item.key)}
                        disabled={isLoading}
                        style={styles.allowButton}
                      >
                        Allow
                      </Button>
                    )}
                  </View>
                )}
                style={styles.permissionItem}
              />
            ))}
          </Card.Content>
        </Card>

        <Card style={styles.actionCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.actionTitle}>
              Ready to Get Started?
            </Text>
            
            <Text variant="bodyMedium" style={styles.actionDescription}>
              You can grant all permissions at once, or set them up individually above.
            </Text>
            
            <View style={styles.actionButtons}>
              <Button
                mode="contained"
                onPress={requestAllPermissions}
                disabled={isLoading}
                loading={isLoading}
                style={styles.primaryButton}
                icon="shield-check"
              >
                Grant All Permissions
              </Button>
              
              {canProceed() && (
                <Button
                  mode="outlined"
                  onPress={() => navigation.navigate('Main' as never)}
                  style={styles.secondaryButton}
                >
                  Continue to App
                </Button>
              )}
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  introCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  introHeader: {
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  introTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },
  introText: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.lg,
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressText: {
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: SPACING.xs,
  },
  progressLabel: {
    color: COLORS.textSecondary,
  },
  permissionsCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  sectionTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  permissionItem: {
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  permissionRight: {
    alignItems: 'flex-end',
    gap: SPACING.xs,
  },
  statusChip: {
    marginBottom: SPACING.xs,
  },
  allowButton: {
    borderColor: COLORS.primary,
  },
  actionCard: {
    elevation: 4,
  },
  actionTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  actionDescription: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  actionButtons: {
    gap: SPACING.md,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    borderColor: COLORS.primary,
  },
});

export default PermissionSetupScreen;
