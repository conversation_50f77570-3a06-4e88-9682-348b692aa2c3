/**
 * Enhanced UI Integration Manager for SafeKeep Web Demo
 * Coordinates all UI enhancement components and provides unified interface
 */

class EnhancedUIIntegration {
    constructor() {
        this.components = {
            theme: null,
            responsive: null,
            accessibility: null,
            pwa: null
        };
        
        this.initialized = false;
        this.eventListeners = new Map();
        
        this.init();
    }
    
    async init() {
        try {
            await this.waitForDOMReady();
            await this.initializeComponents();
            this.setupGlobalEventHandlers();
            this.enhanceExistingElements();
            this.setupComponentIntegration();
            this.createUnifiedControls();
            
            this.initialized = true;
            this.dispatchEvent('enhanced-ui-ready');
            
            console.log('Enhanced UI Integration initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Enhanced UI Integration:', error);
        }
    }
    
    waitForDOMReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }
    
    async initializeComponents() {
        // Wait for component managers to be available
        await this.waitForComponents();
        
        // Store references to component managers
        this.components.theme = window.themeManager;
        this.components.responsive = window.responsiveLayoutManager;
        this.components.accessibility = window.accessibilityManager;
        this.components.pwa = window.pwaManager;
        
        console.log('All UI components initialized');
    }
    
    waitForComponents() {
        return new Promise((resolve) => {
            const checkComponents = () => {
                if (window.themeManager && 
                    window.responsiveLayoutManager && 
                    window.accessibilityManager && 
                    window.pwaManager) {
                    resolve();
                } else {
                    setTimeout(checkComponents, 100);
                }
            };
            checkComponents();
        });
    }
    
    setupGlobalEventHandlers() {
        // Theme change integration
        this.addEventListener('themechange', (event) => {
            this.handleThemeChange(event.detail);
        });
        
        // Breakpoint change integration
        this.addEventListener('breakpointchange', (event) => {
            this.handleBreakpointChange(event.detail);
        });
        
        // Online status integration
        this.addEventListener('onlinestatuschange', (event) => {
            this.handleOnlineStatusChange(event.detail);
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleGlobalKeyboard(event);
        });
        
        // Focus management
        document.addEventListener('focusin', (event) => {
            this.handleFocusChange(event);
        });
        
        // Visibility change
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }
    
    enhanceExistingElements() {
        // Enhance all existing UI elements with new framework
        this.enhanceButtons();
        this.enhanceCards();
        this.enhanceForms();
        this.enhanceProgressBars();
        this.enhanceTables();
        this.addLoadingStates();
        this.addAnimations();
    }
    
    enhanceButtons() {
        const buttons = document.querySelectorAll('button, .btn');
        
        buttons.forEach(button => {
            // Add enhanced button classes
            if (!button.classList.contains('btn')) {
                button.classList.add('btn');
            }
            
            // Add loading state capability
            this.addLoadingCapability(button);
            
            // Add ripple effect
            this.addRippleEffect(button);
            
            // Ensure accessibility
            this.ensureButtonAccessibility(button);
        });
    }
    
    addLoadingCapability(button) {
        const originalHTML = button.innerHTML;
        
        button.setLoading = (loading = true) => {
            if (loading) {
                button.disabled = true;
                button.setAttribute('aria-busy', 'true');
                button.innerHTML = `<span class="spinner"></span> Loading...`;
                button.classList.add('btn-loading');
            } else {
                button.disabled = false;
                button.removeAttribute('aria-busy');
                button.innerHTML = originalHTML;
                button.classList.remove('btn-loading');
            }
        };
    }
    
    addRippleEffect(element) {
        element.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            ripple.className = 'ripple-effect';
            
            // Ensure button has relative positioning
            if (getComputedStyle(this).position === 'static') {
                this.style.position = 'relative';
            }
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }
    
    ensureButtonAccessibility(button) {
        // Ensure minimum touch target size
        const computedStyle = getComputedStyle(button);
        const minSize = 44;
        
        if (parseInt(computedStyle.height) < minSize) {
            button.style.minHeight = `${minSize}px`;
        }
        
        if (parseInt(computedStyle.width) < minSize) {
            button.style.minWidth = `${minSize}px`;
        }
        
        // Add role if missing
        if (!button.hasAttribute('role') && button.tagName !== 'BUTTON') {
            button.setAttribute('role', 'button');
        }
        
        // Add keyboard support for non-button elements
        if (button.tagName !== 'BUTTON' && !button.hasAttribute('tabindex')) {
            button.setAttribute('tabindex', '0');
            
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    button.click();
                }
            });
        }
    }
    
    enhanceCards() {
        const cards = document.querySelectorAll('.card, .demo-card, .plan-card');
        
        cards.forEach(card => {
            // Add hover effects
            card.classList.add('card-enhanced');
            
            // Add focus management for interactive cards
            if (card.querySelector('button, a, [tabindex]')) {
                card.setAttribute('tabindex', '-1');
            }
            
            // Add loading state capability
            this.addCardLoadingState(card);
        });
    }
    
    addCardLoadingState(card) {
        card.setLoading = (loading = true) => {
            if (loading) {
                card.classList.add('card-loading');
                card.setAttribute('aria-busy', 'true');
                
                // Add loading overlay
                const overlay = document.createElement('div');
                overlay.className = 'card-loading-overlay';
                overlay.innerHTML = '<div class="spinner spinner-lg"></div>';
                card.appendChild(overlay);
            } else {
                card.classList.remove('card-loading');
                card.removeAttribute('aria-busy');
                
                // Remove loading overlay
                const overlay = card.querySelector('.card-loading-overlay');
                if (overlay) {
                    overlay.remove();
                }
            }
        };
    }
    
    enhanceForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            this.enhanceFormValidation(form);
            this.addFormSubmissionHandling(form);
        });
    }
    
    enhanceFormValidation(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            // Real-time validation
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                // Clear validation state on input
                input.classList.remove('is-valid', 'is-invalid');
                this.clearFieldFeedback(input);
            });
        });
        
        // Form submission validation
        form.addEventListener('submit', (e) => {
            if (!this.validateForm(form)) {
                e.preventDefault();
            }
        });
    }
    
    validateField(field) {
        const isValid = field.checkValidity();
        
        field.classList.toggle('is-valid', isValid);
        field.classList.toggle('is-invalid', !isValid);
        
        this.showFieldFeedback(field, isValid);
        
        return isValid;
    }
    
    showFieldFeedback(field, isValid) {
        this.clearFieldFeedback(field);
        
        const feedback = document.createElement('div');
        feedback.className = isValid ? 'valid-feedback' : 'invalid-feedback';
        feedback.textContent = isValid ? 'Looks good!' : field.validationMessage;
        
        field.parentNode.appendChild(feedback);
        
        // Announce to screen readers
        if (this.components.accessibility && !isValid) {
            this.components.accessibility.announce(
                `${this.getFieldLabel(field)}: ${field.validationMessage}`,
                'assertive'
            );
        }
    }
    
    clearFieldFeedback(field) {
        const feedback = field.parentNode.querySelector('.valid-feedback, .invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }
    
    validateForm(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    getFieldLabel(field) {
        const label = field.closest('.form-group')?.querySelector('label');
        return label ? label.textContent.trim() : field.name || 'Field';
    }
    
    addFormSubmissionHandling(form) {
        form.addEventListener('submit', (e) => {
            const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
            
            if (submitButton && submitButton.setLoading) {
                submitButton.setLoading(true);
                
                // Auto-disable loading after 5 seconds as fallback
                setTimeout(() => {
                    if (submitButton.setLoading) {
                        submitButton.setLoading(false);
                    }
                }, 5000);
            }
        });
    }
    
    enhanceProgressBars() {
        const progressBars = document.querySelectorAll('.progress-bar, [role="progressbar"]');
        
        progressBars.forEach(bar => {
            this.addProgressBarEnhancements(bar);
        });
    }
    
    addProgressBarEnhancements(progressBar) {
        // Add smooth transitions
        progressBar.style.transition = 'width 0.3s ease';
        
        // Add value display
        if (!progressBar.querySelector('.progress-value')) {
            const valueDisplay = document.createElement('span');
            valueDisplay.className = 'progress-value sr-only';
            progressBar.appendChild(valueDisplay);
        }
        
        // Enhanced update method
        progressBar.updateProgress = (value, max = 100) => {
            const percentage = Math.min(100, Math.max(0, (value / max) * 100));
            
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', value);
            progressBar.setAttribute('aria-valuemax', max);
            
            const valueDisplay = progressBar.querySelector('.progress-value');
            if (valueDisplay) {
                valueDisplay.textContent = `${Math.round(percentage)}%`;
            }
            
            // Announce significant progress changes
            if (this.components.accessibility) {
                const lastAnnounced = progressBar._lastAnnouncedProgress || 0;
                if (percentage - lastAnnounced >= 25 || percentage === 100) {
                    this.components.accessibility.announceStatus(
                        `Progress: ${Math.round(percentage)}%`
                    );
                    progressBar._lastAnnouncedProgress = percentage;
                }
            }
        };
    }
    
    enhanceTables() {
        const tables = document.querySelectorAll('table');
        
        tables.forEach(table => {
            this.addTableEnhancements(table);
        });
    }
    
    addTableEnhancements(table) {
        // Add responsive wrapper if not exists
        if (!table.closest('.table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
        
        // Add sorting capability
        const headers = table.querySelectorAll('th');
        headers.forEach(header => {
            if (!header.hasAttribute('data-no-sort')) {
                this.addTableSorting(header, table);
            }
        });
        
        // Add row selection capability
        this.addTableRowSelection(table);
    }
    
    addTableSorting(header, table) {
        header.style.cursor = 'pointer';
        header.setAttribute('role', 'button');
        header.setAttribute('tabindex', '0');
        header.setAttribute('aria-label', `Sort by ${header.textContent}`);
        
        header.addEventListener('click', () => {
            this.sortTable(table, header);
        });
        
        header.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.sortTable(table, header);
            }
        });
    }
    
    sortTable(table, header) {
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        const currentSort = header.getAttribute('data-sort') || 'none';
        const newSort = currentSort === 'asc' ? 'desc' : 'asc';
        
        // Clear other headers
        table.querySelectorAll('th').forEach(th => {
            th.removeAttribute('data-sort');
            th.removeAttribute('aria-sort');
        });
        
        // Set current header
        header.setAttribute('data-sort', newSort);
        header.setAttribute('aria-sort', newSort === 'asc' ? 'ascending' : 'descending');
        
        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            const comparison = aValue.localeCompare(bValue, undefined, { numeric: true });
            return newSort === 'asc' ? comparison : -comparison;
        });
        
        // Reorder rows
        rows.forEach(row => tbody.appendChild(row));
        
        // Announce sort
        if (this.components.accessibility) {
            this.components.accessibility.announce(
                `Table sorted by ${header.textContent} in ${newSort}ending order`,
                'polite'
            );
        }
    }
    
    addTableRowSelection(table) {
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            row.addEventListener('click', (e) => {
                if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'A') {
                    row.classList.toggle('selected');
                    
                    // Update aria-selected
                    const isSelected = row.classList.contains('selected');
                    row.setAttribute('aria-selected', isSelected);
                    
                    // Dispatch selection event
                    this.dispatchEvent('table-row-selected', {
                        row,
                        selected: isSelected,
                        table
                    });
                }
            });
        });
    }
    
    addLoadingStates() {
        // Add global loading state management
        window.showGlobalLoading = (message = 'Loading...') => {
            this.showGlobalLoading(message);
        };
        
        window.hideGlobalLoading = () => {
            this.hideGlobalLoading();
        };
    }
    
    showGlobalLoading(message) {
        let overlay = document.getElementById('global-loading-overlay');
        
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'global-loading-overlay';
            overlay.className = 'global-loading-overlay';
            overlay.innerHTML = `
                <div class="loading-content">
                    <div class="spinner spinner-lg"></div>
                    <div class="loading-message">${message}</div>
                </div>
            `;
            
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
            `;
            
            document.body.appendChild(overlay);
        } else {
            overlay.querySelector('.loading-message').textContent = message;
            overlay.style.display = 'flex';
        }
        
        // Announce to screen readers
        if (this.components.accessibility) {
            this.components.accessibility.announce(message, 'polite');
        }
    }
    
    hideGlobalLoading() {
        const overlay = document.getElementById('global-loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    addAnimations() {
        // Add intersection observer for scroll animations
        if ('IntersectionObserver' in window) {
            this.setupScrollAnimations();
        }
        
        // Add page transition animations
        this.setupPageTransitions();
    }
    
    setupScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        // Observe elements that should animate in
        const animateElements = document.querySelectorAll('.card, .demo-section, .stats-grid > *');
        animateElements.forEach(el => {
            el.classList.add('animate-on-scroll');
            observer.observe(el);
        });
    }
    
    setupPageTransitions() {
        // Add page load animation
        document.body.classList.add('page-loading');
        
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.body.classList.remove('page-loading');
                document.body.classList.add('page-loaded');
            }, 100);
        });
    }
    
    setupComponentIntegration() {
        // Integrate theme changes with other components
        this.addEventListener('themechange', (event) => {
            // Update charts and visualizations for new theme
            this.updateChartsForTheme(event.detail.theme);
        });
        
        // Integrate responsive changes with accessibility
        this.addEventListener('breakpointchange', (event) => {
            if (this.components.accessibility) {
                this.components.accessibility.announce(
                    `Layout changed to ${event.detail.newBreakpoint} breakpoint`,
                    'polite'
                );
            }
        });
    }
    
    updateChartsForTheme(theme) {
        // Update Chart.js charts if they exist
        if (window.Chart && window.Chart.instances) {
            Object.values(window.Chart.instances).forEach(chart => {
                const isDark = theme === 'dark';
                
                // Update chart colors for theme
                if (chart.options.plugins && chart.options.plugins.legend) {
                    chart.options.plugins.legend.labels.color = isDark ? '#ffffff' : '#333333';
                }
                
                if (chart.options.scales) {
                    Object.values(chart.options.scales).forEach(scale => {
                        if (scale.ticks) {
                            scale.ticks.color = isDark ? '#ffffff' : '#666666';
                        }
                        if (scale.grid) {
                            scale.grid.color = isDark ? '#404040' : '#e9ecef';
                        }
                    });
                }
                
                chart.update();
            });
        }
    }
    
    createUnifiedControls() {
        // Create a unified control panel
        const controlPanel = document.createElement('div');
        controlPanel.id = 'unified-controls';
        controlPanel.className = 'unified-controls-panel';
        controlPanel.innerHTML = `
            <button class="controls-toggle" aria-label="Toggle controls panel">⚙️</button>
            <div class="controls-content">
                <div class="control-section">
                    <h4>Appearance</h4>
                    <div class="control-item">
                        <label>Theme:</label>
                        <select id="theme-selector">
                            <option value="auto">Auto</option>
                            <option value="light">Light</option>
                            <option value="dark">Dark</option>
                            <option value="high-contrast">High Contrast</option>
                        </select>
                    </div>
                </div>
                <div class="control-section">
                    <h4>Accessibility</h4>
                    <div class="control-item">
                        <label>
                            <input type="checkbox" id="enhanced-focus"> Enhanced Focus
                        </label>
                    </div>
                    <div class="control-item">
                        <label>
                            <input type="checkbox" id="reduced-motion"> Reduced Motion
                        </label>
                    </div>
                </div>
                <div class="control-section">
                    <h4>App Features</h4>
                    <div class="control-item">
                        <button class="btn btn-sm" id="install-app">Install App</button>
                    </div>
                    <div class="control-item">
                        <button class="btn btn-sm" id="clear-data">Clear Data</button>
                    </div>
                </div>
            </div>
        `;
        
        controlPanel.style.cssText = `
            position: fixed;
            top: 50%;
            right: -300px;
            transform: translateY(-50%);
            width: 320px;
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg) 0 0 var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            transition: right var(--transition-normal);
        `;
        
        document.body.appendChild(controlPanel);
        
        this.setupUnifiedControlsEvents(controlPanel);
    }
    
    setupUnifiedControlsEvents(panel) {
        const toggle = panel.querySelector('.controls-toggle');
        const content = panel.querySelector('.controls-content');
        let isOpen = false;
        
        toggle.addEventListener('click', () => {
            isOpen = !isOpen;
            panel.style.right = isOpen ? '0' : '-300px';
            toggle.setAttribute('aria-expanded', isOpen);
        });
        
        // Theme selector
        const themeSelector = panel.querySelector('#theme-selector');
        if (this.components.theme) {
            themeSelector.value = this.components.theme.currentTheme;
            themeSelector.addEventListener('change', (e) => {
                this.components.theme.setTheme(e.target.value);
            });
        }
        
        // Enhanced focus toggle
        const enhancedFocus = panel.querySelector('#enhanced-focus');
        enhancedFocus.addEventListener('change', (e) => {
            document.body.classList.toggle('enhanced-focus', e.target.checked);
        });
        
        // Reduced motion toggle
        const reducedMotion = panel.querySelector('#reduced-motion');
        reducedMotion.addEventListener('change', (e) => {
            document.body.classList.toggle('reduced-motion', e.target.checked);
        });
        
        // Install app button
        const installApp = panel.querySelector('#install-app');
        installApp.addEventListener('click', () => {
            if (this.components.pwa && this.components.pwa.canInstall()) {
                this.components.pwa.promptInstall();
            }
        });
        
        // Clear data button
        const clearData = panel.querySelector('#clear-data');
        clearData.addEventListener('click', () => {
            if (confirm('Clear all app data? This cannot be undone.')) {
                this.clearAllData();
            }
        });
        
        // Keyboard shortcut to open panel (Ctrl/Cmd + ,)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === ',') {
                e.preventDefault();
                toggle.click();
            }
        });
    }
    
    clearAllData() {
        // Clear localStorage
        try {
            localStorage.clear();
        } catch (e) {
            console.error('Failed to clear localStorage:', e);
        }
        
        // Clear sessionStorage
        try {
            sessionStorage.clear();
        } catch (e) {
            console.error('Failed to clear sessionStorage:', e);
        }
        
        // Clear caches
        if (this.components.pwa) {
            this.components.pwa.clearCache();
        }
        
        // Reload page
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        
        if (this.components.accessibility) {
            this.components.accessibility.announce('All data cleared. Page will reload.', 'assertive');
        }
    }
    
    handleThemeChange(detail) {
        console.log('Theme changed to:', detail.theme);
        
        // Update unified controls
        const themeSelector = document.querySelector('#theme-selector');
        if (themeSelector) {
            themeSelector.value = detail.userSelected;
        }
    }
    
    handleBreakpointChange(detail) {
        console.log('Breakpoint changed to:', detail.newBreakpoint);
        
        // Update components based on breakpoint
        this.updateComponentsForBreakpoint(detail);
    }
    
    updateComponentsForBreakpoint(detail) {
        // Adjust unified controls for mobile
        const controlPanel = document.getElementById('unified-controls');
        if (controlPanel) {
            if (detail.isMobile) {
                controlPanel.style.width = 'calc(100vw - 20px)';
                controlPanel.style.right = detail.isMobile ? '-calc(100vw - 20px)' : '-320px';
            } else {
                controlPanel.style.width = '320px';
                controlPanel.style.right = '-320px';
            }
        }
    }
    
    handleOnlineStatusChange(detail) {
        console.log('Online status changed:', detail.isOnline);
        
        // Update UI based on online status
        document.body.classList.toggle('offline-mode', !detail.isOnline);
    }
    
    handleGlobalKeyboard(event) {
        // Global keyboard shortcuts
        if (event.altKey) {
            switch (event.key) {
                case 'h':
                    event.preventDefault();
                    this.showKeyboardHelp();
                    break;
                case 'r':
                    event.preventDefault();
                    this.resetToDefaults();
                    break;
            }
        }
    }
    
    showKeyboardHelp() {
        const helpModal = document.createElement('div');
        helpModal.className = 'keyboard-help-modal';
        helpModal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content" role="dialog" aria-labelledby="help-title">
                <div class="modal-header">
                    <h3 id="help-title">Keyboard Shortcuts</h3>
                    <button class="btn btn-sm close-modal" aria-label="Close help">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="shortcut-group">
                        <h4>Navigation</h4>
                        <div class="shortcut-item">
                            <kbd>Alt + M</kbd> <span>Skip to main content</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt + N</kbd> <span>Skip to navigation</span>
                        </div>
                    </div>
                    <div class="shortcut-group">
                        <h4>Accessibility</h4>
                        <div class="shortcut-item">
                            <kbd>Alt + A</kbd> <span>Open accessibility menu</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Alt + F</kbd> <span>Announce current focus</span>
                        </div>
                    </div>
                    <div class="shortcut-group">
                        <h4>App Controls</h4>
                        <div class="shortcut-item">
                            <kbd>Alt + P</kbd> <span>Toggle PWA controls</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl/Cmd + ,</kbd> <span>Open settings</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(helpModal);
        
        // Focus management
        const closeButton = helpModal.querySelector('.close-modal');
        closeButton.focus();
        
        // Event listeners
        const closeModal = () => {
            helpModal.remove();
        };
        
        closeButton.addEventListener('click', closeModal);
        helpModal.querySelector('.modal-backdrop').addEventListener('click', closeModal);
        
        document.addEventListener('keydown', function escapeHandler(e) {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', escapeHandler);
            }
        });
    }
    
    resetToDefaults() {
        if (confirm('Reset all settings to defaults?')) {
            // Reset theme
            if (this.components.theme) {
                this.components.theme.setTheme('auto');
            }
            
            // Reset accessibility settings
            document.body.classList.remove('enhanced-focus', 'reduced-motion');
            
            // Reset form values
            const controls = document.querySelectorAll('#unified-controls input, #unified-controls select');
            controls.forEach(control => {
                if (control.type === 'checkbox') {
                    control.checked = false;
                } else if (control.tagName === 'SELECT') {
                    control.value = control.querySelector('option').value;
                }
            });
            
            if (this.components.accessibility) {
                this.components.accessibility.announce('Settings reset to defaults', 'polite');
            }
        }
    }
    
    handleFocusChange(event) {
        // Add focus indicators for keyboard navigation
        if (event.target.matches('button, a, input, select, textarea, [tabindex]')) {
            document.body.classList.add('keyboard-navigation');
        }
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden
            console.log('Page hidden');
        } else {
            // Page is visible
            console.log('Page visible');
            
            // Check for updates when page becomes visible
            if (this.components.pwa) {
                this.components.pwa.checkForUpdates();
            }
        }
    }
    
    // Utility methods
    addEventListener(event, handler) {
        window.addEventListener(event, handler);
        
        // Store for cleanup
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(handler);
    }
    
    dispatchEvent(eventName, detail = {}) {
        window.dispatchEvent(new CustomEvent(eventName, { detail }));
    }
    
    isInitialized() {
        return this.initialized;
    }
    
    getComponent(name) {
        return this.components[name];
    }
    
    // Cleanup method
    destroy() {
        // Remove event listeners
        this.eventListeners.forEach((handlers, event) => {
            handlers.forEach(handler => {
                window.removeEventListener(event, handler);
            });
        });
        
        // Clean up components
        Object.values(this.components).forEach(component => {
            if (component && typeof component.destroy === 'function') {
                component.destroy();
            }
        });
        
        // Remove UI elements
        const elementsToRemove = [
            '#unified-controls',
            '#global-loading-overlay',
            '.keyboard-help-modal'
        ];
        
        elementsToRemove.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                element.remove();
            }
        });
    }
}

// Add integration-specific CSS
const integrationStyles = `
<style>
/* Ripple effect animation */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Card loading state */
.card-loading {
    position: relative;
    pointer-events: none;
}

.card-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    z-index: 10;
}

/* Button loading state */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading .spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Global loading overlay */
.loading-content {
    text-align: center;
    color: var(--text-primary);
}

.loading-content .spinner {
    margin-bottom: var(--spacing-md);
}

.loading-message {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

/* Scroll animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Page transitions */
.page-loading {
    opacity: 0;
}

.page-loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* Table enhancements */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

th[data-sort] {
    position: relative;
    user-select: none;
}

th[data-sort]:after {
    content: '↕️';
    position: absolute;
    right: 8px;
    opacity: 0.5;
}

th[data-sort="asc"]:after {
    content: '↑';
    opacity: 1;
}

th[data-sort="desc"]:after {
    content: '↓';
    opacity: 1;
}

tr.selected {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

/* Unified controls panel */
.unified-controls-panel {
    font-size: 14px;
}

.controls-toggle {
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.controls-content {
    padding: var(--spacing-lg);
    max-height: 80vh;
    overflow-y: auto;
}

.control-section {
    margin-bottom: var(--spacing-lg);
}

.control-section h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 16px;
    border-bottom: 1px solid var(--border-light);
    padding-bottom: var(--spacing-sm);
}

.control-item {
    margin-bottom: var(--spacing-md);
}

.control-item label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 14px;
}

.control-item select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* Keyboard help modal */
.keyboard-help-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.keyboard-help-modal .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
}

.keyboard-help-modal .modal-content {
    position: relative;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.keyboard-help-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.keyboard-help-modal .modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.keyboard-help-modal .close-modal {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.keyboard-help-modal .modal-body {
    padding: var(--spacing-lg);
}

.shortcut-group {
    margin-bottom: var(--spacing-lg);
}

.shortcut-group h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 16px;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.shortcut-item:last-child {
    border-bottom: none;
}

kbd {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-sm);
    padding: 2px 6px;
    font-family: var(--font-family-mono);
    font-size: 12px;
    color: var(--text-primary);
}

/* Offline mode styles */
.offline-mode {
    filter: grayscale(0.3);
}

.offline-mode::before {
    content: 'Offline Mode';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--warning-color);
    color: var(--text-primary);
    text-align: center;
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
    z-index: 1000;
}

/* Enhanced focus mode */
.enhanced-focus *:focus {
    outline: 4px solid #ffbf47 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 6px rgba(255, 191, 71, 0.3) !important;
}

/* Reduced motion mode */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .keyboard-help-modal .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }
    
    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}
</style>
`;

// Inject integration styles
document.head.insertAdjacentHTML('beforeend', integrationStyles);

// Initialize enhanced UI integration when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.enhancedUIIntegration = new EnhancedUIIntegration();
    });
} else {
    window.enhancedUIIntegration = new EnhancedUIIntegration();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedUIIntegration;
}