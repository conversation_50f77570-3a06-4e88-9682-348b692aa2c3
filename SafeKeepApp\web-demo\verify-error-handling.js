/**
 * Verification Script for Advanced Error Handling and Recovery System
 * This script verifies that all error handling components are working correctly
 */

async function verifyErrorHandling() {
    console.log('🔍 Verifying Error Handling System...');
    
    const results = {
        timestamp: new Date().toISOString(),
        components: {},
        integration: {},
        functionality: {},
        overall: 'UNKNOWN'
    };

    try {
        // Verify component initialization
        results.components = await verifyComponents();
        
        // Verify integration
        results.integration = await verifyIntegration();
        
        // Verify functionality
        results.functionality = await verifyFunctionality();
        
        // Determine overall status
        const allPassed = Object.values(results.components).every(r => r.status === 'PASS') &&
                         Object.values(results.integration).every(r => r.status === 'PASS') &&
                         Object.values(results.functionality).every(r => r.status === 'PASS');
        
        results.overall = allPassed ? 'PASS' : 'FAIL';
        
        // Display results
        displayResults(results);
        
        return results;
        
    } catch (error) {
        console.error('❌ Verification failed:', error);
        results.overall = 'ERROR';
        results.error = error.message;
        return results;
    }
}

async function verifyComponents() {
    console.log('📦 Verifying component initialization...');
    
    const components = {};
    
    // Verify SafeKeepErrorHandler
    components.errorHandler = {
        status: window.errorHandler instanceof SafeKeepErrorHandler ? 'PASS' : 'FAIL',
        details: window.errorHandler ? 'Initialized' : 'Not found',
        methods: window.errorHandler ? Object.getOwnPropertyNames(Object.getPrototypeOf(window.errorHandler)).length : 0
    };
    
    // Verify NetworkQueueManager
    components.networkQueue = {
        status: window.networkQueue instanceof NetworkQueueManager ? 'PASS' : 'FAIL',
        details: window.networkQueue ? 'Initialized' : 'Not found',
        queueLength: window.networkQueue ? window.networkQueue.getQueueStatus().queueLength : 0
    };
    
    // Verify OfflineManager
    components.offlineManager = {
        status: window.offlineManager instanceof OfflineManager ? 'PASS' : 'FAIL',
        details: window.offlineManager ? 'Initialized' : 'Not found',
        features: window.offlineManager ? window.offlineManager.getStatus().availableFeatures.length : 0
    };
    
    // Verify DemoStateManager
    components.demoStateManager = {
        status: window.demoManager instanceof DemoStateManager ? 'PASS' : 'FAIL',
        details: window.demoManager ? 'Initialized' : 'Not found',
        stateKeys: window.demoManager ? Object.keys(window.demoManager.getState()).length : 0
    };
    
    // Verify ErrorHandlingIntegration
    components.integration = {
        status: window.errorHandlingIntegration instanceof ErrorHandlingIntegration ? 'PASS' : 'FAIL',
        details: window.errorHandlingIntegration ? 'Initialized' : 'Not found',
        initialized: window.errorHandlingIntegration ? window.errorHandlingIntegration.initialized : false
    };
    
    return components;
}

async function verifyIntegration() {
    console.log('🔗 Verifying manager integration...');
    
    const integration = {};
    
    // Check if managers have error handling integration
    const managersToCheck = [
        'authManager',
        'backupManager',
        'restoreManager',
        'realtimeManager',
        'encryptionManager',
        'subscriptionManager',
        'paymentProcessor'
    ];
    
    for (const managerName of managersToCheck) {
        const manager = window[managerName];
        integration[managerName] = {
            status: manager ? 'PASS' : 'SKIP',
            details: manager ? 'Available' : 'Not loaded',
            hasReset: manager && typeof manager.reset === 'function',
            enhanced: false
        };
        
        // Check for enhanced methods (added by integration)
        if (manager) {
            switch (managerName) {
                case 'authManager':
                    integration[managerName].enhanced = typeof manager.setGuestMode === 'function';
                    break;
                case 'backupManager':
                    integration[managerName].enhanced = typeof manager.pauseAll === 'function';
                    break;
                case 'restoreManager':
                    integration[managerName].enhanced = typeof manager.pauseAll === 'function';
                    break;
                case 'realtimeManager':
                    integration[managerName].enhanced = typeof manager.fallbackToPolling === 'function';
                    break;
                case 'subscriptionManager':
                    integration[managerName].enhanced = typeof manager.setFreeTier === 'function';
                    break;
                case 'paymentProcessor':
                    integration[managerName].enhanced = typeof manager.retryLastPayment === 'function';
                    break;
            }
        }
    }
    
    return integration;
}

async function verifyFunctionality() {
    console.log('⚙️ Verifying functionality...');
    
    const functionality = {};
    
    // Test error handling
    try {
        const testError = new Error('Test error for verification');
        await window.errorHandler.handleError(testError, 'DEMO_STATE', {
            operation: 'verification_test',
            testId: 'func_test_1'
        });
        
        functionality.errorHandling = {
            status: 'PASS',
            details: 'Error handling works correctly'
        };
    } catch (error) {
        functionality.errorHandling = {
            status: 'FAIL',
            details: `Error handling failed: ${error.message}`
        };
    }
    
    // Test network queue
    try {
        const queueId = window.networkQueue.queueApiCall('/api/test-verification', {
            method: 'GET'
        });
        
        functionality.networkQueue = {
            status: queueId ? 'PASS' : 'FAIL',
            details: queueId ? `Queued operation: ${queueId}` : 'Failed to queue operation'
        };
    } catch (error) {
        functionality.networkQueue = {
            status: 'FAIL',
            details: `Network queue failed: ${error.message}`
        };
    }
    
    // Test offline manager
    try {
        window.offlineManager.cacheData('verification_test', { test: true });
        const cachedData = window.offlineManager.getCachedData('verification_test');
        
        functionality.offlineManager = {
            status: cachedData && cachedData.test ? 'PASS' : 'FAIL',
            details: cachedData ? 'Caching works correctly' : 'Caching failed'
        };
    } catch (error) {
        functionality.offlineManager = {
            status: 'FAIL',
            details: `Offline manager failed: ${error.message}`
        };
    }
    
    // Test demo state manager
    try {
        const originalStep = window.demoManager.getState('demo.currentStep');
        window.demoManager.updateState('demo.currentStep', 999);
        const updatedStep = window.demoManager.getState('demo.currentStep');
        window.demoManager.updateState('demo.currentStep', originalStep);
        
        functionality.demoStateManager = {
            status: updatedStep === 999 ? 'PASS' : 'FAIL',
            details: updatedStep === 999 ? 'State management works correctly' : 'State update failed'
        };
    } catch (error) {
        functionality.demoStateManager = {
            status: 'FAIL',
            details: `Demo state manager failed: ${error.message}`
        };
    }
    
    // Test user notifications
    try {
        window.errorHandler.showUserNotification({
            type: 'info',
            title: 'Verification Test',
            message: 'This is a test notification for verification',
            autoHide: true,
            duration: 1000
        });
        
        // Check if notification was added to DOM
        await new Promise(resolve => setTimeout(resolve, 100));
        const notification = document.querySelector('.safekeep-notification');
        
        functionality.userNotifications = {
            status: notification ? 'PASS' : 'FAIL',
            details: notification ? 'Notifications work correctly' : 'Notification not displayed'
        };
        
        // Clean up
        if (notification) {
            setTimeout(() => notification.remove(), 1000);
        }
    } catch (error) {
        functionality.userNotifications = {
            status: 'FAIL',
            details: `User notifications failed: ${error.message}`
        };
    }
    
    return functionality;
}

function displayResults(results) {
    console.log('\n📊 Error Handling Verification Results:');
    console.log('=====================================');
    
    console.log(`\n🎯 Overall Status: ${results.overall}`);
    console.log(`📅 Timestamp: ${results.timestamp}`);
    
    console.log('\n📦 Components:');
    Object.entries(results.components).forEach(([name, result]) => {
        const icon = result.status === 'PASS' ? '✅' : '❌';
        console.log(`  ${icon} ${name}: ${result.status} - ${result.details}`);
    });
    
    console.log('\n🔗 Integration:');
    Object.entries(results.integration).forEach(([name, result]) => {
        const icon = result.status === 'PASS' ? '✅' : result.status === 'SKIP' ? '⏭️' : '❌';
        const enhanced = result.enhanced ? ' (Enhanced)' : '';
        console.log(`  ${icon} ${name}: ${result.status} - ${result.details}${enhanced}`);
    });
    
    console.log('\n⚙️ Functionality:');
    Object.entries(results.functionality).forEach(([name, result]) => {
        const icon = result.status === 'PASS' ? '✅' : '❌';
        console.log(`  ${icon} ${name}: ${result.status} - ${result.details}`);
    });
    
    // Display summary
    const componentsPassed = Object.values(results.components).filter(r => r.status === 'PASS').length;
    const componentsTotal = Object.keys(results.components).length;
    
    const integrationPassed = Object.values(results.integration).filter(r => r.status === 'PASS').length;
    const integrationTotal = Object.keys(results.integration).length;
    
    const functionalityPassed = Object.values(results.functionality).filter(r => r.status === 'PASS').length;
    const functionalityTotal = Object.keys(results.functionality).length;
    
    console.log('\n📈 Summary:');
    console.log(`  Components: ${componentsPassed}/${componentsTotal} passed`);
    console.log(`  Integration: ${integrationPassed}/${integrationTotal} passed`);
    console.log(`  Functionality: ${functionalityPassed}/${functionalityTotal} passed`);
    
    if (results.overall === 'PASS') {
        console.log('\n🎉 All error handling systems are working correctly!');
    } else {
        console.log('\n⚠️ Some error handling systems need attention.');
    }
    
    // Show integration status if available
    if (window.errorHandlingIntegration) {
        const integrationStatus = window.errorHandlingIntegration.getIntegrationStatus();
        console.log('\n🔧 Integration Status:', integrationStatus);
    }
}

// Auto-run verification if this script is loaded directly
if (typeof window !== 'undefined') {
    window.verifyErrorHandling = verifyErrorHandling;
    
    // Run verification after a short delay to ensure all components are loaded
    setTimeout(() => {
        if (window.errorHandler && window.networkQueue && window.offlineManager && window.demoManager) {
            console.log('🚀 Auto-running error handling verification...');
            verifyErrorHandling();
        } else {
            console.log('⏳ Error handling components not fully loaded yet. Run verifyErrorHandling() manually.');
        }
    }, 2000);
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { verifyErrorHandling };
}