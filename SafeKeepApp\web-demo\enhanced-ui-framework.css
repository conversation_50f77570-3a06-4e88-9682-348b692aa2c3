/* Enhanced UI Framework for SafeKeep Web Demo */
/* Responsive Design, Dark/Light Theme, Accessibility, PWA Features */

/* CSS Custom Properties for Theme Support */
:root {
  /* Light Theme Colors */
  --primary-color: #4facfe;
  --primary-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --secondary-color: #667eea;
  --accent-color: #764ba2;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* Text Colors */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --text-inverse: #ffffff;
  
  /* Border Colors */
  --border-light: #e9ecef;
  --border-medium: #dee2e6;
  --border-dark: #adb5bd;
  
  /* Status Colors */
  --success-color: #28a745;
  --success-bg: #d4edda;
  --success-border: #c3e6cb;
  --warning-color: #ffc107;
  --warning-bg: #fff3cd;
  --warning-border: #ffeaa7;
  --error-color: #dc3545;
  --error-bg: #f8d7da;
  --error-border: #f5c6cb;
  --info-color: #17a2b8;
  --info-bg: #d1ecf1;
  --info-border: #bee5eb;
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 8px rgba(0,0,0,0.12);
  --shadow-lg: 0 8px 16px rgba(0,0,0,0.15);
  --shadow-xl: 0 20px 40px rgba(0,0,0,0.1);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;
  --radius-full: 50%;
  
  /* Typography */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --bg-gradient: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --text-inverse: #1a1a1a;
  
  --border-light: #4a5568;
  --border-medium: #2d3748;
  --border-dark: #1a202c;
  
  --success-bg: #22543d;
  --success-border: #2f855a;
  --warning-bg: #744210;
  --warning-border: #b7791f;
  --error-bg: #742a2a;
  --error-border: #c53030;
  --info-bg: #2c5282;
  --info-border: #3182ce;
}

/* High Contrast Theme for Accessibility */
[data-theme="high-contrast"] {
  --primary-color: #0000ff;
  --bg-primary: #ffffff;
  --bg-secondary: #ffffff;
  --text-primary: #000000;
  --text-secondary: #000000;
  --border-light: #000000;
  --border-medium: #000000;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--bg-gradient);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus Management for Accessibility */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Link for Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--text-inverse);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-sm);
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Container System */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-md);
}

/* Grid System */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
  flex: 1;
  padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--spacing-md) 0;
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
}

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.font-mono { font-family: var(--font-family-mono); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  line-height: var(--line-height-tight);
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--primary-gradient);
  color: var(--text-inverse);
  min-height: 44px; /* Accessibility: minimum touch target */
  position: relative;
  overflow: hidden;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Button Variants */
.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-success {
  background: var(--success-color);
  color: var(--text-inverse);
}

.btn-warning {
  background: var(--warning-color);
  color: var(--text-primary);
}

.btn-danger {
  background: var(--error-color);
  color: var(--text-inverse);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--text-inverse);
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

.btn-block {
  width: 100%;
}

/* Form Controls */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: border-color var(--transition-normal), box-shadow var(--transition-normal);
  min-height: 44px; /* Accessibility: minimum touch target */
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
}

.form-control:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--bg-tertiary);
}

.form-control.is-invalid {
  border-color: var(--error-color);
}

.form-control.is-valid {
  border-color: var(--success-color);
}

textarea.form-control {
  resize: vertical;
  min-height: 120px;
}

.form-text {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.invalid-feedback {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--error-color);
}

.valid-feedback {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--success-color);
}

/* Card System */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
}

.card-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Alert System */
.alert {
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  position: relative;
}

.alert-success {
  color: var(--success-color);
  background: var(--success-bg);
  border-color: var(--success-border);
}

.alert-warning {
  color: var(--warning-color);
  background: var(--warning-bg);
  border-color: var(--warning-border);
}

.alert-danger {
  color: var(--error-color);
  background: var(--error-bg);
  border-color: var(--error-border);
}

.alert-info {
  color: var(--info-color);
  background: var(--info-bg);
  border-color: var(--info-border);
}

.alert-dismissible {
  padding-right: calc(var(--spacing-lg) + 40px);
}

.alert-dismiss {
  position: absolute;
  top: 0;
  right: 0;
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  color: inherit;
}

/* Progress Bars */
.progress {
  display: flex;
  height: 12px;
  overflow: hidden;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  margin: var(--spacing-sm) 0;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--text-inverse);
  text-align: center;
  white-space: nowrap;
  background: var(--primary-gradient);
  transition: width var(--transition-slow);
  border-radius: var(--radius-sm);
}

.progress-bar-striped {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% { background-position-x: 1rem; }
}

/* Badge System */
.badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--radius-full);
  background: var(--primary-color);
  color: var(--text-inverse);
}

.badge-success { background: var(--success-color); }
.badge-warning { background: var(--warning-color); color: var(--text-primary); }
.badge-danger { background: var(--error-color); }
.badge-info { background: var(--info-color); }
.badge-secondary { background: var(--bg-tertiary); color: var(--text-primary); }

/* Spinner/Loading */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

.spinner-lg {
  width: 40px;
  height: 40px;
  border-width: 4px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Utility Classes */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

.gap-xs { gap: var(--spacing-xs) !important; }
.gap-sm { gap: var(--spacing-sm) !important; }
.gap-md { gap: var(--spacing-md) !important; }
.gap-lg { gap: var(--spacing-lg) !important; }
.gap-xl { gap: var(--spacing-xl) !important; }

/* Margin and Padding Utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* Responsive Utilities */
@media (max-width: 576px) {
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
  
  .flex-sm-column { flex-direction: column !important; }
  .text-sm-center { text-align: center !important; }
  
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (max-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
  
  .flex-md-column { flex-direction: column !important; }
  .text-md-center { text-align: center !important; }
  
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (max-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
  
  .flex-lg-column { flex-direction: column !important; }
  .text-lg-center { text-align: center !important; }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn var(--transition-slow) ease-in;
}

.slide-up {
  animation: slideUp var(--transition-slow) ease-out;
}

.scale-in {
  animation: scaleIn var(--transition-normal) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-medium: #000000;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.5);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.5);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.5);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .container {
    box-shadow: none;
    border-radius: 0;
  }
  
  .btn {
    border: 1px solid #000;
    background: transparent !important;
    color: #000 !important;
  }
}