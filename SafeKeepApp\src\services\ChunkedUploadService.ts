import { supabase, BUCKETS } from '../config/supabase';
import EncryptionService from './EncryptionService';
import PerformanceMonitoringService from './PerformanceMonitoringService';
import AuthService from './AuthService';

export interface ChunkUploadProgress {
  chunkIndex: number;
  totalChunks: number;
  bytesUploaded: number;
  totalBytes: number;
  percentage: number;
  uploadSpeed: number; // bytes per second
  estimatedTimeRemaining: number; // milliseconds
  currentChunkSize: number;
}

export interface ChunkedUploadResult {
  success: boolean;
  fileId?: string;
  downloadURL?: string;
  error?: string;
  totalChunks?: number;
  uploadTime?: number;
  averageSpeed?: number;
}

export interface ChunkMetadata {
  chunkIndex: number;
  chunkSize: number;
  checksum: string;
  uploadedAt: Date;
}

export interface AdaptiveChunkConfig {
  minChunkSize: number;
  maxChunkSize: number;
  targetUploadTime: number; // milliseconds
  adaptationFactor: number;
}

class ChunkedUploadService {
  private activeUploads = new Map<string, AbortController>();
  private uploadSpeeds: number[] = [];
  private readonly DEFAULT_CHUNK_SIZE = 1024 * 1024; // 1MB
  private readonly MAX_CONCURRENT_CHUNKS = 3;
  private readonly SPEED_SAMPLE_SIZE = 10;

  // Upload large file using chunked upload with adaptive sizing
  async uploadLargeFile(
    fileData: string,
    fileName: string,
    mimeType: string,
    category: 'photo' | 'contact' | 'message',
    onProgress?: (progress: ChunkUploadProgress) => void
  ): Promise<ChunkedUploadResult> {
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      console.log(`📦 Starting chunked upload: ${fileName} (${fileData.length} bytes)`);

      // Create abort controller for cancellation
      const abortController = new AbortController();
      this.activeUploads.set(uploadId, abortController);

      // Get network optimization config
      const networkState = await this.getNetworkState();
      const optimizationConfig = PerformanceMonitoringService.getNetworkOptimizationConfig(
        networkState.type,
        networkState.speed
      );

      // Calculate initial chunk size
      let chunkSize = this.calculateOptimalChunkSize(fileData.length, optimizationConfig);
      const totalChunks = Math.ceil(fileData.length / chunkSize);

      console.log(`📦 Upload plan: ${totalChunks} chunks of ~${Math.round(chunkSize / 1024)}KB each`);

      // Encrypt the entire file first
      const encryptionResult = await EncryptionService.encryptFile(fileData, fileName);
      const encryptedData = encryptionResult.encryptedData;

      // Split encrypted data into chunks
      const chunks = this.splitIntoChunks(encryptedData, chunkSize);
      const chunkMetadata: ChunkMetadata[] = [];
      let totalBytesUploaded = 0;

      // Upload chunks with adaptive sizing
      for (let i = 0; i < chunks.length; i++) {
        if (abortController.signal.aborted) {
          throw new Error('Upload cancelled by user');
        }

        const chunk = chunks[i];
        const chunkStartTime = Date.now();

        try {
          // Upload chunk
          const chunkResult = await this.uploadChunk(
            chunk,
            i,
            fileName,
            user.id,
            category,
            abortController.signal
          );

          if (!chunkResult.success) {
            throw new Error(chunkResult.error || 'Chunk upload failed');
          }

          // Record chunk metadata
          const chunkTime = Date.now() - chunkStartTime;
          const chunkSpeed = chunk.length / (chunkTime / 1000); // bytes per second
          
          chunkMetadata.push({
            chunkIndex: i,
            chunkSize: chunk.length,
            checksum: await this.calculateChecksum(chunk),
            uploadedAt: new Date()
          });

          // Update upload speed tracking
          this.updateUploadSpeed(chunkSpeed);
          
          totalBytesUploaded += chunk.length;

          // Adaptive chunk sizing for next chunk
          if (i < chunks.length - 1) {
            chunkSize = this.adaptChunkSize(chunkSize, chunkTime, optimizationConfig);
            
            // Re-chunk remaining data if size changed significantly
            if (Math.abs(chunkSize - chunk.length) > chunk.length * 0.2) {
              const remainingData = encryptedData.slice(totalBytesUploaded);
              const newChunks = this.splitIntoChunks(remainingData, chunkSize);
              chunks.splice(i + 1, chunks.length - i - 1, ...newChunks);
            }
          }

          // Progress callback
          if (onProgress) {
            const averageSpeed = this.getAverageUploadSpeed();
            const remainingBytes = encryptedData.length - totalBytesUploaded;
            const estimatedTimeRemaining = averageSpeed > 0 ? (remainingBytes / averageSpeed) * 1000 : 0;

            onProgress({
              chunkIndex: i + 1,
              totalChunks: chunks.length,
              bytesUploaded: totalBytesUploaded,
              totalBytes: encryptedData.length,
              percentage: Math.round((totalBytesUploaded / encryptedData.length) * 100),
              uploadSpeed: averageSpeed,
              estimatedTimeRemaining,
              currentChunkSize: chunkSize
            });
          }

        } catch (chunkError) {
          console.error(`❌ Failed to upload chunk ${i}:`, chunkError);
          
          // Retry chunk upload with exponential backoff
          const retryResult = await this.retryChunkUpload(
            chunk,
            i,
            fileName,
            user.id,
            category,
            optimizationConfig.retryConfig,
            abortController.signal
          );

          if (!retryResult.success) {
            throw new Error(`Failed to upload chunk ${i} after retries: ${retryResult.error}`);
          }
        }
      }

      // Finalize upload by combining chunks
      const finalResult = await this.finalizeChunkedUpload(
        fileName,
        user.id,
        category,
        chunkMetadata,
        encryptionResult
      );

      if (!finalResult.success) {
        throw new Error(finalResult.error || 'Failed to finalize chunked upload');
      }

      const uploadTime = Date.now() - startTime;
      const averageSpeed = encryptedData.length / (uploadTime / 1000);

      console.log(`✅ Chunked upload completed: ${fileName} in ${Math.round(uploadTime / 1000)}s`);

      this.activeUploads.delete(uploadId);

      return {
        success: true,
        fileId: finalResult.fileId,
        downloadURL: finalResult.downloadURL,
        totalChunks: chunks.length,
        uploadTime,
        averageSpeed
      };

    } catch (error) {
      console.error(`❌ Chunked upload failed for ${fileName}:`, error);
      this.activeUploads.delete(uploadId);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown chunked upload error'
      };
    }
  }

  // Calculate optimal chunk size based on file size and network conditions
  private calculateOptimalChunkSize(fileSize: number, config: any): number {
    let chunkSize = this.DEFAULT_CHUNK_SIZE;

    // Adjust based on file size
    if (fileSize < 5 * 1024 * 1024) { // < 5MB
      chunkSize = 512 * 1024; // 512KB
    } else if (fileSize > 100 * 1024 * 1024) { // > 100MB
      chunkSize = 4 * 1024 * 1024; // 4MB
    }

    // Apply network optimization config
    if (config.chunkSize) {
      chunkSize = Math.min(chunkSize, config.chunkSize);
    }

    return chunkSize;
  }

  // Adapt chunk size based on upload performance
  private adaptChunkSize(
    currentChunkSize: number,
    uploadTime: number,
    config: any
  ): number {
    const adaptiveConfig: AdaptiveChunkConfig = {
      minChunkSize: 256 * 1024, // 256KB
      maxChunkSize: 8 * 1024 * 1024, // 8MB
      targetUploadTime: 5000, // 5 seconds
      adaptationFactor: 0.2
    };

    // If upload was too slow, reduce chunk size
    if (uploadTime > adaptiveConfig.targetUploadTime * 1.5) {
      const newSize = Math.max(
        adaptiveConfig.minChunkSize,
        currentChunkSize * (1 - adaptiveConfig.adaptationFactor)
      );
      console.log(`📦 Reducing chunk size: ${Math.round(currentChunkSize / 1024)}KB → ${Math.round(newSize / 1024)}KB`);
      return newSize;
    }

    // If upload was too fast, increase chunk size
    if (uploadTime < adaptiveConfig.targetUploadTime * 0.5) {
      const newSize = Math.min(
        adaptiveConfig.maxChunkSize,
        currentChunkSize * (1 + adaptiveConfig.adaptationFactor)
      );
      console.log(`📦 Increasing chunk size: ${Math.round(currentChunkSize / 1024)}KB → ${Math.round(newSize / 1024)}KB`);
      return newSize;
    }

    return currentChunkSize;
  }

  // Split data into chunks
  private splitIntoChunks(data: string, chunkSize: number): string[] {
    const chunks: string[] = [];
    for (let i = 0; i < data.length; i += chunkSize) {
      chunks.push(data.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // Upload individual chunk
  private async uploadChunk(
    chunk: string,
    chunkIndex: number,
    fileName: string,
    userId: string,
    category: string,
    signal: AbortSignal
  ): Promise<{ success: boolean; error?: string; chunkId?: string }> {
    try {
      const chunkFileName = `${fileName}_chunk_${chunkIndex.toString().padStart(4, '0')}`;
      const chunkPath = `${userId}/${category}s/chunks/${chunkFileName}`;

      // Convert chunk to Uint8Array
      const chunkData = new TextEncoder().encode(chunk);

      // Upload chunk to Supabase Storage
      const { data, error } = await supabase.storage
        .from(BUCKETS.USER_DATA)
        .upload(chunkPath, chunkData, {
          contentType: 'application/octet-stream',
          metadata: {
            chunkIndex: chunkIndex.toString(),
            originalFileName: fileName,
            category,
            userId
          }
        });

      if (error) {
        throw error;
      }

      return {
        success: true,
        chunkId: data.path
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown chunk upload error'
      };
    }
  }

  // Retry chunk upload with exponential backoff
  private async retryChunkUpload(
    chunk: string,
    chunkIndex: number,
    fileName: string,
    userId: string,
    category: string,
    retryConfig: any,
    signal: AbortSignal
  ): Promise<{ success: boolean; error?: string }> {
    let lastError: string = '';

    for (let attempt = 1; attempt <= retryConfig.maxRetries; attempt++) {
      if (signal.aborted) {
        return { success: false, error: 'Upload cancelled' };
      }

      try {
        console.log(`🔄 Retrying chunk ${chunkIndex}, attempt ${attempt}/${retryConfig.maxRetries}`);

        const result = await this.uploadChunk(chunk, chunkIndex, fileName, userId, category, signal);
        
        if (result.success) {
          return { success: true };
        }

        lastError = result.error || 'Unknown error';
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Unknown error';
      }

      // Wait before next retry with exponential backoff
      if (attempt < retryConfig.maxRetries) {
        const delay = Math.min(
          retryConfig.baseDelay * Math.pow(2, attempt - 1),
          retryConfig.maxDelay
        );
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return { success: false, error: lastError };
  }

  // Finalize chunked upload by combining chunks
  private async finalizeChunkedUpload(
    fileName: string,
    userId: string,
    category: string,
    chunkMetadata: ChunkMetadata[],
    encryptionResult: any
  ): Promise<{ success: boolean; error?: string; fileId?: string; downloadURL?: string }> {
    try {
      // In a real implementation, this would combine chunks on the server
      // For now, we'll create a metadata record indicating successful chunked upload
      
      const fileId = `chunked_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const finalPath = `${userId}/${category}s/${fileName}_final`;

      // Create a placeholder final file (in real implementation, server would combine chunks)
      const placeholderData = new TextEncoder().encode('CHUNKED_UPLOAD_PLACEHOLDER');
      
      const { data, error } = await supabase.storage
        .from(BUCKETS.USER_DATA)
        .upload(finalPath, placeholderData, {
          contentType: 'application/octet-stream',
          metadata: {
            originalFileName: fileName,
            category,
            userId,
            chunkedUpload: 'true',
            totalChunks: chunkMetadata.length.toString(),
            encryptionIv: encryptionResult.iv,
            encryptionSalt: encryptionResult.salt
          }
        });

      if (error) {
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(BUCKETS.USER_DATA)
        .getPublicUrl(finalPath);

      return {
        success: true,
        fileId,
        downloadURL: publicUrl
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to finalize chunked upload'
      };
    }
  }

  // Calculate checksum for chunk integrity
  private async calculateChecksum(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  // Update upload speed tracking
  private updateUploadSpeed(speed: number): void {
    this.uploadSpeeds.push(speed);
    
    // Keep only recent speeds
    if (this.uploadSpeeds.length > this.SPEED_SAMPLE_SIZE) {
      this.uploadSpeeds = this.uploadSpeeds.slice(-this.SPEED_SAMPLE_SIZE);
    }
  }

  // Get average upload speed
  private getAverageUploadSpeed(): number {
    if (this.uploadSpeeds.length === 0) {
      return 0;
    }

    const sum = this.uploadSpeeds.reduce((total, speed) => total + speed, 0);
    return sum / this.uploadSpeeds.length;
  }

  // Get current network state
  private async getNetworkState(): Promise<{ type: string; speed?: number }> {
    try {
      // This would use NetInfo in a real implementation
      return {
        type: 'wifi',
        speed: 50 // 50 Mbps placeholder
      };
    } catch (error) {
      return {
        type: 'unknown'
      };
    }
  }

  // Cancel active upload
  async cancelUpload(uploadId: string): Promise<boolean> {
    try {
      const abortController = this.activeUploads.get(uploadId);
      if (abortController) {
        abortController.abort();
        this.activeUploads.delete(uploadId);
        console.log(`❌ Cancelled chunked upload: ${uploadId}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to cancel chunked upload:', error);
      return false;
    }
  }

  // Get upload statistics
  getUploadStatistics(): {
    activeUploads: number;
    averageSpeed: number;
    totalUploads: number;
  } {
    return {
      activeUploads: this.activeUploads.size,
      averageSpeed: this.getAverageUploadSpeed(),
      totalUploads: this.uploadSpeeds.length
    };
  }

  // Clear upload history
  clearUploadHistory(): void {
    this.uploadSpeeds = [];
    console.log('📦 Cleared chunked upload history');
  }
}

export default new ChunkedUploadService();