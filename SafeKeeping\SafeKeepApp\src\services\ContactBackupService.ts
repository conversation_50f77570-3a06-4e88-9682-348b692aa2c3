import { Platform, PermissionsAndroid } from 'react-native';
import Contacts from 'react-native-contacts';
import CloudStorageService from './CloudStorageService';
import AuthService from './AuthService';

export interface ContactData {
  recordID: string;
  displayName: string;
  givenName: string;
  familyName: string;
  phoneNumbers: Array<{
    label: string;
    number: string;
  }>;
  emailAddresses: Array<{
    label: string;
    email: string;
  }>;
  postalAddresses: Array<{
    label: string;
    formattedAddress: string;
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  }>;
  birthday?: {
    year: number;
    month: number;
    day: number;
  };
  company: string;
  jobTitle: string;
  note: string;
  thumbnailPath?: string;
  hasThumbnail: boolean;
  isStarred: boolean;
  lastModified: number;
}

export interface ContactBackupProgress {
  totalContacts: number;
  processedContacts: number;
  currentContact: string;
  percentage: number;
  status: 'scanning' | 'processing' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface ContactBackupResult {
  success: boolean;
  totalContacts: number;
  backedUpContacts: number;
  skippedContacts: number;
  duplicatesFound: number;
  errors: string[];
  duration: number;
}

class ContactBackupService {
  private isBackupRunning = false;
  private shouldPauseBackup = false;
  private progressCallback?: (progress: ContactBackupProgress) => void;

  // Request contacts permission
  async requestContactsPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_CONTACTS,
          {
            title: '📞 Contact Access Permission',
            message: 'SafeKeep needs access to your contacts to back them up safely.\n\nThis helps you:\n• Never lose important phone numbers\n• Restore contacts if you get a new phone\n• Keep family and friends\' numbers safe',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'Allow',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        // iOS permission is handled by react-native-contacts
        return true;
      }
    } catch (error) {
      console.error('Error requesting contacts permission:', error);
      return false;
    }
  }

  // Scan device contacts
  async scanContacts(): Promise<ContactData[]> {
    try {
      console.log('📞 Starting contacts scan...');

      // Check permission first
      const hasPermission = await this.requestContactsPermission();
      if (!hasPermission) {
        throw new Error('Contacts permission denied');
      }

      // Get all contacts
      const contacts = await Contacts.getAll();
      
      const contactData: ContactData[] = contacts.map(contact => ({
        recordID: contact.recordID,
        displayName: contact.displayName || `${contact.givenName} ${contact.familyName}`.trim() || 'Unknown Contact',
        givenName: contact.givenName || '',
        familyName: contact.familyName || '',
        phoneNumbers: contact.phoneNumbers || [],
        emailAddresses: contact.emailAddresses || [],
        postalAddresses: contact.postalAddresses || [],
        birthday: contact.birthday,
        company: contact.company || '',
        jobTitle: contact.jobTitle || '',
        note: contact.note || '',
        thumbnailPath: contact.thumbnailPath,
        hasThumbnail: contact.hasThumbnail || false,
        isStarred: contact.isStarred || false,
        lastModified: Date.now()
      }));

      console.log(`📞 Found ${contactData.length} contacts`);
      return contactData;
    } catch (error) {
      console.error('Error scanning contacts:', error);
      throw new Error('Failed to scan contacts. Please check permissions.');
    }
  }

  // Detect duplicate contacts based on phone numbers and emails
  detectDuplicates(contacts: ContactData[]): ContactData[] {
    console.log('🔍 Starting contact duplicate detection...');
    
    const uniqueContacts: ContactData[] = [];
    const seenIdentifiers = new Set<string>();

    for (const contact of contacts) {
      // Create identifier from phone numbers and emails
      const phoneNumbers = contact.phoneNumbers.map(p => p.number.replace(/\D/g, '')); // Remove non-digits
      const emails = contact.emailAddresses.map(e => e.email.toLowerCase());
      
      // Create unique identifier
      const identifiers = [...phoneNumbers, ...emails].filter(id => id.length > 0);
      
      if (identifiers.length === 0) {
        // Contact with no phone or email - use name as identifier
        const nameId = `name_${contact.displayName.toLowerCase().replace(/\s+/g, '_')}`;
        if (!seenIdentifiers.has(nameId)) {
          seenIdentifiers.add(nameId);
          uniqueContacts.push(contact);
        } else {
          console.log(`📞 Duplicate found (by name): ${contact.displayName}`);
        }
        continue;
      }

      // Check if any identifier has been seen before
      const isDuplicate = identifiers.some(id => seenIdentifiers.has(id));
      
      if (!isDuplicate) {
        // Add all identifiers to seen set
        identifiers.forEach(id => seenIdentifiers.add(id));
        uniqueContacts.push(contact);
      } else {
        console.log(`📞 Duplicate found: ${contact.displayName}`);
      }
    }

    console.log(`🔍 Duplicate detection complete. ${uniqueContacts.length} unique contacts found.`);
    return uniqueContacts;
  }

  // Backup contacts to cloud storage
  async backupContacts(
    contacts: ContactData[],
    onProgress?: (progress: ContactBackupProgress) => void
  ): Promise<ContactBackupResult> {
    if (this.isBackupRunning) {
      throw new Error('Contact backup is already running');
    }

    // Check authentication
    const user = AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User authentication required for backup');
    }

    this.isBackupRunning = true;
    this.shouldPauseBackup = false;
    this.progressCallback = onProgress;

    const startTime = Date.now();
    let backedUpContacts = 0;
    let skippedContacts = 0;
    const errors: string[] = [];

    try {
      console.log(`🚀 Starting backup of ${contacts.length} contacts...`);

      // Step 1: Detect duplicates
      if (onProgress) {
        onProgress({
          totalContacts: contacts.length,
          processedContacts: 0,
          currentContact: 'Detecting duplicates...',
          percentage: 0,
          status: 'processing'
        });
      }

      const uniqueContacts = this.detectDuplicates(contacts);
      const duplicatesFound = contacts.length - uniqueContacts.length;

      // Step 2: Create a single backup file with all contacts
      const contactsBackupData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        totalContacts: uniqueContacts.length,
        contacts: uniqueContacts
      };

      const backupFileName = `contacts_backup_${new Date().toISOString().split('T')[0]}.json`;
      const backupDataString = JSON.stringify(contactsBackupData, null, 2);

      if (onProgress) {
        onProgress({
          totalContacts: uniqueContacts.length,
          processedContacts: 0,
          currentContact: 'Uploading contacts backup...',
          percentage: 50,
          status: 'uploading'
        });
      }

      // Upload the contacts backup file
      const uploadResult = await CloudStorageService.uploadFile(
        backupDataString,
        backupFileName,
        'application/json',
        'contact',
        (progress) => {
          if (onProgress) {
            onProgress({
              totalContacts: uniqueContacts.length,
              processedContacts: Math.round((progress.percentage / 100) * uniqueContacts.length),
              currentContact: 'Uploading contacts backup...',
              percentage: 50 + (progress.percentage / 2), // 50-100%
              status: 'uploading'
            });
          }
        }
      );

      if (uploadResult.success) {
        backedUpContacts = uniqueContacts.length;
        console.log(`✅ Successfully backed up ${backedUpContacts} contacts`);
      } else {
        errors.push(`Failed to upload contacts backup: ${uploadResult.error}`);
        skippedContacts = uniqueContacts.length;
      }

      // Final progress update
      if (onProgress) {
        onProgress({
          totalContacts: uniqueContacts.length,
          processedContacts: uniqueContacts.length,
          currentContact: 'Backup completed!',
          percentage: 100,
          status: 'completed'
        });
      }

      const duration = Date.now() - startTime;
      const result: ContactBackupResult = {
        success: errors.length === 0,
        totalContacts: contacts.length,
        backedUpContacts,
        skippedContacts,
        duplicatesFound,
        errors,
        duration
      };

      console.log('🎉 Contact backup completed:', result);
      return result;

    } catch (error) {
      console.error('❌ Contact backup failed:', error);
      
      if (onProgress) {
        onProgress({
          totalContacts: contacts.length,
          processedContacts: 0,
          currentContact: '',
          percentage: 0,
          status: 'error',
          error: error.message
        });
      }

      return {
        success: false,
        totalContacts: contacts.length,
        backedUpContacts,
        skippedContacts,
        duplicatesFound: 0,
        errors: [error.message],
        duration: Date.now() - startTime
      };
    } finally {
      this.isBackupRunning = false;
      this.progressCallback = undefined;
    }
  }

  // Restore contacts from backup
  async restoreContacts(): Promise<{ success: boolean; contacts?: ContactData[]; error?: string }> {
    try {
      console.log('📞 Starting contact restore...');

      // Get user's contact backup files
      const files = await CloudStorageService.getUserFiles('contact');
      
      if (files.length === 0) {
        return { success: false, error: 'No contact backup found' };
      }

      // Get the most recent backup
      const latestBackup = files.sort((a, b) => 
        new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
      )[0];

      // Download and decrypt the backup
      const downloadResult = await CloudStorageService.downloadFile(latestBackup.id);
      
      if (!downloadResult.success) {
        return { success: false, error: downloadResult.error };
      }

      // Parse the backup data
      const backupData = JSON.parse(downloadResult.data!);
      const contacts = backupData.contacts as ContactData[];

      console.log(`✅ Successfully restored ${contacts.length} contacts`);
      return { success: true, contacts };

    } catch (error) {
      console.error('❌ Contact restore failed:', error);
      return { success: false, error: error.message };
    }
  }

  // Get backup statistics
  async getBackupStats(): Promise<{
    totalBackups: number;
    lastBackupDate?: Date;
    totalContacts: number;
  }> {
    try {
      const files = await CloudStorageService.getUserFiles('contact');
      
      if (files.length === 0) {
        return { totalBackups: 0, totalContacts: 0 };
      }

      const latestBackup = files.sort((a, b) => 
        new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
      )[0];

      // Try to get contact count from latest backup
      let totalContacts = 0;
      try {
        const downloadResult = await CloudStorageService.downloadFile(latestBackup.id);
        if (downloadResult.success) {
          const backupData = JSON.parse(downloadResult.data!);
          totalContacts = backupData.totalContacts || 0;
        }
      } catch (error) {
        console.warn('Could not read backup file for stats:', error);
      }

      return {
        totalBackups: files.length,
        lastBackupDate: new Date(latestBackup.uploaded_at),
        totalContacts
      };
    } catch (error) {
      console.error('Failed to get backup stats:', error);
      return { totalBackups: 0, totalContacts: 0 };
    }
  }

  // Control functions
  pauseBackup(): void {
    this.shouldPauseBackup = true;
    console.log('⏸️ Contact backup paused by user');
  }

  resumeBackup(): void {
    this.shouldPauseBackup = false;
    console.log('▶️ Contact backup resumed by user');
  }

  isRunning(): boolean {
    return this.isBackupRunning;
  }
}

export default new ContactBackupService();
