<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modular Pricing UI - Test Demo</title>
    
    <!-- Import modular pricing styles -->
    <link rel="stylesheet" href="modular-pricing/modular-pricing.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            background: #4facfe;
            color: white;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.2s;
        }
        
        .demo-btn:hover {
            background: #2196f3;
        }
        
        .demo-btn.secondary {
            background: #6c757d;
        }
        
        .demo-btn.secondary:hover {
            background: #5a6268;
        }
        
        .demo-output {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        
        .demo-output h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .demo-output pre {
            margin: 0;
            font-size: 0.85rem;
            color: #666;
            white-space: pre-wrap;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .demo-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>Modular Pricing UI Demo</h1>
        <p>Interactive demonstration of the modular pricing interface</p>
        
        <div class="demo-controls">
            <button class="demo-btn" onclick="selectContacts()">Select Contacts</button>
            <button class="demo-btn" onclick="selectMessages()">Select Messages</button>
            <button class="demo-btn" onclick="selectPhotos()">Select Photos</button>
            <button class="demo-btn" onclick="selectAll()">Select All</button>
            <button class="demo-btn secondary" onclick="clearAll()">Clear All</button>
            <button class="demo-btn secondary" onclick="toggleTheme()">Toggle Theme</button>
        </div>
    </div>

    <!-- Modular Pricing UI will be rendered here -->
    <div id="pricing-container"></div>

    <!-- Demo output -->
    <div class="demo-output">
        <h4>Current Selection:</h4>
        <pre id="demo-output">No services selected</pre>
    </div>

    <!-- Import all JavaScript modules -->
    <script src="modular-pricing/types.js"></script>
    <script src="modular-pricing/config.js"></script>
    <script src="modular-pricing/utils.js"></script>
    <script src="modular-pricing/components/ServiceDetails.js"></script>
    <script src="modular-pricing/components/ServiceCheckbox.js"></script>
    <script src="modular-pricing/components/ServiceSelector.js"></script>
    <script src="modular-pricing/components/PricingCalculator.js"></script>
    <script src="modular-pricing/components/SavingsDisplay.js"></script>
    <script src="modular-pricing/components/PlanRecommendations.js"></script>
    <script src="modular-pricing/components/ModularPricingUI.js"></script>

    <script>
        // Global variables
        let pricingUI = null;
        let currentTheme = 'light';

        // Initialize the demo
        function initDemo() {
            console.log('Initializing Modular Pricing UI Demo...');
            
            // Create the pricing UI
            pricingUI = new ModularPricingUI({
                userId: 'demo-user',
                onSubscriptionSelect: handleSubscriptionSelect,
                onPriceUpdate: handlePriceUpdate,
                initialServices: [],
                theme: currentTheme,
                showRecommendations: true,
                apiBaseUrl: null // Use local calculation
            });

            // Render to container
            const container = document.getElementById('pricing-container');
            container.appendChild(pricingUI.getElement());

            console.log('Modular Pricing UI initialized successfully');
            updateOutput();
        }

        // Event handlers
        function handleSubscriptionSelect(serviceIds, planId, pricingData) {
            console.log('Subscription selected:', {
                services: serviceIds,
                plan: planId,
                pricing: pricingData
            });
            
            alert(`Subscription selected!\nServices: ${serviceIds.join(', ')}\nPlan: ${planId || 'Custom'}\nPrice: ${pricingData ? window.ModularPricingUtils.formatPrice(pricingData.totalPrice) : 'N/A'}/month`);
        }

        function handlePriceUpdate(pricingData) {
            console.log('Price updated:', pricingData);
            updateOutput();
        }

        // Demo control functions
        function selectContacts() {
            if (pricingUI) {
                pricingUI.selectServices(['contacts']);
            }
        }

        function selectMessages() {
            if (pricingUI) {
                pricingUI.selectServices(['messages']);
            }
        }

        function selectPhotos() {
            if (pricingUI) {
                pricingUI.selectServices(['photos']);
            }
        }

        function selectAll() {
            if (pricingUI) {
                pricingUI.selectServices(['contacts', 'messages', 'photos']);
            }
        }

        function clearAll() {
            if (pricingUI) {
                pricingUI.clearSelection();
            }
        }

        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            if (pricingUI) {
                pricingUI.setTheme(currentTheme);
            }
        }

        // Update demo output
        function updateOutput() {
            if (!pricingUI) return;

            const selectedServices = pricingUI.getSelectedServices();
            const currentPricing = pricingUI.getCurrentPricing();
            
            const output = {
                selectedServices: selectedServices,
                serviceCount: selectedServices.length,
                pricing: currentPricing ? {
                    planName: currentPricing.recommendedPlanName,
                    totalPrice: window.ModularPricingUtils.formatPrice(currentPricing.totalPrice),
                    savings: currentPricing.savings > 0 ? window.ModularPricingUtils.formatPrice(currentPricing.savings) : 'None',
                    currency: currentPricing.currency
                } : null,
                theme: currentTheme
            };

            document.getElementById('demo-output').textContent = JSON.stringify(output, null, 2);
        }

        // Error handling
        window.addEventListener('error', function(event) {
            console.error('Demo error:', event.error);
            alert('An error occurred. Check the console for details.');
        });

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Small delay to ensure all scripts are loaded
            setTimeout(initDemo, 100);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (pricingUI) {
                pricingUI.destroy();
            }
        });
    </script>
</body>
</html>