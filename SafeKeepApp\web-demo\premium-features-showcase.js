/**
 * Premium Features Showcase Manager
 * Demonstrates premium features and subscription benefits
 */

class PremiumFeaturesShowcase {
    constructor() {
        this.tierConfig = new SubscriptionTierConfig();
        this.currentTier = 'free';
        this.demoData = this.generateDemoData();
        this.analyticsData = this.generateAnalyticsData();
        this.supportTickets = this.generateSupportTickets();
        this.securityFeatures = this.generateSecurityFeatures();
        this.isInitialized = false;
    }

    /**
     * Initialize the premium features showcase
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            await this.createShowcaseInterface();
            await this.loadUserSubscription();
            this.setupEventListeners();
            this.startDemoUpdates();
            this.isInitialized = true;
            
            console.log('Premium Features Showcase initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Premium Features Showcase:', error);
            throw error;
        }
    }

    /**
     * Create the showcase interface
     */
    async createShowcaseInterface() {
        const container = document.getElementById('premium-features-showcase');
        if (!container) {
            throw new Error('Premium features showcase container not found');
        }

        container.innerHTML = `
            <div class="premium-showcase">
                <div class="showcase-header">
                    <h3>Premium Features Showcase</h3>
                    <div class="current-tier-display">
                        <span class="tier-label">Current Plan:</span>
                        <span class="tier-badge" id="current-tier-badge">Free</span>
                    </div>
                </div>

                <!-- Feature Comparison Matrix -->
                <div class="comparison-section">
                    <h4>Feature Comparison Matrix</h4>
                    <div class="comparison-matrix" id="comparison-matrix">
                        <!-- Matrix will be populated dynamically -->
                    </div>
                </div>

                <!-- Premium-Only Functionality Demonstrations -->
                <div class="premium-demos-section">
                    <h4>Premium Feature Demonstrations</h4>
                    <div class="demo-tabs">
                        <button class="demo-tab active" data-tab="advanced-encryption">Advanced Encryption</button>
                        <button class="demo-tab" data-tab="multi-device-sync">Multi-Device Sync</button>
                        <button class="demo-tab" data-tab="extended-history">Extended History</button>
                        <button class="demo-tab" data-tab="priority-support">Priority Support</button>
                    </div>
                    <div class="demo-content" id="demo-content">
                        <!-- Demo content will be populated dynamically -->
                    </div>
                </div>

                <!-- Usage Analytics and Insights -->
                <div class="analytics-section">
                    <h4>Usage Analytics & Insights</h4>
                    <div class="analytics-dashboard" id="analytics-dashboard">
                        <!-- Analytics will be populated dynamically -->
                    </div>
                </div>

                <!-- Priority Support Simulation -->
                <div class="support-section">
                    <h4>Priority Support Experience</h4>
                    <div class="support-simulation" id="support-simulation">
                        <!-- Support simulation will be populated dynamically -->
                    </div>
                </div>

                <!-- Advanced Security Features -->
                <div class="security-section">
                    <h4>Advanced Security Features</h4>
                    <div class="security-features" id="security-features">
                        <!-- Security features will be populated dynamically -->
                    </div>
                </div>

                <!-- Upgrade Prompt -->
                <div class="upgrade-section">
                    <div class="upgrade-prompt" id="upgrade-prompt">
                        <!-- Upgrade prompt will be populated dynamically -->
                    </div>
                </div>
            </div>
        `;

        await this.populateComparisonMatrix();
        await this.populatePremiumDemos();
        await this.populateAnalyticsDashboard();
        await this.populateSupportSimulation();
        await this.populateSecurityFeatures();
        await this.updateUpgradePrompt();
    } 
   /**
     * Populate the feature comparison matrix
     */
    async populateComparisonMatrix() {
        const container = document.getElementById('comparison-matrix');
        const comparison = this.tierConfig.calculateTierComparison();
        
        let matrixHTML = `
            <div class="matrix-table">
                <div class="matrix-header">
                    <div class="feature-column">Features</div>
                    ${comparison.tiers.map(tier => `
                        <div class="tier-column ${tier.id === this.currentTier ? 'current' : ''}">
                            <div class="tier-name">${tier.name}</div>
                            <div class="tier-price">${this.formatPrice(tier.price)}</div>
                        </div>
                    `).join('')}
                </div>
                <div class="matrix-body">
                    ${comparison.features.map(feature => `
                        <div class="matrix-row">
                            <div class="feature-cell">
                                <div class="feature-name">${feature.name}</div>
                                <div class="feature-description">${feature.description}</div>
                            </div>
                            ${comparison.tiers.map(tier => {
                                const available = feature.availability.find(a => a.tierId === tier.id)?.available;
                                const isCurrentTier = tier.id === this.currentTier;
                                const isAccessible = available && (this.tierConfig.compareTiers(this.currentTier, tier.id) >= 0);
                                
                                return `
                                    <div class="tier-cell ${isCurrentTier ? 'current' : ''}">
                                        <div class="feature-status ${available ? 'available' : 'unavailable'} ${isAccessible ? 'accessible' : 'locked'}">
                                            ${available ? 
                                                (isAccessible ? '✓' : '🔒') : 
                                                '✗'
                                            }
                                        </div>
                                        ${!isAccessible && available ? 
                                            `<button class="try-feature-btn" data-feature="${feature.key}" data-tier="${tier.id}">Try Now</button>` : 
                                            ''
                                        }
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        container.innerHTML = matrixHTML;
    }

    /**
     * Populate premium feature demonstrations
     */
    async populatePremiumDemos() {
        const container = document.getElementById('demo-content');
        
        // Start with advanced encryption demo
        await this.showDemo('advanced-encryption');
    }

    /**
     * Show specific demo
     */
    async showDemo(demoType) {
        const container = document.getElementById('demo-content');
        
        switch (demoType) {
            case 'advanced-encryption':
                container.innerHTML = await this.createAdvancedEncryptionDemo();
                break;
            case 'multi-device-sync':
                container.innerHTML = await this.createMultiDeviceSyncDemo();
                break;
            case 'extended-history':
                container.innerHTML = await this.createExtendedHistoryDemo();
                break;
            case 'priority-support':
                container.innerHTML = await this.createPrioritySupportDemo();
                break;
        }
    }

    /**
     * Create advanced encryption demo
     */
    async createAdvancedEncryptionDemo() {
        const isAccessible = this.tierConfig.hasFeature(this.currentTier, 'advanced_encryption');
        
        return `
            <div class="demo-panel ${isAccessible ? 'accessible' : 'locked'}">
                <div class="demo-header">
                    <h5>Advanced Encryption (AES-256 with Custom Keys)</h5>
                    ${!isAccessible ? '<div class="premium-badge">Premium Feature</div>' : ''}
                </div>
                
                <div class="encryption-demo-content">
                    <div class="encryption-options">
                        <div class="option-group">
                            <label>Encryption Algorithm:</label>
                            <select id="advanced-algorithm" ${!isAccessible ? 'disabled' : ''}>
                                <option value="aes-256-gcm">AES-256-GCM (Premium)</option>
                                <option value="aes-256-cbc">AES-256-CBC (Premium)</option>
                                <option value="chacha20-poly1305">ChaCha20-Poly1305 (Premium)</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>Key Derivation:</label>
                            <select id="key-derivation" ${!isAccessible ? 'disabled' : ''}>
                                <option value="pbkdf2">PBKDF2 (100,000 iterations)</option>
                                <option value="scrypt">Scrypt (Premium)</option>
                                <option value="argon2">Argon2id (Premium)</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>Custom Encryption Key:</label>
                            <input type="password" id="custom-key" placeholder="Enter custom key" ${!isAccessible ? 'disabled' : ''}>
                            <button class="btn secondary" id="generate-key" ${!isAccessible ? 'disabled' : ''}>Generate Secure Key</button>
                        </div>
                    </div>
                    
                    <div class="encryption-demo-area">
                        <div class="demo-input">
                            <label>Sample Data to Encrypt:</label>
                            <textarea id="advanced-demo-data" ${!isAccessible ? 'disabled' : ''}>${isAccessible ? 'This is sensitive data that will be encrypted with advanced algorithms...' : 'Premium feature - upgrade to try'}</textarea>
                        </div>
                        
                        <div class="demo-controls">
                            <button class="btn" id="advanced-encrypt-btn" ${!isAccessible ? 'disabled' : ''}>
                                ${isAccessible ? 'Encrypt with Advanced Algorithm' : 'Upgrade to Use'}
                            </button>
                        </div>
                        
                        <div class="demo-results" id="advanced-encryption-results">
                            ${!isAccessible ? this.createUpgradeOverlay('advanced encryption') : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }    /**

     * Create multi-device sync demo
     */
    async createMultiDeviceSyncDemo() {
        const isAccessible = this.tierConfig.hasFeature(this.currentTier, 'multi_device_sync');
        
        return `
            <div class="demo-panel ${isAccessible ? 'accessible' : 'locked'}">
                <div class="demo-header">
                    <h5>Multi-Device Synchronization</h5>
                    ${!isAccessible ? '<div class="premium-badge">Premium Feature</div>' : ''}
                </div>
                
                <div class="sync-demo-content">
                    <div class="devices-overview">
                        <div class="device-grid">
                            ${this.demoData.devices.map(device => `
                                <div class="device-card ${isAccessible ? device.status : 'locked'}">
                                    <div class="device-icon">${device.icon}</div>
                                    <div class="device-info">
                                        <div class="device-name">${device.name}</div>
                                        <div class="device-status">${isAccessible ? device.status : 'Premium Only'}</div>
                                        <div class="sync-status">
                                            ${isAccessible ? 
                                                `Last sync: ${device.lastSync}` : 
                                                'Sync disabled'
                                            }
                                        </div>
                                    </div>
                                    <div class="sync-indicator ${isAccessible ? device.syncStatus : 'disabled'}"></div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="sync-controls">
                        <button class="btn" id="sync-all-devices" ${!isAccessible ? 'disabled' : ''}>
                            ${isAccessible ? 'Sync All Devices' : 'Upgrade to Sync'}
                        </button>
                        <button class="btn secondary" id="manage-devices" ${!isAccessible ? 'disabled' : ''}>
                            ${isAccessible ? 'Manage Devices' : 'Premium Feature'}
                        </button>
                    </div>
                    
                    <div class="sync-log" id="sync-log">
                        ${!isAccessible ? this.createUpgradeOverlay('multi-device sync') : ''}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create extended history demo
     */
    async createExtendedHistoryDemo() {
        const isAccessible = this.tierConfig.hasFeature(this.currentTier, 'backup_history_extended');
        const tierLimits = this.tierConfig.getTierLimits(this.currentTier);
        
        return `
            <div class="demo-panel ${isAccessible ? 'accessible' : 'locked'}">
                <div class="demo-header">
                    <h5>Extended Backup History</h5>
                    ${!isAccessible ? '<div class="premium-badge">Premium Feature</div>' : ''}
                </div>
                
                <div class="history-demo-content">
                    <div class="history-stats">
                        <div class="stat-card">
                            <div class="stat-label">History Retention</div>
                            <div class="stat-value">${tierLimits.backupHistoryDays} days</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Available Backups</div>
                            <div class="stat-value">${isAccessible ? '47' : '3'}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Storage Used</div>
                            <div class="stat-value">${isAccessible ? '2.4 GB' : '0.1 GB'}</div>
                        </div>
                    </div>
                    
                    <div class="history-timeline">
                        <div class="timeline-header">
                            <h6>Backup History Timeline</h6>
                            <div class="timeline-controls">
                                <select id="history-filter" ${!isAccessible ? 'disabled' : ''}>
                                    <option value="all">All Backups</option>
                                    <option value="successful">Successful Only</option>
                                    <option value="failed">Failed Only</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="timeline-content">
                            ${this.generateHistoryTimeline(isAccessible, tierLimits.backupHistoryDays)}
                        </div>
                    </div>
                    
                    ${!isAccessible ? this.createUpgradeOverlay('extended backup history') : ''}
                </div>
            </div>
        `;
    }

    /**
     * Create priority support demo
     */
    async createPrioritySupportDemo() {
        const isAccessible = this.tierConfig.hasFeature(this.currentTier, 'priority_support');
        
        return `
            <div class="demo-panel ${isAccessible ? 'accessible' : 'locked'}">
                <div class="demo-header">
                    <h5>Priority Support Experience</h5>
                    ${!isAccessible ? '<div class="premium-badge">Premium Feature</div>' : ''}
                </div>
                
                <div class="support-demo-content">
                    <div class="support-comparison">
                        <div class="support-tier ${!isAccessible ? 'current' : ''}">
                            <h6>Standard Support</h6>
                            <div class="support-features">
                                <div class="support-feature">📧 Email support</div>
                                <div class="support-feature">⏱️ 24-48 hour response</div>
                                <div class="support-feature">📚 Knowledge base access</div>
                                <div class="support-feature">🕒 Business hours only</div>
                            </div>
                        </div>
                        
                        <div class="support-tier ${isAccessible ? 'current premium' : 'premium'}">
                            <h6>Priority Support</h6>
                            <div class="support-features">
                                <div class="support-feature">💬 Live chat support</div>
                                <div class="support-feature">⚡ 1-2 hour response</div>
                                <div class="support-feature">📞 Phone support</div>
                                <div class="support-feature">🌍 24/7 availability</div>
                                <div class="support-feature">👨‍💻 Dedicated support agent</div>
                                <div class="support-feature">🔧 Remote assistance</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="support-simulator">
                        <div class="chat-interface">
                            <div class="chat-header">
                                <div class="agent-info">
                                    <div class="agent-avatar">👨‍💻</div>
                                    <div class="agent-details">
                                        <div class="agent-name">${isAccessible ? 'Alex (Premium Support)' : 'Support Bot'}</div>
                                        <div class="agent-status">${isAccessible ? 'Available 24/7' : 'Business hours only'}</div>
                                    </div>
                                </div>
                                <div class="response-time">
                                    Avg response: ${isAccessible ? '< 2 hours' : '24-48 hours'}
                                </div>
                            </div>
                            
                            <div class="chat-messages" id="support-chat">
                                ${this.generateSupportChat(isAccessible)}
                            </div>
                            
                            <div class="chat-input">
                                <input type="text" placeholder="${isAccessible ? 'Type your message...' : 'Upgrade for live chat'}" ${!isAccessible ? 'disabled' : ''}>
                                <button class="btn" ${!isAccessible ? 'disabled' : ''}>
                                    ${isAccessible ? 'Send' : 'Premium Only'}
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    ${!isAccessible ? this.createUpgradeOverlay('priority support') : ''}
                </div>
            </div>
        `;
    }  
  /**
     * Populate analytics dashboard
     */
    async populateAnalyticsDashboard() {
        const container = document.getElementById('analytics-dashboard');
        const isAccessible = this.currentTier !== 'free';
        
        container.innerHTML = `
            <div class="analytics-content ${isAccessible ? 'accessible' : 'locked'}">
                <div class="analytics-overview">
                    <div class="analytics-cards">
                        ${this.analyticsData.overview.map(metric => `
                            <div class="analytics-card">
                                <div class="metric-icon">${metric.icon}</div>
                                <div class="metric-info">
                                    <div class="metric-value">${isAccessible ? metric.value : '---'}</div>
                                    <div class="metric-label">${metric.label}</div>
                                    <div class="metric-change ${metric.trend}">
                                        ${isAccessible ? metric.change : 'Premium Only'}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div class="analytics-charts">
                    <div class="chart-container">
                        <h6>Backup Performance Trends</h6>
                        <div class="chart-placeholder" id="performance-chart">
                            ${isAccessible ? 
                                '<canvas id="performance-canvas"></canvas>' : 
                                this.createUpgradeOverlay('detailed analytics')
                            }
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h6>Storage Usage Analysis</h6>
                        <div class="chart-placeholder" id="storage-chart">
                            ${isAccessible ? 
                                '<canvas id="storage-canvas"></canvas>' : 
                                this.createUpgradeOverlay('storage analytics')
                            }
                        </div>
                    </div>
                </div>
                
                <div class="insights-section">
                    <h6>AI-Powered Insights</h6>
                    <div class="insights-list">
                        ${this.analyticsData.insights.map(insight => `
                            <div class="insight-item ${isAccessible ? '' : 'locked'}">
                                <div class="insight-icon">${insight.icon}</div>
                                <div class="insight-content">
                                    <div class="insight-title">${isAccessible ? insight.title : 'Premium Insight'}</div>
                                    <div class="insight-description">${isAccessible ? insight.description : 'Upgrade to see AI-powered insights'}</div>
                                </div>
                                <div class="insight-action">
                                    <button class="btn secondary small" ${!isAccessible ? 'disabled' : ''}>
                                        ${isAccessible ? 'Learn More' : 'Upgrade'}
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        if (isAccessible) {
            await this.renderAnalyticsCharts();
        }
    }

    /**
     * Populate support simulation
     */
    async populateSupportSimulation() {
        const container = document.getElementById('support-simulation');
        const isAccessible = this.tierConfig.hasFeature(this.currentTier, 'priority_support');
        
        container.innerHTML = `
            <div class="support-content ${isAccessible ? 'accessible' : 'locked'}">
                <div class="support-dashboard">
                    <div class="support-stats">
                        <div class="support-stat">
                            <div class="stat-label">Response Time</div>
                            <div class="stat-value">${isAccessible ? '< 2 hours' : '24-48 hours'}</div>
                        </div>
                        <div class="support-stat">
                            <div class="stat-label">Satisfaction Rate</div>
                            <div class="stat-value">${isAccessible ? '98%' : '85%'}</div>
                        </div>
                        <div class="support-stat">
                            <div class="stat-label">Available Channels</div>
                            <div class="stat-value">${isAccessible ? '4' : '2'}</div>
                        </div>
                    </div>
                    
                    <div class="support-channels">
                        <div class="channel ${isAccessible ? 'available' : 'unavailable'}">
                            <div class="channel-icon">💬</div>
                            <div class="channel-name">Live Chat</div>
                            <div class="channel-status">${isAccessible ? 'Available 24/7' : 'Premium Only'}</div>
                        </div>
                        <div class="channel ${isAccessible ? 'available' : 'unavailable'}">
                            <div class="channel-icon">📞</div>
                            <div class="channel-name">Phone Support</div>
                            <div class="channel-status">${isAccessible ? 'Available 24/7' : 'Premium Only'}</div>
                        </div>
                        <div class="channel available">
                            <div class="channel-icon">📧</div>
                            <div class="channel-name">Email Support</div>
                            <div class="channel-status">Available</div>
                        </div>
                        <div class="channel ${isAccessible ? 'available' : 'unavailable'}">
                            <div class="channel-icon">🔧</div>
                            <div class="channel-name">Remote Assistance</div>
                            <div class="channel-status">${isAccessible ? 'Available' : 'Premium Only'}</div>
                        </div>
                    </div>
                </div>
                
                <div class="ticket-simulator">
                    <h6>Support Ticket Simulation</h6>
                    <div class="ticket-form">
                        <select id="ticket-priority" ${!isAccessible ? 'disabled' : ''}>
                            <option value="low">Low Priority</option>
                            <option value="medium">Medium Priority</option>
                            <option value="high" ${!isAccessible ? 'disabled' : ''}>High Priority (Premium)</option>
                            <option value="urgent" ${!isAccessible ? 'disabled' : ''}>Urgent (Premium)</option>
                        </select>
                        <textarea placeholder="${isAccessible ? 'Describe your issue...' : 'Upgrade for priority support'}" ${!isAccessible ? 'disabled' : ''}></textarea>
                        <button class="btn" id="submit-ticket" ${!isAccessible ? 'disabled' : ''}>
                            ${isAccessible ? 'Submit Priority Ticket' : 'Upgrade for Priority'}
                        </button>
                    </div>
                </div>
                
                ${!isAccessible ? this.createUpgradeOverlay('priority support') : ''}
            </div>
        `;
    }    /**

     * Populate security features
     */
    async populateSecurityFeatures() {
        const container = document.getElementById('security-features');
        
        container.innerHTML = `
            <div class="security-content">
                <div class="security-overview">
                    <div class="security-grid">
                        ${this.securityFeatures.map(feature => {
                            const isAccessible = this.tierConfig.hasFeature(this.currentTier, feature.requiredTier);
                            return `
                                <div class="security-feature ${isAccessible ? 'accessible' : 'locked'}">
                                    <div class="feature-header">
                                        <div class="feature-icon">${feature.icon}</div>
                                        <div class="feature-title">${feature.name}</div>
                                        ${!isAccessible ? '<div class="premium-badge">Premium</div>' : ''}
                                    </div>
                                    <div class="feature-description">${feature.description}</div>
                                    <div class="feature-status">
                                        <div class="status-indicator ${isAccessible ? 'active' : 'inactive'}"></div>
                                        <span>${isAccessible ? 'Active' : 'Upgrade Required'}</span>
                                    </div>
                                    <div class="feature-demo">
                                        <button class="btn secondary small" data-security-demo="${feature.id}" ${!isAccessible ? 'disabled' : ''}>
                                            ${isAccessible ? 'Try Demo' : 'Premium Only'}
                                        </button>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
                
                <div class="security-audit">
                    <h6>Security Audit Report</h6>
                    <div class="audit-content" id="security-audit-content">
                        ${this.generateSecurityAudit()}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update upgrade prompt
     */
    async updateUpgradePrompt() {
        const container = document.getElementById('upgrade-prompt');
        const upgradePath = this.tierConfig.getUpgradePath(this.currentTier);
        
        if (upgradePath.length === 0) {
            container.innerHTML = `
                <div class="max-tier-message">
                    <h5>🎉 You're on the highest tier!</h5>
                    <p>You have access to all premium features. Thank you for your support!</p>
                </div>
            `;
            return;
        }

        const nextTier = upgradePath[0];
        const savings = this.calculateAnnualSavings(nextTier);
        
        container.innerHTML = `
            <div class="upgrade-card">
                <div class="upgrade-header">
                    <h5>Unlock Premium Features</h5>
                    <div class="upgrade-tier">${nextTier.name} Plan</div>
                </div>
                
                <div class="upgrade-benefits">
                    <div class="benefits-list">
                        ${this.getUpgradeBenefits(this.currentTier, nextTier.id).map(benefit => `
                            <div class="benefit-item">
                                <div class="benefit-icon">✨</div>
                                <div class="benefit-text">${benefit}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div class="upgrade-pricing">
                    <div class="price-display">
                        <div class="price-amount">${this.formatPrice(nextTier.price)}</div>
                        <div class="price-period">per month</div>
                    </div>
                    ${savings > 0 ? `
                        <div class="savings-badge">
                            Save ${this.formatPrice(savings)} annually
                        </div>
                    ` : ''}
                </div>
                
                <div class="upgrade-actions">
                    <button class="btn upgrade-btn" id="start-upgrade">
                        Upgrade to ${nextTier.name}
                    </button>
                    <button class="btn secondary" id="compare-plans">
                        Compare All Plans
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Demo tab switching
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('demo-tab')) {
                document.querySelectorAll('.demo-tab').forEach(tab => tab.classList.remove('active'));
                e.target.classList.add('active');
                this.showDemo(e.target.dataset.tab);
            }
        });

        // Feature try buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('try-feature-btn')) {
                this.showFeaturePreview(e.target.dataset.feature, e.target.dataset.tier);
            }
        });

        // Security demo buttons
        document.addEventListener('click', (e) => {
            if (e.target.dataset.securityDemo) {
                this.showSecurityDemo(e.target.dataset.securityDemo);
            }
        });

        // Upgrade buttons
        document.addEventListener('click', (e) => {
            if (e.target.id === 'start-upgrade') {
                this.startUpgradeProcess();
            }
            if (e.target.id === 'compare-plans') {
                this.showPlanComparison();
            }
        });

        // Advanced encryption demo
        document.addEventListener('click', (e) => {
            if (e.target.id === 'advanced-encrypt-btn') {
                this.runAdvancedEncryptionDemo();
            }
            if (e.target.id === 'generate-key') {
                this.generateSecureKey();
            }
        });

        // Multi-device sync demo
        document.addEventListener('click', (e) => {
            if (e.target.id === 'sync-all-devices') {
                this.runSyncDemo();
            }
        });

        // Support ticket simulation
        document.addEventListener('click', (e) => {
            if (e.target.id === 'submit-ticket') {
                this.simulateSupportTicket();
            }
        });
    }    /*
*
     * Generate demo data
     */
    generateDemoData() {
        return {
            devices: [
                {
                    name: 'iPhone 14 Pro',
                    icon: '📱',
                    status: 'online',
                    lastSync: '2 minutes ago',
                    syncStatus: 'synced'
                },
                {
                    name: 'MacBook Pro',
                    icon: '💻',
                    status: 'online',
                    lastSync: '5 minutes ago',
                    syncStatus: 'syncing'
                },
                {
                    name: 'iPad Air',
                    icon: '📱',
                    status: 'offline',
                    lastSync: '1 hour ago',
                    syncStatus: 'pending'
                },
                {
                    name: 'Android Phone',
                    icon: '📱',
                    status: 'online',
                    lastSync: '10 minutes ago',
                    syncStatus: 'synced'
                }
            ]
        };
    }

    /**
     * Generate analytics data
     */
    generateAnalyticsData() {
        return {
            overview: [
                {
                    icon: '📊',
                    label: 'Backup Success Rate',
                    value: '98.5%',
                    change: '****%',
                    trend: 'positive'
                },
                {
                    icon: '⚡',
                    label: 'Avg Backup Speed',
                    value: '45 MB/s',
                    change: '+15%',
                    trend: 'positive'
                },
                {
                    icon: '💾',
                    label: 'Data Backed Up',
                    value: '2.4 TB',
                    change: '+340 GB',
                    trend: 'positive'
                },
                {
                    icon: '🔄',
                    label: 'Restore Success',
                    value: '99.2%',
                    change: '+0.8%',
                    trend: 'positive'
                }
            ],
            insights: [
                {
                    icon: '🎯',
                    title: 'Optimal Backup Time',
                    description: 'Your backups are 23% faster between 2-4 AM when network traffic is lower.'
                },
                {
                    icon: '📈',
                    title: 'Storage Growth Prediction',
                    description: 'Based on current usage, you\'ll need 15% more storage in the next 3 months.'
                },
                {
                    icon: '🔒',
                    title: 'Security Recommendation',
                    description: 'Consider enabling advanced encryption for sensitive files in your Photos folder.'
                },
                {
                    icon: '⚠️',
                    title: 'Backup Pattern Alert',
                    description: 'Unusual backup activity detected on weekends. Consider adjusting your schedule.'
                }
            ]
        };
    }

    /**
     * Generate support tickets
     */
    generateSupportTickets() {
        return [
            {
                id: 'TK-001',
                priority: 'high',
                subject: 'Backup failed with encryption error',
                status: 'resolved',
                responseTime: '45 minutes',
                satisfaction: 5
            },
            {
                id: 'TK-002',
                priority: 'medium',
                subject: 'How to restore specific files?',
                status: 'resolved',
                responseTime: '1.2 hours',
                satisfaction: 4
            }
        ];
    }

    /**
     * Generate security features
     */
    generateSecurityFeatures() {
        return [
            {
                id: 'zero-knowledge',
                name: 'Zero-Knowledge Encryption',
                description: 'Your data is encrypted before leaving your device. We never have access to your encryption keys.',
                icon: '🔐',
                requiredTier: 'advanced_encryption'
            },
            {
                id: 'audit-trail',
                name: 'Security Audit Trail',
                description: 'Complete log of all access attempts and security events for compliance.',
                icon: '📋',
                requiredTier: 'advanced_encryption'
            },
            {
                id: 'penetration-testing',
                name: 'Regular Penetration Testing',
                description: 'Monthly security assessments by certified ethical hackers.',
                icon: '🛡️',
                requiredTier: 'advanced_encryption'
            },
            {
                id: 'compliance-reporting',
                name: 'Compliance Reporting',
                description: 'GDPR, HIPAA, and SOC 2 compliance reports available on demand.',
                icon: '📄',
                requiredTier: 'advanced_encryption'
            }
        ];
    }

    /**
     * Helper methods
     */
    formatPrice(priceInCents) {
        if (priceInCents === 0) return 'Free';
        return `$${(priceInCents / 100).toFixed(2)}`;
    }

    createUpgradeOverlay(featureName) {
        return `
            <div class="upgrade-overlay">
                <div class="overlay-content">
                    <div class="overlay-icon">🔒</div>
                    <div class="overlay-title">Premium Feature</div>
                    <div class="overlay-description">
                        Upgrade to access ${featureName} and unlock all premium features.
                    </div>
                    <button class="btn upgrade-overlay-btn">Upgrade Now</button>
                </div>
            </div>
        `;
    }    generate
HistoryTimeline(isAccessible, days) {
        if (!isAccessible) {
            return `
                <div class="timeline-item locked">
                    <div class="timeline-date">Upgrade to see extended history</div>
                    <div class="timeline-content">Premium feature - upgrade to access ${days} days of backup history</div>
                </div>
            `;
        }

        const items = [];
        for (let i = 0; i < Math.min(days, 10); i++) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            items.push(`
                <div class="timeline-item">
                    <div class="timeline-date">${date.toLocaleDateString()}</div>
                    <div class="timeline-content">
                        <div class="backup-summary">Backup completed successfully</div>
                        <div class="backup-details">Files: ${Math.floor(Math.random() * 1000) + 100}, Size: ${(Math.random() * 5 + 1).toFixed(1)} GB</div>
                    </div>
                </div>
            `);
        }
        return items.join('');
    }

    generateSupportChat(isAccessible) {
        if (!isAccessible) {
            return `
                <div class="chat-message bot">
                    <div class="message-content">
                        Thank you for contacting support. Please note that response times for free accounts are 24-48 hours.
                        Upgrade to Premium for instant live chat support!
                    </div>
                    <div class="message-time">Just now</div>
                </div>
            `;
        }

        return `
            <div class="chat-message bot">
                <div class="message-content">
                    Hi! I'm Alex, your dedicated premium support agent. How can I help you today?
                </div>
                <div class="message-time">Just now</div>
            </div>
            <div class="chat-message user">
                <div class="message-content">
                    I'm having trouble with my backup encryption settings.
                </div>
                <div class="message-time">1 minute ago</div>
            </div>
            <div class="chat-message bot">
                <div class="message-content">
                    I can help you with that right away! Let me check your account settings and walk you through the encryption configuration.
                </div>
                <div class="message-time">30 seconds ago</div>
            </div>
        `;
    }

    generateSecurityAudit() {
        const isAccessible = this.tierConfig.hasFeature(this.currentTier, 'advanced_encryption');
        
        if (!isAccessible) {
            return this.createUpgradeOverlay('security audit reports');
        }

        return `
            <div class="audit-report">
                <div class="audit-score">
                    <div class="score-circle">
                        <div class="score-value">A+</div>
                    </div>
                    <div class="score-details">
                        <div class="score-label">Security Score</div>
                        <div class="score-description">Excellent security posture</div>
                    </div>
                </div>
                
                <div class="audit-items">
                    <div class="audit-item passed">
                        <div class="audit-icon">✅</div>
                        <div class="audit-text">Encryption: AES-256 with secure key management</div>
                    </div>
                    <div class="audit-item passed">
                        <div class="audit-icon">✅</div>
                        <div class="audit-text">Access Control: Multi-factor authentication enabled</div>
                    </div>
                    <div class="audit-item passed">
                        <div class="audit-icon">✅</div>
                        <div class="audit-text">Data Integrity: Regular integrity checks passed</div>
                    </div>
                    <div class="audit-item warning">
                        <div class="audit-icon">⚠️</div>
                        <div class="audit-text">Recommendation: Enable automatic security updates</div>
                    </div>
                </div>
            </div>
        `;
    }

    getUpgradeBenefits(currentTier, targetTier) {
        const currentFeatures = this.tierConfig.getTierFeatures(currentTier);
        const targetFeatures = this.tierConfig.getTierFeatures(targetTier);
        const benefits = [];

        Object.keys(targetFeatures).forEach(feature => {
            if (targetFeatures[feature] && !currentFeatures[feature]) {
                const featureInfo = this.tierConfig.features.get(feature);
                if (featureInfo) {
                    benefits.push(featureInfo.name);
                }
            }
        });

        const currentLimits = this.tierConfig.getTierLimits(currentTier);
        const targetLimits = this.tierConfig.getTierLimits(targetTier);

        if (targetLimits.maxStorageGB > currentLimits.maxStorageGB) {
            benefits.push(`${targetLimits.maxStorageGB}GB storage (vs ${currentLimits.maxStorageGB}GB)`);
        }

        if (targetLimits.backupHistoryDays > currentLimits.backupHistoryDays) {
            benefits.push(`${targetLimits.backupHistoryDays} days backup history`);
        }

        return benefits;
    }

    calculateAnnualSavings(tier) {
        const monthlyPrice = tier.price;
        const annualPrice = monthlyPrice * 10; // 2 months free
        return monthlyPrice * 2;
    }    /*
*
     * Demo action methods
     */
    async runAdvancedEncryptionDemo() {
        const algorithm = document.getElementById('advanced-algorithm').value;
        const keyDerivation = document.getElementById('key-derivation').value;
        const customKey = document.getElementById('custom-key').value;
        const data = document.getElementById('advanced-demo-data').value;

        const resultsContainer = document.getElementById('advanced-encryption-results');
        
        resultsContainer.innerHTML = `
            <div class="encryption-progress">
                <div class="progress-header">Encrypting with ${algorithm.toUpperCase()}...</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>
        `;

        // Simulate encryption process
        await this.animateProgress(resultsContainer.querySelector('.progress-fill'), 100, 2000);

        resultsContainer.innerHTML = `
            <div class="encryption-results">
                <div class="result-section">
                    <h6>Encryption Complete</h6>
                    <div class="result-stats">
                        <div class="stat">Algorithm: ${algorithm.toUpperCase()}</div>
                        <div class="stat">Key Derivation: ${keyDerivation.toUpperCase()}</div>
                        <div class="stat">Encryption Time: 0.23s</div>
                        <div class="stat">Security Level: Military Grade</div>
                    </div>
                </div>
                
                <div class="encrypted-output">
                    <label>Encrypted Data:</label>
                    <div class="hex-display">
                        ${this.generateRandomHex(200)}
                    </div>
                </div>
                
                <div class="security-verification">
                    <div class="verification-badge verified">
                        ✅ Encryption Verified - Data is secure
                    </div>
                </div>
            </div>
        `;
    }

    async runSyncDemo() {
        const logContainer = document.getElementById('sync-log');
        
        logContainer.innerHTML = `
            <div class="sync-progress">
                <div class="sync-header">Synchronizing devices...</div>
                <div class="sync-devices">
                    <div class="sync-device">📱 iPhone 14 Pro - Syncing...</div>
                    <div class="sync-device">💻 MacBook Pro - Syncing...</div>
                    <div class="sync-device">📱 iPad Air - Connecting...</div>
                    <div class="sync-device">📱 Android Phone - Syncing...</div>
                </div>
            </div>
        `;

        // Simulate sync process
        await new Promise(resolve => setTimeout(resolve, 3000));

        logContainer.innerHTML = `
            <div class="sync-complete">
                <div class="sync-header">✅ Synchronization Complete</div>
                <div class="sync-summary">
                    <div class="sync-stat">Devices Synced: 4/4</div>
                    <div class="sync-stat">Data Transferred: 1.2 GB</div>
                    <div class="sync-stat">Sync Time: 2.8 seconds</div>
                    <div class="sync-stat">Conflicts Resolved: 0</div>
                </div>
            </div>
        `;
    }

    async simulateSupportTicket() {
        const priority = document.getElementById('ticket-priority').value;
        const chatContainer = document.getElementById('support-chat');
        
        const newMessage = document.createElement('div');
        newMessage.className = 'chat-message bot';
        newMessage.innerHTML = `
            <div class="message-content">
                Thank you for your ${priority} priority ticket. I'm connecting you with a specialist right now...
            </div>
            <div class="message-time">Just now</div>
        `;
        
        chatContainer.appendChild(newMessage);
        
        // Simulate quick response
        setTimeout(() => {
            const response = document.createElement('div');
            response.className = 'chat-message bot';
            response.innerHTML = `
                <div class="message-content">
                    Hi! I'm Sarah, a senior support specialist. I've reviewed your ticket and I'm ready to help you resolve this issue immediately.
                </div>
                <div class="message-time">30 seconds ago</div>
            `;
            chatContainer.appendChild(response);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }, 1000);
    }

    generateSecureKey() {
        const keyInput = document.getElementById('custom-key');
        const secureKey = this.generateRandomHex(32);
        keyInput.value = secureKey;
    }

    generateRandomHex(length) {
        const chars = '0123456789abcdef';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    async animateProgress(element, targetWidth, duration) {
        const startTime = Date.now();
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            element.style.width = `${progress * targetWidth}%`;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        animate();
        return new Promise(resolve => setTimeout(resolve, duration));
    }

    async renderAnalyticsCharts() {
        // This would integrate with Chart.js to render actual charts
        // For demo purposes, we'll create placeholder charts
        const performanceCanvas = document.getElementById('performance-canvas');
        const storageCanvas = document.getElementById('storage-canvas');
        
        if (performanceCanvas && window.Chart) {
            // Render performance chart
            new Chart(performanceCanvas, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Backup Speed (MB/s)',
                        data: [30, 35, 40, 38, 45, 42],
                        borderColor: '#4facfe',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        if (storageCanvas && window.Chart) {
            // Render storage chart
            new Chart(storageCanvas, {
                type: 'doughnut',
                data: {
                    labels: ['Photos', 'Messages', 'Contacts', 'Other'],
                    datasets: [{
                        data: [45, 25, 15, 15],
                        backgroundColor: ['#4facfe', '#00f2fe', '#667eea', '#764ba2']
                    }]
                },
                options: {
                    responsive: true
                }
            });
        }
    }    /**

     * Load user subscription status
     */
    async loadUserSubscription() {
        // This would typically load from the subscription manager
        // For demo purposes, we'll use the current tier
        this.currentTier = 'free'; // This would be loaded from user data
        
        // Update tier badge
        const tierBadge = document.getElementById('current-tier-badge');
        if (tierBadge) {
            const tier = this.tierConfig.getTier(this.currentTier);
            tierBadge.textContent = tier.name;
            tierBadge.className = `tier-badge ${this.currentTier}`;
        }
    }

    /**
     * Start demo updates
     */
    startDemoUpdates() {
        // Update analytics periodically
        setInterval(() => {
            this.updateAnalyticsData();
        }, 30000);
    }

    updateAnalyticsData() {
        // Update analytics with new random data
        this.analyticsData.overview.forEach(metric => {
            const change = (Math.random() - 0.5) * 2;
            metric.change = `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
            metric.trend = change > 0 ? 'positive' : 'negative';
        });
    }

    /**
     * Feature preview methods
     */
    showFeaturePreview(feature, tier) {
        // Show a modal or overlay with feature preview
        console.log(`Showing preview for ${feature} in ${tier} tier`);
    }

    showSecurityDemo(demoId) {
        // Show security feature demonstration
        console.log(`Showing security demo: ${demoId}`);
    }

    startUpgradeProcess() {
        // Integrate with payment processor
        console.log('Starting upgrade process');
    }

    showPlanComparison() {
        // Show detailed plan comparison
        console.log('Showing plan comparison');
    }

    /**
     * Cleanup method
     */
    destroy() {
        this.isInitialized = false;
        // Clean up any intervals or event listeners
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.PremiumFeaturesShowcase = PremiumFeaturesShowcase;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PremiumFeaturesShowcase;
}