/**
 * Error Handling Integration Script
 * Integrates all error handling components with existing demo systems
 */

class ErrorHandlingIntegration {
    constructor() {
        this.initialized = false;
        this.integrationCallbacks = [];
    }

    async initialize() {
        if (this.initialized) return;

        console.log('🔧 Initializing Error Handling Integration...');

        try {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // Initialize core error handling
            await this.initializeErrorHandling();

            // Integrate with existing managers
            await this.integrateWithManagers();

            // Setup global error interceptors
            this.setupGlobalInterceptors();

            // Setup UI integration
            this.setupUIIntegration();

            // Setup demo-specific integrations
            this.setupDemoIntegrations();

            // Add error handling to existing components
            this.enhanceExistingComponents();

            this.initialized = true;
            console.log('✅ Error Handling Integration Complete');

            // Notify integration callbacks
            this.integrationCallbacks.forEach(callback => {
                try {
                    callback();
                } catch (error) {
                    console.warn('Integration callback failed:', error);
                }
            });

        } catch (error) {
            console.error('❌ Error Handling Integration Failed:', error);
            throw error;
        }
    }

    async initializeErrorHandling() {
        // Ensure all error handling components are loaded
        const requiredComponents = [
            'SafeKeepErrorHandler',
            'NetworkQueueManager', 
            'OfflineManager',
            'DemoStateManager'
        ];

        for (const component of requiredComponents) {
            if (!window[component]) {
                throw new Error(`Required component ${component} not found`);
            }
        }

        // Initialize if not already done
        if (!window.errorHandler) {
            window.errorHandler = new SafeKeepErrorHandler();
        }
        if (!window.networkQueue) {
            window.networkQueue = new NetworkQueueManager();
        }
        if (!window.offlineManager) {
            window.offlineManager = new OfflineManager();
        }
        if (!window.demoManager) {
            window.demoManager = new DemoStateManager();
        }
    }

    async integrateWithManagers() {
        // Integrate with Auth Manager
        if (window.authManager) {
            this.integrateAuthManager();
        }

        // Integrate with Backup Manager
        if (window.backupManager) {
            this.integrateBackupManager();
        }

        // Integrate with Restore Manager
        if (window.restoreManager) {
            this.integrateRestoreManager();
        }

        // Integrate with Realtime Manager
        if (window.realtimeManager) {
            this.integrateRealtimeManager();
        }

        // Integrate with Encryption Manager
        if (window.encryptionManager) {
            this.integrateEncryptionManager();
        }

        // Integrate with Subscription Manager
        if (window.subscriptionManager) {
            this.integrateSubscriptionManager();
        }

        // Integrate with Payment Processor
        if (window.paymentProcessor) {
            this.integratePaymentProcessor();
        }
    }

    integrateAuthManager() {
        const originalSignup = window.authManager.signup;
        const originalSignin = window.authManager.signin;

        window.authManager.signup = async (email, password) => {
            try {
                const result = await originalSignup.call(window.authManager, email, password);
                window.demoManager.setUser({
                    id: result.user?.id,
                    email: result.user?.email,
                    isAuthenticated: true,
                    isGuest: false
                });
                return result;
            } catch (error) {
                await window.errorHandler.handleError(error, 'AUTHENTICATION', {
                    operation: 'signup',
                    email: email
                });
                throw error;
            }
        };

        window.authManager.signin = async (email, password) => {
            try {
                const result = await originalSignin.call(window.authManager, email, password);
                window.demoManager.setUser({
                    id: result.user?.id,
                    email: result.user?.email,
                    isAuthenticated: true,
                    isGuest: false
                });
                return result;
            } catch (error) {
                await window.errorHandler.handleError(error, 'AUTHENTICATION', {
                    operation: 'signin',
                    email: email
                });
                throw error;
            }
        };

        // Add guest mode support
        window.authManager.setGuestMode = (guestUser) => {
            window.demoManager.setUser({
                ...guestUser,
                isGuest: true,
                isAuthenticated: false
            });
        };

        // Add reset method
        window.authManager.reset = async () => {
            window.demoManager.setUser({
                id: null,
                email: null,
                isAuthenticated: false,
                isGuest: false
            });
        };
    }

    integrateBackupManager() {
        const originalStartBackup = window.backupManager.startBackup;
        const originalPauseBackup = window.backupManager.pauseBackup;

        window.backupManager.startBackup = async (options) => {
            try {
                const result = await originalStartBackup.call(window.backupManager, options);
                window.demoManager.addBackupSession({
                    id: result.sessionId,
                    startTime: Date.now(),
                    type: options.type || 'manual',
                    status: 'started'
                });
                return result;
            } catch (error) {
                await window.errorHandler.handleError(error, 'BACKUP', {
                    operation: 'start_backup',
                    options: options
                });
                throw error;
            }
        };

        // Add pause/resume functionality
        window.backupManager.pauseAll = () => {
            // Implementation for pausing all backup operations
            console.log('Pausing all backup operations');
        };

        window.backupManager.resumeAll = () => {
            // Implementation for resuming all backup operations
            console.log('Resuming all backup operations');
        };

        window.backupManager.enablePartialMode = () => {
            // Implementation for partial backup mode
            console.log('Enabling partial backup mode');
        };

        window.backupManager.reset = async () => {
            window.demoManager.updateState('backup.sessions', []);
            window.demoManager.updateState('backup.currentSession', null);
        };
    }

    integrateRestoreManager() {
        const originalStartRestore = window.restoreManager.startRestore;

        window.restoreManager.startRestore = async (options) => {
            try {
                const result = await originalStartRestore.call(window.restoreManager, options);
                window.demoManager.addRestoreSession({
                    id: result.sessionId,
                    startTime: Date.now(),
                    backupSessionId: options.backupSessionId,
                    status: 'started'
                });
                return result;
            } catch (error) {
                await window.errorHandler.handleError(error, 'RESTORE', {
                    operation: 'start_restore',
                    options: options
                });
                throw error;
            }
        };

        // Add pause/resume functionality
        window.restoreManager.pauseAll = () => {
            console.log('Pausing all restore operations');
        };

        window.restoreManager.enablePartialMode = () => {
            console.log('Enabling partial restore mode');
        };

        window.restoreManager.reset = async () => {
            window.demoManager.updateState('restore.sessions', []);
            window.demoManager.updateState('restore.currentSession', null);
        };
    }

    integrateRealtimeManager() {
        const originalConnect = window.realtimeManager.connect;

        window.realtimeManager.connect = async () => {
            try {
                return await originalConnect.call(window.realtimeManager);
            } catch (error) {
                await window.errorHandler.handleError(error, 'REALTIME', {
                    operation: 'connect'
                });
                throw error;
            }
        };

        // Add fallback methods
        window.realtimeManager.fallbackToPolling = () => {
            console.log('Falling back to polling for real-time updates');
            // Implementation for polling fallback
        };

        window.realtimeManager.setUpdateInterval = (interval) => {
            console.log(`Setting update interval to ${interval}ms`);
            // Implementation for setting update interval
        };

        window.realtimeManager.reset = async () => {
            // Implementation for resetting real-time connections
            console.log('Resetting real-time connections');
        };
    }

    integrateEncryptionManager() {
        const originalEncrypt = window.encryptionManager.encrypt;
        const originalDecrypt = window.encryptionManager.decrypt;

        window.encryptionManager.encrypt = async (data, options) => {
            try {
                const result = await originalEncrypt.call(window.encryptionManager, data, options);
                window.demoManager.addEncryptionDemo({
                    type: 'encryption',
                    algorithm: options.algorithm,
                    dataSize: data.length,
                    timestamp: Date.now()
                });
                return result;
            } catch (error) {
                await window.errorHandler.handleError(error, 'ENCRYPTION', {
                    operation: 'encrypt',
                    algorithm: options.algorithm
                });
                throw error;
            }
        };

        window.encryptionManager.decrypt = async (encryptedData, options) => {
            try {
                const result = await originalDecrypt.call(window.encryptionManager, encryptedData, options);
                window.demoManager.addEncryptionDemo({
                    type: 'decryption',
                    algorithm: options.algorithm,
                    dataSize: encryptedData.length,
                    timestamp: Date.now()
                });
                return result;
            } catch (error) {
                await window.errorHandler.handleError(error, 'ENCRYPTION', {
                    operation: 'decrypt',
                    algorithm: options.algorithm
                });
                throw error;
            }
        };

        window.encryptionManager.reset = async () => {
            window.demoManager.updateState('encryption.demonstrations', []);
        };
    }

    integrateSubscriptionManager() {
        const originalUpdateSubscription = window.subscriptionManager.updateSubscription;

        window.subscriptionManager.updateSubscription = async (subscriptionData) => {
            try {
                const result = await originalUpdateSubscription.call(window.subscriptionManager, subscriptionData);
                window.demoManager.setSubscription(subscriptionData.tier, subscriptionData.status);
                return result;
            } catch (error) {
                await window.errorHandler.handleError(error, 'SUBSCRIPTION', {
                    operation: 'update_subscription',
                    tier: subscriptionData.tier
                });
                throw error;
            }
        };

        window.subscriptionManager.setFreeTier = () => {
            window.demoManager.setSubscription('free', 'active');
        };

        window.subscriptionManager.reset = async () => {
            window.demoManager.setSubscription('free', 'active');
        };
    }

    integratePaymentProcessor() {
        const originalProcessPayment = window.paymentProcessor.processPayment;

        window.paymentProcessor.processPayment = async (paymentData) => {
            try {
                return await originalProcessPayment.call(window.paymentProcessor, paymentData);
            } catch (error) {
                await window.errorHandler.handleError(error, 'PAYMENT', {
                    operation: 'process_payment',
                    amount: paymentData.amount
                });
                throw error;
            }
        };

        window.paymentProcessor.retryLastPayment = () => {
            console.log('Retrying last payment');
            // Implementation for retrying payment
        };

        window.paymentProcessor.reset = async () => {
            // Implementation for resetting payment state
            console.log('Resetting payment processor');
        };
    }

    setupGlobalInterceptors() {
        // Intercept fetch requests
        const originalFetch = window.fetch;
        window.fetch = async (url, options = {}) => {
            try {
                const response = await originalFetch(url, options);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response;
            } catch (error) {
                // Queue for retry if offline
                if (!navigator.onLine) {
                    window.networkQueue.queueApiCall(url, options);
                }
                
                await window.errorHandler.handleError(error, 'NETWORK', {
                    operation: 'fetch',
                    url: url,
                    method: options.method || 'GET'
                });
                throw error;
            }
        };

        // Intercept console errors for logging
        const originalConsoleError = console.error;
        console.error = (...args) => {
            originalConsoleError.apply(console, args);
            
            // Log to error handler if it's an actual error
            if (args[0] instanceof Error) {
                window.errorHandler.handleError(args[0], 'SYSTEM', {
                    operation: 'console_error',
                    args: args.slice(1)
                });
            }
        };
    }

    setupUIIntegration() {
        // Add error handling styles
        if (!document.querySelector('#error-handler-styles')) {
            const link = document.createElement('link');
            link.id = 'error-handler-styles';
            link.rel = 'stylesheet';
            link.href = 'error-handler-styles.css';
            document.head.appendChild(link);
        }

        // Add error log viewer button
        this.addErrorLogViewer();

        // Add demo reset button
        this.addDemoResetButton();

        // Add offline status indicator
        this.addOfflineStatusIndicator();
    }

    addErrorLogViewer() {
        const button = document.createElement('button');
        button.id = 'error-log-viewer-btn';
        button.textContent = '🐛 Error Log';
        button.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 9999;
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        `;
        
        button.onclick = () => this.showErrorLogViewer();
        document.body.appendChild(button);
    }

    addDemoResetButton() {
        const button = document.createElement('button');
        button.id = 'demo-reset-btn';
        button.textContent = '🔄 Reset Demo';
        button.style.cssText = `
            position: fixed;
            top: 10px;
            left: 120px;
            z-index: 9999;
            background: #ffc107;
            color: #333;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        `;
        
        button.onclick = () => this.confirmDemoReset();
        document.body.appendChild(button);
    }

    addOfflineStatusIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'connection-status';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        `;
        
        this.updateConnectionStatus(indicator);
        
        window.addEventListener('online', () => this.updateConnectionStatus(indicator));
        window.addEventListener('offline', () => this.updateConnectionStatus(indicator));
        
        document.body.appendChild(indicator);
    }

    updateConnectionStatus(indicator) {
        if (navigator.onLine) {
            indicator.textContent = '🟢 Online';
            indicator.style.background = '#28a745';
            indicator.style.color = 'white';
        } else {
            indicator.textContent = '🔴 Offline';
            indicator.style.background = '#dc3545';
            indicator.style.color = 'white';
        }
    }

    showErrorLogViewer() {
        const existingViewer = document.querySelector('.safekeep-error-log');
        if (existingViewer) {
            existingViewer.classList.toggle('visible');
            return;
        }

        const viewer = document.createElement('div');
        viewer.className = 'safekeep-error-log visible';
        
        const errorLog = window.errorHandler.getErrorLog();
        
        viewer.innerHTML = `
            <div class="safekeep-error-log__header">
                <div class="safekeep-error-log__title">Error Log (${errorLog.length})</div>
                <button class="safekeep-error-log__close" onclick="this.parentElement.parentElement.classList.remove('visible')">×</button>
            </div>
            <div class="safekeep-error-log__content">
                ${errorLog.length === 0 ? '<p>No errors logged</p>' : errorLog.map(error => `
                    <div class="safekeep-error-log__entry safekeep-error-log__entry--${error.severity}">
                        <div class="safekeep-error-log__entry-header">
                            <span class="safekeep-error-log__entry-category">${error.category}</span>
                            <span class="safekeep-error-log__entry-time">${new Date(error.timestamp).toLocaleTimeString()}</span>
                        </div>
                        <div class="safekeep-error-log__entry-message">${error.error.message}</div>
                    </div>
                `).join('')}
            </div>
        `;
        
        document.body.appendChild(viewer);
    }

    confirmDemoReset() {
        if (confirm('Are you sure you want to reset the demo? This will clear all progress and reload the page.')) {
            window.errorHandler.resetDemo();
        }
    }

    setupDemoIntegrations() {
        // Listen for demo state changes
        window.addEventListener('demoStateChange', (event) => {
            const { path, value } = event.detail;
            console.log(`Demo state changed: ${path} = ${JSON.stringify(value)}`);
        });

        // Add error tracking to demo manager
        const originalHandleError = window.demoManager.addError;
        if (originalHandleError) {
            window.demoManager.addError = (error) => {
                originalHandleError.call(window.demoManager, error);
                // Also log to error handler
                window.errorHandler.handleError(new Error(error.message), 'DEMO_STATE', {
                    operation: 'demo_error',
                    errorData: error
                });
            };
        }
    }

    enhanceExistingComponents() {
        // Add error handling to existing buttons
        const buttons = document.querySelectorAll('button[data-action]');
        buttons.forEach(button => {
            const originalClick = button.onclick;
            button.onclick = async (event) => {
                try {
                    if (originalClick) {
                        await originalClick.call(button, event);
                    }
                } catch (error) {
                    await window.errorHandler.handleError(error, 'SYSTEM', {
                        operation: 'button_click',
                        action: button.dataset.action
                    });
                }
            };
        });

        // Add error handling to forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const originalSubmit = form.onsubmit;
            form.onsubmit = async (event) => {
                try {
                    if (originalSubmit) {
                        await originalSubmit.call(form, event);
                    }
                } catch (error) {
                    event.preventDefault();
                    await window.errorHandler.handleError(error, 'SYSTEM', {
                        operation: 'form_submit',
                        formId: form.id
                    });
                }
            };
        });
    }

    // Public API
    onIntegrationComplete(callback) {
        if (this.initialized) {
            callback();
        } else {
            this.integrationCallbacks.push(callback);
        }
    }

    getIntegrationStatus() {
        return {
            initialized: this.initialized,
            errorHandlerReady: !!window.errorHandler,
            networkQueueReady: !!window.networkQueue,
            offlineManagerReady: !!window.offlineManager,
            demoManagerReady: !!window.demoManager,
            managersIntegrated: {
                auth: !!window.authManager?.setGuestMode,
                backup: !!window.backupManager?.pauseAll,
                restore: !!window.restoreManager?.pauseAll,
                realtime: !!window.realtimeManager?.fallbackToPolling,
                encryption: !!window.encryptionManager?.reset,
                subscription: !!window.subscriptionManager?.setFreeTier,
                payment: !!window.paymentProcessor?.retryLastPayment
            }
        };
    }
}

// Initialize integration
const errorHandlingIntegration = new ErrorHandlingIntegration();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        errorHandlingIntegration.initialize();
    });
} else {
    errorHandlingIntegration.initialize();
}

// Export for global access
window.errorHandlingIntegration = errorHandlingIntegration;
window.ErrorHandlingIntegration = ErrorHandlingIntegration;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandlingIntegration;
}