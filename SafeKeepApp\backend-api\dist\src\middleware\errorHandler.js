"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = exports.handleStripeError = exports.handleValidationError = exports.errorHandler = void 0;
const errorHandler = (error, req, res, next) => {
    console.error('Server error:', error);
    let statusCode = 500;
    let errorCode = 'INTERNAL_SERVER_ERROR';
    let message = 'An unexpected error occurred';
    if (error.name === 'ValidationError') {
        statusCode = 400;
        errorCode = 'VALIDATION_ERROR';
        message = error.message;
    }
    else if (error.name === 'UnauthorizedError') {
        statusCode = 401;
        errorCode = 'UNAUTHORIZED';
        message = 'Authentication required';
    }
    else if (error.name === 'ForbiddenError') {
        statusCode = 403;
        errorCode = 'FORBIDDEN';
        message = 'Insufficient permissions';
    }
    else if (error.name === 'NotFoundError') {
        statusCode = 404;
        errorCode = 'NOT_FOUND';
        message = 'Resource not found';
    }
    const response = {
        success: false,
        error: {
            code: errorCode,
            message: message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
            timestamp: new Date().toISOString()
        }
    };
    res.status(statusCode).json(response);
};
exports.errorHandler = errorHandler;
const handleValidationError = (errors) => {
    const error = new Error('Validation failed');
    error.name = 'ValidationError';
    error.details = errors;
    throw error;
};
exports.handleValidationError = handleValidationError;
const handleStripeError = (stripeError) => {
    const error = new Error(`Stripe API error: ${stripeError.message}`);
    error.name = 'StripeError';
    error.details = {
        type: stripeError.type,
        code: stripeError.code,
        decline_code: stripeError.decline_code
    };
    throw error;
};
exports.handleStripeError = handleStripeError;
const notFoundHandler = (req, res) => {
    const response = {
        success: false,
        error: {
            code: 'ROUTE_NOT_FOUND',
            message: `Route ${req.method} ${req.path} not found`,
            timestamp: new Date().toISOString()
        }
    };
    res.status(404).json(response);
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=errorHandler.js.map