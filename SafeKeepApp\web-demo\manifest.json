{"name": "SafeKeep - Secure Media Backup Demo", "short_name": "<PERSON><PERSON><PERSON>", "description": "Experience SafeKeep's secure media backup features in this comprehensive web demo", "start_url": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#4fa<PERSON><PERSON>", "background_color": "#ffffff", "scope": "/", "lang": "en-US", "dir": "ltr", "icons": [{"src": "icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "screenshot-mobile-1.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Safe<PERSON>eep <PERSON> - Main Dashboard"}, {"src": "screenshot-mobile-2.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "<PERSON><PERSON><PERSON> - Backup Progress"}, {"src": "screenshot-desktop-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Safe<PERSON><PERSON> - Desktop View"}], "categories": ["productivity", "utilities", "security"], "shortcuts": [{"name": "Start Backup", "short_name": "Backup", "description": "Start a new backup session", "url": "/?action=backup", "icons": [{"src": "icon-96x96.png", "sizes": "96x96"}]}, {"name": "View History", "short_name": "History", "description": "View backup history", "url": "/?action=history", "icons": [{"src": "icon-96x96.png", "sizes": "96x96"}]}, {"name": "Encryption Demo", "short_name": "Encryption", "description": "Try the encryption demonstration", "url": "/?action=encryption", "icons": [{"src": "icon-96x96.png", "sizes": "96x96"}]}, {"name": "Subscription", "short_name": "Subscribe", "description": "Manage subscription", "url": "/?action=subscription", "icons": [{"src": "icon-96x96.png", "sizes": "96x96"}]}], "related_applications": [{"platform": "play", "url": "https://play.google.com/store/apps/details?id=com.safekeep.app", "id": "com.safekeep.app"}, {"platform": "itunes", "url": "https://apps.apple.com/app/safekeep/id123456789"}], "prefer_related_applications": false, "protocol_handlers": [{"protocol": "web+safekeep", "url": "/?handler=%s"}], "file_handlers": [{"action": "/", "accept": {"image/*": [".jpg", ".jpeg", ".png", ".gif", ".webp"], "video/*": [".mp4", ".mov", ".avi", ".mkv"], "audio/*": [".mp3", ".wav", ".aac", ".flac"]}}], "share_target": {"action": "/", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url", "files": [{"name": "files", "accept": ["image/*", "video/*", "audio/*"]}]}}, "launch_handler": {"client_mode": "focus-existing"}, "display_override": ["window-controls-overlay", "minimal-ui", "standalone", "browser"], "edge_side_panel": {"preferred_width": 400}, "handle_links": "preferred", "id": "safekeep-demo", "iarc_rating_id": "e84b072d-71b3-4d3e-86ae-31a8ce4e53b7"}