#!/bin/bash

echo "Verifying Java version for SafeKeepApp Android build..."

java -version

echo ""
echo "Checking if Java version is 11 or higher..."

# Extract Java version
JAVA_VERSION=$(java -version 2>&1 | grep -i version | cut -d'"' -f2)

# Extract major version
JAVA_MAJOR_VERSION=$(echo $JAVA_VERSION | sed -E 's/^([0-9]+).*$/\1/')

# Handle version format differences (1.8 vs 11)
if [[ $JAVA_MAJOR_VERSION == "1" ]]; then
    JAVA_MAJOR_VERSION=$(echo $JAVA_VERSION | sed -E 's/^1\.([0-9]+).*$/\1/')
fi

if [[ $JAVA_MAJOR_VERSION -lt 11 ]]; then
    echo "ERROR: Java version must be 11 or higher."
    echo "Current version: $JAVA_VERSION (Major: $JAVA_MAJOR_VERSION)"
    echo "Please install JDK 11+ and set <PERSON><PERSON><PERSON>_HOME correctly."
    echo "See docs/java-setup-guide.md for instructions."
    exit 1
else
    echo "SUCCESS: Java version is 11 or higher."
    echo "Current version: $JAVA_VERSION (Major: $JAVA_MAJOR_VERSION)"
    echo "You can proceed with building the Android app."
    exit 0
fi