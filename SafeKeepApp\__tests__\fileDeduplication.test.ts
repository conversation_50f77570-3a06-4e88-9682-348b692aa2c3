import { supabase } from '../src/config/supabase';
import * as fileDeduplication from '../src/utils/fileDeduplication';
import * as fileMetadataManager from '../src/utils/fileMetadataManager';
import * as supabaseHelpers from '../src/utils/supabaseHelpers';

// Mock the supabase client
jest.mock('../src/config/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    rpc: jest.fn().mockReturnThis(),
    storage: {
      from: jest.fn().mockReturnValue({
        remove: jest.fn().mockResolvedValue({ error: null })
      })
    }
  }
}));

// Mock the getCurrentUserId function
jest.mock('../src/utils/supabaseHelpers', () => ({
  getCurrentUserId: jest.fn()
}));

// Mock the getFileMetadata function
jest.mock('../src/utils/fileMetadataManager', () => ({
  getFileMetadata: jest.fn()
}));

describe('File Deduplication', () => {
  const mockUserId = 'test-user-id';
  const mockFileId = 'test-file-id';
  const mockFileHash = 'test-file-hash';
  
  beforeEach(() => {
    jest.clearAllMocks();
    (supabaseHelpers.getCurrentUserId as jest.Mock).mockResolvedValue(mockUserId);
  });

  describe('generateFileHash', () => {
    it('should generate a hash from string data', async () => {
      // Mock the crypto API
      const mockDigest = jest.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4]).buffer);
      Object.defineProperty(global.crypto.subtle, 'digest', {
        value: mockDigest,
        configurable: true
      });

      const result = await fileDeduplication.generateFileHash('test data');
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
      expect(mockDigest).toHaveBeenCalledWith('SHA-256', expect.any(ArrayBuffer));
    });

    it('should generate a hash from ArrayBuffer data', async () => {
      // Mock the crypto API
      const mockDigest = jest.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4]).buffer);
      Object.defineProperty(global.crypto.subtle, 'digest', {
        value: mockDigest,
        configurable: true
      });

      const buffer = new Uint8Array([1, 2, 3, 4]).buffer;
      const result = await fileDeduplication.generateFileHash(buffer);
      
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
      expect(mockDigest).toHaveBeenCalledWith('SHA-256', buffer);
    });
  });

  describe('checkDuplicateByHash', () => {
    it('should return true if duplicate files exist', async () => {
      const mockDuplicates = [
        { id: 'file1', original_name: 'test1.jpg' },
        { id: 'file2', original_name: 'test2.jpg' }
      ];

      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.neq as jest.Mock).mockResolvedValue({
        data: mockDuplicates,
        error: null
      });

      const result = await fileDeduplication.checkDuplicateByHash(mockFileHash, 'exclude-id');

      expect(result).toEqual({
        isDuplicate: true,
        duplicates: mockDuplicates,
        error: null
      });
      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(supabase.eq).toHaveBeenCalledWith('user_id', mockUserId);
      expect(supabase.eq).toHaveBeenCalledWith('hash', mockFileHash);
      expect(supabase.neq).toHaveBeenCalledWith('id', 'exclude-id');
    });

    it('should return false if no duplicates exist', async () => {
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockResolvedValue({
        data: [],
        error: null
      });

      const result = await fileDeduplication.checkDuplicateByHash(mockFileHash);

      expect(result).toEqual({
        isDuplicate: false,
        duplicates: [],
        error: null
      });
    });
  });

  describe('findAllDuplicates', () => {
    it('should find and return all duplicate files', async () => {
      const mockDuplicateGroups = [
        {
          hash: 'hash1',
          files: [
            { id: 'file1', original_name: 'test1.jpg', size: 1000 },
            { id: 'file2', original_name: 'test2.jpg', size: 1000 }
          ]
        }
      ];

      // Mock RPC call
      (supabase.rpc as jest.Mock).mockResolvedValueOnce({
        data: mockDuplicateGroups,
        error: null
      });

      const result = await fileDeduplication.findAllDuplicates();

      expect(result.duplicateGroups).toHaveLength(1);
      expect(result.duplicateGroups[0].hash).toBe('hash1');
      expect(result.duplicateGroups[0].files).toHaveLength(2);
      expect(result.totalPotentialSavings).toBe(1000); // One file's worth of savings
      expect(result.error).toBeNull();
      expect(supabase.rpc).toHaveBeenCalledWith('find_duplicate_files', {
        user_id_param: mockUserId
      });
    });
  });

  describe('resolveDuplicates', () => {
    it('should keep one file and delete the others', async () => {
      const fileToKeep = {
        id: 'keep-file-id',
        user_id: mockUserId,
        hash: mockFileHash,
        storage_path: 'path/to/keep-file.jpg'
      };

      const duplicateFiles = [
        { id: 'dup1', storage_path: 'path/to/dup1.jpg' },
        { id: 'dup2', storage_path: 'path/to/dup2.jpg' }
      ];

      // Mock getFileMetadata
      (fileMetadataManager.getFileMetadata as jest.Mock).mockResolvedValue({
        data: fileToKeep,
        error: null
      });

      // Mock finding duplicates
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.neq as jest.Mock).mockResolvedValue({
        data: duplicateFiles,
        error: null
      });

      // Mock deleting files
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.delete as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockResolvedValue({
        error: null
      });

      const result = await fileDeduplication.resolveDuplicates(mockFileHash, 'keep-file-id');

      expect(result).toEqual({
        success: true,
        error: null
      });
      expect(supabase.storage.from).toHaveBeenCalledWith('user-data');
      expect(supabase.storage.from().remove).toHaveBeenCalledTimes(2);
      expect(supabase.delete).toHaveBeenCalledTimes(2);
    });
  });

  describe('compareFiles', () => {
    it('should compare two files and return if they are identical', async () => {
      const file1 = {
        id: 'file1',
        user_id: mockUserId,
        hash: 'same-hash',
        original_name: 'file1.jpg'
      };

      const file2 = {
        id: 'file2',
        user_id: mockUserId,
        hash: 'same-hash',
        original_name: 'file2.jpg'
      };

      // Mock getFileMetadata for both files
      (fileMetadataManager.getFileMetadata as jest.Mock)
        .mockResolvedValueOnce({ data: file1, error: null })
        .mockResolvedValueOnce({ data: file2, error: null });

      const result = await fileDeduplication.compareFiles('file1', 'file2');

      expect(result).toEqual({
        areIdentical: true,
        hashesMatch: true,
        error: null
      });
    });

    it('should return false if files have different hashes', async () => {
      const file1 = {
        id: 'file1',
        user_id: mockUserId,
        hash: 'hash1',
        original_name: 'file1.jpg'
      };

      const file2 = {
        id: 'file2',
        user_id: mockUserId,
        hash: 'hash2',
        original_name: 'file2.jpg'
      };

      // Mock getFileMetadata for both files
      (fileMetadataManager.getFileMetadata as jest.Mock)
        .mockResolvedValueOnce({ data: file1, error: null })
        .mockResolvedValueOnce({ data: file2, error: null });

      const result = await fileDeduplication.compareFiles('file1', 'file2');

      expect(result).toEqual({
        areIdentical: false,
        hashesMatch: false,
        error: null
      });
    });
  });
});