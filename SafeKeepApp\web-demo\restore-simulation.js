/**
 * Data Restore Simulation Interface
 * Provides UI for backup selection, progress visualization, and data preview
 */

class RestoreSimulation {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.restoreManager = null;
        this.historyManager = null;
        this.currentRestoreSession = null;
        
        if (!this.container) {
            throw new Error(`Container with ID '${containerId}' not found`);
        }
        
        this.init();
    }

    /**
     * Initialize the restore simulation interface
     */
    init() {
        this.render();
        this.attachEventListeners();
    }

    /**
     * Set the restore manager
     */
    setRestoreManager(restoreManager) {
        this.restoreManager = restoreManager;
        
        // Add event listeners
        this.restoreManager.addListener((event, data) => {
            this.handleRestoreEvent(event, data);
        });
    }

    /**
     * Set the history manager
     */
    setHistoryManager(historyManager) {
        this.historyManager = historyManager;
        this.loadAvailableBackups();
    }

    /**
     * Render the restore simulation interface
     */
    render() {
        this.container.innerHTML = `
            <div class="restore-simulation">
                <div class="restore-header">
                    <h3>🔄 Data Restore Simulation</h3>
                    <p>Select a completed backup session to simulate data restoration with real-time progress tracking.</p>
                </div>

                <!-- Backup Selection Section -->
                <div class="backup-selection-section">
                    <h4>📋 Select Backup to Restore</h4>
                    <div id="available-backups" class="available-backups">
                        <div class="loading-placeholder">Loading available backups...</div>
                    </div>
                </div>

                <!-- Data Type Selection Section -->
                <div id="data-type-selection" class="data-type-selection" style="display: none;">
                    <h4>📁 Select Data Types to Restore</h4>
                    <div class="data-type-options">
                        <label class="data-type-option">
                            <input type="checkbox" id="restore-contacts" value="contacts">
                            <span class="checkmark"></span>
                            <div class="option-content">
                                <strong>📞 Contacts</strong>
                                <small id="contacts-count">0 contacts</small>
                            </div>
                        </label>
                        <label class="data-type-option">
                            <input type="checkbox" id="restore-messages" value="messages">
                            <span class="checkmark"></span>
                            <div class="option-content">
                                <strong>💬 Messages</strong>
                                <small id="messages-count">0 messages</small>
                            </div>
                        </label>
                        <label class="data-type-option">
                            <input type="checkbox" id="restore-photos" value="photos">
                            <span class="checkmark"></span>
                            <div class="option-content">
                                <strong>📸 Photos</strong>
                                <small id="photos-count">0 photos</small>
                            </div>
                        </label>
                    </div>
                    
                    <div class="restore-options">
                        <label class="restore-option">
                            <input type="checkbox" id="verify-integrity" checked>
                            <span class="checkmark"></span>
                            <span>Verify data integrity after restore</span>
                        </label>
                        <label class="restore-option">
                            <input type="checkbox" id="selective-restore">
                            <span class="checkmark"></span>
                            <span>Enable selective restore (choose specific items)</span>
                        </label>
                    </div>

                    <div class="restore-controls">
                        <button class="btn" id="start-restore-btn" onclick="restoreSimulation.startRestore()">
                            🚀 Start Restore
                        </button>
                        <button class="btn secondary" onclick="restoreSimulation.cancelSelection()">
                            ❌ Cancel
                        </button>
                    </div>
                </div>

                <!-- Restore Progress Section -->
                <div id="restore-progress-section" class="restore-progress-section" style="display: none;">
                    <h4>⏳ Restore Progress</h4>
                    
                    <div class="overall-progress">
                        <div class="progress-header">
                            <span>Overall Progress</span>
                            <span id="overall-percent">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="overall-progress"></div>
                        </div>
                        <div class="progress-details">
                            <span id="current-phase">Initializing...</span>
                            <span id="estimated-time">Calculating...</span>
                        </div>
                    </div>

                    <div class="phase-progress">
                        <div class="phase-item">
                            <div class="phase-header">
                                <span class="phase-icon" id="download-icon">⏳</span>
                                <span class="phase-name">Download</span>
                                <span class="phase-percent" id="download-percent">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="download-progress"></div>
                            </div>
                            <div class="phase-details" id="download-details">Waiting to start...</div>
                        </div>

                        <div class="phase-item">
                            <div class="phase-header">
                                <span class="phase-icon" id="decryption-icon">⏳</span>
                                <span class="phase-name">Decryption</span>
                                <span class="phase-percent" id="decryption-percent">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="decryption-progress"></div>
                            </div>
                            <div class="phase-details" id="decryption-details">Waiting to start...</div>
                        </div>

                        <div class="phase-item" id="verification-phase">
                            <div class="phase-header">
                                <span class="phase-icon" id="verification-icon">⏳</span>
                                <span class="phase-name">Verification</span>
                                <span class="phase-percent" id="verification-percent">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="verification-progress"></div>
                            </div>
                            <div class="phase-details" id="verification-details">Waiting to start...</div>
                        </div>
                    </div>

                    <div class="restore-controls">
                        <button class="btn danger" id="cancel-restore-btn" onclick="restoreSimulation.cancelRestore()">
                            ⏹️ Cancel Restore
                        </button>
                    </div>
                </div>

                <!-- Restore Results Section -->
                <div id="restore-results-section" class="restore-results-section" style="display: none;">
                    <h4>✅ Restore Complete</h4>
                    
                    <div class="restore-summary">
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-label">Duration</span>
                                <span class="stat-value" id="restore-duration">0s</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Data Restored</span>
                                <span class="stat-value" id="data-restored">0 items</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Integrity Status</span>
                                <span class="stat-value" id="integrity-status">✅ Verified</span>
                            </div>
                        </div>
                    </div>

                    <div class="verification-results" id="verification-results" style="display: none;">
                        <h5>🔍 Verification Results</h5>
                        <div id="verification-details-list"></div>
                    </div>

                    <div class="restored-data-preview">
                        <h5>👁️ Restored Data Preview</h5>
                        <div class="data-tabs">
                            <button class="tab-btn active" onclick="restoreSimulation.switchDataTab('contacts')">
                                📞 Contacts
                            </button>
                            <button class="tab-btn" onclick="restoreSimulation.switchDataTab('messages')">
                                💬 Messages
                            </button>
                            <button class="tab-btn" onclick="restoreSimulation.switchDataTab('photos')">
                                📸 Photos
                            </button>
                        </div>
                        
                        <div class="data-content">
                            <div id="contacts-data" class="data-tab-content active"></div>
                            <div id="messages-data" class="data-tab-content"></div>
                            <div id="photos-data" class="data-tab-content"></div>
                        </div>
                    </div>

                    <div class="restore-controls">
                        <button class="btn" onclick="restoreSimulation.exportRestoredData()">
                            💾 Export Data
                        </button>
                        <button class="btn secondary" onclick="restoreSimulation.startNewRestore()">
                            🔄 New Restore
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.addStyles();
    }

    /**
     * Add CSS styles
     */
    addStyles() {
        if (document.getElementById('restore-simulation-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'restore-simulation-styles';
        styles.textContent = `
            .restore-simulation {
                background: #f8f9fa;
                border-radius: 15px;
                padding: 25px;
                margin: 20px 0;
            }

            .restore-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e9ecef;
            }

            .restore-header h3 {
                color: #4facfe;
                margin-bottom: 10px;
                font-size: 1.5rem;
            }

            .restore-header p {
                color: #666;
                font-size: 1rem;
            }

            .backup-selection-section,
            .data-type-selection,
            .restore-progress-section,
            .restore-results-section {
                background: white;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
                border: 1px solid #e9ecef;
            }

            .backup-selection-section h4,
            .data-type-selection h4,
            .restore-progress-section h4,
            .restore-results-section h4 {
                color: #333;
                margin-bottom: 15px;
                font-size: 1.2rem;
            }

            .available-backups {
                display: grid;
                gap: 15px;
                max-height: 400px;
                overflow-y: auto;
            }

            .backup-item {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .backup-item:hover {
                border-color: #4facfe;
                background: #f8f9ff;
            }

            .backup-item.selected {
                border-color: #4facfe;
                background: #e3f2fd;
            }

            .backup-header {
                display: flex;
                justify-content: between;
                align-items: center;
                margin-bottom: 10px;
            }

            .backup-title {
                font-weight: 600;
                color: #333;
            }

            .backup-date {
                color: #666;
                font-size: 0.9rem;
            }

            .backup-details {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 10px;
                font-size: 0.85rem;
                color: #666;
            }

            .data-type-options {
                display: grid;
                gap: 15px;
                margin-bottom: 20px;
            }

            .data-type-option {
                display: flex;
                align-items: center;
                padding: 15px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .data-type-option:hover {
                border-color: #4facfe;
                background: #f8f9ff;
            }

            .data-type-option input[type="checkbox"] {
                display: none;
            }

            .checkmark {
                width: 20px;
                height: 20px;
                border: 2px solid #ddd;
                border-radius: 4px;
                margin-right: 15px;
                position: relative;
                transition: all 0.2s ease;
            }

            .data-type-option input[type="checkbox"]:checked + .checkmark {
                background: #4facfe;
                border-color: #4facfe;
            }

            .data-type-option input[type="checkbox"]:checked + .checkmark::after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-weight: bold;
            }

            .option-content {
                flex: 1;
            }

            .option-content strong {
                display: block;
                margin-bottom: 5px;
            }

            .option-content small {
                color: #666;
                font-size: 0.85rem;
            }

            .restore-options {
                margin-bottom: 20px;
            }

            .restore-option {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                cursor: pointer;
            }

            .restore-option input[type="checkbox"] {
                display: none;
            }

            .restore-option .checkmark {
                width: 16px;
                height: 16px;
                margin-right: 10px;
            }

            .restore-controls {
                display: flex;
                gap: 10px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .restore-controls .btn {
                padding: 12px 24px;
                font-size: 1rem;
                min-width: 150px;
            }

            .overall-progress {
                margin-bottom: 25px;
            }

            .progress-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                font-weight: 600;
            }

            .progress-bar {
                width: 100%;
                height: 12px;
                background: #e9ecef;
                border-radius: 6px;
                overflow: hidden;
                margin: 10px 0;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4facfe, #00f2fe);
                width: 0%;
                transition: width 0.3s ease;
                border-radius: 6px;
            }

            .progress-details {
                display: flex;
                justify-content: space-between;
                font-size: 0.85rem;
                color: #666;
                margin-top: 8px;
            }

            .phase-progress {
                display: grid;
                gap: 15px;
            }

            .phase-item {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                border-left: 4px solid #e9ecef;
                transition: border-color 0.3s ease;
            }

            .phase-item.active {
                border-left-color: #4facfe;
                background: #f0f8ff;
            }

            .phase-item.completed {
                border-left-color: #28a745;
                background: #f0fff4;
            }

            .phase-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                font-weight: 600;
            }

            .phase-icon {
                margin-right: 10px;
                font-size: 1.2rem;
            }

            .phase-name {
                flex: 1;
            }

            .phase-details {
                font-size: 0.85rem;
                color: #666;
                margin-top: 8px;
            }

            .restore-summary {
                background: #e8f5e8;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .summary-stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }

            .stat-item {
                text-align: center;
                background: white;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #c3e6cb;
            }

            .stat-label {
                display: block;
                font-size: 0.85rem;
                color: #666;
                margin-bottom: 5px;
            }

            .stat-value {
                display: block;
                font-size: 1.2rem;
                font-weight: 600;
                color: #155724;
            }

            .verification-results {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 20px;
            }

            .verification-results h5 {
                margin-bottom: 15px;
                color: #333;
            }

            .verification-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px;
                background: white;
                border-radius: 4px;
                margin-bottom: 8px;
                border-left: 4px solid #28a745;
            }

            .verification-item.warning {
                border-left-color: #ffc107;
            }

            .verification-item.error {
                border-left-color: #dc3545;
            }

            .verification-name {
                font-weight: 600;
            }

            .verification-status {
                font-size: 0.85rem;
                color: #666;
            }

            .restored-data-preview {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .restored-data-preview h5 {
                margin-bottom: 15px;
                color: #333;
            }

            .data-tabs {
                display: flex;
                gap: 5px;
                margin-bottom: 15px;
                border-bottom: 2px solid #e9ecef;
            }

            .tab-btn {
                background: none;
                border: none;
                padding: 10px 15px;
                cursor: pointer;
                border-radius: 8px 8px 0 0;
                transition: all 0.2s ease;
                font-size: 0.9rem;
            }

            .tab-btn:hover {
                background: #e9ecef;
            }

            .tab-btn.active {
                background: #4facfe;
                color: white;
            }

            .data-content {
                min-height: 200px;
                max-height: 400px;
                overflow-y: auto;
            }

            .data-tab-content {
                display: none;
            }

            .data-tab-content.active {
                display: block;
            }

            .data-item {
                background: white;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
                border: 1px solid #e9ecef;
            }

            .data-item-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .data-item-title {
                font-weight: 600;
                color: #333;
            }

            .data-item-meta {
                font-size: 0.85rem;
                color: #666;
            }

            .data-item-content {
                color: #555;
                line-height: 1.4;
            }

            .photo-item {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .photo-thumbnail {
                width: 60px;
                height: 60px;
                border-radius: 8px;
                object-fit: cover;
            }

            .photo-details {
                flex: 1;
            }

            .loading-placeholder {
                text-align: center;
                padding: 40px;
                color: #666;
                font-style: italic;
            }

            .empty-state {
                text-align: center;
                padding: 40px;
                color: #666;
            }

            .empty-state h5 {
                margin-bottom: 10px;
                color: #999;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                .backup-details {
                    grid-template-columns: 1fr;
                }
                
                .summary-stats {
                    grid-template-columns: 1fr;
                }
                
                .restore-controls {
                    flex-direction: column;
                    align-items: center;
                }
                
                .data-tabs {
                    flex-wrap: wrap;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Event listeners will be attached to dynamically created elements
    }

    /**
     * Load available backups
     */
    loadAvailableBackups() {
        if (!this.historyManager) return;

        const availableBackups = this.restoreManager ? 
            this.restoreManager.getAvailableBackups(this.historyManager) : 
            this.historyManager.getSessions({ status: 'completed' });

        const container = document.getElementById('available-backups');
        
        if (availableBackups.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <h5>No Backups Available</h5>
                    <p>Complete a backup session first to enable restore functionality.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = availableBackups.map(backup => `
            <div class="backup-item" onclick="restoreSimulation.selectBackup('${backup.id}')">
                <div class="backup-header">
                    <span class="backup-title">
                        ${backup.type === 'manual' ? '🔧' : '⏰'} 
                        ${backup.type.charAt(0).toUpperCase() + backup.type.slice(1)} Backup
                    </span>
                    <span class="backup-date">${new Date(backup.date).toLocaleString()}</span>
                </div>
                <div class="backup-details">
                    <div>📞 ${backup.itemCounts?.contacts || 0} contacts</div>
                    <div>💬 ${backup.itemCounts?.messages || 0} messages</div>
                    <div>📸 ${backup.itemCounts?.photos || 0} photos</div>
                    <div>💾 ${RestoreManager.formatFileSize(backup.size || 0)}</div>
                    <div>🔒 ${backup.algorithm || 'AES-256-GCM'}</div>
                    <div>⏱️ ${RestoreManager.formatDuration(backup.duration || 0)}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Select a backup for restoration
     */
    selectBackup(backupId) {
        // Remove previous selection
        document.querySelectorAll('.backup-item').forEach(item => {
            item.classList.remove('selected');
        });

        // Add selection to clicked item
        event.target.closest('.backup-item').classList.add('selected');

        this.selectedBackupId = backupId;

        // Load backup details and show data type selection
        this.loadBackupDetails(backupId);
        document.getElementById('data-type-selection').style.display = 'block';
    }

    /**
     * Load backup details for data type selection
     */
    loadBackupDetails(backupId) {
        if (!this.historyManager) return;

        const session = this.historyManager.getSession(backupId);
        if (!session) return;

        // Update data type counts
        document.getElementById('contacts-count').textContent = 
            `${session.progress.contacts.completed} contacts`;
        document.getElementById('messages-count').textContent = 
            `${session.progress.messages.completed} messages`;
        document.getElementById('photos-count').textContent = 
            `${session.progress.photos.completed} photos`;

        // Enable/disable checkboxes based on available data
        document.getElementById('restore-contacts').disabled = session.progress.contacts.completed === 0;
        document.getElementById('restore-messages').disabled = session.progress.messages.completed === 0;
        document.getElementById('restore-photos').disabled = session.progress.photos.completed === 0;

        // Auto-select available data types
        document.getElementById('restore-contacts').checked = session.progress.contacts.completed > 0;
        document.getElementById('restore-messages').checked = session.progress.messages.completed > 0;
        document.getElementById('restore-photos').checked = session.progress.photos.completed > 0;
    }

    /**
     * Start the restore process
     */
    async startRestore() {
        if (!this.selectedBackupId || !this.restoreManager) return;

        // Get selected data types
        const selectedTypes = [];
        if (document.getElementById('restore-contacts').checked) selectedTypes.push('contacts');
        if (document.getElementById('restore-messages').checked) selectedTypes.push('messages');
        if (document.getElementById('restore-photos').checked) selectedTypes.push('photos');

        if (selectedTypes.length === 0) {
            alert('Please select at least one data type to restore.');
            return;
        }

        // Get restore options
        const options = {
            verifyIntegrity: document.getElementById('verify-integrity').checked,
            selectiveRestore: document.getElementById('selective-restore').checked
        };

        // Hide selection sections and show progress
        document.getElementById('data-type-selection').style.display = 'none';
        document.getElementById('restore-progress-section').style.display = 'block';

        // Show/hide verification phase based on option
        document.getElementById('verification-phase').style.display = 
            options.verifyIntegrity ? 'block' : 'none';

        try {
            this.currentRestoreSession = await this.restoreManager.startRestore(
                this.selectedBackupId, 
                selectedTypes, 
                options
            );
        } catch (error) {
            alert(`Restore failed: ${error.message}`);
            this.resetInterface();
        }
    }

    /**
     * Handle restore events
     */
    handleRestoreEvent(event, data) {
        switch (event) {
            case 'restore_started':
                this.updateRestoreProgress(data);
                break;
            case 'restore_progress':
                this.updateRestoreProgress(data);
                break;
            case 'restore_phase_started':
                this.updatePhaseStatus(data.phase, 'running');
                break;
            case 'restore_phase_completed':
                this.updatePhaseStatus(data.phase, 'completed');
                break;
            case 'restore_completed':
                this.showRestoreResults(data);
                break;
            case 'restore_failed':
                this.showRestoreError(data);
                break;
            case 'restore_cancelled':
                this.resetInterface();
                break;
            case 'restore_network_event':
                this.updateNetworkStatus(data);
                break;
            case 'restore_decryption_type':
                this.updateDecryptionStatus(data);
                break;
            case 'restore_verification_step':
                this.updateVerificationStep(data);
                break;
        }
    }

    /**
     * Update restore progress
     */
    updateRestoreProgress(session) {
        // Update overall progress
        document.getElementById('overall-progress').style.width = `${session.progress.overall}%`;
        document.getElementById('overall-percent').textContent = `${session.progress.overall}%`;

        // Update phase progress
        document.getElementById('download-progress').style.width = `${session.progress.download}%`;
        document.getElementById('download-percent').textContent = `${session.progress.download}%`;
        
        document.getElementById('decryption-progress').style.width = `${session.progress.decryption}%`;
        document.getElementById('decryption-percent').textContent = `${session.progress.decryption}%`;
        
        document.getElementById('verification-progress').style.width = `${session.progress.verification}%`;
        document.getElementById('verification-percent').textContent = `${session.progress.verification}%`;

        // Update current phase
        document.getElementById('current-phase').textContent = 
            session.status.charAt(0).toUpperCase() + session.status.slice(1) + '...';

        // Calculate estimated time (simplified)
        const elapsed = Date.now() - session.startTime.getTime();
        const estimatedTotal = session.progress.overall > 0 ? 
            (elapsed / session.progress.overall) * 100 : 0;
        const remaining = Math.max(0, estimatedTotal - elapsed);
        
        document.getElementById('estimated-time').textContent = 
            remaining > 0 ? `~${Math.ceil(remaining / 1000)}s remaining` : 'Calculating...';
    }

    /**
     * Update phase status
     */
    updatePhaseStatus(phase, status) {
        const phaseElement = document.querySelector(`.phase-item:has(#${phase}-progress)`);
        if (!phaseElement) return;

        phaseElement.classList.remove('active', 'completed');
        
        if (status === 'running') {
            phaseElement.classList.add('active');
            document.getElementById(`${phase}-icon`).textContent = '🔄';
            document.getElementById(`${phase}-details`).textContent = 'In progress...';
        } else if (status === 'completed') {
            phaseElement.classList.add('completed');
            document.getElementById(`${phase}-icon`).textContent = '✅';
            document.getElementById(`${phase}-details`).textContent = 'Completed successfully';
        }
    }

    /**
     * Update network status
     */
    updateNetworkStatus(data) {
        document.getElementById('download-details').textContent = data.details;
    }

    /**
     * Update decryption status
     */
    updateDecryptionStatus(data) {
        if (data.status === 'starting') {
            document.getElementById('decryption-details').textContent = 
                `Decrypting ${data.dataType}...`;
        } else if (data.status === 'completed') {
            document.getElementById('decryption-details').textContent = 
                `Decrypted ${data.itemCount} ${data.dataType} items`;
        }
    }

    /**
     * Update verification step
     */
    updateVerificationStep(data) {
        document.getElementById('verification-details').textContent = 
            `${data.step}: ${data.status}`;
    }

    /**
     * Show restore results
     */
    showRestoreResults(session) {
        // Hide progress section and show results
        document.getElementById('restore-progress-section').style.display = 'none';
        document.getElementById('restore-results-section').style.display = 'block';

        // Update summary stats
        const duration = session.endTime.getTime() - session.startTime.getTime();
        document.getElementById('restore-duration').textContent = 
            RestoreManager.formatDuration(duration);

        const totalItems = Object.values(session.restoredData)
            .reduce((sum, items) => sum + items.length, 0);
        document.getElementById('data-restored').textContent = `${totalItems} items`;

        // Show verification results if available
        if (session.verificationResults && Object.keys(session.verificationResults).length > 0) {
            document.getElementById('verification-results').style.display = 'block';
            this.displayVerificationResults(session.verificationResults);
        }

        // Display restored data
        this.displayRestoredData(session.restoredData);
    }

    /**
     * Display verification results
     */
    displayVerificationResults(results) {
        const container = document.getElementById('verification-details-list');
        
        container.innerHTML = Object.entries(results).map(([step, result]) => `
            <div class="verification-item ${result.status}">
                <div>
                    <div class="verification-name">${step}</div>
                    <div class="verification-status">${result.details}</div>
                </div>
                <div>${result.status === 'passed' ? '✅' : '⚠️'}</div>
            </div>
        `).join('');
    }

    /**
     * Display restored data
     */
    displayRestoredData(restoredData) {
        // Display contacts
        if (restoredData.contacts) {
            document.getElementById('contacts-data').innerHTML = 
                this.renderContactsData(restoredData.contacts);
        }

        // Display messages
        if (restoredData.messages) {
            document.getElementById('messages-data').innerHTML = 
                this.renderMessagesData(restoredData.messages);
        }

        // Display photos
        if (restoredData.photos) {
            document.getElementById('photos-data').innerHTML = 
                this.renderPhotosData(restoredData.photos);
        }
    }

    /**
     * Render contacts data
     */
    renderContactsData(contacts) {
        if (contacts.length === 0) {
            return '<div class="empty-state"><h5>No contacts restored</h5></div>';
        }

        return contacts.map(contact => `
            <div class="data-item">
                <div class="data-item-header">
                    <div class="data-item-title">
                        <img src="${contact.avatar}" alt="${contact.name}" style="width: 24px; height: 24px; border-radius: 50%; margin-right: 8px;">
                        ${contact.name}
                    </div>
                    <div class="data-item-meta">
                        Last contact: ${new Date(contact.lastContact).toLocaleDateString()}
                    </div>
                </div>
                <div class="data-item-content">
                    <div>📞 ${contact.phone}</div>
                    <div>📧 ${contact.email}</div>
                    <div>🏷️ ${contact.groups.join(', ')}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Render messages data
     */
    renderMessagesData(messages) {
        if (messages.length === 0) {
            return '<div class="empty-state"><h5>No messages restored</h5></div>';
        }

        return messages.map(message => `
            <div class="data-item">
                <div class="data-item-header">
                    <div class="data-item-title">
                        ${message.type === 'received' ? '📨' : '📤'} 
                        ${message.fromName || message.toName || 'Unknown'}
                    </div>
                    <div class="data-item-meta">
                        ${new Date(message.timestamp).toLocaleString()}
                    </div>
                </div>
                <div class="data-item-content">
                    ${message.message}
                </div>
            </div>
        `).join('');
    }

    /**
     * Render photos data
     */
    renderPhotosData(photos) {
        if (photos.length === 0) {
            return '<div class="empty-state"><h5>No photos restored</h5></div>';
        }

        return photos.map(photo => `
            <div class="data-item">
                <div class="photo-item">
                    <img src="${photo.thumbnail}" alt="${photo.filename}" class="photo-thumbnail">
                    <div class="photo-details">
                        <div class="data-item-title">${photo.filename}</div>
                        <div class="data-item-meta">
                            ${RestoreManager.formatFileSize(photo.size)} • 
                            ${new Date(photo.dateTaken).toLocaleDateString()} • 
                            ${photo.location}
                        </div>
                        <div class="data-item-content">
                            ${photo.metadata.camera} • ${photo.metadata.resolution}
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Switch data tab
     */
    switchDataTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        // Update tab content
        document.querySelectorAll('.data-tab-content').forEach(content => content.classList.remove('active'));
        document.getElementById(`${tabName}-data`).classList.add('active');
    }

    /**
     * Export restored data
     */
    exportRestoredData() {
        if (!this.currentRestoreSession) return;

        const data = {
            restoreSession: {
                id: this.currentRestoreSession.id,
                backupSessionId: this.currentRestoreSession.backupSessionId,
                timestamp: new Date().toISOString(),
                duration: this.currentRestoreSession.endTime.getTime() - this.currentRestoreSession.startTime.getTime()
            },
            restoredData: this.currentRestoreSession.restoredData,
            verificationResults: this.currentRestoreSession.verificationResults
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `restored-data-${this.currentRestoreSession.id}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        alert('Restored data exported successfully!');
    }

    /**
     * Start a new restore
     */
    startNewRestore() {
        this.resetInterface();
        this.loadAvailableBackups();
    }

    /**
     * Cancel selection
     */
    cancelSelection() {
        document.getElementById('data-type-selection').style.display = 'none';
        document.querySelectorAll('.backup-item').forEach(item => {
            item.classList.remove('selected');
        });
        this.selectedBackupId = null;
    }

    /**
     * Cancel restore
     */
    cancelRestore() {
        if (this.restoreManager) {
            this.restoreManager.cancelRestore();
        }
        this.resetInterface();
    }

    /**
     * Show restore error
     */
    showRestoreError(session) {
        alert(`Restore failed: ${session.errors[session.errors.length - 1]?.message || 'Unknown error'}`);
        this.resetInterface();
    }

    /**
     * Reset interface to initial state
     */
    resetInterface() {
        document.getElementById('data-type-selection').style.display = 'none';
        document.getElementById('restore-progress-section').style.display = 'none';
        document.getElementById('restore-results-section').style.display = 'none';
        
        this.selectedBackupId = null;
        this.currentRestoreSession = null;
        
        // Reset progress bars
        document.querySelectorAll('.progress-fill').forEach(fill => {
            fill.style.width = '0%';
        });
        
        // Reset phase status
        document.querySelectorAll('.phase-item').forEach(item => {
            item.classList.remove('active', 'completed');
        });
    }
}

// Make it available globally
window.RestoreSimulation = RestoreSimulation;