import { supabase, STORAGE_PATHS, TABLES, BUCKETS } from '../config/supabase';
import EncryptionService from './EncryptionService';
import AuthService from './AuthService';

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  percentage: number;
  state: 'running' | 'paused' | 'success' | 'error';
  error?: string;
}

export interface FileMetadata {
  id: string;
  user_id: string;
  original_name: string;
  encrypted_name: string;
  mime_type: string;
  size: number;
  encrypted_size: number;
  uploaded_at: Date;
  last_modified: Date;
  category: 'photo' | 'contact' | 'message';
  hash: string;
  encryption_iv: string;
  encryption_salt: string;
  storage_path: string;
  is_backed_up: boolean;
}

export interface StorageQuota {
  used: number;
  total: number;
  percentage: number;
  remaining: number;
}

export interface UploadResult {
  success: boolean;
  fileId?: string;
  downloadURL?: string;
  error?: string;
  metadata?: FileMetadata;
}

class CloudStorageService {
  private activeUploads = new Map<string, AbortController>();

  // Upload encrypted file to Supabase Storage
  async uploadFile(
    fileData: string,
    fileName: string,
    mimeType: string,
    category: 'photo' | 'contact' | 'message',
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      console.log(`☁️ Starting upload: ${fileName}`);

      // Check storage quota
      const quota = await this.getStorageQuota();
      const estimatedSize = fileData.length * 1.5; // Estimate encrypted size

      if (quota.remaining < estimatedSize) {
        return {
          success: false,
          error: 'Storage quota exceeded. Please upgrade your plan or delete some files.'
        };
      }

      // Encrypt file data
      const encryptionResult = await EncryptionService.encryptFile(fileData, fileName);

      // Convert encrypted data to Uint8Array for Supabase
      const encryptedData = new TextEncoder().encode(encryptionResult.encryptedData);

      // Generate file path
      const filePath = this.getFilePath(user.id, category, encryptionResult.fileName);

      // Create abort controller for cancellation
      const uploadId = `${user.id}_${Date.now()}`;
      const abortController = new AbortController();
      this.activeUploads.set(uploadId, abortController);

      try {
        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(BUCKETS.USER_DATA)
          .upload(filePath, encryptedData, {
            contentType: 'application/octet-stream',
            metadata: {
              originalName: fileName,
              category,
              encrypted: 'true',
              userId: user.id
            }
          });

        if (uploadError) {
          throw uploadError;
        }

        // Simulate progress for now (Supabase doesn't provide real-time progress)
        if (onProgress) {
          onProgress({
            bytesTransferred: encryptedData.length,
            totalBytes: encryptedData.length,
            percentage: 100,
            state: 'success'
          });
        }

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from(BUCKETS.USER_DATA)
          .getPublicUrl(filePath);

        // Create file metadata
        const fileId = `${user.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const metadata: FileMetadata = {
          id: fileId,
          user_id: user.id,
          original_name: fileName,
          encrypted_name: encryptionResult.fileName,
          mime_type: mimeType,
          size: fileData.length,
          encrypted_size: encryptedData.length,
          uploaded_at: new Date(),
          last_modified: new Date(),
          category,
          hash: await this.generateFileHash(fileData),
          encryption_iv: encryptionResult.iv,
          encryption_salt: encryptionResult.salt,
          storage_path: filePath,
          is_backed_up: true
        };

        // Store metadata in database
        const { error: metadataError } = await supabase
          .from(TABLES.FILE_METADATA)
          .insert(metadata);

        if (metadataError) {
          throw metadataError;
        }

        // Update user storage usage
        await this.updateStorageUsage(user.id);

        console.log(`✅ Upload completed: ${fileName}`);
        this.activeUploads.delete(uploadId);

        return {
          success: true,
          fileId,
          downloadURL: publicUrl,
          metadata
        };
      } catch (uploadError: any) {
        console.error(`❌ Upload failed for ${fileName}:`, uploadError);
        this.activeUploads.delete(uploadId);
        return { success: false, error: uploadError.message || 'Unknown upload error' };
      }
    } catch (error: any) {
      console.error(`❌ Upload preparation failed for ${fileName}:`, error);
      return { success: false, error: error.message || 'Unknown preparation error' };
    }
  }

  // Download and decrypt file
  async downloadFile(fileId: string): Promise<{ success: boolean; data?: string; fileName?: string; error?: string }> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      console.log(`☁️ Starting download: ${fileId}`);

      // Get file metadata
      const { data: metadata, error: metadataError } = await supabase
        .from(TABLES.FILE_METADATA)
        .select('*')
        .eq('id', fileId)
        .single();

      if (metadataError || !metadata) {
        return { success: false, error: 'File not found' };
      }

      // Verify user owns this file
      if (metadata.user_id !== user.id) {
        return { success: false, error: 'Access denied' };
      }

      // Download encrypted file from Supabase Storage
      const { data: fileData, error: downloadError } = await supabase.storage
        .from(BUCKETS.USER_DATA)
        .download(metadata.storage_path);

      if (downloadError || !fileData) {
        return { success: false, error: 'Failed to download file' };
      }

      const encryptedData = await fileData.text();

      // Decrypt file
      const decryptionResult = await EncryptionService.decryptFile(
        encryptedData,
        metadata.encryption_iv,
        metadata.encryption_salt,
        metadata.encrypted_name
      );

      if (!decryptionResult.success) {
        return { success: false, error: decryptionResult.error };
      }

      console.log(`✅ Download completed: ${fileId}`);

      return {
        success: true,
        data: decryptionResult.data,
        fileName: metadata.original_name
      };
    } catch (error: any) {
      console.error(`❌ Download failed for ${fileId}:`, error);
      return { success: false, error: error.message || 'Unknown download error' };
    }
  }

  // Delete file from storage and metadata
  async deleteFile(fileId: string): Promise<boolean> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return false;
      }

      console.log(`🗑️ Deleting file: ${fileId}`);

      // Get file metadata
      const { data: metadata, error: metadataError } = await supabase
        .from(TABLES.FILE_METADATA)
        .select('*')
        .eq('id', fileId)
        .single();

      if (metadataError || !metadata) {
        return false;
      }

      // Verify user owns this file
      if (metadata.user_id !== user.id) {
        return false;
      }

      // Delete from Supabase Storage
      const { error: storageError } = await supabase.storage
        .from(BUCKETS.USER_DATA)
        .remove([metadata.storage_path]);

      if (storageError) {
        console.error('Failed to delete from storage:', storageError);
        // Continue with metadata deletion even if storage deletion fails
      }

      // Delete metadata from database
      const { error: deleteError } = await supabase
        .from(TABLES.FILE_METADATA)
        .delete()
        .eq('id', fileId);

      if (deleteError) {
        throw deleteError;
      }

      // Update storage usage
      await this.updateStorageUsage(user.id);

      console.log(`✅ File deleted: ${fileId}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to delete file ${fileId}:`, error);
      return false;
    }
  }

  // Get user's file list
  async getUserFiles(category?: 'photo' | 'contact' | 'message'): Promise<FileMetadata[]> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return [];
      }

      let query = supabase
        .from(TABLES.FILE_METADATA)
        .select('*')
        .eq('user_id', user.id);

      if (category) {
        query = query.eq('category', category);
      }

      const { data: files, error } = await query.order('uploaded_at', { ascending: false });

      if (error) {
        throw error;
      }

      return (files || []).map(file => ({
        ...file,
        uploaded_at: new Date(file.uploaded_at),
        last_modified: new Date(file.last_modified)
      })) as FileMetadata[];
    } catch (error) {
      console.error('Failed to get user files:', error);
      return [];
    }
  }

  // Get storage quota information
  async getStorageQuota(): Promise<StorageQuota> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) {
        return { used: 0, total: 0, percentage: 0, remaining: 0 };
      }

      const userProfile = await AuthService.getUserProfile();
      if (!userProfile) {
        return { used: 0, total: 0, percentage: 0, remaining: 0 };
      }

      const used = userProfile.storage_used;
      const total = userProfile.storage_quota;
      const percentage = total > 0 ? Math.round((used / total) * 100) : 0;
      const remaining = Math.max(0, total - used);

      return { used, total, percentage, remaining };
    } catch (error) {
      console.error('Failed to get storage quota:', error);
      return { used: 0, total: 0, percentage: 0, remaining: 0 };
    }
  }

  // Cancel active upload
  async cancelUpload(uploadId: string): Promise<boolean> {
    try {
      const abortController = this.activeUploads.get(uploadId);
      if (abortController) {
        abortController.abort();
        this.activeUploads.delete(uploadId);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to cancel upload:', error);
      return false;
    }
  }

  // Private helper methods
  private getFilePath(userId: string, category: 'photo' | 'contact' | 'message', fileName: string): string {
    // Simple path structure: userId/category/fileName
    return `${userId}/${category}s/${fileName}`;
  }

  private async generateFileHash(data: string): Promise<string> {
    // Simple hash for duplicate detection
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private async updateStorageUsage(userId: string): Promise<void> {
    try {
      // Calculate total storage used
      const files = await this.getUserFiles();
      const totalUsed = files.reduce((sum, file) => sum + file.encrypted_size, 0);

      // Update user profile
      await AuthService.updateStorageUsage(totalUsed);
    } catch (error) {
      console.error('Failed to update storage usage:', error);
    }
  }
}

export default new CloudStorageService();
