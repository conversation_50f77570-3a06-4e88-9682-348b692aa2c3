import React, { useState } from 'react';
import { View, Text, StyleSheet, Button, Modal, ActivityIndicator } from 'react-native';
import ProgressBar from 'react-native-progress/Bar';
import { useBackupProgressScreen } from '../../hooks/useBackupProgressScreen'; // Import your custom hook here

interface ProgressItem {
  label: string;
  progress: number;
  currentItem?: string;
  estimatedTime?: string;
  error?: string;
  retry?: () => void;
}

const BackupProgressScreen: React.FC = () => {
  const {
    progressItems,
    handleCancel,
    isBackupInProgress,
  } = useBackupProgressScreen();

  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  const onCancel = () => setShowCancelConfirm(true);
  const onConfirmCancel = async () => {
    setIsCancelling(true);
    await handleCancel();
    setIsCancelling(false);
    setShowCancelConfirm(false);
  };
  const onDismissCancel = () => setShowCancelConfirm(false);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Backup Progress</Text>
      {progressItems.map((item: ProgressItem, idx: number) => (
        <View key={item.label} style={styles.progressItem}>
          <Text style={styles.label}>{item.label}</Text>
          <ProgressBar progress={item.progress} width={null} style={styles.progressBar} />
          {item.currentItem && <Text style={styles.currentItem}>Processing: {item.currentItem}</Text>}
          {item.estimatedTime && <Text style={styles.estimatedTime}>Est. time left: {item.estimatedTime}</Text>}
          {item.error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Error: {item.error}</Text>
              {item.retry && <Button title="Retry" onPress={item.retry} />}
            </View>
          )}
        </View>
      ))}
      <Button title="Cancel Backup" onPress={onCancel} disabled={isCancelling} color="#d9534f" />
      <Modal visible={showCancelConfirm} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text>Are you sure you want to cancel the backup?</Text>
            <View style={styles.modalButtons}>
              <Button title="Yes, Cancel" onPress={onConfirmCancel} color="#d9534f" />
              <Button title="No" onPress={onDismissCancel} />
            </View>
            {isCancelling && <ActivityIndicator style={{ marginTop: 10 }} />}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 24, backgroundColor: '#fff' },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 24, textAlign: 'center' },
  progressItem: { marginBottom: 32 },
  label: { fontSize: 18, fontWeight: '600', marginBottom: 8 },
  progressBar: { height: 12, borderRadius: 6, marginBottom: 8 },
  currentItem: { fontSize: 14, color: '#555' },
  estimatedTime: { fontSize: 14, color: '#888' },
  errorContainer: { marginTop: 8 },
  errorText: { color: '#d9534f', marginBottom: 4 },
  modalOverlay: { flex: 1, backgroundColor: 'rgba(0,0,0,0.4)', justifyContent: 'center', alignItems: 'center' },
  modalContent: { backgroundColor: '#fff', padding: 24, borderRadius: 8, width: 300, alignItems: 'center' },
  modalButtons: { flexDirection: 'row', justifyContent: 'space-between', width: '100%', marginTop: 16 },
});

export default BackupProgressScreen;
