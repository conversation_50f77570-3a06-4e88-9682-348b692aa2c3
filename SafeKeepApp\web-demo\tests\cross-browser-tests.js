/**
 * Cross-Browser Compatibility Tests
 * Tests functionality across different browsers using Playwright
 */

const { chromium, firefox, webkit } = require('playwright');
const fs = require('fs');
const path = require('path');

class CrossBrowserTests {
    constructor() {
        this.browsers = {};
        this.testResults = [];
        this.baseUrl = 'http://localhost:3000';
        this.startTime = Date.now();
        this.browserConfigs = {
            chromium: {
                name: 'Chromium',
                engine: chromium,
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            firefox: {
                name: 'Firefox',
                engine: firefox,
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
            },
            webkit: {
                name: 'Safari/WebKit',
                engine: webkit,
                userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
            }
        };
    }

    async initialize() {
        console.log('🌐 Initializing Cross-Browser Tests...');
        
        // Launch all browsers
        for (const [browserName, config] of Object.entries(this.browserConfigs)) {
            try {
                this.browsers[browserName] = await config.engine.launch({
                    headless: true,
                    args: ['--no-sandbox', '--disable-setuid-sandbox']
                });
                console.log(`✅ ${config.name} browser launched`);
            } catch (error) {
                console.warn(`⚠️ Failed to launch ${config.name}: ${error.message}`);
            }
        }

        // Ensure test reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
    }

    async runAllTests() {
        await this.initialize();

        const testSuites = [
            this.testBasicFunctionality,
            this.testWebSocketSupport,
            this.testLocalStorageSupport,
            this.testCSSCompatibility,
            this.testJavaScriptFeatures,
            this.testFormHandling,
            this.testFileAPISupport,
            this.testWebCryptoAPI,
            this.testResponsiveDesign,
            this.testAccessibilityFeatures
        ];

        console.log('🌐 Running Cross-Browser Compatibility Tests...');
        console.log('===============================================');

        for (const testSuite of testSuites) {
            await this.runTestSuiteAcrossBrowsers(testSuite);
        }

        await this.cleanup();
        this.generateReport();
    }

    async runTestSuiteAcrossBrowsers(testFunction) {
        const testName = testFunction.name;
        console.log(`\n🧪 Running ${testName} across all browsers...`);

        for (const [browserName, browser] of Object.entries(this.browsers)) {
            if (!browser) continue;

            const startTime = Date.now();
            let context = null;
            let page = null;

            try {
                context = await browser.newContext({
                    userAgent: this.browserConfigs[browserName].userAgent,
                    viewport: { width: 1280, height: 720 }
                });
                
                page = await context.newPage();
                
                const result = await testFunction.call(this, page, browserName);
                const duration = Date.now() - startTime;

                this.testResults.push({
                    test: testName,
                    browser: this.browserConfigs[browserName].name,
                    status: result.success ? 'PASSED' : 'FAILED',
                    duration,
                    details: result.details,
                    compatibility: result.compatibility || {},
                    errors: result.errors || []
                });

                console.log(`  ${this.browserConfigs[browserName].name}: ${result.success ? '✅' : '❌'} - ${duration}ms`);

            } catch (error) {
                const duration = Date.now() - startTime;
                console.error(`  ${this.browserConfigs[browserName].name}: ❌ - ${error.message}`);
                
                this.testResults.push({
                    test: testName,
                    browser: this.browserConfigs[browserName].name,
                    status: 'ERROR',
                    duration,
                    details: 'Test execution failed',
                    compatibility: {},
                    errors: [error.message]
                });
            } finally {
                if (page) await page.close();
                if (context) await context.close();
            }
        }
    }

    async testBasicFunctionality(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 10000 });
            steps.push('✅ Page loaded successfully');

            // Test basic DOM manipulation
            const title = await page.textContent('h1');
            if (title) {
                steps.push('✅ DOM content accessible');
                compatibility.domSupport = true;
            }

            // Test event handling
            await page.click('#start-backup-btn');
            await page.waitForSelector('#backup-progress-container', { timeout: 5000 });
            steps.push('✅ Event handling working');
            compatibility.eventHandling = true;

            // Test CSS animations
            const hasAnimations = await page.evaluate(() => {
                const element = document.querySelector('#backup-progress-bar');
                if (element) {
                    const computedStyle = window.getComputedStyle(element);
                    return computedStyle.animationName !== 'none' || computedStyle.transitionProperty !== 'none';
                }
                return false;
            });
            
            if (hasAnimations) {
                steps.push('✅ CSS animations supported');
                compatibility.cssAnimations = true;
            }

            return {
                success: true,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testWebSocketSupport(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test WebSocket availability
            const webSocketSupport = await page.evaluate(() => {
                return typeof WebSocket !== 'undefined';
            });

            compatibility.webSocketSupport = webSocketSupport;
            steps.push(`${webSocketSupport ? '✅' : '❌'} WebSocket API available`);

            if (webSocketSupport) {
                // Test WebSocket connection
                const connectionTest = await page.evaluate(() => {
                    return new Promise((resolve) => {
                        try {
                            const ws = new WebSocket('ws://localhost:8080');
                            ws.onopen = () => {
                                ws.close();
                                resolve(true);
                            };
                            ws.onerror = () => resolve(false);
                            setTimeout(() => resolve(false), 3000);
                        } catch (error) {
                            resolve(false);
                        }
                    });
                });

                compatibility.webSocketConnection = connectionTest;
                steps.push(`${connectionTest ? '✅' : '⚠️'} WebSocket connection ${connectionTest ? 'successful' : 'failed (server may be down)'}`);
            }

            return {
                success: webSocketSupport,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testLocalStorageSupport(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test localStorage availability
            const localStorageTest = await page.evaluate(() => {
                try {
                    const testKey = 'browser-test-key';
                    const testValue = 'browser-test-value';
                    
                    localStorage.setItem(testKey, testValue);
                    const retrieved = localStorage.getItem(testKey);
                    localStorage.removeItem(testKey);
                    
                    return retrieved === testValue;
                } catch (error) {
                    return false;
                }
            });

            compatibility.localStorage = localStorageTest;
            steps.push(`${localStorageTest ? '✅' : '❌'} localStorage support`);

            // Test sessionStorage
            const sessionStorageTest = await page.evaluate(() => {
                try {
                    const testKey = 'session-test-key';
                    const testValue = 'session-test-value';
                    
                    sessionStorage.setItem(testKey, testValue);
                    const retrieved = sessionStorage.getItem(testKey);
                    sessionStorage.removeItem(testKey);
                    
                    return retrieved === testValue;
                } catch (error) {
                    return false;
                }
            });

            compatibility.sessionStorage = sessionStorageTest;
            steps.push(`${sessionStorageTest ? '✅' : '❌'} sessionStorage support`);

            // Test IndexedDB
            const indexedDBTest = await page.evaluate(() => {
                return typeof indexedDB !== 'undefined';
            });

            compatibility.indexedDB = indexedDBTest;
            steps.push(`${indexedDBTest ? '✅' : '❌'} IndexedDB support`);

            const allStorageSupported = localStorageTest && sessionStorageTest && indexedDBTest;

            return {
                success: allStorageSupported,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testCSSCompatibility(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test CSS Grid support
            const gridSupport = await page.evaluate(() => {
                const testElement = document.createElement('div');
                testElement.style.display = 'grid';
                return testElement.style.display === 'grid';
            });

            compatibility.cssGrid = gridSupport;
            steps.push(`${gridSupport ? '✅' : '❌'} CSS Grid support`);

            // Test Flexbox support
            const flexboxSupport = await page.evaluate(() => {
                const testElement = document.createElement('div');
                testElement.style.display = 'flex';
                return testElement.style.display === 'flex';
            });

            compatibility.flexbox = flexboxSupport;
            steps.push(`${flexboxSupport ? '✅' : '❌'} Flexbox support`);

            // Test CSS Variables
            const cssVariablesSupport = await page.evaluate(() => {
                const testElement = document.createElement('div');
                testElement.style.setProperty('--test-var', 'test');
                return testElement.style.getPropertyValue('--test-var') === 'test';
            });

            compatibility.cssVariables = cssVariablesSupport;
            steps.push(`${cssVariablesSupport ? '✅' : '❌'} CSS Variables support`);

            // Test CSS Transforms
            const transformSupport = await page.evaluate(() => {
                const testElement = document.createElement('div');
                testElement.style.transform = 'translateX(10px)';
                return testElement.style.transform !== '';
            });

            compatibility.cssTransforms = transformSupport;
            steps.push(`${transformSupport ? '✅' : '❌'} CSS Transforms support`);

            // Test CSS Transitions
            const transitionSupport = await page.evaluate(() => {
                const testElement = document.createElement('div');
                testElement.style.transition = 'opacity 0.3s';
                return testElement.style.transition !== '';
            });

            compatibility.cssTransitions = transitionSupport;
            steps.push(`${transitionSupport ? '✅' : '❌'} CSS Transitions support`);

            const allCSSSupported = gridSupport && flexboxSupport && cssVariablesSupport && 
                                   transformSupport && transitionSupport;

            return {
                success: allCSSSupported,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testJavaScriptFeatures(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test ES6 features
            const es6Support = await page.evaluate(() => {
                try {
                    // Arrow functions
                    const arrowFunc = () => true;
                    
                    // Template literals
                    const template = `test ${1 + 1}`;
                    
                    // Destructuring
                    const [a, b] = [1, 2];
                    
                    // Promises
                    const promise = new Promise(resolve => resolve(true));
                    
                    // Classes
                    class TestClass {
                        constructor() {
                            this.value = true;
                        }
                    }
                    
                    return arrowFunc() && template === 'test 2' && a === 1 && 
                           promise instanceof Promise && new TestClass().value;
                } catch (error) {
                    return false;
                }
            });

            compatibility.es6Features = es6Support;
            steps.push(`${es6Support ? '✅' : '❌'} ES6 features support`);

            // Test Async/Await
            const asyncAwaitSupport = await page.evaluate(async () => {
                try {
                    const asyncFunc = async () => {
                        await new Promise(resolve => setTimeout(resolve, 1));
                        return true;
                    };
                    return await asyncFunc();
                } catch (error) {
                    return false;
                }
            });

            compatibility.asyncAwait = asyncAwaitSupport;
            steps.push(`${asyncAwaitSupport ? '✅' : '❌'} Async/Await support`);

            // Test Fetch API
            const fetchSupport = await page.evaluate(() => {
                return typeof fetch !== 'undefined';
            });

            compatibility.fetchAPI = fetchSupport;
            steps.push(`${fetchSupport ? '✅' : '❌'} Fetch API support`);

            // Test JSON support
            const jsonSupport = await page.evaluate(() => {
                try {
                    const obj = { test: true };
                    const json = JSON.stringify(obj);
                    const parsed = JSON.parse(json);
                    return parsed.test === true;
                } catch (error) {
                    return false;
                }
            });

            compatibility.jsonSupport = jsonSupport;
            steps.push(`${jsonSupport ? '✅' : '❌'} JSON support`);

            const allJSSupported = es6Support && asyncAwaitSupport && fetchSupport && jsonSupport;

            return {
                success: allJSSupported,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testFormHandling(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to a form (signup form)
            await page.click('#signup-btn');
            await page.waitForSelector('#signup-form', { timeout: 3000 });

            // Test form input types
            const inputTypeSupport = await page.evaluate(() => {
                const emailInput = document.createElement('input');
                emailInput.type = 'email';
                const passwordInput = document.createElement('input');
                passwordInput.type = 'password';
                
                return {
                    email: emailInput.type === 'email',
                    password: passwordInput.type === 'password'
                };
            });

            compatibility.inputTypes = inputTypeSupport;
            steps.push(`${inputTypeSupport.email ? '✅' : '❌'} Email input type support`);
            steps.push(`${inputTypeSupport.password ? '✅' : '❌'} Password input type support`);

            // Test form validation
            const validationSupport = await page.evaluate(() => {
                const form = document.createElement('form');
                const input = document.createElement('input');
                input.required = true;
                form.appendChild(input);
                
                return typeof input.checkValidity === 'function';
            });

            compatibility.formValidation = validationSupport;
            steps.push(`${validationSupport ? '✅' : '❌'} Form validation API support`);

            // Test form submission
            await page.fill('#email-input', '<EMAIL>');
            await page.fill('#password-input', 'testpassword');
            
            const formSubmissionTest = await page.evaluate(() => {
                const form = document.querySelector('#signup-form');
                return form && typeof form.submit === 'function';
            });

            compatibility.formSubmission = formSubmissionTest;
            steps.push(`${formSubmissionTest ? '✅' : '❌'} Form submission support`);

            const allFormSupported = inputTypeSupport.email && inputTypeSupport.password && 
                                   validationSupport && formSubmissionTest;

            return {
                success: allFormSupported,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testFileAPISupport(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test File API
            const fileAPISupport = await page.evaluate(() => {
                return typeof File !== 'undefined' && 
                       typeof FileReader !== 'undefined' && 
                       typeof Blob !== 'undefined';
            });

            compatibility.fileAPI = fileAPISupport;
            steps.push(`${fileAPISupport ? '✅' : '❌'} File API support`);

            // Test FileReader functionality
            if (fileAPISupport) {
                const fileReaderTest = await page.evaluate(() => {
                    return new Promise((resolve) => {
                        try {
                            const reader = new FileReader();
                            const blob = new Blob(['test content'], { type: 'text/plain' });
                            
                            reader.onload = () => {
                                resolve(reader.result === 'test content');
                            };
                            reader.onerror = () => resolve(false);
                            
                            reader.readAsText(blob);
                        } catch (error) {
                            resolve(false);
                        }
                    });
                });

                compatibility.fileReader = fileReaderTest;
                steps.push(`${fileReaderTest ? '✅' : '❌'} FileReader functionality`);
            }

            // Test drag and drop support
            const dragDropSupport = await page.evaluate(() => {
                const div = document.createElement('div');
                return 'ondragstart' in div && 'ondrop' in div;
            });

            compatibility.dragDrop = dragDropSupport;
            steps.push(`${dragDropSupport ? '✅' : '❌'} Drag and drop support`);

            const allFileSupported = fileAPISupport && (compatibility.fileReader !== false) && dragDropSupport;

            return {
                success: allFileSupported,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testWebCryptoAPI(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test Web Crypto API availability
            const webCryptoSupport = await page.evaluate(() => {
                return typeof crypto !== 'undefined' && 
                       typeof crypto.subtle !== 'undefined';
            });

            compatibility.webCrypto = webCryptoSupport;
            steps.push(`${webCryptoSupport ? '✅' : '❌'} Web Crypto API support`);

            if (webCryptoSupport) {
                // Test basic crypto operations
                const cryptoOperationTest = await page.evaluate(async () => {
                    try {
                        // Generate random values
                        const randomArray = new Uint8Array(16);
                        crypto.getRandomValues(randomArray);
                        
                        // Test key generation
                        const key = await crypto.subtle.generateKey(
                            {
                                name: 'AES-GCM',
                                length: 256
                            },
                            true,
                            ['encrypt', 'decrypt']
                        );
                        
                        return randomArray.some(val => val !== 0) && key !== null;
                    } catch (error) {
                        return false;
                    }
                });

                compatibility.cryptoOperations = cryptoOperationTest;
                steps.push(`${cryptoOperationTest ? '✅' : '❌'} Crypto operations functionality`);
            }

            return {
                success: webCryptoSupport && (compatibility.cryptoOperations !== false),
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testResponsiveDesign(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            // Test different viewport sizes
            const viewports = [
                { width: 320, height: 568, name: 'Mobile' },
                { width: 768, height: 1024, name: 'Tablet' },
                { width: 1280, height: 720, name: 'Desktop' }
            ];

            for (const viewport of viewports) {
                await page.setViewportSize(viewport);
                await page.goto(this.baseUrl);
                await page.waitForSelector('#demo-container', { timeout: 5000 });

                // Check if layout adapts
                const layoutTest = await page.evaluate(() => {
                    const container = document.querySelector('#demo-container');
                    if (!container) return false;
                    
                    const computedStyle = window.getComputedStyle(container);
                    return computedStyle.display !== 'none' && 
                           container.offsetWidth > 0 && 
                           container.offsetHeight > 0;
                });

                compatibility[`${viewport.name.toLowerCase()}Layout`] = layoutTest;
                steps.push(`${layoutTest ? '✅' : '❌'} ${viewport.name} layout (${viewport.width}x${viewport.height})`);
            }

            // Test media queries
            const mediaQuerySupport = await page.evaluate(() => {
                return typeof window.matchMedia !== 'undefined';
            });

            compatibility.mediaQueries = mediaQuerySupport;
            steps.push(`${mediaQuerySupport ? '✅' : '❌'} Media queries support`);

            const allResponsiveSupported = Object.values(compatibility).every(val => val === true);

            return {
                success: allResponsiveSupported,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async testAccessibilityFeatures(page, browserName) {
        const steps = [];
        const compatibility = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test ARIA support
            const ariaSupport = await page.evaluate(() => {
                const element = document.createElement('div');
                element.setAttribute('aria-label', 'test');
                element.setAttribute('role', 'button');
                
                return element.getAttribute('aria-label') === 'test' && 
                       element.getAttribute('role') === 'button';
            });

            compatibility.ariaSupport = ariaSupport;
            steps.push(`${ariaSupport ? '✅' : '❌'} ARIA attributes support`);

            // Test keyboard navigation
            const keyboardNavTest = await page.evaluate(() => {
                const buttons = document.querySelectorAll('button');
                return Array.from(buttons).some(button => 
                    button.tabIndex >= 0 || button.tabIndex === undefined
                );
            });

            compatibility.keyboardNavigation = keyboardNavTest;
            steps.push(`${keyboardNavTest ? '✅' : '❌'} Keyboard navigation support`);

            // Test focus management
            const focusTest = await page.evaluate(() => {
                const button = document.querySelector('#start-backup-btn');
                if (button) {
                    button.focus();
                    return document.activeElement === button;
                }
                return false;
            });

            compatibility.focusManagement = focusTest;
            steps.push(`${focusTest ? '✅' : '❌'} Focus management`);

            // Test screen reader compatibility
            const screenReaderSupport = await page.evaluate(() => {
                const elements = document.querySelectorAll('[aria-label], [aria-describedby], [role]');
                return elements.length > 0;
            });

            compatibility.screenReaderSupport = screenReaderSupport;
            steps.push(`${screenReaderSupport ? '✅' : '❌'} Screen reader compatibility`);

            const allAccessibilitySupported = ariaSupport && keyboardNavTest && 
                                            focusTest && screenReaderSupport;

            return {
                success: allAccessibilitySupported,
                details: steps.join('\n'),
                compatibility
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                compatibility,
                errors: [error.message]
            };
        }
    }

    async cleanup() {
        for (const [browserName, browser] of Object.entries(this.browsers)) {
            if (browser) {
                await browser.close();
                console.log(`✅ ${this.browserConfigs[browserName].name} browser closed`);
            }
        }
    }

    generateReport() {
        const totalDuration = Date.now() - this.startTime;
        
        // Calculate results by browser
        const browserResults = {};
        const testResults = {};
        
        this.testResults.forEach(result => {
            if (!browserResults[result.browser]) {
                browserResults[result.browser] = { passed: 0, failed: 0, errors: 0, total: 0 };
            }
            if (!testResults[result.test]) {
                testResults[result.test] = { passed: 0, failed: 0, errors: 0, total: 0 };
            }
            
            browserResults[result.browser].total++;
            testResults[result.test].total++;
            
            if (result.status === 'PASSED') {
                browserResults[result.browser].passed++;
                testResults[result.test].passed++;
            } else if (result.status === 'FAILED') {
                browserResults[result.browser].failed++;
                testResults[result.test].failed++;
            } else {
                browserResults[result.browser].errors++;
                testResults[result.test].errors++;
            }
        });

        console.log('\n🌐 Cross-Browser Compatibility Test Results');
        console.log('===========================================');
        
        // Browser-wise results
        console.log('\n📊 Results by Browser:');
        Object.entries(browserResults).forEach(([browser, stats]) => {
            const successRate = ((stats.passed / stats.total) * 100).toFixed(1);
            console.log(`${browser}:`);
            console.log(`  Passed: ${stats.passed}/${stats.total} (${successRate}%)`);
            console.log(`  Failed: ${stats.failed}`);
            console.log(`  Errors: ${stats.errors}`);
        });

        // Test-wise results
        console.log('\n📋 Results by Test:');
        Object.entries(testResults).forEach(([test, stats]) => {
            const successRate = ((stats.passed / stats.total) * 100).toFixed(1);
            console.log(`${test}:`);
            console.log(`  Passed: ${stats.passed}/${stats.total} (${successRate}%)`);
            console.log(`  Failed: ${stats.failed}`);
            console.log(`  Errors: ${stats.errors}`);
        });

        // Compatibility matrix
        console.log('\n🔍 Feature Compatibility Matrix:');
        const compatibilityMatrix = {};
        this.testResults.forEach(result => {
            if (result.compatibility) {
                Object.entries(result.compatibility).forEach(([feature, supported]) => {
                    if (!compatibilityMatrix[feature]) {
                        compatibilityMatrix[feature] = {};
                    }
                    compatibilityMatrix[feature][result.browser] = supported;
                });
            }
        });

        Object.entries(compatibilityMatrix).forEach(([feature, browsers]) => {
            console.log(`\n${feature}:`);
            Object.entries(browsers).forEach(([browser, supported]) => {
                console.log(`  ${browser}: ${supported ? '✅' : '❌'}`);
            });
        });

        // Generate detailed report
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalDuration,
                browserResults,
                testResults,
                compatibilityMatrix
            },
            results: this.testResults
        };

        // Ensure reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        fs.writeFileSync(
            path.join(reportsDir, 'cross-browser-test-report.json'),
            JSON.stringify(report, null, 2)
        );

        console.log(`\n📄 Report saved to test-reports/cross-browser-test-report.json`);
        console.log(`⏱️ Total test duration: ${totalDuration}ms`);
    }
}

// Run tests if called directly
if (require.main === module) {
    const runner = new CrossBrowserTests();
    runner.runAllTests().catch(console.error);
}

module.exports = CrossBrowserTests;