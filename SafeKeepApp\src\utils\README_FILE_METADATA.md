# File Metadata Management

This module provides comprehensive functionality for managing file metadata in the SafeKeep application. It handles storing, retrieving, organizing, and deduplicating file metadata in the Supabase backend.

## Features

### Core Functionality
- Store and retrieve file metadata
- Search files with advanced filtering and sorting
- Update file metadata properties
- Delete file metadata

### File Organization
- Virtual folder management
- File tagging system
- Move files between folders
- Rename and delete virtual folders

### File Deduplication
- Detect duplicate files using hash comparison
- Find all duplicate files for a user
- Resolve duplicates by keeping one file and deleting others
- Compare files to check if they are identical

## Usage Examples

### Creating File Metadata

```typescript
import { createFileMetadata } from './fileMetadataManager';

// Create new file metadata with deduplication check
const result = await createFileMetadata({
  user_id: userId,
  original_name: 'vacation-photo.jpg',
  encrypted_name: 'encrypted-filename.enc',
  mime_type: 'image/jpeg',
  size: 1024000,
  encrypted_size: 1500000,
  category: 'photo',
  hash: fileHash,
  encryption_iv: 'iv-value',
  encryption_salt: 'salt-value',
  storage_path: `${userId}/photos/encrypted-filename.enc`
});

if (result.isDuplicate) {
  console.log('File already exists:', result.existingMetadata);
} else if (result.id) {
  console.log('File metadata created with ID:', result.id);
} else {
  console.error('Failed to create file metadata:', result.error);
}
```

### Searching Files

```typescript
import { searchFiles, FileSearchParams } from './fileMetadataManager';

// Search for photos from last month with specific tags
const searchParams: FileSearchParams = {
  category: 'photo',
  startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
  endDate: new Date(),
  tags: ['vacation', 'family'],
  sortBy: 'uploaded_at',
  sortDirection: 'desc',
  limit: 20,
  offset: 0
};

const { data: files, totalCount, error } = await searchFiles(searchParams);

console.log(`Found ${totalCount} files, showing ${files.length}`);
```

### Managing Tags

```typescript
import { addTagsToFile, removeTagsFromFile, getUserTags } from './fileMetadataManager';

// Add tags to a file
await addTagsToFile('file-id', ['vacation', 'beach', '2023']);

// Remove tags from a file
await removeTagsFromFile('file-id', ['beach']);

// Get all unique tags used by the current user
const { tags } = await getUserTags();
console.log('User tags:', tags);
```

### Finding Duplicate Files

```typescript
import { findAllDuplicates, resolveDuplicates } from './fileDeduplication';

// Find all duplicate files
const { duplicateGroups, totalPotentialSavings } = await findAllDuplicates();

console.log(`Found ${duplicateGroups.length} groups of duplicates`);
console.log(`Potential storage savings: ${totalPotentialSavings} bytes`);

// Resolve duplicates by keeping one file and deleting others
if (duplicateGroups.length > 0) {
  const group = duplicateGroups[0];
  const fileToKeep = group.files[0].id;
  
  await resolveDuplicates(group.hash, fileToKeep);
  console.log('Duplicates resolved');
}
```

### Managing Virtual Folders

```typescript
import { 
  createVirtualFolder, 
  moveFileToFolder, 
  getVirtualFolders 
} from './fileOrganizer';

// Create a new virtual folder
await createVirtualFolder('Vacations/Summer2023');

// Move a file to a folder
await moveFileToFolder('file-id', 'Vacations/Summer2023');

// Get all virtual folders
const { folders } = await getVirtualFolders();
console.log('Virtual folders:', folders);
```

## Database Schema

The file metadata is stored in the `file_metadata` table with the following structure:

```sql
CREATE TABLE file_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_name TEXT NOT NULL,
  encrypted_name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT NOT NULL,
  encrypted_size BIGINT NOT NULL,
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  last_modified TIMESTAMPTZ DEFAULT NOW(),
  category TEXT CHECK (category IN ('photo', 'contact', 'message')),
  hash TEXT NOT NULL,
  encryption_iv TEXT NOT NULL,
  encryption_salt TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  is_backed_up BOOLEAN DEFAULT true,
  device_id TEXT,
  sync_status TEXT DEFAULT 'synced',
  tags TEXT[] DEFAULT '{}'
);
```

## Security Considerations

- All file metadata operations enforce Row-Level Security (RLS) policies
- Users can only access their own file metadata
- File content is stored encrypted in Supabase Storage
- Hash values are used for deduplication but don't compromise encryption