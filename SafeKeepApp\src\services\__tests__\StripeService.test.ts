import { StripeService, SAFEKEEP_SERVICES, SAFEKEEP_COMBINATIONS } from '../StripeService';

// Mock fetch for testing
global.fetch = jest.fn();

describe('StripeService - Modular Pricing Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Management', () => {
    test('should get individual service by ID', () => {
      const service = StripeService.getService('contacts');
      expect(service).toBeDefined();
      expect(service?.id).toBe('contacts');
      expect(service?.name).toBe('Contacts Backup');
      expect(service?.price).toBe(199);
    });

    test('should get all individual services', () => {
      const services = StripeService.getAllServices();
      expect(Object.keys(services)).toHaveLength(3);
      expect(services.CONTACTS.id).toBe('contacts');
      expect(services.MESSAGES.id).toBe('messages');
      expect(services.PHOTOS.id).toBe('photos');
    });

    test('should get combination plan by ID', () => {
      const combination = StripeService.getCombination('contacts_messages');
      expect(combination).toBeDefined();
      expect(combination?.id).toBe('contacts_messages');
      expect(combination?.services).toEqual(['contacts', 'messages']);
      expect(combination?.price).toBe(399);
    });

    test('should get all combination plans', () => {
      const combinations = StripeService.getAllCombinations();
      expect(Object.keys(combinations)).toHaveLength(4);
      expect(combinations.ALL_SERVICES.services).toEqual(['contacts', 'messages', 'photos']);
    });
  });

  describe('Pricing Calculations', () => {
    test('should calculate individual service total correctly', () => {
      const total = StripeService.calculateIndividualTotal(['contacts', 'messages']);
      expect(total).toBe(498); // 199 + 299
    });

    test('should find best combination for services', () => {
      const result = StripeService.findBestCombination(['contacts', 'messages']);
      expect(result.combination?.id).toBe('contacts_messages');
      expect(result.savings).toBe(99); // 498 - 399
      expect(result.individualTotal).toBe(498);
    });

    test('should return no combination when individual is better', () => {
      const result = StripeService.findBestCombination(['contacts']);
      expect(result.combination).toBeNull();
      expect(result.savings).toBe(0);
      expect(result.individualTotal).toBe(199);
    });
  });

  describe('API Integration', () => {
    test('should fetch service combinations from backend', async () => {
      const mockResponse = {
        success: true,
        data: [
          {
            planId: 'contacts_messages',
            planName: 'Contacts + Messages',
            priceCents: 399,
            services: ['contacts', 'messages'],
            storageGb: 5,
            isPopular: false
          }
        ]
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const combinations = await StripeService.getServiceCombinations();
      expect(combinations).toEqual(mockResponse.data);
      expect(fetch).toHaveBeenCalledWith('http://localhost:3000/api/pricing/combinations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });

    test('should calculate optimal price via backend', async () => {
      const mockResponse = {
        success: true,
        data: {
          recommendedPlanId: 'contacts_messages',
          recommendedPlanName: 'Contacts + Messages',
          priceCents: 399,
          savingsCents: 99,
          individualTotalCents: 498
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await StripeService.calculateOptimalPrice(['contacts', 'messages']);
      expect(result).toEqual(mockResponse.data);
      expect(fetch).toHaveBeenCalledWith('http://localhost:3000/api/pricing/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ serviceIds: ['contacts', 'messages'] }),
      });
    });

    test('should validate service combination via backend', async () => {
      const mockResponse = {
        success: true,
        data: {
          isValid: true,
          errors: [],
          warnings: ['Consider the Contacts + Messages plan for better value']
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await StripeService.validateServiceCombination(['contacts', 'messages']);
      expect(result).toEqual(mockResponse.data);
      expect(fetch).toHaveBeenCalledWith('http://localhost:3000/api/services/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ serviceIds: ['contacts', 'messages'] }),
      });
    });

    test('should create payment intent with service metadata', async () => {
      const mockResponse = {
        success: true,
        data: {
          client_secret: 'pi_test_client_secret',
          payment_intent_id: 'pi_test_123',
          amount: 399,
          serviceIds: ['contacts', 'messages'],
          planName: 'Contacts + Messages'
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const paymentData = {
        amount: 399,
        currency: 'usd',
        description: 'Contacts + Messages Plan',
        serviceIds: ['contacts', 'messages'],
        userId: 'user_123',
        planId: 'contacts_messages',
        planName: 'Contacts + Messages'
      };

      const result = await StripeService.createPaymentIntent(paymentData);
      expect(result).toEqual(mockResponse.data);
      expect(fetch).toHaveBeenCalledWith('http://localhost:3000/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: 399,
          currency: 'usd',
          description: 'Contacts + Messages Plan',
          customerId: undefined,
          serviceIds: ['contacts', 'messages'],
          userId: 'user_123',
          planId: 'contacts_messages',
          planName: 'Contacts + Messages',
          metadata: undefined,
        }),
      });
    });

    test('should create modular subscription', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 'sub_123',
          userId: 'user_123',
          planId: 'contacts_messages',
          serviceIds: ['contacts', 'messages'],
          totalPriceCents: 399,
          status: 'active'
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const subscriptionRequest = {
        userId: 'user_123',
        serviceIds: ['contacts', 'messages'],
        paymentMethodId: 'pm_test_123'
      };

      const result = await StripeService.createModularSubscription(subscriptionRequest);
      expect(result).toEqual(mockResponse.data);
      expect(fetch).toHaveBeenCalledWith('http://localhost:3000/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscriptionRequest),
      });
    });

    test('should get user subscription details', async () => {
      const mockResponse = {
        success: true,
        data: {
          subscriptionId: 'sub_123',
          planId: 'contacts_messages',
          planName: 'Contacts + Messages',
          currentPriceCents: 399,
          status: 'active',
          services: ['contacts', 'messages']
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await StripeService.getUserSubscription('user_123');
      expect(result).toEqual(mockResponse.data);
      expect(fetch).toHaveBeenCalledWith('http://localhost:3000/api/subscriptions/user_123', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });

    test('should check service access', async () => {
      const mockResponse = {
        success: true,
        data: {
          hasAccess: true,
          expiresAt: new Date('2024-02-01'),
          planName: 'Contacts + Messages'
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await StripeService.checkServiceAccess('user_123', 'contacts');
      expect(result).toEqual(mockResponse.data);
      expect(fetch).toHaveBeenCalledWith('http://localhost:3000/api/services/access/user_123/contacts', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      const mockErrorResponse = {
        success: false,
        error: {
          code: 'PRICING_ERROR',
          message: 'Failed to retrieve service combinations',
          timestamp: new Date().toISOString()
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => mockErrorResponse,
      });

      await expect(StripeService.getServiceCombinations()).rejects.toThrow('Failed to retrieve service combinations');
    });

    test('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(StripeService.calculateOptimalPrice(['contacts'])).rejects.toThrow('Network error');
    });

    test('should handle invalid service IDs in calculations', () => {
      const total = StripeService.calculateIndividualTotal(['invalid_service']);
      expect(total).toBe(0);
    });
  });

  describe('Backward Compatibility', () => {
    test('should support legacy getPlan method', () => {
      const plan = StripeService.getPlan('contacts');
      expect(plan).toBeDefined();
      expect(plan?.id).toBe('contacts');
    });

    test('should support legacy getAllPlans method', () => {
      const plans = StripeService.getAllPlans();
      expect(Object.keys(plans)).toHaveLength(7); // 3 services + 4 combinations
    });
  });
});