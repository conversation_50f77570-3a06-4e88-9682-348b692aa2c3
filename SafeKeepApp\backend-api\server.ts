import app from './src/app';

const PORT = process.env.PORT || 3000;

/**
 * Server startup for SafeKeep modular pricing backend API
 */

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 SafeKeep Modular Pricing API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`💳 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Log available endpoints
  console.log('\n📋 Available endpoints:');
  console.log(`   GET  /api/health - Health check`);
  console.log(`   GET  /api/pricing/combinations - Get service combinations`);
  console.log(`   POST /api/pricing/calculate - Calculate optimal pricing`);
  console.log(`   GET  /api/pricing/recommendations/:userId - Get plan recommendations`);
  console.log(`   POST /api/subscriptions - Create subscription`);
  console.log(`   PUT  /api/subscriptions/:subscriptionId - Update subscription`);
  console.log(`   GET  /api/subscriptions/:userId - Get subscription details`);
  console.log(`   DELETE /api/subscriptions/:subscriptionId - Cancel subscription`);
  console.log(`   GET  /api/services/user/:userId - Get user services`);
  console.log(`   POST /api/services/validate - Validate service combination`);
  console.log(`   GET  /api/services/access/:userId/:serviceType - Check service access`);
  console.log(`   POST /api/billing/payment-intent - Create payment intent`);
  console.log(`   POST /api/billing/webhook - Handle Stripe webhooks`);
  console.log(`   GET  /api/billing/status/:userId - Get billing status`);
  
  // Validate environment configuration
  if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY.includes('PLACEHOLDER')) {
    console.warn('⚠️  WARNING: Stripe secret key not properly configured!');
  }
  
  if (!process.env.JWT_SECRET) {
    console.warn('⚠️  WARNING: JWT secret not configured!');
  }
  
  if (!process.env.DATABASE_URL && !process.env.SUPABASE_URL) {
    console.warn('⚠️  WARNING: Database connection not configured!');
  }
  
  console.log('\n✅ Server startup complete');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Server shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Server shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

export default server;