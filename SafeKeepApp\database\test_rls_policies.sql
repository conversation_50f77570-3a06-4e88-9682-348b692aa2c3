-- <PERSON><PERSON> script to test RLS policies for file_metadata table

-- First, let's check if <PERSON><PERSON> is enabled on the file_metadata table
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'file_metadata';

-- List all policies on the file_metadata table
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'file_metadata';

-- Test policy for SELECT
-- This should return rows only for the current user
SELECT 
  'SELECT policy test' as test_name,
  COUNT(*) as row_count,
  CASE 
    WHEN COUNT(*) > 0 THEN 'User can read their own data'
    ELSE 'No data found for user'
  END as result
FROM file_metadata
WHERE user_id = auth.uid();

-- Test policy for INSERT
-- This should succeed for the current user
DO $$
DECLARE
  test_id UUID;
BEGIN
  -- Try to insert a row for the current user
  INSERT INTO file_metadata (
    user_id, original_name, encrypted_name, mime_type, 
    size, encrypted_size, category, hash, 
    encryption_iv, encryption_salt, storage_path
  ) VALUES (
    auth.uid(), 'test.txt', 'test.enc', 'text/plain',
    100, 150, 'photo', 'test-hash',
    'test-iv', 'test-salt', auth.uid() || '/test.txt'
  )
  RETURNING id INTO test_id;
  
  -- Clean up the test data
  DELETE FROM file_metadata WHERE id = test_id;
  
  RAISE NOTICE 'INSERT policy test: User can insert their own data (ID: %)', test_id;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'INSERT policy test: User cannot insert their own data - %', SQLERRM;
END;
$$;

-- Test policy for INSERT with another user's ID
-- This should fail
DO $$
BEGIN
  -- Try to insert a row for another user
  INSERT INTO file_metadata (
    user_id, original_name, encrypted_name, mime_type, 
    size, encrypted_size, category, hash, 
    encryption_iv, encryption_salt, storage_path
  ) VALUES (
    '00000000-0000-0000-0000-000000000000', 'test.txt', 'test.enc', 'text/plain',
    100, 150, 'photo', 'test-hash',
    'test-iv', 'test-salt', '00000000-0000-0000-0000-000000000000/test.txt'
  );
  
  RAISE NOTICE 'INSERT policy test (negative): Security issue - User can insert data for other users';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'INSERT policy test (negative): Correct behavior - User cannot insert data for other users';
END;
$$;

-- Test policy for UPDATE
-- This should succeed for the current user's data
DO $$
DECLARE
  test_id UUID;
BEGIN
  -- Insert a test row
  INSERT INTO file_metadata (
    user_id, original_name, encrypted_name, mime_type, 
    size, encrypted_size, category, hash, 
    encryption_iv, encryption_salt, storage_path
  ) VALUES (
    auth.uid(), 'test.txt', 'test.enc', 'text/plain',
    100, 150, 'photo', 'test-hash',
    'test-iv', 'test-salt', auth.uid() || '/test.txt'
  )
  RETURNING id INTO test_id;
  
  -- Try to update the row
  UPDATE file_metadata 
  SET original_name = 'updated.txt'
  WHERE id = test_id;
  
  -- Clean up the test data
  DELETE FROM file_metadata WHERE id = test_id;
  
  RAISE NOTICE 'UPDATE policy test: User can update their own data (ID: %)', test_id;
EXCEPTION
  WHEN OTHERS THEN
    -- Clean up in case of error
    IF test_id IS NOT NULL THEN
      DELETE FROM file_metadata WHERE id = test_id;
    END IF;
    RAISE NOTICE 'UPDATE policy test: User cannot update their own data - %', SQLERRM;
END;
$$;

-- Test policy for DELETE
-- This should succeed for the current user's data
DO $$
DECLARE
  test_id UUID;
BEGIN
  -- Insert a test row
  INSERT INTO file_metadata (
    user_id, original_name, encrypted_name, mime_type, 
    size, encrypted_size, category, hash, 
    encryption_iv, encryption_salt, storage_path
  ) VALUES (
    auth.uid(), 'test.txt', 'test.enc', 'text/plain',
    100, 150, 'photo', 'test-hash',
    'test-iv', 'test-salt', auth.uid() || '/test.txt'
  )
  RETURNING id INTO test_id;
  
  -- Try to delete the row
  DELETE FROM file_metadata WHERE id = test_id;
  
  RAISE NOTICE 'DELETE policy test: User can delete their own data (ID: %)', test_id;
EXCEPTION
  WHEN OTHERS THEN
    -- Clean up in case of error
    IF test_id IS NOT NULL THEN
      DELETE FROM file_metadata WHERE id = test_id;
    END IF;
    RAISE NOTICE 'DELETE policy test: User cannot delete their own data - %', SQLERRM;
END;
$$;