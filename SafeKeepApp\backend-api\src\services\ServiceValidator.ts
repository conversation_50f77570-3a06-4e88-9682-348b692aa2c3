import { ValidationResult, AccessResult, UserService, SERVICE_TYPES } from '../types/modular-pricing';
import { supabase } from '../utils/database';

export class ServiceValidator {
  /**
   * Validate service combination
   * Requirements: 4.1, 4.2, 4.3, 4.4
   */
  async validateServiceCombination(serviceIds: string[]): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Requirement 4.1: Ensure at least one service is selected
      if (!serviceIds || serviceIds.length === 0) {
        errors.push('At least one service must be selected');
        return { isValid: false, errors, warnings };
      }

      // Requirement 4.3: Prevent duplicate service selections
      const uniqueServiceIds = [...new Set(serviceIds)];
      if (uniqueServiceIds.length !== serviceIds.length) {
        errors.push('Duplicate services are not allowed');
      }

      // Requirement 4.2: Ensure all selected services exist in the system
      const { data: existingServices, error: servicesError } = await supabase
        .from('service_types')
        .select('id, name, is_active')
        .in('id', uniqueServiceIds);

      if (servicesError) {
        throw new Error(`Database error: ${servicesError.message}`);
      }

      // Check if all requested services exist
      const existingServiceIds = existingServices?.map(s => s.id) || [];
      const nonExistentServices = uniqueServiceIds.filter(id => !existingServiceIds.includes(id));
      
      if (nonExistentServices.length > 0) {
        errors.push(`Invalid service(s): ${nonExistentServices.join(', ')}`);
      }

      // Check if all services are active
      const inactiveServices = existingServices?.filter(s => !s.is_active).map(s => s.id) || [];
      if (inactiveServices.length > 0) {
        errors.push(`Inactive service(s): ${inactiveServices.join(', ')}`);
      }

      // Validate against known service types
      const validServiceTypes = Object.values(SERVICE_TYPES);
      const invalidServiceTypes = uniqueServiceIds.filter(id => !validServiceTypes.includes(id as any));
      if (invalidServiceTypes.length > 0) {
        errors.push(`Unknown service type(s): ${invalidServiceTypes.join(', ')}`);
      }

      // Add warnings for common combinations
      if (uniqueServiceIds.length === 1) {
        warnings.push('Consider combining services for better value');
      }

      if (uniqueServiceIds.length === 3) {
        warnings.push('Complete backup package available with potential savings');
      }

      // Requirement 4.4: Return specific error messages for each validation failure
      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      console.error('Error validating service combination:', error);
      return {
        isValid: false,
        errors: ['Service validation failed due to system error'],
        warnings: []
      };
    }
  }

  /**
   * Check if user has access to a specific service
   * Requirements: 5.1, 5.2, 5.3, 5.4
   */
  async checkServiceAccess(userId: string, serviceType: string): Promise<AccessResult> {
    try {
      // Requirement 5.1: Verify user has access to the corresponding service
      const { data: hasAccess, error: accessError } = await supabase
        .rpc('user_has_service_access', {
          user_uuid: userId,
          service_type_id: serviceType
        });

      if (accessError) {
        throw new Error(`Database error checking access: ${accessError.message}`);
      }

      if (!hasAccess) {
        // Requirement 5.3: Deny access for inactive subscriptions
        return { hasAccess: false };
      }

      // Requirement 5.2: Allow access for active subscriptions
      // Requirement 5.4: Return clear access status and expiration information
      const { data: subscriptionDetails, error: subscriptionError } = await supabase
        .rpc('get_user_subscription_details', {
          user_uuid: userId
        });

      if (subscriptionError) {
        console.warn('Could not fetch subscription details:', subscriptionError.message);
        return { hasAccess: true }; // Access confirmed, but no expiration info
      }

      if (subscriptionDetails && subscriptionDetails.length > 0) {
        const subscription = subscriptionDetails[0];
        return {
          hasAccess: true,
          expiresAt: subscription.next_billing_date ? new Date(subscription.next_billing_date) : undefined,
          planName: subscription.plan_name
        };
      }

      return { hasAccess: true };

    } catch (error) {
      console.error('Error checking service access:', error);
      return { hasAccess: false };
    }
  }

  /**
   * Get user's active services
   * Requirements: 5.1, 5.2, 5.3, 5.4
   */
  async getUserServices(userId: string): Promise<UserService[]> {
    try {
      // Use the database function to get user's services
      const { data: userServices, error: servicesError } = await supabase
        .rpc('get_user_services', {
          user_uuid: userId
        });

      if (servicesError) {
        throw new Error(`Database error fetching user services: ${servicesError.message}`);
      }

      if (!userServices || userServices.length === 0) {
        return [];
      }

      // Transform database result to match UserService interface
      return userServices.map((service: any) => ({
        serviceId: service.service_id,
        serviceName: service.service_name,
        isActive: service.is_active,
        activatedAt: new Date(service.activated_at),
        expiresAt: undefined // Will be populated from subscription details if needed
      }));

    } catch (error) {
      console.error('Error fetching user services:', error);
      return [];
    }
  }
}