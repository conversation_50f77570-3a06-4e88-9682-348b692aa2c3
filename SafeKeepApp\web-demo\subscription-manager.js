/**
 * Subscription Manager for SafeKeep Web Demo
 * Handles subscription tiers, billing, and feature access control
 */

class SubscriptionManager {
    constructor(stripeManager) {
        this.stripeManager = stripeManager;
        this.currentUser = null;
        this.currentSubscription = null;
        this.usageTracking = {
            storageUsed: 0,
            backupCount: 0,
            restoreCount: 0
        };
        
        // Feature access control
        this.featureAccess = {
            free: {
                maxStorageGB: 1,
                maxBackupsPerMonth: 5,
                maxRestoresPerMonth: 2,
                backupFrequencies: ['manual'],
                prioritySupport: false,
                advancedEncryption: false,
                multiDeviceSync: false,
                backupHistoryDays: 7
            },
            basic: {
                maxStorageGB: 10,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 20,
                backupFrequencies: ['manual', 'daily'],
                prioritySupport: false,
                advancedEncryption: true,
                multiDeviceSync: false,
                backupHistoryDays: 30
            },
            premium: {
                maxStorageGB: 100,
                maxBackupsPerMonth: -1, // unlimited
                maxRestoresPerMonth: -1, // unlimited
                backupFrequencies: ['manual', 'daily', 'weekly'],
                prioritySupport: true,
                advancedEncryption: true,
                multiDeviceSync: true,
                backupHistoryDays: 90
            }
        };
    }

    /**
     * Initialize subscription manager
     */
    async initialize(userId) {
        try {
            this.currentUser = userId;
            await this.loadUserSubscription();
            this.setupUI();
            console.log('✅ Subscription Manager initialized');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Subscription Manager:', error);
            throw error;
        }
    }

    /**
     * Load user subscription data
     */
    async loadUserSubscription() {
        try {
            // In a real app, this would fetch from your database
            // For demo, we'll use localStorage
            const savedSubscription = localStorage.getItem(`subscription_${this.currentUser}`);
            if (savedSubscription) {
                this.currentSubscription = JSON.parse(savedSubscription);
            } else {
                // Default to free tier
                this.currentSubscription = {
                    id: `sub_free_${this.currentUser}`,
                    userId: this.currentUser,
                    tierId: 'free',
                    status: 'active',
                    currentPeriodStart: Math.floor(Date.now() / 1000),
                    currentPeriodEnd: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
                    cancelAtPeriodEnd: false
                };
                this.saveSubscription();
            }
            
            // Load usage tracking
            const savedUsage = localStorage.getItem(`usage_${this.currentUser}`);
            if (savedUsage) {
                this.usageTracking = JSON.parse(savedUsage);
            }
            
        } catch (error) {
            console.error('❌ Failed to load subscription:', error);
            throw error;
        }
    }    /**

     * Save subscription to localStorage
     */
    saveSubscription() {
        localStorage.setItem(`subscription_${this.currentUser}`, JSON.stringify(this.currentSubscription));
        localStorage.setItem(`usage_${this.currentUser}`, JSON.stringify(this.usageTracking));
    }

    /**
     * Setup subscription UI
     */
    setupUI() {
        this.createSubscriptionDashboard();
        this.updateSubscriptionDisplay();
        this.setupEventListeners();
    }

    /**
     * Create subscription dashboard HTML
     */
    createSubscriptionDashboard() {
        const container = document.getElementById('subscription-container') || this.createContainer();
        
        container.innerHTML = `
            <div class="subscription-dashboard">
                <div class="subscription-header">
                    <h3>💳 Subscription Management</h3>
                    <div class="subscription-status" id="subscription-status">
                        <span class="status-badge" id="status-badge">Free</span>
                    </div>
                </div>
                
                <div class="subscription-content">
                    <!-- Current Plan -->
                    <div class="current-plan-section">
                        <h4>Current Plan</h4>
                        <div class="plan-card current" id="current-plan-card">
                            <div class="plan-header">
                                <h5 id="current-plan-name">Free Plan</h5>
                                <div class="plan-price" id="current-plan-price">$0/month</div>
                            </div>
                            <div class="plan-features" id="current-plan-features">
                                <!-- Features will be populated dynamically -->
                            </div>
                            <div class="plan-usage" id="current-plan-usage">
                                <!-- Usage will be populated dynamically -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Available Plans -->
                    <div class="available-plans-section">
                        <h4>Available Plans</h4>
                        <div class="plans-grid" id="plans-grid">
                            <!-- Plans will be populated dynamically -->
                        </div>
                    </div>
                    
                    <!-- Billing Information -->
                    <div class="billing-section" id="billing-section" style="display: none;">
                        <h4>Billing Information</h4>
                        <div class="billing-info" id="billing-info">
                            <!-- Billing info will be populated dynamically -->
                        </div>
                    </div>
                    
                    <!-- Payment Methods -->
                    <div class="payment-methods-section" id="payment-methods-section" style="display: none;">
                        <h4>Payment Methods</h4>
                        <div class="payment-methods" id="payment-methods">
                            <!-- Payment methods will be populated dynamically -->
                        </div>
                        <button class="btn" id="add-payment-method-btn">Add Payment Method</button>
                    </div>
                </div>
            </div>
            
            <!-- Payment Modal -->
            <div class="payment-modal" id="payment-modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4>Upgrade Subscription</h4>
                        <button class="close-btn" id="close-payment-modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="selected-plan" id="selected-plan-info">
                            <!-- Selected plan info -->
                        </div>
                        <div class="payment-form">
                            <div id="card-element">
                                <!-- Stripe Elements will create form elements here -->
                            </div>
                            <div id="card-errors" role="alert"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn secondary" id="cancel-payment">Cancel</button>
                        <button class="btn" id="confirm-payment">
                            <span id="payment-button-text">Complete Payment</span>
                            <span id="payment-loading" style="display: none;">Processing...</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create container if it doesn't exist
     */
    createContainer() {
        const container = document.createElement('div');
        container.id = 'subscription-container';
        container.className = 'demo-section';
        
        // Insert after backup history section or at the end
        const backupHistorySection = document.querySelector('.backup-history-dashboard');
        if (backupHistorySection) {
            backupHistorySection.parentNode.insertBefore(container, backupHistorySection.nextSibling);
        } else {
            document.querySelector('.main-content').appendChild(container);
        }
        
        return container;
    } 
   /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Close payment modal
        document.getElementById('close-payment-modal')?.addEventListener('click', () => {
            this.hidePaymentModal();
        });
        
        document.getElementById('cancel-payment')?.addEventListener('click', () => {
            this.hidePaymentModal();
        });
        
        // Confirm payment
        document.getElementById('confirm-payment')?.addEventListener('click', () => {
            this.processPayment();
        });
        
        // Add payment method
        document.getElementById('add-payment-method-btn')?.addEventListener('click', () => {
            this.showAddPaymentMethodModal();
        });
    }

    /**
     * Update subscription display
     */
    updateSubscriptionDisplay() {
        this.updateCurrentPlan();
        this.updateAvailablePlans();
        this.updateUsageDisplay();
        this.updateBillingInfo();
    }

    /**
     * Update current plan display
     */
    updateCurrentPlan() {
        const tier = this.stripeManager.getSubscriptionTier(this.currentSubscription.tierId);
        const statusBadge = document.getElementById('status-badge');
        const planName = document.getElementById('current-plan-name');
        const planPrice = document.getElementById('current-plan-price');
        const planFeatures = document.getElementById('current-plan-features');
        
        if (statusBadge) {
            statusBadge.textContent = tier.name;
            statusBadge.className = `status-badge ${this.currentSubscription.tierId}`;
        }
        
        if (planName) planName.textContent = `${tier.name} Plan`;
        if (planPrice) planPrice.textContent = tier.price === 0 ? 'Free' : `$${(tier.price / 100).toFixed(2)}/month`;
        
        if (planFeatures) {
            const features = this.featureAccess[this.currentSubscription.tierId];
            planFeatures.innerHTML = `
                <div class="feature-list">
                    <div class="feature-item">
                        <span class="feature-icon">💾</span>
                        <span>${features.maxStorageGB}GB Storage</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">🔄</span>
                        <span>${features.maxBackupsPerMonth === -1 ? 'Unlimited' : features.maxBackupsPerMonth} Backups/month</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">📅</span>
                        <span>${features.backupFrequencies.join(', ')} backup</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">${features.prioritySupport ? '🚀' : '📧'}</span>
                        <span>${features.prioritySupport ? 'Priority' : 'Standard'} Support</span>
                    </div>
                    <div class="feature-item">
                        <span class="feature-icon">${features.advancedEncryption ? '🔐' : '🔒'}</span>
                        <span>${features.advancedEncryption ? 'Advanced' : 'Standard'} Encryption</span>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Update available plans display
     */
    updateAvailablePlans() {
        const plansGrid = document.getElementById('plans-grid');
        if (!plansGrid) return;
        
        const tiers = this.stripeManager.getSubscriptionTiers();
        const currentTierId = this.currentSubscription.tierId;
        
        plansGrid.innerHTML = '';
        
        Object.values(tiers).forEach(tier => {
            if (tier.id === currentTierId) return; // Skip current plan
            
            const planCard = document.createElement('div');
            planCard.className = `plan-card ${tier.id}`;
            
            const features = this.featureAccess[tier.id];
            const isUpgrade = this.isUpgrade(tier.id);
            
            planCard.innerHTML = `
                <div class="plan-header">
                    <h5>${tier.name} Plan</h5>
                    <div class="plan-price">
                        ${tier.price === 0 ? 'Free' : `$${(tier.price / 100).toFixed(2)}/month`}
                    </div>
                </div>
                <div class="plan-features">
                    <div class="feature-list">
                        <div class="feature-item">
                            <span class="feature-icon">💾</span>
                            <span>${features.maxStorageGB}GB Storage</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🔄</span>
                            <span>${features.maxBackupsPerMonth === -1 ? 'Unlimited' : features.maxBackupsPerMonth} Backups/month</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">📅</span>
                            <span>${features.backupFrequencies.join(', ')} backup</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">${features.prioritySupport ? '🚀' : '📧'}</span>
                            <span>${features.prioritySupport ? 'Priority' : 'Standard'} Support</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">${features.advancedEncryption ? '🔐' : '🔒'}</span>
                            <span>${features.advancedEncryption ? 'Advanced' : 'Standard'} Encryption</span>
                        </div>
                    </div>
                </div>
                <div class="plan-actions">
                    <button class="btn ${isUpgrade ? 'primary' : 'secondary'}" 
                            onclick="subscriptionManager.selectPlan('${tier.id}')">
                        ${isUpgrade ? 'Upgrade' : 'Downgrade'} to ${tier.name}
                    </button>
                </div>
            `;
            
            plansGrid.appendChild(planCard);
        });
    }    /**

     * Update usage display
     */
    updateUsageDisplay() {
        const usageContainer = document.getElementById('current-plan-usage');
        if (!usageContainer) return;
        
        const features = this.featureAccess[this.currentSubscription.tierId];
        const storagePercent = (this.usageTracking.storageUsed / features.maxStorageGB) * 100;
        const backupPercent = features.maxBackupsPerMonth === -1 ? 0 : 
            (this.usageTracking.backupCount / features.maxBackupsPerMonth) * 100;
        
        usageContainer.innerHTML = `
            <div class="usage-section">
                <h5>Current Usage</h5>
                <div class="usage-item">
                    <div class="usage-label">
                        <span>Storage Used</span>
                        <span>${this.usageTracking.storageUsed.toFixed(2)}GB / ${features.maxStorageGB}GB</span>
                    </div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: ${Math.min(storagePercent, 100)}%"></div>
                    </div>
                </div>
                <div class="usage-item">
                    <div class="usage-label">
                        <span>Backups This Month</span>
                        <span>${this.usageTracking.backupCount} / ${features.maxBackupsPerMonth === -1 ? '∞' : features.maxBackupsPerMonth}</span>
                    </div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: ${Math.min(backupPercent, 100)}%"></div>
                    </div>
                </div>
                <div class="usage-item">
                    <div class="usage-label">
                        <span>Restores This Month</span>
                        <span>${this.usageTracking.restoreCount} / ${features.maxRestoresPerMonth === -1 ? '∞' : features.maxRestoresPerMonth}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update billing information
     */
    updateBillingInfo() {
        const billingSection = document.getElementById('billing-section');
        const billingInfo = document.getElementById('billing-info');
        
        if (this.currentSubscription.tierId === 'free') {
            billingSection.style.display = 'none';
            return;
        }
        
        billingSection.style.display = 'block';
        
        const nextBillingDate = new Date(this.currentSubscription.currentPeriodEnd * 1000);
        const tier = this.stripeManager.getSubscriptionTier(this.currentSubscription.tierId);
        
        billingInfo.innerHTML = `
            <div class="billing-details">
                <div class="billing-item">
                    <span class="billing-label">Current Plan:</span>
                    <span class="billing-value">${tier.name} - $${(tier.price / 100).toFixed(2)}/month</span>
                </div>
                <div class="billing-item">
                    <span class="billing-label">Next Billing Date:</span>
                    <span class="billing-value">${nextBillingDate.toLocaleDateString()}</span>
                </div>
                <div class="billing-item">
                    <span class="billing-label">Status:</span>
                    <span class="billing-value status-${this.currentSubscription.status}">${this.currentSubscription.status}</span>
                </div>
                ${this.currentSubscription.cancelAtPeriodEnd ? `
                    <div class="billing-item">
                        <span class="billing-label">Cancellation:</span>
                        <span class="billing-value">Will cancel on ${nextBillingDate.toLocaleDateString()}</span>
                    </div>
                ` : ''}
            </div>
            <div class="billing-actions">
                ${!this.currentSubscription.cancelAtPeriodEnd ? `
                    <button class="btn danger" onclick="subscriptionManager.cancelSubscription()">
                        Cancel Subscription
                    </button>
                ` : `
                    <button class="btn" onclick="subscriptionManager.reactivateSubscription()">
                        Reactivate Subscription
                    </button>
                `}
            </div>
        `;
    }

    /**
     * Check if tier change is an upgrade
     */
    isUpgrade(newTierId) {
        const tierOrder = { free: 0, basic: 1, premium: 2 };
        return tierOrder[newTierId] > tierOrder[this.currentSubscription.tierId];
    }

    /**
     * Select a plan for upgrade/downgrade
     */
    async selectPlan(tierId) {
        try {
            const tier = this.stripeManager.getSubscriptionTier(tierId);
            if (!tier) {
                throw new Error('Invalid subscription tier');
            }
            
            if (tierId === 'free') {
                // Downgrade to free
                await this.downgradeToFree();
            } else {
                // Show payment modal for paid plans
                this.showPaymentModal(tier);
            }
            
        } catch (error) {
            console.error('❌ Failed to select plan:', error);
            alert('Failed to select plan. Please try again.');
        }
    }    /**

     * Show payment modal
     */
    showPaymentModal(tier) {
        const modal = document.getElementById('payment-modal');
        const selectedPlanInfo = document.getElementById('selected-plan-info');
        const paymentButtonText = document.getElementById('payment-button-text');
        
        selectedPlanInfo.innerHTML = `
            <div class="selected-plan-details">
                <h5>${tier.name} Plan</h5>
                <div class="plan-price">$${(tier.price / 100).toFixed(2)}/month</div>
                <div class="plan-description">
                    Upgrade to ${tier.name} to unlock premium features and increased storage.
                </div>
            </div>
        `;
        
        paymentButtonText.textContent = `Complete Payment - $${(tier.price / 100).toFixed(2)}`;
        
        // Create Stripe card element
        if (!this.stripeManager.cardElement) {
            const cardElement = this.stripeManager.createCardElement();
            this.stripeManager.mountCardElement('card-element');
        }
        
        modal.style.display = 'flex';
        this.selectedTier = tier;
    }

    /**
     * Hide payment modal
     */
    hidePaymentModal() {
        const modal = document.getElementById('payment-modal');
        modal.style.display = 'none';
        this.selectedTier = null;
    }

    /**
     * Process payment
     */
    async processPayment() {
        if (!this.selectedTier) return;
        
        const submitButton = document.getElementById('confirm-payment');
        const buttonText = document.getElementById('payment-button-text');
        const loadingText = document.getElementById('payment-loading');
        
        try {
            // Show loading state
            submitButton.disabled = true;
            buttonText.style.display = 'none';
            loadingText.style.display = 'inline';
            
            // Create payment intent
            const paymentIntent = await this.stripeManager.createPaymentIntent({
                amount: this.selectedTier.price,
                currency: this.selectedTier.currency,
                description: `SafeKeep ${this.selectedTier.name} Subscription`,
                metadata: {
                    userId: this.currentUser,
                    tierId: this.selectedTier.id
                }
            });
            
            // Confirm payment with Stripe
            const result = await this.stripeManager.stripe.confirmCardPayment(
                paymentIntent.client_secret,
                {
                    payment_method: {
                        card: this.stripeManager.cardElement,
                        billing_details: {
                            email: '<EMAIL>' // In real app, use actual user email
                        }
                    }
                }
            );
            
            if (result.error) {
                throw new Error(result.error.message);
            }
            
            // Payment succeeded - update subscription
            await this.upgradeSubscription(this.selectedTier.id);
            
            this.hidePaymentModal();
            alert(`Successfully upgraded to ${this.selectedTier.name} plan!`);
            
        } catch (error) {
            console.error('❌ Payment failed:', error);
            alert(`Payment failed: ${error.message}`);
        } finally {
            // Reset button state
            submitButton.disabled = false;
            buttonText.style.display = 'inline';
            loadingText.style.display = 'none';
        }
    }

    /**
     * Upgrade subscription
     */
    async upgradeSubscription(newTierId) {
        try {
            const oldTierId = this.currentSubscription.tierId;
            
            // Update subscription
            this.currentSubscription.tierId = newTierId;
            this.currentSubscription.status = 'active';
            this.currentSubscription.cancelAtPeriodEnd = false;
            this.currentSubscription.currentPeriodStart = Math.floor(Date.now() / 1000);
            this.currentSubscription.currentPeriodEnd = Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60);
            
            this.saveSubscription();
            this.updateSubscriptionDisplay();
            
            console.log(`✅ Subscription upgraded from ${oldTierId} to ${newTierId}`);
            
            // Simulate webhook event
            await this.stripeManager.processWebhook({
                type: 'customer.subscription.updated',
                data: {
                    object: this.currentSubscription
                }
            });
            
        } catch (error) {
            console.error('❌ Failed to upgrade subscription:', error);
            throw error;
        }
    }

    /**
     * Downgrade to free
     */
    async downgradeToFree() {
        try {
            const oldTierId = this.currentSubscription.tierId;
            
            // Update subscription
            this.currentSubscription.tierId = 'free';
            this.currentSubscription.status = 'active';
            this.currentSubscription.cancelAtPeriodEnd = false;
            
            this.saveSubscription();
            this.updateSubscriptionDisplay();
            
            console.log(`✅ Subscription downgraded from ${oldTierId} to free`);
            alert('Successfully downgraded to Free plan!');
            
        } catch (error) {
            console.error('❌ Failed to downgrade subscription:', error);
            alert('Failed to downgrade subscription. Please try again.');
        }
    }

    /**
     * Cancel subscription
     */
    async cancelSubscription() {
        try {
            if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
                return;
            }
            
            this.currentSubscription.cancelAtPeriodEnd = true;
            this.saveSubscription();
            this.updateSubscriptionDisplay();
            
            console.log('✅ Subscription marked for cancellation');
            alert('Subscription will be canceled at the end of your billing period.');
            
        } catch (error) {
            console.error('❌ Failed to cancel subscription:', error);
            alert('Failed to cancel subscription. Please try again.');
        }
    }

    /**
     * Reactivate subscription
     */
    async reactivateSubscription() {
        try {
            this.currentSubscription.cancelAtPeriodEnd = false;
            this.saveSubscription();
            this.updateSubscriptionDisplay();
            
            console.log('✅ Subscription reactivated');
            alert('Subscription has been reactivated!');
            
        } catch (error) {
            console.error('❌ Failed to reactivate subscription:', error);
            alert('Failed to reactivate subscription. Please try again.');
        }
    }

    /**
     * Check feature access
     */
    hasFeatureAccess(feature) {
        const features = this.featureAccess[this.currentSubscription.tierId];
        return features[feature] || false;
    }

    /**
     * Check usage limits
     */
    checkUsageLimit(type) {
        const features = this.featureAccess[this.currentSubscription.tierId];
        
        switch (type) {
            case 'storage':
                return this.usageTracking.storageUsed < features.maxStorageGB;
            case 'backup':
                return features.maxBackupsPerMonth === -1 || 
                       this.usageTracking.backupCount < features.maxBackupsPerMonth;
            case 'restore':
                return features.maxRestoresPerMonth === -1 || 
                       this.usageTracking.restoreCount < features.maxRestoresPerMonth;
            default:
                return true;
        }
    }

    /**
     * Track usage
     */
    trackUsage(type, amount = 1) {
        switch (type) {
            case 'storage':
                this.usageTracking.storageUsed += amount;
                break;
            case 'backup':
                this.usageTracking.backupCount += amount;
                break;
            case 'restore':
                this.usageTracking.restoreCount += amount;
                break;
        }
        
        this.saveSubscription();
        this.updateUsageDisplay();
    }

    /**
     * Reset monthly usage (simulate billing cycle)
     */
    resetMonthlyUsage() {
        this.usageTracking.backupCount = 0;
        this.usageTracking.restoreCount = 0;
        this.saveSubscription();
        this.updateUsageDisplay();
        console.log('🔄 Monthly usage reset');
    }

    /**
     * Get current subscription info
     */
    getCurrentSubscription() {
        return {
            ...this.currentSubscription,
            tier: this.stripeManager.getSubscriptionTier(this.currentSubscription.tierId),
            features: this.featureAccess[this.currentSubscription.tierId],
            usage: this.usageTracking
        };
    }

    /**
     * Show add payment method modal
     */
    showAddPaymentMethodModal() {
        // This would show a modal to add a new payment method
        // For demo purposes, we'll just simulate adding one
        alert('Add Payment Method feature - This would show a form to add a new payment method');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.SubscriptionManager = SubscriptionManager;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionManager;
}