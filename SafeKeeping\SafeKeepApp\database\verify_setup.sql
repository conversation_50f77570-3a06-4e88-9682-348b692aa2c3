-- SafeKeep Database Verification Script
-- Run this after setting up the database to ensure everything is working correctly

-- 1. Verify all tables exist
SELECT 
  'Tables Check' as check_type,
  COUNT(*) as found_count,
  5 as expected_count,
  CASE WHEN COUNT(*) = 5 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status');

-- 2. Verify functions exist
SELECT 
  'Functions Check' as check_type,
  COUNT(*) as found_count,
  7 as expected_count,
  CASE WHEN COUNT(*) >= 7 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name IN (
    'update_user_storage_usage',
    'handle_new_user',
    'check_storage_quota',
    'update_backup_progress',
    'cleanup_orphaned_files',
    'get_subscription_limits',
    'refresh_storage_usage'
  );

-- 3. Verify triggers exist
SELECT 
  'Triggers Check' as check_type,
  COUNT(*) as found_count,
  3 as expected_count,
  CASE WHEN COUNT(*) >= 3 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM information_schema.triggers
WHERE trigger_schema = 'public'
  AND trigger_name IN (
    'on_auth_user_created',
    'check_quota_before_insert',
    'update_storage_on_file_change'
  );

-- 4. Verify view exists
SELECT 
  'Views Check' as check_type,
  COUNT(*) as found_count,
  1 as expected_count,
  CASE WHEN COUNT(*) = 1 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM information_schema.views
WHERE table_schema = 'public'
  AND table_name = 'user_storage_summary';

-- 5. Test subscription limits function
SELECT 
  'Function Test' as check_type,
  'get_subscription_limits' as function_name,
  CASE 
    WHEN storage_quota = 5368709120 AND max_devices = 1 THEN '✅ PASS'
    ELSE '❌ FAIL'
  END as status
FROM public.get_subscription_limits('00000000-0000-0000-0000-000000000000'::UUID);

-- 6. Verify table constraints
SELECT 
  'Constraints Check' as check_type,
  COUNT(*) as found_count,
  'Multiple' as expected_count,
  CASE WHEN COUNT(*) > 10 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM information_schema.table_constraints
WHERE table_schema = 'public'
  AND constraint_type IN ('CHECK', 'FOREIGN KEY', 'UNIQUE');

-- 7. Verify indexes exist
SELECT 
  'Indexes Check' as check_type,
  COUNT(*) as found_count,
  'Multiple' as expected_count,
  CASE WHEN COUNT(*) > 15 THEN '✅ PASS' ELSE '❌ FAIL' END as status
FROM pg_indexes
WHERE schemaname = 'public'
  AND tablename IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status');

-- Summary Report
SELECT 
  '=== SETUP VERIFICATION SUMMARY ===' as summary,
  '' as details;

-- Detailed table information
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_schema = 'public'
  AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage')
ORDER BY table_name, ordinal_position;