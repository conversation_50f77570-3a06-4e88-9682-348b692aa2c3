-- Function to find duplicate files based on hash comparison
-- This function returns files with the same hash for a specific user
CREATE OR REPLACE FUNCTION find_duplicate_files(user_id_param UUID)
RETURNS TABLE (
  hash TEXT,
  files JSONB
) AS $$
BEGIN
  RETURN QUERY
  WITH duplicate_hashes AS (
    SELECT 
      hash,
      COUNT(*) as file_count
    FROM 
      file_metadata
    WHERE 
      user_id = user_id_param
    GROUP BY 
      hash
    HAVING 
      COUNT(*) > 1
  )
  SELECT 
    dh.hash,
    jsonb_agg(
      jsonb_build_object(
        'id', fm.id,
        'original_name', fm.original_name,
        'size', fm.size,
        'category', fm.category,
        'uploaded_at', fm.uploaded_at,
        'storage_path', fm.storage_path
      )
    ) as files
  FROM 
    duplicate_hashes dh
  JOIN 
    file_metadata fm ON dh.hash = fm.hash
  WHERE 
    fm.user_id = user_id_param
  GROUP BY 
    dh.hash;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION find_duplicate_files(UUID) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION find_duplicate_files(UUID) IS 'Finds files with duplicate hashes for a specific user';