{"name": "safekeep-test-server", "version": "1.0.0", "description": "Test server for SafeKeep Stripe integration", "main": "test-server.js", "scripts": {"start": "node test-server.js", "dev": "nodemon test-server.js", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@supabase/supabase-js": "^2.52.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "stripe": "^14.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["safekeep", "stripe", "payments", "test"], "author": "SafeKeep Team", "license": "MIT"}