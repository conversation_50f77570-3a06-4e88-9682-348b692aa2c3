<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Demo</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #F8F9FA;
            color: #333333;
            line-height: 1.6;
        }
        
        .phone-container {
            max-width: 375px;
            margin: 20px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            min-height: 700px;
            height: auto;
        }
        
        .screen {
            min-height: 100%;
            display: flex;
            flex-direction: column;
            padding: 20px;
            padding-bottom: 20px;
        }
        
        .hidden {
            display: none;
        }
        
        .title {
            font-size: 32px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .subtitle {
            font-size: 18px;
            color: #666666;
            margin-bottom: 24px;
            text-align: center;
        }
        
        .description {
            font-size: 16px;
            color: #333333;
            text-align: center;
            line-height: 1.5;
            margin-bottom: 32px;
        }
        
        .features {
            margin-bottom: 40px;
        }
        
        .feature {
            font-size: 16px;
            color: #333333;
            margin-bottom: 12px;
            padding: 12px 16px;
            background: rgba(74, 144, 226, 0.05);
            border-radius: 12px;
            border-left: 4px solid #4A90E2;
            display: flex;
            align-items: center;
        }

        .feature-icon {
            font-size: 20px;
            margin-right: 12px;
            color: #4A90E2;
        }
        
        .button {
            background-color: #4A90E2;
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin-top: auto;
        }
        
        .button:hover {
            background-color: #357ABD;
        }
        
        .tab-bar {
            display: flex;
            background: white;
            border-top: 1px solid #E1E5E9;
            margin: 0 -20px -20px -20px;
        }
        
        .tab {
            flex: 1;
            padding: 8px 4px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            color: #666666;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            min-width: 0;
            overflow: hidden;
        }

        .tab.active {
            color: #4A90E2;
            font-weight: bold;
        }

        .tab-icon {
            font-size: 14px;
            flex-shrink: 0;
        }

        .tab span:last-child {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #E0E0E0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: #4A90E2;
            border-radius: 4px;
            transition: width 0.5s ease-in-out;
        }

        .backup-tab {
            flex: 1;
            padding: 12px 16px;
            background: none;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .backup-tab.active {
            background: #4A90E2;
            color: white;
        }

        .backup-tab:hover:not(.active) {
            background: rgba(74, 144, 226, 0.1);
        }
        
        .card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: 1px solid rgba(74, 144, 226, 0.1);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 16px;
        }
        
        .status-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 6px;
            background-color: #50E3C2;
            margin-right: 8px;
        }
        
        .stats-container {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .stat-card {
            flex: 1;
            background: white;
            padding: 24px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: 1px solid rgba(74, 144, 226, 0.1);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #4A90E2;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 16px;
            color: #666666;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #E1E5E9;
            border-radius: 4px;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #4A90E2;
            border-radius: 4px;
            width: 75%;
        }
        
        .setting-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #E1E5E9;
        }
        
        .setting-row:last-child {
            border-bottom: none;
        }
        
        .switch {
            width: 50px;
            height: 30px;
            background-color: #4A90E2;
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: 0.3s;
        }

        .switch.off {
            background-color: #E1E5E9;
        }

        .switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background: white;
            top: 2px;
            right: 2px;
            transition: 0.3s;
        }

        .switch.off::after {
            right: 22px;
        }



        .top-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #E1E5E9;
            margin: -20px -20px 20px -20px;
        }

        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-button {
            background: none;
            border: 1px solid #E1E5E9;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            color: #4A90E2;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            border-radius: 6px;
            z-index: 1;
            right: 0;
        }

        .dropdown-content div {
            color: #333;
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #E1E5E9;
        }

        .dropdown-content div:last-child {
            border-bottom: none;
        }

        .dropdown-content div:hover {
            background-color: #F8F9FA;
        }

        .dropdown.show .dropdown-content {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Authentication Screen -->
    <div class="phone-container">
        <div id="auth" class="screen">
            <div style="text-align: center; padding: 40px 20px 20px;">
                <div style="font-size: 64px; color: #4A90E2; margin-bottom: 16px;">🛡️</div>
                <h1 style="color: #333; margin: 0; font-size: 28px;">SafeKeep</h1>
                <p style="color: #666; margin: 8px 0 0; font-size: 16px;">Your memories, safely encrypted</p>
            </div>

            <div class="card" style="margin: 20px;">
                <h2 style="color: #333; margin-bottom: 8px; text-align: center;">Welcome Back</h2>
                <p style="color: #666; text-align: center; margin-bottom: 24px;">
                    Sign in to access your secure backups
                </p>

                <div style="margin-bottom: 16px;">
                    <input type="email" placeholder="Email Address" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px;" id="email-input" value="<EMAIL>">
                </div>

                <div style="margin-bottom: 24px;">
                    <input type="password" placeholder="Password" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px;" id="password-input" value="••••••••">
                </div>

                <button class="button" style="width: 100%; margin-bottom: 12px;" onclick="handleSignIn()">
                    🔐 Sign In
                </button>

                <button style="background: none; border: none; color: #4A90E2; cursor: pointer; width: 100%; padding: 8px;" onclick="showForgotPassword()">
                    Forgot Password?
                </button>

                <div style="text-align: center; margin: 20px 0; color: #666;">or</div>

                <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer;" onclick="toggleToSignUp()">
                    New to SafeKeep? Create Account
                </button>
            </div>

            <div class="card" style="margin: 20px; background: rgba(76, 175, 80, 0.1);">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <span style="color: #4CAF50; margin-right: 8px;">🛡️</span>
                    <span style="font-weight: 600; color: #333;">Your Data is Secure</span>
                </div>
                <div style="font-size: 14px; color: #666; line-height: 1.4;">
                    • All data encrypted with AES-256 before upload<br>
                    • Only you have the decryption keys<br>
                    • We cannot access your personal information<br>
                    • Your privacy is our top priority
                </div>
            </div>
        </div>
    </div>

    <!-- Sign Up Screen -->
    <div class="phone-container hidden">
        <div id="signup" class="screen">
            <div style="text-align: center; padding: 40px 20px 20px;">
                <div style="font-size: 64px; color: #4A90E2; margin-bottom: 16px;">🛡️</div>
                <h1 style="color: #333; margin: 0; font-size: 28px;">Join SafeKeep</h1>
                <p style="color: #666; margin: 8px 0 0; font-size: 16px;">Create your secure account</p>
            </div>

            <div class="card" style="margin: 20px;">
                <h2 style="color: #333; margin-bottom: 8px; text-align: center;">Create Account</h2>
                <p style="color: #666; text-align: center; margin-bottom: 24px;">
                    Start protecting your precious data today
                </p>

                <div style="margin-bottom: 16px;">
                    <input type="text" placeholder="Your Name" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px;" id="name-input" value="John Doe">
                </div>

                <div style="margin-bottom: 16px;">
                    <input type="email" placeholder="Email Address" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px;" id="signup-email-input" value="<EMAIL>">
                </div>

                <div style="margin-bottom: 16px;">
                    <input type="password" placeholder="Password" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px;" id="signup-password-input" value="••••••••">
                </div>

                <div style="margin-bottom: 24px;">
                    <input type="password" placeholder="Confirm Password" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px;" id="confirm-password-input" value="••••••••">
                </div>

                <button class="button" style="width: 100%; margin-bottom: 12px;" onclick="handleSignUp()">
                    🎉 Create Account
                </button>

                <div style="text-align: center; margin: 20px 0; color: #666;">or</div>

                <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer;" onclick="toggleToSignIn()">
                    Already have an account? Sign In
                </button>
            </div>

            <div class="card" style="margin: 20px; background: rgba(76, 175, 80, 0.1);">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                    <span style="color: #4CAF50; margin-right: 8px;">🛡️</span>
                    <span style="font-weight: 600; color: #333;">Military-Grade Security</span>
                </div>
                <div style="color: #666; font-size: 14px; line-height: 1.4;">
                    • End-to-end AES-256 encryption<br>
                    • Zero-knowledge architecture<br>
                    • Your data, your keys<br>
                    • GDPR & CCPA compliant
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Screen -->
    <div class="phone-container hidden">
        <div id="subscription" class="screen">
            <div style="text-align: center; padding: 40px 20px 20px;">
                <div style="font-size: 64px; color: #4A90E2; margin-bottom: 16px;">💳</div>
                <h1 style="color: #333; margin: 0; font-size: 28px;">Choose Your Plan</h1>
                <p style="color: #666; margin: 8px 0 0; font-size: 16px;">Secure your precious data</p>
            </div>

            <!-- Individual Services -->
            <div style="margin: 20px 20px 10px;">
                <h3 style="color: #333; margin: 0 0 16px; font-size: 18px;">Individual Services</h3>
            </div>

            <div class="card" style="margin: 10px 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="color: #333; margin: 0;">Contacts Only</h4>
                    <span style="font-size: 18px; font-weight: bold; color: #28a745;">$0.99<span style="font-size: 12px;">/mo</span></span>
                </div>
                <p style="color: #666; margin: 0 0 12px; font-size: 14px;">Secure backup for your contacts</p>
                <button class="button" style="width: 100%; font-size: 14px; padding: 8px;" onclick="selectPlan('contacts_only', 99)">
                    Select Plan
                </button>
            </div>

            <div class="card" style="margin: 10px 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="color: #333; margin: 0;">Messages Only</h4>
                    <span style="font-size: 18px; font-weight: bold; color: #17a2b8;">$1.99<span style="font-size: 12px;">/mo</span></span>
                </div>
                <p style="color: #666; margin: 0 0 12px; font-size: 14px;">Secure backup for your messages</p>
                <button class="button" style="width: 100%; font-size: 14px; padding: 8px;" onclick="selectPlan('messages_only', 199)">
                    Select Plan
                </button>
            </div>

            <div class="card" style="margin: 10px 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="color: #333; margin: 0;">Photos Only</h4>
                    <span style="font-size: 18px; font-weight: bold; color: #fd7e14;">$4.99<span style="font-size: 12px;">/mo</span></span>
                </div>
                <p style="color: #666; margin: 0 0 12px; font-size: 14px;">Secure backup for your photos</p>
                <button class="button" style="width: 100%; font-size: 14px; padding: 8px;" onclick="selectPlan('photos_only', 499)">
                    Select Plan
                </button>
            </div>

            <!-- Combination Services -->
            <div style="margin: 30px 20px 10px;">
                <h3 style="color: #333; margin: 0 0 16px; font-size: 18px;">Combination Services</h3>
            </div>

            <div class="card" style="margin: 10px 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="color: #333; margin: 0;">Contacts + Messages</h4>
                    <span style="font-size: 18px; font-weight: bold; color: #6f42c1;">$2.49<span style="font-size: 12px;">/mo</span></span>
                </div>
                <p style="color: #666; margin: 0 0 12px; font-size: 14px;">Backup contacts and messages together</p>
                <button class="button" style="width: 100%; font-size: 14px; padding: 8px;" onclick="selectPlan('contacts_messages', 249)">
                    Select Plan
                </button>
            </div>

            <div class="card" style="margin: 10px 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="color: #333; margin: 0;">Contacts + Photos</h4>
                    <span style="font-size: 18px; font-weight: bold; color: #e83e8c;">$5.49<span style="font-size: 12px;">/mo</span></span>
                </div>
                <p style="color: #666; margin: 0 0 12px; font-size: 14px;">Backup contacts and photos together</p>
                <button class="button" style="width: 100%; font-size: 14px; padding: 8px;" onclick="selectPlan('contacts_photos', 549)">
                    Select Plan
                </button>
            </div>

            <div class="card" style="margin: 10px 20px; border: 2px solid #20c997; background: rgba(32, 201, 151, 0.05);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="color: #333; margin: 0;">Messages + Photos</h4>
                    <div style="text-align: right;">
                        <span style="background: #4CAF50; color: white; padding: 2px 6px; border-radius: 8px; font-size: 10px; font-weight: bold; display: block; margin-bottom: 4px;">POPULAR</span>
                        <span style="font-size: 18px; font-weight: bold; color: #20c997;">$6.49<span style="font-size: 12px;">/mo</span></span>
                    </div>
                </div>
                <p style="color: #666; margin: 0 0 12px; font-size: 14px;">Backup messages and photos together</p>
                <button class="button" style="width: 100%; font-size: 14px; padding: 8px;" onclick="selectPlan('messages_photos', 649)">
                    Select Plan
                </button>
            </div>

            <div class="card" style="margin: 10px 20px; border: 2px solid #007bff; background: rgba(0, 123, 255, 0.05);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                    <h4 style="color: #333; margin: 0;">Complete Backup</h4>
                    <span style="font-size: 18px; font-weight: bold; color: #007bff;">$6.99<span style="font-size: 12px;">/mo</span></span>
                </div>
                <p style="color: #666; margin: 0 0 12px; font-size: 14px;">Full backup solution for all your data</p>
                <div style="margin-bottom: 12px;">
                    <div style="display: flex; align-items: center; margin-bottom: 4px;">
                        <span style="color: #4CAF50; margin-right: 8px; font-size: 12px;">✓</span>
                        <span style="color: #333; font-size: 12px;">Contacts + Messages + Photos</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 4px;">
                        <span style="color: #4CAF50; margin-right: 8px; font-size: 12px;">✓</span>
                        <span style="color: #333; font-size: 12px;">100GB Storage</span>
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 4px;">
                        <span style="color: #4CAF50; margin-right: 8px; font-size: 12px;">✓</span>
                        <span style="color: #333; font-size: 12px;">Priority Support</span>
                    </div>
                </div>
                <button class="button" style="width: 100%; font-size: 14px; padding: 8px;" onclick="selectPlan('complete_backup', 699)">
                    Select Plan
                </button>
            </div>

            <!-- Payment Form (Initially Hidden) -->
            <div id="payment-form-container" style="display: none; margin: 20px;">
                <div class="card">
                    <h3 style="color: #333; margin-bottom: 16px; text-align: center;">Payment Information</h3>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">Email Address</label>
                        <input type="email" id="payment-email" placeholder="<EMAIL>" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px;">
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">Card Details</label>
                        <div style="background: #f8f9fa; padding: 8px 12px; border-radius: 6px; margin-bottom: 8px; font-size: 12px; color: #666;">
                            💡 <strong>Test Card:</strong> **************** | <strong>Expiry:</strong> 12/25 | <strong>CVC:</strong> 123
                        </div>
                        <div id="card-element" style="padding: 12px; border: 1px solid #ddd; border-radius: 6px; background: white;">
                            <!-- Stripe Elements will create form elements here -->
                        </div>
                        <div id="card-errors" style="color: #f44336; margin-top: 8px; font-size: 14px;"></div>
                    </div>

                    <button id="submit-payment" class="button" style="width: 100%; margin-bottom: 12px;" onclick="handleStripePayment()">
                        <span id="payment-button-text">Complete Payment</span>
                        <span id="payment-loading" style="display: none;">Processing...</span>
                    </button>

                    <button style="background: none; border: 1px solid #ccc; color: #666; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer;" onclick="hidePaymentForm()">
                        Cancel
                    </button>
                </div>
            </div>

            <div style="text-align: center; margin: 20px;">
                <button style="background: none; border: none; color: #4A90E2; cursor: pointer; text-decoration: underline;" onclick="handleFreeTrial()">
                    Try Free Version First
                </button>
            </div>

            <div class="card" style="margin: 20px; background: rgba(255, 152, 0, 0.1);">
                <div style="text-align: center; margin-bottom: 12px;">
                    <span style="font-weight: 600; color: #333;">🧪 Test Payment Instructions</span>
                </div>
                <div style="color: #666; font-size: 14px; text-align: center; line-height: 1.6;">
                    <strong>💰 New Modular Pricing:</strong><br>
                    Choose individual services or combinations<br>
                    Prices range from $0.99 to $6.99/month<br><br>

                    <strong>✅ Successful Payment:</strong><br>
                    Card: <code style="background: white; padding: 2px 4px; border-radius: 3px;">****************</code><br>
                    Expiry: <code style="background: white; padding: 2px 4px; border-radius: 3px;">12/25</code> (MM/YY format)<br>
                    CVC: <code style="background: white; padding: 2px 4px; border-radius: 3px;">123</code><br><br>

                    <strong>❌ Declined Payment:</strong><br>
                    Card: <code style="background: white; padding: 2px 4px; border-radius: 3px;">****************</code><br>
                    Same expiry and CVC as above
                </div>
            </div>
        </div>
    </div>

    <!-- Welcome Screen -->
    <div class="phone-container hidden">
        <div id="onboarding" class="screen hidden">
            <div class="top-tabs">
                <button class="tab active" onclick="showOnboarding()">
                    <span class="tab-icon">🏠</span>
                    <span>Home</span>
                </button>
                <button class="tab" onclick="showPermissions()">
                    <span class="tab-icon">🔐</span>
                    <span>Setup</span>
                </button>
                <button class="tab" onclick="showDashboard()">
                    <span class="tab-icon">📊</span>
                    <span>Dashboard</span>
                </button>
                <button class="tab" onclick="showBackup()">
                    <span class="tab-icon">💾</span>
                    <span>Backup</span>
                </button>
                <button class="tab" onclick="showRecovery()">
                    <span class="tab-icon">🔄</span>
                    <span>Recovery</span>
                </button>
                <button class="tab" onclick="showSettings()">
                    <span class="tab-icon">⚙️</span>
                    <span>Settings</span>
                </button>
            </div>

            <h1 class="title" style="text-align: center; margin-bottom: 16px;">SafeKeep</h1>
            <p class="subtitle" style="text-align: center; margin-bottom: 24px;">Your personal data backup solution</p>

            <div class="card">
                <p style="color: #333333; text-align: center; line-height: 1.5; margin-bottom: 24px;">
                    Automatically backup your photos, contacts, and text messages with
                    military-grade encryption. Simple, secure, and reliable.
                </p>

                <div class="features">
                    <div class="feature">
                        <span class="feature-icon">📷</span>
                        <span>Automatic photo backup</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">👥</span>
                        <span>Automatic contact synchronization</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">💬</span>
                        <span>Automatic text message backup</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">🔐</span>
                        <span>End-to-end encryption</span>
                    </div>
                    <div class="feature">
                        <span class="feature-icon">☁️</span>
                        <span>Secure cloud storage</span>
                    </div>
                </div>

                <button class="button" style="margin-top: 24px; width: 100%;" onclick="showPermissions()">
                    Get Started
                </button>
            </div>
        </div>

        <!-- Permission Setup Screen -->
        <div id="permissions" class="screen hidden">
            <div class="top-tabs">
                <button class="tab" onclick="showOnboarding()">
                    <span class="tab-icon">🏠</span>
                    <span>Home</span>
                </button>
                <button class="tab active" onclick="showPermissions()">
                    <span class="tab-icon">🔐</span>
                    <span>Setup</span>
                </button>
                <button class="tab" onclick="showDashboard()">
                    <span class="tab-icon">📊</span>
                    <span>Dashboard</span>
                </button>
                <button class="tab" onclick="showBackup()">
                    <span class="tab-icon">💾</span>
                    <span>Backup</span>
                </button>
                <button class="tab" onclick="showSettings()">
                    <span class="tab-icon">⚙️</span>
                    <span>Settings</span>
                </button>
            </div>

            <h1 class="title" style="text-align: left; margin-bottom: 24px;">Setup Permissions</h1>

            <div class="card">
                <div style="text-align: center; margin-bottom: 24px;">
                    <span class="feature-icon" style="font-size: 48px;">🛡️</span>
                    <h2 style="color: #4A90E2; margin: 16px 0 8px 0;">Let's Keep Your Data Safe</h2>
                </div>
                <p style="color: #666666; text-align: center; margin-bottom: 24px;">
                    SafeKeep needs a few permissions to backup your important data.
                    Don't worry - we'll explain exactly what each one does and why it helps you!
                </p>

                <div style="background: rgba(74, 144, 226, 0.1); padding: 16px; border-radius: 8px; margin-bottom: 24px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-weight: bold;">Setup Progress</span>
                        <span style="color: #4A90E2; font-weight: bold;">67%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 67%;"></div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="color: #333; margin-bottom: 16px;">Permissions Needed</h3>

                <div class="setting-row">
                    <div style="display: flex; align-items: center;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">📷</span>
                        <div>
                            <div style="font-weight: 600;">Photo Access</div>
                            <div style="font-size: 14px; color: #666666;">Backup your precious family photos safely</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="background: #50E3C2; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">✓ Allowed</span>
                    </div>
                </div>

                <div class="setting-row" id="contacts-row">
                    <div style="display: flex; align-items: center;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">👥</span>
                        <div>
                            <div style="font-weight: 600;">Contact Access</div>
                            <div style="font-size: 14px; color: #666666;">Save important phone numbers and contacts</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span id="contacts-status" style="background: #F5A623; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">? Not Asked</span>
                        <button id="contacts-allow" style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 4px 12px; border-radius: 4px; cursor: pointer;" onclick="grantContactsPermission()">Allow</button>
                    </div>
                </div>

                <div class="setting-row" id="messages-row">
                    <div style="display: flex; align-items: center;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">💬</span>
                        <div>
                            <div style="font-weight: 600;">Message Access</div>
                            <div style="font-size: 14px; color: #666666;">Backup important text conversations</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span id="messages-status" style="background: #F5A623; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">Optional</span>
                        <button id="messages-allow" style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 4px 12px; border-radius: 4px; cursor: pointer;" onclick="grantMessagesPermission()">Allow</button>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3 style="color: #333; margin-bottom: 8px; text-align: center;">Ready to Get Started?</h3>
                <p style="color: #666666; text-align: center; margin-bottom: 24px;">
                    You can grant all permissions at once, or set them up individually above.
                </p>

                <button id="grant-all-btn" class="button" style="margin-bottom: 12px; width: 100%;" onclick="grantAllPermissions()">
                    <span class="feature-icon" style="margin-right: 8px;">🛡️</span>
                    Grant All Permissions
                </button>

                <button id="continue-btn" style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer;" onclick="continueToApp()">
                    Continue to App
                </button>
            </div>
        </div>
        
        <!-- Dashboard Screen -->
        <div id="dashboard" class="screen hidden">
            <div class="top-tabs">
                <button class="tab" onclick="showOnboarding()">
                    <span class="tab-icon">🏠</span>
                    <span>Home</span>
                </button>
                <button class="tab" onclick="showPermissions()">
                    <span class="tab-icon">🔐</span>
                    <span>Setup</span>
                </button>
                <button class="tab active" onclick="showDashboard()">
                    <span class="tab-icon">📊</span>
                    <span>Dashboard</span>
                </button>
                <button class="tab" onclick="showBackup()">
                    <span class="tab-icon">💾</span>
                    <span>Backup</span>
                </button>
                <button class="tab" onclick="showSettings()">
                    <span class="tab-icon">⚙️</span>
                    <span>Settings</span>
                </button>
            </div>

            <h1 class="title" style="text-align: left; margin-bottom: 24px;">Dashboard</h1>
            
            <div class="card">
                <div class="card-title">
                    <span class="feature-icon">✅</span>
                    Backup Status
                </div>
                <div class="status-row">
                    <div class="status-indicator"></div>
                    <span>All backed up</span>
                </div>
                <div style="font-size: 14px; color: #666666; margin-top: 8px;">
                    <span class="feature-icon" style="font-size: 14px;">🕐</span>
                    Last backup: Today at 2:30 PM
                </div>
            </div>

            <div class="stats-container">
                <div class="stat-card">
                    <div class="feature-icon" style="font-size: 32px; margin-bottom: 8px;">📷</div>
                    <div class="stat-number">247</div>
                    <div class="stat-label">Photos</div>
                </div>
                <div class="stat-card">
                    <div class="feature-icon" style="font-size: 32px; margin-bottom: 8px;">👥</div>
                    <div class="stat-number">89</div>
                    <div class="stat-label">Contacts</div>
                </div>
            </div>

            <div class="stats-container">
                <div class="stat-card">
                    <div class="feature-icon" style="font-size: 32px; margin-bottom: 8px;">💬</div>
                    <div class="stat-number">156</div>
                    <div class="stat-label">Text Messages</div>
                </div>
                <div class="stat-card">
                    <div class="feature-icon" style="font-size: 32px; margin-bottom: 8px;">💾</div>
                    <div class="stat-number">3.2 GB</div>
                    <div class="stat-label">Total Backed Up</div>
                </div>
            </div>

            <div class="card">
                <div class="card-title">
                    <span class="feature-icon">☁️</span>
                    Cloud Storage
                </div>
                <div style="text-align: center; margin-bottom: 16px;">
                    <div style="font-size: 18px; color: #333; margin-bottom: 8px;">
                        1.2 GB of 5.0 GB used
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 24%;"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 14px; color: #666;">
                        <span>24% used</span>
                        <span>3.8 GB remaining</span>
                    </div>
                </div>

                <div style="background: rgba(74, 144, 226, 0.1); padding: 12px; border-radius: 8px; margin-bottom: 16px;">
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                        <span style="color: #4A90E2;">🔐</span>
                        <span style="font-weight: 600; color: #333;">AES-256 Encryption + Supabase</span>
                    </div>
                    <div style="font-size: 14px; color: #666;">
                        All data encrypted client-side before upload to Supabase. PostgreSQL metadata storage with real-time capabilities.
                    </div>
                </div>

                <div style="display: flex; gap: 8px;">
                    <div style="flex: 1; text-align: center; padding: 8px; background: #f8f9fa; border-radius: 6px;">
                        <div style="font-size: 14px; color: #666;">Photos</div>
                        <div style="font-weight: bold; color: #4A90E2;">892 MB</div>
                    </div>
                    <div style="flex: 1; text-align: center; padding: 8px; background: #f8f9fa; border-radius: 6px;">
                        <div style="font-size: 14px; color: #666;">Contacts</div>
                        <div style="font-weight: bold; color: #4A90E2;">2.1 MB</div>
                    </div>
                    <div style="flex: 1; text-align: center; padding: 8px; background: #f8f9fa; border-radius: 6px;">
                        <div style="font-size: 14px; color: #666;">Messages</div>
                        <div style="font-weight: bold; color: #4A90E2;">315 MB</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Backup Screen -->
        <div id="backup" class="screen hidden">
            <div class="top-tabs">
                <button class="tab" onclick="showOnboarding()">
                    <span class="tab-icon">🏠</span>
                    <span>Home</span>
                </button>
                <button class="tab" onclick="showPermissions()">
                    <span class="tab-icon">🔐</span>
                    <span>Setup</span>
                </button>
                <button class="tab" onclick="showDashboard()">
                    <span class="tab-icon">📊</span>
                    <span>Dashboard</span>
                </button>
                <button class="tab active" onclick="showBackup()">
                    <span class="tab-icon">💾</span>
                    <span>Backup</span>
                </button>
                <button class="tab" onclick="showSettings()">
                    <span class="tab-icon">⚙️</span>
                    <span>Settings</span>
                </button>
            </div>

            <h1 class="title" style="text-align: left; margin-bottom: 16px;">Backup Center</h1>

            <!-- Backup Type Tabs -->
            <div style="display: flex; background: #f8f9fa; border-radius: 8px; padding: 4px; margin-bottom: 24px;">
                <button id="photos-tab" class="backup-tab active" onclick="showPhotoBackup()">
                    <span class="tab-icon">📷</span>
                    <span>Photos</span>
                </button>
                <button id="contacts-tab" class="backup-tab" onclick="showContactBackup()">
                    <span class="tab-icon">👥</span>
                    <span>Contacts</span>
                </button>
                <button id="messages-tab" class="backup-tab" onclick="showMessageBackup()">
                    <span class="tab-icon">💬</span>
                    <span>Messages</span>
                </button>
            </div>

            <!-- Photo Backup Content -->
            <div id="photo-backup-content">
                <!-- Photo Library Status -->
                <div class="card">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 32px; margin-right: 12px;">📷</span>
                        <h2 style="color: #333; margin: 0;">Photo Library</h2>
                    </div>

                    <div id="photo-scan-status">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span style="font-weight: 600;">Photos Found:</span>
                            <span style="background: #4A90E2; color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px;">1,247</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span style="font-weight: 600;">Total Size:</span>
                            <span style="color: #666;">2.3 GB</span>
                        </div>
                        <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 8px 16px; border-radius: 6px; cursor: pointer;" onclick="rescanPhotos()">
                            🔄 Rescan Photos
                        </button>
                    </div>
                </div>

                <!-- Backup Progress -->
                <div id="backup-progress-card" class="card" style="display: none;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">☁️</span>
                        <h3 style="color: #333; margin: 0;">Uploading Photos...</h3>
                    </div>

                    <div id="current-photo" style="color: #666; margin-bottom: 12px; font-size: 14px;">
                        Processing: IMG_2024_001.jpg
                    </div>

                    <div class="progress-bar" style="margin-bottom: 12px;">
                        <div id="backup-progress-fill" class="progress-fill" style="width: 0%;"></div>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span id="progress-photos" style="color: #666; font-size: 14px;">0 of 1,247 photos</span>
                        <span id="progress-percent" style="color: #666; font-size: 14px;">0%</span>
                    </div>

                    <div id="progress-size" style="color: #666; font-size: 14px; margin-bottom: 16px;">
                        0 MB of 2.3 GB
                    </div>

                    <div style="display: flex; justify-content: center;">
                        <button id="pause-resume-btn" style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 8px 16px; border-radius: 6px; cursor: pointer;" onclick="toggleBackupPause()">
                            ⏸️ Pause
                        </button>
                    </div>
                </div>

                <!-- Backup Results -->
                <div id="backup-results-card" class="card" style="display: none;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 32px; margin-right: 12px;">✅</span>
                        <h3 style="color: #333; margin: 0;">Backup Complete!</h3>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #50E3C2; margin-right: 8px;">✓</span>
                                <span>Photos Backed Up</span>
                            </div>
                            <span style="font-weight: bold;">1,189 photos</span>
                        </div>
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #F5A623; margin-right: 8px;">⚠️</span>
                                <span>Duplicates Skipped</span>
                            </div>
                            <span style="font-weight: bold;">58 duplicates</span>
                        </div>
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #4A90E2; margin-right: 8px;">⏱️</span>
                                <span>Time Taken</span>
                            </div>
                            <span style="font-weight: bold;">3m 42s</span>
                        </div>
                    </div>
                </div>

                <!-- Action Card -->
                <div class="card">
                    <h3 style="color: #333; margin-bottom: 8px; text-align: center;">Ready to Backup Your Photos?</h3>
                    <p style="color: #666666; text-align: center; margin-bottom: 24px;">
                        Your photos will be safely encrypted and stored in the cloud.
                        Duplicates are automatically detected and skipped.
                    </p>

                    <button id="start-backup-btn" class="button" style="width: 100%; margin-bottom: 12px;" onclick="startPhotoBackup()">
                        <span class="feature-icon" style="margin-right: 8px;">☁️</span>
                        Start Photo Backup
                    </button>

                    <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer;" onclick="restorePhotos()">
                        <span class="feature-icon" style="margin-right: 8px;">📥</span>
                        Restore from Backup
                    </button>
                </div>
            </div>

            <!-- Contact Backup Content -->
            <div id="contacts-backup-content" style="display: none;">
                <!-- Contact Library Status -->
                <div class="card">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 32px; margin-right: 12px;">📞</span>
                        <h2 style="color: #333; margin: 0;">Contact Library</h2>
                    </div>

                    <div id="contact-scan-status">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span style="font-weight: 600;">Contacts Found:</span>
                            <span style="background: #4A90E2; color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px;">156</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span style="font-weight: 600;">With Phone Numbers:</span>
                            <span style="color: #666;">142</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span style="font-weight: 600;">With Email Addresses:</span>
                            <span style="color: #666;">89</span>
                        </div>
                        <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 8px 16px; border-radius: 6px; cursor: pointer;" onclick="rescanContacts()">
                            🔄 Rescan Contacts
                        </button>
                    </div>
                </div>

                <!-- Contact Backup Progress -->
                <div id="contact-progress-card" class="card" style="display: none;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">☁️</span>
                        <h3 style="color: #333; margin: 0;">🔐 Encrypting & Uploading Contacts...</h3>
                    </div>

                    <div id="current-contact" style="color: #666; margin-bottom: 12px; font-size: 14px;">
                        Processing: John Smith
                    </div>

                    <div class="progress-bar" style="margin-bottom: 12px;">
                        <div id="contact-progress-fill" class="progress-fill" style="width: 0%;"></div>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span id="contact-progress-count" style="color: #666; font-size: 14px;">0 of 156 contacts</span>
                        <span id="contact-progress-percent" style="color: #666; font-size: 14px;">0%</span>
                    </div>
                </div>

                <!-- Contact Backup Results -->
                <div id="contact-results-card" class="card" style="display: none;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 32px; margin-right: 12px;">✅</span>
                        <h3 style="color: #333; margin: 0;">Contact Backup Complete!</h3>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #50E3C2; margin-right: 8px;">✓</span>
                                <span>Contacts Backed Up</span>
                            </div>
                            <span style="font-weight: bold;">142 contacts</span>
                        </div>
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #F5A623; margin-right: 8px;">⚠️</span>
                                <span>Duplicates Skipped</span>
                            </div>
                            <span style="font-weight: bold;">14 duplicates</span>
                        </div>
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #4A90E2; margin-right: 8px;">⏱️</span>
                                <span>Time Taken</span>
                            </div>
                            <span style="font-weight: bold;">12 seconds</span>
                        </div>
                    </div>
                </div>

                <!-- Backup History -->
                <div class="card">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">📊</span>
                        <h3 style="color: #333; margin: 0;">Backup History</h3>
                    </div>

                    <div style="display: flex; justify-content: space-around; margin-bottom: 16px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4A90E2;">3</div>
                            <div style="font-size: 14px; color: #666;">Total Backups</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4A90E2;">142</div>
                            <div style="font-size: 14px; color: #666;">Contacts Backed Up</div>
                        </div>
                    </div>

                    <div style="text-align: center; color: #666; font-size: 14px;">
                        Last backup: Today
                    </div>
                </div>

                <!-- Action Card -->
                <div class="card">
                    <h3 style="color: #333; margin-bottom: 8px; text-align: center;">Ready to Backup Your Contacts?</h3>
                    <p style="color: #666666; text-align: center; margin-bottom: 24px;">
                        Your contacts will be safely encrypted and stored in the cloud.
                        You can restore them on any device anytime.
                    </p>

                    <button id="start-contact-backup-btn" class="button" style="width: 100%; margin-bottom: 12px;" onclick="startContactBackup()">
                        <span class="feature-icon" style="margin-right: 8px;">☁️</span>
                        Start Contact Backup
                    </button>

                    <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer;" onclick="restoreContacts()">
                        <span class="feature-icon" style="margin-right: 8px;">📥</span>
                        Restore from Backup
                    </button>
                </div>
            </div>

            <!-- Message Backup Content -->
            <div id="messages-backup-content" style="display: none;">
                <!-- Message Library Status -->
                <div class="card">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 32px; margin-right: 12px;">💬</span>
                        <h2 style="color: #333; margin: 0;">Message Library</h2>
                    </div>

                    <div style="background: rgba(255, 152, 0, 0.1); padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                        <div style="display: flex; align-items: center;">
                            <span style="color: #FF9800; margin-right: 8px;">ℹ️</span>
                            <span style="font-size: 12px; color: #666;">iOS Demo Mode: Showing simulated message data</span>
                        </div>
                    </div>

                    <div id="message-scan-status">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span style="font-weight: 600;">Messages Found:</span>
                            <span style="background: #4A90E2; color: white; padding: 4px 12px; border-radius: 16px; font-size: 14px;">2,341</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                            <span style="font-weight: 600;">Conversations:</span>
                            <span style="color: #666;">47</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <span style="font-weight: 600;">Most Recent:</span>
                            <span style="color: #666;">Today</span>
                        </div>
                        <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 8px 16px; border-radius: 6px; cursor: pointer;" onclick="rescanMessages()">
                            🔄 Rescan Messages
                        </button>
                    </div>
                </div>

                <!-- Message Backup Progress -->
                <div id="message-progress-card" class="card" style="display: none;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">☁️</span>
                        <h3 style="color: #333; margin: 0;">🔐 Encrypting & Uploading Messages...</h3>
                    </div>

                    <div id="current-conversation" style="color: #666; margin-bottom: 12px; font-size: 14px;">
                        Processing: Mom (23 messages)
                    </div>

                    <div class="progress-bar" style="margin-bottom: 12px;">
                        <div id="message-progress-fill" class="progress-fill" style="width: 0%;"></div>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span id="message-progress-count" style="color: #666; font-size: 14px;">0 of 2,341 messages</span>
                        <span id="message-progress-percent" style="color: #666; font-size: 14px;">0%</span>
                    </div>
                </div>

                <!-- Message Backup Results -->
                <div id="message-results-card" class="card" style="display: none;">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 32px; margin-right: 12px;">✅</span>
                        <h3 style="color: #333; margin: 0;">Message Backup Complete!</h3>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #50E3C2; margin-right: 8px;">✓</span>
                                <span>Messages Backed Up</span>
                            </div>
                            <span style="font-weight: bold;">2,341 messages</span>
                        </div>
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #4A90E2; margin-right: 8px;">💬</span>
                                <span>Conversations Preserved</span>
                            </div>
                            <span style="font-weight: bold;">47 threads</span>
                        </div>
                        <div class="setting-row" style="border: none; padding: 8px 0;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #4A90E2; margin-right: 8px;">⏱️</span>
                                <span>Time Taken</span>
                            </div>
                            <span style="font-weight: bold;">18 seconds</span>
                        </div>
                    </div>
                </div>

                <!-- Backup History -->
                <div class="card">
                    <div style="display: flex; align-items: center; margin-bottom: 16px;">
                        <span class="feature-icon" style="font-size: 24px; margin-right: 12px;">📊</span>
                        <h3 style="color: #333; margin: 0;">Backup History</h3>
                    </div>

                    <div style="display: flex; justify-content: space-around; margin-bottom: 16px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4A90E2;">2</div>
                            <div style="font-size: 14px; color: #666;">Total Backups</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4A90E2;">2,341</div>
                            <div style="font-size: 14px; color: #666;">Messages Backed Up</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #4A90E2;">47</div>
                            <div style="font-size: 14px; color: #666;">Conversations</div>
                        </div>
                    </div>

                    <div style="text-align: center; color: #666; font-size: 14px;">
                        Last backup: Today
                    </div>
                </div>

                <!-- Action Card -->
                <div class="card">
                    <h3 style="color: #333; margin-bottom: 8px; text-align: center;">Ready to Backup Your Messages?</h3>
                    <p style="color: #666666; text-align: center; margin-bottom: 24px;">
                        Your text messages and conversations will be safely encrypted and stored in the cloud.
                        You can restore them on any device anytime.
                    </p>

                    <button id="start-message-backup-btn" class="button" style="width: 100%; margin-bottom: 12px;" onclick="startMessageBackup()">
                        <span class="feature-icon" style="margin-right: 8px;">☁️</span>
                        Start Message Backup
                    </button>

                    <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer;" onclick="restoreMessages()">
                        <span class="feature-icon" style="margin-right: 8px;">📥</span>
                        Restore from Backup
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Recovery Screen -->
        <div id="recovery" class="screen hidden">
            <div class="top-tabs">
                <button class="tab" onclick="showOnboarding()">
                    <span class="tab-icon">🏠</span>
                    <span>Home</span>
                </button>
                <button class="tab" onclick="showPermissions()">
                    <span class="tab-icon">🔐</span>
                    <span>Setup</span>
                </button>
                <button class="tab" onclick="showDashboard()">
                    <span class="tab-icon">📊</span>
                    <span>Dashboard</span>
                </button>
                <button class="tab" onclick="showBackup()">
                    <span class="tab-icon">💾</span>
                    <span>Backup</span>
                </button>
                <button class="tab active" onclick="showRecovery()">
                    <span class="tab-icon">🔄</span>
                    <span>Recovery</span>
                </button>
                <button class="tab" onclick="showSettings()">
                    <span class="tab-icon">⚙️</span>
                    <span>Settings</span>
                </button>
            </div>

            <h1 class="title" style="text-align: left; margin-bottom: 24px;">Data Recovery</h1>

            <!-- Backup Summary -->
            <div class="card">
                <div style="display: flex; align-items: center; margin-bottom: 16px;">
                    <span class="feature-icon" style="font-size: 32px; margin-right: 12px;">🔄</span>
                    <h2 style="color: #333; margin: 0;">Your Backup Data</h2>
                </div>

                <p style="color: #666; margin-bottom: 20px;">
                    We found your backup data! You can restore all your important information to this device.
                </p>

                <div style="display: flex; justify-content: space-around; margin-bottom: 20px; background: #f8f9fa; padding: 16px; border-radius: 8px;">
                    <div style="text-align: center;">
                        <div style="font-size: 20px; font-weight: bold; color: #4A90E2;">1,566</div>
                        <div style="font-size: 12px; color: #666;">Total Items</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 20px; font-weight: bold; color: #4A90E2;">2.8 GB</div>
                        <div style="font-size: 12px; color: #666;">Data Size</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 20px; font-weight: bold; color: #4A90E2;">2 days ago</div>
                        <div style="font-size: 12px; color: #666;">Last Backup</div>
                    </div>
                </div>
            </div>

            <!-- Available Data Categories -->
            <div class="card">
                <h3 style="color: #333; margin-bottom: 16px;">Available Data</h3>

                <div style="margin-bottom: 12px; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <span style="color: #4CAF50; margin-right: 8px; font-size: 20px;">📸</span>
                            <div>
                                <div style="font-weight: 600;">Photos</div>
                                <div style="font-size: 12px; color: #666;">Last updated: Today</div>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: #4CAF50; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-bottom: 4px;">1,247</div>
                            <div style="font-size: 12px; color: #666;">2.1 GB</div>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 12px; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <span style="color: #2196F3; margin-right: 8px; font-size: 20px;">📱</span>
                            <div>
                                <div style="font-weight: 600;">Contacts</div>
                                <div style="font-size: 12px; color: #666;">Last updated: Yesterday</div>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: #2196F3; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-bottom: 4px;">156</div>
                            <div style="font-size: 12px; color: #666;">2 MB</div>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 12px; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <span style="color: #FF9800; margin-right: 8px; font-size: 20px;">💬</span>
                            <div>
                                <div style="font-weight: 600;">Messages</div>
                                <div style="font-size: 12px; color: #666;">Last updated: 2 days ago</div>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: #FF9800; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-bottom: 4px;">2,341</div>
                            <div style="font-size: 12px; color: #666;">15 MB</div>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 16px; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center;">
                            <span style="color: #9C27B0; margin-right: 8px; font-size: 20px;">📄</span>
                            <div>
                                <div style="font-weight: 600;">Documents</div>
                                <div style="font-size: 12px; color: #666;">Last updated: 1 week ago</div>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="background: #9C27B0; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-bottom: 4px;">23</div>
                            <div style="font-size: 12px; color: #666;">45 MB</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card">
                <h3 style="color: #333; margin-bottom: 8px; text-align: center;">Ready to Restore Your Data?</h3>
                <p style="color: #666; text-align: center; margin-bottom: 24px;">
                    Choose how you want to restore your backed up information.
                </p>

                <button class="button" style="width: 100%; margin-bottom: 12px;" onclick="startDataRestore()">
                    <span class="feature-icon" style="margin-right: 8px;">🔄</span>
                    Restore All Data
                </button>

                <button style="background: none; border: 1px solid #4A90E2; color: #4A90E2; padding: 12px 24px; border-radius: 8px; width: 100%; cursor: pointer; margin-bottom: 16px;" onclick="selectiveRestore()">
                    <span class="feature-icon" style="margin-right: 8px;">✅</span>
                    Choose What to Restore
                </button>

                <div style="display: flex; align-items: flex-start; background: rgba(74, 144, 226, 0.1); padding: 12px; border-radius: 8px; border-left: 3px solid #4A90E2;">
                    <span style="color: #4A90E2; margin-right: 8px;">ℹ️</span>
                    <div style="font-size: 12px; color: #666; line-height: 1.4;">
                        Your data is encrypted and will be decrypted securely on this device.
                        The restore process may take several minutes.
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Screen -->
        <div id="settings" class="screen hidden">
            <div class="top-tabs">
                <button class="tab" onclick="showOnboarding()">
                    <span class="tab-icon">🏠</span>
                    <span>Home</span>
                </button>
                <button class="tab" onclick="showDashboard()">
                    <span class="tab-icon">📊</span>
                    <span>Dashboard</span>
                </button>
                <button class="tab" onclick="showBackup()">
                    <span class="tab-icon">💾</span>
                    <span>Backup</span>
                </button>
                <button class="tab active" onclick="showSettings()">
                    <span class="tab-icon">⚙️</span>
                    <span>Settings</span>
                </button>
            </div>

            <h1 class="title" style="text-align: left; margin-bottom: 24px;">Settings</h1>
            
            <div class="card">
                <div class="card-title">Backup Settings</div>
                
                <div class="setting-row">
                    <div>
                        <div style="font-weight: 600;">Auto Backup</div>
                        <div style="font-size: 14px; color: #666666;">Automatically backup new photos and contacts</div>
                    </div>
                    <div class="switch" id="autoBackupSwitch" onclick="toggleSwitch('autoBackupSwitch')"></div>
                </div>

                <div class="setting-row">
                    <div>
                        <div style="font-weight: 600;">WiFi Only</div>
                        <div style="font-size: 14px; color: #666666;">Only backup when connected to WiFi</div>
                    </div>
                    <div class="switch" id="wifiOnlySwitch" onclick="toggleSwitch('wifiOnlySwitch')"></div>
                </div>
                
                <div class="setting-row">
                    <div>
                        <div style="font-weight: 600;">Backup Frequency</div>
                        <div style="font-size: 14px; color: #666666;">How often to run automatic backups</div>
                    </div>
                    <div class="dropdown">
                        <button class="dropdown-button" onclick="toggleDropdown()">
                            <span id="frequencyDisplay">Daily</span>
                            <span>▼</span>
                        </button>
                        <div class="dropdown-content" id="frequencyDropdown">
                            <div onclick="selectFrequency('Daily')">Daily</div>
                            <div onclick="selectFrequency('Weekly')">Weekly</div>
                            <div onclick="selectFrequency('Monthly')">Monthly</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-title">💳 Subscription</div>

                <div class="setting-row">
                    <div>
                        <div style="font-weight: 600;">Current Plan</div>
                        <div style="font-size: 14px; color: #666666;" id="current-plan-display">Complete Backup - $6.99/month</div>
                    </div>
                    <div style="color: #4CAF50; font-weight: 600;">Active</div>
                </div>

                <div class="setting-row">
                    <div>
                        <div style="font-weight: 600;">Manage Subscription</div>
                        <div style="font-size: 14px; color: #666666;">View plans, upgrade, or manage billing</div>
                    </div>
                    <button style="background: #4A90E2; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;" onclick="showSubscriptionFromSettings()">
                        Manage
                    </button>
                </div>

                <div class="setting-row">
                    <div>
                        <div style="font-weight: 600;">Storage Used</div>
                        <div style="font-size: 14px; color: #666666;">12.5 GB of 50 GB used</div>
                    </div>
                    <div style="color: #4A90E2; font-weight: 600;">25%</div>
                </div>
            </div>

            <div class="card">
                <div class="card-title">Notifications</div>

                <div class="setting-row">
                    <div>
                        <div style="font-weight: 600;">Push Notifications</div>
                        <div style="font-size: 14px; color: #666666;">Get notified about backup status and completion</div>
                    </div>
                    <div class="switch" id="notificationSwitch" onclick="toggleSwitch('notificationSwitch')"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Authentication functions
        function handleSignIn() {
            const email = document.getElementById('email-input').value;
            const password = document.getElementById('password-input').value;

            if (!email || !password) {
                alert('Please enter both email and password.');
                return;
            }

            // Simulate authentication
            setTimeout(() => {
                alert('🎉 Welcome Back!\n\nYou have been signed in successfully.\nYour encrypted data is ready to sync!');
                showOnboarding();
            }, 1000);
        }

        function showForgotPassword() {
            const email = document.getElementById('email-input').value;
            if (!email) {
                alert('📧 Password Reset\n\nPlease enter your email address first, then click "Forgot Password" again.');
                return;
            }

            alert('📧 Reset Email Sent\n\nWe have sent a password reset link to ' + email + '.\n\nPlease check your inbox and follow the instructions.');
        }

        function toggleToSignUp() {
            hideAllContainers();
            document.querySelector('#signup').parentElement.classList.remove('hidden');
        }

        function toggleToSignIn() {
            hideAllContainers();
            document.querySelector('#auth').parentElement.classList.remove('hidden');
        }

        function handleSignUp() {
            const name = document.getElementById('name-input').value;
            const email = document.getElementById('signup-email-input').value;

            // Simulate account creation
            setTimeout(() => {
                alert('🎉 Account Created!\n\nWelcome to SafeKeep, ' + name + '!\n\nYour account has been created with military-grade encryption.\n\nNow choose your subscription plan to start backing up your data!');

                // Show subscription screen
                hideAllContainers();
                document.querySelector('#subscription').parentElement.classList.remove('hidden');
            }, 1000);
        }

        // Initialize Stripe
        const stripe = Stripe('pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP');
        let cardElement = null;
        let selectedPlan = { id: 'complete_backup', amount: 699, name: 'Complete Backup' };

        function selectPlan(planId, amount) {
            const planNames = {
                'contacts_only': 'Contacts Only',
                'messages_only': 'Messages Only',
                'photos_only': 'Photos Only',
                'contacts_messages': 'Contacts + Messages',
                'contacts_photos': 'Contacts + Photos',
                'messages_photos': 'Messages + Photos',
                'complete_backup': 'Complete Backup'
            };
            
            selectedPlan = { 
                id: planId, 
                amount: amount, 
                name: planNames[planId] || planId 
            };
            
            showPaymentForm();
        }

        function showPaymentForm() {
            document.getElementById('payment-form-container').style.display = 'block';

            // Update payment button text with selected plan
            const paymentButtonText = document.getElementById('payment-button-text');
            if (paymentButtonText) {
                paymentButtonText.textContent = `Complete Payment - $${(selectedPlan.amount / 100).toFixed(2)}`;
            }

            // Initialize Stripe Elements if not already done
            if (!cardElement) {
                const elements = stripe.elements();
                cardElement = elements.create('card', {
                    style: {
                        base: {
                            fontSize: '16px',
                            color: '#424770',
                            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                            '::placeholder': {
                                color: '#aab7c4',
                            },
                        },
                        invalid: {
                            color: '#9e2146',
                        },
                    },
                    hidePostalCode: true,
                });
                cardElement.mount('#card-element');

                // Handle real-time validation errors from the card Element
                cardElement.on('change', ({error}) => {
                    const displayError = document.getElementById('card-errors');
                    if (error) {
                        displayError.textContent = error.message;
                    } else {
                        displayError.textContent = '';
                    }
                });
            }

            // Pre-fill email from signup
            const signupEmail = document.getElementById('signup-email-input')?.value || '<EMAIL>';
            document.getElementById('payment-email').value = signupEmail;
        }

        function hidePaymentForm() {
            document.getElementById('payment-form-container').style.display = 'none';
        }

        async function handleStripePayment() {
            const submitButton = document.getElementById('submit-payment');
            const buttonText = document.getElementById('payment-button-text');
            const loading = document.getElementById('payment-loading');
            const email = document.getElementById('payment-email').value;

            if (!email) {
                alert('Please enter your email address.');
                return;
            }

            // Show loading state
            submitButton.disabled = true;
            buttonText.style.display = 'none';
            loading.style.display = 'inline';

            try {
                // Step 1: Create payment intent on backend
                console.log('🔗 Creating payment intent...');

                const response = await fetch('http://localhost:3000/api/create-payment-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: selectedPlan.amount,
                        currency: 'usd',
                        description: `SafeKeep ${selectedPlan.name} Subscription`
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to create payment intent');
                }

                const { client_secret } = await response.json();

                if (!client_secret) {
                    throw new Error('No client secret received');
                }

                console.log('✅ Payment intent created');

                // Step 2: Confirm payment with Stripe
                console.log('💳 Confirming payment...');

                const {error, paymentIntent} = await stripe.confirmCardPayment(client_secret, {
                    payment_method: {
                        card: cardElement,
                        billing_details: {
                            email: email,
                        },
                    }
                });

                if (error) {
                    // Payment failed
                    console.error('❌ Payment failed:', error);
                    alert('❌ Payment Failed\n\n' + error.message + '\n\nPlease try again with a different card or check your card details.');
                } else {
                    // Payment succeeded
                    console.log('🎉 Payment successful:', paymentIntent);

                    // Show detailed payment information
                    const paymentDetails = `🎉 Payment Successful!

Welcome to SafeKeep ${selectedPlan.name}!

📋 Payment Details:
• Payment ID: ${paymentIntent.id}
• Amount: $${(paymentIntent.amount / 100).toFixed(2)}
• Plan: ${selectedPlan.name}
• Status: ${paymentIntent.status}
• Created: ${new Date(paymentIntent.created * 1000).toLocaleString()}

🔍 Find this payment in your Stripe Dashboard:
1. Go to: https://dashboard.stripe.com
2. Make sure you're in "Test mode" (top-left toggle)
3. Navigate to: Payments → All payments
4. Look for Payment ID: ${paymentIntent.id}

Your subscription is now active!`;

                    alert(paymentDetails);

                    // Update current plan display in settings
                    updateCurrentPlanDisplay();

                    // Hide payment form and continue to app
                    hidePaymentForm();
                    setTimeout(() => {
                        showOnboarding();
                    }, 1000);
                }

            } catch (error) {
                console.error('💥 Unexpected error:', error);
                alert('💥 Connection Error\n\nCould not connect to payment server.\n\nPlease make sure:\n1. The test server is running (node test-server.js)\n2. Server is on http://localhost:3000\n\nError: ' + error.message);
            } finally {
                // Reset button state
                submitButton.disabled = false;
                buttonText.style.display = 'inline';
                loading.style.display = 'none';
            }
        }

        function updateCurrentPlanDisplay() {
            const currentPlanDisplay = document.getElementById('current-plan-display');
            if (currentPlanDisplay && selectedPlan) {
                currentPlanDisplay.textContent = `${selectedPlan.name} - $${(selectedPlan.amount / 100).toFixed(2)}/month`;
            }
        }

        function handleFreeTrial() {
            alert('🆓 Free Trial\n\nYou can explore SafeKeep with limited features:\n\n• Basic photo backup\n• Contact backup\n• 1GB storage\n\nYou can upgrade anytime from Settings.\n\nContinuing to app...');

            setTimeout(() => {
                showOnboarding();
            }, 1000);
        }

        function showSubscriptionFromSettings() {
            hideAllContainers();
            document.querySelector('#subscription').parentElement.classList.remove('hidden');
        }

        function showAuth() {
            hideAllScreens();
            hideAllContainers();
            document.getElementById('auth').parentElement.classList.remove('hidden');
            document.getElementById('auth').classList.remove('hidden');
        }

        function showOnboarding() {
            console.log('Showing welcome');
            hideAllScreens();
            hideAllContainers();
            document.getElementById('onboarding').parentElement.classList.remove('hidden');
            document.getElementById('onboarding').classList.remove('hidden');
            updateTabStates('welcome');
        }

        function showPermissions() {
            console.log('Showing permissions');
            hideAllScreens();
            hideAllContainers();
            document.getElementById('permissions').parentElement.classList.remove('hidden');
            document.getElementById('permissions').classList.remove('hidden');
            updateTabStates('permissions');
        }

        // Permission state tracking
        let permissionState = {
            photos: true,     // Already granted in demo
            contacts: false,
            messages: false
        };

        function grantContactsPermission() {
            // Simulate permission request dialog
            if (confirm('SafeKeep needs access to your contacts to back them up safely.\n\nThis helps you:\n• Never lose important phone numbers\n• Restore contacts if you get a new phone\n• Keep family and friends\' numbers safe\n\nAllow SafeKeep to access your contacts?')) {
                permissionState.contacts = true;
                updateContactsUI();
                updateProgress();
                showPermissionGrantedMessage('Contacts');
            }
        }

        function grantMessagesPermission() {
            // Simulate permission request dialog
            if (confirm('SafeKeep can backup your text messages to save important conversations.\n\nThis helps you:\n• Save important family conversations\n• Backup messages from loved ones\n• Never lose precious text memories\n\nAllow SafeKeep to access your messages?')) {
                permissionState.messages = true;
                updateMessagesUI();
                updateProgress();
                showPermissionGrantedMessage('Messages');
            }
        }

        function grantAllPermissions() {
            // Simulate granting all permissions at once
            if (confirm('SafeKeep will request access to:\n\n📷 Photos - Already granted\n👥 Contacts - To backup phone numbers\n💬 Messages - To backup conversations\n\nThis keeps all your important data safe!\n\nGrant all permissions?')) {
                permissionState.contacts = true;
                permissionState.messages = true;
                updateContactsUI();
                updateMessagesUI();
                updateProgress();

                setTimeout(() => {
                    alert('🎉 Perfect! All permissions granted!\n\nSafeKeep can now backup all your important data. You\'re all set to start using the app!');
                    setTimeout(() => {
                        showDashboard();
                    }, 1000);
                }, 500);
            }
        }

        function updateContactsUI() {
            const statusElement = document.getElementById('contacts-status');
            const buttonElement = document.getElementById('contacts-allow');

            if (permissionState.contacts) {
                statusElement.style.background = '#50E3C2';
                statusElement.textContent = '✓ Allowed';
                buttonElement.style.display = 'none';
            }
        }

        function updateMessagesUI() {
            const statusElement = document.getElementById('messages-status');
            const buttonElement = document.getElementById('messages-allow');

            if (permissionState.messages) {
                statusElement.style.background = '#50E3C2';
                statusElement.textContent = '✓ Allowed';
                buttonElement.style.display = 'none';
            }
        }

        function updateProgress() {
            const totalPermissions = 3;
            const grantedPermissions = Object.values(permissionState).filter(granted => granted).length;
            const progressPercent = Math.round((grantedPermissions / totalPermissions) * 100);

            // Update progress bar
            const progressFill = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-bar').parentElement.querySelector('span:last-child');

            if (progressFill) {
                progressFill.style.width = progressPercent + '%';
            }
            if (progressText) {
                progressText.textContent = progressPercent + '%';
            }

            // Update continue button state
            const continueBtn = document.getElementById('continue-btn');
            if (permissionState.photos) { // At least photos permission is required
                continueBtn.style.opacity = '1';
                continueBtn.style.cursor = 'pointer';
            }
        }

        function showPermissionGrantedMessage(permissionType) {
            const messages = {
                'Contacts': '✅ Great! Your contacts will now be safely backed up.',
                'Messages': '✅ Perfect! Your important conversations will be saved.'
            };

            setTimeout(() => {
                alert(messages[permissionType]);
            }, 300);
        }

        function continueToApp() {
            if (!permissionState.photos) {
                alert('⚠️ Photo access is required to use SafeKeep.\n\nPlease grant photo permission to continue.');
                return;
            }

            const grantedCount = Object.values(permissionState).filter(granted => granted).length;
            const totalCount = 3;

            if (grantedCount === totalCount) {
                alert('🎉 Excellent! All permissions are set up.\n\nSafeKeep is ready to keep your data safe!');
            } else {
                alert(`✅ You're ready to go!\n\nSafeKeep has ${grantedCount} of ${totalCount} permissions. You can always add more permissions later in Settings.`);
            }

            setTimeout(() => {
                showDashboard();
            }, 1000);
        }

        // Photo Backup functionality
        let backupState = {
            isRunning: false,
            isPaused: false,
            currentPhoto: 0,
            totalPhotos: 1247,
            currentPhotoName: '',
            bytesUploaded: 0,
            totalBytes: 2.3 * 1024 * 1024 * 1024 // 2.3 GB in bytes
        };

        function showPhotoBackup() {
            updateBackupTabs('photos');
            document.getElementById('photo-backup-content').style.display = 'block';
            document.getElementById('contacts-backup-content').style.display = 'none';
            document.getElementById('messages-backup-content').style.display = 'none';
        }

        function showContactBackup() {
            updateBackupTabs('contacts');
            document.getElementById('photo-backup-content').style.display = 'none';
            document.getElementById('contacts-backup-content').style.display = 'block';
            document.getElementById('messages-backup-content').style.display = 'none';
        }

        function showMessageBackup() {
            updateBackupTabs('messages');
            document.getElementById('photo-backup-content').style.display = 'none';
            document.getElementById('contacts-backup-content').style.display = 'none';
            document.getElementById('messages-backup-content').style.display = 'block';
        }

        function updateBackupTabs(activeTab) {
            document.getElementById('photos-tab').classList.remove('active');
            document.getElementById('contacts-tab').classList.remove('active');
            document.getElementById('messages-tab').classList.remove('active');
            document.getElementById(activeTab + '-tab').classList.add('active');
        }

        function rescanPhotos() {
            alert('🔍 Scanning photo library...\n\nFound 1,247 photos (2.3 GB)\n\nYour photo library is ready for backup!');
        }

        function restorePhotos() {
            if (confirm('📸 Restore Photos?\n\nThis will download your latest photo backup from Supabase Cloud Storage.\n\nNote: Photos will be restored and available for review before saving to your camera roll.')) {
                setTimeout(() => {
                    alert('✅ Restore Complete!\n\nSuccessfully restored 1,247 photos from your backup.\n\n• All photos decrypted securely on your device\n• Original quality and metadata preserved\n• Photos are now available for review\n• You can save them to your camera roll\n• Your memories are safely restored!');
                }, 1500);
            }
        }

        function startPhotoBackup() {
            if (confirm('🚀 Start Photo Backup?\n\nThis will backup 1,247 photos to Supabase Cloud Storage.\n\n• AES-256 encryption applied to each photo\n• Duplicates automatically detected and skipped\n• Secure authentication with your account\n• PostgreSQL metadata storage\n• You can pause anytime\n\nStart encrypted backup now?')) {
                backupState.isRunning = true;
                backupState.isPaused = false;
                backupState.currentPhoto = 0;

                // Hide action card, show progress card
                document.getElementById('start-backup-btn').parentElement.style.display = 'none';
                document.getElementById('backup-progress-card').style.display = 'block';
                document.getElementById('backup-results-card').style.display = 'none';

                // Update progress header to show encryption
                document.querySelector('#backup-progress-card h3').innerHTML = '🔐 Encrypting & Uploading Photos...';

                simulatePhotoBackup();
            }
        }

        function simulatePhotoBackup() {
            if (!backupState.isRunning || backupState.isPaused) return;

            const photoNames = [
                'IMG_2024_001.jpg', 'Family_vacation.jpg', 'Birthday_party.jpg',
                'Sunset_beach.jpg', 'Christmas_2023.jpg', 'Garden_flowers.jpg',
                'Kids_playing.jpg', 'Wedding_ceremony.jpg', 'Mountain_hike.jpg'
            ];

            backupState.currentPhoto++;
            backupState.currentPhotoName = photoNames[Math.floor(Math.random() * photoNames.length)];
            backupState.bytesUploaded = (backupState.currentPhoto / backupState.totalPhotos) * backupState.totalBytes;

            const percentage = Math.round((backupState.currentPhoto / backupState.totalPhotos) * 100);

            // Update UI
            document.getElementById('current-photo').textContent = `Processing: ${backupState.currentPhotoName}`;
            document.getElementById('backup-progress-fill').style.width = percentage + '%';
            document.getElementById('progress-photos').textContent = `${backupState.currentPhoto} of ${backupState.totalPhotos} photos`;
            document.getElementById('progress-percent').textContent = percentage + '%';
            document.getElementById('progress-size').textContent = `${(backupState.bytesUploaded / (1024 * 1024 * 1024)).toFixed(1)} GB of 2.3 GB`;

            if (backupState.currentPhoto >= backupState.totalPhotos) {
                // Backup complete
                backupState.isRunning = false;
                showBackupComplete();
            } else {
                // Continue backup
                setTimeout(simulatePhotoBackup, 50 + Math.random() * 100);
            }
        }

        function showBackupComplete() {
            document.getElementById('backup-progress-card').style.display = 'none';
            document.getElementById('backup-results-card').style.display = 'block';

            setTimeout(() => {
                alert('🎉 Backup Complete!\n\nSuccessfully backed up 1,189 photos in 3m 42s!\n\n• 58 duplicates were skipped\n• All photos encrypted with AES-256\n• Securely stored in Supabase Cloud Storage\n• Metadata stored in PostgreSQL database\n• Only you have the decryption keys\n• Your memories are protected forever!');
            }, 500);
        }

        function toggleBackupPause() {
            if (backupState.isPaused) {
                backupState.isPaused = false;
                document.getElementById('pause-resume-btn').innerHTML = '⏸️ Pause';
                simulatePhotoBackup(); // Resume
            } else {
                backupState.isPaused = true;
                document.getElementById('pause-resume-btn').innerHTML = '▶️ Resume';
            }
        }

        // Contact Backup functionality
        let contactBackupState = {
            isRunning: false,
            currentContact: 0,
            totalContacts: 156,
            currentContactName: ''
        };

        function rescanContacts() {
            alert('🔍 Scanning contact library...\n\nFound 156 contacts:\n• 142 with phone numbers\n• 89 with email addresses\n\nYour contact library is ready for backup!');
        }

        function startContactBackup() {
            if (confirm('🚀 Start Contact Backup?\n\nThis will backup 156 contacts to Supabase Cloud Storage.\n\n• AES-256 encryption applied to all contact data\n• Duplicates automatically detected and skipped\n• All phone numbers and emails included\n• You can restore on any device\n\nStart encrypted backup now?')) {
                contactBackupState.isRunning = true;
                contactBackupState.currentContact = 0;

                // Hide backup button, show progress card
                document.getElementById('start-contact-backup-btn').style.display = 'none';
                document.getElementById('contact-progress-card').style.display = 'block';
                document.getElementById('contact-results-card').style.display = 'none';

                simulateContactBackup();
            }
        }

        function simulateContactBackup() {
            if (!contactBackupState.isRunning) return;

            const contactNames = [
                'John Smith', 'Mary Johnson', 'David Wilson', 'Sarah Brown',
                'Michael Davis', 'Lisa Miller', 'Robert Garcia', 'Jennifer Martinez',
                'William Rodriguez', 'Elizabeth Lopez', 'James Gonzalez', 'Maria Hernandez'
            ];

            contactBackupState.currentContact++;
            contactBackupState.currentContactName = contactNames[Math.floor(Math.random() * contactNames.length)];

            const percentage = Math.round((contactBackupState.currentContact / contactBackupState.totalContacts) * 100);

            // Update UI
            document.getElementById('current-contact').textContent = `Processing: ${contactBackupState.currentContactName}`;
            document.getElementById('contact-progress-fill').style.width = percentage + '%';
            document.getElementById('contact-progress-count').textContent = `${contactBackupState.currentContact} of ${contactBackupState.totalContacts} contacts`;
            document.getElementById('contact-progress-percent').textContent = percentage + '%';

            if (contactBackupState.currentContact >= contactBackupState.totalContacts) {
                // Backup complete
                contactBackupState.isRunning = false;
                showContactBackupComplete();
            } else {
                // Continue backup (faster than photos since contacts are smaller)
                setTimeout(simulateContactBackup, 30 + Math.random() * 50);
            }
        }

        function showContactBackupComplete() {
            document.getElementById('contact-progress-card').style.display = 'none';
            document.getElementById('contact-results-card').style.display = 'block';

            // Show the backup button again after completion
            document.getElementById('start-contact-backup-btn').style.display = 'block';

            setTimeout(() => {
                alert('🎉 Contact Backup Complete!\n\nSuccessfully backed up 142 contacts in 12 seconds!\n\n• 14 duplicates were skipped\n• All contacts encrypted with AES-256\n• Securely stored in Supabase Cloud Storage\n• You can restore them on any device!');
            }, 500);
        }

        function restoreContacts() {
            if (confirm('📞 Restore Contacts?\n\nThis will download your latest contact backup from Supabase Cloud Storage.\n\nNote: This will show you the contacts for review before adding them to your phone.')) {
                setTimeout(() => {
                    alert('✅ Restore Complete!\n\nSuccessfully restored 142 contacts from your backup.\n\n• All contact information decrypted\n• Phone numbers and emails included\n• You can now review and import them\n• Your contacts are safe and secure!');
                }, 1000);
            }
        }

        // Data Recovery functionality
        function startDataRestore() {
            if (confirm('🔄 Restore All Data?\n\nThis will restore all your backed up data:\n\n• 1,247 photos\n• 156 contacts\n• 2,341 messages\n• 23 documents\n\nTotal: 1,566 items (2.8 GB)\n\nThis may take several minutes depending on your internet connection.')) {
                setTimeout(() => {
                    alert('🎉 Data Restore Complete!\n\nSuccessfully restored all your data:\n\n✅ 1,247 photos restored to camera roll\n✅ 156 contacts merged with existing contacts\n✅ 2,341 messages restored to app\n✅ 23 documents saved to files\n\n🔐 All data was decrypted securely on your device\n📱 Your memories are now safely restored!');
                }, 3000);
            }
        }

        function selectiveRestore() {
            alert('📋 Selective Restore\n\nThis feature would show you:\n\n• Checkboxes for each data category\n• Download options (Wi-Fi only vs cellular)\n• Merge strategies for contacts\n• Preview options before restore\n\nFor this demo, use "Restore All Data" to see the complete flow.');
        }

        // Message Backup functionality
        let messageBackupState = {
            isRunning: false,
            currentMessage: 0,
            totalMessages: 2341,
            currentConversation: ''
        };

        function rescanMessages() {
            alert('🔍 Scanning message library...\n\nFound 2,341 messages in 47 conversations:\n• Most recent: Today\n• Oldest: 6 months ago\n• Conversations with family, friends, and work\n\nYour message library is ready for backup!');
        }

        function startMessageBackup() {
            if (confirm('🚀 Start Message Backup?\n\nThis will backup 2,341 messages from 47 conversations.\n\n• All conversations will be preserved\n• Messages will be encrypted for security\n• You can restore on any device\n• Conversation history maintained\n\nStart backup now?')) {
                messageBackupState.isRunning = true;
                messageBackupState.currentMessage = 0;

                // Hide backup button, show progress card
                document.getElementById('start-message-backup-btn').style.display = 'none';
                document.getElementById('message-progress-card').style.display = 'block';
                document.getElementById('message-results-card').style.display = 'none';

                simulateMessageBackup();
            }
        }

        function simulateMessageBackup() {
            if (!messageBackupState.isRunning) return;

            const conversations = [
                'Mom (23 messages)', 'Dad (18 messages)', 'Sarah (45 messages)',
                'John (32 messages)', 'Work Group (156 messages)', 'Bank (8 messages)',
                'Doctor (12 messages)', 'Pizza Place (5 messages)', 'Best Friend (89 messages)'
            ];

            // Simulate processing multiple messages at once (faster than individual)
            const messagesPerStep = Math.floor(Math.random() * 15) + 5; // 5-20 messages per step
            messageBackupState.currentMessage += messagesPerStep;

            if (messageBackupState.currentMessage > messageBackupState.totalMessages) {
                messageBackupState.currentMessage = messageBackupState.totalMessages;
            }

            messageBackupState.currentConversation = conversations[Math.floor(Math.random() * conversations.length)];

            const percentage = Math.round((messageBackupState.currentMessage / messageBackupState.totalMessages) * 100);

            // Update UI
            document.getElementById('current-conversation').textContent = `Processing: ${messageBackupState.currentConversation}`;
            document.getElementById('message-progress-fill').style.width = percentage + '%';
            document.getElementById('message-progress-count').textContent = `${messageBackupState.currentMessage} of ${messageBackupState.totalMessages} messages`;
            document.getElementById('message-progress-percent').textContent = percentage + '%';

            if (messageBackupState.currentMessage >= messageBackupState.totalMessages) {
                // Backup complete
                messageBackupState.isRunning = false;
                showMessageBackupComplete();
            } else {
                // Continue backup (faster than photos since messages are smaller)
                setTimeout(simulateMessageBackup, 20 + Math.random() * 30);
            }
        }

        function showMessageBackupComplete() {
            document.getElementById('message-progress-card').style.display = 'none';
            document.getElementById('message-results-card').style.display = 'block';

            // Show the backup button again after completion
            document.getElementById('start-message-backup-btn').style.display = 'block';

            setTimeout(() => {
                alert('🎉 Message Backup Complete!\n\nSuccessfully backed up 2,341 messages in 18 seconds!\n\n• 47 conversations preserved\n• All messages encrypted with AES-256\n• Securely stored in Supabase Cloud Storage\n• You can restore them on any device!');
            }, 500);
        }

        function restoreMessages() {
            if (confirm('💬 Restore Messages?\n\nThis will download your latest message backup from Supabase Cloud Storage.\n\nNote: This will show you the conversations for review.')) {
                setTimeout(() => {
                    alert('✅ Restore Complete!\n\nSuccessfully restored 47 conversation threads from your backup.\n\n• All message history decrypted\n• Conversations organized by contact\n• You can now review your message history\n• Your conversations are safe and secure!');
                }, 1000);
            }
        }

        function showDashboard() {
            console.log('Showing dashboard');
            hideAllScreens();
            hideAllContainers();
            document.getElementById('dashboard').parentElement.classList.remove('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
            updateTabStates('dashboard');
        }

        function showBackup() {
            console.log('Showing backup');
            hideAllScreens();
            hideAllContainers();
            document.getElementById('backup').parentElement.classList.remove('hidden');
            document.getElementById('backup').classList.remove('hidden');
            updateTabStates('backup');
        }

        function showRecovery() {
            console.log('Showing recovery');
            hideAllScreens();
            hideAllContainers();
            document.getElementById('recovery').parentElement.classList.remove('hidden');
            document.getElementById('recovery').classList.remove('hidden');
            updateTabStates('recovery');
        }

        function showSettings() {
            console.log('Showing settings');
            hideAllScreens();
            hideAllContainers();
            document.getElementById('settings').parentElement.classList.remove('hidden');
            document.getElementById('settings').classList.remove('hidden');
            updateTabStates('settings');
        }

        function updateTabStates(activeTab) {
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Add active class to current tab
            if (activeTab === 'welcome') {
                const homeTabs = document.querySelectorAll('.tab');
                homeTabs.forEach(tab => {
                    if (tab.textContent.includes('Home')) {
                        tab.classList.add('active');
                    }
                });
            } else if (activeTab === 'dashboard') {
                const dashboardTabs = document.querySelectorAll('.tab');
                dashboardTabs.forEach(tab => {
                    if (tab.textContent.includes('Dashboard')) {
                        tab.classList.add('active');
                    }
                });
            } else if (activeTab === 'backup') {
                const backupTabs = document.querySelectorAll('.tab');
                backupTabs.forEach(tab => {
                    if (tab.textContent.includes('Backup')) {
                        tab.classList.add('active');
                    }
                });
            } else if (activeTab === 'settings') {
                const settingsTabs = document.querySelectorAll('.tab');
                settingsTabs.forEach(tab => {
                    if (tab.textContent.includes('Settings')) {
                        tab.classList.add('active');
                    }
                });
            }
        }

        function hideAllContainers() {
            const containers = document.querySelectorAll('.phone-container');
            containers.forEach(container => {
                container.classList.add('hidden');
            });
        }

        function hideAllScreens() {
            document.getElementById('auth').classList.add('hidden');
            document.getElementById('onboarding').classList.add('hidden');
            document.getElementById('permissions').classList.add('hidden');
            document.getElementById('dashboard').classList.add('hidden');
            document.getElementById('backup').classList.add('hidden');
            document.getElementById('recovery').classList.add('hidden');
            document.getElementById('settings').classList.add('hidden');
        }

        function toggleSwitch(switchId) {
            const switchElement = document.getElementById(switchId);
            if (switchElement.classList.contains('off')) {
                switchElement.classList.remove('off');
            } else {
                switchElement.classList.add('off');
            }
        }

        function toggleDropdown() {
            const dropdown = document.querySelector('.dropdown');
            dropdown.classList.toggle('show');
        }

        function selectFrequency(frequency) {
            document.getElementById('frequencyDisplay').textContent = frequency;
            const dropdown = document.querySelector('.dropdown');
            dropdown.classList.remove('show');
        }

        // Close dropdown when clicking outside
        window.onclick = function(event) {
            if (!event.target.matches('.dropdown-button') && !event.target.matches('.dropdown-button span')) {
                const dropdown = document.querySelector('.dropdown');
                if (dropdown && dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            }
        }
    </script>
</body>
</html>
