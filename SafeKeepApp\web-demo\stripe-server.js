/**
 * Stripe Server for SafeKeep Web Demo
 * Handles server-side Stripe operations and webhook processing
 */

const http = require('http');
const url = require('url');
const querystring = require('querystring');

class StripeServer {
    constructor(stripeConfig) {
        this.config = stripeConfig.getConfig();
        this.subscriptionTiers = stripeConfig.getSubscriptionTiers();
        this.webhookEvents = stripeConfig.getWebhookEvents();
        
        // In-memory storage for demo (use database in production)
        this.customers = new Map();
        this.subscriptions = new Map();
        this.paymentIntents = new Map();
        this.paymentMethods = new Map();
        this.invoices = new Map();
        
        // Webhook handlers
        this.webhookHandlers = new Map();
        this.setupWebhookHandlers();
        
        console.log('🔧 Stripe Server initialized');
    }
    
    /**
     * Start the Stripe server
     */
    start(port = 3001) {
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });
        
        this.server.listen(port, () => {
            console.log(`🚀 Stripe Server running on http://localhost:${port}`);
            console.log('📋 Available endpoints:');
            console.log('  • POST /api/stripe/create-customer');
            console.log('  • POST /api/stripe/create-payment-intent');
            console.log('  • POST /api/stripe/create-subscription');
            console.log('  • POST /api/stripe/webhook');
            console.log('  • GET  /api/stripe/status');
        });
        
        return this.server;
    }
    
    /**
     * Handle HTTP requests
     */
    async handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        const path = parsedUrl.pathname;
        const method = req.method;
        
        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        
        // Handle preflight requests
        if (method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }
        
        try {
            // Route requests
            if (path === '/api/stripe/status' && method === 'GET') {
                await this.handleStatus(req, res);
            } else if (path === '/api/stripe/create-customer' && method === 'POST') {
                await this.handleCreateCustomer(req, res);
            } else if (path === '/api/stripe/create-payment-intent' && method === 'POST') {
                await this.handleCreatePaymentIntent(req, res);
            } else if (path === '/api/stripe/create-subscription' && method === 'POST') {
                await this.handleCreateSubscription(req, res);
            } else if (path === '/api/stripe/webhook' && method === 'POST') {
                await this.handleWebhook(req, res);
            } else if (path.startsWith('/api/stripe/customer/') && method === 'GET') {
                await this.handleGetCustomer(req, res);
            } else if (path.startsWith('/api/stripe/subscription/') && method === 'GET') {
                await this.handleGetSubscription(req, res);
            } else {
                this.sendError(res, 404, 'Endpoint not found');
            }
        } catch (error) {
            console.error('❌ Request handling error:', error);
            this.sendError(res, 500, 'Internal server error');
        }
    }
    
    /**
     * Handle status endpoint
     */
    async handleStatus(req, res) {
        const status = {
            status: 'OK',
            stripe_configured: !!(this.config.publishableKey && this.config.secretKey),
            webhook_configured: !!this.config.webhookSecret,
            demo_mode: this.config.demoMode,
            subscription_tiers: Object.keys(this.subscriptionTiers).length,
            customers_count: this.customers.size,
            subscriptions_count: this.subscriptions.size,
            timestamp: new Date().toISOString()
        };
        
        this.sendJSON(res, 200, status);
    }
    
    /**
     * Handle create customer
     */
    async handleCreateCustomer(req, res) {
        const body = await this.getRequestBody(req);
        const { email, name, metadata } = JSON.parse(body);
        
        // Simulate network delay in demo mode
        if (this.config.simulateNetworkDelay) {
            await this.delay(this.config.networkDelayMs);
        }
        
        const customerId = `cus_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const customer = {
            id: customerId,
            email: email,
            name: name || '',
            created: Math.floor(Date.now() / 1000),
            metadata: metadata || {},
            subscriptions: [],
            paymentMethods: [],
            defaultPaymentMethod: null,
            balance: 0,
            currency: this.config.currency
        };
        
        this.customers.set(customerId, customer);
        
        console.log(`✅ Customer created: ${customerId} (${email})`);
        this.sendJSON(res, 200, customer);
    }
    
    /**
     * Handle create payment intent
     */
    async handleCreatePaymentIntent(req, res) {
        const body = await this.getRequestBody(req);
        const { amount, currency, description, metadata, customerId } = JSON.parse(body);
        
        // Simulate network delay
        if (this.config.simulateNetworkDelay) {
            await this.delay(this.config.networkDelayMs);
        }
        
        const paymentIntentId = `pi_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const clientSecret = `${paymentIntentId}_secret_${Math.random().toString(36).substr(2, 16)}`;
        
        const paymentIntent = {
            id: paymentIntentId,
            client_secret: clientSecret,
            amount: amount,
            currency: currency || this.config.currency,
            status: 'requires_payment_method',
            created: Math.floor(Date.now() / 1000),
            description: description || '',
            metadata: metadata || {},
            customer: customerId || null,
            payment_method: null,
            last_payment_error: null
        };
        
        this.paymentIntents.set(paymentIntentId, paymentIntent);
        
        console.log(`✅ Payment intent created: ${paymentIntentId} (${(amount / 100).toFixed(2)})`);
        this.sendJSON(res, 200, {
            client_secret: clientSecret,
            payment_intent_id: paymentIntentId,
            amount: amount,
            currency: currency || this.config.currency
        });
    }
    
    /**
     * Handle create subscription
     */
    async handleCreateSubscription(req, res) {
        const body = await this.getRequestBody(req);
        const { customerId, tierId, paymentMethodId, trialEnd } = JSON.parse(body);
        
        // Simulate network delay
        if (this.config.simulateNetworkDelay) {
            await this.delay(this.config.networkDelayMs);
        }
        
        const tier = this.subscriptionTiers[tierId];
        if (!tier) {
            this.sendError(res, 400, `Invalid subscription tier: ${tierId}`);
            return;
        }
        
        const customer = this.customers.get(customerId);
        if (!customer) {
            this.sendError(res, 400, `Customer not found: ${customerId}`);
            return;
        }
        
        const subscriptionId = `sub_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const now = Math.floor(Date.now() / 1000);
        
        const subscription = {
            id: subscriptionId,
            customer: customerId,
            status: 'active',
            current_period_start: now,
            current_period_end: now + (30 * 24 * 60 * 60), // 30 days
            created: now,
            plan: {
                id: tier.stripePriceId,
                amount: tier.price,
                currency: tier.currency,
                interval: tier.interval,
                product: tier.stripeProductId
            },
            cancel_at_period_end: false,
            canceled_at: null,
            trial_end: trialEnd || null,
            default_payment_method: paymentMethodId || null,
            metadata: {
                tierId: tierId,
                tierName: tier.name
            }
        };
        
        this.subscriptions.set(subscriptionId, subscription);
        
        // Update customer
        customer.subscriptions.push(subscriptionId);
        this.customers.set(customerId, customer);
        
        console.log(`✅ Subscription created: ${subscriptionId} (${tier.name})`);
        this.sendJSON(res, 200, subscription);
    }
    
    /**
     * Handle webhook events
     */
    async handleWebhook(req, res) {
        const body = await this.getRequestBody(req);
        const signature = req.headers['stripe-signature'];
        
        try {
            // In a real implementation, you would verify the webhook signature
            // For demo purposes, we'll just parse the event
            const event = JSON.parse(body);
            
            console.log(`📨 Webhook received: ${event.type}`);
            
            const handler = this.webhookHandlers.get(event.type);
            if (handler) {
                await handler(event.data.object);
                console.log(`✅ Webhook processed: ${event.type}`);
            } else {
                console.log(`⚠️ No handler for webhook: ${event.type}`);
            }
            
            this.sendJSON(res, 200, { received: true });
            
        } catch (error) {
            console.error(`❌ Webhook processing failed:`, error);
            this.sendError(res, 400, 'Webhook processing failed');
        }
    }
    
    /**
     * Handle get customer
     */
    async handleGetCustomer(req, res) {
        const customerId = req.url.split('/').pop();
        const customer = this.customers.get(customerId);
        
        if (!customer) {
            this.sendError(res, 404, 'Customer not found');
            return;
        }
        
        this.sendJSON(res, 200, customer);
    }
    
    /**
     * Handle get subscription
     */
    async handleGetSubscription(req, res) {
        const subscriptionId = req.url.split('/').pop();
        const subscription = this.subscriptions.get(subscriptionId);
        
        if (!subscription) {
            this.sendError(res, 404, 'Subscription not found');
            return;
        }
        
        this.sendJSON(res, 200, subscription);
    }
    
    /**
     * Setup webhook handlers
     */
    setupWebhookHandlers() {
        this.webhookHandlers.set('customer.created', this.handleCustomerCreated.bind(this));
        this.webhookHandlers.set('customer.updated', this.handleCustomerUpdated.bind(this));
        this.webhookHandlers.set('customer.deleted', this.handleCustomerDeleted.bind(this));
        this.webhookHandlers.set('customer.subscription.created', this.handleSubscriptionCreated.bind(this));
        this.webhookHandlers.set('customer.subscription.updated', this.handleSubscriptionUpdated.bind(this));
        this.webhookHandlers.set('customer.subscription.deleted', this.handleSubscriptionDeleted.bind(this));
        this.webhookHandlers.set('payment_intent.succeeded', this.handlePaymentSucceeded.bind(this));
        this.webhookHandlers.set('payment_intent.payment_failed', this.handlePaymentFailed.bind(this));
        this.webhookHandlers.set('invoice.payment_succeeded', this.handleInvoicePaymentSucceeded.bind(this));
        this.webhookHandlers.set('invoice.payment_failed', this.handleInvoicePaymentFailed.bind(this));
    }
    
    // Webhook event handlers
    async handleCustomerCreated(customer) {
        console.log(`👤 Customer created webhook: ${customer.id}`);
        // Additional customer creation logic
    }
    
    async handleCustomerUpdated(customer) {
        console.log(`👤 Customer updated webhook: ${customer.id}`);
        // Additional customer update logic
    }
    
    async handleCustomerDeleted(customer) {
        console.log(`👤 Customer deleted webhook: ${customer.id}`);
        // Additional customer deletion logic
    }
    
    async handleSubscriptionCreated(subscription) {
        console.log(`📋 Subscription created webhook: ${subscription.id}`);
        // Update user permissions, send welcome email, etc.
    }
    
    async handleSubscriptionUpdated(subscription) {
        console.log(`📋 Subscription updated webhook: ${subscription.id}`);
        // Update user permissions based on new subscription status
    }
    
    async handleSubscriptionDeleted(subscription) {
        console.log(`📋 Subscription deleted webhook: ${subscription.id}`);
        // Revoke premium features, send cancellation email, etc.
    }
    
    async handlePaymentSucceeded(paymentIntent) {
        console.log(`💳 Payment succeeded webhook: ${paymentIntent.id}`);
        // Fulfill order, send receipt, etc.
    }
    
    async handlePaymentFailed(paymentIntent) {
        console.log(`💳 Payment failed webhook: ${paymentIntent.id}`);
        // Send payment failure notification, retry logic, etc.
    }
    
    async handleInvoicePaymentSucceeded(invoice) {
        console.log(`🧾 Invoice payment succeeded webhook: ${invoice.id}`);
        // Update subscription status, send receipt, etc.
    }
    
    async handleInvoicePaymentFailed(invoice) {
        console.log(`🧾 Invoice payment failed webhook: ${invoice.id}`);
        // Handle failed recurring payment, send dunning email, etc.
    }
    
    /**
     * Utility methods
     */
    async getRequestBody(req) {
        return new Promise((resolve, reject) => {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });
            req.on('end', () => {
                resolve(body);
            });
            req.on('error', reject);
        });
    }
    
    sendJSON(res, statusCode, data) {
        res.writeHead(statusCode, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(data, null, 2));
    }
    
    sendError(res, statusCode, message) {
        res.writeHead(statusCode, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: message }));
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Get demo data for testing
     */
    getDemoData() {
        return {
            customers: Array.from(this.customers.values()),
            subscriptions: Array.from(this.subscriptions.values()),
            paymentIntents: Array.from(this.paymentIntents.values()),
            paymentMethods: Array.from(this.paymentMethods.values())
        };
    }
    
    /**
     * Reset demo data
     */
    resetDemoData() {
        this.customers.clear();
        this.subscriptions.clear();
        this.paymentIntents.clear();
        this.paymentMethods.clear();
        console.log('🔄 Stripe server demo data reset');
    }
    
    /**
     * Stop the server
     */
    stop() {
        if (this.server) {
            this.server.close(() => {
                console.log('🛑 Stripe Server stopped');
            });
        }
    }
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StripeServer;
}

// For direct execution
if (require.main === module) {
    const StripeConfig = require('./stripe-config.js');
    const config = new StripeConfig();
    const server = new StripeServer(config);
    server.start(3001);
}