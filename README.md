# 🔐 SafeKeep - Secure Data Backup & Recovery App

SafeKeep is a comprehensive React Native application designed to help users securely backup and restore their precious data including photos, contacts, and messages. Built with end-to-end encryption and cloud storage integration.

## ✨ Features

### 📱 **Complete Data Backup**
- **📸 Photo Backup** - Secure backup of camera roll and photo library
- **📞 Contact Backup** - Complete contact information with deduplication
- **💬 Message Backup** - SMS/text message conversations with threading
- **🔐 End-to-End Encryption** - AES-256 encryption for all data
- **☁️ Cloud Storage** - Supabase integration for secure cloud storage

### 💳 **Stripe Payment Integration**
- **Subscription Management** - Multiple pricing tiers (Basic, Premium, Family)
- **Secure Payment Processing** - PCI-compliant payment handling
- **Test Mode Support** - Complete testing environment with test cards
- **Web Payment Testing** - Browser-based payment testing interface

### 🎯 **User-Friendly Design**
- **Grandparent-Friendly UI** - Designed for non-technical users
- **Clear Navigation** - Intuitive tab-based navigation
- **Progress Tracking** - Real-time backup/restore progress
- **Interactive Demo** - Web-based demo for all features

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- React Native development environment
- Stripe account (for payments)
- Supabase account (for cloud storage)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/Antongolivier/SafeKeeping.git
cd SafeKeeping
```

2. **Install React Native dependencies**
```bash
cd SafeKeepApp
npm install
```

3. **Install backend dependencies**
```bash
cd ..
npm install
```

4. **Set up environment variables**
```bash
# Copy the example .env file
cp .env.example .env

# Edit .env with your actual keys
# STRIPE_SECRET_KEY=sk_test_your_key_here
# STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
# SUPABASE_URL=your_supabase_url
# SUPABASE_ANON_KEY=your_supabase_key
```

### 🧪 Testing Stripe Integration

1. **Start the test server**
```bash
node test-server.js
```

2. **Open the web payment test**
```bash
# Open stripe-payment-test.html in your browser
# Or visit: file:///path/to/SafeKeeping/stripe-payment-test.html
```

3. **Use test cards**
- **Success**: `4242 4242 4242 4242`
- **Declined**: `4000 0000 0000 0002`
- **Expiry**: Any future date
- **CVC**: Any 3 digits

## 📁 Project Structure

```
SafeKeeping/
├── SafeKeepApp/                 # React Native application
│   ├── src/
│   │   ├── components/          # Reusable UI components
│   │   ├── screens/             # App screens
│   │   ├── services/            # Business logic & API services
│   │   ├── store/               # Redux state management
│   │   └── utils/               # Utilities and constants
│   ├── android/                 # Android-specific files
│   ├── ios/                     # iOS-specific files
│   └── web-demo.html           # Interactive web demo
├── backend-api-example.js       # Production backend example
├── test-server.js              # Development test server
├── stripe-payment-test.html    # Web payment testing interface
├── STRIPE_SETUP_GUIDE.md       # Detailed Stripe setup guide
└── README.md                   # This file
```

## 🔧 Development

### Running the React Native App

```bash
cd SafeKeepApp

# For iOS
npm run ios

# For Android
npm run android

# Start Metro bundler
npm start
```

### Testing Features

1. **Web Demo** - Open `SafeKeepApp/web-demo.html` for interactive feature demo
2. **Payment Testing** - Open `stripe-payment-test.html` for payment flow testing
3. **Backend Testing** - Open `test-stripe-integration.html` for API testing

## 🔐 Security Features

### Data Protection
- **Client-side encryption** before cloud upload
- **Zero-knowledge architecture** - service cannot read user data
- **Secure key management** - environment variable protection
- **PCI-compliant payments** - Stripe handles all payment data

### Privacy
- **No data collection** beyond necessary backup metadata
- **Local processing** - sensitive operations happen on device
- **Encrypted transmission** - all data encrypted in transit
- **User control** - users own their encryption keys

## 💳 Stripe Integration

### Payment Plans
- **Basic**: $2.99/month - 5GB storage, basic backup
- **Premium**: $9.99/month - 50GB storage, advanced features
- **Family**: $19.99/month - 200GB storage, up to 6 users

### Testing
- Complete test environment with mock data
- Real Stripe integration with test keys
- Web-based payment testing interface
- Comprehensive error handling

## 🌐 Web Demo

The project includes a comprehensive web demo showcasing all features:
- Interactive backup simulations
- Payment flow demonstrations
- Feature explanations with animations
- Mobile-responsive design

## 📚 Documentation

- **[Stripe Setup Guide](STRIPE_SETUP_GUIDE.md)** - Complete Stripe integration setup
- **[Database Schema](SafeKeepApp/database/SETUP_GUIDE.md)** - Database structure and setup
- **[API Documentation](backend-api-example.js)** - Backend API reference

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: [GitHub Issues](https://github.com/Antongolivier/SafeKeeping/issues)
- **Documentation**: Check the guides in the repository
- **Stripe Support**: [Stripe Documentation](https://stripe.com/docs)

## 🎯 Roadmap

- [ ] iOS App Store deployment
- [ ] Google Play Store deployment
- [ ] Advanced backup scheduling
- [ ] Family sharing features
- [ ] Multi-device synchronization
- [ ] Backup analytics dashboard

---

**Built with ❤️ for secure data protection and peace of mind.**
