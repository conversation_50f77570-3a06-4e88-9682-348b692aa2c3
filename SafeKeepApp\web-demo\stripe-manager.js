/**
 * Stripe Manager for SafeKeep Web Demo
 * Handles Stripe integration infrastructure
 */

class StripeManager {
    constructor(stripeConfig = null) {
        this.stripe = null;
        this.elements = null;
        this.cardElement = null;
        this.customers = new Map(); // In-memory customer storage for demo
        this.subscriptions = new Map(); // In-memory subscription storage for demo
        this.paymentMethods = new Map(); // In-memory payment methods for demo
        
        // Initialize configuration
        if (stripeConfig) {
            this.stripeConfig = stripeConfig;
            this.config = stripeConfig.getConfig();
        } else {
            // Fallback to default configuration
            this.config = {
                publishableKey: 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
                secretKey: 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
                webhookSecret: 'whsec_demo_webhook_secret_for_safekeep_demo',
                demoMode: true,
                simulateNetworkDelay: true,
                networkDelayMs: 1000
            };
        }
        
        // API base URL for server communication
        this.apiBaseUrl = 'http://localhost:3001';
        
        // Get subscription tiers from configuration
        this.subscriptionTiers = this.stripeConfig ? 
            this.stripeConfig.getSubscriptionTiers() : 
            this.getDefaultSubscriptionTiers();
        
        this.webhookHandlers = new Map();
        this.setupWebhookHandlers();
    }
    
    /**
     * Get default subscription tiers (fallback)
     */
    getDefaultSubscriptionTiers() {
        return {
            // Individual Services
            contacts_only: {
                id: 'contacts_only',
                name: 'Contacts Only',
                price: 99, // $0.99
                currency: 'usd',
                interval: 'month',
                features: {
                    contactsBackup: true,
                    messagesBackup: false,
                    photosBackup: false,
                    storageLimit: 1,
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30,
                    restoreSpeed: 'standard'
                }
            },
            messages_only: {
                id: 'messages_only',
                name: 'Messages Only',
                price: 199, // $1.99
                currency: 'usd',
                interval: 'month',
                features: {
                    contactsBackup: false,
                    messagesBackup: true,
                    photosBackup: false,
                    storageLimit: 5,
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30,
                    restoreSpeed: 'standard'
                }
            },
            photos_only: {
                id: 'photos_only',
                name: 'Photos Only',
                price: 499, // $4.99
                currency: 'usd',
                interval: 'month',
                features: {
                    contactsBackup: false,
                    messagesBackup: false,
                    photosBackup: true,
                    storageLimit: 50,
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30,
                    restoreSpeed: 'standard'
                }
            },
            // Combination Services
            contacts_messages: {
                id: 'contacts_messages',
                name: 'Contacts + Messages',
                price: 249, // $2.49
                currency: 'usd',
                interval: 'month',
                features: {
                    contactsBackup: true,
                    messagesBackup: true,
                    photosBackup: false,
                    storageLimit: 10,
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30,
                    restoreSpeed: 'standard'
                }
            },
            contacts_photos: {
                id: 'contacts_photos',
                name: 'Contacts + Photos',
                price: 549, // $5.49
                currency: 'usd',
                interval: 'month',
                features: {
                    contactsBackup: true,
                    messagesBackup: false,
                    photosBackup: true,
                    storageLimit: 60,
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30,
                    restoreSpeed: 'standard'
                }
            },
            messages_photos: {
                id: 'messages_photos',
                name: 'Messages + Photos',
                price: 649, // $6.49
                currency: 'usd',
                interval: 'month',
                features: {
                    contactsBackup: false,
                    messagesBackup: true,
                    photosBackup: true,
                    storageLimit: 70,
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30,
                    restoreSpeed: 'standard'
                }
            },
            complete_backup: {
                id: 'complete_backup',
                name: 'Complete Backup',
                price: 699, // $6.99
                currency: 'usd',
                interval: 'month',
                features: {
                    contactsBackup: true,
                    messagesBackup: true,
                    photosBackup: true,
                    storageLimit: 100,
                    backupFrequency: ['manual', 'daily', 'weekly'],
                    prioritySupport: true,
                    advancedEncryption: true,
                    multiDeviceSync: true,
                    backupHistory: 90,
                    restoreSpeed: 'priority'
                }
            }
        };
    }    /**

     * Initialize Stripe with publishable key
     */
    async initialize() {
        try {
            if (typeof Stripe === 'undefined') {
                throw new Error('Stripe.js not loaded. Make sure to include the Stripe script.');
            }
            
            this.stripe = Stripe(this.config.publishableKey);
            this.elements = this.stripe.elements();
            
            console.log('✅ Stripe Manager initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Stripe Manager:', error);
            throw error;
        }
    }

    /**
     * Create Stripe customer
     */
    async createCustomer(customerData) {
        try {
            if (this.config.demoMode) {
                // Demo mode - use local storage
                const customerId = `cus_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                
                const customer = {
                    id: customerId,
                    email: customerData.email,
                    name: customerData.name || '',
                    created: Math.floor(Date.now() / 1000),
                    metadata: customerData.metadata || {},
                    subscriptions: [],
                    paymentMethods: [],
                    defaultPaymentMethod: null
                };
                
                this.customers.set(customerId, customer);
                console.log('✅ Demo customer created:', customerId);
                return customer;
            } else {
                // Production mode - use server API
                const response = await fetch(`${this.apiBaseUrl}/api/stripe/create-customer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(customerData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const customer = await response.json();
                console.log('✅ Customer created via API:', customer.id);
                return customer;
            }
        } catch (error) {
            console.error('❌ Failed to create customer:', error);
            throw error;
        }
    }
    
    /**
     * Create customer via server API
     */
    async createCustomerViaAPI(customerData) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/stripe/create-customer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(customerData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const customer = await response.json();
            console.log('✅ Customer created via server API:', customer.id);
            return customer;
        } catch (error) {
            console.error('❌ Failed to create customer via API:', error);
            throw error;
        }
    }

    /**
     * Get customer by ID
     */
    async getCustomer(customerId) {
        const customer = this.customers.get(customerId);
        if (!customer) {
            throw new Error(`Customer ${customerId} not found`);
        }
        return customer;
    }

    /**
     * Update customer
     */
    async updateCustomer(customerId, updateData) {
        const customer = await this.getCustomer(customerId);
        
        Object.assign(customer, {
            ...updateData,
            updated: Math.floor(Date.now() / 1000)
        });
        
        this.customers.set(customerId, customer);
        return customer;
    }

    /**
     * Create payment session for subscription
     */
    async createPaymentSession(sessionData) {
        try {
            const sessionId = `cs_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const tier = this.subscriptionTiers[sessionData.tierId];
            
            if (!tier) {
                throw new Error(`Invalid subscription tier: ${sessionData.tierId}`);
            }
            
            const session = {
                id: sessionId,
                customerId: sessionData.customerId,
                tierId: sessionData.tierId,
                amount: tier.price,
                currency: tier.currency,
                status: 'pending',
                created: Math.floor(Date.now() / 1000),
                expiresAt: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
                metadata: sessionData.metadata || {}
            };
            
            console.log('✅ Payment session created:', sessionId);
            return session;
        } catch (error) {
            console.error('❌ Failed to create payment session:', error);
            throw error;
        }
    }
    
    /**
     * Create payment intent via server API
     */
    async createPaymentIntentViaAPI(paymentData) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/stripe/create-payment-intent`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(paymentData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            console.log('✅ Payment intent created via API:', result.payment_intent_id);
            return result;
        } catch (error) {
            console.error('❌ Failed to create payment intent via API:', error);
            throw error;
        }
    }

    /**
     * Create payment intent
     */
    async createPaymentIntent(paymentData) {
        try {
            if (this.config.demoMode) {
                // Demo mode - simulate locally
                const paymentIntentId = `pi_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const clientSecret = `${paymentIntentId}_secret_${Math.random().toString(36).substr(2, 16)}`;
                
                const paymentIntent = {
                    id: paymentIntentId,
                    client_secret: clientSecret,
                    amount: paymentData.amount,
                    currency: paymentData.currency || this.config.currency,
                    status: 'requires_payment_method',
                    created: Math.floor(Date.now() / 1000),
                    description: paymentData.description || '',
                    metadata: paymentData.metadata || {}
                };
                
                console.log('✅ Demo payment intent created:', paymentIntentId);
                return paymentIntent;
            } else {
                // Production mode - use server API
                return await this.createPaymentIntentViaAPI(paymentData);
            }
        } catch (error) {
            console.error('❌ Failed to create payment intent:', error);
            throw error;
        }
    }

    /**
     * Confirm payment intent (demo simulation)
     */
    async confirmPaymentIntent(paymentIntentId, paymentMethodData) {
        try {
            // Simulate payment processing delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Simulate success/failure based on test card numbers
            const cardNumber = paymentMethodData.card?.number || '';
            let status = 'succeeded';
            
            // Test card numbers for different scenarios
            if (cardNumber.includes('****************')) {
                status = 'requires_payment_method'; // Card declined
            } else if (cardNumber.includes('****************')) {
                status = 'requires_payment_method'; // Expired card
            } else if (cardNumber.includes('****************')) {
                status = 'requires_payment_method'; // Incorrect CVC
            }
            
            const result = {
                paymentIntent: {
                    id: paymentIntentId,
                    status: status,
                    amount_received: status === 'succeeded' ? paymentMethodData.amount : 0,
                    created: Math.floor(Date.now() / 1000),
                    payment_method: {
                        id: `pm_demo_${Date.now()}`,
                        type: 'card',
                        card: {
                            brand: this.getCardBrand(cardNumber),
                            last4: cardNumber.slice(-4),
                            exp_month: paymentMethodData.card?.exp_month || 12,
                            exp_year: paymentMethodData.card?.exp_year || 2025
                        }
                    }
                }
            };
            
            if (status !== 'succeeded') {
                result.error = {
                    type: 'card_error',
                    code: 'card_declined',
                    message: 'Your card was declined.'
                };
            }
            
            console.log(`✅ Payment intent ${status}:`, paymentIntentId);
            return result;
        } catch (error) {
            console.error('❌ Failed to confirm payment intent:', error);
            throw error;
        }
    }

    /**
     * Get card brand from number
     */
    getCardBrand(cardNumber) {
        const number = cardNumber.replace(/\s/g, '');
        if (number.startsWith('4')) return 'visa';
        if (number.startsWith('5') || number.startsWith('2')) return 'mastercard';
        if (number.startsWith('3')) return 'amex';
        if (number.startsWith('6')) return 'discover';
        return 'unknown';
    }    /**

     * Create subscription
     */
    async createSubscription(subscriptionData) {
        try {
            const subscriptionId = `sub_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const tier = this.subscriptionTiers[subscriptionData.tierId];
            
            if (!tier) {
                throw new Error(`Invalid subscription tier: ${subscriptionData.tierId}`);
            }
            
            const now = Math.floor(Date.now() / 1000);
            const subscription = {
                id: subscriptionId,
                customerId: subscriptionData.customerId,
                tierId: subscriptionData.tierId,
                status: 'active',
                current_period_start: now,
                current_period_end: now + (30 * 24 * 60 * 60), // 30 days
                created: now,
                plan: tier,
                cancel_at_period_end: false,
                canceled_at: null,
                trial_end: subscriptionData.trialEnd || null,
                metadata: subscriptionData.metadata || {}
            };
            
            this.subscriptions.set(subscriptionId, subscription);
            
            // Update customer with subscription
            const customer = await this.getCustomer(subscriptionData.customerId);
            customer.subscriptions.push(subscriptionId);
            this.customers.set(subscriptionData.customerId, customer);
            
            console.log('✅ Subscription created:', subscriptionId);
            return subscription;
        } catch (error) {
            console.error('❌ Failed to create subscription:', error);
            throw error;
        }
    }

    /**
     * Get subscription by ID
     */
    async getSubscription(subscriptionId) {
        const subscription = this.subscriptions.get(subscriptionId);
        if (!subscription) {
            throw new Error(`Subscription ${subscriptionId} not found`);
        }
        return subscription;
    }

    /**
     * Update subscription
     */
    async updateSubscription(subscriptionId, updateData) {
        const subscription = await this.getSubscription(subscriptionId);
        
        Object.assign(subscription, {
            ...updateData,
            updated: Math.floor(Date.now() / 1000)
        });
        
        this.subscriptions.set(subscriptionId, subscription);
        return subscription;
    }

    /**
     * Cancel subscription
     */
    async cancelSubscription(subscriptionId, cancelAtPeriodEnd = true) {
        const subscription = await this.getSubscription(subscriptionId);
        
        if (cancelAtPeriodEnd) {
            subscription.cancel_at_period_end = true;
            subscription.status = 'active'; // Remains active until period end
        } else {
            subscription.status = 'canceled';
            subscription.canceled_at = Math.floor(Date.now() / 1000);
        }
        
        this.subscriptions.set(subscriptionId, subscription);
        console.log('✅ Subscription canceled:', subscriptionId);
        return subscription;
    } 
   /**
     * Add payment method to customer
     */
    async addPaymentMethod(customerId, paymentMethodData) {
        try {
            const paymentMethodId = `pm_demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            const paymentMethod = {
                id: paymentMethodId,
                customerId: customerId,
                type: paymentMethodData.type || 'card',
                card: {
                    brand: paymentMethodData.card?.brand || 'visa',
                    last4: paymentMethodData.card?.last4 || '4242',
                    exp_month: paymentMethodData.card?.exp_month || 12,
                    exp_year: paymentMethodData.card?.exp_year || 2025,
                    fingerprint: `fp_${Math.random().toString(36).substr(2, 16)}`
                },
                billing_details: paymentMethodData.billing_details || {},
                created: Math.floor(Date.now() / 1000)
            };
            
            this.paymentMethods.set(paymentMethodId, paymentMethod);
            
            // Update customer with payment method
            const customer = await this.getCustomer(customerId);
            customer.paymentMethods.push(paymentMethodId);
            
            // Set as default if it's the first payment method
            if (!customer.defaultPaymentMethod) {
                customer.defaultPaymentMethod = paymentMethodId;
            }
            
            this.customers.set(customerId, customer);
            
            console.log('✅ Payment method added:', paymentMethodId);
            return paymentMethod;
        } catch (error) {
            console.error('❌ Failed to add payment method:', error);
            throw error;
        }
    }

    /**
     * Get customer payment methods
     */
    async getCustomerPaymentMethods(customerId) {
        const customer = await this.getCustomer(customerId);
        const paymentMethods = [];
        
        for (const pmId of customer.paymentMethods) {
            const pm = this.paymentMethods.get(pmId);
            if (pm) {
                paymentMethods.push(pm);
            }
        }
        
        return paymentMethods;
    }

    /**
     * Remove payment method
     */
    async removePaymentMethod(paymentMethodId) {
        const paymentMethod = this.paymentMethods.get(paymentMethodId);
        if (!paymentMethod) {
            throw new Error(`Payment method ${paymentMethodId} not found`);
        }
        
        // Remove from customer
        const customer = await this.getCustomer(paymentMethod.customerId);
        customer.paymentMethods = customer.paymentMethods.filter(id => id !== paymentMethodId);
        
        // Update default if this was the default
        if (customer.defaultPaymentMethod === paymentMethodId) {
            customer.defaultPaymentMethod = customer.paymentMethods[0] || null;
        }
        
        this.customers.set(paymentMethod.customerId, customer);
        this.paymentMethods.delete(paymentMethodId);
        
        console.log('✅ Payment method removed:', paymentMethodId);
        return { id: paymentMethodId, deleted: true };
    } 
   /**
     * Setup webhook handlers
     */
    setupWebhookHandlers() {
        // Customer events
        this.webhookHandlers.set('customer.created', this.handleCustomerCreated.bind(this));
        this.webhookHandlers.set('customer.updated', this.handleCustomerUpdated.bind(this));
        this.webhookHandlers.set('customer.deleted', this.handleCustomerDeleted.bind(this));
        
        // Subscription events
        this.webhookHandlers.set('customer.subscription.created', this.handleSubscriptionCreated.bind(this));
        this.webhookHandlers.set('customer.subscription.updated', this.handleSubscriptionUpdated.bind(this));
        this.webhookHandlers.set('customer.subscription.deleted', this.handleSubscriptionDeleted.bind(this));
        
        // Payment events
        this.webhookHandlers.set('payment_intent.succeeded', this.handlePaymentSucceeded.bind(this));
        this.webhookHandlers.set('payment_intent.payment_failed', this.handlePaymentFailed.bind(this));
        
        // Invoice events
        this.webhookHandlers.set('invoice.payment_succeeded', this.handleInvoicePaymentSucceeded.bind(this));
        this.webhookHandlers.set('invoice.payment_failed', this.handleInvoicePaymentFailed.bind(this));
    }

    /**
     * Process webhook event
     */
    async processWebhook(event) {
        try {
            console.log(`📨 Processing webhook: ${event.type}`);
            
            const handler = this.webhookHandlers.get(event.type);
            if (handler) {
                await handler(event.data.object);
                console.log(`✅ Webhook processed: ${event.type}`);
            } else {
                console.log(`⚠️ No handler for webhook: ${event.type}`);
            }
            
            return { received: true };
        } catch (error) {
            console.error(`❌ Webhook processing failed for ${event.type}:`, error);
            throw error;
        }
    }
    
    /**
     * Send webhook to server for processing
     */
    async sendWebhookToServer(event) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/stripe/webhook`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Stripe-Signature': 'demo_signature' // In production, use real signature
                },
                body: JSON.stringify(event)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            console.log('✅ Webhook sent to server:', event.type);
            return result;
        } catch (error) {
            console.error('❌ Failed to send webhook to server:', error);
            throw error;
        }
    }
    
    /**
     * Get server status
     */
    async getServerStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/stripe/status`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const status = await response.json();
            console.log('✅ Server status retrieved');
            return status;
        } catch (error) {
            console.error('❌ Failed to get server status:', error);
            throw error;
        }
    }

    // Webhook event handlers
    async handleCustomerCreated(customer) {
        console.log('👤 Customer created:', customer.id);
        // Additional customer creation logic here
    }

    async handleCustomerUpdated(customer) {
        console.log('👤 Customer updated:', customer.id);
        // Additional customer update logic here
    }

    async handleCustomerDeleted(customer) {
        console.log('👤 Customer deleted:', customer.id);
        // Additional customer deletion logic here
    }

    async handleSubscriptionCreated(subscription) {
        console.log('📋 Subscription created:', subscription.id);
        // Update user permissions, send welcome email, etc.
    }

    async handleSubscriptionUpdated(subscription) {
        console.log('📋 Subscription updated:', subscription.id);
        // Update user permissions based on new subscription status
    }

    async handleSubscriptionDeleted(subscription) {
        console.log('📋 Subscription deleted:', subscription.id);
        // Revoke premium features, send cancellation email, etc.
    }

    async handlePaymentSucceeded(paymentIntent) {
        console.log('💳 Payment succeeded:', paymentIntent.id);
        // Fulfill order, send receipt, etc.
    }

    async handlePaymentFailed(paymentIntent) {
        console.log('💳 Payment failed:', paymentIntent.id);
        // Send payment failure notification, retry logic, etc.
    }

    async handleInvoicePaymentSucceeded(invoice) {
        console.log('🧾 Invoice payment succeeded:', invoice.id);
        // Update subscription status, send receipt, etc.
    }

    async handleInvoicePaymentFailed(invoice) {
        console.log('🧾 Invoice payment failed:', invoice.id);
        // Handle failed recurring payment, send dunning email, etc.
    }    /*
*
     * Get subscription tiers
     */
    getSubscriptionTiers() {
        return this.subscriptionTiers;
    }

    /**
     * Get subscription tier by ID
     */
    getSubscriptionTier(tierId) {
        return this.subscriptionTiers[tierId];
    }

    /**
     * Create Stripe Elements card element
     */
    createCardElement(options = {}) {
        if (!this.elements) {
            throw new Error('Stripe Elements not initialized');
        }
        
        const defaultOptions = {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
        };
        
        this.cardElement = this.elements.create('card', { ...defaultOptions, ...options });
        return this.cardElement;
    }

    /**
     * Mount card element to DOM
     */
    mountCardElement(elementId) {
        if (!this.cardElement) {
            throw new Error('Card element not created');
        }
        
        this.cardElement.mount(`#${elementId}`);
        
        // Add event listeners
        this.cardElement.on('change', (event) => {
            const displayError = document.getElementById('card-errors');
            if (displayError) {
                if (event.error) {
                    displayError.textContent = event.error.message;
                } else {
                    displayError.textContent = '';
                }
            }
        });
    }

    /**
     * Get demo data for testing
     */
    getDemoData() {
        return {
            customers: Array.from(this.customers.values()),
            subscriptions: Array.from(this.subscriptions.values()),
            paymentMethods: Array.from(this.paymentMethods.values()),
            subscriptionTiers: this.subscriptionTiers
        };
    }

    /**
     * Reset demo data
     */
    resetDemoData() {
        this.customers.clear();
        this.subscriptions.clear();
        this.paymentMethods.clear();
        console.log('🔄 Demo data reset');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.StripeManager = StripeManager;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StripeManager;
}