import { Router } from 'express';
import { BillingController } from '../controllers/BillingController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const billingController = new BillingController();

/**
 * Billing routes for modular pricing system
 * Payment intent and webhook routes require authentication
 * Webhook route has special handling for Stripe signature verification
 */

// POST /api/billing/payment-intent - Creates Stripe payment intent for service combination
router.post('/payment-intent', authenticateToken, (req, res) => {
  billingController.createPaymentIntent(req, res);
});

// POST /api/billing/webhook - Handles Stripe webhook events (no auth required for webhooks)
router.post('/webhook', (req, res) => {
  billingController.handleWebhook(req, res);
});

// GET /api/billing/status/:userId - Returns billing status and next payment date
router.get('/status/:userId', authenticateToken, (req, res) => {
  billingController.getBillingStatus(req, res);
});

export default router;