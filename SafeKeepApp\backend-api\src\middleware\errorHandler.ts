import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types/modular-pricing';

// Custom error classes
export class ValidationError extends Error {
  public details: string[];
  
  constructor(message: string, details: string[] = []) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }
}

export class ServiceCombinationError extends ValidationError {
  constructor(message: string, details: string[] = []) {
    super(message, details);
    this.name = 'ServiceCombinationError';
  }
}

export class StripeError extends Error {
  public stripeCode?: string;
  public stripeType?: string;
  public declineCode?: string;
  public statusCode: number;
  
  constructor(message: string, stripeError?: any) {
    super(message);
    this.name = 'StripeError';
    
    if (stripeError) {
      this.stripeCode = stripeError.code;
      this.stripeType = stripeError.type;
      this.declineCode = stripeError.decline_code;
      this.statusCode = this.getStatusCodeFromStripeError(stripeError);
    } else {
      this.statusCode = 500;
    }
  }
  
  private getStatusCodeFromStripeError(stripeError: any): number {
    switch (stripeError.type) {
      case 'card_error':
        return 402; // Payment Required
      case 'rate_limit_error':
        return 429; // Too Many Requests
      case 'invalid_request_error':
        return 400; // Bad Request
      case 'authentication_error':
        return 401; // Unauthorized
      case 'api_connection_error':
      case 'api_error':
      default:
        return 502; // Bad Gateway
    }
  }
}

export class DatabaseError extends Error {
  public statusCode: number = 500;
  
  constructor(message: string, originalError?: any) {
    super(message);
    this.name = 'DatabaseError';
    
    // Add original error details if available
    if (originalError) {
      this.stack = originalError.stack;
    }
  }
}

export class BusinessLogicError extends Error {
  public statusCode: number;
  
  constructor(message: string, statusCode: number = 422) {
    super(message);
    this.name = 'BusinessLogicError';
    this.statusCode = statusCode;
  }
}

/**
 * Global error handling middleware
 * Handles all types of errors with appropriate status codes and messages
 */
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log error for debugging
  console.error(`Error in ${req.method} ${req.path}:`, {
    message: error.message,
    stack: error.stack,
    user: req.user?.id,
    timestamp: new Date().toISOString()
  });

  // Default error response
  let statusCode = 500;
  let errorCode = 'INTERNAL_SERVER_ERROR';
  let message = 'An unexpected error occurred';
  let details: any = undefined;

  // Handle specific error types
  if (error instanceof ValidationError || error instanceof ServiceCombinationError) {
    statusCode = 400;
    errorCode = 'VALIDATION_ERROR';
    message = error.message;
    details = error.details;
  } else if (error instanceof StripeError) {
    statusCode = error.statusCode;
    errorCode = 'STRIPE_ERROR';
    message = error.message;
    details = {
      type: error.stripeType,
      code: error.stripeCode,
      decline_code: error.declineCode
    };
  } else if (error instanceof DatabaseError) {
    statusCode = 500;
    errorCode = 'DATABASE_ERROR';
    message = 'Database operation failed';
    // Don't expose internal database errors in production
    if (process.env.NODE_ENV === 'development') {
      details = error.message;
    }
  } else if (error instanceof BusinessLogicError) {
    statusCode = error.statusCode;
    errorCode = 'BUSINESS_LOGIC_ERROR';
    message = error.message;
  } else if (error.name === 'UnauthorizedError' || error.message?.includes('token')) {
    statusCode = 401;
    errorCode = 'UNAUTHORIZED';
    message = 'Authentication required';
  } else if (error.name === 'ForbiddenError' || error.message?.includes('permission')) {
    statusCode = 403;
    errorCode = 'FORBIDDEN';
    message = 'Insufficient permissions';
  } else if (error.name === 'NotFoundError' || error.message?.includes('not found')) {
    statusCode = 404;
    errorCode = 'NOT_FOUND';
    message = 'Resource not found';
  } else if (error.name === 'TimeoutError') {
    statusCode = 408;
    errorCode = 'REQUEST_TIMEOUT';
    message = 'Request timeout';
  } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
    statusCode = 503;
    errorCode = 'SERVICE_UNAVAILABLE';
    message = 'External service unavailable';
  }

  const response: ApiResponse = {
    success: false,
    error: {
      code: errorCode,
      message: message,
      details: details,
      timestamp: new Date().toISOString()
    }
  };

  res.status(statusCode).json(response);
};

/**
 * Validation error handler for service combinations
 * Creates and throws a ServiceCombinationError with detailed validation messages
 */
export const handleValidationError = (errors: string[]): never => {
  throw new ServiceCombinationError('Invalid service combination', errors);
};

/**
 * Enhanced Stripe API error handler with retry logic
 * Handles different types of Stripe errors appropriately
 */
export const handleStripeError = (stripeError: any): never => {
  let message = 'Payment processing failed';
  
  // Customize message based on error type
  switch (stripeError.type) {
    case 'card_error':
      message = stripeError.message || 'Your card was declined';
      break;
    case 'rate_limit_error':
      message = 'Too many requests. Please try again later';
      break;
    case 'invalid_request_error':
      message = 'Invalid payment request';
      break;
    case 'authentication_error':
      message = 'Payment authentication failed';
      break;
    case 'api_connection_error':
      message = 'Payment service temporarily unavailable';
      break;
    case 'api_error':
      message = 'Payment processing error';
      break;
    default:
      message = stripeError.message || 'Payment processing failed';
  }
  
  throw new StripeError(message, stripeError);
};

/**
 * Database error handler
 * Wraps database errors with appropriate error handling
 */
export const handleDatabaseError = (error: any, operation: string): never => {
  const message = `Database ${operation} failed`;
  throw new DatabaseError(message, error);
};

/**
 * Business logic error handler
 * For application-specific business rule violations
 */
export const handleBusinessLogicError = (message: string, statusCode: number = 422): never => {
  throw new BusinessLogicError(message, statusCode);
};

/**
 * Retry logic for external API calls
 * Implements exponential backoff for retryable errors
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      // Don't retry for certain error types
      if (error instanceof ValidationError || 
          error instanceof ServiceCombinationError ||
          error instanceof BusinessLogicError ||
          (error instanceof StripeError && error.stripeType === 'card_error')) {
        throw error;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      console.warn(`Retry attempt ${attempt}/${maxRetries} after ${delay}ms delay:`, error.message);
    }
  }
  
  throw lastError;
};

/**
 * Async error wrapper for route handlers
 * Automatically catches async errors and passes them to error middleware
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Request validation middleware
 * Validates request body against schema and throws validation errors
 */
export const validateRequest = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const validationErrors = error.details.map((detail: any) => detail.message);
      throw new ValidationError('Request validation failed', validationErrors);
    }
    
    next();
  };
};

/**
 * Service combination validation
 * Validates service combinations and throws appropriate errors
 */
export const validateServiceCombination = (serviceIds: string[]): void => {
  const errors: string[] = [];
  
  // Check if at least one service is selected
  if (!serviceIds || serviceIds.length === 0) {
    errors.push('At least one service must be selected');
  }
  
  // Check for duplicate services
  const uniqueServices = new Set(serviceIds);
  if (uniqueServices.size !== serviceIds.length) {
    errors.push('Duplicate services are not allowed');
  }
  
  // Check for valid service IDs
  const validServices = ['contacts', 'messages', 'photos'];
  const invalidServices = serviceIds.filter(id => !validServices.includes(id));
  if (invalidServices.length > 0) {
    errors.push(`Invalid service IDs: ${invalidServices.join(', ')}`);
  }
  
  if (errors.length > 0) {
    throw new ServiceCombinationError('Invalid service combination', errors);
  }
};

/**
 * 404 handler for undefined routes
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  const response: ApiResponse = {
    success: false,
    error: {
      code: 'ROUTE_NOT_FOUND',
      message: `Route ${req.method} ${req.path} not found`,
      timestamp: new Date().toISOString()
    }
  };
  res.status(404).json(response);
};