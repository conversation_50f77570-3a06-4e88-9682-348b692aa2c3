/**
 * Analytics Dashboard - Real-time analytics dashboard for performance monitoring
 * Implements requirements 6.1, 6.2, 6.3, 6.4 for comprehensive backup analytics visualization
 */

class AnalyticsDashboard {
    constructor(performanceMonitor) {
        this.performanceMonitor = performanceMonitor;
        this.container = null;
        this.charts = {};
        this.updateInterval = null;
        this.isVisible = false;
        
        this.init();
    }

    init() {
        this.createDashboard();
        this.setupEventListeners();
        console.log('Analytics Dashboard initialized');
    }

    createDashboard() {
        // Create dashboard container
        this.container = document.createElement('div');
        this.container.id = 'analytics-dashboard';
        this.container.className = 'analytics-dashboard hidden';
        
        this.container.innerHTML = `
            <div class="dashboard-header">
                <h2>Performance Analytics Dashboard</h2>
                <div class="dashboard-controls">
                    <button id="refresh-analytics" class="btn btn-primary">Refresh</button>
                    <button id="export-analytics" class="btn btn-secondary">Export Data</button>
                    <button id="clear-analytics" class="btn btn-warning">Clear Data</button>
                    <button id="close-analytics" class="btn btn-close">×</button>
                </div>
            </div>
            
            <div class="dashboard-content">
                <div class="metrics-grid">
                    <!-- Performance Overview -->
                    <div class="metric-card">
                        <h3>Performance Overview</h3>
                        <div id="performance-overview" class="metric-content">
                            <div class="metric-item">
                                <span class="metric-label">Page Load Time:</span>
                                <span id="page-load-time" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">First Contentful Paint:</span>
                                <span id="fcp-time" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Largest Contentful Paint:</span>
                                <span id="lcp-time" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Cumulative Layout Shift:</span>
                                <span id="cls-score" class="metric-value">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Backup Performance -->
                    <div class="metric-card">
                        <h3>Backup Performance</h3>
                        <div id="backup-performance" class="metric-content">
                            <div class="metric-item">
                                <span class="metric-label">Success Rate:</span>
                                <span id="backup-success-rate" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Average Duration:</span>
                                <span id="backup-avg-duration" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Throughput:</span>
                                <span id="backup-throughput" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Total Backups:</span>
                                <span id="backup-total" class="metric-value">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Resource Usage -->
                    <div class="metric-card">
                        <h3>Resource Usage</h3>
                        <div id="resource-usage" class="metric-content">
                            <div class="metric-item">
                                <span class="metric-label">Memory Used:</span>
                                <span id="memory-used" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Memory Total:</span>
                                <span id="memory-total" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Connection Type:</span>
                                <span id="connection-type" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Network Speed:</span>
                                <span id="network-speed" class="metric-value">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- Error Analysis -->
                    <div class="metric-card">
                        <h3>Error Analysis</h3>
                        <div id="error-analysis" class="metric-content">
                            <div class="metric-item">
                                <span class="metric-label">Total Errors:</span>
                                <span id="total-errors" class="metric-value">-</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Recent Errors:</span>
                                <span id="recent-errors" class="metric-value">-</span>
                            </div>
                            <div id="error-list" class="error-list"></div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="charts-section">
                    <div class="chart-container">
                        <h3>Backup Performance Trends</h3>
                        <canvas id="backup-performance-chart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h3>Network Performance</h3>
                        <canvas id="network-performance-chart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h3>User Interaction Heatmap</h3>
                        <div id="heatmap-container" class="heatmap-container"></div>
                    </div>
                </div>

                <!-- Recommendations Section -->
                <div class="recommendations-section">
                    <h3>Performance Recommendations</h3>
                    <div id="recommendations-list" class="recommendations-list"></div>
                </div>

                <!-- A/B Testing Section -->
                <div class="ab-testing-section">
                    <h3>A/B Testing Results</h3>
                    <div id="ab-testing-results" class="ab-testing-results"></div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.container);
    }

    setupEventListeners() {
        // Dashboard controls
        document.getElementById('refresh-analytics').addEventListener('click', () => {
            this.updateDashboard();
        });
        
        document.getElementById('export-analytics').addEventListener('click', () => {
            this.exportAnalytics();
        });
        
        document.getElementById('clear-analytics').addEventListener('click', () => {
            this.clearAnalytics();
        });
        
        document.getElementById('close-analytics').addEventListener('click', () => {
            this.hide();
        });
    }

    show() {
        this.container.classList.remove('hidden');
        this.isVisible = true;
        this.startRealTimeUpdates();
        this.updateDashboard();
    }

    hide() {
        this.container.classList.add('hidden');
        this.isVisible = false;
        this.stopRealTimeUpdates();
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    startRealTimeUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(() => {
            if (this.isVisible) {
                this.updateDashboard();
            }
        }, 5000); // Update every 5 seconds
    }

    stopRealTimeUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    updateDashboard() {
        const metrics = this.performanceMonitor.getMetricsSummary();
        
        this.updatePerformanceOverview(metrics);
        this.updateBackupPerformance(metrics);
        this.updateResourceUsage(metrics);
        this.updateErrorAnalysis(metrics);
        this.updateCharts(metrics);
        this.updateRecommendations(metrics);
        this.updateABTestingResults(metrics);
    }

    updatePerformanceOverview(metrics) {
        const pageLoad = metrics.pageLoad;
        
        document.getElementById('page-load-time').textContent = 
            pageLoad.loadComplete ? `${pageLoad.loadComplete.toFixed(2)}ms` : 'N/A';
        
        document.getElementById('fcp-time').textContent = 
            pageLoad.fcp ? `${pageLoad.fcp.toFixed(2)}ms` : 'N/A';
        
        document.getElementById('lcp-time').textContent = 
            pageLoad.lcp ? `${pageLoad.lcp.toFixed(2)}ms` : 'N/A';
        
        document.getElementById('cls-score').textContent = 
            pageLoad.cls ? pageLoad.cls.toFixed(3) : 'N/A';
    }

    updateBackupPerformance(metrics) {
        const backup = metrics.backupPerformance;
        
        if (backup.analysis) {
            document.getElementById('backup-success-rate').textContent = 
                `${(backup.analysis.successRate * 100).toFixed(1)}%`;
            
            document.getElementById('backup-avg-duration').textContent = 
                `${(backup.analysis.avgDuration / 1000).toFixed(2)}s`;
            
            document.getElementById('backup-throughput').textContent = 
                `${this.formatBytes(backup.analysis.avgThroughput)}/s`;
        }
        
        document.getElementById('backup-total').textContent = backup.total.toString();
    }

    updateResourceUsage(metrics) {
        const resource = metrics.resourceUsage;
        
        if (resource.memory) {
            document.getElementById('memory-used').textContent = 
                this.formatBytes(resource.memory.used);
            
            document.getElementById('memory-total').textContent = 
                this.formatBytes(resource.memory.total);
        }
        
        if (resource.connection) {
            document.getElementById('connection-type').textContent = 
                resource.connection.effectiveType || 'Unknown';
            
            document.getElementById('network-speed').textContent = 
                resource.connection.downlink ? `${resource.connection.downlink} Mbps` : 'Unknown';
        }
    }

    updateErrorAnalysis(metrics) {
        const errors = metrics.errors;
        
        document.getElementById('total-errors').textContent = errors.total.toString();
        document.getElementById('recent-errors').textContent = errors.recent.length.toString();
        
        // Update error list
        const errorList = document.getElementById('error-list');
        errorList.innerHTML = '';
        
        errors.recent.slice(0, 5).forEach(error => {
            const errorItem = document.createElement('div');
            errorItem.className = 'error-item';
            errorItem.innerHTML = `
                <div class="error-message">${error.message}</div>
                <div class="error-time">${new Date(error.timestamp).toLocaleTimeString()}</div>
            `;
            errorList.appendChild(errorItem);
        });
    }

    updateCharts(metrics) {
        this.updateBackupPerformanceChart(metrics.backupPerformance.recent);
        this.updateNetworkPerformanceChart(metrics.networkMetrics);
        this.updateHeatmap(metrics.heatmapData);
    }

    updateBackupPerformanceChart(backupData) {
        const canvas = document.getElementById('backup-performance-chart');
        const ctx = canvas.getContext('2d');
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        if (backupData.length === 0) {
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('No backup data available', canvas.width / 2, canvas.height / 2);
            return;
        }
        
        // Draw simple line chart
        const padding = 40;
        const chartWidth = canvas.width - 2 * padding;
        const chartHeight = canvas.height - 2 * padding;
        
        // Find min/max values
        const durations = backupData.map(b => b.duration);
        const minDuration = Math.min(...durations);
        const maxDuration = Math.max(...durations);
        
        // Draw axes
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, canvas.height - padding);
        ctx.lineTo(canvas.width - padding, canvas.height - padding);
        ctx.stroke();
        
        // Draw data points
        ctx.strokeStyle = '#007bff';
        ctx.fillStyle = '#007bff';
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        backupData.forEach((backup, index) => {
            const x = padding + (index / (backupData.length - 1)) * chartWidth;
            const y = canvas.height - padding - ((backup.duration - minDuration) / (maxDuration - minDuration)) * chartHeight;
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
            
            // Draw point
            ctx.fillRect(x - 2, y - 2, 4, 4);
        });
        
        ctx.stroke();
        
        // Add labels
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Backup Duration Trends', canvas.width / 2, 20);
    }

    updateNetworkPerformanceChart(networkData) {
        const canvas = document.getElementById('network-performance-chart');
        const ctx = canvas.getContext('2d');
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        if (!networkData.requests || networkData.requests.length === 0) {
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('No network data available', canvas.width / 2, canvas.height / 2);
            return;
        }
        
        // Draw network request success/failure chart
        const requests = networkData.requests.slice(-20); // Last 20 requests
        const padding = 40;
        const barWidth = (canvas.width - 2 * padding) / requests.length;
        
        // Draw axes
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, canvas.height - padding);
        ctx.lineTo(canvas.width - padding, canvas.height - padding);
        ctx.stroke();
        
        // Draw bars
        requests.forEach((request, index) => {
            const x = padding + index * barWidth;
            const barHeight = (request.duration / 1000) * (canvas.height - 2 * padding) / 5; // Scale to 5 seconds max
            const y = canvas.height - padding - barHeight;
            
            ctx.fillStyle = request.success ? '#28a745' : '#dc3545';
            ctx.fillRect(x, y, barWidth - 2, barHeight);
        });
        
        // Add labels
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Network Request Performance', canvas.width / 2, 20);
    }

    updateHeatmap(heatmapData) {
        const container = document.getElementById('heatmap-container');
        container.innerHTML = '';
        
        if (heatmapData.length === 0) {
            container.innerHTML = '<p>No interaction data available</p>';
            return;
        }
        
        // Create simple heatmap visualization
        const heatmapCanvas = document.createElement('canvas');
        heatmapCanvas.width = 400;
        heatmapCanvas.height = 300;
        container.appendChild(heatmapCanvas);
        
        const ctx = heatmapCanvas.getContext('2d');
        
        // Draw heatmap points
        heatmapData.forEach(point => {
            const x = (point.x / window.innerWidth) * heatmapCanvas.width;
            const y = (point.y / window.innerHeight) * heatmapCanvas.height;
            
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, 20);
            gradient.addColorStop(0, 'rgba(255, 0, 0, 0.8)');
            gradient.addColorStop(1, 'rgba(255, 0, 0, 0)');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, 20, 0, 2 * Math.PI);
            ctx.fill();
        });
    }

    updateRecommendations(metrics) {
        const container = document.getElementById('recommendations-list');
        container.innerHTML = '';
        
        if (metrics.backupPerformance.analysis && metrics.backupPerformance.analysis.recommendations) {
            const recommendations = metrics.backupPerformance.analysis.recommendations;
            
            recommendations.forEach(rec => {
                const recElement = document.createElement('div');
                recElement.className = `recommendation recommendation-${rec.severity}`;
                recElement.innerHTML = `
                    <div class="recommendation-header">
                        <span class="recommendation-type">${rec.type.toUpperCase()}</span>
                        <span class="recommendation-severity">${rec.severity.toUpperCase()}</span>
                    </div>
                    <div class="recommendation-message">${rec.message}</div>
                    <button class="recommendation-action" data-action="${rec.action}">
                        Take Action
                    </button>
                `;
                container.appendChild(recElement);
            });
        } else {
            container.innerHTML = '<p>No recommendations available</p>';
        }
    }

    updateABTestingResults(metrics) {
        const container = document.getElementById('ab-testing-results');
        container.innerHTML = '';
        
        if (metrics.abTests && metrics.abTests.length > 0) {
            // Group A/B test results by test name
            const testResults = {};
            metrics.abTests.forEach(test => {
                if (!testResults[test.testName]) {
                    testResults[test.testName] = {};
                }
                if (!testResults[test.testName][test.variant]) {
                    testResults[test.testName][test.variant] = 0;
                }
                testResults[test.testName][test.variant]++;
            });
            
            Object.entries(testResults).forEach(([testName, variants]) => {
                const testElement = document.createElement('div');
                testElement.className = 'ab-test-result';
                
                let variantHtml = '';
                Object.entries(variants).forEach(([variant, count]) => {
                    variantHtml += `
                        <div class="variant-result">
                            <span class="variant-name">${variant}:</span>
                            <span class="variant-count">${count} events</span>
                        </div>
                    `;
                });
                
                testElement.innerHTML = `
                    <h4>${testName}</h4>
                    ${variantHtml}
                `;
                container.appendChild(testElement);
            });
        } else {
            container.innerHTML = '<p>No A/B test data available</p>';
        }
    }

    exportAnalytics() {
        const data = this.performanceMonitor.exportMetrics();
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    clearAnalytics() {
        if (confirm('Are you sure you want to clear all analytics data?')) {
            this.performanceMonitor.clearMetrics();
            this.updateDashboard();
        }
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnalyticsDashboard;
}