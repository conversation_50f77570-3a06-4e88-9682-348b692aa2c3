import { BillingIntegration } from '../BillingIntegration';
import { BillingMetadata, SubscriptionRequest } from '../../types/modular-pricing';
import Stripe from 'stripe';

// Mock Stripe
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    paymentIntents: {
      create: jest.fn(),
    },
    customers: {
      create: jest.fn(),
      update: jest.fn(),
      list: jest.fn(),
    },
    paymentMethods: {
      attach: jest.fn(),
    },
    subscriptions: {
      create: jest.fn(),
      update: jest.fn(),
      cancel: jest.fn(),
      retrieve: jest.fn(),
    },
    subscriptionItems: {
      update: jest.fn(),
    },
    prices: {
      create: jest.fn(),
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
  }));
});

describe('BillingIntegration', () => {
  let billingIntegration: BillingIntegration;
  let mockStripe: any;

  beforeEach(() => {
    // Set up environment variable
    process.env.STRIPE_SECRET_KEY = 'sk_test_123';
    
    // Create new instance and get the mocked Stripe
    billingIntegration = new BillingIntegration();
    mockStripe = (billingIntegration as any).stripe;
  });

  afterEach(() => {
    jest.clearAllMocks();
    delete process.env.STRIPE_SECRET_KEY;
  });

  describe('constructor', () => {
    it('should throw error if STRIPE_SECRET_KEY is not provided', () => {
      delete process.env.STRIPE_SECRET_KEY;
      expect(() => new BillingIntegration()).toThrow('STRIPE_SECRET_KEY environment variable is required');
    });

    it('should initialize Stripe with correct parameters', () => {
      expect(Stripe).toHaveBeenCalledWith('sk_test_123', {
        apiVersion: '2023-08-16',
      });
    });
  });

  describe('createPaymentIntent', () => {
    const mockMetadata: BillingMetadata = {
      userId: 'user123',
      serviceIds: ['contacts', 'messages'],
      planId: 'contacts_messages',
      planName: 'Contacts + Messages',
    };

    it('should create payment intent successfully', async () => {
      const mockPaymentIntent = {
        id: 'pi_123',
        amount: 249,
        currency: 'usd',
      };

      mockStripe.paymentIntents.create.mockResolvedValue(mockPaymentIntent as any);

      const result = await billingIntegration.createPaymentIntent(249, mockMetadata);

      expect(result).toEqual({
        success: true,
        paymentIntentId: 'pi_123',
      });

      expect(mockStripe.paymentIntents.create).toHaveBeenCalledWith({
        amount: 249,
        currency: 'usd',
        metadata: {
          userId: 'user123',
          serviceIds: JSON.stringify(['contacts', 'messages']),
          planId: 'contacts_messages',
          planName: 'Contacts + Messages',
          type: 'modular_subscription',
        },
        description: 'SafeKeep Contacts + Messages subscription for services: contacts, messages',
        automatic_payment_methods: {
          enabled: true,
        },
      });
    });

    it('should handle payment intent creation error', async () => {
      const error = new Error('Stripe API error');
      mockStripe.paymentIntents.create.mockRejectedValue(error);

      const result = await billingIntegration.createPaymentIntent(249, mockMetadata);

      expect(result).toEqual({
        success: false,
        error: 'Stripe API error',
      });
    });

    it('should handle non-Error exceptions', async () => {
      mockStripe.paymentIntents.create.mockRejectedValue('String error');

      const result = await billingIntegration.createPaymentIntent(249, mockMetadata);

      expect(result).toEqual({
        success: false,
        error: 'Failed to create payment intent',
      });
    });
  });

  describe('processSubscriptionPayment', () => {
    const mockSubscriptionData: SubscriptionRequest = {
      userId: 'user123',
      serviceIds: ['contacts', 'messages'],
      paymentMethodId: 'pm_123',
    };

    it('should process subscription payment successfully with existing customer', async () => {
      const mockCustomer = { id: 'cus_123' };
      const mockSubscription = {
        id: 'sub_123',
        latest_invoice: {
          payment_intent: {
            id: 'pi_123',
          },
        },
      };

      mockStripe.paymentMethods.attach.mockResolvedValue({} as any);
      mockStripe.customers.update.mockResolvedValue({} as any);
      mockStripe.subscriptions.create.mockResolvedValue(mockSubscription as any);

      const subscriptionDataWithCustomer = {
        ...mockSubscriptionData,
        customerId: 'cus_123',
      };

      const result = await billingIntegration.processSubscriptionPayment(subscriptionDataWithCustomer);

      expect(result).toEqual({
        success: true,
        subscriptionId: 'sub_123',
        paymentIntentId: 'pi_123',
      });

      expect(mockStripe.customers.create).not.toHaveBeenCalled();
      expect(mockStripe.paymentMethods.attach).toHaveBeenCalledWith('pm_123', {
        customer: 'cus_123',
      });
    });

    it('should create new customer if not provided', async () => {
      const mockCustomer = { id: 'cus_new' };
      const mockSubscription = {
        id: 'sub_123',
        latest_invoice: {
          payment_intent: {
            id: 'pi_123',
          },
        },
      };

      mockStripe.customers.create.mockResolvedValue(mockCustomer as any);
      mockStripe.paymentMethods.attach.mockResolvedValue({} as any);
      mockStripe.customers.update.mockResolvedValue({} as any);
      mockStripe.subscriptions.create.mockResolvedValue(mockSubscription as any);

      const result = await billingIntegration.processSubscriptionPayment(mockSubscriptionData);

      expect(result).toEqual({
        success: true,
        subscriptionId: 'sub_123',
        paymentIntentId: 'pi_123',
      });

      expect(mockStripe.customers.create).toHaveBeenCalledWith({
        metadata: {
          userId: 'user123',
          serviceIds: JSON.stringify(['contacts', 'messages']),
        },
      });
    });

    it('should handle subscription creation without payment method', async () => {
      const mockCustomer = { id: 'cus_new' };
      const mockSubscription = {
        id: 'sub_123',
        latest_invoice: {
          payment_intent: {
            id: 'pi_123',
          },
        },
      };

      mockStripe.customers.create.mockResolvedValue(mockCustomer as any);
      mockStripe.subscriptions.create.mockResolvedValue(mockSubscription as any);

      const subscriptionDataWithoutPaymentMethod = {
        userId: 'user123',
        serviceIds: ['contacts', 'messages'],
      };

      const result = await billingIntegration.processSubscriptionPayment(subscriptionDataWithoutPaymentMethod);

      expect(result).toEqual({
        success: true,
        subscriptionId: 'sub_123',
        paymentIntentId: 'pi_123',
      });

      expect(mockStripe.paymentMethods.attach).not.toHaveBeenCalled();
      expect(mockStripe.customers.update).not.toHaveBeenCalled();
    });

    it('should handle subscription payment processing error', async () => {
      const error = new Error('Subscription creation failed');
      mockStripe.customers.create.mockRejectedValue(error);

      const result = await billingIntegration.processSubscriptionPayment(mockSubscriptionData);

      expect(result).toEqual({
        success: false,
        error: 'Subscription creation failed',
      });
    });
  });

  describe('updateSubscription', () => {
    it('should update subscription successfully', async () => {
      const mockSubscription = {
        id: 'sub_123',
        metadata: { userId: 'user123' },
        items: {
          data: [{ id: 'si_123' }],
        },
      };

      const mockPrice = { id: 'price_123' };

      mockStripe.subscriptions.retrieve.mockResolvedValue(mockSubscription as any);
      mockStripe.subscriptions.update.mockResolvedValue({} as any);
      mockStripe.prices.create.mockResolvedValue(mockPrice as any);
      mockStripe.subscriptionItems.update.mockResolvedValue({} as any);

      const result = await billingIntegration.updateSubscription('sub_123', ['contacts', 'photos'], 549);

      expect(result).toEqual({
        success: true,
        subscriptionId: 'sub_123',
      });

      expect(mockStripe.subscriptions.update).toHaveBeenCalledWith('sub_123', {
        metadata: {
          userId: 'user123',
          serviceIds: JSON.stringify(['contacts', 'photos']),
          updatedAt: expect.any(String),
        },
      });

      expect(mockStripe.prices.create).toHaveBeenCalledWith({
        currency: 'usd',
        unit_amount: 549,
        recurring: {
          interval: 'month',
        },
        product_data: {
          name: 'SafeKeep Modular Backup Services',
          metadata: {
            services: 'contacts,photos',
          },
        },
      });

      expect(mockStripe.subscriptionItems.update).toHaveBeenCalledWith('si_123', {
        price: 'price_123',
      });
    });

    it('should handle update subscription error', async () => {
      const error = new Error('Update failed');
      mockStripe.subscriptions.retrieve.mockRejectedValue(error);

      const result = await billingIntegration.updateSubscription('sub_123', ['contacts'], 99);

      expect(result).toEqual({
        success: false,
        error: 'Update failed',
      });
    });
  });

  describe('cancelSubscription', () => {
    it('should cancel subscription successfully', async () => {
      mockStripe.subscriptions.cancel.mockResolvedValue({} as any);

      const result = await billingIntegration.cancelSubscription('sub_123');

      expect(result).toEqual({
        success: true,
        subscriptionId: 'sub_123',
      });

      expect(mockStripe.subscriptions.cancel).toHaveBeenCalledWith('sub_123');
    });

    it('should handle cancel subscription error', async () => {
      const error = new Error('Cancel failed');
      mockStripe.subscriptions.cancel.mockRejectedValue(error);

      const result = await billingIntegration.cancelSubscription('sub_123');

      expect(result).toEqual({
        success: false,
        error: 'Cancel failed',
      });
    });
  });

  describe('getCustomerByUserId', () => {
    it('should find customer by user ID', async () => {
      const mockCustomers = {
        data: [
          { id: 'cus_123', metadata: { userId: 'user123' } },
          { id: 'cus_456', metadata: { userId: 'user456' } },
        ],
      };

      mockStripe.customers.list.mockResolvedValue(mockCustomers as any);

      const result = await billingIntegration.getCustomerByUserId('user123');

      expect(result).toEqual({ id: 'cus_123', metadata: { userId: 'user123' } });
    });

    it('should return null if customer not found', async () => {
      const mockCustomers = {
        data: [
          { id: 'cus_456', metadata: { userId: 'user456' } },
        ],
      };

      mockStripe.customers.list.mockResolvedValue(mockCustomers as any);

      const result = await billingIntegration.getCustomerByUserId('user123');

      expect(result).toBeNull();
    });

    it('should handle customer retrieval error', async () => {
      const error = new Error('API error');
      mockStripe.customers.list.mockRejectedValue(error);

      const result = await billingIntegration.getCustomerByUserId('user123');

      expect(result).toBeNull();
    });
  });

  describe('handleWebhook', () => {
    it('should handle invoice.payment_succeeded event', async () => {
      const mockEvent = {
        type: 'invoice.payment_succeeded',
        data: {
          object: {
            id: 'in_123',
            subscription: 'sub_123',
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'subscription_activated',
      });
    });

    it('should handle invoice.payment_failed event', async () => {
      const mockEvent = {
        type: 'invoice.payment_failed',
        data: {
          object: {
            id: 'in_123',
            subscription: 'sub_123',
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'subscription_payment_failed',
      });
    });

    it('should handle customer.subscription.created event', async () => {
      const mockEvent = {
        type: 'customer.subscription.created',
        data: {
          object: {
            id: 'sub_123',
            metadata: {
              userId: 'user123',
              serviceIds: JSON.stringify(['contacts', 'messages']),
            },
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'subscription_created',
      });
    });

    it('should handle customer.subscription.updated event with active status', async () => {
      const mockEvent = {
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_123',
            status: 'active',
            metadata: {
              userId: 'user123',
              serviceIds: JSON.stringify(['contacts', 'messages']),
            },
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'subscription_activated',
      });
    });

    it('should handle customer.subscription.updated event with past_due status', async () => {
      const mockEvent = {
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_123',
            status: 'past_due',
            metadata: {
              userId: 'user123',
            },
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'subscription_past_due',
      });
    });

    it('should handle customer.subscription.deleted event', async () => {
      const mockEvent = {
        type: 'customer.subscription.deleted',
        data: {
          object: {
            id: 'sub_123',
            metadata: {
              userId: 'user123',
            },
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'subscription_deleted',
      });
    });

    it('should handle payment_intent.succeeded event for modular subscription', async () => {
      const mockEvent = {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_123',
            metadata: {
              type: 'modular_subscription',
              userId: 'user123',
              serviceIds: JSON.stringify(['contacts', 'messages']),
            },
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'modular_payment_succeeded',
      });
    });

    it('should handle payment_intent.payment_failed event for modular subscription', async () => {
      const mockEvent = {
        type: 'payment_intent.payment_failed',
        data: {
          object: {
            id: 'pi_123',
            metadata: {
              type: 'modular_subscription',
              userId: 'user123',
              serviceIds: JSON.stringify(['contacts', 'messages']),
            },
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'modular_payment_failed',
      });
    });

    it('should handle unhandled webhook event types', async () => {
      const mockEvent = {
        type: 'some.unknown.event',
        data: {
          object: {},
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'ignored',
      });
    });

    it('should handle webhook processing errors', async () => {
      const mockEvent = {
        type: 'invoice.payment_succeeded',
        data: {
          object: null, // This will cause an error
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result.processed).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle events without metadata gracefully', async () => {
      const mockEvent = {
        type: 'customer.subscription.created',
        data: {
          object: {
            id: 'sub_123',
            metadata: {},
          },
        },
      };

      const result = await billingIntegration.handleWebhook(mockEvent as any);

      expect(result).toEqual({
        processed: true,
        action: 'subscription_created_no_metadata',
      });
    });
  });

  describe('verifyWebhookSignature', () => {
    it('should verify webhook signature successfully', () => {
      const mockEvent = { type: 'test.event' };
      mockStripe.webhooks.constructEvent.mockReturnValue(mockEvent as any);

      const result = billingIntegration.verifyWebhookSignature(
        'payload',
        'signature',
        'endpoint_secret'
      );

      expect(result).toEqual(mockEvent);
      expect(mockStripe.webhooks.constructEvent).toHaveBeenCalledWith(
        'payload',
        'signature',
        'endpoint_secret'
      );
    });

    it('should throw error for invalid webhook signature', () => {
      const error = new Error('Invalid signature');
      mockStripe.webhooks.constructEvent.mockImplementation(() => {
        throw error;
      });

      expect(() => {
        billingIntegration.verifyWebhookSignature(
          'payload',
          'invalid_signature',
          'endpoint_secret'
        );
      }).toThrow('Invalid webhook signature');
    });
  });
});