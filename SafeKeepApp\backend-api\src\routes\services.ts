import { Router } from 'express';
import { ServiceController } from '../controllers/ServiceController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const serviceController = new ServiceController();

/**
 * Service routes for modular pricing system
 * All routes require authentication
 */

// GET /api/services/user/:userId - Returns user's active services
router.get('/user/:userId', authenticateToken, (req, res) => {
  serviceController.getUserServices(req, res);
});

// POST /api/services/validate - Validates service combination
router.post('/validate', authenticateToken, (req, res) => {
  serviceController.validateServiceCombination(req, res);
});

// GET /api/services/access/:userId/:serviceType - Checks if user has access to specific service
router.get('/access/:userId/:serviceType', authenticateToken, (req, res) => {
  serviceController.checkServiceAccess(req, res);
});

export default router;