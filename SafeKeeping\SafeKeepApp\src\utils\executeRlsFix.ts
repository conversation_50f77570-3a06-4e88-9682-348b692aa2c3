import { supabase, handleSupabaseError } from './supabaseClient';

/**
 * Executes the RLS policy fix directly without requiring the SQL file to be uploaded to storage
 * This addresses the syntax error with CREATE POLICY IF NOT EXISTS
 */
export async function executeRlsFix(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Starting direct RLS policy fix...');
    
    // SQL script to fix RLS policies
    const sqlScript = `
    -- Function to safely create policies by dropping them first
    CREATE OR REPLACE FUNCTION safe_create_policy(
      policy_name TEXT,
      table_name TEXT,
      command TEXT,
      using_expr TEXT DEFAULT NULL,
      check_expr TEXT DEFAULT NULL
    ) RETURNS VOID AS $$
    BEGIN
      -- Drop the policy if it exists
      EXECUTE format('DROP POLICY IF EXISTS %I ON %I', policy_name, table_name);
      
      -- Create the policy with appropriate parameters
      IF command = 'ALL' THEN
        IF using_expr IS NOT NULL AND check_expr IS NOT NULL THEN
          EXECUTE format('CREATE POLICY %I ON %I FOR %s USING (%s) WITH CHECK (%s)', 
                        policy_name, table_name, command, using_expr, check_expr);
        E<PERSON>IF using_expr IS NOT NULL THEN
          EXECUTE format('CREATE POLICY %I ON %I FOR %s USING (%s)', 
                        policy_name, table_name, command, using_expr);
        ELSE
          RAISE EXCEPTION 'Invalid policy parameters for ALL command';
        END IF;
      ELSIF command IN ('SELECT', 'DELETE') THEN
        IF using_expr IS NOT NULL THEN
          EXECUTE format('CREATE POLICY %I ON %I FOR %s USING (%s)', 
                        policy_name, table_name, command, using_expr);
        ELSE
          RAISE EXCEPTION 'USING expression is required for % command', command;
        END IF;
      ELSIF command IN ('INSERT') THEN
        IF check_expr IS NOT NULL THEN
          EXECUTE format('CREATE POLICY %I ON %I FOR %s WITH CHECK (%s)', 
                        policy_name, table_name, command, check_expr);
        ELSE
          RAISE EXCEPTION 'WITH CHECK expression is required for INSERT command';
        END IF;
      ELSIF command IN ('UPDATE') THEN
        IF using_expr IS NOT NULL AND check_expr IS NOT NULL THEN
          EXECUTE format('CREATE POLICY %I ON %I FOR %s USING (%s) WITH CHECK (%s)', 
                        policy_name, table_name, command, using_expr, check_expr);
        ELSIF using_expr IS NOT NULL THEN
          EXECUTE format('CREATE POLICY %I ON %I FOR %s USING (%s)', 
                        policy_name, table_name, command, using_expr);
        ELSE
          RAISE EXCEPTION 'USING expression is required for UPDATE command';
        END IF;
      ELSE
        RAISE EXCEPTION 'Unsupported command: %', command;
      END IF;
      
      RAISE NOTICE 'Successfully created policy % on table %', policy_name, table_name;
    END;
    $$ LANGUAGE plpgsql;

    -- Enable RLS on all tables
    DO $$ 
    BEGIN
      EXECUTE 'ALTER TABLE public.users ENABLE ROW LEVEL SECURITY';
      EXECUTE 'ALTER TABLE public.file_metadata ENABLE ROW LEVEL SECURITY';
      EXECUTE 'ALTER TABLE public.backup_sessions ENABLE ROW LEVEL SECURITY';
      EXECUTE 'ALTER TABLE public.storage_usage ENABLE ROW LEVEL SECURITY';
      
      -- Check if sync_status table exists before enabling RLS
      IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'sync_status') THEN
        EXECUTE 'ALTER TABLE public.sync_status ENABLE ROW LEVEL SECURITY';
      END IF;
    END $$;

    -- Create policies for users table
    SELECT safe_create_policy('Users can view own profile', 'users', 'SELECT', 'id = auth.uid()');
    SELECT safe_create_policy('Users can update own profile', 'users', 'UPDATE', 'id = auth.uid()');
    SELECT safe_create_policy('Users can insert own profile', 'users', 'INSERT', NULL, 'id = auth.uid()');
    SELECT safe_create_policy('Users can delete own profile', 'users', 'DELETE', 'id = auth.uid()');

    -- Create policies for file_metadata table
    SELECT safe_create_policy('Users can view own files', 'file_metadata', 'SELECT', 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can insert own files', 'file_metadata', 'INSERT', NULL, 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can update own files', 'file_metadata', 'UPDATE', 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can delete own files', 'file_metadata', 'DELETE', 'user_id = auth.uid()');

    -- Create policies for backup_sessions table
    SELECT safe_create_policy('Users can view own backup sessions', 'backup_sessions', 'SELECT', 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can insert own backup sessions', 'backup_sessions', 'INSERT', NULL, 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can update own backup sessions', 'backup_sessions', 'UPDATE', 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can delete own backup sessions', 'backup_sessions', 'DELETE', 'user_id = auth.uid()');

    -- Create policies for storage_usage table
    SELECT safe_create_policy('Users can view own storage usage', 'storage_usage', 'SELECT', 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can insert own storage usage', 'storage_usage', 'INSERT', NULL, 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can update own storage usage', 'storage_usage', 'UPDATE', 'user_id = auth.uid()');
    SELECT safe_create_policy('Users can delete own storage usage', 'storage_usage', 'DELETE', 'user_id = auth.uid()');

    -- Create policies for sync_status table if it exists
    DO $$ 
    BEGIN
      IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'sync_status') THEN
        PERFORM safe_create_policy('Users can view own sync status', 'sync_status', 'SELECT', 'user_id = auth.uid()');
        PERFORM safe_create_policy('Users can insert own sync status', 'sync_status', 'INSERT', NULL, 'user_id = auth.uid()');
        PERFORM safe_create_policy('Users can update own sync status', 'sync_status', 'UPDATE', 'user_id = auth.uid()');
        PERFORM safe_create_policy('Users can delete own sync status', 'sync_status', 'DELETE', 'user_id = auth.uid()');
      END IF;
    END $$;

    -- Create policies for user_storage_summary view if it exists
    DO $$ 
    BEGIN
      IF EXISTS (SELECT FROM pg_views WHERE schemaname = 'public' AND viewname = 'user_storage_summary') THEN
        PERFORM safe_create_policy('Users can view own storage summary', 'user_storage_summary', 'SELECT', 'id = auth.uid()');
      END IF;
    END $$;
    `;
    
    // Create exec_sql function if it doesn't exist
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT)
      RETURNS VOID AS $$
      BEGIN
        EXECUTE sql_query;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      -- Grant execute permission to authenticated users
      GRANT EXECUTE ON FUNCTION exec_sql(TEXT) TO authenticated;
      
      COMMENT ON FUNCTION exec_sql(TEXT) IS 'Executes SQL queries with elevated privileges';
    `;
    
    // First try to create the exec_sql function
    try {
      await supabase.rpc('exec_sql', { sql_query: createFunctionSql });
    } catch (error) {
      // If the function doesn't exist yet, we need to create it using a direct query
      // This requires admin privileges
      console.log('Creating exec_sql function using direct query...');
      
      const { error: directError } = await supabase.from('_rpc').select('*').rpc('exec_sql', { 
        sql_query: createFunctionSql 
      });
      
      if (directError) {
        console.error('Error creating exec_sql function:', directError);
        return { 
          success: false, 
          message: `Failed to create exec_sql function. Please run the SQL script manually using database admin privileges.` 
        };
      }
    }
    
    // Now execute the main fix script
    const { error: execError } = await supabase.rpc('exec_sql', { 
      sql_query: sqlScript 
    });
    
    if (execError) {
      console.error('Error executing SQL script:', execError);
      return { 
        success: false, 
        message: `Failed to execute SQL script: ${handleSupabaseError(execError, 'executeRlsFix')}` 
      };
    }
    
    console.log('RLS policy fix completed successfully');
    return { 
      success: true, 
      message: 'RLS policies have been fixed successfully' 
    };
  } catch (error) {
    console.error('Unexpected error fixing RLS policies:', error);
    return { 
      success: false, 
      message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}