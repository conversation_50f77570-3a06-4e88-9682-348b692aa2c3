"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceValidator = void 0;
class ServiceValidator {
    async validateServiceCombination(serviceIds) {
        return {
            isValid: true,
            errors: [],
            warnings: []
        };
    }
    async checkServiceAccess(userId, serviceType) {
        return {
            hasAccess: false
        };
    }
    async getUserServices(userId) {
        return [];
    }
}
exports.ServiceValidator = ServiceValidator;
//# sourceMappingURL=ServiceValidator.js.map