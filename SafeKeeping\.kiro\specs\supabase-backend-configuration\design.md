# Design Document

## Overview

This design outlines the complete Supabase backend configuration for the SafeKeep application. The backend will provide secure user authentication, encrypted data storage, subscription management, and real-time synchronization capabilities. The architecture follows a zero-knowledge approach where the backend stores encrypted data but cannot decrypt user content.

The design leverages Supabase's integrated suite of services including PostgreSQL database, authentication, storage buckets, and real-time subscriptions to create a scalable and secure cloud backend for the React Native application.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "React Native App"
        A[SafeKeep Mobile App]
        B[Authentication Service]
        C[Cloud Storage Service]
        D[Encryption Service]
    end
    
    subgraph "Supabase Backend"
        E[Supabase Auth]
        F[PostgreSQL Database]
        G[Storage Buckets]
        H[Real-time Engine]
        I[Edge Functions]
    end
    
    subgraph "External Services"
        J[Stripe Payments]
        K[Email Service]
    end
    
    A --> B
    A --> C
    A --> D
    B --> E
    C --> F
    C --> G
    F --> H
    E --> K
    A --> J
    I --> J
```

### Database Schema Architecture

The database follows a user-centric design with row-level security (RLS) policies ensuring users can only access their own data:

- **Users Table**: Core user profiles with authentication and subscription data
- **File Metadata Table**: Encrypted file information and storage references
- **Backup Sessions Table**: Tracking backup operations and progress
- **Storage Usage Table**: Real-time storage quota tracking by category
- **Sync Status Table**: Device synchronization state management

### Security Architecture

- **Zero-Knowledge Design**: All user data encrypted client-side before upload
- **Row-Level Security**: Database policies prevent cross-user data access
- **JWT Authentication**: Secure session management with automatic refresh
- **Encrypted Storage**: All files stored as encrypted blobs in storage buckets
- **API Security**: All endpoints protected by authentication middleware

## Components and Interfaces

### 1. Database Configuration

#### Core Tables Schema

**Users Table**
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT auth.uid(),
  email TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_login_at TIMESTAMPTZ DEFAULT NOW(),
  storage_used BIGINT DEFAULT 0,
  storage_quota BIGINT DEFAULT 5368709120, -- 5GB default
  encryption_key_id TEXT,
  subscription_tier TEXT DEFAULT 'basic',
  subscription_status TEXT DEFAULT 'active',
  subscription_expires_at TIMESTAMPTZ,
  backup_settings JSONB DEFAULT '{"auto_backup": true, "wifi_only": true, "frequency": "daily"}'::jsonb
);
```

**File Metadata Table**
```sql
CREATE TABLE file_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_name TEXT NOT NULL,
  encrypted_name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT NOT NULL,
  encrypted_size BIGINT NOT NULL,
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  last_modified TIMESTAMPTZ DEFAULT NOW(),
  category TEXT CHECK (category IN ('photo', 'contact', 'message')),
  hash TEXT NOT NULL,
  encryption_iv TEXT NOT NULL,
  encryption_salt TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  is_backed_up BOOLEAN DEFAULT true,
  device_id TEXT,
  sync_status TEXT DEFAULT 'synced'
);
```

**Backup Sessions Table**
```sql
CREATE TABLE backup_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_type TEXT CHECK (session_type IN ('manual', 'automatic')),
  status TEXT CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  total_files INTEGER DEFAULT 0,
  processed_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  total_bytes BIGINT DEFAULT 0,
  processed_bytes BIGINT DEFAULT 0,
  error_message TEXT,
  device_id TEXT
);
```

**Storage Usage Table**
```sql
CREATE TABLE storage_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  category TEXT CHECK (category IN ('photo', 'contact', 'message', 'other')),
  bytes_used BIGINT DEFAULT 0,
  file_count INTEGER DEFAULT 0,
  last_updated TIMESTAMPTZ DEFAULT NOW()
);
```

#### Row-Level Security Policies

```sql
-- Users can only access their own data
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- File metadata policies
ALTER TABLE file_metadata ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage own files" ON file_metadata FOR ALL USING (auth.uid() = user_id);

-- Backup session policies
ALTER TABLE backup_sessions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage own backup sessions" ON backup_sessions FOR ALL USING (auth.uid() = user_id);

-- Storage usage policies
ALTER TABLE storage_usage ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own storage usage" ON storage_usage FOR SELECT USING (auth.uid() = user_id);
```

### 2. Storage Bucket Configuration

#### Bucket Structure
```
user-data/
├── {user_id}/
│   ├── photos/
│   │   ├── encrypted_photo_files
│   │   └── thumbnails/
│   ├── contacts/
│   │   └── encrypted_contact_files
│   ├── messages/
│   │   └── encrypted_message_files
│   └── metadata/
│       └── backup_manifests
```

#### Storage Policies
```sql
-- User data bucket policies
CREATE POLICY "Users can upload own files" ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'user-data' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view own files" ON storage.objects FOR SELECT 
USING (bucket_id = 'user-data' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own files" ON storage.objects FOR DELETE 
USING (bucket_id = 'user-data' AND auth.uid()::text = (storage.foldername(name))[1]);
```

### 3. Authentication Configuration

#### Auth Settings
```javascript
const authConfig = {
  autoRefreshToken: true,
  persistSession: true,
  detectSessionInUrl: false,
  flowType: 'pkce',
  storage: AsyncStorage,
  storageKey: 'safekeep-auth-token',
  debug: __DEV__
};
```

#### Email Templates
- Welcome email with account verification
- Password reset with secure token
- Subscription confirmation and updates
- Backup completion notifications

### 4. Real-time Subscriptions

#### Backup Progress Tracking
```javascript
const backupProgressSubscription = supabase
  .channel('backup-progress')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'backup_sessions',
    filter: `user_id=eq.${userId}`
  }, handleBackupProgress);
```

#### Storage Usage Updates
```javascript
const storageUsageSubscription = supabase
  .channel('storage-usage')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'storage_usage',
    filter: `user_id=eq.${userId}`
  }, handleStorageUpdate);
```

### 5. Edge Functions

#### Subscription Management Function
```javascript
// Handle Stripe webhook events
export const handleStripeWebhook = async (req) => {
  const signature = req.headers['stripe-signature'];
  const event = stripe.webhooks.constructEvent(req.body, signature, webhookSecret);
  
  switch (event.type) {
    case 'customer.subscription.created':
    case 'customer.subscription.updated':
      await updateUserSubscription(event.data.object);
      break;
    case 'customer.subscription.deleted':
      await handleSubscriptionCancellation(event.data.object);
      break;
  }
};
```

#### Storage Cleanup Function
```javascript
// Automated cleanup of orphaned files
export const cleanupOrphanedFiles = async () => {
  const orphanedFiles = await supabase
    .from('file_metadata')
    .select('storage_path')
    .is('user_id', null);
    
  for (const file of orphanedFiles) {
    await supabase.storage
      .from('user-data')
      .remove([file.storage_path]);
  }
};
```

## Data Models

### TypeScript Interfaces

```typescript
export interface Database {
  public: {
    Tables: {
      users: {
        Row: UserProfile;
        Insert: UserProfileInsert;
        Update: UserProfileUpdate;
      };
      file_metadata: {
        Row: FileMetadata;
        Insert: FileMetadataInsert;
        Update: FileMetadataUpdate;
      };
      backup_sessions: {
        Row: BackupSession;
        Insert: BackupSessionInsert;
        Update: BackupSessionUpdate;
      };
      storage_usage: {
        Row: StorageUsage;
        Insert: StorageUsageInsert;
        Update: StorageUsageUpdate;
      };
    };
  };
}

export interface UserProfile {
  id: string;
  email: string;
  display_name: string;
  created_at: string;
  last_login_at: string;
  storage_used: number;
  storage_quota: number;
  encryption_key_id: string | null;
  subscription_tier: 'basic' | 'premium' | 'family';
  subscription_status: 'active' | 'cancelled' | 'expired';
  subscription_expires_at: string | null;
  backup_settings: {
    auto_backup: boolean;
    wifi_only: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
  };
}

export interface FileMetadata {
  id: string;
  user_id: string;
  original_name: string;
  encrypted_name: string;
  mime_type: string;
  size: number;
  encrypted_size: number;
  uploaded_at: string;
  last_modified: string;
  category: 'photo' | 'contact' | 'message';
  hash: string;
  encryption_iv: string;
  encryption_salt: string;
  storage_path: string;
  is_backed_up: boolean;
  device_id: string | null;
  sync_status: 'synced' | 'pending' | 'failed';
}
```

## Error Handling

### Database Error Handling
```typescript
export const handleSupabaseError = (error: any, context: string): string => {
  console.error(`Supabase error in ${context}:`, error);
  
  const errorMappings = {
    'JWT expired': 'Your session has expired. Please sign in again.',
    'Row Level Security': 'Access denied. You can only access your own data.',
    'duplicate key': 'This item already exists.',
    'foreign key': 'Invalid reference. Please try again.',
    'not found': 'The requested item was not found.',
    'storage quota exceeded': 'Storage limit reached. Please upgrade your plan.'
  };
  
  for (const [key, message] of Object.entries(errorMappings)) {
    if (error?.message?.includes(key)) {
      return message;
    }
  }
  
  return error?.message || 'An unexpected error occurred. Please try again.';
};
```

### Network Error Handling
```typescript
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      const backoffDelay = delay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, backoffDelay));
    }
  }
  throw new Error('Max retries exceeded');
};
```

## Testing Strategy

### Unit Testing
- Database schema validation
- RLS policy enforcement
- Authentication flow testing
- Storage bucket access control
- Error handling scenarios

### Integration Testing
- End-to-end backup and restore flows
- Subscription management integration
- Real-time synchronization testing
- Cross-device data consistency
- Performance under load

### Security Testing
- Authentication bypass attempts
- Data access control validation
- Encryption key management
- SQL injection prevention
- File upload security

### Test Data Management
```sql
-- Test user creation
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at)
VALUES ('test-user-id', '<EMAIL>', NOW(), NOW(), NOW());

-- Test data cleanup
CREATE OR REPLACE FUNCTION cleanup_test_data()
RETURNS void AS $$
BEGIN
  DELETE FROM file_metadata WHERE user_id LIKE 'test-%';
  DELETE FROM backup_sessions WHERE user_id LIKE 'test-%';
  DELETE FROM storage_usage WHERE user_id LIKE 'test-%';
  DELETE FROM users WHERE id LIKE 'test-%';
END;
$$ LANGUAGE plpgsql;
```

### Performance Monitoring
- Database query performance tracking
- Storage bucket access latency
- Real-time subscription performance
- Memory usage optimization
- Network bandwidth monitoring