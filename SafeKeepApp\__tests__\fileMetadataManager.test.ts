import { supabase } from '../src/config/supabase';
import * as fileMetadataManager from '../src/utils/fileMetadataManager';
import * as supabaseHelpers from '../src/utils/supabaseHelpers';

// Mock the supabase client
jest.mock('../src/config/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    group: jest.fn().mockReturnThis(),
    rpc: jest.fn().mockReturnThis(),
  }
}));

// Mock the getCurrentUserId function
jest.mock('../src/utils/supabaseHelpers', () => ({
  getCurrentUserId: jest.fn(),
  handleSupabaseError: jest.fn(error => error.message || 'Unknown error')
}));

describe('File Metadata Manager', () => {
  const mockUserId = 'test-user-id';
  const mockFileId = 'test-file-id';
  const mockFileHash = 'test-file-hash';
  
  beforeEach(() => {
    jest.clearAllMocks();
    (supabaseHelpers.getCurrentUserId as jest.Mock).mockResolvedValue(mockUserId);
  });

  describe('createFileMetadata', () => {
    it('should create file metadata and check for duplicates', async () => {
      // Mock checkFileExists to return no duplicates
      jest.spyOn(fileMetadataManager, 'checkFileExists').mockResolvedValue({
        exists: false
      });

      // Mock supabase response
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.insert as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.single as jest.Mock).mockResolvedValue({
        data: { id: mockFileId },
        error: null
      });

      const mockMetadata = {
        user_id: mockUserId,
        original_name: 'test-file.jpg',
        encrypted_name: 'encrypted-test-file.enc',
        mime_type: 'image/jpeg',
        size: 1024,
        encrypted_size: 1500,
        category: 'photo' as const,
        hash: mockFileHash,
        encryption_iv: 'test-iv',
        encryption_salt: 'test-salt',
        storage_path: `${mockUserId}/photos/encrypted-test-file.enc`
      };

      const result = await fileMetadataManager.createFileMetadata(mockMetadata);

      expect(result).toEqual({
        id: mockFileId,
        error: null,
        isDuplicate: false
      });
      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(supabase.insert).toHaveBeenCalledWith(mockMetadata);
    });

    it('should return existing metadata if file is a duplicate', async () => {
      const existingMetadata = {
        id: 'existing-file-id',
        user_id: mockUserId,
        original_name: 'existing-file.jpg',
        encrypted_name: 'encrypted-existing-file.enc',
        mime_type: 'image/jpeg',
        size: 1024,
        encrypted_size: 1500,
        category: 'photo' as const,
        hash: mockFileHash,
        encryption_iv: 'test-iv',
        encryption_salt: 'test-salt',
        storage_path: `${mockUserId}/photos/encrypted-existing-file.enc`
      };

      // Mock checkFileExists to return a duplicate
      jest.spyOn(fileMetadataManager, 'checkFileExists').mockResolvedValue({
        exists: true,
        metadata: existingMetadata
      });

      const mockMetadata = {
        user_id: mockUserId,
        original_name: 'test-file.jpg',
        encrypted_name: 'encrypted-test-file.enc',
        mime_type: 'image/jpeg',
        size: 1024,
        encrypted_size: 1500,
        category: 'photo' as const,
        hash: mockFileHash,
        encryption_iv: 'test-iv',
        encryption_salt: 'test-salt',
        storage_path: `${mockUserId}/photos/encrypted-test-file.enc`
      };

      const result = await fileMetadataManager.createFileMetadata(mockMetadata);

      expect(result).toEqual({
        id: 'existing-file-id',
        error: null,
        isDuplicate: true,
        existingMetadata: existingMetadata
      });
      // Verify that insert was not called
      expect(supabase.insert).not.toHaveBeenCalled();
    });
  });

  describe('searchFiles', () => {
    it('should search files with filters and return results with count', async () => {
      const mockFiles = [
        { id: 'file1', original_name: 'test1.jpg' },
        { id: 'file2', original_name: 'test2.jpg' }
      ];

      // Mock count query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.count as jest.Mock).mockResolvedValueOnce({
        count: 2,
        error: null
      });

      // Mock main query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.order as jest.Mock).mockReturnThis();
      (supabase.limit as jest.Mock).mockReturnThis();
      (supabase.range as jest.Mock).mockResolvedValueOnce({
        data: mockFiles,
        error: null
      });

      const searchParams = {
        category: 'photo' as const,
        limit: 10,
        offset: 0,
        sortBy: 'uploaded_at' as const,
        sortDirection: 'desc' as const
      };

      const result = await fileMetadataManager.searchFiles(searchParams);

      expect(result).toEqual({
        data: mockFiles,
        totalCount: 2,
        error: null
      });
      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(supabase.eq).toHaveBeenCalledWith('user_id', mockUserId);
      expect(supabase.eq).toHaveBeenCalledWith('category', 'photo');
      expect(supabase.order).toHaveBeenCalledWith('uploaded_at', { ascending: false });
      expect(supabase.limit).toHaveBeenCalledWith(10);
    });
  });

  describe('findDuplicateFiles', () => {
    it('should find and return duplicate files', async () => {
      const mockDuplicates = [
        {
          hash: 'hash1',
          files: [
            { id: 'file1', original_name: 'test1.jpg', size: 1000 },
            { id: 'file2', original_name: 'test2.jpg', size: 1000 }
          ]
        }
      ];

      // Mock RPC call
      (supabase.rpc as jest.Mock).mockResolvedValueOnce({
        data: mockDuplicates,
        error: null
      });

      const result = await fileMetadataManager.findDuplicateFiles();

      expect(result).toEqual({
        duplicates: mockDuplicates,
        error: null
      });
      expect(supabase.rpc).toHaveBeenCalledWith('find_duplicate_files', {
        user_id_param: mockUserId
      });
    });

    it('should fall back to client-side implementation if RPC fails', async () => {
      const mockFiles = [
        { id: 'file1', hash: 'hash1', size: 1000 },
        { id: 'file2', hash: 'hash1', size: 1000 },
        { id: 'file3', hash: 'hash2', size: 2000 }
      ];

      // Mock RPC failure
      (supabase.rpc as jest.Mock).mockResolvedValueOnce({
        data: null,
        error: { message: 'Function not found' }
      });

      // Mock fallback query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockResolvedValueOnce({
        data: mockFiles,
        error: null
      });

      const result = await fileMetadataManager.findDuplicateFiles();

      expect(result.duplicates).toHaveLength(1); // Only one hash has duplicates
      expect(result.duplicates[0].hash).toBe('hash1');
      expect(result.duplicates[0].files).toHaveLength(2);
      expect(result.error).toBeNull();
    });
  });

  // Add more tests for other functions as needed
});