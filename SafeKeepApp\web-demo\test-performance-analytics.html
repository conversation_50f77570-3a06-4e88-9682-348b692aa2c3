<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Analytics Test - SafeKeep Demo</title>
    
    <!-- Performance Analytics Styles -->
    <link rel="stylesheet" href="analytics-dashboard-styles.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .test-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .demo-area {
            background: white;
            padding: 20px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #ddd;
        }
        
        .progress-demo {
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .form-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        .metrics-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .log-area {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        
        .performance-toggle-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 9999;
            font-size: 14px;
        }
        
        .performance-toggle-btn:hover {
            background: #0056b3;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                text-align: center;
            }
            
            .form-demo {
                grid-template-columns: 1fr;
            }
            
            .metrics-display {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Performance Analytics Test</h1>
            <p>Comprehensive testing environment for SafeKeep performance monitoring system</p>
        </div>

        <!-- Performance Monitoring Section -->
        <div class="test-section">
            <h2>📊 Performance Monitoring</h2>
            <p>Test the core performance monitoring capabilities including Web Vitals, resource usage, and error tracking.</p>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="triggerPerformanceTest()">Run Performance Test</button>
                <button class="btn btn-secondary" onclick="simulateSlowOperation()">Simulate Slow Operation</button>
                <button class="btn btn-warning" onclick="triggerMemoryTest()">Memory Usage Test</button>
                <button class="btn btn-danger" onclick="triggerError()">Trigger Test Error</button>
            </div>
            
            <div class="metrics-display" id="performance-metrics">
                <div class="metric-card">
                    <div class="metric-value" id="page-load-metric">-</div>
                    <div class="metric-label">Page Load Time (ms)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-metric">-</div>
                    <div class="metric-label">Memory Usage (MB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="error-metric">0</div>
                    <div class="metric-label">Error Count</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="interaction-metric">0</div>
                    <div class="metric-label">User Interactions</div>
                </div>
            </div>
        </div>

        <!-- Backup Performance Section -->
        <div class="test-section">
            <h2>💾 Backup Performance Testing</h2>
            <p>Simulate backup operations to test performance tracking and analytics.</p>
            
            <div class="button-group">
                <button class="btn btn-success backup-btn" onclick="simulateBackup('contacts')">Backup Contacts</button>
                <button class="btn btn-success backup-btn" onclick="simulateBackup('messages')">Backup Messages</button>
                <button class="btn btn-success backup-btn" onclick="simulateBackup('photos')">Backup Photos</button>
                <button class="btn btn-primary" onclick="simulateBackup('full')">Full Backup</button>
            </div>
            
            <div class="demo-area">
                <div class="progress-demo">
                    <h4>Backup Progress</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" id="backup-progress"></div>
                    </div>
                    <div id="backup-status">Ready to start backup...</div>
                </div>
            </div>
        </div>

        <!-- User Interaction Tracking Section -->
        <div class="test-section">
            <h2>🖱️ User Interaction Tracking</h2>
            <p>Test heatmap generation and user interaction analytics.</p>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="generateRandomClicks()">Generate Random Clicks</button>
                <button class="btn btn-secondary" onclick="simulateFormInteraction()">Simulate Form Usage</button>
                <button class="btn btn-warning" onclick="triggerScrollTest()">Scroll Behavior Test</button>
                <button class="btn btn-success" onclick="showHeatmapData()">Show Heatmap Data</button>
            </div>
            
            <div class="demo-area">
                <div class="form-demo">
                    <div class="form-group">
                        <label for="test-input">Test Input Field</label>
                        <input type="text" id="test-input" placeholder="Type something...">
                    </div>
                    <div class="form-group">
                        <label for="test-select">Test Select</label>
                        <select id="test-select">
                            <option value="">Choose option...</option>
                            <option value="option1">Option 1</option>
                            <option value="option2">Option 2</option>
                            <option value="option3">Option 3</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="test-textarea">Test Textarea</label>
                        <textarea id="test-textarea" rows="3" placeholder="Enter text..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- A/B Testing Section -->
        <div class="test-section">
            <h2>🧪 A/B Testing Framework</h2>
            <p>Test the A/B testing framework with different variants and conversion tracking.</p>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="showABTestVariants()">Show Current Variants</button>
                <button class="btn btn-secondary" onclick="trackConversion()">Track Conversion</button>
                <button class="btn btn-warning" onclick="resetABTests()">Reset A/B Tests</button>
                <button class="btn btn-success" onclick="showABTestResults()">Show Test Results</button>
            </div>
            
            <div class="demo-area" id="ab-test-demo">
                <p>A/B test variants will be applied automatically. Check the console for variant assignments.</p>
            </div>
        </div>

        <!-- Performance Optimization Section -->
        <div class="test-section">
            <h2>⚡ Performance Optimization</h2>
            <p>Generate performance recommendations and optimization suggestions.</p>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="runPerformanceAnalysis()">Analyze Performance</button>
                <button class="btn btn-secondary" onclick="simulatePerformanceIssues()">Simulate Issues</button>
                <button class="btn btn-success" onclick="showRecommendations()">Show Recommendations</button>
                <button class="btn btn-warning" onclick="exportAnalyticsData()">Export Data</button>
            </div>
            
            <div class="demo-area">
                <div id="recommendations-area">
                    <p>Run performance analysis to see optimization recommendations.</p>
                </div>
            </div>
        </div>

        <!-- Real-time Analytics Section -->
        <div class="test-section">
            <h2>📈 Real-time Analytics Dashboard</h2>
            <p>Access the comprehensive analytics dashboard with real-time updates.</p>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="showAnalyticsDashboard()">Open Analytics Dashboard</button>
                <button class="btn btn-secondary" onclick="toggleRealTimeUpdates()">Toggle Real-time Updates</button>
                <button class="btn btn-success" onclick="generateSampleData()">Generate Sample Data</button>
                <button class="btn btn-warning" onclick="clearAllData()">Clear All Data</button>
            </div>
        </div>

        <!-- System Status Section -->
        <div class="test-section">
            <h2>🔍 System Status</h2>
            <p>Monitor the current status of the performance analytics system.</p>
            
            <div class="demo-area">
                <div id="system-status">
                    <p><span class="status-indicator status-good"></span>Performance Monitor: <span id="monitor-status">Initializing...</span></p>
                    <p><span class="status-indicator status-good"></span>Heatmap Tracker: <span id="heatmap-status">Initializing...</span></p>
                    <p><span class="status-indicator status-good"></span>A/B Testing: <span id="abtest-status">Initializing...</span></p>
                    <p><span class="status-indicator status-good"></span>Analytics Dashboard: <span id="dashboard-status">Initializing...</span></p>
                </div>
            </div>
            
            <div class="log-area" id="system-log">
                <div>System initializing...</div>
            </div>
        </div>
    </div>

    <!-- Load Performance Analytics Components -->
    <script src="performance-monitor.js"></script>
    <script src="analytics-dashboard.js"></script>
    <script src="heatmap-tracker.js"></script>
    <script src="ab-testing-framework.js"></script>
    <script src="performance-optimizer.js"></script>
    <script src="performance-analytics-integration.js"></script>
    <script src="test-performance-analytics.js"></script>

    <script>
        // Global variables for testing
        let performanceAnalytics = null;
        let testData = {
            backupCount: 0,
            errorCount: 0,
            interactionCount: 0
        };

        // Initialize system when page loads
        document.addEventListener('DOMContentLoaded', async () => {
            log('Page loaded, initializing performance analytics...');
            
            try {
                // Wait for the global performance analytics to initialize
                await waitForGlobalInit();
                
                performanceAnalytics = window.performanceAnalytics;
                
                updateSystemStatus();
                startMetricsUpdater();
                
                log('Performance analytics system ready!');
                
            } catch (error) {
                log('Failed to initialize: ' + error.message, 'error');
            }
        });

        async function waitForGlobalInit() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 50;
                
                const checkInit = () => {
                    attempts++;
                    
                    if (window.performanceAnalytics && window.performanceAnalytics.isInitialized) {
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        reject(new Error('Timeout waiting for performance analytics initialization'));
                    } else {
                        setTimeout(checkInit, 100);
                    }
                };
                
                checkInit();
            });
        }

        function updateSystemStatus() {
            if (!performanceAnalytics) return;
            
            document.getElementById('monitor-status').textContent = 
                performanceAnalytics.performanceMonitor ? 'Active' : 'Inactive';
            
            document.getElementById('heatmap-status').textContent = 
                performanceAnalytics.heatmapTracker ? 'Active' : 'Inactive';
            
            document.getElementById('abtest-status').textContent = 
                performanceAnalytics.abTestingFramework ? 'Active' : 'Inactive';
            
            document.getElementById('dashboard-status').textContent = 
                performanceAnalytics.analyticsDashboard ? 'Active' : 'Inactive';
        }

        function startMetricsUpdater() {
            setInterval(() => {
                updateMetricsDisplay();
            }, 2000);
        }

        function updateMetricsDisplay() {
            if (!performanceAnalytics) return;
            
            const metrics = performanceAnalytics.getPerformanceMetrics();
            if (!metrics) return;
            
            // Update page load time
            if (metrics.pageLoad.loadComplete) {
                document.getElementById('page-load-metric').textContent = 
                    Math.round(metrics.pageLoad.loadComplete);
            }
            
            // Update memory usage
            if (metrics.resourceUsage.memory) {
                const memoryMB = Math.round(metrics.resourceUsage.memory.used / 1024 / 1024);
                document.getElementById('memory-metric').textContent = memoryMB;
            }
            
            // Update error count
            document.getElementById('error-metric').textContent = metrics.errors.total;
            
            // Update interaction count
            document.getElementById('interaction-metric').textContent = metrics.userInteractions.total;
        }

        // Test Functions
        function triggerPerformanceTest() {
            log('Running performance test...');
            
            // Simulate CPU-intensive operation
            const start = performance.now();
            let result = 0;
            for (let i = 0; i < 1000000; i++) {
                result += Math.random();
            }
            const duration = performance.now() - start;
            
            log(`Performance test completed in ${duration.toFixed(2)}ms`);
        }

        function simulateSlowOperation() {
            log('Simulating slow operation...');
            
            const progressBar = document.getElementById('backup-progress');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    log('Slow operation completed');
                }
                
                progressBar.style.width = progress + '%';
            }, 200);
        }

        function triggerMemoryTest() {
            log('Running memory usage test...');
            
            // Create large arrays to increase memory usage
            const largeArrays = [];
            for (let i = 0; i < 10; i++) {
                largeArrays.push(new Array(100000).fill(Math.random()));
            }
            
            setTimeout(() => {
                // Clear arrays
                largeArrays.length = 0;
                log('Memory test completed');
            }, 2000);
        }

        function triggerError() {
            log('Triggering test error...');
            testData.errorCount++;
            
            try {
                throw new Error('This is a test error for performance monitoring');
            } catch (error) {
                if (performanceAnalytics && performanceAnalytics.performanceMonitor) {
                    performanceAnalytics.performanceMonitor.recordError(error, {
                        context: 'user_triggered_test',
                        testNumber: testData.errorCount
                    });
                }
                log('Test error recorded');
            }
        }

        function simulateBackup(type) {
            log(`Starting ${type} backup simulation...`);
            testData.backupCount++;
            
            const progressBar = document.getElementById('backup-progress');
            const statusDiv = document.getElementById('backup-status');
            
            let progress = 0;
            statusDiv.textContent = `Backing up ${type}...`;
            
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    statusDiv.textContent = `${type} backup completed successfully!`;
                    log(`${type} backup completed`);
                    
                    // Track A/B test conversion
                    if (performanceAnalytics && performanceAnalytics.abTestingFramework) {
                        performanceAnalytics.abTestingFramework.trackConversion(
                            'backup_button_color', 
                            'backup_completion'
                        );
                    }
                }
                
                progressBar.style.width = progress + '%';
            }, 300);
        }

        function generateRandomClicks() {
            log('Generating random clicks for heatmap testing...');
            
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    const x = Math.random() * window.innerWidth;
                    const y = Math.random() * window.innerHeight;
                    
                    const clickEvent = new MouseEvent('click', {
                        clientX: x,
                        clientY: y,
                        bubbles: true
                    });
                    
                    document.elementFromPoint(x, y)?.dispatchEvent(clickEvent);
                    testData.interactionCount++;
                }, i * 100);
            }
        }

        function simulateFormInteraction() {
            log('Simulating form interactions...');
            
            const inputs = ['test-input', 'test-select', 'test-textarea'];
            
            inputs.forEach((inputId, index) => {
                setTimeout(() => {
                    const element = document.getElementById(inputId);
                    if (element) {
                        element.focus();
                        
                        if (element.type === 'text' || element.tagName === 'TEXTAREA') {
                            element.value = 'Test input ' + Date.now();
                        } else if (element.tagName === 'SELECT') {
                            element.selectedIndex = 1;
                        }
                        
                        setTimeout(() => element.blur(), 500);
                        testData.interactionCount++;
                    }
                }, index * 600);
            });
        }

        function triggerScrollTest() {
            log('Running scroll behavior test...');
            
            const scrollPositions = [0, 200, 400, 600, 800, 0];
            
            scrollPositions.forEach((position, index) => {
                setTimeout(() => {
                    window.scrollTo({ top: position, behavior: 'smooth' });
                }, index * 500);
            });
        }

        function showHeatmapData() {
            if (!performanceAnalytics) {
                alert('Performance analytics not initialized');
                return;
            }
            
            const heatmapData = performanceAnalytics.getHeatmapData();
            log('Heatmap data retrieved: ' + JSON.stringify(heatmapData, null, 2));
            
            alert(`Heatmap Data:\nTotal Interactions: ${heatmapData?.interactions?.length || 0}\nClick Data: ${heatmapData?.clickData?.length || 0}\nScroll Data: ${heatmapData?.scrollData?.length || 0}`);
        }

        function showABTestVariants() {
            if (!performanceAnalytics || !performanceAnalytics.abTestingFramework) {
                alert('A/B testing framework not initialized');
                return;
            }
            
            const framework = performanceAnalytics.abTestingFramework;
            const variants = {
                'backup_button_color': framework.getVariant('backup_button_color'),
                'progress_bar_style': framework.getVariant('progress_bar_style'),
                'dashboard_layout': framework.getVariant('dashboard_layout')
            };
            
            log('Current A/B test variants: ' + JSON.stringify(variants, null, 2));
            alert('Current A/B Test Variants:\n' + JSON.stringify(variants, null, 2));
        }

        function trackConversion() {
            if (!performanceAnalytics || !performanceAnalytics.abTestingFramework) {
                alert('A/B testing framework not initialized');
                return;
            }
            
            performanceAnalytics.abTestingFramework.trackConversion('backup_button_color', 'manual_conversion');
            log('Manual conversion tracked');
        }

        function resetABTests() {
            if (!performanceAnalytics || !performanceAnalytics.abTestingFramework) {
                alert('A/B testing framework not initialized');
                return;
            }
            
            performanceAnalytics.abTestingFramework.resetUserVariants();
            log('A/B test variants reset');
            location.reload(); // Reload to see new variants
        }

        function showABTestResults() {
            if (!performanceAnalytics) {
                alert('Performance analytics not initialized');
                return;
            }
            
            const results = performanceAnalytics.getABTestResults();
            log('A/B test results: ' + JSON.stringify(results, null, 2));
            
            let summary = 'A/B Test Results:\n\n';
            Object.entries(results).forEach(([testName, result]) => {
                summary += `${testName}:\n`;
                Object.entries(result.variants).forEach(([variant, stats]) => {
                    summary += `  ${variant}: ${stats.assignments} assignments, ${stats.conversions} conversions\n`;
                });
                summary += '\n';
            });
            
            alert(summary);
        }

        function runPerformanceAnalysis() {
            if (!performanceAnalytics) {
                alert('Performance analytics not initialized');
                return;
            }
            
            log('Running performance analysis...');
            const recommendations = performanceAnalytics.getPerformanceRecommendations();
            
            const recommendationsArea = document.getElementById('recommendations-area');
            if (recommendations.length === 0) {
                recommendationsArea.innerHTML = '<p>No performance issues detected. System is running optimally!</p>';
            } else {
                let html = '<h4>Performance Recommendations:</h4>';
                recommendations.forEach(rec => {
                    html += `
                        <div style="margin: 10px 0; padding: 10px; border-left: 4px solid ${rec.severity === 'high' ? '#dc3545' : rec.severity === 'medium' ? '#ffc107' : '#28a745'}; background: #f8f9fa;">
                            <strong>${rec.title}</strong> (${rec.severity})
                            <p>${rec.description}</p>
                        </div>
                    `;
                });
                recommendationsArea.innerHTML = html;
            }
            
            log(`Generated ${recommendations.length} performance recommendations`);
        }

        function simulatePerformanceIssues() {
            log('Simulating performance issues...');
            
            // Trigger multiple errors
            for (let i = 0; i < 5; i++) {
                setTimeout(() => triggerError(), i * 100);
            }
            
            // Simulate slow network requests
            fetch('/nonexistent-endpoint').catch(() => {
                log('Simulated network error');
            });
            
            // Consume memory
            triggerMemoryTest();
        }

        function showRecommendations() {
            runPerformanceAnalysis();
        }

        function exportAnalyticsData() {
            if (!performanceAnalytics) {
                alert('Performance analytics not initialized');
                return;
            }
            
            const data = performanceAnalytics.exportAllData();
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance-analytics-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            log('Analytics data exported');
        }

        function showAnalyticsDashboard() {
            if (!performanceAnalytics) {
                alert('Performance analytics not initialized');
                return;
            }
            
            performanceAnalytics.showAnalyticsDashboard();
            log('Analytics dashboard opened');
        }

        function toggleRealTimeUpdates() {
            log('Toggling real-time updates...');
            // This would toggle the real-time update functionality
        }

        function generateSampleData() {
            log('Generating sample data...');
            
            // Generate multiple interactions
            generateRandomClicks();
            
            setTimeout(() => {
                simulateFormInteraction();
            }, 1000);
            
            setTimeout(() => {
                simulateBackup('sample');
            }, 2000);
        }

        function clearAllData() {
            if (!performanceAnalytics) {
                alert('Performance analytics not initialized');
                return;
            }
            
            if (confirm('Are you sure you want to clear all analytics data?')) {
                if (performanceAnalytics.performanceMonitor) {
                    performanceAnalytics.performanceMonitor.clearMetrics();
                }
                
                if (performanceAnalytics.heatmapTracker) {
                    performanceAnalytics.heatmapTracker.clearData();
                }
                
                if (performanceAnalytics.abTestingFramework) {
                    performanceAnalytics.abTestingFramework.clearEvents();
                }
                
                testData = { backupCount: 0, errorCount: 0, interactionCount: 0 };
                log('All analytics data cleared');
            }
        }

        function log(message, type = 'info') {
            const logArea = document.getElementById('system-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warning' ? '#ffc107' : '#00ff00';
            
            const logEntry = document.createElement('div');
            logEntry.style.color = color;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(`[Performance Analytics] ${message}`);
        }
    </script>
</body>
</html>