/* PricingCalculator Component Styles */

.pricing-calculator {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.pricing-calculator.pricing-updated {
    border-color: #4facfe;
    box-shadow: 0 4px 20px rgba(79, 172, 254, 0.2);
}

/* Header */
.pricing-header {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f8f9fa;
}

.pricing-title {
    color: #333;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.pricing-title::before {
    content: '💰';
    font-size: 1.2rem;
}

/* Main price display */
.main-price {
    text-align: center;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    border-radius: 12px;
    border: 1px solid #e3f2fd;
}

.price-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-value {
    display: inline-block;
    color: #4facfe;
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    transition: all 0.3s ease;
}

.price-frequency {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin-left: 4px;
}

/* Pricing breakdown */
.pricing-breakdown {
    margin-bottom: 20px;
}

.breakdown-title {
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.breakdown-title::before {
    content: '📋';
    font-size: 1rem;
}

.breakdown-list {
    display: grid;
    gap: 8px;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4facfe;
    transition: all 0.2s ease;
}

.breakdown-item:hover {
    background: #e3f2fd;
    transform: translateX(4px);
}

.breakdown-item-name {
    color: #333;
    font-weight: 500;
    flex: 1;
}

.breakdown-item-price {
    color: #4facfe;
    font-weight: 600;
    font-size: 0.95rem;
}

.breakdown-empty {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
    margin: 0;
}

/* Savings indicator */
.savings-indicator {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.savings-indicator.hidden {
    display: none;
}

.savings-indicator.savings-highlight {
    animation: savingsHighlight 1s ease-out;
}

@keyframes savingsHighlight {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    50% { 
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.savings-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.savings-label {
    color: #155724;
    font-weight: 600;
    font-size: 1rem;
}

.savings-amount {
    color: #28a745;
    font-weight: 700;
    font-size: 1.2rem;
}

.savings-percentage {
    color: #155724;
    font-weight: 500;
    font-size: 0.9rem;
    background: rgba(40, 167, 69, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
}

/* Loading state */
.pricing-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px;
    text-align: center;
}

.pricing-loading.hidden {
    display: none;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #666;
    font-weight: 500;
}

.pricing-display.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Error state */
.pricing-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.pricing-error.hidden {
    display: none;
}

.error-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.error-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.error-message {
    color: #721c24;
    font-weight: 500;
    flex: 1;
    min-width: 200px;
}

.error-retry {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.2s;
}

.error-retry:hover {
    background: #c82333;
}

/* Mobile responsive design */
@media (max-width: 768px) {
    .pricing-calculator {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .pricing-title {
        font-size: 1.2rem;
    }
    
    .main-price {
        padding: 16px;
        margin-bottom: 20px;
    }
    
    .price-value {
        font-size: 2rem;
    }
    
    .price-frequency {
        font-size: 0.9rem;
    }
    
    .breakdown-item {
        padding: 10px 12px;
    }
    
    .breakdown-item-name {
        font-size: 0.9rem;
    }
    
    .breakdown-item-price {
        font-size: 0.85rem;
    }
    
    .savings-content {
        flex-direction: column;
        gap: 4px;
    }
    
    .savings-amount {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .pricing-calculator {
        padding: 16px;
    }
    
    .pricing-header {
        margin-bottom: 20px;
        padding-bottom: 12px;
    }
    
    .pricing-title {
        font-size: 1.1rem;
    }
    
    .main-price {
        padding: 12px;
        margin-bottom: 16px;
    }
    
    .price-value {
        font-size: 1.8rem;
    }
    
    .breakdown-title {
        font-size: 1rem;
    }
    
    .breakdown-item {
        padding: 8px 10px;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .breakdown-item-price {
        align-self: flex-end;
        font-size: 0.9rem;
    }
    
    .savings-indicator {
        padding: 12px;
    }
    
    .error-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .error-retry {
        align-self: flex-end;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .pricing-calculator {
        border-width: 3px;
        border-color: #000;
    }
    
    .main-price {
        border-width: 2px;
        border-color: #000;
    }
    
    .breakdown-item {
        border-left-width: 6px;
        border-left-color: #000;
    }
    
    .savings-indicator {
        border-width: 3px;
        border-color: #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .pricing-calculator,
    .price-value,
    .breakdown-item,
    .savings-indicator {
        transition: none;
    }
    
    .loading-spinner {
        animation: none;
    }
    
    .savings-indicator.savings-highlight {
        animation: none;
    }
    
    .breakdown-item:hover {
        transform: none;
    }
}

/* Focus management */
.pricing-calculator:focus-within {
    outline: 2px solid #4facfe;
    outline-offset: 4px;
}

/* Print styles */
@media print {
    .pricing-calculator {
        box-shadow: none;
        border: 2px solid #000;
    }
    
    .loading-spinner {
        display: none;
    }
    
    .error-retry {
        display: none;
    }
}