const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

const supabaseUrl = 'https://babgywcvqyclvxdkckkd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzMDc1MSwiZXhwIjoyMDY3MjA2NzUxfQ.DUKouq8tLc1QwyWKw9pze3tHXIiN-L9SNpdvhEQw3cs';

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupDatabase() {
  console.log('🚀 Starting SafeKeep Database Setup...\n');

  try {
    // Read the complete setup SQL
    const setupSQL = fs.readFileSync('./SafeKeepApp/database/complete_setup.sql', 'utf8');
    
    console.log('📊 Executing database schema setup...');
    
    // Execute the complete setup SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: setupSQL
    });

    if (error) {
      console.error('❌ Error setting up database schema:', error);
      
      // Try alternative approach - execute SQL directly
      console.log('🔄 Trying alternative approach...');
      
      // Split SQL into individual statements and execute them
      const statements = setupSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          try {
            const { error: stmtError } = await supabase.rpc('exec_sql', {
              sql: statement + ';'
            });
            
            if (stmtError) {
              console.log(`⚠️  Statement failed (might be expected): ${statement.substring(0, 50)}...`);
              console.log(`   Error: ${stmtError.message}`);
            } else {
              console.log(`✅ Executed: ${statement.substring(0, 50)}...`);
            }
          } catch (err) {
            console.log(`⚠️  Statement error: ${err.message}`);
          }
        }
      }
    } else {
      console.log('✅ Database schema setup completed successfully!');
    }

    // Verify tables were created
    console.log('\n🔍 Verifying database setup...');
    
    try {
      // Use RPC to query system tables
      const { data: tables, error: tablesError } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status')
          ORDER BY table_name;
        `
      });

      if (tablesError) {
        console.error('❌ Error checking tables:', tablesError);
        
        // Fallback: try to query each table directly
        console.log('🔄 Trying direct table verification...');
        const requiredTables = ['users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status'];
        const existingTables = [];
        
        for (const tableName of requiredTables) {
          try {
            const { error } = await supabase.from(tableName).select('*').limit(1);
            if (!error) {
              existingTables.push(tableName);
              console.log(`✅ Table exists: ${tableName}`);
            }
          } catch (err) {
            console.log(`❌ Table missing: ${tableName}`);
          }
        }
        
        console.log(`📋 Found ${existingTables.length}/${requiredTables.length} tables`);
      } else {
        const tableNames = tables.map(t => t.table_name);
        console.log('📋 Tables found:', tableNames);
        
        if (tableNames.length === 5) {
          console.log('✅ All 5 tables created successfully!');
        } else {
          console.log(`⚠️  Expected 5 tables, found ${tableNames.length}`);
        }
      }
    } catch (err) {
      console.log('⚠️  Could not verify tables using system query, trying direct approach...');
      
      // Direct table verification
      const requiredTables = ['users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status'];
      const existingTables = [];
      
      for (const tableName of requiredTables) {
        try {
          const { error } = await supabase.from(tableName).select('*').limit(1);
          if (!error) {
            existingTables.push(tableName);
            console.log(`✅ Table exists: ${tableName}`);
          }
        } catch (tableErr) {
          console.log(`❌ Table missing: ${tableName}`);
        }
      }
      
      console.log(`📋 Found ${existingTables.length}/${requiredTables.length} tables`);
    }

    // Check storage buckets
    console.log('\n🗄️  Checking storage buckets...');
    
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error checking buckets:', bucketsError);
    } else {
      console.log('📦 Existing buckets:', buckets.map(b => b.name));
      
      const requiredBuckets = ['user-data', 'thumbnails'];
      const missingBuckets = requiredBuckets.filter(name => 
        !buckets.some(bucket => bucket.name === name)
      );

      if (missingBuckets.length > 0) {
        console.log(`🔧 Creating missing buckets: ${missingBuckets.join(', ')}`);
        
        for (const bucketName of missingBuckets) {
          const bucketOptions = bucketName === 'user-data' 
            ? { public: false, fileSizeLimit: 52428800 } // 50MB
            : { public: true, fileSizeLimit: 5242880 };  // 5MB

          const { error: createError } = await supabase.storage.createBucket(bucketName, bucketOptions);
          
          if (createError) {
            console.error(`❌ Error creating bucket ${bucketName}:`, createError);
          } else {
            console.log(`✅ Created bucket: ${bucketName}`);
          }
        }
      } else {
        console.log('✅ All required buckets exist!');
      }
    }

    // Test authentication
    console.log('\n🔐 Testing authentication...');
    
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.log('ℹ️  No active session (expected for service role)');
    }

    console.log('\n🎉 Database setup completed!');
    console.log('\n📋 Next steps:');
    console.log('1. ✅ Database schema is set up');
    console.log('2. ✅ Environment variables are configured');
    console.log('3. 🔧 Test your React Native app connection');
    console.log('4. 🔧 Try creating a test user to verify triggers work');
    
  } catch (error) {
    console.error('💥 Unexpected error during setup:', error);
  }
}

// Run the setup
setupDatabase();