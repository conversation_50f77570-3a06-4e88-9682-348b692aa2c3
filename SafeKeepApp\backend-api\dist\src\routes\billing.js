"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const BillingController_1 = require("../controllers/BillingController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const billingController = new BillingController_1.BillingController();
router.post('/payment-intent', auth_1.authenticateToken, (req, res) => {
    billingController.createPaymentIntent(req, res);
});
router.post('/webhook', (req, res) => {
    billingController.handleWebhook(req, res);
});
router.get('/status/:userId', auth_1.authenticateToken, (req, res) => {
    billingController.getBillingStatus(req, res);
});
exports.default = router;
//# sourceMappingURL=billing.js.map