const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://babgywcvqyclvxdkckkd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzMDc1MSwiZXhwIjoyMDY3MjA2NzUxfQ.DUKouq8tLc1QwyWKw9pze3tHXIiN-L9SNpdvhEQw3cs';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseStatus() {
  console.log('🔍 Checking SafeKeep Database Status...\n');

  try {
    // Check if tables exist by trying to query them
    const requiredTables = ['users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status'];
    const tableStatus = {};

    for (const tableName of requiredTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);

        if (error) {
          tableStatus[tableName] = `❌ Error: ${error.message}`;
        } else {
          tableStatus[tableName] = `✅ Exists (${data.length} sample records)`;
        }
      } catch (err) {
        tableStatus[tableName] = `❌ Not accessible: ${err.message}`;
      }
    }

    console.log('📊 Database Tables Status:');
    for (const [table, status] of Object.entries(tableStatus)) {
      console.log(`   ${table}: ${status}`);
    }

    // Check storage buckets
    console.log('\n🗄️  Storage Buckets Status:');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error checking buckets:', bucketsError);
    } else {
      console.log('📦 Available buckets:');
      buckets.forEach(bucket => {
        console.log(`   - ${bucket.name} (${bucket.public ? 'public' : 'private'})`);
      });

      const requiredBuckets = ['user-data', 'thumbnails'];
      const missingBuckets = requiredBuckets.filter(name => 
        !buckets.some(bucket => bucket.name === name)
      );

      if (missingBuckets.length === 0) {
        console.log('✅ All required storage buckets exist!');
      } else {
        console.log(`❌ Missing buckets: ${missingBuckets.join(', ')}`);
      }
    }

    // Test authentication
    console.log('\n🔐 Authentication Test:');
    try {
      // Try to create a test user (this will fail if auth is not properly set up)
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'testpassword123'
      });

      if (authError) {
        console.log(`ℹ️  Auth test result: ${authError.message}`);
      } else {
        console.log('✅ Authentication is working');
        
        // Clean up test user if created
        if (authData.user) {
          console.log('🧹 Cleaning up test user...');
        }
      }
    } catch (err) {
      console.log(`ℹ️  Auth test: ${err.message}`);
    }

    // Check RLS policies
    console.log('\n🔒 Security Status:');
    console.log('ℹ️  Row Level Security (RLS) policies should be configured in Supabase Dashboard');
    console.log('ℹ️  Storage policies should be configured for user-data and thumbnails buckets');

    // Summary
    console.log('\n📋 Summary:');
    const workingTables = Object.values(tableStatus).filter(status => status.includes('✅')).length;
    console.log(`   Database Tables: ${workingTables}/${requiredTables.length} working`);
    console.log(`   Storage Buckets: ${buckets ? buckets.length : 0} total, 2 required`);
    
    if (workingTables === requiredTables.length && buckets && buckets.length >= 2) {
      console.log('\n🎉 Your Supabase database appears to be fully set up!');
      console.log('\n✅ Ready for your SafeKeep app to connect and start working!');
    } else {
      console.log('\n⚠️  Your database needs some setup. Please run the SQL scripts manually in Supabase Dashboard.');
    }

  } catch (error) {
    console.error('💥 Unexpected error during status check:', error);
  }
}

checkDatabaseStatus();