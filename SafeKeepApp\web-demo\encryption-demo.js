/**
 * Encryption Demonstration Interface for SafeKeep Web Demo
 * Provides visual encryption/decryption demonstration with before/after comparison
 */

class EncryptionDemo {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.encryptionManager = new window.EncryptionManager();
        this.currentDemo = null;
        
        this.setupEventListeners();
        this.render();
    }

    setupEventListeners() {
        this.encryptionManager.addListener((event, data) => {
            this.handleEncryptionEvent(event, data);
        });
    }

    handleEncryptionEvent(event, data) {
        switch (event) {
            case 'key_generated':
                this.updateKeyDisplay(data);
                break;
            case 'encryption_started':
                this.showEncryptionProgress(true);
                break;
            case 'encryption_completed':
                this.showEncryptionResults(data);
                this.showEncryptionProgress(false);
                break;
            case 'decryption_started':
                this.showDecryptionProgress(true);
                break;
            case 'decryption_completed':
                this.showDecryptionResults(data);
                this.showDecryptionProgress(false);
                break;
            case 'error':
                this.showError(data.message);
                this.showEncryptionProgress(false);
                this.showDecryptionProgress(false);
                break;
        }
    }

    render() {
        if (!this.container) {
            console.error(`Container with ID '${this.containerId}' not found`);
            return;
        }

        this.container.innerHTML = `
            <div class="encryption-demo">
                <div class="demo-header">
                    <h3>🔐 Encryption Demonstration</h3>
                    <p>See how SafeKeep encrypts your data with military-grade security</p>
                </div>

                <!-- Algorithm Selection -->
                <div class="algorithm-section">
                    <h4>🔧 Encryption Algorithm</h4>
                    <div class="algorithm-selector">
                        ${this.renderAlgorithmOptions()}
                    </div>
                    <div class="algorithm-info" id="algorithm-info">
                        ${this.renderAlgorithmInfo('AES-256-GCM')}
                    </div>
                </div>

                <!-- Key Management -->
                <div class="key-section">
                    <h4>🔑 Key Management</h4>
                    <div class="key-controls">
                        <button class="btn" onclick="encryptionDemo.generateNewKey()">Generate New Key</button>
                        <button class="btn secondary" onclick="encryptionDemo.showKeyDetails()">Show Key Details</button>
                    </div>
                    <div class="key-display" id="key-display" style="display: none;">
                        <!-- Key details will be populated here -->
                    </div>
                </div>

                <!-- Data Input -->
                <div class="data-section">
                    <h4>📝 Data to Encrypt</h4>
                    <div class="data-controls">
                        <select id="sample-data-type" onchange="encryptionDemo.loadSampleData()">
                            <option value="contact">Contact Information</option>
                            <option value="message">Text Message</option>
                            <option value="photo">Photo Metadata</option>
                            <option value="document">Document</option>
                        </select>
                        <button class="btn secondary" onclick="encryptionDemo.loadSampleData()">Load Sample</button>
                        <button class="btn secondary" onclick="encryptionDemo.clearData()">Clear</button>
                    </div>
                    <div class="data-input">
                        <textarea id="original-data" placeholder="Enter data to encrypt or load sample data..." rows="8"></textarea>
                    </div>
                </div>

                <!-- Encryption Controls -->
                <div class="encryption-controls">
                    <button class="btn success" onclick="encryptionDemo.encryptData()" id="encrypt-btn">
                        🔒 Encrypt Data
                    </button>
                    <button class="btn" onclick="encryptionDemo.decryptData()" id="decrypt-btn" disabled>
                        🔓 Decrypt Data
                    </button>
                    <button class="btn secondary" onclick="encryptionDemo.resetDemo()">
                        🔄 Reset Demo
                    </button>
                </div>

                <!-- Progress Indicators -->
                <div class="progress-section" id="encryption-progress" style="display: none;">
                    <div class="progress-header">
                        <span>🔄 Encrypting Data...</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="encryption-progress-bar"></div>
                    </div>
                </div>

                <div class="progress-section" id="decryption-progress" style="display: none;">
                    <div class="progress-header">
                        <span>🔄 Decrypting Data...</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="decryption-progress-bar"></div>
                    </div>
                </div>

                <!-- Before/After Comparison -->
                <div class="comparison-section" id="comparison-section" style="display: none;">
                    <h4>📊 Before/After Comparison</h4>
                    <div class="comparison-grid">
                        <div class="comparison-panel original">
                            <h5>📄 Original Data</h5>
                            <div class="data-preview" id="original-preview">
                                <!-- Original data preview -->
                            </div>
                        </div>
                        <div class="comparison-panel encrypted">
                            <h5>🔒 Encrypted Data</h5>
                            <div class="data-preview" id="encrypted-preview">
                                <!-- Encrypted data preview -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Decryption Results -->
                <div class="decryption-section" id="decryption-section" style="display: none;">
                    <h4>🔓 Decryption Results</h4>
                    <div class="decryption-panel">
                        <h5>✅ Decrypted Data</h5>
                        <div class="data-preview" id="decrypted-preview">
                            <!-- Decrypted data preview -->
                        </div>
                        <div class="verification-status" id="verification-status">
                            <!-- Verification status -->
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="metrics-section" id="metrics-section" style="display: none;">
                    <h4>⚡ Performance Metrics</h4>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-label">Encryption Time</div>
                            <div class="metric-value" id="encryption-time">-</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Decryption Time</div>
                            <div class="metric-value" id="decryption-time">-</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Throughput</div>
                            <div class="metric-value" id="throughput">-</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Data Size Change</div>
                            <div class="metric-value" id="size-change">-</div>
                        </div>
                    </div>
                </div>

                <!-- Security Strength -->
                <div class="security-section">
                    <h4>🛡️ Security Strength</h4>
                    <div class="security-info" id="security-info">
                        ${this.renderSecurityInfo('AES-256-GCM')}
                    </div>
                </div>

                <!-- Error Display -->
                <div class="error-section" id="error-section" style="display: none;">
                    <div class="error-message" id="error-message"></div>
                </div>
            </div>
        `;

        // Load initial sample data
        this.loadSampleData();
    }

    renderAlgorithmOptions() {
        const algorithms = this.encryptionManager.getAlgorithms();
        return algorithms.map(algo => `
            <label class="algorithm-option">
                <input type="radio" name="algorithm" value="${algo.name}" 
                       ${algo.name === 'AES-256-GCM' ? 'checked' : ''}
                       onchange="encryptionDemo.selectAlgorithm('${algo.name}')">
                <span class="algorithm-label">
                    <strong>${algo.name}</strong>
                    <small>${algo.description}</small>
                </span>
            </label>
        `).join('');
    }

    renderAlgorithmInfo(algorithm) {
        const algo = this.encryptionManager.algorithms[algorithm];
        return `
            <div class="algorithm-details">
                <div class="algorithm-badge" style="background-color: ${algo.color}">
                    ${algo.strength}
                </div>
                <div class="algorithm-specs">
                    <div><strong>Key Size:</strong> ${algo.keySize} bits</div>
                    <div><strong>Performance:</strong> ${algo.performance}</div>
                    <div><strong>Description:</strong> ${algo.description}</div>
                </div>
            </div>
        `;
    }

    renderSecurityInfo(algorithm) {
        const strength = this.encryptionManager.demonstrateStrength(algorithm);
        return `
            <div class="security-details">
                <div class="security-badge" style="background-color: ${strength.color}">
                    ${strength.strength}
                </div>
                <div class="security-specs">
                    <div><strong>Algorithm:</strong> ${strength.algorithm}</div>
                    <div><strong>Key Size:</strong> ${strength.keySize} bits</div>
                    <div><strong>Key Space:</strong> 2^${strength.keySize} possible keys</div>
                    <div><strong>Brute Force Time:</strong> ${strength.bruteForceTime}</div>
                </div>
                <div class="security-explanation">
                    <p>This encryption is virtually unbreakable with current technology. 
                    Even with the world's most powerful computers, it would take ${strength.bruteForceTime} 
                    to try all possible keys.</p>
                </div>
            </div>
        `;
    }

    selectAlgorithm(algorithm) {
        this.encryptionManager.currentAlgorithm = algorithm;
        document.getElementById('algorithm-info').innerHTML = this.renderAlgorithmInfo(algorithm);
        document.getElementById('security-info').innerHTML = this.renderSecurityInfo(algorithm);
        
        // Reset current demo if algorithm changes
        if (this.currentDemo) {
            this.resetDemo();
        }
    }

    async generateNewKey() {
        try {
            await this.encryptionManager.generateKey();
        } catch (error) {
            this.showError(`Key generation failed: ${error.message}`);
        }
    }

    updateKeyDisplay(keyData) {
        const keyDisplay = document.getElementById('key-display');
        keyDisplay.innerHTML = `
            <div class="key-details">
                <div class="key-info">
                    <div><strong>Algorithm:</strong> ${keyData.algorithm}</div>
                    <div><strong>Key Size:</strong> ${keyData.keySize} bits</div>
                    <div><strong>Generation Time:</strong> ${keyData.generationTime.toFixed(2)}ms</div>
                </div>
                <div class="key-values">
                    <div class="key-value">
                        <label>Encryption Key (Hex):</label>
                        <div class="hex-display">${keyData.keyHex}</div>
                    </div>
                    <div class="key-value">
                        <label>Initialization Vector (Hex):</label>
                        <div class="hex-display">${keyData.ivHex}</div>
                    </div>
                </div>
                <div class="key-warning">
                    ⚠️ In production, keys are never displayed and are stored securely
                </div>
            </div>
        `;
    }

    showKeyDetails() {
        const keyDisplay = document.getElementById('key-display');
        keyDisplay.style.display = keyDisplay.style.display === 'none' ? 'block' : 'none';
    }

    loadSampleData() {
        const dataType = document.getElementById('sample-data-type').value;
        const sampleData = this.encryptionManager.generateSampleData(dataType);
        document.getElementById('original-data').value = JSON.stringify(sampleData, null, 2);
    }

    clearData() {
        document.getElementById('original-data').value = '';
        this.resetDemo();
    }

    async encryptData() {
        const originalData = document.getElementById('original-data').value.trim();
        
        if (!originalData) {
            this.showError('Please enter data to encrypt or load sample data');
            return;
        }

        try {
            let dataToEncrypt;
            try {
                dataToEncrypt = JSON.parse(originalData);
            } catch {
                dataToEncrypt = originalData;
            }

            this.currentDemo = await this.encryptionManager.encryptData(dataToEncrypt);
            document.getElementById('decrypt-btn').disabled = false;
        } catch (error) {
            this.showError(`Encryption failed: ${error.message}`);
        }
    }

    async decryptData() {
        if (!this.currentDemo) {
            this.showError('No encrypted data available. Please encrypt data first.');
            return;
        }

        try {
            const decryptionResult = await this.encryptionManager.decryptData(
                this.currentDemo.encryptedData,
                this.currentDemo.keyHex,
                this.currentDemo.ivHex,
                this.currentDemo.algorithm
            );
            
            this.showDecryptionResults(decryptionResult);
        } catch (error) {
            this.showError(`Decryption failed: ${error.message}`);
        }
    }

    showEncryptionProgress(show) {
        const progressSection = document.getElementById('encryption-progress');
        if (show) {
            progressSection.style.display = 'block';
            this.animateProgress('encryption-progress-bar', 2000);
        } else {
            progressSection.style.display = 'none';
        }
    }

    showDecryptionProgress(show) {
        const progressSection = document.getElementById('decryption-progress');
        if (show) {
            progressSection.style.display = 'block';
            this.animateProgress('decryption-progress-bar', 1500);
        } else {
            progressSection.style.display = 'none';
        }
    }

    animateProgress(elementId, duration) {
        const progressBar = document.getElementById(elementId);
        let progress = 0;
        const interval = 50;
        const increment = (100 / duration) * interval;

        const timer = setInterval(() => {
            progress += increment;
            if (progress >= 100) {
                progress = 100;
                clearInterval(timer);
            }
            progressBar.style.width = `${progress}%`;
        }, interval);
    }

    showEncryptionResults(encryptionData) {
        // Show comparison section
        document.getElementById('comparison-section').style.display = 'block';
        
        // Update original data preview
        document.getElementById('original-preview').innerHTML = `
            <div class="data-content readable">
                <pre>${JSON.stringify(encryptionData.originalData, null, 2)}</pre>
            </div>
            <div class="data-stats">
                <span>Size: ${encryptionData.originalSize} bytes</span>
                <span>Type: Readable</span>
            </div>
        `;
        
        // Update encrypted data preview
        document.getElementById('encrypted-preview').innerHTML = `
            <div class="data-content encrypted">
                <div class="hex-display">${encryptionData.encryptedData}</div>
            </div>
            <div class="data-stats">
                <span>Size: ${encryptionData.encryptedSize} bytes</span>
                <span>Type: Encrypted (Unreadable)</span>
            </div>
        `;
        
        // Show performance metrics
        this.showPerformanceMetrics(encryptionData);
    }

    showDecryptionResults(decryptionData) {
        // Show decryption section
        document.getElementById('decryption-section').style.display = 'block';
        
        // Update decrypted data preview
        document.getElementById('decrypted-preview').innerHTML = `
            <div class="data-content readable">
                <pre>${JSON.stringify(decryptionData.decryptedData, null, 2)}</pre>
            </div>
        `;
        
        // Update verification status
        document.getElementById('verification-status').innerHTML = `
            <div class="verification-badge ${decryptionData.verified ? 'verified' : 'failed'}">
                ${decryptionData.verified ? '✅ Data Integrity Verified' : '❌ Verification Failed'}
            </div>
        `;
        
        // Update performance metrics with decryption time
        document.getElementById('decryption-time').textContent = `${decryptionData.decryptionTime.toFixed(2)}ms`;
    }

    showPerformanceMetrics(data) {
        document.getElementById('metrics-section').style.display = 'block';
        
        document.getElementById('encryption-time').textContent = `${data.encryptionTime.toFixed(2)}ms`;
        document.getElementById('throughput').textContent = `${(data.throughput / 1024).toFixed(1)} KB/s`;
        
        const sizeChange = ((data.encryptedSize - data.originalSize) / data.originalSize * 100);
        document.getElementById('size-change').textContent = `${sizeChange > 0 ? '+' : ''}${sizeChange.toFixed(1)}%`;
    }

    showError(message) {
        const errorSection = document.getElementById('error-section');
        const errorMessage = document.getElementById('error-message');
        
        errorMessage.textContent = message;
        errorSection.style.display = 'block';
        
        // Hide error after 5 seconds
        setTimeout(() => {
            errorSection.style.display = 'none';
        }, 5000);
    }

    resetDemo() {
        this.currentDemo = null;
        
        // Hide all result sections
        document.getElementById('comparison-section').style.display = 'none';
        document.getElementById('decryption-section').style.display = 'none';
        document.getElementById('metrics-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        
        // Reset controls
        document.getElementById('decrypt-btn').disabled = true;
        
        // Clear data
        document.getElementById('original-data').value = '';
    }
}

// Make EncryptionDemo available globally
window.EncryptionDemo = EncryptionDemo;