/**
 * Encryption Manager for SafeKeep Web Demo
 * Demonstrates encryption/decryption capabilities with visual feedback
 */

class EncryptionManager {
    constructor() {
        this.algorithms = {
            'AES-256-GCM': {
                name: 'AES-256-GCM',
                keySize: 256,
                description: 'Advanced Encryption Standard with Galois/Counter Mode',
                strength: 'Military Grade',
                performance: 'Excellent',
                color: '#28a745'
            },
            'AES-192-GCM': {
                name: 'AES-192-GCM',
                keySize: 192,
                description: 'AES with 192-bit key and GCM mode',
                strength: 'Very High',
                performance: 'Very Good',
                color: '#17a2b8'
            },
            'AES-128-GCM': {
                name: 'AES-128-GCM',
                keySize: 128,
                description: 'AES with 128-bit key and GCM mode',
                strength: 'High',
                performance: 'Excellent',
                color: '#ffc107'
            }
        };
        
        this.currentAlgorithm = 'AES-256-GCM';
        this.currentKey = null;
        this.currentIV = null;
        this.performanceMetrics = {
            encryptionTime: 0,
            decryptionTime: 0,
            throughput: 0,
            compressionRatio: 1.0
        };
        
        this.listeners = [];
    }

    // Generate cryptographic key
    async generateKey(algorithm = this.currentAlgorithm) {
        const startTime = performance.now();
        
        try {
            const keySize = this.algorithms[algorithm].keySize;
            
            // Generate random key
            const key = new Uint8Array(keySize / 8);
            crypto.getRandomValues(key);
            
            // Generate IV (12 bytes for GCM)
            const iv = new Uint8Array(12);
            crypto.getRandomValues(iv);
            
            this.currentKey = key;
            this.currentIV = iv;
            this.currentAlgorithm = algorithm;
            
            const generationTime = performance.now() - startTime;
            
            this.notifyListeners('key_generated', {
                algorithm,
                keySize,
                generationTime,
                keyHex: this.arrayToHex(key),
                ivHex: this.arrayToHex(iv)
            });
            
            return {
                key: key,
                iv: iv,
                algorithm: algorithm,
                generationTime: generationTime
            };
        } catch (error) {
            this.notifyListeners('error', { message: `Key generation failed: ${error.message}` });
            throw error;
        }
    }

    // Encrypt data with visual progress
    async encryptData(data, options = {}) {
        if (!this.currentKey || !this.currentIV) {
            await this.generateKey();
        }

        const startTime = performance.now();
        const originalSize = new TextEncoder().encode(JSON.stringify(data)).length;
        
        try {
            // Convert data to string if needed
            const dataString = typeof data === 'string' ? data : JSON.stringify(data);
            const dataBytes = new TextEncoder().encode(dataString);
            
            // Simulate encryption process with visual feedback
            this.notifyListeners('encryption_started', {
                algorithm: this.currentAlgorithm,
                originalSize: originalSize,
                dataType: typeof data
            });
            
            // Import key for Web Crypto API
            const cryptoKey = await crypto.subtle.importKey(
                'raw',
                this.currentKey,
                { name: 'AES-GCM' },
                false,
                ['encrypt']
            );
            
            // Encrypt the data
            const encryptedBuffer = await crypto.subtle.encrypt(
                {
                    name: 'AES-GCM',
                    iv: this.currentIV
                },
                cryptoKey,
                dataBytes
            );
            
            const encryptedArray = new Uint8Array(encryptedBuffer);
            const encryptedHex = this.arrayToHex(encryptedArray);
            
            const encryptionTime = performance.now() - startTime;
            const encryptedSize = encryptedArray.length;
            const compressionRatio = originalSize / encryptedSize;
            
            // Update performance metrics
            this.performanceMetrics.encryptionTime = encryptionTime;
            this.performanceMetrics.throughput = (originalSize / encryptionTime) * 1000; // bytes per second
            this.performanceMetrics.compressionRatio = compressionRatio;
            
            const result = {
                originalData: data,
                encryptedData: encryptedHex,
                algorithm: this.currentAlgorithm,
                keyHex: this.arrayToHex(this.currentKey),
                ivHex: this.arrayToHex(this.currentIV),
                originalSize: originalSize,
                encryptedSize: encryptedSize,
                encryptionTime: encryptionTime,
                compressionRatio: compressionRatio,
                throughput: this.performanceMetrics.throughput
            };
            
            this.notifyListeners('encryption_completed', result);
            
            return result;
        } catch (error) {
            this.notifyListeners('error', { message: `Encryption failed: ${error.message}` });
            throw error;
        }
    }

    // Decrypt data with visual progress
    async decryptData(encryptedHex, keyHex, ivHex, algorithm = this.currentAlgorithm) {
        const startTime = performance.now();
        
        try {
            this.notifyListeners('decryption_started', {
                algorithm: algorithm,
                encryptedSize: encryptedHex.length / 2
            });
            
            // Convert hex strings back to arrays
            const encryptedArray = this.hexToArray(encryptedHex);
            const keyArray = this.hexToArray(keyHex);
            const ivArray = this.hexToArray(ivHex);
            
            // Import key for Web Crypto API
            const cryptoKey = await crypto.subtle.importKey(
                'raw',
                keyArray,
                { name: 'AES-GCM' },
                false,
                ['decrypt']
            );
            
            // Decrypt the data
            const decryptedBuffer = await crypto.subtle.decrypt(
                {
                    name: 'AES-GCM',
                    iv: ivArray
                },
                cryptoKey,
                encryptedArray
            );
            
            const decryptedString = new TextDecoder().decode(decryptedBuffer);
            let decryptedData;
            
            try {
                decryptedData = JSON.parse(decryptedString);
            } catch {
                decryptedData = decryptedString;
            }
            
            const decryptionTime = performance.now() - startTime;
            this.performanceMetrics.decryptionTime = decryptionTime;
            
            const result = {
                decryptedData: decryptedData,
                decryptionTime: decryptionTime,
                algorithm: algorithm,
                verified: true
            };
            
            this.notifyListeners('decryption_completed', result);
            
            return result;
        } catch (error) {
            this.notifyListeners('error', { message: `Decryption failed: ${error.message}` });
            throw error;
        }
    }

    // Demonstrate encryption strength
    demonstrateStrength(algorithm = this.currentAlgorithm) {
        const algo = this.algorithms[algorithm];
        const keySpace = Math.pow(2, algo.keySize);
        const bruteForceTime = this.calculateBruteForceTime(algo.keySize);
        
        return {
            algorithm: algorithm,
            keySize: algo.keySize,
            keySpace: keySpace,
            bruteForceTime: bruteForceTime,
            strength: algo.strength,
            description: algo.description,
            color: algo.color
        };
    }

    // Calculate theoretical brute force time
    calculateBruteForceTime(keySize) {
        const keysPerSecond = 1e12; // Assume 1 trillion keys per second
        const totalKeys = Math.pow(2, keySize);
        const averageTime = totalKeys / (2 * keysPerSecond);
        
        // Convert to human readable format
        const years = averageTime / (365.25 * 24 * 3600);
        
        if (years < 1) {
            return `${Math.round(averageTime / (24 * 3600))} days`;
        } else if (years < 1000) {
            return `${Math.round(years)} years`;
        } else if (years < 1e6) {
            return `${(years / 1000).toFixed(1)}K years`;
        } else if (years < 1e9) {
            return `${(years / 1e6).toFixed(1)}M years`;
        } else if (years < 1e12) {
            return `${(years / 1e9).toFixed(1)}B years`;
        } else {
            return `${(years / 1e12).toFixed(1)}T years`;
        }
    }

    // Get available algorithms
    getAlgorithms() {
        return Object.values(this.algorithms);
    }

    // Get current performance metrics
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }

    // Utility functions
    arrayToHex(array) {
        return Array.from(array)
            .map(byte => byte.toString(16).padStart(2, '0'))
            .join('');
    }

    hexToArray(hex) {
        const array = new Uint8Array(hex.length / 2);
        for (let i = 0; i < hex.length; i += 2) {
            array[i / 2] = parseInt(hex.substr(i, 2), 16);
        }
        return array;
    }

    // Event handling
    addListener(callback) {
        this.listeners.push(callback);
    }

    removeListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Encryption manager listener error:', error);
            }
        });
    }

    // Generate sample data for demonstration
    generateSampleData(type = 'contact') {
        const samples = {
            contact: {
                name: 'John Doe',
                phone: '+1234567890',
                email: '<EMAIL>',
                address: '123 Main St, Anytown, USA',
                notes: 'Important business contact'
            },
            message: {
                from: '+1234567890',
                to: '+0987654321',
                message: 'This is a confidential message that needs encryption',
                timestamp: new Date().toISOString(),
                type: 'SMS'
            },
            photo: {
                filename: 'family_vacation.jpg',
                size: 2048576,
                type: 'image/jpeg',
                location: { lat: 40.7128, lng: -74.0060 },
                timestamp: new Date().toISOString(),
                metadata: {
                    camera: 'iPhone 13 Pro',
                    settings: 'f/1.5, 1/120s, ISO 100'
                }
            },
            document: {
                title: 'Confidential Business Plan',
                content: 'This document contains sensitive business information including financial projections, strategic plans, and competitive analysis.',
                author: 'Jane Smith',
                created: new Date().toISOString(),
                classification: 'Confidential'
            }
        };
        
        return samples[type] || samples.contact;
    }
}

// Make EncryptionManager available globally
window.EncryptionManager = EncryptionManager;