import { supabase } from './supabaseClient';
import { executeRlsFix } from './executeRlsFix';
import { verifyRlsPolicies, testRlsPolicies } from './verifyRlsPolicies';

/**
 * Comprehensive utility to test and fix RLS policies
 * This function will:
 * 1. Verify current RLS policies
 * 2. Test policy enforcement
 * 3. Fix policies if needed
 * 4. Verify again after fixing
 */
export async function testAndFixRlsPolicies(): Promise<{
  success: boolean;
  message: string;
  details: {
    initialVerification: any;
    initialTest: any;
    fixApplied: boolean;
    fixResult?: any;
    finalVerification?: any;
    finalTest?: any;
  };
}> {
  const details: any = {
    initialVerification: null,
    initialTest: null,
    fixApplied: false,
    fixResult: null,
    finalVerification: null,
    finalTest: null
  };

  try {
    console.log('🔍 Testing RLS policies...');

    // Step 1: Verify current RLS policies
    console.log('Step 1: Verifying current RLS policies...');
    const initialVerification = await verifyRlsPolicies();
    details.initialVerification = initialVerification;
    console.log('Initial verification result:', initialVerification);

    // Step 2: Test policy enforcement
    console.log('Step 2: Testing policy enforcement...');
    const initialTest = await testRlsPolicies();
    details.initialTest = initialTest;
    console.log('Initial test result:', initialTest);

    // Determine if fix is needed
    const needsFix = !initialVerification.success || 
                    !initialVerification.policies || 
                    initialVerification.policies.length === 0 ||
                    (initialTest.success && initialTest.results && 
                     (!initialTest.results.can_read || !initialTest.results.can_insert || 
                      !initialTest.results.can_update || !initialTest.results.can_delete));

    if (needsFix) {
      // Step 3: Fix policies
      console.log('Step 3: Fixing RLS policies...');
      details.fixApplied = true;
      const fixResult = await executeRlsFix();
      details.fixResult = fixResult;
      console.log('Fix result:', fixResult);

      if (fixResult.success) {
        // Step 4: Verify again after fixing
        console.log('Step 4: Verifying RLS policies after fix...');
        const finalVerification = await verifyRlsPolicies();
        details.finalVerification = finalVerification;
        console.log('Final verification result:', finalVerification);

        // Step 5: Test again after fixing
        console.log('Step 5: Testing policy enforcement after fix...');
        const finalTest = await testRlsPolicies();
        details.finalTest = finalTest;
        console.log('Final test result:', finalTest);

        if (finalVerification.success && finalTest.success) {
          return {
            success: true,
            message: 'RLS policies were fixed and are now working correctly',
            details
          };
        } else {
          return {
            success: false,
            message: 'RLS policies were fixed but are still not working correctly',
            details
          };
        }
      } else {
        return {
          success: false,
          message: `Failed to fix RLS policies: ${fixResult.message}`,
          details
        };
      }
    } else {
      return {
        success: true,
        message: 'RLS policies are already correctly configured and working',
        details
      };
    }
  } catch (error) {
    console.error('❌ Error testing/fixing RLS policies:', error);
    return {
      success: false,
      message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}`,
      details
    };
  }
}

/**
 * Simple function to run the RLS policy fix from a UI component
 */
export async function runRlsPolicyFix(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Running RLS policy fix...');
    const result = await executeRlsFix();
    return result;
  } catch (error) {
    console.error('Error running RLS policy fix:', error);
    return {
      success: false,
      message: `Error: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}