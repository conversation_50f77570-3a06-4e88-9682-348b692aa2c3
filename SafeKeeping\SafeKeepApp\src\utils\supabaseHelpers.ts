// Supabase Helper Utilities for SafeKeep
import { supabase, STORAGE_PATHS, TABLES, BUCKETS } from '../config/supabase';
import { Database } from '../config/supabase';

// Type definitions for better TypeScript support
export type UserProfile = Database['public']['Tables']['users']['Row'];
export type FileMetadata = Database['public']['Tables']['file_metadata']['Row'];
export type BackupSession = Database['public']['Tables']['backup_sessions']['Row'];
export type StorageUsage = Database['public']['Tables']['storage_usage']['Row'];

// Helper function to get user's storage path
export const getUserStoragePath = (userId: string, category: 'photos' | 'contacts' | 'messages' | 'metadata' | 'thumbnails'): string => {
  const pathTemplate = STORAGE_PATHS[category.toUpperCase() as keyof typeof STORAGE_PATHS];
  return pathTemplate.replace('{userId}', userId);
};

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any, context: string): string => {
  console.error(`Supabase error in ${context}:`, error);
  
  if (error?.message) {
    // Common Supabase error messages
    if (error.message.includes('JWT expired')) {
      return 'Your session has expired. Please sign in again.';
    }
    if (error.message.includes('Row Level Security')) {
      return 'Access denied. You can only access your own data.';
    }
    if (error.message.includes('duplicate key')) {
      return 'This item already exists.';
    }
    if (error.message.includes('foreign key')) {
      return 'Invalid reference. Please try again.';
    }
    if (error.message.includes('not found')) {
      return 'The requested item was not found.';
    }
    
    return error.message;
  }
  
  return 'An unexpected error occurred. Please try again.';
};

// Helper function to check if user is authenticated
export const isUserAuthenticated = async (): Promise<boolean> => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    return !error && !!user;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
};

// Helper function to get current user ID
export const getCurrentUserId = async (): Promise<string | null> => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error || !user) {
      return null;
    }
    return user.id;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
};

// Helper function to create file metadata entry
export const createFileMetadata = async (fileData: {
  userId: string;
  originalName: string;
  encryptedName: string;
  mimeType: string;
  size: number;
  encryptedSize: number;
  category: 'photo' | 'contact' | 'message';
  hash: string;
  encryptionIv: string;
  encryptionSalt: string;
  storagePath: string;
}): Promise<{ data: FileMetadata | null; error: string | null }> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.FILE_METADATA)
      .insert({
        user_id: fileData.userId,
        original_name: fileData.originalName,
        encrypted_name: fileData.encryptedName,
        mime_type: fileData.mimeType,
        size: fileData.size,
        encrypted_size: fileData.encryptedSize,
        category: fileData.category,
        hash: fileData.hash,
        encryption_iv: fileData.encryptionIv,
        encryption_salt: fileData.encryptionSalt,
        storage_path: fileData.storagePath,
      })
      .select()
      .single();

    if (error) {
      return { data: null, error: handleSupabaseError(error, 'createFileMetadata') };
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error: handleSupabaseError(error, 'createFileMetadata') };
  }
};

// Helper function to get user's files by category
export const getUserFilesByCategory = async (
  userId: string, 
  category?: 'photo' | 'contact' | 'message'
): Promise<{ data: FileMetadata[] | null; error: string | null }> => {
  try {
    let query = supabase
      .from(TABLES.FILE_METADATA)
      .select('*')
      .eq('user_id', userId)
      .order('uploaded_at', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query;

    if (error) {
      return { data: null, error: handleSupabaseError(error, 'getUserFilesByCategory') };
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error: handleSupabaseError(error, 'getUserFilesByCategory') };
  }
};

// Helper function to update storage usage
export const updateUserStorageUsage = async (userId: string): Promise<{ error: string | null }> => {
  try {
    // This is handled automatically by database triggers, but we can manually refresh if needed
    const { error } = await supabase.rpc('refresh_storage_usage', { user_id: userId });

    if (error) {
      return { error: handleSupabaseError(error, 'updateUserStorageUsage') };
    }

    return { error: null };
  } catch (error) {
    return { error: handleSupabaseError(error, 'updateUserStorageUsage') };
  }
};

// Helper function to get user's storage quota and usage
export const getUserStorageInfo = async (userId: string): Promise<{
  data: { used: number; quota: number; usage: StorageUsage[] } | null;
  error: string | null;
}> => {
  try {
    // Get user's total quota and usage
    const { data: userData, error: userError } = await supabase
      .from(TABLES.USERS)
      .select('storage_used, storage_quota')
      .eq('id', userId)
      .single();

    if (userError) {
      return { data: null, error: handleSupabaseError(userError, 'getUserStorageInfo') };
    }

    // Get detailed usage by category
    const { data: usageData, error: usageError } = await supabase
      .from(TABLES.STORAGE_USAGE)
      .select('*')
      .eq('user_id', userId);

    if (usageError) {
      return { data: null, error: handleSupabaseError(usageError, 'getUserStorageInfo') };
    }

    return {
      data: {
        used: userData.storage_used,
        quota: userData.storage_quota,
        usage: usageData,
      },
      error: null,
    };
  } catch (error) {
    return { data: null, error: handleSupabaseError(error, 'getUserStorageInfo') };
  }
};

// Helper function to create backup session
export const createBackupSession = async (sessionData: {
  userId: string;
  sessionType: 'manual' | 'automatic';
  totalFiles: number;
  totalBytes: number;
}): Promise<{ data: BackupSession | null; error: string | null }> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.BACKUP_SESSIONS)
      .insert({
        user_id: sessionData.userId,
        session_type: sessionData.sessionType,
        total_files: sessionData.totalFiles,
        total_bytes: sessionData.totalBytes,
      })
      .select()
      .single();

    if (error) {
      return { data: null, error: handleSupabaseError(error, 'createBackupSession') };
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error: handleSupabaseError(error, 'createBackupSession') };
  }
};

// Helper function to update backup session progress
export const updateBackupSessionProgress = async (
  sessionId: string,
  progress: {
    processedFiles?: number;
    failedFiles?: number;
    processedBytes?: number;
    status?: 'running' | 'completed' | 'failed' | 'cancelled';
    errorMessage?: string;
  }
): Promise<{ error: string | null }> => {
  try {
    const updateData: any = { ...progress };
    
    if (progress.status === 'completed' || progress.status === 'failed' || progress.status === 'cancelled') {
      updateData.completed_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from(TABLES.BACKUP_SESSIONS)
      .update(updateData)
      .eq('id', sessionId);

    if (error) {
      return { error: handleSupabaseError(error, 'updateBackupSessionProgress') };
    }

    return { error: null };
  } catch (error) {
    return { error: handleSupabaseError(error, 'updateBackupSessionProgress') };
  }
};

// Helper function to test Supabase connection
export const testSupabaseConnection = async (): Promise<{ success: boolean; error?: string }> => {
  try {
    // Test database connection
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('count', { count: 'exact', head: true });

    if (error) {
      return { success: false, error: handleSupabaseError(error, 'testSupabaseConnection') };
    }

    // Test storage connection
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets();

    if (storageError) {
      return { success: false, error: handleSupabaseError(storageError, 'testSupabaseConnection - Storage') };
    }

    console.log('✅ Supabase connection test successful');
    console.log(`📊 Database accessible, ${buckets?.length || 0} storage buckets available`);
    
    return { success: true };
  } catch (error) {
    return { success: false, error: handleSupabaseError(error, 'testSupabaseConnection') };
  }
};
