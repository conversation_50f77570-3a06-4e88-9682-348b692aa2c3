/**
 * Subscription Tier Configuration Manager
 * Handles tier configuration, feature management, and access control
 */

class SubscriptionTierConfig {
    constructor() {
        this.tiers = new Map();
        this.features = new Map();
        this.loadDefaultConfiguration();
    }

    /**
     * Load default tier configuration
     */
    loadDefaultConfiguration() {
        // Define available features
        this.features.set('contacts_backup', {
            name: 'Contacts Backup',
            description: 'Secure backup of your contacts',
            category: 'backup'
        });
        
        this.features.set('messages_backup', {
            name: 'Messages Backup',
            description: 'Secure backup of your messages',
            category: 'backup'
        });
        
        this.features.set('photos_backup', {
            name: 'Photos Backup',
            description: 'Secure backup of your photos',
            category: 'backup'
        });
        
        this.features.set('backup_encryption', {
            name: 'Backup Encryption',
            description: 'Encrypt backups for security',
            category: 'security'
        });
        
        this.features.set('backup_scheduling', {
            name: 'Backup Scheduling',
            description: 'Automated backup scheduling',
            category: 'automation'
        });
        
        this.features.set('priority_support', {
            name: 'Priority Support',
            description: '24/7 priority customer support',
            category: 'support'
        });
        
        this.features.set('advanced_encryption', {
            name: 'Advanced Encryption',
            description: 'AES-256 encryption with custom keys',
            category: 'security'
        });
        
        this.features.set('multi_device_sync', {
            name: 'Multi-Device Sync',
            description: 'Sync backups across multiple devices',
            category: 'sync'
        });
        
        this.features.set('backup_history_extended', {
            name: 'Extended Backup History',
            description: 'Keep backup history for extended periods',
            category: 'storage'
        });

        // Define subscription tiers - Individual Services
        this.tiers.set('contacts_only', {
            id: 'contacts_only',
            name: 'Contacts Only',
            description: 'Secure backup for your contacts',
            price: 99, // $0.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 1,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 10,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: true,
                messages_backup: false,
                photos_backup: false,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: false,
                multi_device_sync: false,
                backup_history_extended: false
            },
            color: '#28a745',
            popular: false
        });

        this.tiers.set('messages_only', {
            id: 'messages_only',
            name: 'Messages Only',
            description: 'Secure backup for your messages',
            price: 199, // $1.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 5,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 10,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: false,
                messages_backup: true,
                photos_backup: false,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: false,
                multi_device_sync: false,
                backup_history_extended: false
            },
            color: '#17a2b8',
            popular: false
        });

        this.tiers.set('photos_only', {
            id: 'photos_only',
            name: 'Photos Only',
            description: 'Secure backup for your photos',
            price: 499, // $4.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 50,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 10,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: false,
                messages_backup: false,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: false,
                multi_device_sync: false,
                backup_history_extended: false
            },
            color: '#fd7e14',
            popular: false
        });

        // Combination Services
        this.tiers.set('contacts_messages', {
            id: 'contacts_messages',
            name: 'Contacts + Messages',
            description: 'Backup contacts and messages together',
            price: 249, // $2.49
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 10,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 15,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: true,
                messages_backup: true,
                photos_backup: false,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: true,
                multi_device_sync: false,
                backup_history_extended: true
            },
            color: '#6f42c1',
            popular: false
        });

        this.tiers.set('contacts_photos', {
            id: 'contacts_photos',
            name: 'Contacts + Photos',
            description: 'Backup contacts and photos together',
            price: 549, // $5.49
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 60,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 15,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: true,
                messages_backup: false,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: true,
                multi_device_sync: false,
                backup_history_extended: true
            },
            color: '#e83e8c',
            popular: false
        });

        this.tiers.set('messages_photos', {
            id: 'messages_photos',
            name: 'Messages + Photos',
            description: 'Backup messages and photos together',
            price: 649, // $6.49
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 70,
                maxBackupsPerMonth: 50,
                maxRestoresPerMonth: 15,
                backupFrequencies: ['manual', 'daily'],
                backupHistoryDays: 30
            },
            features: {
                contacts_backup: false,
                messages_backup: true,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: false,
                advanced_encryption: true,
                multi_device_sync: false,
                backup_history_extended: true
            },
            color: '#20c997',
            popular: true
        });

        this.tiers.set('complete_backup', {
            id: 'complete_backup',
            name: 'Complete Backup',
            description: 'Full backup solution for all your data',
            price: 699, // $6.99
            currency: 'usd',
            interval: 'month',
            limits: {
                maxStorageGB: 100,
                maxBackupsPerMonth: -1, // unlimited
                maxRestoresPerMonth: -1, // unlimited
                backupFrequencies: ['manual', 'daily', 'weekly'],
                backupHistoryDays: 90
            },
            features: {
                contacts_backup: true,
                messages_backup: true,
                photos_backup: true,
                backup_encryption: true,
                backup_scheduling: true,
                priority_support: true,
                advanced_encryption: true,
                multi_device_sync: true,
                backup_history_extended: true
            },
            color: '#007bff',
            popular: false
        });
    }

    /**
     * Get all subscription tiers
     */
    getAllTiers() {
        return Array.from(this.tiers.values());
    }

    /**
     * Get tier by ID
     */
    getTier(tierId) {
        return this.tiers.get(tierId);
    }

    /**
     * Get tier features
     */
    getTierFeatures(tierId) {
        const tier = this.tiers.get(tierId);
        return tier ? tier.features : {};
    }

    /**
     * Get tier limits
     */
    getTierLimits(tierId) {
        const tier = this.tiers.get(tierId);
        return tier ? tier.limits : {};
    }

    /**
     * Check if tier has feature
     */
    hasFeature(tierId, featureKey) {
        const tier = this.tiers.get(tierId);
        return tier && tier.features[featureKey] === true;
    }

    /**
     * Get feature limit for tier
     */
    getFeatureLimit(tierId, limitKey) {
        const tier = this.tiers.get(tierId);
        return tier ? tier.limits[limitKey] : null;
    }

    /**
     * Compare tiers by price
     */
    compareTiers(tierA, tierB) {
        const tierAData = this.tiers.get(tierA);
        const tierBData = this.tiers.get(tierB);
        
        if (!tierAData || !tierBData) return 0;
        
        return tierAData.price - tierBData.price;
    }

    /**
     * Get upgrade path (higher priced tiers)
     */
    getUpgradePath(currentTier) {
        const currentTierData = this.tiers.get(currentTier);
        if (!currentTierData) return [];
        
        return Array.from(this.tiers.values())
            .filter(tier => tier.price > currentTierData.price)
            .sort((a, b) => a.price - b.price);
    }

    /**
     * Get downgrade path (lower priced tiers)
     */
    getDowngradePath(currentTier) {
        const currentTierData = this.tiers.get(currentTier);
        if (!currentTierData) return [];
        
        return Array.from(this.tiers.values())
            .filter(tier => tier.price < currentTierData.price)
            .sort((a, b) => b.price - a.price);
    }

    /**
     * Get tiers by category
     */
    getTiersByCategory() {
        const individual = [];
        const combination = [];
        
        for (const tier of this.tiers.values()) {
            const featureCount = [
                tier.features.contacts_backup,
                tier.features.messages_backup,
                tier.features.photos_backup
            ].filter(Boolean).length;
            
            if (featureCount === 1) {
                individual.push(tier);
            } else if (featureCount > 1) {
                combination.push(tier);
            }
        }
        
        return {
            individual: individual.sort((a, b) => a.price - b.price),
            combination: combination.sort((a, b) => a.price - b.price)
        };
    }

    /**
     * Calculate tier comparison
     */
    calculateTierComparison() {
        const tiers = this.getAllTiers();
        const features = Array.from(this.features.keys());
        
        const comparison = {
            tiers: tiers,
            features: features.map(key => ({
                key: key,
                ...this.features.get(key),
                availability: tiers.map(tier => ({
                    tierId: tier.id,
                    available: tier.features[key] || false
                }))
            }))
        };
        
        return comparison;
    }

    /**
     * Get tier pricing information
     */
    getTierPricing(tierId) {
        const tier = this.tiers.get(tierId);
        if (!tier) return null;
        
        return {
            id: tier.id,
            name: tier.name,
            price: tier.price,
            currency: tier.currency,
            interval: tier.interval,
            formattedPrice: tier.price === 0 ? 'Free' : `$${(tier.price / 100).toFixed(2)}/${tier.interval}`,
            annualPrice: tier.price * 12,
            formattedAnnualPrice: tier.price === 0 ? 'Free' : `$${(tier.price * 12 / 100).toFixed(2)}/year`
        };
    }

    /**
     * Validate tier configuration
     */
    validateTierConfig(tierConfig) {
        const required = ['id', 'name', 'price', 'limits', 'features'];
        const missing = required.filter(field => !tierConfig.hasOwnProperty(field));
        
        if (missing.length > 0) {
            throw new Error(`Missing required fields: ${missing.join(', ')}`);
        }
        
        // Validate limits
        const requiredLimits = ['maxStorageGB', 'maxBackupsPerMonth', 'maxRestoresPerMonth'];
        const missingLimits = requiredLimits.filter(limit => !tierConfig.limits.hasOwnProperty(limit));
        
        if (missingLimits.length > 0) {
            throw new Error(`Missing required limits: ${missingLimits.join(', ')}`);
        }
        
        return true;
    }

    /**
     * Add or update tier
     */
    updateTier(tierId, tierConfig) {
        this.validateTierConfig(tierConfig);
        this.tiers.set(tierId, { ...tierConfig, id: tierId });
        return this.tiers.get(tierId);
    }

    /**
     * Remove tier
     */
    removeTier(tierId) {
        return this.tiers.delete(tierId);
    }

    /**
     * Export configuration
     */
    exportConfiguration() {
        return {
            tiers: Object.fromEntries(this.tiers),
            features: Object.fromEntries(this.features),
            version: '1.0.0',
            exportedAt: new Date().toISOString()
        };
    }

    /**
     * Import configuration
     */
    importConfiguration(config) {
        if (config.tiers) {
            this.tiers = new Map(Object.entries(config.tiers));
        }
        
        if (config.features) {
            this.features = new Map(Object.entries(config.features));
        }
        
        return true;
    }

    /**
     * Get tier recommendations based on usage
     */
    getTierRecommendations(usage) {
        const recommendations = [];
        
        for (const [tierId, tier] of this.tiers) {
            let score = 0;
            let reasons = [];
            
            // Check storage usage
            if (usage.storageUsed <= tier.limits.maxStorageGB) {
                score += 10;
            } else {
                reasons.push(`Storage limit exceeded (${usage.storageUsed}GB > ${tier.limits.maxStorageGB}GB)`);
            }
            
            // Check backup frequency
            if (tier.limits.maxBackupsPerMonth === -1 || usage.backupCount <= tier.limits.maxBackupsPerMonth) {
                score += 10;
            } else {
                reasons.push(`Backup limit exceeded (${usage.backupCount} > ${tier.limits.maxBackupsPerMonth})`);
            }
            
            // Check restore frequency
            if (tier.limits.maxRestoresPerMonth === -1 || usage.restoreCount <= tier.limits.maxRestoresPerMonth) {
                score += 10;
            } else {
                reasons.push(`Restore limit exceeded (${usage.restoreCount} > ${tier.limits.maxRestoresPerMonth})`);
            }
            
            recommendations.push({
                tier: tier,
                score: score,
                suitable: score >= 20,
                reasons: reasons
            });
        }
        
        return recommendations.sort((a, b) => b.score - a.score);
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.SubscriptionTierConfig = SubscriptionTierConfig;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionTierConfig;
}