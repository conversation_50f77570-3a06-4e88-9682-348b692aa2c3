#!/usr/bin/env node

/**
 * Quick verification script for SafeKeep Stripe Integration
 * Tests basic functionality and configuration
 */

const StripeConfig = require('./stripe-config.js');
const StripeServer = require('./stripe-server.js');

async function verifyStripeIntegration() {
    console.log('🔍 Verifying SafeKeep Stripe Integration...');
    console.log('=' .repeat(50));
    
    let server = null;
    let allTestsPassed = true;
    
    try {
        // Test 1: Configuration Validation
        console.log('\n1️⃣ Testing Configuration...');
        const config = new StripeConfig();
        const validation = config.validateConfig();
        
        if (validation.valid) {
            console.log('   ✅ Configuration is valid');
        } else {
            console.log('   ❌ Configuration errors:');
            validation.errors.forEach(error => console.log(`      • ${error}`));
            allTestsPassed = false;
        }
        
        // Test 2: Subscription Tiers
        console.log('\n2️⃣ Testing Subscription Tiers...');
        const tiers = config.getSubscriptionTiers();
        const requiredTiers = ['free', 'basic', 'premium'];
        
        let tiersValid = true;
        requiredTiers.forEach(tierId => {
            if (tiers[tierId]) {
                console.log(`   ✅ ${tierId} tier configured`);
            } else {
                console.log(`   ❌ ${tierId} tier missing`);
                tiersValid = false;
            }
        });
        
        if (!tiersValid) allTestsPassed = false;
        
        // Test 3: Server Startup
        console.log('\n3️⃣ Testing Server Startup...');
        server = new StripeServer(config);
        
        const httpServer = await new Promise((resolve, reject) => {
            const srv = server.start(3002); // Use different port for testing
            srv.on('listening', () => resolve(srv));
            srv.on('error', reject);
            
            // Timeout after 5 seconds
            setTimeout(() => reject(new Error('Server startup timeout')), 5000);
        });
        
        console.log('   ✅ Server started successfully');
        
        // Test 4: API Endpoints
        console.log('\n4️⃣ Testing API Endpoints...');
        
        // Test status endpoint
        try {
            const response = await fetch('http://localhost:3002/api/stripe/status');
            const status = await response.json();
            
            if (status.status === 'OK') {
                console.log('   ✅ Status endpoint working');
            } else {
                console.log('   ❌ Status endpoint returned unexpected response');
                allTestsPassed = false;
            }
        } catch (error) {
            console.log('   ❌ Status endpoint failed:', error.message);
            allTestsPassed = false;
        }
        
        // Test customer creation endpoint
        try {
            const response = await fetch('http://localhost:3002/api/stripe/create-customer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: '<EMAIL>',
                    name: 'Verification Test User'
                })
            });
            
            const customer = await response.json();
            
            if (customer.id && customer.email === '<EMAIL>') {
                console.log('   ✅ Customer creation endpoint working');
            } else {
                console.log('   ❌ Customer creation endpoint failed');
                allTestsPassed = false;
            }
        } catch (error) {
            console.log('   ❌ Customer creation endpoint failed:', error.message);
            allTestsPassed = false;
        }
        
        // Test payment intent creation endpoint
        try {
            const response = await fetch('http://localhost:3002/api/stripe/create-payment-intent', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    amount: 999,
                    currency: 'usd',
                    description: 'Verification test payment'
                })
            });
            
            const paymentIntent = await response.json();
            
            if (paymentIntent.client_secret && paymentIntent.payment_intent_id) {
                console.log('   ✅ Payment intent creation endpoint working');
            } else {
                console.log('   ❌ Payment intent creation endpoint failed');
                allTestsPassed = false;
            }
        } catch (error) {
            console.log('   ❌ Payment intent creation endpoint failed:', error.message);
            allTestsPassed = false;
        }
        
        // Test 5: Demo Data Operations
        console.log('\n5️⃣ Testing Demo Data Operations...');
        
        const demoData = server.getDemoData();
        console.log(`   ✅ Demo data accessible (${demoData.customers.length} customers)`);
        
        server.resetDemoData();
        const resetData = server.getDemoData();
        
        if (resetData.customers.length === 0) {
            console.log('   ✅ Demo data reset working');
        } else {
            console.log('   ❌ Demo data reset failed');
            allTestsPassed = false;
        }
        
        // Test 6: Webhook Processing
        console.log('\n6️⃣ Testing Webhook Processing...');
        
        try {
            const testWebhook = {
                type: 'customer.created',
                data: {
                    object: {
                        id: 'cus_test_webhook',
                        email: '<EMAIL>'
                    }
                }
            };
            
            const response = await fetch('http://localhost:3002/api/stripe/webhook', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Stripe-Signature': 'test_signature'
                },
                body: JSON.stringify(testWebhook)
            });
            
            const result = await response.json();
            
            if (result.received === true) {
                console.log('   ✅ Webhook processing working');
            } else {
                console.log('   ❌ Webhook processing failed');
                allTestsPassed = false;
            }
        } catch (error) {
            console.log('   ❌ Webhook processing failed:', error.message);
            allTestsPassed = false;
        }
        
    } catch (error) {
        console.error('\n💥 Verification failed with error:', error.message);
        allTestsPassed = false;
    } finally {
        // Clean up server
        if (server) {
            server.stop();
        }
    }
    
    // Final Results
    console.log('\n' + '=' .repeat(50));
    if (allTestsPassed) {
        console.log('🎉 ALL TESTS PASSED! Stripe integration is ready.');
        console.log('\n📋 Next steps:');
        console.log('1. Start the Stripe server: node start-stripe-server.js');
        console.log('2. Start the web demo: node server.js');
        console.log('3. Open stripe-integration-test.html for full testing');
        console.log('4. Test payment flows with test cards');
    } else {
        console.log('❌ SOME TESTS FAILED! Please check the errors above.');
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Verify Stripe keys in stripe-config.js');
        console.log('2. Check network connectivity');
        console.log('3. Ensure no port conflicts');
        console.log('4. Review error messages above');
    }
    console.log('=' .repeat(50));
    
    process.exit(allTestsPassed ? 0 : 1);
}

// Add fetch polyfill for Node.js
if (typeof fetch === 'undefined') {
    global.fetch = require('node-fetch');
}

// Run verification
verifyStripeIntegration().catch(error => {
    console.error('💥 Verification script failed:', error);
    process.exit(1);
});