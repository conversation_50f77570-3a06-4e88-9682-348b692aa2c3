{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AAqBO,MAAM,iBAAiB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACzF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,QAAQ,GAAgB;YAC5B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,0BAA0B;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC;QACF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,OAAO;KACR;IAID,IAAI;QAEF,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,qBAAqB;YACzB,KAAK,EAAE,yBAAyB;SACjC,CAAC;QACF,IAAI,EAAE,CAAC;KACR;IAAC,OAAO,KAAK,EAAE;QACd,MAAM,QAAQ,GAAgB;YAC5B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,sBAAsB;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC;SACF,CAAC;QACF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAChC;AACH,CAAC,CAAC;AArCW,QAAA,iBAAiB,qBAqC5B;AAMK,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,EAAE;IAC1C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,MAAM,QAAQ,GAAgB;gBAC5B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,cAAc;oBACpB,OAAO,EAAE,yBAAyB;oBAClC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;YACF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/B,OAAO;SACR;QAID,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;YAC1B,MAAM,QAAQ,GAAgB;gBAC5B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,GAAG,IAAI,gBAAgB;oBAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;YACF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/B,OAAO;SACR;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAhCW,QAAA,WAAW,eAgCtB;AAMK,MAAM,aAAa,GAAG,CAAC,GAAY,EAAiB,EAAE;IAC3D,OAAO,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI,CAAC;AAC9B,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB"}