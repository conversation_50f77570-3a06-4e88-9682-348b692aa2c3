/**
 * Usage Quota Manager for SafeKeep Web Demo
 * Handles usage tracking, quota enforcement, and limit notifications
 */

class UsageQuotaManager {
    constructor(subscriptionManager, tierConfig) {
        this.subscriptionManager = subscriptionManager;
        this.tierConfig = tierConfig;
        this.usageData = new Map();
        this.quotaWarnings = new Map();
        this.usageListeners = new Set();
        
        // Initialize usage tracking
        this.initializeUsageTracking();
    }

    /**
     * Initialize usage tracking system
     */
    initializeUsageTracking() {
        // Set up periodic usage checks
        this.usageCheckInterval = setInterval(() => {
            this.checkUsageLimits();
        }, 60000); // Check every minute
        
        // Set up monthly reset
        this.setupMonthlyReset();
        
        console.log('✅ Usage Quota Manager initialized');
    }

    /**
     * Setup monthly usage reset
     */
    setupMonthlyReset() {
        const now = new Date();
        const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        const timeUntilReset = nextMonth.getTime() - now.getTime();
        
        setTimeout(() => {
            this.resetMonthlyUsage();
            // Set up recurring monthly reset
            setInterval(() => {
                this.resetMonthlyUsage();
            }, 30 * 24 * 60 * 60 * 1000); // 30 days
        }, timeUntilReset);
    }

    /**
     * Track usage for a specific action
     */
    trackUsage(userId, usageType, amount = 1, metadata = {}) {
        const subscription = this.subscriptionManager.getCurrentSubscription();
        if (!subscription) {
            console.warn('No active subscription found for usage tracking');
            return false;
        }

        // Get current usage
        const currentUsage = this.getUserUsage(userId);
        
        // Update usage based on type
        switch (usageType) {
            case 'storage':
                currentUsage.storageUsed += amount;
                break;
            case 'backup':
                currentUsage.backupCount += amount;
                currentUsage.lastBackupAt = new Date();
                break;
            case 'restore':
                currentUsage.restoreCount += amount;
                currentUsage.lastRestoreAt = new Date();
                break;
            case 'api_call':
                currentUsage.apiCalls += amount;
                break;
            case 'bandwidth':
                currentUsage.bandwidthUsed += amount;
                break;
            default:
                console.warn(`Unknown usage type: ${usageType}`);
                return false;
        }

        // Add metadata
        if (metadata) {
            currentUsage.metadata = currentUsage.metadata || {};
            currentUsage.metadata[usageType] = {
                ...currentUsage.metadata[usageType],
                ...metadata,
                lastUpdated: new Date()
            };
        }

        // Save usage data
        this.saveUserUsage(userId, currentUsage);
        
        // Check limits after tracking
        const limitCheck = this.checkUsageLimit(userId, usageType);
        
        // Notify listeners
        this.notifyUsageListeners(userId, usageType, amount, currentUsage, limitCheck);
        
        // Update subscription manager
        if (this.subscriptionManager.trackUsage) {
            this.subscriptionManager.trackUsage(usageType, amount);
        }
        
        return limitCheck.allowed;
    }

    /**
     * Get user usage data
     */
    getUserUsage(userId) {
        if (!this.usageData.has(userId)) {
            this.usageData.set(userId, this.createEmptyUsage());
        }
        return this.usageData.get(userId);
    }

    /**
     * Create empty usage object
     */
    createEmptyUsage() {
        return {
            storageUsed: 0,
            backupCount: 0,
            restoreCount: 0,
            apiCalls: 0,
            bandwidthUsed: 0,
            periodStart: new Date(),
            periodEnd: this.getNextPeriodEnd(),
            lastBackupAt: null,
            lastRestoreAt: null,
            metadata: {}
        };
    }

    /**
     * Get next period end date
     */
    getNextPeriodEnd() {
        const now = new Date();
        return new Date(now.getFullYear(), now.getMonth() + 1, 1);
    }

    /**
     * Save user usage data
     */
    saveUserUsage(userId, usage) {
        this.usageData.set(userId, usage);
        
        // Persist to localStorage for demo
        localStorage.setItem(`usage_quota_${userId}`, JSON.stringify({
            ...usage,
            periodStart: usage.periodStart.toISOString(),
            periodEnd: usage.periodEnd.toISOString(),
            lastBackupAt: usage.lastBackupAt?.toISOString(),
            lastRestoreAt: usage.lastRestoreAt?.toISOString()
        }));
    }

    /**
     * Load user usage data
     */
    loadUserUsage(userId) {
        const saved = localStorage.getItem(`usage_quota_${userId}`);
        if (saved) {
            const usage = JSON.parse(saved);
            usage.periodStart = new Date(usage.periodStart);
            usage.periodEnd = new Date(usage.periodEnd);
            usage.lastBackupAt = usage.lastBackupAt ? new Date(usage.lastBackupAt) : null;
            usage.lastRestoreAt = usage.lastRestoreAt ? new Date(usage.lastRestoreAt) : null;
            this.usageData.set(userId, usage);
            return usage;
        }
        return this.createEmptyUsage();
    }

    /**
     * Check usage limit for specific type
     */
    checkUsageLimit(userId, usageType) {
        const subscription = this.subscriptionManager.getCurrentSubscription();
        if (!subscription) {
            return { allowed: false, reason: 'No active subscription' };
        }

        const usage = this.getUserUsage(userId);
        const limits = subscription.features;
        
        switch (usageType) {
            case 'storage':
                const storageLimit = limits.maxStorageGB;
                const storageUsed = usage.storageUsed;
                const storageAllowed = storageUsed < storageLimit;
                
                return {
                    allowed: storageAllowed,
                    current: storageUsed,
                    limit: storageLimit,
                    percentage: (storageUsed / storageLimit) * 100,
                    remaining: Math.max(0, storageLimit - storageUsed),
                    reason: storageAllowed ? null : `Storage limit exceeded (${storageUsed}GB / ${storageLimit}GB)`
                };
                
            case 'backup':
                const backupLimit = limits.maxBackupsPerMonth;
                const backupCount = usage.backupCount;
                const backupAllowed = backupLimit === -1 || backupCount < backupLimit;
                
                return {
                    allowed: backupAllowed,
                    current: backupCount,
                    limit: backupLimit,
                    percentage: backupLimit === -1 ? 0 : (backupCount / backupLimit) * 100,
                    remaining: backupLimit === -1 ? Infinity : Math.max(0, backupLimit - backupCount),
                    reason: backupAllowed ? null : `Backup limit exceeded (${backupCount} / ${backupLimit})`
                };
                
            case 'restore':
                const restoreLimit = limits.maxRestoresPerMonth;
                const restoreCount = usage.restoreCount;
                const restoreAllowed = restoreLimit === -1 || restoreCount < restoreLimit;
                
                return {
                    allowed: restoreAllowed,
                    current: restoreCount,
                    limit: restoreLimit,
                    percentage: restoreLimit === -1 ? 0 : (restoreCount / restoreLimit) * 100,
                    remaining: restoreLimit === -1 ? Infinity : Math.max(0, restoreLimit - restoreCount),
                    reason: restoreAllowed ? null : `Restore limit exceeded (${restoreCount} / ${restoreLimit})`
                };
                
            default:
                return { allowed: true, reason: 'Unknown usage type' };
        }
    }

    /**
     * Check all usage limits
     */
    checkUsageLimits(userId = null) {
        const targetUserId = userId || this.subscriptionManager.currentUser;
        if (!targetUserId) return {};
        
        const results = {
            storage: this.checkUsageLimit(targetUserId, 'storage'),
            backup: this.checkUsageLimit(targetUserId, 'backup'),
            restore: this.checkUsageLimit(targetUserId, 'restore')
        };
        
        // Check for warnings
        this.checkQuotaWarnings(targetUserId, results);
        
        return results;
    }

    /**
     * Check for quota warnings
     */
    checkQuotaWarnings(userId, limits) {
        const warnings = [];
        
        Object.entries(limits).forEach(([type, limit]) => {
            if (limit.percentage >= 80 && limit.percentage < 100) {
                warnings.push({
                    type: type,
                    level: 'warning',
                    message: `${type} usage is at ${limit.percentage.toFixed(1)}% of limit`,
                    current: limit.current,
                    limit: limit.limit
                });
            } else if (limit.percentage >= 100) {
                warnings.push({
                    type: type,
                    level: 'error',
                    message: `${type} limit exceeded`,
                    current: limit.current,
                    limit: limit.limit
                });
            }
        });
        
        this.quotaWarnings.set(userId, warnings);
        return warnings;
    }

    /**
     * Get quota warnings for user
     */
    getQuotaWarnings(userId) {
        return this.quotaWarnings.get(userId) || [];
    }

    /**
     * Reset monthly usage
     */
    resetMonthlyUsage(userId = null) {
        if (userId) {
            const usage = this.getUserUsage(userId);
            usage.backupCount = 0;
            usage.restoreCount = 0;
            usage.apiCalls = 0;
            usage.bandwidthUsed = 0;
            usage.periodStart = new Date();
            usage.periodEnd = this.getNextPeriodEnd();
            this.saveUserUsage(userId, usage);
        } else {
            // Reset for all users
            for (const [uid, usage] of this.usageData) {
                usage.backupCount = 0;
                usage.restoreCount = 0;
                usage.apiCalls = 0;
                usage.bandwidthUsed = 0;
                usage.periodStart = new Date();
                usage.periodEnd = this.getNextPeriodEnd();
                this.saveUserUsage(uid, usage);
            }
        }
        
        console.log('🔄 Monthly usage reset completed');
    }

    /**
     * Get usage statistics
     */
    getUsageStatistics(userId) {
        const usage = this.getUserUsage(userId);
        const limits = this.checkUsageLimits(userId);
        
        return {
            current: usage,
            limits: limits,
            warnings: this.getQuotaWarnings(userId),
            period: {
                start: usage.periodStart,
                end: usage.periodEnd,
                daysRemaining: Math.ceil((usage.periodEnd - new Date()) / (1000 * 60 * 60 * 24))
            },
            recommendations: this.getUsageRecommendations(userId, usage, limits)
        };
    }

    /**
     * Get usage recommendations
     */
    getUsageRecommendations(userId, usage, limits) {
        const recommendations = [];
        
        // Storage recommendations
        if (limits.storage.percentage > 80) {
            recommendations.push({
                type: 'storage',
                priority: limits.storage.percentage > 95 ? 'high' : 'medium',
                message: 'Consider upgrading your plan for more storage',
                action: 'upgrade'
            });
        }
        
        // Backup recommendations
        if (limits.backup.percentage > 80) {
            recommendations.push({
                type: 'backup',
                priority: limits.backup.percentage > 95 ? 'high' : 'medium',
                message: 'You\'re approaching your monthly backup limit',
                action: 'optimize'
            });
        }
        
        // Restore recommendations
        if (limits.restore.percentage > 80) {
            recommendations.push({
                type: 'restore',
                priority: limits.restore.percentage > 95 ? 'high' : 'medium',
                message: 'Consider upgrading for unlimited restores',
                action: 'upgrade'
            });
        }
        
        return recommendations;
    }

    /**
     * Add usage listener
     */
    addUsageListener(callback) {
        this.usageListeners.add(callback);
    }

    /**
     * Remove usage listener
     */
    removeUsageListener(callback) {
        this.usageListeners.delete(callback);
    }

    /**
     * Notify usage listeners
     */
    notifyUsageListeners(userId, usageType, amount, currentUsage, limitCheck) {
        this.usageListeners.forEach(callback => {
            try {
                callback({
                    userId,
                    usageType,
                    amount,
                    currentUsage,
                    limitCheck,
                    timestamp: new Date()
                });
            } catch (error) {
                console.error('Error in usage listener:', error);
            }
        });
    }

    /**
     * Simulate usage for demo
     */
    simulateUsage(userId, scenario = 'normal') {
        const scenarios = {
            normal: {
                storage: 0.1,
                backup: 1,
                restore: 0
            },
            heavy: {
                storage: 0.5,
                backup: 3,
                restore: 1
            },
            light: {
                storage: 0.05,
                backup: 1,
                restore: 0
            }
        };
        
        const usage = scenarios[scenario] || scenarios.normal;
        
        if (usage.storage > 0) {
            this.trackUsage(userId, 'storage', usage.storage);
        }
        
        if (usage.backup > 0) {
            this.trackUsage(userId, 'backup', usage.backup);
        }
        
        if (usage.restore > 0) {
            this.trackUsage(userId, 'restore', usage.restore);
        }
        
        console.log(`📊 Simulated ${scenario} usage for user ${userId}`);
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.usageCheckInterval) {
            clearInterval(this.usageCheckInterval);
        }
        
        this.usageListeners.clear();
        console.log('🧹 Usage Quota Manager cleaned up');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.UsageQuotaManager = UsageQuotaManager;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UsageQuotaManager;
}