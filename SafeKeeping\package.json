{"name": "safekeep-test-server", "version": "1.0.0", "description": "Test server for SafeKeep Stripe integration", "main": "test-server.js", "scripts": {"start": "node test-server.js", "dev": "nodemon test-server.js", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "stripe": "^14.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["safekeep", "stripe", "payments", "test"], "author": "SafeKeep Team", "license": "MIT"}