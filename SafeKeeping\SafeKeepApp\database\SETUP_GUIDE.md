# SafeKeep Supabase Setup Guide

This guide will help you set up your Supabase project for the SafeKeep app.

## 🚀 Quick Setup Steps

### 1. Database Schema Setup

1. **Open Supabase Dashboard**
   - Go to [https://babgywcvqyclvxdkckkd.supabase.co](https://babgywcvqyclvxdkckkd.supabase.co)
   - Navigate to **SQL Editor**

2. **Run the Schema Script**
   - Copy the contents of `schema.sql`
   - Paste into the SQL Editor
   - Click **Run** to execute all commands

### 2. Storage Buckets Setup

1. **Navigate to Storage**
   - Go to **Storage** in the left sidebar
   - Click **Create Bucket**

2. **Create user-data Bucket**
   ```
   Bucket Name: user-data
   Public: false (private)
   File size limit: 50MB
   Allowed MIME types: application/octet-stream
   ```

3. **Create thumbnails Bucket**
   ```
   Bucket Name: thumbnails
   Public: true
   File size limit: 5MB
   Allowed MIME types: image/jpeg, image/png, image/webp
   ```

### 3. Storage Policies Setup

1. **Go to Storage > Policies**

2. **Create Policy for user-data bucket**
   - **Policy Name**: "Users can manage own encrypted files"
   - **Operation**: All operations
   - **Target Roles**: authenticated
   - **Policy Definition**:
   ```sql
   bucket_id = 'user-data' AND (storage.foldername(name))[1] = auth.uid()::text
   ```

3. **Create Policy for thumbnails bucket**
   - **Policy Name**: "Users can manage own thumbnails"
   - **Operation**: All operations
   - **Target Roles**: authenticated
   - **Policy Definition**:
   ```sql
   bucket_id = 'thumbnails' AND (storage.foldername(name))[1] = auth.uid()::text
   ```

### 4. Authentication Settings

1. **Go to Authentication > Settings**

2. **Configure Email Settings**
   - Enable **Email confirmations** (recommended)
   - Set **Site URL**: `safekeep://` (for mobile deep links)
   - Add **Redirect URLs**:
     - `safekeep://reset-password`
     - `safekeep://confirm-email`

3. **Configure Auth Providers**
   - **Email**: Enabled ✅
   - **Phone**: Optional
   - **OAuth**: Optional (Google, Apple, etc.)

### 5. API Settings

1. **Go to Settings > API**

2. **Verify Configuration**
   - **Project URL**: `https://babgywcvqyclvxdkckkd.supabase.co`
   - **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` ✅
   - **Service Role Key**: Keep this secret! (for admin operations)

## 📊 Database Schema Overview

### Tables Created:

1. **users** - User profiles and settings
   - Extends Supabase auth.users
   - Storage quota and usage tracking
   - Backup preferences

2. **file_metadata** - Encrypted file information
   - File details and encryption keys
   - Category organization (photo/contact/message)
   - Upload tracking

3. **backup_sessions** - Backup history
   - Session tracking and progress
   - Success/failure statistics
   - Error logging

4. **storage_usage** - Storage analytics
   - Per-category usage tracking
   - Real-time usage updates
   - Quota monitoring

5. **sync_status** - Synchronization state
   - Real-time sync tracking
   - Pending operations
   - Error handling

### Security Features:

- **Row Level Security (RLS)** - Users can only access their own data
- **Automatic triggers** - Storage usage updates automatically
- **User creation handling** - New users get proper initialization
- **Secure policies** - Fine-grained access control

## 🔐 Security Configuration

### Row Level Security Policies:

All tables have RLS enabled with policies ensuring:
- Users can only see their own data
- Proper authentication required
- No cross-user data access

### Storage Security:

- **Private buckets** for encrypted user data
- **Path-based access control** using user IDs
- **MIME type restrictions** for security
- **File size limits** to prevent abuse

## 🧪 Testing Your Setup

### 1. Test Database Connection

Run this query in SQL Editor to verify setup:

```sql
-- Check if all tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status');
```

Should return 5 rows.

### 2. Test Storage Buckets

1. Go to **Storage**
2. Verify both buckets exist:
   - `user-data` (private)
   - `thumbnails` (public)

### 3. Test Authentication

1. Go to **Authentication > Users**
2. Try creating a test user
3. Check if user appears in both:
   - Auth users table
   - Public users table (via trigger)

## 🚨 Important Security Notes

### Environment Variables:

Never commit these to version control:
- **SUPABASE_URL**: `https://babgywcvqyclvxdkckkd.supabase.co`
- **SUPABASE_ANON_KEY**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **SUPABASE_SERVICE_KEY**: Keep this secret!

### Production Checklist:

- [ ] RLS enabled on all tables ✅
- [ ] Storage policies configured ✅
- [ ] Email confirmation enabled
- [ ] Rate limiting configured
- [ ] Backup strategy in place
- [ ] Monitoring alerts set up

## 📱 Mobile App Configuration

The React Native app is already configured with your credentials:

```typescript
// src/config/supabase.ts
const supabaseUrl = 'https://babgywcvqyclvxdkckkd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
```

## 🔄 Real-time Features

Your setup includes real-time capabilities:

- **Live backup progress** - Real-time session updates
- **Storage usage updates** - Instant quota tracking
- **Sync status monitoring** - Live sync state
- **File upload progress** - Real-time progress bars

## 📞 Support

If you encounter issues:

1. **Check Supabase Logs** - Dashboard > Logs
2. **Verify Policies** - Ensure RLS policies are correct
3. **Test Connections** - Use SQL Editor to test queries
4. **Check Storage** - Verify bucket permissions

## 🎉 Next Steps

After setup is complete:

1. **Test the mobile app** with real authentication
2. **Try uploading encrypted files** to storage
3. **Monitor real-time updates** in the dashboard
4. **Set up production monitoring** and alerts

Your SafeKeep app is now ready with a secure, scalable Supabase backend! 🚀
