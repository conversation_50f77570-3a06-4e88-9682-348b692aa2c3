# RLS Policy Fix Guide

This guide explains how to fix the SQL syntax error related to RLS (Row-Level Security) policies in the SafeKeep application.

## The Issue

The error occurs because PostgreSQL's `CREATE POLICY` statement doesn't support the `IF NOT EXISTS` clause, resulting in this error:

```
ERROR: 42601: syntax error at or near "NOT"
LINE 2: CREATE POLICY IF NOT EXISTS "Users can view own profile"
                      ^
```

## Solution Files

1. **fix_policy_syntax.sql**: A SQL script that creates a helper function to safely create policies by dropping them first if they exist.

2. **fixRlsPolicies.ts**: A TypeScript utility that runs the fix script through Supabase.

3. **verifyRlsPolicies.ts**: A TypeScript utility to verify that policies are correctly applied.

4. **upload_fix_script.js**: A Node.js script to upload the fix script to Supabase storage.

## How to Fix the Issue

### Option 1: Run the Fix Script Locally

1. Connect to your Supabase PostgreSQL database using psql or another SQL client.
2. Run the contents of `fix_policy_syntax.sql` directly.

### Option 2: Use the Provided Utilities

1. Upload the fix script to <PERSON>pabase:

```bash
cd SafeKeepApp
node database/upload_fix_script.js
```

2. In your application, import and call the fix function:

```typescript
import { fixRlsPolicies } from './utils/fixRlsPolicies';

// Call the function when needed
const result = await fixRlsPolicies();
console.log(result.message);
```

3. Verify the policies are working:

```typescript
import { verifyRlsPolicies, testRlsPolicies } from './utils/verifyRlsPolicies';

// Verify policy configuration
const verifyResult = await verifyRlsPolicies();
console.log(verifyResult.message);

// Test policy enforcement
const testResult = await testRlsPolicies();
console.log(testResult.message);
```

## How the Fix Works

The solution creates a PL/pgSQL function called `safe_create_policy` that:

1. Drops the policy if it already exists
2. Creates a new policy with the correct syntax
3. Handles different policy types (SELECT, INSERT, UPDATE, DELETE)

This approach avoids the need for `IF NOT EXISTS` while still ensuring policies are created without errors.

## Preventing Future Issues

When creating RLS policies in PostgreSQL:

1. Always use the correct syntax: `CREATE POLICY policy_name ON table_name FOR command USING (expression)` 
2. Drop existing policies first with `DROP POLICY IF EXISTS policy_name ON table_name`
3. Use helper functions like the one provided to manage policy creation safely

## Additional Resources

- [PostgreSQL RLS Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [Supabase RLS Guide](https://supabase.com/docs/guides/auth/row-level-security)