-- SafeKeep Database Schema Migration
-- Creates core tables for user data, file metadata, backup sessions, and storage usage

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY DEFAULT auth.uid(),
  email TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_login_at TIMESTAMPTZ DEFAULT NOW(),
  storage_used BIGINT DEFAULT 0,
  storage_quota BIGINT DEFAULT 5368709120, -- 5GB default (5 * 1024 * 1024 * 1024)
  encryption_key_id TEXT,
  subscription_tier TEXT DEFAULT 'basic' CHECK (subscription_tier IN ('basic', 'premium', 'family')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'past_due')),
  subscription_expires_at TIMESTAMPTZ,
  stripe_customer_id TEXT UNIQUE,
  backup_settings JSONB DEFAULT '{"auto_backup": true, "wifi_only": true, "frequency": "daily"}'::jsonb,
  
  -- Constraints
  CONSTRAINT valid_storage_quota CHECK (storage_quota > 0),
  CONSTRAINT valid_storage_used CHECK (storage_used >= 0),
  CONSTRAINT storage_within_quota CHECK (storage_used <= storage_quota)
);

-- Create indexes for users table
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_subscription_tier ON public.users(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_users_stripe_customer_id ON public.users(stripe_customer_id);

-- Create file_metadata table
CREATE TABLE IF NOT EXISTS public.file_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  original_name TEXT NOT NULL,
  encrypted_name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT NOT NULL CHECK (size > 0),
  encrypted_size BIGINT NOT NULL CHECK (encrypted_size > 0),
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  last_modified TIMESTAMPTZ DEFAULT NOW(),
  category TEXT NOT NULL CHECK (category IN ('photo', 'contact', 'message')),
  hash TEXT NOT NULL, -- SHA-256 hash for deduplication
  encryption_iv TEXT NOT NULL,
  encryption_salt TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  is_backed_up BOOLEAN DEFAULT true,
  device_id TEXT,
  sync_status TEXT DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'failed')),
  
  -- Ensure unique hash per user for deduplication
  UNIQUE(user_id, hash)
);

-- Create indexes for file_metadata table
CREATE INDEX IF NOT EXISTS idx_file_metadata_user_id ON public.file_metadata(user_id);
CREATE INDEX IF NOT EXISTS idx_file_metadata_category ON public.file_metadata(category);
CREATE INDEX IF NOT EXISTS idx_file_metadata_uploaded_at ON public.file_metadata(uploaded_at DESC);
CREATE INDEX IF NOT EXISTS idx_file_metadata_hash ON public.file_metadata(hash);
CREATE INDEX IF NOT EXISTS idx_file_metadata_sync_status ON public.file_metadata(sync_status);

-- Create backup_sessions table
CREATE TABLE IF NOT EXISTS public.backup_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  session_type TEXT NOT NULL CHECK (session_type IN ('manual', 'automatic')),
  status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  total_files INTEGER DEFAULT 0 CHECK (total_files >= 0),
  processed_files INTEGER DEFAULT 0 CHECK (processed_files >= 0),
  failed_files INTEGER DEFAULT 0 CHECK (failed_files >= 0),
  total_bytes BIGINT DEFAULT 0 CHECK (total_bytes >= 0),
  processed_bytes BIGINT DEFAULT 0 CHECK (processed_bytes >= 0),
  error_message TEXT,
  device_id TEXT,
  
  -- Ensure processed files don't exceed total
  CONSTRAINT valid_processed_files CHECK (processed_files <= total_files),
  CONSTRAINT valid_failed_files CHECK (failed_files <= total_files),
  CONSTRAINT valid_processed_bytes CHECK (processed_bytes <= total_bytes),
  -- Completed sessions must have completion time
  CONSTRAINT completed_sessions_have_end_time CHECK (
    (status IN ('completed', 'failed', 'cancelled') AND completed_at IS NOT NULL) OR
    (status = 'running' AND completed_at IS NULL)
  )
);

-- Create indexes for backup_sessions table
CREATE INDEX IF NOT EXISTS idx_backup_sessions_user_id ON public.backup_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_backup_sessions_status ON public.backup_sessions(status);
CREATE INDEX IF NOT EXISTS idx_backup_sessions_started_at ON public.backup_sessions(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_backup_sessions_type ON public.backup_sessions(session_type);

-- Create storage_usage table for detailed usage tracking
CREATE TABLE IF NOT EXISTS public.storage_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  category TEXT NOT NULL CHECK (category IN ('photo', 'contact', 'message', 'other')),
  bytes_used BIGINT DEFAULT 0 CHECK (bytes_used >= 0),
  file_count INTEGER DEFAULT 0 CHECK (file_count >= 0),
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure one record per user per category
  UNIQUE(user_id, category)
);

-- Create indexes for storage_usage table
CREATE INDEX IF NOT EXISTS idx_storage_usage_user_id ON public.storage_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_storage_usage_category ON public.storage_usage(category);

-- Create sync_status table for device synchronization
CREATE TABLE IF NOT EXISTS public.sync_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  device_name TEXT,
  last_sync_at TIMESTAMPTZ DEFAULT NOW(),
  sync_version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  
  -- Ensure one record per user per device
  UNIQUE(user_id, device_id)
);

-- Create indexes for sync_status table
CREATE INDEX IF NOT EXISTS idx_sync_status_user_id ON public.sync_status(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_status_device_id ON public.sync_status(device_id);
CREATE INDEX IF NOT EXISTS idx_sync_status_active ON public.sync_status(is_active);

-- Add comments for documentation
COMMENT ON TABLE public.users IS 'Extended user profiles with subscription and storage information';
COMMENT ON TABLE public.file_metadata IS 'Metadata for all encrypted files stored in cloud storage';
COMMENT ON TABLE public.backup_sessions IS 'Tracking information for backup operations';
COMMENT ON TABLE public.storage_usage IS 'Detailed storage usage breakdown by category';
COMMENT ON TABLE public.sync_status IS 'Device synchronization status tracking';

-- Create a view for user storage summary
CREATE OR REPLACE VIEW public.user_storage_summary AS
SELECT 
  u.id,
  u.email,
  u.storage_used,
  u.storage_quota,
  ROUND((u.storage_used::DECIMAL / u.storage_quota::DECIMAL) * 100, 2) as usage_percentage,
  (u.storage_quota - u.storage_used) as remaining_bytes,
  COALESCE(photo_usage.bytes_used, 0) as photo_bytes,
  COALESCE(contact_usage.bytes_used, 0) as contact_bytes,
  COALESCE(message_usage.bytes_used, 0) as message_bytes,
  COALESCE(photo_usage.file_count, 0) as photo_count,
  COALESCE(contact_usage.file_count, 0) as contact_count,
  COALESCE(message_usage.file_count, 0) as message_count
FROM public.users u
LEFT JOIN public.storage_usage photo_usage ON u.id = photo_usage.user_id AND photo_usage.category = 'photo'
LEFT JOIN public.storage_usage contact_usage ON u.id = contact_usage.user_id AND contact_usage.category = 'contact'
LEFT JOIN public.storage_usage message_usage ON u.id = message_usage.user_id AND message_usage.category = 'message';

COMMENT ON VIEW public.user_storage_summary IS 'Comprehensive view of user storage usage across all categories';