import { supabase } from '../src/config/supabase';
import CloudStorageService from '../src/services/CloudStorageService';
import AuthService from '../src/services/AuthService';

jest.mock('../src/config/supabase');
jest.mock('../src/services/AuthService');

const mockSelect = jest.fn();
supabase.from = jest.fn(() => ({
  select: mockSelect,
}));

mockSelect.mockReturnValue({
  eq: jest.fn().mockReturnValue({
    order: jest.fn().mockReturnValue({
      then: jest.fn().mockImplementation((callback) => callback({ data: [], error: null })),
    }),
  }),
});

AuthService.getCurrentUser = jest.fn(() => ({ id: 'test-user-id' }));

describe('CloudStorageService', () => {
  describe('getUserFiles', () => {
    it('should retrieve user files without category filter', async () => {
      const files = await CloudStorageService.getUserFiles();

      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(mockSelect).toHaveBeenCalledWith('*');
      expect(files).toEqual([]);
    });

    it('should retrieve user files with category filter', async () => {
      const category = 'photo';
      const files = await CloudStorageService.getUserFiles(category);

      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(mockSelect).toHaveBeenCalledWith('*');
      expect(files).toEqual([]);
    });

    it('should handle errors gracefully', async () => {
      mockSelect.mockReturnValueOnce({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            then: jest.fn().mockImplementation((callback) => callback({ data: null, error: new Error('Query failed') })),
          }),
        }),
      });

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const files = await CloudStorageService.getUserFiles();

      expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching user files:', expect.any(Error));

      consoleErrorSpy.mockRestore();
    });
  });
});
