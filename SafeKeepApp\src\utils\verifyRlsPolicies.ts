import { supabase } from './supabaseClient';

/**
 * Interface for RLS policy verification result
 */
interface RlsPolicy {
  table_name: string;
  policy_name: string;
  cmd: string;
  policy_exists: boolean;
  policy_using: string | null;
  policy_with_check: string | null;
}

/**
 * Interface for RLS policy test result
 */
interface RlsPolicyTest {
  user_id: string;
  can_read: boolean;
  can_insert: boolean;
  can_update: boolean;
  can_delete: boolean;
}

/**
 * Verifies that RLS policies exist and are correctly configured
 * @returns List of policies and their configuration
 */
export async function verifyRlsPolicies(): Promise<{ 
  success: boolean; 
  message: string;
  policies?: RlsPolicy[] 
}> {
  try {
    console.log('Verifying RLS policies...');
    
    const { data, error } = await supabase.rpc('verify_rls_policies');
    
    if (error) {
      console.error('Error verifying RLS policies:', error);
      return { 
        success: false, 
        message: `Failed to verify RLS policies: ${error.message}` 
      };
    }
    
    if (!data || data.length === 0) {
      return { 
        success: false, 
        message: 'No RLS policies found. Policies may not be configured correctly.' 
      };
    }
    
    return { 
      success: true, 
      message: `Found ${data.length} RLS policies configured correctly.`,
      policies: data as RlsPolicy[]
    };
  } catch (error) {
    console.error('Unexpected error verifying RLS policies:', error);
    return { 
      success: false, 
      message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}

/**
 * Tests RLS policies by attempting operations as the current user
 * @returns Test results showing which operations are allowed
 */
export async function testRlsPolicies(): Promise<{ 
  success: boolean; 
  message: string;
  results?: RlsPolicyTest 
}> {
  try {
    console.log('Testing RLS policies...');
    
    // Get current user ID
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { 
        success: false, 
        message: 'User must be authenticated to test RLS policies' 
      };
    }
    
    const { data, error } = await supabase.rpc('test_rls_policies', {
      test_user_id: user.id
    });
    
    if (error) {
      console.error('Error testing RLS policies:', error);
      return { 
        success: false, 
        message: `Failed to test RLS policies: ${error.message}` 
      };
    }
    
    return { 
      success: true, 
      message: 'RLS policy tests completed successfully',
      results: data as RlsPolicyTest
    };
  } catch (error) {
    console.error('Unexpected error testing RLS policies:', error);
    return { 
      success: false, 
      message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}