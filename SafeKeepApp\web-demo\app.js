/**
 * SafeKeep Web Demo Application
 * Demonstrates React Native app functionality in a web browser
 */

// Supabase configuration
const SUPABASE_URL = 'https://babgywcvqyclvxdkckkd.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2MzA3NTEsImV4cCI6MjA2NzIwNjc1MX0.XMSozwfTcSSCXX1yHXfb8LG99oiU8tE_twgYvNthZSs';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzMDc1MSwiZXhwIjoyMDY3MjA2NzUxfQ.DUKouq8tLc1QwyWKw9pze3tHXIiN-L9SNpdvhEQw3cs';

// Initialize Supabase clients
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
const adminSupabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Initialize Enhanced Authentication Manager
let authManager;

// Initialize Real-time Progress Manager and Backup Console
let progressManager;
let backupConsole;

// Initialize Encryption Demo
let encryptionDemo;

// Initialize Backup History Manager and Dashboard
let backupHistoryManager;
let backupHistoryDashboard;

// Initialize Restore Manager and Simulation
let restoreManager;
let restoreSimulation;

// Initialize Schedule Manager and Console
let scheduleManager;
let scheduleConsole;

// Initialize Stripe Manager and Subscription Manager
let stripeManager;
let subscriptionManager;

// Initialize Subscription Management Dashboard
let subscriptionDashboard;
let tierConfig;
let usageManager;

// Global state
let currentUser = null;
let backupStats = {
    totalBackups: 0,
    totalFiles: 0,
    totalSize: 0,
    successRate: 100
};

// Utility functions
function log(message, type = 'info') {
    const logContainer = document.getElementById('activity-log');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    
    const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
    logEntry.textContent = `${timestamp} ${icon} ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function updateStatus(element, status, text, details = '') {
    const statusDot = document.getElementById(`${element}-status`);
    const statusText = document.getElementById(`${element}-text`);
    const statusDetails = document.getElementById(`${element}-details`);
    
    statusDot.className = `status-dot ${status}`;
    statusText.textContent = text;
    if (statusDetails) statusDetails.textContent = details;
}

function updateProgress(elementId, percentage) {
    const progressBar = document.getElementById(`${elementId}-progress`);
    if (progressBar) {
        progressBar.style.width = `${percentage}%`;
    }
}

function updateStats() {
    document.getElementById('total-backups').textContent = backupStats.totalBackups;
    document.getElementById('total-files').textContent = backupStats.totalFiles;
    document.getElementById('total-size').textContent = `${(backupStats.totalSize / 1024 / 1024).toFixed(1)} MB`;
    document.getElementById('success-rate').textContent = `${backupStats.successRate}%`;
}

// Connection testing
async function testConnections() {
    log('🔍 Testing Supabase connections...');
    
    // Test database connection
    try {
        const { data, error } = await supabase.from('users').select('count').limit(1);
        if (error) {
            updateStatus('db', 'error', 'Connection Failed', error.message);
            log(`Database connection failed: ${error.message}`, 'error');
        } else {
            updateStatus('db', 'connected', 'Connected', 'Database accessible');
            log('Database connection successful', 'success');
        }
    } catch (err) {
        updateStatus('db', 'error', 'Error', err.message);
        log(`Database error: ${err.message}`, 'error');
    }

    // Test storage (use admin client for bucket listing)
    try {
        const { data: buckets, error } = await adminSupabase.storage.listBuckets();
        if (error) {
            updateStatus('storage', 'error', 'Storage Failed', error.message);
            log(`Storage connection failed: ${error.message}`, 'error');
        } else {
            const requiredBuckets = ['user-data', 'thumbnails'];
            const availableBuckets = buckets.map(b => b.name);
            const hasRequired = requiredBuckets.every(name => availableBuckets.includes(name));
            
            log(`Available buckets: [${availableBuckets.join(', ')}]`, 'info');
            log(`Required buckets: [${requiredBuckets.join(', ')}]`, 'info');
            
            if (hasRequired) {
                updateStatus('storage', 'connected', 'Connected', `${buckets.length} buckets available`);
                log(`Storage connected: All required buckets found!`, 'success');
            } else {
                const missingBuckets = requiredBuckets.filter(name => !availableBuckets.includes(name));
                updateStatus('storage', 'error', 'Missing Buckets', `Missing: ${missingBuckets.join(', ')}`);
                log(`Storage: Missing required buckets: ${missingBuckets.join(', ')}`, 'error');
                log(`Note: Available buckets are: ${availableBuckets.join(', ')}`, 'info');
            }
        }
    } catch (err) {
        updateStatus('storage', 'error', 'Error', err.message);
        log(`Storage error: ${err.message}`, 'error');
    }

    // Test authentication
    try {
        const { data: session, error } = await supabase.auth.getSession();
        if (error) {
            updateStatus('auth', 'error', 'Auth Failed', error.message);
            log(`Authentication failed: ${error.message}`, 'error');
        } else {
            updateStatus('auth', 'connected', 'Ready', 'Auth service available');
            log('Authentication service ready', 'success');
            
            if (session.session) {
                currentUser = session.session.user;
                updateAuthInfo(`Signed in as: ${currentUser.email}`);
                log(`User authenticated: ${currentUser.email}`, 'success');
            }
        }
    } catch (err) {
        updateStatus('auth', 'error', 'Error', err.message);
        log(`Auth error: ${err.message}`, 'error');
    }
}

// Enhanced Authentication functions
function updateAuthInfo(message, type = 'info') {
    const authInfoElement = document.getElementById('auth-info');
    authInfoElement.textContent = message;
    
    // Add visual styling based on message type
    authInfoElement.className = `auth-info ${type}`;
}

async function signUp() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        updateAuthInfo('Please enter email and password', 'error');
        return;
    }
    
    if (password.length < 8) {
        updateAuthInfo('Password must be at least 8 characters long', 'error');
        return;
    }
    
    updateAuthInfo('Creating account...', 'info');
    
    const result = await authManager.signUp(email, password, {
        displayName: email.split('@')[0]
    });
    
    if (result.error) {
        updateAuthInfo(`Sign up failed: ${result.error.message}`, 'error');
    } else {
        updateAuthInfo('Sign up successful! You can now use all demo features.', 'success');
        // Clear form
        document.getElementById('email').value = '';
        document.getElementById('password').value = '';
    }
}

async function signIn() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        updateAuthInfo('Please enter email and password', 'error');
        return;
    }
    
    updateAuthInfo('Signing in...', 'info');
    
    const result = await authManager.signIn(email, password);
    
    if (result.error) {
        updateAuthInfo(`Sign in failed: ${result.error.message}`, 'error');
    } else {
        updateAuthInfo(`Signed in successfully as: ${result.data.user.email}`, 'success');
        // Clear form
        document.getElementById('email').value = '';
        document.getElementById('password').value = '';
    }
}

async function signOut() {
    updateAuthInfo('Signing out...', 'info');
    
    const result = await authManager.signOut();
    
    if (result.error) {
        updateAuthInfo(`Sign out failed: ${result.error.message}`, 'error');
    } else {
        updateAuthInfo('Signed out successfully', 'success');
    }
}

async function createDemoUser() {
    updateAuthInfo('Creating demo user...', 'info');
    
    const result = await authManager.createDemoUser();
    
    if (result.error) {
        updateAuthInfo(`Demo user creation failed: ${result.error.message}`, 'error');
    } else {
        updateAuthInfo('Demo user created! Check the activity log for credentials.', 'success');
    }
}

async function useDemoCredentials() {
    const credentials = authManager.getDemoCredentials();
    
    if (!credentials) {
        updateAuthInfo('No demo credentials found. Create a demo user first.', 'error');
        return;
    }
    
    document.getElementById('email').value = credentials.email;
    document.getElementById('password').value = credentials.password;
    updateAuthInfo('Demo credentials loaded. Click Sign In to continue.', 'info');
}

// Backup simulation functions
async function simulateContactBackup() {
    log('📞 Starting contact backup simulation...');
    document.getElementById('contact-status').textContent = 'Backing up contacts...';
    
    const contacts = [
        { name: 'John Doe', phone: '+1234567890', email: '<EMAIL>' },
        { name: 'Jane Smith', phone: '+0987654321', email: '<EMAIL>' },
        { name: 'Bob Johnson', phone: '+1122334455', email: '<EMAIL>' },
        { name: 'Alice Brown', phone: '+5566778899', email: '<EMAIL>' },
        { name: 'Charlie Wilson', phone: '+9988776655', email: '<EMAIL>' }
    ];
    
    // Create backup session in history
    const session = backupHistoryManager?.addSession({
        type: 'manual',
        status: 'running',
        progress: {
            contacts: { total: contacts.length, completed: 0, failed: 0 },
            messages: { total: 0, completed: 0, failed: 0 },
            photos: { total: 0, completed: 0, failed: 0 },
            overall: { total: contacts.length, completed: 0, failed: 0 }
        }
    });
    
    for (let i = 0; i < contacts.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 500));
        const progress = ((i + 1) / contacts.length) * 100;
        updateProgress('contact', progress);
        log(`Backing up contact: ${contacts[i].name}`);
    }
    
    // Simulate file upload
    try {
        const contactData = JSON.stringify(contacts);
        const fileName = `demo/contacts-backup-${Date.now()}.json`;
        
        log('Uploading encrypted contact data...');
        
        // Upload to Supabase storage
        const { data, error } = await adminSupabase.storage
            .from('user-data')
            .upload(fileName, contactData, {
                contentType: 'application/json'
            });
        
        if (error) {
            throw new Error(`Upload failed: ${error.message}`);
        }
        
        document.getElementById('contact-status').textContent = 'Upload complete!';
        
        // Update backup session
        if (session) {
            backupHistoryManager.updateSession(session.id, {
                status: 'completed',
                endTime: new Date(),
                progress: {
                    contacts: { total: contacts.length, completed: contacts.length, failed: 0 },
                    messages: { total: 0, completed: 0, failed: 0 },
                    photos: { total: 0, completed: 0, failed: 0 },
                    overall: { total: contacts.length, completed: contacts.length, failed: 0 }
                },
                encryption: {
                    algorithm: 'AES-256-GCM',
                    keyId: `key-${Date.now()}`,
                    originalSize: contactData.length,
                    encryptedSize: Math.round(contactData.length * 1.02) // 2% overhead
                },
                performance: {
                    transferRate: contactData.length / 5, // 5 seconds
                    averageFileSize: contactData.length / contacts.length,
                    errorRate: 0
                }
            });
        }
        
        backupStats.totalBackups++;
        backupStats.totalFiles += contacts.length;
        backupStats.totalSize += contactData.length;
        updateStats();
        
        log(`Contact backup completed: ${contacts.length} contacts uploaded to ${fileName}`, 'success');
        
        // Keep demo file for 2 minutes so you can see it in Supabase
        setTimeout(async () => {
            await adminSupabase.storage.from('user-data').remove([fileName]);
            log(`Cleaned up demo file: ${fileName}`, 'info');
        }, 120000); // 2 minutes
        
    } catch (err) {
        document.getElementById('contact-status').textContent = 'Backup failed';
        
        // Update backup session as failed
        if (session) {
            backupHistoryManager.updateSession(session.id, {
                status: 'failed',
                endTime: new Date(),
                errors: [{ 
                    time: new Date(), 
                    phase: 'contacts', 
                    message: err.message, 
                    itemName: 'contacts-backup.json' 
                }]
            });
        }
        
        log(`Contact backup failed: ${err.message}`, 'error');
    }
}

async function simulateMessageBackup() {
    log('💬 Starting message backup simulation...');
    document.getElementById('message-status').textContent = 'Backing up messages...';
    
    const messages = [
        { from: '+1234567890', message: 'Hey, how are you?', timestamp: new Date().toISOString() },
        { from: '+0987654321', message: 'Meeting at 3pm today', timestamp: new Date().toISOString() },
        { from: '+1122334455', message: 'Thanks for the help!', timestamp: new Date().toISOString() },
        { from: '+5566778899', message: 'See you tomorrow', timestamp: new Date().toISOString() },
        { from: '+9988776655', message: 'Happy birthday!', timestamp: new Date().toISOString() }
    ];
    
    for (let i = 0; i < messages.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 400));
        const progress = ((i + 1) / messages.length) * 100;
        updateProgress('message', progress);
        log(`Backing up message from: ${messages[i].from}`);
    }
    
    try {
        const messageData = JSON.stringify(messages);
        const fileName = `demo/messages-backup-${Date.now()}.json`;
        
        log('Encrypting and uploading message data...');
        
        // Upload to Supabase storage
        const { data, error } = await adminSupabase.storage
            .from('user-data')
            .upload(fileName, messageData, {
                contentType: 'application/json'
            });
        
        if (error) {
            throw new Error(`Upload failed: ${error.message}`);
        }
        
        document.getElementById('message-status').textContent = 'Upload complete!';
        
        backupStats.totalBackups++;
        backupStats.totalFiles += messages.length;
        backupStats.totalSize += messageData.length;
        updateStats();
        
        log(`Message backup completed: ${messages.length} messages uploaded to ${fileName}`, 'success');
        
        // Keep demo file for 2 minutes so you can see it in Supabase
        setTimeout(async () => {
            await adminSupabase.storage.from('user-data').remove([fileName]);
            log(`Cleaned up demo file: ${fileName}`, 'info');
        }, 120000); // 2 minutes
        
    } catch (err) {
        document.getElementById('message-status').textContent = 'Backup failed';
        log(`Message backup failed: ${err.message}`, 'error');
    }
}

async function simulatePhotoBackup() {
    log('📸 Starting photo backup simulation...');
    document.getElementById('photo-status').textContent = 'Scanning photos...';
    
    const photos = [
        { filename: 'IMG_001.jpg', size: 2048000, type: 'image/jpeg' },
        { filename: 'IMG_002.png', size: 1536000, type: 'image/png' },
        { filename: 'IMG_003.jpg', size: 3072000, type: 'image/jpeg' },
        { filename: 'IMG_004.heic', size: 1024000, type: 'image/heic' },
        { filename: 'IMG_005.jpg', size: 2560000, type: 'image/jpeg' }
    ];
    
    log('Filtering out video files (videos excluded)...');
    await new Promise(resolve => setTimeout(resolve, 500));
    
    for (let i = 0; i < photos.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 800));
        const progress = ((i + 1) / photos.length) * 100;
        updateProgress('photo', progress);
        log(`Processing photo: ${photos[i].filename} (${(photos[i].size / 1024 / 1024).toFixed(1)}MB)`);
    }
    
    try {
        const photoMetadata = JSON.stringify(photos);
        const fileName = `demo/photos-metadata-${Date.now()}.json`;
        
        log('Encrypting and uploading photo metadata...');
        
        // Upload to Supabase storage
        const { data, error } = await adminSupabase.storage
            .from('user-data')
            .upload(fileName, photoMetadata, {
                contentType: 'application/json'
            });
        
        if (error) {
            throw new Error(`Upload failed: ${error.message}`);
        }
        
        document.getElementById('photo-status').textContent = 'Upload complete!';
        
        backupStats.totalBackups++;
        backupStats.totalFiles += photos.length;
        backupStats.totalSize += photos.reduce((sum, photo) => sum + photo.size, 0);
        updateStats();
        
        log(`Photo backup completed: ${photos.length} photos metadata uploaded to ${fileName}`, 'success');
        
        // Keep demo file for 2 minutes so you can see it in Supabase
        setTimeout(async () => {
            await adminSupabase.storage.from('user-data').remove([fileName]);
            log(`Cleaned up demo file: ${fileName}`, 'info');
        }, 120000); // 2 minutes
        
    } catch (err) {
        document.getElementById('photo-status').textContent = 'Backup failed';
        log(`Photo backup failed: ${err.message}`, 'error');
    }
}

async function runFullBackup() {
    log('🔄 Starting full backup session...');
    document.getElementById('full-status').textContent = 'Initializing backup session...';
    
    // Create backup session in history
    const session = backupHistoryManager?.addSession({
        type: 'manual',
        status: 'running',
        progress: {
            contacts: { total: 5, completed: 0, failed: 0 },
            messages: { total: 5, completed: 0, failed: 0 },
            photos: { total: 5, completed: 0, failed: 0 },
            overall: { total: 15, completed: 0, failed: 0 }
        }
    });
    
    log(`Created backup session: ${session?.id || 'unknown'}`);
    
    try {
        // Simulate backup session creation in database
        await new Promise(resolve => setTimeout(resolve, 500));
        updateProgress('full', 10);
        
        // Run all backup types
        log('Running contact backup...');
        document.getElementById('full-status').textContent = 'Backing up contacts...';
        await simulateContactBackup();
        updateProgress('full', 40);
        
        log('Running message backup...');
        document.getElementById('full-status').textContent = 'Backing up messages...';
        await simulateMessageBackup();
        updateProgress('full', 70);
        
        log('Running photo backup...');
        document.getElementById('full-status').textContent = 'Backing up photos...';
        await simulatePhotoBackup();
        updateProgress('full', 90);
        
        // Finalize session
        log('Finalizing backup session...');
        document.getElementById('full-status').textContent = 'Finalizing backup...';
        await new Promise(resolve => setTimeout(resolve, 1000));
        updateProgress('full', 100);
        
        document.getElementById('full-status').textContent = 'Full backup complete!';
        
        // Update backup session as completed
        if (session) {
            backupHistoryManager.updateSession(session.id, {
                status: 'completed',
                endTime: new Date(),
                progress: {
                    contacts: { total: 5, completed: 5, failed: 0 },
                    messages: { total: 5, completed: 5, failed: 0 },
                    photos: { total: 5, completed: 5, failed: 0 },
                    overall: { total: 15, completed: 15, failed: 0 }
                },
                encryption: {
                    algorithm: 'AES-256-GCM',
                    keyId: `key-${Date.now()}`,
                    originalSize: 15 * 1024 * 1024, // 15MB estimated
                    encryptedSize: 15.3 * 1024 * 1024 // 15.3MB with overhead
                },
                performance: {
                    transferRate: 2.5 * 1024 * 1024, // 2.5 MB/s
                    averageFileSize: 1024 * 1024, // 1MB average
                    errorRate: 0
                }
            });
        }
        
        log(`Full backup session completed: ${session?.id || 'unknown'}`, 'success');
        
        // Update overall stats
        backupStats.totalBackups++;
        updateStats();
        
    } catch (err) {
        document.getElementById('full-status').textContent = 'Full backup failed';
        
        // Update backup session as failed
        if (session) {
            backupHistoryManager.updateSession(session.id, {
                status: 'failed',
                endTime: new Date(),
                errors: [{ 
                    time: new Date(), 
                    phase: 'full', 
                    message: err.message, 
                    itemName: 'full-backup' 
                }]
            });
        }
        
        log(`Full backup failed: ${err.message}`, 'error');
    }
}

// List files in storage
async function listStorageFiles() {
    log('📁 Listing files in storage...');
    
    try {
        const { data: files, error } = await adminSupabase.storage
            .from('user-data')
            .list('demo', {
                limit: 100,
                offset: 0
            });

        if (error) {
            log(`❌ Failed to list files: ${error.message}`, 'error');
        } else {
            if (files && files.length > 0) {
                log(`📋 Found ${files.length} files in storage:`, 'success');
                files.forEach(file => {
                    log(`  • ${file.name} (${(file.metadata?.size || 0)} bytes)`, 'info');
                });
            } else {
                log('📋 No files found in storage', 'info');
            }
        }
    } catch (err) {
        log(`❌ Error listing files: ${err.message}`, 'error');
    }
}

// Clean up all demo files
async function cleanupDemoFiles() {
    log('🧹 Cleaning up all demo files...');
    
    try {
        const { data: files, error: listError } = await adminSupabase.storage
            .from('user-data')
            .list('demo');

        if (listError) {
            log(`❌ Failed to list files for cleanup: ${listError.message}`, 'error');
            return;
        }

        if (files && files.length > 0) {
            const filePaths = files.map(file => `demo/${file.name}`);
            const { error: deleteError } = await adminSupabase.storage
                .from('user-data')
                .remove(filePaths);

            if (deleteError) {
                log(`❌ Failed to cleanup files: ${deleteError.message}`, 'error');
            } else {
                log(`✅ Cleaned up ${files.length} demo files`, 'success');
            }
        } else {
            log('ℹ️  No demo files to cleanup', 'info');
        }
    } catch (err) {
        log(`❌ Cleanup error: ${err.message}`, 'error');
    }
}

// Initialize the demo
async function initDemo() {
    log('🚀 Initializing Enhanced SafeKeep Web Demo...');
    
    // Initialize Enhanced Authentication Manager
    authManager = new window.AuthManager(supabase, adminSupabase);
    
    // Initialize Real-time Progress Manager
    progressManager = new window.RealtimeProgressManager();
    
    // Initialize Backup Console
    backupConsole = new window.BackupConsole('backup-console-container');
    backupConsole.setProgressManager(progressManager);
    
    // Initialize Encryption Demo
    encryptionDemo = new window.EncryptionDemo('encryption-demo-container');
    
    // Initialize Backup History Manager and Dashboard
    backupHistoryManager = new window.BackupHistoryManager(supabase, adminSupabase);
    backupHistoryDashboard = new window.BackupHistoryDashboard('backup-history-dashboard-container');
    backupHistoryDashboard.setHistoryManager(backupHistoryManager);
    
    // Initialize Restore Manager and Simulation
    restoreManager = new window.RestoreManager(supabase, adminSupabase);
    restoreSimulation = new window.RestoreSimulation('restore-simulation-container');
    restoreSimulation.setRestoreManager(restoreManager);
    restoreSimulation.setHistoryManager(backupHistoryManager);
    
    // Initialize Schedule Manager and Console
    try {
        if (window.ScheduleManager) {
            scheduleManager = new window.ScheduleManager(supabase, adminSupabase);
            await scheduleManager.initialize();
            log('Schedule Manager initialized successfully', 'success');
        } else {
            log('ScheduleManager class not found', 'error');
        }
        
        if (window.ScheduleConsole) {
            scheduleConsole = new window.ScheduleConsole('schedule-console-container');
            if (scheduleManager) {
                scheduleConsole.setScheduleManager(scheduleManager);
            }
            log('Schedule Console initialized successfully', 'success');
        } else {
            log('ScheduleConsole class not found', 'error');
        }
    } catch (error) {
        log(`Schedule system initialization failed: ${error.message}`, 'error');
        console.error('Schedule system error:', error);
    }
    
    // Initialize Subscription Management System
    try {
        if (window.SubscriptionTierConfig) {
            tierConfig = new window.SubscriptionTierConfig();
            log('Subscription Tier Config initialized successfully', 'success');
        } else {
            log('SubscriptionTierConfig class not found', 'error');
        }
        
        if (window.StripeManager) {
            stripeManager = new window.StripeManager();
            await stripeManager.initialize();
            log('Stripe Manager initialized successfully', 'success');
        } else {
            log('StripeManager class not found', 'error');
        }
        
        if (window.UsageQuotaManager && subscriptionManager && tierConfig) {
            usageManager = new window.UsageQuotaManager(subscriptionManager, tierConfig);
            log('Usage Quota Manager initialized successfully', 'success');
        } else {
            log('UsageQuotaManager dependencies not available', 'error');
        }
    } catch (error) {
        log(`Subscription system initialization failed: ${error.message}`, 'error');
        console.error('Subscription system error:', error);
    }
    
    // Set up auth event listeners
    authManager.addListener((event, user) => {
        if (event === 'signed_in') {
            currentUser = user;
            updateAuthInfo(`Signed in as: ${user.email}`, 'success');
            log(`User authenticated: ${user.email}`, 'success');
            
            // Load user profile and subscription info
            loadUserDashboard();
        } else if (event === 'signed_out') {
            currentUser = null;
            updateAuthInfo('Ready to sign up or sign in', 'info');
            log('User signed out', 'info');
            
            // Clear user dashboard
            clearUserDashboard();
        }
    });
    
    // Test connections
    await testConnections();
    
    // Check for existing session
    const { data: { session } } = await supabase.auth.getSession();
    if (session?.user) {
        currentUser = session.user;
        updateAuthInfo(`Signed in as: ${session.user.email}`, 'success');
        loadUserDashboard();
    }
    
    log('Enhanced demo initialization complete', 'success');
    log('🎯 New features: Enhanced auth, user profiles, demo users, advanced scheduling', 'info');
    log('🕒 Advanced Backup Scheduling: Create schedules with condition-based triggers', 'info');
    log('💡 Files will be kept for 2 minutes so you can see them in Supabase Dashboard', 'info');
}

// Load user dashboard information
async function loadUserDashboard() {
    if (!authManager.isAuthenticated()) return;
    
    try {
        const profile = await authManager.loadUserProfile();
        const subscription = await authManager.getUserSubscriptionTier();
        
        if (profile) {
            log(`👤 Profile loaded: ${profile.display_name}`, 'success');
            log(`💾 Storage used: ${(profile.storage_used / 1024 / 1024).toFixed(1)}MB of ${(profile.storage_quota / 1024 / 1024 / 1024).toFixed(1)}GB`, 'info');
            log(`⭐ Subscription: ${subscription.tier} tier`, 'info');
        }
    } catch (error) {
        log(`❌ Failed to load user dashboard: ${error.message}`, 'error');
    }
}

// Clear user dashboard
function clearUserDashboard() {
    // Reset any user-specific UI elements
    log('🧹 User dashboard cleared', 'info');
}

// Test schedule system function
async function testScheduleSystem() {
    log('🧪 Testing Schedule System...', 'info');
    
    if (!scheduleManager) {
        log('Schedule Manager not initialized, trying manual init...', 'error');
        await manualInitComponents();
        return;
    }
    
    try {
        // Test creating a schedule
        const testSchedule = await scheduleManager.createSchedule({
            name: 'Demo Test Schedule',
            frequency: 'daily',
            time: '15:00',
            days: [1, 2, 3, 4, 5],
            dataTypes: ['contacts', 'messages'],
            conditions: {
                wifiOnly: true,
                minBatteryLevel: 30,
                maxStorageUsage: 80,
                requireCharging: false,
                requireIdle: false
            }
        });
        
        log(`✅ Test schedule created: ${testSchedule.name}`, 'success');
        
        // Test condition evaluation
        const testResult = scheduleManager.testSchedule(testSchedule);
        log(`🔍 Schedule test result: ${testResult.canRunNow ? 'CAN RUN' : 'BLOCKED'}`, 'info');
        
        // Get stats
        const stats = scheduleManager.getScheduleStats();
        log(`📊 Schedule stats: ${stats.totalSchedules} total, ${stats.enabledSchedules} enabled`, 'info');
        
    } catch (error) {
        log(`❌ Schedule test failed: ${error.message}`, 'error');
    }
}

// Manual component initialization function
async function manualInitComponents() {
    log('🔧 Manual component initialization...', 'info');
    
    try {
        // Check if classes are available
        if (!window.ScheduleManager) {
            log('❌ ScheduleManager class not found - check script loading', 'error');
            return;
        }
        
        if (!window.ScheduleConsole) {
            log('❌ ScheduleConsole class not found - check script loading', 'error');
            return;
        }
        
        // Initialize Schedule Manager
        if (!scheduleManager) {
            scheduleManager = new window.ScheduleManager(supabase, adminSupabase);
            await scheduleManager.initialize();
            log('✅ Schedule Manager manually initialized', 'success');
        }
        
        // Initialize Schedule Console
        if (!scheduleConsole) {
            scheduleConsole = new window.ScheduleConsole('schedule-console-container');
            scheduleConsole.setScheduleManager(scheduleManager);
            log('✅ Schedule Console manually initialized', 'success');
        }
        
        log('🎉 Manual initialization complete!', 'success');
        
    } catch (error) {
        log(`❌ Manual initialization failed: ${error.message}`, 'error');
        console.error('Manual init error:', error);
    }
}

// Subscription Management Dashboard Functions
async function initializeSubscriptionDashboard() {
    try {
        log('🚀 Initializing Subscription Management Dashboard...', 'info');
        
        // Check if required components are available
        if (!window.SubscriptionManagementDashboard) {
            throw new Error('SubscriptionManagementDashboard class not found');
        }
        
        if (!tierConfig) {
            tierConfig = new window.SubscriptionTierConfig();
        }
        
        if (!stripeManager) {
            stripeManager = new window.StripeManager();
            await stripeManager.initialize();
        }
        
        // Create subscription dashboard
        subscriptionDashboard = new window.SubscriptionManagementDashboard(
            stripeManager,
            tierConfig,
            usageManager
        );
        
        // Get dashboard container and mount it
        const container = subscriptionDashboard.getDashboardContainer();
        const mountPoint = document.getElementById('subscription-dashboard-container');
        
        if (mountPoint) {
            // Clear existing content
            mountPoint.innerHTML = '';
            mountPoint.appendChild(container);
            
            log('✅ Subscription Management Dashboard initialized successfully', 'success');
        } else {
            throw new Error('Subscription dashboard container not found');
        }
        
    } catch (error) {
        log(`❌ Failed to initialize Subscription Management Dashboard: ${error.message}`, 'error');
        console.error('Subscription dashboard error:', error);
    }
}

async function testSubscriptionManagement() {
    try {
        log('🧪 Running Subscription Management tests...', 'info');
        
        if (window.quickTestSubscriptionManagement) {
            const result = await window.quickTestSubscriptionManagement();
            
            if (result.success) {
                log('✅ Subscription Management tests passed', 'success');
            } else {
                log(`❌ Subscription Management tests failed: ${result.error?.message || 'Unknown error'}`, 'error');
            }
        } else if (window.verifySubscriptionManagement) {
            const result = await window.verifySubscriptionManagement();
            
            if (result.success) {
                log('✅ Subscription Management verification passed', 'success');
            } else {
                log(`❌ Subscription Management verification failed: ${result.message}`, 'error');
            }
        } else {
            log('⚠️ Subscription Management test functions not found', 'error');
        }
        
    } catch (error) {
        log(`❌ Subscription Management test failed: ${error.message}`, 'error');
        console.error('Subscription test error:', error);
    }
}

// Start the demo when page loads
document.addEventListener('DOMContentLoaded', initDemo);