import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { BackupSession, BackupProgress, BackupError, BackupConfiguration } from '../../types/backup';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Backup statistics interface
export interface BackupStatistics {
  totalBackups: number;
  totalItems: number;
  totalDataSize: number;
  lastSuccessfulBackup?: string;
  averageBackupTime: number;
  successRate: number;
  dataTypeBreakdown: {
    contacts: { count: number; size: number };
    messages: { count: number; size: number };
    photos: { count: number; size: number };
  };
}

// Enhanced backup state interface
interface BackupState {
  // Current session state
  currentSession: BackupSession | null;
  isBackupInProgress: boolean;
  
  // Progress tracking
  realTimeProgress: {
    contacts: BackupProgress;
    messages: BackupProgress;
    photos: BackupProgress;
  };
  
  // History and statistics
  backupHistory: BackupSession[];
  statistics: BackupStatistics;
  
  // Configuration
  configuration: BackupConfiguration;
  
  // Error handling
  errors: BackupError[];
  
  // UI state
  loading: boolean;
  lastUpdated: string;
}

const initialProgress: BackupProgress = {
  total: 0,
  completed: 0,
  failed: 0,
  status: 'pending',
  errors: [],
};

const initialStatistics: BackupStatistics = {
  totalBackups: 0,
  totalItems: 0,
  totalDataSize: 0,
  averageBackupTime: 0,
  successRate: 0,
  dataTypeBreakdown: {
    contacts: { count: 0, size: 0 },
    messages: { count: 0, size: 0 },
    photos: { count: 0, size: 0 },
  },
};

const initialConfiguration: BackupConfiguration = {
  autoBackup: false,
  wifiOnly: true,
  includeContacts: true,
  includeMessages: true,
  includePhotos: true,
  compressionLevel: 'medium',
};

const initialState: BackupState = {
  currentSession: null,
  isBackupInProgress: false,
  realTimeProgress: {
    contacts: initialProgress,
    messages: initialProgress,
    photos: initialProgress,
  },
  backupHistory: [],
  statistics: initialStatistics,
  configuration: initialConfiguration,
  errors: [],
  loading: false,
  lastUpdated: new Date().toISOString(),
};

// Async thunks for backup history management
export const loadBackupHistory = createAsyncThunk(
  'backup/loadHistory',
  async () => {
    try {
      const historyData = await AsyncStorage.getItem('backup_history');
      const statisticsData = await AsyncStorage.getItem('backup_statistics');
      const configData = await AsyncStorage.getItem('backup_configuration');
      
      return {
        history: historyData ? JSON.parse(historyData) : [],
        statistics: statisticsData ? JSON.parse(statisticsData) : initialStatistics,
        configuration: configData ? JSON.parse(configData) : initialConfiguration,
      };
    } catch (error) {
      console.error('Failed to load backup history:', error);
      return {
        history: [],
        statistics: initialStatistics,
        configuration: initialConfiguration,
      };
    }
  }
);

export const saveBackupHistory = createAsyncThunk(
  'backup/saveHistory',
  async (data: { history: BackupSession[]; statistics: BackupStatistics; configuration: BackupConfiguration }) => {
    try {
      await AsyncStorage.setItem('backup_history', JSON.stringify(data.history));
      await AsyncStorage.setItem('backup_statistics', JSON.stringify(data.statistics));
      await AsyncStorage.setItem('backup_configuration', JSON.stringify(data.configuration));
      return true;
    } catch (error) {
      console.error('Failed to save backup history:', error);
      throw error;
    }
  }
);

const backupSlice = createSlice({
  name: 'backup',
  initialState,
  reducers: {
    // Session management
    startBackupSession: (state, action: PayloadAction<BackupSession>) => {
      state.currentSession = action.payload;
      state.isBackupInProgress = true;
      state.realTimeProgress = {
        contacts: { ...initialProgress, status: 'pending' },
        messages: { ...initialProgress, status: 'pending' },
        photos: { ...initialProgress, status: 'pending' },
      };
      state.errors = [];
      state.lastUpdated = new Date().toISOString();
    },
    
    updateBackupSession: (state, action: PayloadAction<Partial<BackupSession>>) => {
      if (state.currentSession) {
        state.currentSession = { ...state.currentSession, ...action.payload };
        state.lastUpdated = new Date().toISOString();
      }
    },
    
    completeBackupSession: (state, action: PayloadAction<{ endTime: Date; status: BackupSession['status'] }>) => {
      if (state.currentSession) {
        state.currentSession.endTime = action.payload.endTime;
        state.currentSession.status = action.payload.status;
        state.isBackupInProgress = false;
        
        // Add to history
        state.backupHistory.unshift({ ...state.currentSession });
        
        // Keep only last 50 sessions in memory
        if (state.backupHistory.length > 50) {
          state.backupHistory = state.backupHistory.slice(0, 50);
        }
        
        state.lastUpdated = new Date().toISOString();
      }
    },
    
    cancelBackupSession: (state) => {
      if (state.currentSession) {
        state.currentSession.status = 'cancelled';
        state.currentSession.endTime = new Date();
        state.isBackupInProgress = false;
        state.lastUpdated = new Date().toISOString();
      }
    },
    
    // Real-time progress updates
    updateDataTypeProgress: (state, action: PayloadAction<{ 
      dataType: 'contacts' | 'messages' | 'photos'; 
      progress: Partial<BackupProgress> 
    }>) => {
      const { dataType, progress } = action.payload;
      state.realTimeProgress[dataType] = { 
        ...state.realTimeProgress[dataType], 
        ...progress 
      };
      
      // Update current session progress
      if (state.currentSession) {
        state.currentSession.progress[dataType] = state.realTimeProgress[dataType];
        
        // Update total progress
        const totalItems = Object.values(state.realTimeProgress).reduce((sum, p) => sum + p.total, 0);
        const completedItems = Object.values(state.realTimeProgress).reduce((sum, p) => sum + p.completed, 0);
        
        state.currentSession.totalItems = totalItems;
        state.currentSession.completedItems = completedItems;
      }
      
      state.lastUpdated = new Date().toISOString();
    },
    
    addProgressError: (state, action: PayloadAction<{ 
      dataType: 'contacts' | 'messages' | 'photos'; 
      error: BackupError 
    }>) => {
      const { dataType, error } = action.payload;
      state.realTimeProgress[dataType].errors.push(error);
      state.errors.push(error);
      state.lastUpdated = new Date().toISOString();
    },
    
    // Configuration management
    updateConfiguration: (state, action: PayloadAction<Partial<BackupConfiguration>>) => {
      state.configuration = { ...state.configuration, ...action.payload };
      state.lastUpdated = new Date().toISOString();
    },
    
    // Statistics calculation
    calculateStatistics: (state) => {
      const completedSessions = state.backupHistory.filter(s => s.status === 'completed');
      const totalSessions = state.backupHistory.length;
      
      if (completedSessions.length === 0) {
        state.statistics = initialStatistics;
        return;
      }
      
      const totalItems = completedSessions.reduce((sum, s) => sum + s.completedItems, 0);
      const totalDataSize = completedSessions.reduce((sum, s) => {
        return sum + Object.values(s.progress).reduce((typeSum, p) => {
          return typeSum + p.completed * 1024; // Estimate 1KB per item
        }, 0);
      }, 0);
      
      const totalBackupTime = completedSessions.reduce((sum, s) => {
        if (s.endTime && s.startTime) {
          return sum + (new Date(s.endTime).getTime() - new Date(s.startTime).getTime());
        }
        return sum;
      }, 0);
      
      const averageBackupTime = totalBackupTime / completedSessions.length;
      const successRate = totalSessions > 0 ? (completedSessions.length / totalSessions) * 100 : 0;
      
      // Calculate data type breakdown
      const dataTypeBreakdown = completedSessions.reduce((breakdown, session) => {
        breakdown.contacts.count += session.progress.contacts.completed;
        breakdown.messages.count += session.progress.messages.completed;
        breakdown.photos.count += session.progress.photos.completed;
        
        // Estimate sizes (contacts: 0.5KB, messages: 1KB, photos: 2MB)
        breakdown.contacts.size += session.progress.contacts.completed * 512;
        breakdown.messages.size += session.progress.messages.completed * 1024;
        breakdown.photos.size += session.progress.photos.completed * 2048 * 1024;
        
        return breakdown;
      }, {
        contacts: { count: 0, size: 0 },
        messages: { count: 0, size: 0 },
        photos: { count: 0, size: 0 },
      });
      
      state.statistics = {
        totalBackups: completedSessions.length,
        totalItems,
        totalDataSize,
        lastSuccessfulBackup: completedSessions[0]?.endTime?.toString(),
        averageBackupTime,
        successRate,
        dataTypeBreakdown,
      };
      
      state.lastUpdated = new Date().toISOString();
    },
    
    // Error management
    clearErrors: (state) => {
      state.errors = [];
      state.realTimeProgress.contacts.errors = [];
      state.realTimeProgress.messages.errors = [];
      state.realTimeProgress.photos.errors = [];
      state.lastUpdated = new Date().toISOString();
    },
    
    removeError: (state, action: PayloadAction<string>) => {
      state.errors = state.errors.filter(error => error.id !== action.payload);
      
      // Remove from progress errors too
      Object.keys(state.realTimeProgress).forEach(key => {
        const dataType = key as keyof typeof state.realTimeProgress;
        state.realTimeProgress[dataType].errors = state.realTimeProgress[dataType].errors.filter(
          error => error.id !== action.payload
        );
      });
      
      state.lastUpdated = new Date().toISOString();
    },
    
    // UI state management
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    // Reset state
    resetBackupState: (state) => {
      return { ...initialState, configuration: state.configuration };
    },
  },
  
  extraReducers: (builder) => {
    builder
      .addCase(loadBackupHistory.pending, (state) => {
        state.loading = true;
      })
      .addCase(loadBackupHistory.fulfilled, (state, action) => {
        state.backupHistory = action.payload.history;
        state.statistics = action.payload.statistics;
        state.configuration = action.payload.configuration;
        state.loading = false;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(loadBackupHistory.rejected, (state) => {
        state.loading = false;
      })
      .addCase(saveBackupHistory.pending, (state) => {
        state.loading = true;
      })
      .addCase(saveBackupHistory.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(saveBackupHistory.rejected, (state) => {
        state.loading = false;
      });
  },
});

export const {
  startBackupSession,
  updateBackupSession,
  completeBackupSession,
  cancelBackupSession,
  updateDataTypeProgress,
  addProgressError,
  updateConfiguration,
  calculateStatistics,
  clearErrors,
  removeError,
  setLoading,
  resetBackupState,
} = backupSlice.actions;

export default backupSlice.reducer;
