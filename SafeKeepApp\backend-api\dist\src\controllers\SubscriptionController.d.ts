import { Request, Response } from 'express';
export declare class SubscriptionController {
    createSubscription(req: Request, res: Response): Promise<void>;
    updateSubscription(req: Request, res: Response): Promise<void>;
    getSubscriptionDetails(req: Request, res: Response): Promise<void>;
    cancelSubscription(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=SubscriptionController.d.ts.map