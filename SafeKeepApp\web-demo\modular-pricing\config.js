/**
 * Configuration for Modular Pricing UI
 * Contains service definitions, pricing plans, and UI settings
 */

/**
 * Service configuration with features and pricing
 * @type {Object.<string, ServiceOption>}
 */
const SERVICE_CONFIG = {
    contacts: {
        id: 'contacts',
        name: 'Contacts Backup',
        description: 'Secure backup of your contact list',
        features: [
            'Complete contact information',
            'Phone numbers and emails',
            'Contact photos and notes',
            'Automatic sync',
            'Cross-device restore'
        ],
        icon: '👥',
        individualPrice: 299 // $2.99 in cents
    },
    messages: {
        id: 'messages',
        name: 'Messages Backup',
        description: 'Backup your text messages and conversations',
        features: [
            'SMS and MMS messages',
            'Message attachments',
            'Conversation history',
            'Search and filter',
            'Export capabilities'
        ],
        icon: '💬',
        individualPrice: 399 // $3.99 in cents
    },
    photos: {
        id: 'photos',
        name: 'Photos Backup',
        description: 'Secure cloud storage for your photos',
        features: [
            'Unlimited photo backup',
            'Original quality preservation',
            'Smart organization',
            'Face recognition',
            'Easy sharing'
        ],
        icon: '📸',
        individualPrice: 599 // $5.99 in cents
    }
};

/**
 * Pricing plans configuration
 * @type {Object}
 */
const PRICING_PLANS = {
    individual: {
        contacts: { price: 299, name: 'Contacts Only' },
        messages: { price: 399, name: 'Messages Only' },
        photos: { price: 599, name: 'Photos Only' }
    },
    combinations: {
        contactsMessages: { 
            price: 599, 
            name: 'Contacts + Messages', 
            savings: 99,
            services: ['contacts', 'messages']
        },
        contactsPhotos: { 
            price: 799, 
            name: 'Contacts + Photos', 
            savings: 99,
            services: ['contacts', 'photos']
        },
        messagesPhotos: { 
            price: 899, 
            name: 'Messages + Photos', 
            savings: 99,
            services: ['messages', 'photos']
        },
        complete: { 
            price: 999, 
            name: 'Complete Backup', 
            savings: 297, 
            isPopular: true,
            badge: 'Most Popular',
            services: ['contacts', 'messages', 'photos']
        }
    }
};

/**
 * UI configuration settings
 */
const UI_CONFIG = {
    currency: 'USD',
    currencySymbol: '$',
    animationDuration: 300,
    debounceDelay: 500,
    mobileBreakpoint: 768,
    tabletBreakpoint: 1024
};

/**
 * API endpoints configuration
 */
const API_CONFIG = {
    baseUrl: '/api',
    endpoints: {
        pricing: '/pricing/calculate',
        combinations: '/pricing/combinations',
        validate: '/services/validate'
    }
};

// Export configurations
if (typeof window !== 'undefined') {
    window.ModularPricingConfig = {
        SERVICE_CONFIG,
        PRICING_PLANS,
        UI_CONFIG,
        API_CONFIG
    };
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SERVICE_CONFIG,
        PRICING_PLANS,
        UI_CONFIG,
        API_CONFIG
    };
}