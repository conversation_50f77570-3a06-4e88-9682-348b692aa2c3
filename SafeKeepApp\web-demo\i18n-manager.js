/**
 * Internationalization (i18n) Manager
 * Comprehensive multi-language support system without external API dependencies
 */

class I18nManager {
    constructor() {
        this.currentLanguage = 'en';
        this.fallbackLanguage = 'en';
        this.translations = new Map();
        this.supportedLanguages = new Map();
        this.formatters = new Map();
        this.rtlLanguages = new Set(['ar', 'he', 'fa', 'ur']);
        this.loadingPromises = new Map();
        
        this.initializeSupportedLanguages();
        this.initializeFormatters();
        this.detectUserLanguage();
    }

    initializeSupportedLanguages() {
        this.supportedLanguages.set('en', {
            code: 'en',
            name: 'English',
            nativeName: 'English',
            flag: '🇺🇸',
            rtl: false,
            dateFormat: 'MM/DD/YYYY',
            timeFormat: '12h',
            currency: 'USD',
            numberFormat: 'en-US'
        });

        this.supportedLanguages.set('es', {
            code: 'es',
            name: 'Spanish',
            nativeName: 'Español',
            flag: '🇪🇸',
            rtl: false,
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h',
            currency: 'EUR',
            numberFormat: 'es-ES'
        });

        this.supportedLanguages.set('fr', {
            code: 'fr',
            name: 'French',
            nativeName: 'Français',
            flag: '🇫🇷',
            rtl: false,
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h',
            currency: 'EUR',
            numberFormat: 'fr-FR'
        });

        this.supportedLanguages.set('de', {
            code: 'de',
            name: 'German',
            nativeName: 'Deutsch',
            flag: '🇩🇪',
            rtl: false,
            dateFormat: 'DD.MM.YYYY',
            timeFormat: '24h',
            currency: 'EUR',
            numberFormat: 'de-DE'
        });

        this.supportedLanguages.set('it', {
            code: 'it',
            name: 'Italian',
            nativeName: 'Italiano',
            flag: '🇮🇹',
            rtl: false,
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h',
            currency: 'EUR',
            numberFormat: 'it-IT'
        });

        this.supportedLanguages.set('pt', {
            code: 'pt',
            name: 'Portuguese',
            nativeName: 'Português',
            flag: '🇵🇹',
            rtl: false,
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h',
            currency: 'EUR',
            numberFormat: 'pt-PT'
        });

        this.supportedLanguages.set('ru', {
            code: 'ru',
            name: 'Russian',
            nativeName: 'Русский',
            flag: '🇷🇺',
            rtl: false,
            dateFormat: 'DD.MM.YYYY',
            timeFormat: '24h',
            currency: 'RUB',
            numberFormat: 'ru-RU'
        });

        this.supportedLanguages.set('zh', {
            code: 'zh',
            name: 'Chinese',
            nativeName: '中文',
            flag: '🇨🇳',
            rtl: false,
            dateFormat: 'YYYY/MM/DD',
            timeFormat: '24h',
            currency: 'CNY',
            numberFormat: 'zh-CN'
        });

        this.supportedLanguages.set('ja', {
            code: 'ja',
            name: 'Japanese',
            nativeName: '日本語',
            flag: '🇯🇵',
            rtl: false,
            dateFormat: 'YYYY/MM/DD',
            timeFormat: '24h',
            currency: 'JPY',
            numberFormat: 'ja-JP'
        });

        this.supportedLanguages.set('ko', {
            code: 'ko',
            name: 'Korean',
            nativeName: '한국어',
            flag: '🇰🇷',
            rtl: false,
            dateFormat: 'YYYY.MM.DD',
            timeFormat: '24h',
            currency: 'KRW',
            numberFormat: 'ko-KR'
        });

        this.supportedLanguages.set('ar', {
            code: 'ar',
            name: 'Arabic',
            nativeName: 'العربية',
            flag: '🇸🇦',
            rtl: true,
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '12h',
            currency: 'SAR',
            numberFormat: 'ar-SA'
        });

        this.supportedLanguages.set('hi', {
            code: 'hi',
            name: 'Hindi',
            nativeName: 'हिन्दी',
            flag: '🇮🇳',
            rtl: false,
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '12h',
            currency: 'INR',
            numberFormat: 'hi-IN'
        });
    }

    initializeFormatters() {
        // Initialize number formatters for each supported language
        this.supportedLanguages.forEach((lang, code) => {
            try {
                this.formatters.set(`number-${code}`, new Intl.NumberFormat(lang.numberFormat));
                this.formatters.set(`currency-${code}`, new Intl.NumberFormat(lang.numberFormat, {
                    style: 'currency',
                    currency: lang.currency
                }));
                this.formatters.set(`date-${code}`, new Intl.DateTimeFormat(lang.numberFormat));
                this.formatters.set(`time-${code}`, new Intl.DateTimeFormat(lang.numberFormat, {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: lang.timeFormat === '12h'
                }));
            } catch (error) {
                console.warn(`Failed to initialize formatters for ${code}:`, error);
            }
        });
    }

    detectUserLanguage() {
        // Try to detect user's preferred language from various sources
        let detectedLanguage = this.fallbackLanguage;

        // 1. Check localStorage for saved preference
        const savedLanguage = localStorage.getItem('safekeep-language');
        if (savedLanguage && this.supportedLanguages.has(savedLanguage)) {
            detectedLanguage = savedLanguage;
        }
        // 2. Check browser language
        else if (navigator.language) {
            const browserLang = navigator.language.split('-')[0];
            if (this.supportedLanguages.has(browserLang)) {
                detectedLanguage = browserLang;
            }
        }
        // 3. Check browser languages array
        else if (navigator.languages) {
            for (const lang of navigator.languages) {
                const langCode = lang.split('-')[0];
                if (this.supportedLanguages.has(langCode)) {
                    detectedLanguage = langCode;
                    break;
                }
            }
        }

        this.currentLanguage = detectedLanguage;
        this.updateDocumentLanguage();
    }

    async loadTranslations(languageCode) {
        if (this.translations.has(languageCode)) {
            return this.translations.get(languageCode);
        }

        // Check if already loading
        if (this.loadingPromises.has(languageCode)) {
            return this.loadingPromises.get(languageCode);
        }

        // Create loading promise
        const loadingPromise = this.loadTranslationFile(languageCode);
        this.loadingPromises.set(languageCode, loadingPromise);

        try {
            const translations = await loadingPromise;
            this.translations.set(languageCode, translations);
            this.loadingPromises.delete(languageCode);
            return translations;
        } catch (error) {
            this.loadingPromises.delete(languageCode);
            console.error(`Failed to load translations for ${languageCode}:`, error);
            
            // Return fallback translations if available
            if (languageCode !== this.fallbackLanguage && this.translations.has(this.fallbackLanguage)) {
                return this.translations.get(this.fallbackLanguage);
            }
            
            throw error;
        }
    }

    async loadTranslationFile(languageCode) {
        // In a real implementation, this would load from separate JSON files
        // For demo purposes, we'll return embedded translations
        return this.getEmbeddedTranslations(languageCode);
    }

    getEmbeddedTranslations(languageCode) {
        const translations = {
            en: {
                // Common UI elements
                common: {
                    loading: 'Loading...',
                    error: 'Error',
                    success: 'Success',
                    warning: 'Warning',
                    info: 'Information',
                    cancel: 'Cancel',
                    confirm: 'Confirm',
                    save: 'Save',
                    delete: 'Delete',
                    edit: 'Edit',
                    close: 'Close',
                    back: 'Back',
                    next: 'Next',
                    previous: 'Previous',
                    finish: 'Finish',
                    retry: 'Retry',
                    refresh: 'Refresh',
                    search: 'Search',
                    filter: 'Filter',
                    sort: 'Sort',
                    export: 'Export',
                    import: 'Import',
                    download: 'Download',
                    upload: 'Upload',
                    settings: 'Settings',
                    help: 'Help',
                    about: 'About',
                    contact: 'Contact',
                    privacy: 'Privacy',
                    terms: 'Terms of Service',
                    language: 'Language',
                    theme: 'Theme',
                    profile: 'Profile',
                    logout: 'Logout',
                    login: 'Login',
                    signup: 'Sign Up',
                    email: 'Email',
                    password: 'Password',
                    username: 'Username',
                    name: 'Name',
                    phone: 'Phone',
                    address: 'Address',
                    city: 'City',
                    country: 'Country',
                    date: 'Date',
                    time: 'Time',
                    size: 'Size',
                    type: 'Type',
                    status: 'Status',
                    actions: 'Actions',
                    details: 'Details',
                    description: 'Description',
                    title: 'Title',
                    category: 'Category',
                    tags: 'Tags',
                    priority: 'Priority',
                    progress: 'Progress',
                    completed: 'Completed',
                    pending: 'Pending',
                    failed: 'Failed',
                    active: 'Active',
                    inactive: 'Inactive',
                    enabled: 'Enabled',
                    disabled: 'Disabled',
                    public: 'Public',
                    private: 'Private',
                    shared: 'Shared',
                    owner: 'Owner',
                    created: 'Created',
                    updated: 'Updated',
                    modified: 'Modified',
                    version: 'Version',
                    total: 'Total',
                    count: 'Count',
                    amount: 'Amount',
                    price: 'Price',
                    currency: 'Currency',
                    free: 'Free',
                    premium: 'Premium',
                    trial: 'Trial',
                    subscription: 'Subscription',
                    billing: 'Billing',
                    payment: 'Payment',
                    invoice: 'Invoice',
                    receipt: 'Receipt'
                },

                // SafeKeep specific terms
                safekeep: {
                    appName: 'SafeKeep',
                    tagline: 'Your Data, Secured Forever',
                    backup: 'Backup',
                    restore: 'Restore',
                    encryption: 'Encryption',
                    security: 'Security',
                    privacy: 'Privacy',
                    storage: 'Storage',
                    sync: 'Sync',
                    contacts: 'Contacts',
                    messages: 'Messages',
                    photos: 'Photos',
                    documents: 'Documents',
                    files: 'Files',
                    folders: 'Folders',
                    device: 'Device',
                    cloud: 'Cloud',
                    local: 'Local',
                    remote: 'Remote',
                    automatic: 'Automatic',
                    manual: 'Manual',
                    scheduled: 'Scheduled',
                    realtime: 'Real-time',
                    offline: 'Offline',
                    online: 'Online',
                    connected: 'Connected',
                    disconnected: 'Disconnected',
                    syncing: 'Syncing',
                    synced: 'Synced',
                    backing_up: 'Backing up',
                    backed_up: 'Backed up',
                    restoring: 'Restoring',
                    restored: 'Restored',
                    encrypting: 'Encrypting',
                    encrypted: 'Encrypted',
                    decrypting: 'Decrypting',
                    decrypted: 'Decrypted',
                    secure: 'Secure',
                    protected: 'Protected',
                    verified: 'Verified',
                    authenticated: 'Authenticated',
                    authorized: 'Authorized',
                    unauthorized: 'Unauthorized',
                    access_denied: 'Access Denied',
                    permission_required: 'Permission Required',
                    quota_exceeded: 'Quota Exceeded',
                    storage_full: 'Storage Full',
                    network_error: 'Network Error',
                    connection_lost: 'Connection Lost',
                    server_error: 'Server Error',
                    client_error: 'Client Error',
                    validation_error: 'Validation Error',
                    authentication_failed: 'Authentication Failed',
                    backup_failed: 'Backup Failed',
                    restore_failed: 'Restore Failed',
                    encryption_failed: 'Encryption Failed',
                    decryption_failed: 'Decryption Failed',
                    sync_failed: 'Sync Failed',
                    upload_failed: 'Upload Failed',
                    download_failed: 'Download Failed'
                },

                // Navigation and menus
                navigation: {
                    dashboard: 'Dashboard',
                    backups: 'Backups',
                    restore: 'Restore',
                    security: 'Security',
                    settings: 'Settings',
                    account: 'Account',
                    billing: 'Billing',
                    support: 'Support',
                    documentation: 'Documentation',
                    api: 'API',
                    integrations: 'Integrations',
                    analytics: 'Analytics',
                    reports: 'Reports',
                    logs: 'Logs',
                    monitoring: 'Monitoring',
                    alerts: 'Alerts',
                    notifications: 'Notifications',
                    preferences: 'Preferences',
                    advanced: 'Advanced',
                    developer: 'Developer',
                    admin: 'Admin'
                },

                // Messages and notifications
                messages: {
                    welcome: 'Welcome to SafeKeep!',
                    backup_started: 'Backup has started',
                    backup_completed: 'Backup completed successfully',
                    backup_failed: 'Backup failed. Please try again.',
                    restore_started: 'Restore has started',
                    restore_completed: 'Restore completed successfully',
                    restore_failed: 'Restore failed. Please try again.',
                    sync_started: 'Synchronization started',
                    sync_completed: 'Synchronization completed',
                    sync_failed: 'Synchronization failed',
                    upload_started: 'Upload started',
                    upload_completed: 'Upload completed',
                    upload_failed: 'Upload failed',
                    download_started: 'Download started',
                    download_completed: 'Download completed',
                    download_failed: 'Download failed',
                    encryption_started: 'Encryption started',
                    encryption_completed: 'Encryption completed',
                    encryption_failed: 'Encryption failed',
                    decryption_started: 'Decryption started',
                    decryption_completed: 'Decryption completed',
                    decryption_failed: 'Decryption failed',
                    settings_saved: 'Settings saved successfully',
                    settings_failed: 'Failed to save settings',
                    profile_updated: 'Profile updated successfully',
                    profile_failed: 'Failed to update profile',
                    password_changed: 'Password changed successfully',
                    password_failed: 'Failed to change password',
                    email_verified: 'Email verified successfully',
                    email_failed: 'Failed to verify email',
                    account_created: 'Account created successfully',
                    account_failed: 'Failed to create account',
                    login_successful: 'Login successful',
                    login_failed: 'Login failed',
                    logout_successful: 'Logout successful',
                    session_expired: 'Session expired. Please login again.',
                    network_offline: 'You are currently offline',
                    network_online: 'Connection restored',
                    storage_warning: 'Storage space is running low',
                    storage_full: 'Storage space is full',
                    quota_warning: 'Approaching quota limit',
                    quota_exceeded: 'Quota limit exceeded',
                    permission_granted: 'Permission granted',
                    permission_denied: 'Permission denied',
                    feature_unavailable: 'This feature is not available',
                    maintenance_mode: 'System is under maintenance',
                    update_available: 'Update available',
                    update_required: 'Update required',
                    unsupported_browser: 'Unsupported browser',
                    javascript_required: 'JavaScript is required',
                    cookies_required: 'Cookies are required'
                },

                // Forms and validation
                forms: {
                    required_field: 'This field is required',
                    invalid_email: 'Please enter a valid email address',
                    invalid_password: 'Password must be at least 8 characters',
                    password_mismatch: 'Passwords do not match',
                    invalid_phone: 'Please enter a valid phone number',
                    invalid_url: 'Please enter a valid URL',
                    invalid_date: 'Please enter a valid date',
                    invalid_number: 'Please enter a valid number',
                    min_length: 'Minimum length is {min} characters',
                    max_length: 'Maximum length is {max} characters',
                    min_value: 'Minimum value is {min}',
                    max_value: 'Maximum value is {max}',
                    file_too_large: 'File is too large',
                    file_type_invalid: 'Invalid file type',
                    upload_limit_exceeded: 'Upload limit exceeded',
                    form_invalid: 'Please correct the errors below',
                    form_submitted: 'Form submitted successfully',
                    form_failed: 'Form submission failed'
                },

                // Time and dates
                time: {
                    now: 'Now',
                    today: 'Today',
                    yesterday: 'Yesterday',
                    tomorrow: 'Tomorrow',
                    this_week: 'This week',
                    last_week: 'Last week',
                    next_week: 'Next week',
                    this_month: 'This month',
                    last_month: 'Last month',
                    next_month: 'Next month',
                    this_year: 'This year',
                    last_year: 'Last year',
                    next_year: 'Next year',
                    seconds_ago: '{count} seconds ago',
                    minutes_ago: '{count} minutes ago',
                    hours_ago: '{count} hours ago',
                    days_ago: '{count} days ago',
                    weeks_ago: '{count} weeks ago',
                    months_ago: '{count} months ago',
                    years_ago: '{count} years ago',
                    in_seconds: 'in {count} seconds',
                    in_minutes: 'in {count} minutes',
                    in_hours: 'in {count} hours',
                    in_days: 'in {count} days',
                    in_weeks: 'in {count} weeks',
                    in_months: 'in {count} months',
                    in_years: 'in {count} years'
                },

                // Security and compliance
                security: {
                    zero_knowledge: 'Zero-Knowledge Encryption',
                    end_to_end: 'End-to-End Encryption',
                    aes_256: 'AES-256 Encryption',
                    two_factor: 'Two-Factor Authentication',
                    biometric: 'Biometric Authentication',
                    secure_backup: 'Secure Backup',
                    encrypted_storage: 'Encrypted Storage',
                    secure_transmission: 'Secure Transmission',
                    data_integrity: 'Data Integrity',
                    access_control: 'Access Control',
                    audit_trail: 'Audit Trail',
                    compliance: 'Compliance',
                    gdpr_compliant: 'GDPR Compliant',
                    hipaa_compliant: 'HIPAA Compliant',
                    soc2_compliant: 'SOC 2 Compliant',
                    iso27001_compliant: 'ISO 27001 Compliant',
                    penetration_tested: 'Penetration Tested',
                    security_audit: 'Security Audit',
                    vulnerability_scan: 'Vulnerability Scan',
                    threat_detection: 'Threat Detection',
                    incident_response: 'Incident Response',
                    security_monitoring: 'Security Monitoring',
                    risk_assessment: 'Risk Assessment',
                    security_policy: 'Security Policy',
                    privacy_policy: 'Privacy Policy',
                    data_protection: 'Data Protection',
                    consent_management: 'Consent Management',
                    right_to_erasure: 'Right to Erasure',
                    data_portability: 'Data Portability',
                    breach_notification: 'Breach Notification'
                }
            },

            es: {
                common: {
                    loading: 'Cargando...',
                    error: 'Error',
                    success: 'Éxito',
                    warning: 'Advertencia',
                    info: 'Información',
                    cancel: 'Cancelar',
                    confirm: 'Confirmar',
                    save: 'Guardar',
                    delete: 'Eliminar',
                    edit: 'Editar',
                    close: 'Cerrar',
                    back: 'Atrás',
                    next: 'Siguiente',
                    previous: 'Anterior',
                    finish: 'Finalizar',
                    retry: 'Reintentar',
                    refresh: 'Actualizar',
                    search: 'Buscar',
                    filter: 'Filtrar',
                    sort: 'Ordenar',
                    export: 'Exportar',
                    import: 'Importar',
                    download: 'Descargar',
                    upload: 'Subir',
                    settings: 'Configuración',
                    help: 'Ayuda',
                    about: 'Acerca de',
                    contact: 'Contacto',
                    privacy: 'Privacidad',
                    terms: 'Términos de Servicio',
                    language: 'Idioma',
                    theme: 'Tema',
                    profile: 'Perfil',
                    logout: 'Cerrar Sesión',
                    login: 'Iniciar Sesión',
                    signup: 'Registrarse',
                    email: 'Correo Electrónico',
                    password: 'Contraseña',
                    username: 'Nombre de Usuario',
                    name: 'Nombre',
                    phone: 'Teléfono',
                    address: 'Dirección',
                    city: 'Ciudad',
                    country: 'País',
                    date: 'Fecha',
                    time: 'Hora',
                    size: 'Tamaño',
                    type: 'Tipo',
                    status: 'Estado',
                    actions: 'Acciones',
                    details: 'Detalles',
                    description: 'Descripción',
                    title: 'Título',
                    category: 'Categoría',
                    tags: 'Etiquetas',
                    priority: 'Prioridad',
                    progress: 'Progreso',
                    completed: 'Completado',
                    pending: 'Pendiente',
                    failed: 'Fallido',
                    active: 'Activo',
                    inactive: 'Inactivo',
                    enabled: 'Habilitado',
                    disabled: 'Deshabilitado',
                    public: 'Público',
                    private: 'Privado',
                    shared: 'Compartido',
                    owner: 'Propietario',
                    created: 'Creado',
                    updated: 'Actualizado',
                    modified: 'Modificado',
                    version: 'Versión',
                    total: 'Total',
                    count: 'Cantidad',
                    amount: 'Cantidad',
                    price: 'Precio',
                    currency: 'Moneda',
                    free: 'Gratis',
                    premium: 'Premium',
                    trial: 'Prueba',
                    subscription: 'Suscripción',
                    billing: 'Facturación',
                    payment: 'Pago',
                    invoice: 'Factura',
                    receipt: 'Recibo'
                },

                safekeep: {
                    appName: 'SafeKeep',
                    tagline: 'Tus Datos, Seguros Para Siempre',
                    backup: 'Copia de Seguridad',
                    restore: 'Restaurar',
                    encryption: 'Cifrado',
                    security: 'Seguridad',
                    privacy: 'Privacidad',
                    storage: 'Almacenamiento',
                    sync: 'Sincronizar',
                    contacts: 'Contactos',
                    messages: 'Mensajes',
                    photos: 'Fotos',
                    documents: 'Documentos',
                    files: 'Archivos',
                    folders: 'Carpetas',
                    device: 'Dispositivo',
                    cloud: 'Nube',
                    local: 'Local',
                    remote: 'Remoto',
                    automatic: 'Automático',
                    manual: 'Manual',
                    scheduled: 'Programado',
                    realtime: 'Tiempo Real',
                    offline: 'Sin Conexión',
                    online: 'En Línea',
                    connected: 'Conectado',
                    disconnected: 'Desconectado',
                    syncing: 'Sincronizando',
                    synced: 'Sincronizado',
                    backing_up: 'Creando Copia',
                    backed_up: 'Copia Creada',
                    restoring: 'Restaurando',
                    restored: 'Restaurado',
                    encrypting: 'Cifrando',
                    encrypted: 'Cifrado',
                    decrypting: 'Descifrando',
                    decrypted: 'Descifrado'
                },

                navigation: {
                    dashboard: 'Panel de Control',
                    backups: 'Copias de Seguridad',
                    restore: 'Restaurar',
                    security: 'Seguridad',
                    settings: 'Configuración',
                    account: 'Cuenta',
                    billing: 'Facturación',
                    support: 'Soporte',
                    documentation: 'Documentación',
                    api: 'API',
                    integrations: 'Integraciones',
                    analytics: 'Analíticas',
                    reports: 'Informes',
                    logs: 'Registros',
                    monitoring: 'Monitoreo',
                    alerts: 'Alertas',
                    notifications: 'Notificaciones',
                    preferences: 'Preferencias',
                    advanced: 'Avanzado',
                    developer: 'Desarrollador',
                    admin: 'Administrador'
                },

                messages: {
                    welcome: '¡Bienvenido a SafeKeep!',
                    backup_started: 'La copia de seguridad ha comenzado',
                    backup_completed: 'Copia de seguridad completada exitosamente',
                    backup_failed: 'La copia de seguridad falló. Por favor, inténtalo de nuevo.',
                    restore_started: 'La restauración ha comenzado',
                    restore_completed: 'Restauración completada exitosamente',
                    restore_failed: 'La restauración falló. Por favor, inténtalo de nuevo.',
                    settings_saved: 'Configuración guardada exitosamente',
                    login_successful: 'Inicio de sesión exitoso',
                    login_failed: 'Error en el inicio de sesión',
                    network_offline: 'Actualmente estás sin conexión',
                    network_online: 'Conexión restaurada'
                },

                forms: {
                    required_field: 'Este campo es obligatorio',
                    invalid_email: 'Por favor, introduce una dirección de correo válida',
                    invalid_password: 'La contraseña debe tener al menos 8 caracteres',
                    password_mismatch: 'Las contraseñas no coinciden',
                    form_submitted: 'Formulario enviado exitosamente',
                    form_failed: 'Error al enviar el formulario'
                },

                time: {
                    now: 'Ahora',
                    today: 'Hoy',
                    yesterday: 'Ayer',
                    tomorrow: 'Mañana',
                    seconds_ago: 'hace {count} segundos',
                    minutes_ago: 'hace {count} minutos',
                    hours_ago: 'hace {count} horas',
                    days_ago: 'hace {count} días'
                },

                security: {
                    zero_knowledge: 'Cifrado de Conocimiento Cero',
                    end_to_end: 'Cifrado de Extremo a Extremo',
                    two_factor: 'Autenticación de Dos Factores',
                    secure_backup: 'Copia de Seguridad Segura',
                    data_protection: 'Protección de Datos'
                }
            },

            fr: {
                common: {
                    loading: 'Chargement...',
                    error: 'Erreur',
                    success: 'Succès',
                    warning: 'Avertissement',
                    info: 'Information',
                    cancel: 'Annuler',
                    confirm: 'Confirmer',
                    save: 'Enregistrer',
                    delete: 'Supprimer',
                    edit: 'Modifier',
                    close: 'Fermer',
                    back: 'Retour',
                    next: 'Suivant',
                    previous: 'Précédent',
                    finish: 'Terminer',
                    retry: 'Réessayer',
                    refresh: 'Actualiser',
                    search: 'Rechercher',
                    filter: 'Filtrer',
                    sort: 'Trier',
                    export: 'Exporter',
                    import: 'Importer',
                    download: 'Télécharger',
                    upload: 'Téléverser',
                    settings: 'Paramètres',
                    help: 'Aide',
                    language: 'Langue',
                    profile: 'Profil',
                    logout: 'Déconnexion',
                    login: 'Connexion',
                    email: 'E-mail',
                    password: 'Mot de passe'
                },

                safekeep: {
                    appName: 'SafeKeep',
                    tagline: 'Vos Données, Sécurisées Pour Toujours',
                    backup: 'Sauvegarde',
                    restore: 'Restaurer',
                    encryption: 'Chiffrement',
                    security: 'Sécurité',
                    privacy: 'Confidentialité',
                    contacts: 'Contacts',
                    messages: 'Messages',
                    photos: 'Photos'
                },

                navigation: {
                    dashboard: 'Tableau de Bord',
                    backups: 'Sauvegardes',
                    security: 'Sécurité',
                    settings: 'Paramètres'
                },

                messages: {
                    welcome: 'Bienvenue sur SafeKeep !',
                    backup_completed: 'Sauvegarde terminée avec succès',
                    login_successful: 'Connexion réussie'
                }
            },

            de: {
                common: {
                    loading: 'Laden...',
                    error: 'Fehler',
                    success: 'Erfolg',
                    cancel: 'Abbrechen',
                    save: 'Speichern',
                    settings: 'Einstellungen',
                    language: 'Sprache',
                    login: 'Anmelden',
                    email: 'E-Mail',
                    password: 'Passwort'
                },

                safekeep: {
                    appName: 'SafeKeep',
                    tagline: 'Ihre Daten, Für Immer Gesichert',
                    backup: 'Sicherung',
                    security: 'Sicherheit',
                    contacts: 'Kontakte'
                },

                navigation: {
                    dashboard: 'Dashboard',
                    backups: 'Sicherungen',
                    security: 'Sicherheit'
                },

                messages: {
                    welcome: 'Willkommen bei SafeKeep!',
                    login_successful: 'Anmeldung erfolgreich'
                }
            },

            // Add more languages with basic translations
            it: {
                common: {
                    loading: 'Caricamento...',
                    language: 'Lingua',
                    login: 'Accedi'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: 'Backup'
                }
            },

            pt: {
                common: {
                    loading: 'Carregando...',
                    language: 'Idioma',
                    login: 'Entrar'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: 'Backup'
                }
            },

            ru: {
                common: {
                    loading: 'Загрузка...',
                    language: 'Язык',
                    login: 'Войти'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: 'Резервная копия'
                }
            },

            zh: {
                common: {
                    loading: '加载中...',
                    language: '语言',
                    login: '登录'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: '备份'
                }
            },

            ja: {
                common: {
                    loading: '読み込み中...',
                    language: '言語',
                    login: 'ログイン'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: 'バックアップ'
                }
            },

            ko: {
                common: {
                    loading: '로딩 중...',
                    language: '언어',
                    login: '로그인'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: '백업'
                }
            },

            ar: {
                common: {
                    loading: 'جاري التحميل...',
                    language: 'اللغة',
                    login: 'تسجيل الدخول'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: 'نسخ احتياطي'
                }
            },

            hi: {
                common: {
                    loading: 'लोड हो रहा है...',
                    language: 'भाषा',
                    login: 'लॉगिन'
                },
                safekeep: {
                    appName: 'SafeKeep',
                    backup: 'बैकअप'
                }
            }
        };

        return translations[languageCode] || translations[this.fallbackLanguage];
    }

    async setLanguage(languageCode) {
        if (!this.supportedLanguages.has(languageCode)) {
            throw new Error(`Unsupported language: ${languageCode}`);
        }

        try {
            await this.loadTranslations(languageCode);
            this.currentLanguage = languageCode;
            
            // Save preference
            localStorage.setItem('safekeep-language', languageCode);
            
            // Update document
            this.updateDocumentLanguage();
            
            // Trigger language change event
            this.dispatchLanguageChangeEvent();
            
            return true;
        } catch (error) {
            console.error('Failed to set language:', error);
            return false;
        }
    }

    updateDocumentLanguage() {
        const lang = this.supportedLanguages.get(this.currentLanguage);
        if (lang) {
            document.documentElement.lang = lang.code;
            document.documentElement.dir = lang.rtl ? 'rtl' : 'ltr';
            
            // Update CSS classes for RTL support
            if (lang.rtl) {
                document.body.classList.add('rtl');
                document.body.classList.remove('ltr');
            } else {
                document.body.classList.add('ltr');
                document.body.classList.remove('rtl');
            }
        }
    }

    dispatchLanguageChangeEvent() {
        const event = new CustomEvent('languageChanged', {
            detail: {
                language: this.currentLanguage,
                languageInfo: this.supportedLanguages.get(this.currentLanguage)
            }
        });
        window.dispatchEvent(event);
    }

    // Translation methods
    t(key, params = {}) {
        return this.translate(key, params);
    }

    translate(key, params = {}) {
        const translations = this.translations.get(this.currentLanguage);
        if (!translations) {
            console.warn(`No translations loaded for ${this.currentLanguage}`);
            return key;
        }

        const value = this.getNestedValue(translations, key);
        if (value === undefined) {
            // Try fallback language
            const fallbackTranslations = this.translations.get(this.fallbackLanguage);
            if (fallbackTranslations) {
                const fallbackValue = this.getNestedValue(fallbackTranslations, key);
                if (fallbackValue !== undefined) {
                    return this.interpolate(fallbackValue, params);
                }
            }
            
            console.warn(`Translation not found for key: ${key}`);
            return key;
        }

        return this.interpolate(value, params);
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    interpolate(template, params) {
        if (typeof template !== 'string') {
            return template;
        }

        return template.replace(/\{(\w+)\}/g, (match, key) => {
            return params[key] !== undefined ? params[key] : match;
        });
    }

    // Formatting methods
    formatNumber(number, options = {}) {
        const formatter = this.formatters.get(`number-${this.currentLanguage}`);
        if (formatter) {
            return formatter.format(number);
        }
        return number.toString();
    }

    formatCurrency(amount, currency = null) {
        const lang = this.supportedLanguages.get(this.currentLanguage);
        const currencyCode = currency || lang?.currency || 'USD';
        
        try {
            const formatter = new Intl.NumberFormat(lang?.numberFormat || 'en-US', {
                style: 'currency',
                currency: currencyCode
            });
            return formatter.format(amount);
        } catch (error) {
            console.warn('Currency formatting failed:', error);
            return `${currencyCode} ${amount}`;
        }
    }

    formatDate(date, options = {}) {
        const lang = this.supportedLanguages.get(this.currentLanguage);
        
        try {
            const formatter = new Intl.DateTimeFormat(lang?.numberFormat || 'en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                ...options
            });
            return formatter.format(date);
        } catch (error) {
            console.warn('Date formatting failed:', error);
            return date.toLocaleDateString();
        }
    }

    formatTime(date, options = {}) {
        const lang = this.supportedLanguages.get(this.currentLanguage);
        
        try {
            const formatter = new Intl.DateTimeFormat(lang?.numberFormat || 'en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: lang?.timeFormat === '12h',
                ...options
            });
            return formatter.format(date);
        } catch (error) {
            console.warn('Time formatting failed:', error);
            return date.toLocaleTimeString();
        }
    }

    formatRelativeTime(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) {
            return this.translate('time.seconds_ago', { count: diffInSeconds });
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return this.translate('time.minutes_ago', { count: minutes });
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return this.translate('time.hours_ago', { count: hours });
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return this.translate('time.days_ago', { count: days });
        }
    }

    // Utility methods
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    getCurrentLanguageInfo() {
        return this.supportedLanguages.get(this.currentLanguage);
    }

    getSupportedLanguages() {
        return Array.from(this.supportedLanguages.values());
    }

    isRTL() {
        return this.rtlLanguages.has(this.currentLanguage);
    }

    async initialize() {
        try {
            await this.loadTranslations(this.currentLanguage);
            
            // Load fallback language if different
            if (this.currentLanguage !== this.fallbackLanguage) {
                await this.loadTranslations(this.fallbackLanguage);
            }
            
            this.updateDocumentLanguage();
            return true;
        } catch (error) {
            console.error('Failed to initialize i18n:', error);
            return false;
        }
    }

    // DOM manipulation helpers
    translateElement(element) {
        // Translate text content
        const textKey = element.getAttribute('data-i18n');
        if (textKey) {
            element.textContent = this.translate(textKey);
        }

        // Translate attributes
        const attributes = ['title', 'placeholder', 'alt', 'aria-label'];
        attributes.forEach(attr => {
            const attrKey = element.getAttribute(`data-i18n-${attr}`);
            if (attrKey) {
                element.setAttribute(attr, this.translate(attrKey));
            }
        });
    }

    translatePage() {
        // Translate all elements with data-i18n attributes
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => this.translateElement(element));

        // Translate elements with attribute translations
        const attrElements = document.querySelectorAll('[data-i18n-title], [data-i18n-placeholder], [data-i18n-alt], [data-i18n-aria-label]');
        attrElements.forEach(element => this.translateElement(element));
    }
}

// Create global instance
const i18n = new I18nManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = I18nManager;
}

// Make available globally
window.i18n = i18n;