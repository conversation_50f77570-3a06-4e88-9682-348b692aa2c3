import { BackupDatabaseService } from '../BackupDatabaseService';
import { supabase } from '../../config/supabase';
import { BackupConfiguration } from '../../types/backup';

// Mock the supabase client
jest.mock('../../config/supabase', () => ({
  supabase: {
    from: jest.fn(),
    rpc: jest.fn(),
    channel: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('BackupDatabaseService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createBackupSession', () => {
    it('should create a new backup session successfully', async () => {
      const mockSession = {
        id: 'session-123',
        user_id: 'user-123',
        total_items: 100,
        status: 'pending',
        configuration: { autoBackup: true, wifiOnly: true },
        start_time: '2024-01-01T00:00:00Z',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockSession, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const configuration: BackupConfiguration = {
        autoBackup: true,
        wifiOnly: true,
        includeContacts: true,
        includeMessages: true,
        includePhotos: true,
        compressionLevel: 'medium',
      };

      const result = await BackupDatabaseService.createBackupSession(100, configuration);

      expect(mockSupabase.from).toHaveBeenCalledWith('backup_sessions');
      expect(mockQuery.insert).toHaveBeenCalledWith({
        total_items: 100,
        configuration,
        status: 'pending',
      });
      expect(result).toEqual(mockSession);
    });

    it('should throw error when creation fails', async () => {
      const mockQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: null, 
          error: { message: 'Database error' } 
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const configuration: BackupConfiguration = {
        autoBackup: true,
        wifiOnly: true,
        includeContacts: true,
        includeMessages: true,
        includePhotos: true,
        compressionLevel: 'medium',
      };

      await expect(
        BackupDatabaseService.createBackupSession(100, configuration)
      ).rejects.toThrow('Failed to create backup session: Database error');
    });
  });

  describe('updateBackupSession', () => {
    it('should update backup session successfully', async () => {
      const mockSession = {
        id: 'session-123',
        status: 'completed',
        end_time: '2024-01-01T01:00:00Z',
      };

      const mockQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockSession, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const updates = { status: 'completed' as const, end_time: '2024-01-01T01:00:00Z' };
      const result = await BackupDatabaseService.updateBackupSession('session-123', updates);

      expect(mockQuery.update).toHaveBeenCalledWith(updates);
      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'session-123');
      expect(result).toEqual(mockSession);
    });
  });

  describe('getBackupSession', () => {
    it('should get backup session successfully', async () => {
      const mockSession = {
        id: 'session-123',
        user_id: 'user-123',
        status: 'completed',
      };

      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockSession, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await BackupDatabaseService.getBackupSession('session-123');

      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'session-123');
      expect(result).toEqual(mockSession);
    });

    it('should return null when session not found', async () => {
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ 
          data: null, 
          error: { code: 'PGRST116' } 
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await BackupDatabaseService.getBackupSession('session-123');

      expect(result).toBeNull();
    });
  });

  describe('createBackupItem', () => {
    it('should create backup item successfully', async () => {
      const mockItem = {
        id: 'item-123',
        session_id: 'session-123',
        item_type: 'photo',
        original_id: 'photo-456',
        status: 'pending',
      };

      const mockQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockItem, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const encryptedData = new Uint8Array([1, 2, 3, 4]);
      const metadata = { size: 1024, timestamp: '2024-01-01T00:00:00Z' };

      const result = await BackupDatabaseService.createBackupItem(
        'session-123',
        'photo',
        'photo-456',
        encryptedData,
        metadata,
        '/path/to/file',
        'checksum123',
        1024
      );

      expect(mockQuery.insert).toHaveBeenCalledWith({
        session_id: 'session-123',
        item_type: 'photo',
        original_id: 'photo-456',
        encrypted_data: encryptedData,
        metadata,
        file_path: '/path/to/file',
        checksum: 'checksum123',
        size_bytes: 1024,
        status: 'pending',
      });
      expect(result).toEqual(mockItem);
    });
  });

  describe('initializeBackupProgress', () => {
    it('should initialize backup progress successfully', async () => {
      mockSupabase.rpc.mockResolvedValue({ error: null });

      await BackupDatabaseService.initializeBackupProgress({
        session_id: 'session-123',
        contacts_total: 100,
        messages_total: 500,
        photos_total: 1000,
      });

      expect(mockSupabase.rpc).toHaveBeenCalledWith('initialize_backup_progress', {
        p_session_id: 'session-123',
        p_contacts_total: 100,
        p_messages_total: 500,
        p_photos_total: 1000,
      });
    });

    it('should throw error when initialization fails', async () => {
      mockSupabase.rpc.mockResolvedValue({ error: { message: 'RPC error' } });

      await expect(
        BackupDatabaseService.initializeBackupProgress({
          session_id: 'session-123',
          contacts_total: 100,
        })
      ).rejects.toThrow('Failed to initialize backup progress: RPC error');
    });
  });

  describe('updateBackupProgress', () => {
    it('should update backup progress successfully', async () => {
      mockSupabase.rpc.mockResolvedValue({ error: null });

      await BackupDatabaseService.updateBackupProgress({
        session_id: 'session-123',
        item_type: 'photo',
        completed_increment: 1,
        failed_increment: 0,
      });

      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_backup_progress', {
        p_session_id: 'session-123',
        p_item_type: 'photo',
        p_completed_increment: 1,
        p_failed_increment: 0,
      });
    });
  });

  describe('getBackupProgress', () => {
    it('should get backup progress successfully', async () => {
      const mockProgress = [
        {
          session_id: 'session-123',
          item_type: 'photo',
          total_count: 1000,
          completed_count: 500,
          failed_count: 10,
        },
      ];

      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ data: mockProgress, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await BackupDatabaseService.getBackupProgress('session-123');

      expect(mockQuery.eq).toHaveBeenCalledWith('session_id', 'session-123');
      expect(result).toEqual(mockProgress);
    });
  });

  describe('getBackupSessionSummary', () => {
    it('should get backup session summary successfully', async () => {
      const mockSummary = {
        session_id: 'session-123',
        status: 'completed',
        total_items: 1600,
        completed_items: 1590,
        contacts_total: 100,
        contacts_completed: 100,
        messages_total: 500,
        messages_completed: 490,
        photos_total: 1000,
        photos_completed: 1000,
      };

      mockSupabase.rpc.mockResolvedValue({ data: [mockSummary], error: null });

      const result = await BackupDatabaseService.getBackupSessionSummary('session-123');

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_backup_session_summary', {
        p_session_id: 'session-123',
      });
      expect(result).toEqual(mockSummary);
    });

    it('should return null when no summary found', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: [], error: null });

      const result = await BackupDatabaseService.getBackupSessionSummary('session-123');

      expect(result).toBeNull();
    });
  });

  describe('cancelBackupSession', () => {
    it('should cancel backup session successfully', async () => {
      const mockSession = {
        id: 'session-123',
        status: 'cancelled',
        end_time: expect.any(String),
      };

      const mockQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockSession, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await BackupDatabaseService.cancelBackupSession('session-123');

      expect(mockQuery.update).toHaveBeenCalledWith({
        status: 'cancelled',
        end_time: expect.any(String),
      });
      expect(result).toEqual(mockSession);
    });
  });

  describe('getBackupStatistics', () => {
    it('should get backup statistics successfully', async () => {
      const mockSessions = [
        { status: 'completed', start_time: '2024-01-03T00:00:00Z' },
        { status: 'completed', start_time: '2024-01-02T00:00:00Z' },
        { status: 'failed', start_time: '2024-01-01T00:00:00Z' },
      ];

      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ data: mockSessions, error: null }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await BackupDatabaseService.getBackupStatistics();

      expect(result).toEqual({
        totalSessions: 3,
        successfulSessions: 2,
        failedSessions: 1,
        lastBackupTime: '2024-01-03T00:00:00Z',
      });
    });
  });

  describe('cleanupOldBackupSessions', () => {
    it('should cleanup old backup sessions successfully', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: 5, error: null });

      const result = await BackupDatabaseService.cleanupOldBackupSessions();

      expect(mockSupabase.rpc).toHaveBeenCalledWith('cleanup_old_backup_sessions');
      expect(result).toBe(5);
    });
  });

  describe('subscribeToBackupProgress', () => {
    it('should set up real-time subscription for backup progress', () => {
      const mockChannel = {
        on: jest.fn().mockReturnThis(),
        subscribe: jest.fn(),
      };

      mockSupabase.channel.mockReturnValue(mockChannel as any);

      const callback = jest.fn();
      BackupDatabaseService.subscribeToBackupProgress('session-123', callback);

      expect(mockSupabase.channel).toHaveBeenCalledWith('backup_progress_session-123');
      expect(mockChannel.on).toHaveBeenCalledWith(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'backup_progress',
          filter: 'session_id=eq.session-123',
        },
        expect.any(Function)
      );
      expect(mockChannel.subscribe).toHaveBeenCalled();
    });
  });

  describe('subscribeToBackupSession', () => {
    it('should set up real-time subscription for backup session', () => {
      const mockChannel = {
        on: jest.fn().mockReturnThis(),
        subscribe: jest.fn(),
      };

      mockSupabase.channel.mockReturnValue(mockChannel as any);

      const callback = jest.fn();
      BackupDatabaseService.subscribeToBackupSession('session-123', callback);

      expect(mockSupabase.channel).toHaveBeenCalledWith('backup_session_session-123');
      expect(mockChannel.on).toHaveBeenCalledWith(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'backup_sessions',
          filter: 'id=eq.session-123',
        },
        expect.any(Function)
      );
      expect(mockChannel.subscribe).toHaveBeenCalled();
    });
  });
});