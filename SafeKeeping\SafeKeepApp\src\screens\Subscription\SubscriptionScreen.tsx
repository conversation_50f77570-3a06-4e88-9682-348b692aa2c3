import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import {
  Button,
  Card,
  Text,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  useStripe,
  useConfirmPayment,
  CardField,
  CardFieldInput,
} from '@stripe/stripe-react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { COLORS, SPACING } from '../../utils/constants';
import { SAFEKEEP_PLANS, StripeService } from '../../services/StripeService';
import { RootStackParamList } from '../../navigation/AppNavigator';

type PlanType = keyof typeof SAFEKEEP_PLANS;
type SubscriptionScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const SubscriptionScreen = () => {
  const navigation = useNavigation<SubscriptionScreenNavigationProp>();
  const { confirmPayment } = useConfirmPayment();
  const [selectedPlan, setSelectedPlan] = useState<PlanType>('PREMIUM');
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardDetails, setCardDetails] = useState<CardFieldInput.Details | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  const handlePlanSelect = (planType: PlanType) => {
    setSelectedPlan(planType);
    setShowPaymentForm(false);
  };

  const handleSubscribe = () => {
    setShowPaymentForm(true);
  };

  const handlePayment = async () => {
    if (!cardDetails?.complete) {
      Alert.alert('Incomplete Card', 'Please enter complete card details.');
      return;
    }

    setIsProcessing(true);

    try {
      const plan = SAFEKEEP_PLANS[selectedPlan];
      
      // Create payment intent via backend
      const paymentIntentResponse = await StripeService.createPaymentIntent({
        amount: plan.price,
        currency: plan.currency,
        description: `SafeKeep ${plan.name} Subscription`,
        planId: plan.id,
      });

      if (!paymentIntentResponse.client_secret) {
        throw new Error('Failed to create payment intent');
      }

      // Confirm payment with Stripe
      const { error, paymentIntent } = await confirmPayment(
        paymentIntentResponse.client_secret,
        {
          paymentMethodType: 'Card',
          paymentMethodData: {
            billingDetails: {
              email: '<EMAIL>', // In production, get from user profile
            },
          },
        }
      );

      if (error) {
        console.error('Payment confirmation error:', error);
        Alert.alert('Payment Failed', error.message || 'Payment failed. Please try again.');
      } else if (paymentIntent) {
        console.log('Payment successful:', paymentIntent);
        Alert.alert(
          '🎉 Welcome to SafeKeep Premium!',
          `Your ${plan.name} subscription is now active!\n\nYou now have access to:\n${plan.features.map(f => `• ${f}`).join('\n')}\n\nLet's set up your device permissions to start backing up your data!`,
          [
            {
              text: 'Set Up Permissions',
              onPress: () => {
                setShowPaymentForm(false);
                navigation.navigate('PermissionSetup');
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert('Payment Error', error.message || 'An unexpected error occurred.');
    } finally {
      setIsProcessing(false);
    }
  };



  const renderPlanCard = (planType: PlanType) => {
    const plan = SAFEKEEP_PLANS[planType];
    const isSelected = selectedPlan === planType;
    const isPopular = planType === 'PREMIUM';

    return (
      <Card 
        key={planType}
        style={[
          styles.planCard,
          isSelected && styles.selectedPlanCard,
          isPopular && styles.popularPlanCard
        ]}
        onPress={() => handlePlanSelect(planType)}
      >
        <Card.Content>
          {isPopular && (
            <Chip style={styles.popularChip} textStyle={styles.popularChipText}>
              Most Popular
            </Chip>
          )}
          
          <Text variant="headlineSmall" style={styles.planName}>
            {plan.name}
          </Text>
          
          <View style={styles.priceContainer}>
            <Text variant="headlineLarge" style={styles.planPrice}>
              ${(plan.price / 100).toFixed(2)}
            </Text>
            <Text variant="bodyMedium" style={styles.planInterval}>
              /{plan.interval}
            </Text>
          </View>

          <View style={styles.featuresContainer}>
            {plan.features.map((feature, index) => (
              <View key={index} style={styles.featureRow}>
                <Icon name="check-circle" size={16} color={COLORS.success} />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>

          {isSelected && (
            <Icon 
              name="check-circle" 
              size={24} 
              color={COLORS.primary} 
              style={styles.selectedIcon}
            />
          )}
        </Card.Content>
      </Card>
    );
  };

  if (showPaymentForm) {
    const plan = SAFEKEEP_PLANS[selectedPlan];
    
    return (
      <SafeAreaView style={styles.container}>
        <Header
          centerComponent={{
            text: 'Complete Payment',
            style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
          }}
          leftComponent={{
            icon: 'arrow-back',
            color: '#fff',
            onPress: () => setShowPaymentForm(false)
          }}
          backgroundColor={COLORS.primary}
        />

        <ScrollView style={styles.content}>
          {/* Selected Plan Summary */}
          <Card style={styles.summaryCard}>
            <Card.Content>
              <Text variant="titleLarge" style={styles.summaryTitle}>
                {plan.name}
              </Text>
              <Text variant="headlineMedium" style={styles.summaryPrice}>
                ${(plan.price / 100).toFixed(2)}/{plan.interval}
              </Text>
              <Text style={styles.summaryDescription}>
                You're subscribing to {plan.name} with all premium features included.
              </Text>
            </Card.Content>
          </Card>

          {/* Payment Form */}
          <Card style={styles.paymentCard}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.paymentTitle}>
                Payment Information
              </Text>
              
              <Text style={styles.securityNote}>
                🔒 Your payment information is encrypted and secure
              </Text>

              {/* Test Card Info */}
              {__DEV__ && (
                <View style={styles.testCardInfo}>
                  <Text style={styles.testCardTitle}>Test Card Numbers:</Text>
                  <Text style={styles.testCardNumber}>✅ Success: 4242 4242 4242 4242</Text>
                  <Text style={styles.testCardNumber}>❌ Declined: 4000 0000 0000 0002</Text>
                  <Text style={styles.testCardNote}>Use any future expiry and any CVC</Text>
                </View>
              )}

              {/* Stripe Card Input */}
              <View style={styles.cardFieldContainer}>
                <CardField
                  postalCodeEnabled={true}
                  placeholders={{
                    number: '4242 4242 4242 4242',
                    expiration: 'MM/YY',
                    cvc: 'CVC',
                    postalCode: 'ZIP',
                  }}
                  cardStyle={{
                    backgroundColor: '#FFFFFF',
                    textColor: '#000000',
                    fontSize: 16,
                    placeholderColor: '#999999',
                    borderWidth: 1,
                    borderColor: '#E0E0E0',
                    borderRadius: 8,
                  }}
                  style={styles.cardField}
                  onCardChange={(cardDetails) => {
                    setCardDetails(cardDetails);
                  }}
                />
              </View>

              {/* Payment Button */}
              <Button
                mode="contained"
                onPress={handlePayment}
                disabled={!cardDetails?.complete || isProcessing}
                style={styles.paymentButton}
                contentStyle={styles.paymentButtonContent}
              >
                {isProcessing ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  `Subscribe for $${(plan.price / 100).toFixed(2)}/${plan.interval}`
                )}
              </Button>

              <Text style={styles.disclaimer}>
                By subscribing, you agree to SafeKeep's Terms of Service and Privacy Policy.
                You can cancel anytime from your account settings.
              </Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Choose Your Plan',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.headerTitle}>
            Secure Your Precious Data
          </Text>
          <Text style={styles.headerSubtitle}>
            Choose the perfect plan for your backup needs. All plans include end-to-end encryption and secure cloud storage.
          </Text>
        </View>

        {/* Plans */}
        <View style={styles.plansContainer}>
          {(Object.keys(SAFEKEEP_PLANS) as PlanType[]).map(renderPlanCard)}
        </View>

        {/* Subscribe Button */}
        <Button
          mode="contained"
          onPress={handleSubscribe}
          style={styles.subscribeButton}
          contentStyle={styles.subscribeButtonContent}
        >
          Continue with {SAFEKEEP_PLANS[selectedPlan].name}
        </Button>

        {/* Skip Option for Trial */}
        <Button
          mode="text"
          onPress={() => {
            Alert.alert(
              '🆓 Try SafeKeep Free',
              'You can explore SafeKeep with limited features and subscribe later when you\'re ready. You\'ll have access to basic backup features.',
              [
                { text: 'Subscribe Now', style: 'default' },
                {
                  text: 'Try Free Version',
                  onPress: () => navigation.navigate('PermissionSetup'),
                  style: 'cancel'
                }
              ]
            );
          }}
          style={styles.skipButton}
        >
          Try Free Version First
        </Button>

        {/* Features Highlight */}
        <Card style={styles.featuresCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.featuresTitle}>
              Why Choose SafeKeep?
            </Text>
            
            <View style={styles.highlightFeatures}>
              <View style={styles.highlightFeature}>
                <Icon name="shield-check" size={24} color={COLORS.primary} />
                <Text style={styles.highlightFeatureText}>
                  End-to-end encryption keeps your data private
                </Text>
              </View>
              
              <View style={styles.highlightFeature}>
                <Icon name="cloud-upload" size={24} color={COLORS.primary} />
                <Text style={styles.highlightFeatureText}>
                  Automatic backup to secure cloud storage
                </Text>
              </View>
              
              <View style={styles.highlightFeature}>
                <Icon name="devices" size={24} color={COLORS.primary} />
                <Text style={styles.highlightFeatureText}>
                  Access your data from any device, anywhere
                </Text>
              </View>
              
              <View style={styles.highlightFeature}>
                <Icon name="heart" size={24} color={COLORS.primary} />
                <Text style={styles.highlightFeatureText}>
                  Designed for grandparents and families
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  headerTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  plansContainer: {
    marginBottom: SPACING.xl,
  },
  planCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedPlanCard: {
    borderColor: COLORS.primary,
    backgroundColor: 'rgba(74, 144, 226, 0.05)',
  },
  popularPlanCard: {
    borderColor: COLORS.success,
  },
  popularChip: {
    backgroundColor: COLORS.success,
    alignSelf: 'flex-start',
    marginBottom: SPACING.sm,
  },
  popularChipText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  planName: {
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: SPACING.md,
  },
  planPrice: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  planInterval: {
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  featuresContainer: {
    marginTop: SPACING.sm,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  featureText: {
    color: COLORS.text,
    marginLeft: SPACING.sm,
    flex: 1,
  },
  selectedIcon: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
  },
  subscribeButton: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.xl,
  },
  subscribeButtonContent: {
    height: 50,
  },
  skipButton: {
    marginTop: SPACING.md,
    alignSelf: 'center',
  },
  featuresCard: {
    elevation: 4,
    marginBottom: SPACING.xl,
  },
  featuresTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  highlightFeatures: {
    gap: SPACING.md,
  },
  highlightFeature: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  highlightFeatureText: {
    color: COLORS.text,
    marginLeft: SPACING.md,
    flex: 1,
    lineHeight: 20,
  },
  summaryCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
    backgroundColor: 'rgba(74, 144, 226, 0.05)',
  },
  summaryTitle: {
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  summaryPrice: {
    color: COLORS.primary,
    fontWeight: 'bold',
    marginBottom: SPACING.sm,
  },
  summaryDescription: {
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  paymentCard: {
    elevation: 4,
    marginBottom: SPACING.xl,
  },
  paymentTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  securityNote: {
    color: COLORS.success,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  testCardInfo: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    padding: SPACING.md,
    borderRadius: 8,
    marginBottom: SPACING.lg,
  },
  testCardTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
    color: COLORS.warning,
  },
  testCardNumber: {
    fontFamily: 'monospace',
    fontSize: 12,
    marginBottom: SPACING.xs,
  },
  testCardNote: {
    fontSize: 11,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  cardFieldContainer: {
    marginBottom: SPACING.lg,
  },
  cardField: {
    width: '100%',
    height: 50,
    marginVertical: SPACING.sm,
  },
  paymentButton: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.md,
  },
  paymentButtonContent: {
    height: 50,
  },
  disclaimer: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default SubscriptionScreen;
