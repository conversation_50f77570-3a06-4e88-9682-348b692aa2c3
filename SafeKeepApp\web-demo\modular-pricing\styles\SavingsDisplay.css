/* SavingsDisplay Component Styles */

.savings-display {
    margin: 20px 0;
    transition: all 0.3s ease;
}

.savings-display.has-savings {
    animation: savingsAppear 0.5s ease-out;
}

@keyframes savingsAppear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Savings indicator */
.savings-indicator {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    border: 2px solid #28a745;
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    position: relative;
    overflow: hidden;
}

.savings-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(40, 167, 69, 0.1),
        transparent
    );
    animation: savingsShimmer 2s infinite;
}

@keyframes savingsShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.savings-icon {
    font-size: 2rem;
    flex-shrink: 0;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.savings-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.savings-label {
    color: #155724;
    font-weight: 600;
    font-size: 1.1rem;
}

.savings-amount {
    color: #28a745;
    font-weight: 700;
    font-size: 1.4rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.savings-percentage {
    color: #155724;
    font-weight: 500;
    font-size: 1rem;
    background: rgba(40, 167, 69, 0.15);
    padding: 4px 12px;
    border-radius: 20px;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

/* Savings message */
.savings-message {
    text-align: center;
    margin-bottom: 16px;
}

.savings-message-text {
    margin: 0;
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.95rem;
    line-height: 1.4;
}

.savings-message-text.message-excellent {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #28a745;
}

.savings-message-text.message-good {
    background: linear-gradient(135deg, #e2f3ff 0%, #cce7ff 100%);
    color: #0c5460;
    border: 1px solid #17a2b8;
}

.savings-message-text.message-basic {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffc107;
}

/* Comparison view */
.savings-comparison {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-top: 16px;
}

.comparison-title {
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 16px 0;
    text-align: center;
    padding-bottom: 12px;
    border-bottom: 2px solid #f8f9fa;
}

.comparison-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.comparison-section.individual {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
}

.comparison-section.bundle {
    background: #d4edda;
    border-left: 4px solid #28a745;
}

.comparison-label {
    font-weight: 500;
    color: #333;
    flex: 1;
}

.comparison-price {
    font-weight: 600;
    font-size: 1.1rem;
}

.comparison-price.original {
    color: #dc3545;
    text-decoration: line-through;
    opacity: 0.8;
}

.comparison-price.discounted {
    color: #28a745;
}

/* No savings content */
.no-savings-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    text-align: center;
}

.info-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.info-message {
    margin: 0;
    color: #666;
    font-weight: 500;
    font-size: 0.95rem;
    flex: 1;
}

/* Highlight animation */
.savings-display.savings-highlight {
    animation: savingsHighlight 1s ease-out;
}

@keyframes savingsHighlight {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 15px rgba(40, 167, 69, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Achievement popup */
.savings-achievement {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    animation: achievementPopup 2s ease-out forwards;
}

.achievement-content {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 12px;
}

.achievement-icon {
    font-size: 2rem;
    animation: achievementBounce 0.6s ease-out;
}

.achievement-text {
    font-weight: 600;
    font-size: 1.2rem;
}

@keyframes achievementPopup {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
    30% {
        transform: translate(-50%, -50%) scale(1);
    }
    90% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

@keyframes achievementBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Mobile responsive design */
@media (max-width: 768px) {
    .savings-indicator {
        padding: 16px;
        gap: 12px;
        flex-direction: column;
        text-align: center;
    }
    
    .savings-content {
        justify-content: center;
        gap: 8px;
    }
    
    .savings-amount {
        font-size: 1.2rem;
    }
    
    .savings-percentage {
        font-size: 0.9rem;
        padding: 3px 10px;
    }
    
    .savings-comparison {
        padding: 16px;
    }
    
    .comparison-section {
        padding: 10px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }
    
    .comparison-price {
        align-self: flex-end;
        font-size: 1rem;
    }
    
    .no-savings-content {
        padding: 12px 16px;
        flex-direction: column;
        gap: 8px;
    }
    
    .achievement-content {
        padding: 16px 24px;
        margin: 0 20px;
    }
    
    .achievement-text {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .savings-indicator {
        padding: 12px;
        border-radius: 12px;
    }
    
    .savings-icon {
        font-size: 1.5rem;
    }
    
    .savings-label {
        font-size: 1rem;
    }
    
    .savings-amount {
        font-size: 1.1rem;
    }
    
    .savings-message-text {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .comparison-title {
        font-size: 1rem;
    }
    
    .comparison-section {
        padding: 8px 10px;
    }
    
    .comparison-label {
        font-size: 0.9rem;
    }
    
    .comparison-price {
        font-size: 0.95rem;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .savings-indicator {
        border-width: 3px;
        border-color: #000;
    }
    
    .savings-comparison {
        border-width: 2px;
        border-color: #000;
    }
    
    .comparison-section.individual {
        border-left-width: 6px;
        border-left-color: #000;
    }
    
    .comparison-section.bundle {
        border-left-width: 6px;
        border-left-color: #000;
    }
    
    .no-savings-content {
        border-width: 2px;
        border-color: #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .savings-display,
    .savings-indicator,
    .comparison-section {
        transition: none;
    }
    
    .savings-display.has-savings,
    .savings-display.savings-highlight {
        animation: none;
    }
    
    .savings-icon {
        animation: none;
    }
    
    .savings-indicator::before {
        animation: none;
    }
    
    .savings-achievement {
        animation: none;
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    
    .achievement-icon {
        animation: none;
    }
}

/* Print styles */
@media print {
    .savings-display {
        break-inside: avoid;
    }
    
    .savings-indicator {
        border: 2px solid #000;
        background: #f0f0f0;
    }
    
    .savings-achievement {
        display: none;
    }
}