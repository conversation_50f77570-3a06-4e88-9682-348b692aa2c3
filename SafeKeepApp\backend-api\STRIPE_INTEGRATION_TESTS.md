# Stripe Integration Tests

This document describes the comprehensive Stripe integration tests for the modular pricing backend API.

## Overview

The Stripe integration tests verify that our billing system correctly handles:
- Payment intent creation with service combination metadata
- Subscription management through Stripe API
- Webhook processing for modular subscription events
- Billing accuracy for different service combinations

## Test Categories

### 1. Payment Intent Creation with Service Combination Metadata

Tests that payment intents are created correctly with:
- Proper metadata for service combinations
- Correct amounts for different service plans
- Proper currency and payment method settings

**Key Test Cases:**
- Single service subscriptions (contacts, messages, photos)
- Multiple service combinations
- Full backup plans
- Metadata validation

### 2. Subscription Management Through Stripe API

Tests subscription lifecycle management:
- Creating subscriptions with service metadata
- Updating subscriptions with new service combinations
- Canceling subscriptions
- Customer management

**Key Test Cases:**
- Subscription creation with various service combinations
- Subscription updates and service changes
- Subscription cancellation
- Customer retrieval by user ID

### 3. Webhook Processing for Modular Subscription Events

Tests webhook event handling for:
- Invoice payment events
- Subscription lifecycle events
- Payment intent events
- Error handling for malformed events

**Key Test Cases:**
- `invoice.payment_succeeded` events
- `customer.subscription.created` events
- `customer.subscription.updated` events with status changes
- `payment_intent.succeeded` for modular subscriptions
- `payment_intent.payment_failed` for modular subscriptions
- Webhook signature verification

### 4. Billing Accuracy for Different Service Combinations

Tests that billing amounts are correct for:
- Individual services
- Service combinations
- Billing cycles
- Pricing calculations

**Key Test Cases:**
- Contacts only: $0.99
- Messages only: $1.49
- Photos only: $2.99
- Contacts + Messages: $2.49
- Contacts + Photos: $3.99
- Messages + Photos: $4.49
- Full Backup (all services): $5.49

### 5. Error Handling and Edge Cases

Tests error scenarios:
- Invalid payment amounts
- Invalid customer IDs
- Malformed webhook events
- Network failures
- Stripe API errors

## Running the Tests

### Prerequisites

1. **Stripe Test Account**: You need a Stripe test account with test API keys
2. **Environment Variables**: Set the following environment variables:
   ```bash
   STRIPE_SECRET_KEY=sk_test_your_test_key_here
   ```

### Running All Tests

```bash
# Run all tests including Stripe integration
npm test

# Run only Stripe integration tests
node test-stripe-integration.js

# Run with coverage
npm run test:coverage
```

### Running Specific Test Suites

```bash
# Run only payment intent tests
npx jest --testNamePattern="Payment Intent Creation"

# Run only webhook tests
npx jest --testNamePattern="Webhook Processing"

# Run only billing accuracy tests
npx jest --testNamePattern="Billing Accuracy"
```

## Test Environment Setup

The tests use the following setup:

1. **Jest Configuration**: Custom Jest config with TypeScript support
2. **Test Timeout**: 30 seconds for integration tests
3. **Environment Variables**: Loaded from `.env.test` file
4. **Cleanup**: Automatic cleanup of test resources after each test

## Test Data

The tests use the following test data patterns:

- **User IDs**: `test-user-{purpose}` (e.g., `test-user-123`, `test-user-webhook`)
- **Service Combinations**: Various combinations of `['contacts', 'messages', 'photos']`
- **Plan Names**: Descriptive names like "Contacts + Messages", "Full Backup"

## Expected Results

### Success Criteria

All tests should pass with:
- ✅ Payment intents created with correct metadata
- ✅ Subscriptions managed successfully
- ✅ Webhooks processed correctly
- ✅ Billing amounts calculated accurately
- ✅ Error cases handled gracefully

### Performance Expectations

- Individual tests should complete within 10 seconds
- Full test suite should complete within 5 minutes
- No memory leaks or hanging connections

## Troubleshooting

### Common Issues

1. **Invalid Stripe Key**: Ensure you're using a test key (`sk_test_...`)
2. **Network Timeouts**: Check internet connection and Stripe API status
3. **Rate Limiting**: Stripe test mode has rate limits; add delays if needed
4. **Resource Cleanup**: Tests clean up resources, but manual cleanup may be needed

### Debug Mode

Run tests with debug output:

```bash
DEBUG=stripe:* npm test
```

## Integration with CI/CD

These tests can be integrated into CI/CD pipelines with:

1. **Environment Variables**: Set Stripe test keys in CI environment
2. **Test Isolation**: Each test cleans up its resources
3. **Parallel Execution**: Tests can run in parallel with proper resource management
4. **Reporting**: Jest provides detailed test reports and coverage

## Security Considerations

- **Test Keys Only**: Never use production Stripe keys in tests
- **Data Isolation**: Test data is isolated and cleaned up
- **No Sensitive Data**: Tests use mock/test data only
- **Environment Separation**: Clear separation between test and production environments

## Maintenance

### Regular Updates

- Update test cases when adding new service types
- Update pricing when billing amounts change
- Update webhook handlers when adding new event types
- Review and update error handling scenarios

### Monitoring

- Monitor test execution times
- Track test failure rates
- Review Stripe API changes that might affect tests
- Update dependencies regularly