/**
 * Heatmap Tracker - User interaction tracking and heatmap generation
 * Implements user interaction tracking for analytics dashboard
 */

class HeatmapTracker {
    constructor(performanceMonitor) {
        this.performanceMonitor = performanceMonitor;
        this.interactions = [];
        this.scrollData = [];
        this.clickData = [];
        this.hoverData = [];
        this.isTracking = false;
        
        this.config = {
            maxInteractions: 1000,
            trackClicks: true,
            trackHovers: true,
            trackScrolls: true,
            trackMouseMovement: false, // Disabled by default for performance
            hoverThreshold: 1000, // ms
            scrollThreshold: 100 // ms
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startTracking();
        console.log('Heatmap Tracker initialized');
    }

    setupEventListeners() {
        // Click tracking
        if (this.config.trackClicks) {
            document.addEventListener('click', (event) => {
                this.trackClick(event);
            }, { passive: true });
        }

        // Hover tracking
        if (this.config.trackHovers) {
            let hoverTimer = null;
            let hoverStartTime = null;
            
            document.addEventListener('mouseenter', (event) => {
                hoverStartTime = Date.now();
                hoverTimer = setTimeout(() => {
                    this.trackHover(event, Date.now() - hoverStartTime);
                }, this.config.hoverThreshold);
            }, { passive: true });
            
            document.addEventListener('mouseleave', () => {
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                    hoverTimer = null;
                }
            }, { passive: true });
        }

        // Scroll tracking
        if (this.config.trackScrolls) {
            let scrollTimer = null;
            let lastScrollTime = 0;
            
            window.addEventListener('scroll', () => {
                const now = Date.now();
                if (now - lastScrollTime > this.config.scrollThreshold) {
                    this.trackScroll();
                    lastScrollTime = now;
                }
            }, { passive: true });
        }

        // Mouse movement tracking (optional, performance intensive)
        if (this.config.trackMouseMovement) {
            let mouseMoveTimer = null;
            
            document.addEventListener('mousemove', (event) => {
                if (mouseMoveTimer) return;
                
                mouseMoveTimer = setTimeout(() => {
                    this.trackMouseMovement(event);
                    mouseMoveTimer = null;
                }, 100); // Throttle to every 100ms
            }, { passive: true });
        }

        // Form interaction tracking
        document.addEventListener('focus', (event) => {
            if (event.target.matches('input, textarea, select')) {
                this.trackFormInteraction(event, 'focus');
            }
        }, { passive: true });

        document.addEventListener('blur', (event) => {
            if (event.target.matches('input, textarea, select')) {
                this.trackFormInteraction(event, 'blur');
            }
        }, { passive: true });

        // Page visibility changes
        document.addEventListener('visibilitychange', () => {
            this.trackVisibilityChange();
        });
    }

    trackClick(event) {
        if (!this.isTracking) return;

        const clickData = {
            type: 'click',
            x: event.clientX,
            y: event.clientY,
            pageX: event.pageX,
            pageY: event.pageY,
            timestamp: Date.now(),
            target: {
                tagName: event.target.tagName,
                id: event.target.id,
                className: event.target.className,
                textContent: event.target.textContent?.substring(0, 50) || '',
                attributes: this.getElementAttributes(event.target)
            },
            page: {
                url: window.location.href,
                pathname: window.location.pathname,
                title: document.title
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight,
                scrollX: window.scrollX,
                scrollY: window.scrollY
            }
        };

        this.clickData.push(clickData);
        this.interactions.push(clickData);
        
        // Report to performance monitor
        if (this.performanceMonitor) {
            this.performanceMonitor.recordUserInteraction('click', event);
        }

        this.cleanupOldData();
    }

    trackHover(event, duration) {
        if (!this.isTracking) return;

        const hoverData = {
            type: 'hover',
            x: event.clientX,
            y: event.clientY,
            duration,
            timestamp: Date.now(),
            target: {
                tagName: event.target.tagName,
                id: event.target.id,
                className: event.target.className,
                textContent: event.target.textContent?.substring(0, 50) || ''
            },
            page: {
                url: window.location.href,
                pathname: window.location.pathname
            }
        };

        this.hoverData.push(hoverData);
        this.interactions.push(hoverData);
        this.cleanupOldData();
    }

    trackScroll() {
        if (!this.isTracking) return;

        const scrollData = {
            type: 'scroll',
            scrollX: window.scrollX,
            scrollY: window.scrollY,
            scrollTop: document.documentElement.scrollTop,
            scrollHeight: document.documentElement.scrollHeight,
            clientHeight: document.documentElement.clientHeight,
            scrollPercentage: (document.documentElement.scrollTop / 
                (document.documentElement.scrollHeight - document.documentElement.clientHeight)) * 100,
            timestamp: Date.now(),
            page: {
                url: window.location.href,
                pathname: window.location.pathname
            }
        };

        this.scrollData.push(scrollData);
        this.interactions.push(scrollData);
        this.cleanupOldData();
    }

    trackMouseMovement(event) {
        if (!this.isTracking) return;

        const movementData = {
            type: 'mousemove',
            x: event.clientX,
            y: event.clientY,
            timestamp: Date.now(),
            page: {
                pathname: window.location.pathname
            }
        };

        this.interactions.push(movementData);
        this.cleanupOldData();
    }

    trackFormInteraction(event, interactionType) {
        if (!this.isTracking) return;

        const formData = {
            type: 'form_interaction',
            interactionType,
            timestamp: Date.now(),
            target: {
                tagName: event.target.tagName,
                type: event.target.type,
                id: event.target.id,
                name: event.target.name,
                className: event.target.className
            },
            page: {
                url: window.location.href,
                pathname: window.location.pathname
            }
        };

        this.interactions.push(formData);
        this.cleanupOldData();
    }

    trackVisibilityChange() {
        if (!this.isTracking) return;

        const visibilityData = {
            type: 'visibility_change',
            hidden: document.hidden,
            visibilityState: document.visibilityState,
            timestamp: Date.now(),
            page: {
                url: window.location.href,
                pathname: window.location.pathname
            }
        };

        this.interactions.push(visibilityData);
    }

    getElementAttributes(element) {
        const attributes = {};
        if (element.attributes) {
            for (let attr of element.attributes) {
                if (attr.name !== 'class' && attr.name !== 'id') {
                    attributes[attr.name] = attr.value;
                }
            }
        }
        return attributes;
    }

    cleanupOldData() {
        // Keep only the most recent interactions to prevent memory issues
        if (this.interactions.length > this.config.maxInteractions) {
            this.interactions = this.interactions.slice(-this.config.maxInteractions);
        }
        
        if (this.clickData.length > this.config.maxInteractions / 2) {
            this.clickData = this.clickData.slice(-this.config.maxInteractions / 2);
        }
        
        if (this.hoverData.length > this.config.maxInteractions / 4) {
            this.hoverData = this.hoverData.slice(-this.config.maxInteractions / 4);
        }
        
        if (this.scrollData.length > this.config.maxInteractions / 4) {
            this.scrollData = this.scrollData.slice(-this.config.maxInteractions / 4);
        }
    }

    // Heatmap generation methods
    generateClickHeatmap(timeRange = null) {
        let clicks = this.clickData;
        
        if (timeRange) {
            const cutoff = Date.now() - timeRange;
            clicks = clicks.filter(click => click.timestamp > cutoff);
        }
        
        return this.processHeatmapData(clicks);
    }

    generateScrollHeatmap(timeRange = null) {
        let scrolls = this.scrollData;
        
        if (timeRange) {
            const cutoff = Date.now() - timeRange;
            scrolls = scrolls.filter(scroll => scroll.timestamp > cutoff);
        }
        
        // Convert scroll data to heatmap format
        return scrolls.map(scroll => ({
            x: window.innerWidth / 2, // Center horizontally for scroll heatmap
            y: (scroll.scrollPercentage / 100) * window.innerHeight,
            intensity: 1,
            timestamp: scroll.timestamp
        }));
    }

    generateHoverHeatmap(timeRange = null) {
        let hovers = this.hoverData;
        
        if (timeRange) {
            const cutoff = Date.now() - timeRange;
            hovers = hovers.filter(hover => hover.timestamp > cutoff);
        }
        
        return this.processHeatmapData(hovers, 'duration');
    }

    processHeatmapData(data, intensityField = null) {
        const heatmapPoints = [];
        const gridSize = 20; // Pixel grid size for clustering
        const clusters = {};
        
        // Cluster nearby points
        data.forEach(point => {
            const gridX = Math.floor(point.x / gridSize) * gridSize;
            const gridY = Math.floor(point.y / gridSize) * gridSize;
            const key = `${gridX},${gridY}`;
            
            if (!clusters[key]) {
                clusters[key] = {
                    x: gridX,
                    y: gridY,
                    count: 0,
                    totalIntensity: 0,
                    points: []
                };
            }
            
            clusters[key].count++;
            clusters[key].totalIntensity += intensityField ? (point[intensityField] || 1) : 1;
            clusters[key].points.push(point);
        });
        
        // Convert clusters to heatmap points
        Object.values(clusters).forEach(cluster => {
            heatmapPoints.push({
                x: cluster.x + gridSize / 2,
                y: cluster.y + gridSize / 2,
                intensity: cluster.totalIntensity,
                count: cluster.count,
                points: cluster.points
            });
        });
        
        return heatmapPoints;
    }

    // Analytics methods
    getInteractionStats(timeRange = null) {
        let interactions = this.interactions;
        
        if (timeRange) {
            const cutoff = Date.now() - timeRange;
            interactions = interactions.filter(interaction => interaction.timestamp > cutoff);
        }
        
        const stats = {
            total: interactions.length,
            clicks: interactions.filter(i => i.type === 'click').length,
            hovers: interactions.filter(i => i.type === 'hover').length,
            scrolls: interactions.filter(i => i.type === 'scroll').length,
            formInteractions: interactions.filter(i => i.type === 'form_interaction').length,
            timeRange: timeRange || 'all_time',
            startTime: interactions.length > 0 ? Math.min(...interactions.map(i => i.timestamp)) : null,
            endTime: interactions.length > 0 ? Math.max(...interactions.map(i => i.timestamp)) : null
        };
        
        return stats;
    }

    getMostClickedElements(limit = 10, timeRange = null) {
        let clicks = this.clickData;
        
        if (timeRange) {
            const cutoff = Date.now() - timeRange;
            clicks = clicks.filter(click => click.timestamp > cutoff);
        }
        
        const elementCounts = {};
        
        clicks.forEach(click => {
            const key = `${click.target.tagName}#${click.target.id}.${click.target.className}`;
            if (!elementCounts[key]) {
                elementCounts[key] = {
                    element: click.target,
                    count: 0,
                    lastClick: null
                };
            }
            elementCounts[key].count++;
            elementCounts[key].lastClick = click.timestamp;
        });
        
        return Object.values(elementCounts)
            .sort((a, b) => b.count - a.count)
            .slice(0, limit);
    }

    getScrollBehavior(timeRange = null) {
        let scrolls = this.scrollData;
        
        if (timeRange) {
            const cutoff = Date.now() - timeRange;
            scrolls = scrolls.filter(scroll => scroll.timestamp > cutoff);
        }
        
        if (scrolls.length === 0) return null;
        
        const maxScroll = Math.max(...scrolls.map(s => s.scrollPercentage));
        const avgScroll = scrolls.reduce((sum, s) => sum + s.scrollPercentage, 0) / scrolls.length;
        const scrollSessions = this.groupScrollSessions(scrolls);
        
        return {
            maxScrollDepth: maxScroll,
            averageScrollDepth: avgScroll,
            totalScrollEvents: scrolls.length,
            scrollSessions: scrollSessions.length,
            averageSessionLength: scrollSessions.reduce((sum, s) => sum + s.duration, 0) / scrollSessions.length
        };
    }

    groupScrollSessions(scrolls) {
        const sessions = [];
        let currentSession = null;
        const sessionTimeout = 30000; // 30 seconds
        
        scrolls.forEach(scroll => {
            if (!currentSession || scroll.timestamp - currentSession.lastActivity > sessionTimeout) {
                currentSession = {
                    startTime: scroll.timestamp,
                    endTime: scroll.timestamp,
                    lastActivity: scroll.timestamp,
                    scrollEvents: [scroll],
                    maxDepth: scroll.scrollPercentage
                };
                sessions.push(currentSession);
            } else {
                currentSession.endTime = scroll.timestamp;
                currentSession.lastActivity = scroll.timestamp;
                currentSession.scrollEvents.push(scroll);
                currentSession.maxDepth = Math.max(currentSession.maxDepth, scroll.scrollPercentage);
            }
        });
        
        // Calculate session durations
        sessions.forEach(session => {
            session.duration = session.endTime - session.startTime;
        });
        
        return sessions;
    }

    // Control methods
    startTracking() {
        this.isTracking = true;
        console.log('Heatmap tracking started');
    }

    stopTracking() {
        this.isTracking = false;
        console.log('Heatmap tracking stopped');
    }

    clearData() {
        this.interactions = [];
        this.scrollData = [];
        this.clickData = [];
        this.hoverData = [];
        console.log('Heatmap data cleared');
    }

    // Export methods
    exportHeatmapData() {
        return {
            interactions: this.interactions,
            clickData: this.clickData,
            hoverData: this.hoverData,
            scrollData: this.scrollData,
            stats: this.getInteractionStats(),
            exportTime: Date.now()
        };
    }

    exportClickHeatmap(timeRange = null) {
        return {
            type: 'click_heatmap',
            data: this.generateClickHeatmap(timeRange),
            timeRange,
            exportTime: Date.now()
        };
    }

    exportScrollHeatmap(timeRange = null) {
        return {
            type: 'scroll_heatmap',
            data: this.generateScrollHeatmap(timeRange),
            timeRange,
            exportTime: Date.now()
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HeatmapTracker;
}