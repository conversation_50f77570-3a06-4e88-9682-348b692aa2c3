import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AppSettings } from '../../utils/types';

interface SettingsState {
  settings: AppSettings;
  isLoading: boolean;
}

const initialState: SettingsState = {
  settings: {
    autoBackup: true,
    wifiOnly: true,
    backupFrequency: 'daily',
    notifications: true,
  },
  isLoading: false,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    updateSettings: (state, action: PayloadAction<Partial<AppSettings>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    resetSettings: (state) => {
      state.settings = initialState.settings;
    },
  },
});

export const {
  setLoading,
  updateSettings,
  resetSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;
