import { Router } from 'express';
import { SubscriptionController } from '../controllers/SubscriptionController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const subscriptionController = new SubscriptionController();

/**
 * Subscription routes for modular pricing system
 * All routes require authentication
 */

// POST /api/subscriptions - Creates new subscription with service combination
router.post('/', authenticateToken, (req, res) => {
  subscriptionController.createSubscription(req, res);
});

// PUT /api/subscriptions/:subscriptionId - Updates existing subscription services
router.put('/:subscriptionId', authenticateToken, (req, res) => {
  subscriptionController.updateSubscription(req, res);
});

// GET /api/subscriptions/:userId - Returns user's current subscription details
router.get('/:userId', authenticateToken, (req, res) => {
  subscriptionController.getSubscriptionDetails(req, res);
});

// DELETE /api/subscriptions/:subscriptionId - Cancels subscription
router.delete('/:subscriptionId', authenticateToken, (req, res) => {
  subscriptionController.cancelSubscription(req, res);
});

export default router;