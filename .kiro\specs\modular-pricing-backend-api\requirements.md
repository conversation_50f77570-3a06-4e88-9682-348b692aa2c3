# Requirements Document

## Introduction

This feature updates the SafeKeep backend API to fully support the new modular pricing system that allows users to select individual services (Contacts, Messages, Photos) or combination packages. The backend API needs to handle subscription creation with service combinations, calculate optimal pricing, manage billing logic for different service combinations, validate service combinations, and verify user access to specific services.

## Requirements

### Requirement 1

**User Story:** As a user, I want to create subscriptions with specific service combinations, so that I only pay for the backup services I need.

#### Acceptance Criteria

1. WHEN a user requests subscription creation with selected services THEN the system SHALL accept an array of service IDs (contacts, messages, photos)
2. WHEN creating a subscription THEN the system SHALL validate that the selected service combination is valid
3. WHEN a valid service combination is provided THEN the system SHALL create the subscription with the optimal pricing plan
4. WHEN an invalid service combination is provided THEN the system SHALL return a 400 error with validation details

### Requirement 2

**User Story:** As a user, I want the system to calculate optimal pricing for my selected services, so that I get the best value for my chosen combination.

#### Acceptance Criteria

1. WHEN a user requests pricing calculation with service IDs THEN the system SHALL return the most cost-effective plan
2. WHEN multiple plans could satisfy the service selection THEN the system SHALL recommend the cheapest option
3. WHEN individual services would be cheaper than combinations THEN the system SHALL recommend individual pricing
4. WHEN combination plans offer savings THEN the system SHALL return the savings amount and recommended plan

### Requirement 3

**User Story:** As a user, I want my billing to be handled correctly for different service combinations, so that I'm charged accurately for my selected services.

#### Acceptance Criteria

1. WHEN a subscription is created THEN the system SHALL store the selected services and total price
2. WHEN billing occurs THEN the system SHALL charge based on the user's current service selection
3. WHEN a user changes services THEN the system SHALL update billing to reflect the new service combination
4. WHEN a subscription is cancelled THEN the system SHALL deactivate all associated services

### Requirement 4

**User Story:** As a system administrator, I want service combination validation, so that users can only select valid service combinations.

#### Acceptance Criteria

1. WHEN validating service combinations THEN the system SHALL ensure at least one service is selected
2. WHEN validating service combinations THEN the system SHALL ensure all selected services exist in the system
3. WHEN validating service combinations THEN the system SHALL prevent duplicate service selections
4. WHEN validation fails THEN the system SHALL return specific error messages for each validation failure

### Requirement 5

**User Story:** As a user, I want the system to verify my access to specific services, so that I can only use backup features I've paid for.

#### Acceptance Criteria

1. WHEN a user attempts to backup data THEN the system SHALL verify they have access to the corresponding service
2. WHEN a user has an active subscription THEN the system SHALL allow access to all services in their plan
3. WHEN a user's subscription is inactive THEN the system SHALL deny access to all paid services
4. WHEN checking service access THEN the system SHALL return clear access status and expiration information