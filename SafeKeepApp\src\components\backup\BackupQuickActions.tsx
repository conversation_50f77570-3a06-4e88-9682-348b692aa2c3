import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Card, IconButton, Badge } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { COLORS, SPACING } from '../../utils/constants';
import { 
  useIsBackupInProgress, 
  useLastSuccessfulBackup,
  useBackupErrors
} from '../../store/hooks/backupHooks';

type NavigationProp = StackNavigationProp<RootStackParamList>;

const BackupQuickActions: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const isBackupInProgress = useIsBackupInProgress();
  const lastSuccessfulBackup = useLastSuccessfulBackup();
  const backupErrors = useBackupErrors();
  const hasErrors = backupErrors.length > 0;

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleQuickBackup = () => {
    navigation.navigate('Backup');
  };

  const handleViewProgress = () => {
    navigation.navigate('BackupProgress');
  };

  const handleSettings = () => {
    navigation.navigate('BackupSettings');
  };

  return (
    <Card style={styles.container}>
      <Card.Title title="Backup Quick Actions" />
      <Card.Content>
        <View style={styles.actionsContainer}>
          <TouchableOpacity 
            style={[styles.actionButton, isBackupInProgress && styles.disabledButton]} 
            onPress={handleQuickBackup}
            disabled={isBackupInProgress}
          >
            <IconButton icon="backup-restore" size={24} />
            <Text style={styles.actionText}>Quick Backup</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={handleViewProgress}
          >
            <View style={styles.iconContainer}>
              <IconButton icon="progress-check" size={24} />
              {(isBackupInProgress || hasErrors) && (
                <Badge
                  size={8}
                  style={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    backgroundColor: isBackupInProgress ? COLORS.warning : COLORS.error,
                  }}
                />
              )}
            </View>
            <Text style={styles.actionText}>Progress</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={handleSettings}
          >
            <IconButton icon="cog" size={24} />
            <Text style={styles.actionText}>Settings</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>
            Last backup: {formatDate(lastSuccessfulBackup)}
          </Text>
          {isBackupInProgress && (
            <Text style={styles.inProgressText}>Backup in progress...</Text>
          )}
          {hasErrors && !isBackupInProgress && (
            <Text style={styles.errorText}>{backupErrors.length} error(s) detected</Text>
          )}
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: SPACING.md,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.md,
  },
  actionButton: {
    alignItems: 'center',
    padding: SPACING.sm,
    borderRadius: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  actionText: {
    marginTop: 4,
    fontSize: 12,
  },
  iconContainer: {
    position: 'relative',
  },
  statusContainer: {
    alignItems: 'center',
    marginTop: SPACING.sm,
  },
  statusText: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  inProgressText: {
    fontSize: 12,
    color: COLORS.warning,
    fontWeight: 'bold',
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: COLORS.error,
    fontWeight: 'bold',
    marginTop: 4,
  },
});

export default BackupQuickActions;