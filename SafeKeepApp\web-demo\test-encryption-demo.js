/**
 * Test script for Encryption Demonstration Module
 * Run this to verify encryption functionality works correctly
 */

async function testEncryptionDemo() {
    console.log('🔐 Testing Encryption Demonstration Module...');
    
    try {
        // Test 1: Initialize Encryption Manager
        console.log('\n1. Testing Encryption Manager initialization...');
        const encryptionManager = new EncryptionManager();
        console.log('✅ Encryption Manager initialized successfully');
        
        // Test 2: Generate Key
        console.log('\n2. Testing key generation...');
        const keyData = await encryptionManager.generateKey('AES-256-GCM');
        console.log('✅ Key generated successfully:', {
            algorithm: keyData.algorithm,
            keySize: keyData.key.length * 8,
            ivSize: keyData.iv.length * 8,
            generationTime: keyData.generationTime
        });
        
        // Test 3: Encrypt Sample Data
        console.log('\n3. Testing data encryption...');
        const sampleData = {
            name: '<PERSON>',
            phone: '+1234567890',
            email: '<EMAIL>',
            sensitive: 'This is confidential information'
        };
        
        const encryptionResult = await encryptionManager.encryptData(sampleData);
        console.log('✅ Data encrypted successfully:', {
            originalSize: encryptionResult.originalSize,
            encryptedSize: encryptionResult.encryptedSize,
            encryptionTime: encryptionResult.encryptionTime,
            algorithm: encryptionResult.algorithm
        });
        
        // Test 4: Decrypt Data
        console.log('\n4. Testing data decryption...');
        const decryptionResult = await encryptionManager.decryptData(
            encryptionResult.encryptedData,
            encryptionResult.keyHex,
            encryptionResult.ivHex,
            encryptionResult.algorithm
        );
        
        console.log('✅ Data decrypted successfully:', {
            decryptionTime: decryptionResult.decryptionTime,
            verified: decryptionResult.verified,
            dataMatches: JSON.stringify(decryptionResult.decryptedData) === JSON.stringify(sampleData)
        });
        
        // Test 5: Security Strength Demonstration
        console.log('\n5. Testing security strength calculation...');
        const strengthInfo = encryptionManager.demonstrateStrength('AES-256-GCM');
        console.log('✅ Security strength calculated:', {
            algorithm: strengthInfo.algorithm,
            keySize: strengthInfo.keySize,
            strength: strengthInfo.strength,
            bruteForceTime: strengthInfo.bruteForceTime
        });
        
        // Test 6: Performance Metrics
        console.log('\n6. Testing performance metrics...');
        const metrics = encryptionManager.getPerformanceMetrics();
        console.log('✅ Performance metrics retrieved:', metrics);
        
        // Test 7: Algorithm Information
        console.log('\n7. Testing algorithm information...');
        const algorithms = encryptionManager.getAlgorithms();
        console.log('✅ Available algorithms:', algorithms.map(a => a.name));
        
        console.log('\n🎉 All encryption tests passed successfully!');
        console.log('\n📊 Test Summary:');
        console.log(`- Encryption time: ${encryptionResult.encryptionTime.toFixed(2)}ms`);
        console.log(`- Decryption time: ${decryptionResult.decryptionTime.toFixed(2)}ms`);
        console.log(`- Throughput: ${(encryptionResult.throughput / 1024).toFixed(1)} KB/s`);
        console.log(`- Data integrity: ${decryptionResult.verified ? 'Verified' : 'Failed'}`);
        console.log(`- Security level: ${strengthInfo.strength}`);
        
        return true;
    } catch (error) {
        console.error('❌ Encryption test failed:', error);
        return false;
    }
}

// Test different algorithms
async function testAllAlgorithms() {
    console.log('\n🔧 Testing all encryption algorithms...');
    
    const algorithms = ['AES-256-GCM', 'AES-192-GCM', 'AES-128-GCM'];
    const testData = 'This is a test message for encryption';
    
    for (const algorithm of algorithms) {
        try {
            console.log(`\nTesting ${algorithm}...`);
            const manager = new EncryptionManager();
            
            const keyData = await manager.generateKey(algorithm);
            const encryptResult = await manager.encryptData(testData);
            const decryptResult = await manager.decryptData(
                encryptResult.encryptedData,
                encryptResult.keyHex,
                encryptResult.ivHex,
                algorithm
            );
            
            console.log(`✅ ${algorithm}: Encrypt ${encryptResult.encryptionTime.toFixed(2)}ms, Decrypt ${decryptResult.decryptionTime.toFixed(2)}ms`);
        } catch (error) {
            console.error(`❌ ${algorithm} failed:`, error.message);
        }
    }
}

// Test with different data types
async function testDataTypes() {
    console.log('\n📝 Testing different data types...');
    
    const manager = new EncryptionManager();
    await manager.generateKey();
    
    const testCases = [
        { name: 'String', data: 'Simple text message' },
        { name: 'Number', data: 12345 },
        { name: 'Object', data: { name: 'Test', value: 123 } },
        { name: 'Array', data: [1, 2, 3, 'test'] },
        { name: 'Large Text', data: 'A'.repeat(10000) }
    ];
    
    for (const testCase of testCases) {
        try {
            console.log(`\nTesting ${testCase.name}...`);
            const encryptResult = await manager.encryptData(testCase.data);
            const decryptResult = await manager.decryptData(
                encryptResult.encryptedData,
                encryptResult.keyHex,
                encryptResult.ivHex
            );
            
            const dataMatches = JSON.stringify(decryptResult.decryptedData) === JSON.stringify(testCase.data);
            console.log(`✅ ${testCase.name}: ${encryptResult.originalSize} bytes, integrity ${dataMatches ? 'OK' : 'FAILED'}`);
        } catch (error) {
            console.error(`❌ ${testCase.name} failed:`, error.message);
        }
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Starting comprehensive encryption tests...');
    
    const basicTest = await testEncryptionDemo();
    if (basicTest) {
        await testAllAlgorithms();
        await testDataTypes();
    }
    
    console.log('\n✨ All tests completed!');
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
    window.testEncryptionDemo = testEncryptionDemo;
    window.testAllAlgorithms = testAllAlgorithms;
    window.testDataTypes = testDataTypes;
    window.runAllTests = runAllTests;
} else if (typeof module !== 'undefined') {
    module.exports = {
        testEncryptionDemo,
        testAllAlgorithms,
        testDataTypes,
        runAllTests
    };
}

console.log('🔐 Encryption test functions loaded. Run runAllTests() to start testing.');