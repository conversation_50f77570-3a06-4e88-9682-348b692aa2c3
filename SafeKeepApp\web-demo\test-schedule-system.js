/**
 * Test Advanced Backup Scheduling System
 * Comprehensive testing for schedule manager and console
 */

async function testScheduleSystem() {
    console.log('🧪 Starting Advanced Backup Scheduling System Tests...');
    
    try {
        // Initialize components
        const scheduleManager = new window.ScheduleManager(supabase, adminSupabase);
        const scheduleConsole = new window.ScheduleConsole('test-schedule-container');
        
        await scheduleManager.initialize();
        scheduleConsole.setScheduleManager(scheduleManager);
        
        console.log('✅ Components initialized successfully');
        
        // Test 1: Create basic daily schedule
        console.log('\n📋 Test 1: Creating daily schedule...');
        const dailySchedule = await scheduleManager.createSchedule({
            name: 'Daily Evening Backup',
            frequency: 'daily',
            time: '22:00',
            days: [1, 2, 3, 4, 5], // Weekdays only
            dataTypes: ['contacts', 'messages'],
            conditions: {
                wifiOnly: true,
                minBatteryLevel: 30,
                maxStorageUsage: 80,
                requireCharging: false,
                requireIdle: true
            }
        });
        
        console.log('✅ Daily schedule created:', dailySchedule.id);
        console.log('   Next run:', dailySchedule.nextRun?.toLocaleString());
        
        // Test 2: Create weekly schedule
        console.log('\n📋 Test 2: Creating weekly schedule...');
        const weeklySchedule = await scheduleManager.createSchedule({
            name: 'Weekly Full Backup',
            frequency: 'weekly',
            time: '02:00',
            days: [0], // Sunday
            dataTypes: ['contacts', 'messages', 'photos'],
            conditions: {
                wifiOnly: true,
                minBatteryLevel: 50,
                maxStorageUsage: 70,
                requireCharging: true,
                requireIdle: true
            }
        });
        
        console.log('✅ Weekly schedule created:', weeklySchedule.id);
        console.log('   Next run:', weeklySchedule.nextRun?.toLocaleString());
        
        // Test 3: Test condition evaluation
        console.log('\n📋 Test 3: Testing condition evaluation...');
        
        // Set good conditions
        scheduleManager.updateSimulatedConditions({
            networkType: 'wifi',
            batteryLevel: 85,
            storageUsage: 45,
            isCharging: true,
            deviceIdle: true
        });
        
        const goodConditionsTest = scheduleManager.testSchedule(dailySchedule);
        console.log('✅ Good conditions test:', goodConditionsTest.canRunNow ? 'PASS' : 'FAIL');
        
        // Set bad conditions
        scheduleManager.updateSimulatedConditions({
            networkType: 'cellular',
            batteryLevel: 15,
            storageUsage: 90,
            isCharging: false,
            deviceIdle: false
        });
        
        const badConditionsTest = scheduleManager.testSchedule(dailySchedule);
        console.log('✅ Bad conditions test:', !badConditionsTest.canRunNow ? 'PASS' : 'FAIL');
        console.log('   Failed conditions:', badConditionsTest.failedConditions.length);
        
        // Test 4: Schedule modification
        console.log('\n📋 Test 4: Testing schedule modification...');
        await scheduleManager.updateSchedule(dailySchedule.id, {
            name: 'Updated Daily Backup',
            time: '23:30',
            conditions: {
                ...dailySchedule.conditions,
                minBatteryLevel: 25
            }
        });
        
        const updatedSchedule = scheduleManager.getSchedule(dailySchedule.id);
        console.log('✅ Schedule updated:', updatedSchedule.name);
        console.log('   New time:', updatedSchedule.time);
        console.log('   New battery requirement:', updatedSchedule.conditions.minBatteryLevel + '%');
        
        // Test 5: Schedule statistics
        console.log('\n📋 Test 5: Testing schedule statistics...');
        const stats = scheduleManager.getScheduleStats();
        console.log('✅ Schedule statistics:');
        console.log('   Total schedules:', stats.totalSchedules);
        console.log('   Enabled schedules:', stats.enabledSchedules);
        console.log('   Success rate:', stats.successRate.toFixed(1) + '%');
        console.log('   Next run:', stats.nextScheduledRun?.name || 'None');
        
        // Test 6: Simulate backup execution
        console.log('\n📋 Test 6: Simulating backup execution...');
        
        // Reset to good conditions
        scheduleManager.updateSimulatedConditions({
            networkType: 'wifi',
            batteryLevel: 85,
            storageUsage: 45,
            isCharging: true,
            deviceIdle: true
        });
        
        // Manually trigger backup execution
        const backupResult = await scheduleManager.simulateBackupExecution(dailySchedule);
        console.log('✅ Backup simulation result:', backupResult.success ? 'SUCCESS' : 'FAILED');
        if (backupResult.success) {
            console.log('   Files backed up:', backupResult.filesBackedUp);
            console.log('   Data size:', backupResult.dataSize + 'MB');
            console.log('   Duration:', (backupResult.duration / 1000).toFixed(1) + 's');
        }
        
        // Test 7: Schedule toggle and deletion
        console.log('\n📋 Test 7: Testing schedule toggle and deletion...');
        
        // Toggle schedule
        const toggledSchedule = await scheduleManager.toggleSchedule(weeklySchedule.id);
        console.log('✅ Schedule toggled:', toggledSchedule.enabled ? 'ENABLED' : 'DISABLED');
        
        // Delete schedule
        await scheduleManager.deleteSchedule(weeklySchedule.id);
        const deletedSchedule = scheduleManager.getSchedule(weeklySchedule.id);
        console.log('✅ Schedule deleted:', deletedSchedule ? 'FAILED' : 'SUCCESS');
        
        // Test 8: Next run calculation
        console.log('\n📋 Test 8: Testing next run calculations...');
        
        const testSchedules = [
            { frequency: 'daily', time: '08:00', days: [1, 2, 3, 4, 5] },
            { frequency: 'weekly', time: '02:00', days: [0] },
            { frequency: 'monthly', time: '01:00', days: [] }
        ];
        
        testSchedules.forEach((scheduleConfig, index) => {
            const nextRun = scheduleManager.calculateNextRun({
                enabled: true,
                ...scheduleConfig
            });
            console.log(`   Schedule ${index + 1} (${scheduleConfig.frequency}):`, nextRun?.toLocaleString() || 'Not calculated');
        });
        
        // Test 9: Concurrent schedule handling
        console.log('\n📋 Test 9: Testing concurrent schedules...');
        
        // Create multiple schedules with different times
        const morningSchedule = await scheduleManager.createSchedule({
            name: 'Morning Quick Backup',
            frequency: 'daily',
            time: '08:00',
            days: [1, 2, 3, 4, 5],
            dataTypes: ['contacts'],
            conditions: {
                wifiOnly: false,
                minBatteryLevel: 20,
                maxStorageUsage: 90,
                requireCharging: false,
                requireIdle: false
            }
        });
        
        const eveningSchedule = await scheduleManager.createSchedule({
            name: 'Evening Full Backup',
            frequency: 'daily',
            time: '20:00',
            days: [1, 2, 3, 4, 5, 6, 0],
            dataTypes: ['contacts', 'messages', 'photos'],
            conditions: {
                wifiOnly: true,
                minBatteryLevel: 40,
                maxStorageUsage: 80,
                requireCharging: false,
                requireIdle: true
            }
        });
        
        const finalStats = scheduleManager.getScheduleStats();
        console.log('✅ Multiple schedules created:');
        console.log('   Total active schedules:', finalStats.enabledSchedules);
        console.log('   Next scheduled run:', finalStats.nextScheduledRun?.name || 'None');
        
        // Test 10: Condition simulation scenarios
        console.log('\n📋 Test 10: Testing various condition scenarios...');
        
        const scenarios = [
            { name: 'Optimal WiFi', networkType: 'wifi', batteryLevel: 90, storageUsage: 30, isCharging: true, deviceIdle: true },
            { name: 'Low Battery', networkType: 'wifi', batteryLevel: 15, storageUsage: 30, isCharging: false, deviceIdle: true },
            { name: 'Cellular Only', networkType: 'cellular', batteryLevel: 70, storageUsage: 30, isCharging: false, deviceIdle: true },
            { name: 'Storage Full', networkType: 'wifi', batteryLevel: 70, storageUsage: 95, isCharging: false, deviceIdle: true },
            { name: 'Device Busy', networkType: 'wifi', batteryLevel: 70, storageUsage: 30, isCharging: false, deviceIdle: false }
        ];
        
        scenarios.forEach(scenario => {
            scheduleManager.updateSimulatedConditions(scenario);
            const testResult = scheduleManager.testSchedule(morningSchedule);
            console.log(`   ${scenario.name}: ${testResult.canRunNow ? 'CAN RUN' : 'BLOCKED'} (${testResult.failedConditions.length} failed conditions)`);
        });
        
        console.log('\n🎉 All Advanced Backup Scheduling System tests completed successfully!');
        console.log('\n📊 Final System State:');
        console.log('   Total schedules:', scheduleManager.getSchedules().length);
        console.log('   Active timers:', scheduleManager.activeTimers.size);
        console.log('   System initialized:', scheduleManager.isInitialized);
        
        return {
            success: true,
            scheduleManager,
            scheduleConsole,
            testResults: {
                schedulesCreated: scheduleManager.getSchedules().length,
                testsRun: 10,
                allTestsPassed: true
            }
        };
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Auto-run tests if this file is loaded directly
if (typeof window !== 'undefined' && window.location.search.includes('test=schedule')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(testScheduleSystem, 2000); // Wait for other components to load
    });
}

// Export for manual testing
if (typeof window !== 'undefined') {
    window.testScheduleSystem = testScheduleSystem;
}