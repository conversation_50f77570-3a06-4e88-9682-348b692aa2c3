import React from 'react';
import { StripeProvider } from '@stripe/stripe-react-native';
import { STRIPE_PUBLISHABLE_KEY_EXPORT } from '../services/StripeService';

interface StripeWrapperProps {
  children: React.ReactNode;
}

const StripeWrapper: React.FC<StripeWrapperProps> = ({ children }) => {
  // Validate that the publishable key is available
  if (!STRIPE_PUBLISHABLE_KEY_EXPORT) {
    console.error('❌ Stripe publishable key not found. Please check your .env file.');
    
    // Return a fallback component in development
    if (__DEV__) {
      return (
        <div style={{ 
          padding: 20, 
          backgroundColor: '#ffebee', 
          border: '1px solid #f44336',
          borderRadius: 4,
          margin: 20 
        }}>
          <h3 style={{ color: '#d32f2f', margin: 0 }}>⚠️ Stripe Configuration Missing</h3>
          <p style={{ color: '#d32f2f', margin: '10px 0 0 0' }}>
            Please add your Stripe publishable key to the .env file:
            <br />
            <code>STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here</code>
          </p>
        </div>
      );
    }
    
    // In production, throw an error
    throw new Error('Stripe publishable key is required');
  }

  return (
    <StripeProvider
      publishableKey={STRIPE_PUBLISHABLE_KEY_EXPORT}
      merchantIdentifier="merchant.com.safekeep.app" // Replace with your merchant ID
      urlScheme="safekeep" // Replace with your app's URL scheme
    >
      {children}
    </StripeProvider>
  );
};

export default StripeWrapper;
