/**
 * Test script for Backup History and Analytics Dashboard
 * Run this to verify the backup history functionality
 */

const { createServer } = require('http');
const { readFileSync } = require('fs');
const { join } = require('path');

// Simple HTTP server for testing
const server = createServer((req, res) => {
    let filePath = req.url === '/' ? '/index.html' : req.url;
    
    // Remove query parameters
    filePath = filePath.split('?')[0];
    
    const fullPath = join(__dirname, filePath);
    
    try {
        const content = readFileSync(fullPath);
        
        // Set content type based on file extension
        let contentType = 'text/html';
        if (filePath.endsWith('.js')) contentType = 'application/javascript';
        else if (filePath.endsWith('.css')) contentType = 'text/css';
        else if (filePath.endsWith('.json')) contentType = 'application/json';
        
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(content);
    } catch (error) {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
    }
});

const PORT = 4000;
server.listen(PORT, () => {
    console.log('🚀 Backup History Test Server Started');
    console.log(`📊 Open http://localhost:${PORT} to test the backup history dashboard`);
    console.log('');
    console.log('✅ Test Features:');
    console.log('  1. Navigate to the "Backup History & Analytics" section');
    console.log('  2. Check the Overview tab for summary statistics');
    console.log('  3. View the Sessions tab for detailed backup history');
    console.log('  4. Explore the Analytics tab for charts and trends');
    console.log('  5. Try the Restore tab to simulate data restoration');
    console.log('');
    console.log('🔧 Testing Steps:');
    console.log('  1. Sign up or use demo credentials');
    console.log('  2. Run some backup simulations');
    console.log('  3. Check how they appear in the history dashboard');
    console.log('  4. Click on sessions to view detailed information');
    console.log('  5. Try the restore simulation feature');
    console.log('');
    console.log('Press Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down test server...');
    server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
    });
});