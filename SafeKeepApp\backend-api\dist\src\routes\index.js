"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const pricing_1 = __importDefault(require("./pricing"));
const subscriptions_1 = __importDefault(require("./subscriptions"));
const services_1 = __importDefault(require("./services"));
const billing_1 = __importDefault(require("./billing"));
const router = (0, express_1.Router)();
router.use('/pricing', pricing_1.default);
router.use('/subscriptions', subscriptions_1.default);
router.use('/services', services_1.default);
router.use('/billing', billing_1.default);
router.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        service: 'SafeKeep Modular Pricing API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
            pricing: '/api/pricing',
            subscriptions: '/api/subscriptions',
            services: '/api/services',
            billing: '/api/billing'
        }
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map