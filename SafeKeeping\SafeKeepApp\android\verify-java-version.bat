@echo off
echo Verifying Java version for SafeKeepApp Android build...

java -version 2>&1 | findstr "version"

echo.
echo Checking if Java version is 11 or higher...

for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
)

set JAVA_VERSION=%JAVA_VERSION:"=%
set JAVA_VERSION=%JAVA_VERSION:.=%
set JAVA_VERSION=%JAVA_VERSION:_=%
set JAVA_VERSION=%JAVA_VERSION:+=%

if %JAVA_VERSION% LSS 110 (
    echo ERROR: Java version must be 11 or higher.
    echo Current version appears to be less than Java 11.
    echo Please install JDK 11+ and set JAVA_HOME correctly.
    echo See docs/java-setup-guide.md for instructions.
    exit /b 1
) else (
    echo SUCCESS: Java version is 11 or higher.
    echo You can proceed with building the Android app.
    exit /b 0
)