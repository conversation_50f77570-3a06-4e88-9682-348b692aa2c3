/**
 * Tutorial System Styles
 * Comprehensive styling for the interactive tutorial overlay system
 */

/* Tutorial Overlay System */
.tutorial-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.tutorial-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
}

.tutorial-spotlight {
    position: absolute;
    background: transparent;
    border: 3px solid #4facfe;
    border-radius: 8px;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    pointer-events: none;
}

.tutorial-content {
    position: absolute;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    min-width: 300px;
    z-index: 10001;
}

.tutorial-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 15px;
    border-bottom: 1px solid #e9ecef;
}

.tutorial-title {
    margin: 0;
    font-size: 1.2rem;
    color: #333;
    font-weight: 600;
}

.tutorial-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.tutorial-close:hover {
    background: #f8f9fa;
    color: #333;
}

.tutorial-body {
    padding: 20px;
}

.tutorial-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.tutorial-media {
    margin-top: 15px;
}

.tutorial-footer {
    padding: 15px 20px 20px;
    border-top: 1px solid #e9ecef;
}

.tutorial-progress {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.step-counter {
    font-size: 0.9rem;
    color: #666;
    white-space: nowrap;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

.tutorial-controls {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-tutorial {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-skip {
    background: #6c757d;
    color: white;
}

.btn-skip:hover {
    background: #5a6268;
}

.btn-prev {
    background: #e9ecef;
    color: #495057;
}

.btn-prev:hover {
    background: #dee2e6;
}

.btn-next,
.btn-finish {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-next:hover,
.btn-finish:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

/* Tutorial positioning */
.tutorial-content.position-top::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 10px solid transparent;
    border-top-color: white;
}

.tutorial-content.position-bottom::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 10px solid transparent;
    border-bottom-color: white;
}

.tutorial-content.position-left::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 10px solid transparent;
    border-left-color: white;
}

.tutorial-content.position-right::after {
    content: '';
    position: absolute;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 10px solid transparent;
    border-right-color: white;
}

/* Tutorial Tooltip System */
.tutorial-tooltip {
    position: fixed;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 300px;
    z-index: 9999;
    display: none;
    transform: translate(-50%, -100%);
}

.tutorial-tooltip.position-bottom {
    transform: translate(-50%, 10px);
}

.tutorial-tooltip.position-left {
    transform: translate(-100%, -50%);
}

.tutorial-tooltip.position-right {
    transform: translate(10px, -50%);
}

.tooltip-content {
    padding: 15px;
}

.tooltip-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.tooltip-description {
    color: #666;
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 12px;
}

.tooltip-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.btn-tooltip {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-got-it {
    background: #4facfe;
    color: white;
}

.btn-got-it:hover {
    background: #3d8bfe;
}

.btn-learn-more {
    background: #e9ecef;
    color: #495057;
}

.btn-learn-more:hover {
    background: #dee2e6;
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
}

.tutorial-tooltip.position-top .tooltip-arrow {
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border: 8px solid transparent;
    border-top-color: white;
}

.tutorial-tooltip.position-bottom .tooltip-arrow {
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border: 8px solid transparent;
    border-bottom-color: white;
}

.tutorial-tooltip.position-left .tooltip-arrow {
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    border: 8px solid transparent;
    border-left-color: white;
}

.tutorial-tooltip.position-right .tooltip-arrow {
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    border: 8px solid transparent;
    border-right-color: white;
}

/* Tutorial Progress Indicator */
.tutorial-progress-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 50px;
    padding: 10px 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 9998;
    transition: all 0.3s ease;
}

.progress-circle {
    position: relative;
    width: 60px;
    height: 60px;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-background {
    fill: none;
    stroke: #e9ecef;
    stroke-width: 4;
}

.progress-ring-progress {
    fill: none;
    stroke: #4facfe;
    stroke-width: 4;
    stroke-linecap: round;
    transition: stroke-dashoffset 0.3s ease;
}

.progress-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8rem;
    font-weight: 600;
    color: #4facfe;
}

.progress-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

/* Tutorial Menu */
.tutorial-menu {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9997;
}

.tutorial-menu-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(79, 172, 254, 0.3);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tutorial-menu-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(79, 172, 254, 0.4);
}

.tutorial-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-weight: 600;
    display: none;
}

.tutorial-menu-content {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 400px;
    max-height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: translateY(20px) scale(0.9);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tutorial-menu.open .tutorial-menu-content {
    transform: translateY(0) scale(1);
    opacity: 1;
    visibility: visible;
}

.tutorial-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.tutorial-menu-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.tutorial-menu-close {
    background: none;
    border: none;
    font-size: 1.3rem;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.tutorial-menu-close:hover {
    background: #f8f9fa;
    color: #333;
}

.tutorial-menu-body {
    max-height: 350px;
    overflow-y: auto;
    padding: 10px 0;
}

.tutorial-categories {
    padding: 0 20px;
}

.tutorial-category {
    margin-bottom: 25px;
}

.category-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4facfe;
    margin: 0 0 15px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tutorial-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
    position: relative;
}

.tutorial-item:hover {
    border-color: #4facfe;
    background: #f8f9ff;
}

.tutorial-item.completed {
    background: #f8fff8;
    border-color: #28a745;
}

.tutorial-info {
    flex: 1;
}

.tutorial-info h6 {
    margin: 0 0 5px 0;
    font-size: 0.95rem;
    color: #333;
    font-weight: 600;
}

.tutorial-info p {
    margin: 0 0 8px 0;
    font-size: 0.8rem;
    color: #666;
    line-height: 1.4;
}

.tutorial-meta {
    display: flex;
    gap: 10px;
    align-items: center;
}

.difficulty {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty.beginner {
    background: #d4edda;
    color: #155724;
}

.difficulty.intermediate {
    background: #fff3cd;
    color: #856404;
}

.difficulty.advanced {
    background: #f8d7da;
    color: #721c24;
}

.duration {
    font-size: 0.7rem;
    color: #666;
}

.tutorial-actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.btn-start-tutorial,
.btn-continue-tutorial {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-start-tutorial {
    background: #4facfe;
    color: white;
}

.btn-start-tutorial:hover {
    background: #3d8bfe;
}

.btn-continue-tutorial {
    background: #28a745;
    color: white;
}

.btn-continue-tutorial:hover {
    background: #218838;
}

.completion-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.tutorial-menu-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn-reset-progress,
.btn-demo-reset {
    padding: 8px 16px;
    border: 1px solid #dc3545;
    background: white;
    color: #dc3545;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-reset-progress:hover,
.btn-demo-reset:hover {
    background: #dc3545;
    color: white;
}

/* Tutorial Completion Message */
.tutorial-completion-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10002;
    animation: fadeIn 0.3s ease;
}

.completion-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.completion-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.completion-content h3 {
    color: #333;
    margin: 0 0 15px 0;
    font-size: 1.3rem;
}

.completion-content p {
    color: #666;
    margin: 0 0 25px 0;
    line-height: 1.5;
}

.btn-close-completion {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-close-completion:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

/* Tutorial Highlight Effects */
.tutorial-highlight {
    position: relative;
    z-index: 9999;
    box-shadow: 0 0 0 3px #4facfe, 0 0 20px rgba(79, 172, 254, 0.3) !important;
    border-radius: 8px !important;
}

.tutorial-pulse {
    animation: tutorialPulse 2s infinite;
}

.tutorial-shake {
    animation: tutorialShake 0.5s ease-in-out;
}

.tutorial-glow {
    animation: tutorialGlow 2s infinite alternate;
}

@keyframes tutorialPulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes tutorialShake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

@keyframes tutorialGlow {
    0% {
        box-shadow: 0 0 0 3px #4facfe, 0 0 20px rgba(79, 172, 254, 0.3);
    }

    100% {
        box-shadow: 0 0 0 3px #4facfe, 0 0 30px rgba(79, 172, 254, 0.6);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .tutorial-content {
        max-width: 90vw;
        min-width: 280px;
    }

    .tutorial-menu-content {
        width: 90vw;
        max-width: 350px;
        right: 50%;
        transform: translateX(50%) translateY(20px) scale(0.9);
    }

    .tutorial-menu.open .tutorial-menu-content {
        transform: translateX(50%) translateY(0) scale(1);
    }

    .tutorial-progress-indicator {
        top: 10px;
        right: 10px;
        padding: 8px 12px;
    }

    .progress-circle {
        width: 40px;
        height: 40px;
    }

    .progress-ring {
        width: 40px;
        height: 40px;
    }

    /* Note: SVG circle attributes (cx, cy, r) for .progress-ring-background and .progress-ring-progress
       should be set directly in the HTML markup, not in CSS. For mobile responsive design,
       use JavaScript to dynamically update these attributes based on screen size. */

    .tutorial-menu-toggle {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .tutorial-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .tutorial-actions {
        flex-direction: row;
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .tutorial-content {
        margin: 20px;
        max-width: calc(100vw - 40px);
    }

    .tutorial-controls {
        flex-wrap: wrap;
        gap: 8px;
    }

    .btn-tutorial {
        flex: 1;
        min-width: 80px;
    }

    .tutorial-menu-content {
        bottom: 60px;
        max-height: 70vh;
    }

    .completion-content {
        margin: 20px;
        padding: 30px 20px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {

    .tutorial-content,
    .tutorial-tooltip,
    .tutorial-progress-indicator,
    .tutorial-menu-content,
    .completion-content {
        background: #2d3748;
        color: #e2e8f0;
    }

    .tutorial-title,
    .tooltip-title,
    .tutorial-menu-header h4,
    .completion-content h3 {
        color: #e2e8f0;
    }

    .tutorial-description,
    .tooltip-description,
    .completion-content p {
        color: #a0aec0;
    }

    .tutorial-header,
    .tutorial-footer,
    .tutorial-menu-header,
    .tutorial-menu-footer {
        border-color: #4a5568;
    }

    .tutorial-item {
        background: #374151;
        border-color: #4a5568;
    }

    .tutorial-item:hover {
        background: #4a5568;
        border-color: #4facfe;
    }

    .tutorial-item.completed {
        background: #065f46;
        border-color: #10b981;
    }
}

/* Accessibility improvements */
.tutorial-overlay,
.tutorial-tooltip,
.tutorial-menu-content {
    font-size: 16px;
    /* Ensure minimum font size for accessibility */
}

.btn-tutorial,
.btn-tooltip,
.tutorial-menu-toggle {
    min-height: 44px;
    /* Minimum touch target size */
    min-width: 44px;
}

/* Focus styles for keyboard navigation */
.btn-tutorial:focus,
.btn-tooltip:focus,
.tutorial-close:focus,
.tutorial-menu-close:focus,
.tutorial-menu-toggle:focus {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {

    .tutorial-content,
    .tutorial-tooltip,
    .tutorial-menu-content {
        border: 2px solid #000;
    }

    .tutorial-highlight {
        box-shadow: 0 0 0 4px #000 !important;
    }

    .btn-tutorial,
    .btn-tooltip {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

    .tutorial-spotlight,
    .progress-fill,
    .progress-ring-progress,
    .tutorial-menu-content,
    .tutorial-completion-message {
        transition: none;
    }

    .tutorial-pulse,
    .tutorial-shake,
    .tutorial-glow {
        animation: none;
    }

    .btn-tutorial:hover,
    .btn-tooltip:hover,
    .tutorial-menu-toggle:hover {
        transform: none;
    }
}