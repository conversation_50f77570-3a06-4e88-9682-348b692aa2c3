"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractUserId = exports.requireRole = exports.authenticateToken = void 0;
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        const response = {
            success: false,
            error: {
                code: 'MISSING_TOKEN',
                message: 'Access token is required',
                timestamp: new Date().toISOString()
            }
        };
        res.status(401).json(response);
        return;
    }
    try {
        req.user = {
            id: 'placeholder-user-id',
            email: '<EMAIL>'
        };
        next();
    }
    catch (error) {
        const response = {
            success: false,
            error: {
                code: 'INVALID_TOKEN',
                message: 'Invalid access token',
                timestamp: new Date().toISOString()
            }
        };
        res.status(403).json(response);
    }
};
exports.authenticateToken = authenticateToken;
const requireRole = (role) => {
    return (req, res, next) => {
        if (!req.user) {
            const response = {
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Authentication required',
                    timestamp: new Date().toISOString()
                }
            };
            res.status(401).json(response);
            return;
        }
        if (req.user.role !== role) {
            const response = {
                success: false,
                error: {
                    code: 'INSUFFICIENT_PERMISSIONS',
                    message: `${role} role required`,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(403).json(response);
            return;
        }
        next();
    };
};
exports.requireRole = requireRole;
const extractUserId = (req) => {
    return req.user?.id || null;
};
exports.extractUserId = extractUserId;
//# sourceMappingURL=auth.js.map