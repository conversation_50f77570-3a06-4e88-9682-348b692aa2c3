/**
 * Verification Script for Payment Processing Interface
 * Quick verification of payment processing components
 */

async function verifyPaymentInterface() {
    console.log('🔍 Verifying Payment Processing Interface...');
    
    try {
        // Check if all required classes are available
        const requiredClasses = [
            'PaymentProcessor',
            'PaymentMethodsManager',
            'SubscriptionFlowManager',
            'TaxCalculator'
        ];
        
        const missingClasses = requiredClasses.filter(className => !window[className]);
        
        if (missingClasses.length > 0) {
            throw new Error(`Missing required classes: ${missingClasses.join(', ')}`);
        }
        
        console.log('✅ All required payment classes loaded');
        
        // Initialize components
        console.log('🔧 Initializing payment components...');
        
        const stripeManager = new StripeManager();
        const subscriptionManager = new SubscriptionManager(stripeManager);
        const paymentProcessor = new PaymentProcessor(stripeManager, subscriptionManager);
        const paymentMethodsManager = new PaymentMethodsManager(stripeManager, paymentProcessor);
        const subscriptionFlowManager = new SubscriptionFlowManager(subscriptionManager, paymentProcessor, paymentMethodsManager);
        const taxCalculator = new TaxCalculator();
        
        // Initialize Stripe and subscription manager
        await stripeManager.initialize();
        await subscriptionManager.initialize('verification_user');
        console.log('✅ Core components initialized');
        
        // Test payment processor initialization
        await paymentProcessor.initialize();
        if (!paymentProcessor.stripe || !paymentProcessor.elements) {
            throw new Error('Payment processor failed to initialize Stripe elements');
        }
        console.log('✅ Payment processor initialized');
        
        // Test payment methods manager
        await paymentMethodsManager.initialize('verification_customer');
        if (paymentMethodsManager.currentCustomerId !== 'verification_customer') {
            throw new Error('Payment methods manager failed to set customer ID');
        }
        console.log('✅ Payment methods manager initialized');
        
        // Test subscription flow manager
        await subscriptionFlowManager.initialize();
        console.log('✅ Subscription flow manager initialized');
        
        // Test tax calculator
        const testTax = await taxCalculator.calculateTax(1000, { country: 'US', state: 'CA' });
        if (typeof testTax !== 'number') {
            throw new Error('Tax calculator failed to calculate tax');
        }
        console.log('✅ Tax calculator working');
        
        // Test payment intent creation
        const paymentIntentData = await paymentProcessor.createPaymentIntent({
            tierId: 'basic',
            billingAddress: { country: 'US', state: 'CA' }
        });
        
        if (!paymentIntentData.client_secret || !paymentIntentData.tier) {
            throw new Error('Payment intent creation failed');
        }
        console.log('✅ Payment intent creation verified');
        
        // Test billing address form creation
        const testContainer = document.createElement('div');
        testContainer.id = 'verification-billing-container';
        document.body.appendChild(testContainer);
        
        paymentProcessor.createBillingAddressForm('verification-billing-container');
        
        if (!document.querySelector('.billing-address-form')) {
            throw new Error('Billing address form creation failed');
        }
        
        testContainer.remove();
        console.log('✅ Billing address form creation verified');
        
        // Test payment method addition
        const testPaymentMethod = {
            id: 'pm_verification_123',
            type: 'card',
            card: {
                brand: 'visa',
                last4: '4242',
                exp_month: 12,
                exp_year: 2025
            },
            billing_details: {
                name: 'Verification User',
                email: '<EMAIL>'
            }
        };
        
        await paymentMethodsManager.addPaymentMethod(testPaymentMethod, true);
        
        if (!paymentMethodsManager.paymentMethods.has('pm_verification_123')) {
            throw new Error('Payment method addition failed');
        }
        console.log('✅ Payment method management verified');
        
        // Test subscription flow initialization
        await subscriptionFlowManager.startUpgradeFlow('premium');
        
        if (subscriptionFlowManager.currentFlow !== 'upgrade' || 
            subscriptionFlowManager.flowData.targetTierId !== 'premium') {
            throw new Error('Subscription flow initialization failed');
        }
        
        subscriptionFlowManager.cancelFlow();
        console.log('✅ Subscription flow management verified');
        
        // Test tax information retrieval
        const taxInfo = taxCalculator.getTaxInfo('US', 'CA');
        if (!taxInfo.rate || !taxInfo.description || !taxInfo.percentage) {
            throw new Error('Tax information retrieval failed');
        }
        console.log('✅ Tax information system verified');
        
        // Test UI component creation
        const uiContainer = document.createElement('div');
        uiContainer.id = 'verification-ui-container';
        document.body.appendChild(uiContainer);
        
        paymentMethodsManager.createPaymentMethodsInterface();
        
        if (!document.getElementById('payment-methods-container')) {
            throw new Error('Payment methods UI creation failed');
        }
        
        // Cleanup UI test
        const createdContainer = document.getElementById('payment-methods-container');
        if (createdContainer) {
            createdContainer.remove();
        }
        uiContainer.remove();
        console.log('✅ UI component creation verified');
        
        // Test CSS styles loading
        const testElement = document.createElement('div');
        testElement.className = 'payment-modal';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement);
        const hasStyles = computedStyle.position === 'fixed';
        
        testElement.remove();
        
        if (!hasStyles) {
            console.warn('⚠️ Payment interface CSS styles may not be loaded');
        } else {
            console.log('✅ CSS styles verified');
        }
        
        // Cleanup
        paymentProcessor.cleanup();
        paymentMethodsManager.cleanup();
        subscriptionFlowManager.cleanup();
        
        console.log('🎉 Payment Processing Interface verification completed successfully!');
        
        return {
            success: true,
            message: 'All payment interface components verified successfully',
            components: {
                paymentProcessor: '✅ Working',
                paymentMethodsManager: '✅ Working',
                subscriptionFlowManager: '✅ Working',
                taxCalculator: '✅ Working',
                stripeIntegration: '✅ Working',
                uiComponents: '✅ Working',
                cssStyles: hasStyles ? '✅ Loaded' : '⚠️ Check loading'
            }
        };
        
    } catch (error) {
        console.error('❌ Payment interface verification failed:', error);
        
        return {
            success: false,
            message: error.message,
            error: error
        };
    }
}

/**
 * Display verification results in UI
 */
function displayPaymentVerificationResults(results) {
    const container = document.createElement('div');
    container.id = 'payment-verification-results';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${results.success ? '#d4edda' : '#f8d7da'};
        border: 1px solid ${results.success ? '#c3e6cb' : '#f5c6cb'};
        color: ${results.success ? '#155724' : '#721c24'};
        padding: 15px;
        border-radius: 8px;
        max-width: 400px;
        z-index: 10000;
        font-family: monospace;
        font-size: 12px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        max-height: 80vh;
        overflow-y: auto;
    `;
    
    container.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 10px;">
            ${results.success ? '✅' : '❌'} Payment Interface Verification
        </div>
        <div style="margin-bottom: 10px;">
            ${results.message}
        </div>
        ${results.components ? `
            <div style="font-size: 11px;">
                <div style="font-weight: bold; margin-bottom: 5px;">Components:</div>
                ${Object.entries(results.components).map(([key, status]) => 
                    `<div style="margin-left: 10px;">${key}: ${status}</div>`
                ).join('')}
            </div>
        ` : ''}
        <div style="margin-top: 10px;">
            <button onclick="this.parentElement.remove()" style="
                padding: 5px 10px;
                border: none;
                background: ${results.success ? '#28a745' : '#dc3545'};
                color: white;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 5px;
            ">Close</button>
            <button onclick="window.open('?test=payment', '_blank')" style="
                padding: 5px 10px;
                border: none;
                background: #007bff;
                color: white;
                border-radius: 4px;
                cursor: pointer;
            ">Run Full Tests</button>
        </div>
    `;
    
    document.body.appendChild(container);
    
    // Auto-remove after 15 seconds
    setTimeout(() => {
        if (container.parentElement) {
            container.remove();
        }
    }, 15000);
}

/**
 * Test payment interface integration
 */
async function testPaymentIntegration() {
    console.log('🔗 Testing Payment Interface Integration...');
    
    try {
        // Test complete payment flow simulation
        const stripeManager = new StripeManager();
        const subscriptionManager = new SubscriptionManager(stripeManager);
        const paymentProcessor = new PaymentProcessor(stripeManager, subscriptionManager);
        
        await stripeManager.initialize();
        await subscriptionManager.initialize('integration_test_user');
        await paymentProcessor.initialize();
        
        // Simulate payment processing
        const billingAddress = {
            name: 'Integration Test',
            email: '<EMAIL>',
            line1: '123 Integration St',
            city: 'Test City',
            state: 'CA',
            postal_code: '12345',
            country: 'US'
        };
        
        const result = await paymentProcessor.processSubscriptionPayment('basic', billingAddress);
        
        console.log('Integration test result:', result);
        return result.success;
        
    } catch (error) {
        console.error('Integration test failed:', error);
        return false;
    }
}

/**
 * Run verification when page loads
 */
if (typeof window !== 'undefined') {
    window.verifyPaymentInterface = verifyPaymentInterface;
    window.displayPaymentVerificationResults = displayPaymentVerificationResults;
    window.testPaymentIntegration = testPaymentIntegration;
    
    // Auto-run verification if requested
    window.addEventListener('load', async () => {
        if (window.location.search.includes('verify=payment')) {
            const results = await verifyPaymentInterface();
            displayPaymentVerificationResults(results);
            
            // Also test integration
            const integrationSuccess = await testPaymentIntegration();
            console.log(`🔗 Integration test: ${integrationSuccess ? '✅ Passed' : '❌ Failed'}`);
        }
    });
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { verifyPaymentInterface, testPaymentIntegration };
}