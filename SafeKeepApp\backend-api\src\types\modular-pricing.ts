// TypeScript interfaces and types for modular pricing system

export interface PricingResult {
  recommendedPlanId: string;
  recommendedPlanName: string;
  priceCents: number;
  savingsCents: number;
  individualTotalCents: number;
}

export interface ServiceCombination {
  planId: string;
  planName: string;
  priceCents: number;
  services: string[];
  storageGb: number;
  isPopular: boolean;
}

export interface PlanRecommendation {
  planId: string;
  planName: string;
  priceCents: number;
  reason: string;
  savingsCents: number;
}

export interface SubscriptionRequest {
  userId: string;
  serviceIds: string[];
  paymentMethodId?: string;
  customerId?: string;
}

export interface SubscriptionDetails {
  subscriptionId: string;
  planId: string;
  planName: string;
  currentPriceCents: number;
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  services: string[];
  storageQuotaGb: number;
  storageUsedGb: number;
  nextBillingDate: Date;
}

export interface ModularSubscription {
  id: string;
  userId: string;
  planId: string;
  serviceIds: string[];
  totalPriceCents: number;
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ServiceSelection {
  userId: string;
  serviceTypeId: string;
  isActive: boolean;
  activatedAt: Date;
  deactivatedAt?: Date;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface AccessResult {
  hasAccess: boolean;
  expiresAt?: Date;
  planName?: string;
}

export interface UserService {
  serviceId: string;
  serviceName: string;
  isActive: boolean;
  activatedAt: Date;
  expiresAt?: Date;
}

export interface BillingMetadata {
  userId: string;
  serviceIds: string[];
  planId: string;
  planName: string;
}

export interface PaymentResult {
  success: boolean;
  paymentIntentId?: string;
  subscriptionId?: string;
  error?: string;
}

export interface WebhookResult {
  processed: boolean;
  action?: string;
  error?: string;
}

export interface PricingCalculation {
  requestedServices: string[];
  optimalPlan: {
    planId: string;
    planName: string;
    priceCents: number;
  };
  alternatives: Array<{
    planId: string;
    planName: string;
    priceCents: number;
    savingsCents: number;
  }>;
  individualTotal: number;
  calculatedAt: Date;
}

export interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
  };
}

// Service type constants
export const SERVICE_TYPES = {
  CONTACTS: 'contacts',
  MESSAGES: 'messages',
  PHOTOS: 'photos'
} as const;

export type ServiceType = typeof SERVICE_TYPES[keyof typeof SERVICE_TYPES];

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ErrorResponse['error'];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}