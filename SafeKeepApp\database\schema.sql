-- SafeKeep Database Schema for Supabase
-- Run these commands in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    display_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    storage_used BIGINT DEFAULT 0,
    storage_quota BIGINT DEFAULT 5368709120, -- 5GB in bytes
    encryption_key_id TEXT,
    backup_settings JSONB DEFAULT '{
        "auto_backup": true,
        "wifi_only": true,
        "frequency": "daily"
    }'::jsonb
);

-- Create file_metadata table
CREATE TABLE public.file_metadata (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    original_name TEXT NOT NULL,
    encrypted_name TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    size BIGINT NOT NULL,
    encrypted_size BIGINT NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_modified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    category TEXT CHECK (category IN ('photo', 'contact', 'message')) NOT NULL,
    hash TEXT NOT NULL,
    encryption_iv TEXT NOT NULL,
    encryption_salt TEXT NOT NULL,
    storage_path TEXT NOT NULL,
    is_backed_up BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create backup_sessions table
CREATE TABLE public.backup_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    session_type TEXT CHECK (session_type IN ('manual', 'automatic')) NOT NULL,
    status TEXT CHECK (status IN ('running', 'completed', 'failed', 'cancelled')) DEFAULT 'running',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    total_files INTEGER NOT NULL,
    processed_files INTEGER DEFAULT 0,
    failed_files INTEGER DEFAULT 0,
    total_bytes BIGINT NOT NULL,
    processed_bytes BIGINT DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create storage_usage table for detailed tracking
CREATE TABLE public.storage_usage (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    category TEXT CHECK (category IN ('photo', 'contact', 'message')) NOT NULL,
    file_count INTEGER DEFAULT 0,
    total_size BIGINT DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sync_status table for real-time sync tracking
CREATE TABLE public.sync_status (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status TEXT CHECK (sync_status IN ('synced', 'pending', 'error')) DEFAULT 'pending',
    pending_uploads INTEGER DEFAULT 0,
    pending_downloads INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_file_metadata_user_id ON public.file_metadata(user_id);
CREATE INDEX idx_file_metadata_category ON public.file_metadata(category);
CREATE INDEX idx_file_metadata_uploaded_at ON public.file_metadata(uploaded_at DESC);
CREATE INDEX idx_file_metadata_hash ON public.file_metadata(hash);
CREATE INDEX idx_backup_sessions_user_id ON public.backup_sessions(user_id);
CREATE INDEX idx_backup_sessions_status ON public.backup_sessions(status);
CREATE INDEX idx_storage_usage_user_id ON public.storage_usage(user_id);
CREATE INDEX idx_sync_status_user_id ON public.sync_status(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_file_metadata_updated_at BEFORE UPDATE ON public.file_metadata
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_backup_sessions_updated_at BEFORE UPDATE ON public.backup_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_storage_usage_updated_at BEFORE UPDATE ON public.storage_usage
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sync_status_updated_at BEFORE UPDATE ON public.sync_status
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storage_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sync_status ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- File metadata policies
CREATE POLICY "Users can view own files" ON public.file_metadata
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own files" ON public.file_metadata
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own files" ON public.file_metadata
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own files" ON public.file_metadata
    FOR DELETE USING (auth.uid() = user_id);

-- Backup sessions policies
CREATE POLICY "Users can view own backup sessions" ON public.backup_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own backup sessions" ON public.backup_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own backup sessions" ON public.backup_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- Storage usage policies
CREATE POLICY "Users can view own storage usage" ON public.storage_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own storage usage" ON public.storage_usage
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own storage usage" ON public.storage_usage
    FOR UPDATE USING (auth.uid() = user_id);

-- Sync status policies
CREATE POLICY "Users can view own sync status" ON public.sync_status
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sync status" ON public.sync_status
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sync status" ON public.sync_status
    FOR UPDATE USING (auth.uid() = user_id);

-- Create storage buckets (run these in Supabase Storage)
-- Note: These need to be created through the Supabase Dashboard or Storage API

-- Bucket: user-data
-- Public: false
-- File size limit: 50MB
-- Allowed MIME types: application/octet-stream (for encrypted files)

-- Bucket: thumbnails  
-- Public: true
-- File size limit: 5MB
-- Allowed MIME types: image/jpeg, image/png, image/webp

-- Storage policies (create these in Supabase Dashboard under Storage > Policies)

-- Policy for user-data bucket:
-- Name: "Users can upload own encrypted files"
-- Operation: INSERT
-- Target roles: authenticated
-- Policy definition: bucket_id = 'user-data' AND (storage.foldername(name))[1] = auth.uid()::text

-- Policy for user-data bucket:
-- Name: "Users can view own encrypted files"  
-- Operation: SELECT
-- Target roles: authenticated
-- Policy definition: bucket_id = 'user-data' AND (storage.foldername(name))[1] = auth.uid()::text

-- Policy for user-data bucket:
-- Name: "Users can delete own encrypted files"
-- Operation: DELETE  
-- Target roles: authenticated
-- Policy definition: bucket_id = 'user-data' AND (storage.foldername(name))[1] = auth.uid()::text

-- Function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, display_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'display_name', split_part(NEW.email, '@', 1))
    );
    
    -- Initialize storage usage tracking
    INSERT INTO public.storage_usage (user_id, category, file_count, total_size)
    VALUES 
        (NEW.id, 'photo', 0, 0),
        (NEW.id, 'contact', 0, 0),
        (NEW.id, 'message', 0, 0);
    
    -- Initialize sync status
    INSERT INTO public.sync_status (user_id, sync_status)
    VALUES (NEW.id, 'synced');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update storage usage
CREATE OR REPLACE FUNCTION public.update_storage_usage()
RETURNS TRIGGER AS $$
BEGIN
    -- Update storage usage when files are added/removed
    IF TG_OP = 'INSERT' THEN
        INSERT INTO public.storage_usage (user_id, category, file_count, total_size)
        VALUES (NEW.user_id, NEW.category, 1, NEW.encrypted_size)
        ON CONFLICT (user_id, category) 
        DO UPDATE SET 
            file_count = storage_usage.file_count + 1,
            total_size = storage_usage.total_size + NEW.encrypted_size,
            last_updated = NOW();
            
        -- Update user's total storage used
        UPDATE public.users 
        SET storage_used = (
            SELECT COALESCE(SUM(total_size), 0) 
            FROM public.storage_usage 
            WHERE user_id = NEW.user_id
        )
        WHERE id = NEW.user_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.storage_usage 
        SET 
            file_count = GREATEST(file_count - 1, 0),
            total_size = GREATEST(total_size - OLD.encrypted_size, 0),
            last_updated = NOW()
        WHERE user_id = OLD.user_id AND category = OLD.category;
        
        -- Update user's total storage used
        UPDATE public.users 
        SET storage_used = (
            SELECT COALESCE(SUM(total_size), 0) 
            FROM public.storage_usage 
            WHERE user_id = OLD.user_id
        )
        WHERE id = OLD.user_id;
        
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for storage usage updates
CREATE TRIGGER update_storage_usage_trigger
    AFTER INSERT OR DELETE ON public.file_metadata
    FOR EACH ROW EXECUTE FUNCTION public.update_storage_usage();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
