/**
 * Real-time Backup Console
 * Advanced UI component for displaying real-time backup progress
 */

class BackupConsole {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.progressManager = null;
        this.activeSessionId = null;
        this.updateInterval = null;
        
        this.initializeConsole();
    }

    initializeConsole() {
        this.container.innerHTML = `
            <div class="backup-console">
                <div class="console-header">
                    <div>
                        <h3>🚀 Real-time Backup Console</h3>
                        <div style="display: flex; align-items: center; gap: 15px; margin-top: 5px; font-size: 0.85rem;">
                            <div class="status-indicator">
                                <div class="status-dot connected" id="websocket-status"></div>
                                <span>WebSocket Connected</span>
                            </div>
                            <div>Active Sessions: <span id="active-sessions-count">0</span></div>
                            <div>Queue: <span id="session-queue-count">0</span></div>
                        </div>
                    </div>
                    <div class="console-controls">
                        <button class="btn secondary" onclick="backupConsole.startAdvancedBackup()">Start Advanced Backup</button>
                        <button class="btn secondary" onclick="backupConsole.startConcurrentBackups()">Start Multiple Sessions</button>
                        <button class="btn danger" onclick="backupConsole.pauseBackup()" id="pause-btn" disabled>Pause</button>
                        <button class="btn" onclick="backupConsole.resumeBackup()" id="resume-btn" disabled>Resume</button>
                        <button class="btn danger" onclick="backupConsole.cancelBackup()" id="cancel-btn" disabled>Cancel</button>
                    </div>
                </div>
                
                <div class="console-body" id="console-body">
                    <div class="console-placeholder">
                        <p>🎯 Click "Start Advanced Backup" to see real-time progress in action!</p>
                        <p>Features: Live progress bars, transfer rates, ETA, error handling, and more.</p>
                    </div>
                </div>
            </div>
        `;
    }

    setProgressManager(progressManager) {
        this.progressManager = progressManager;
    }

    async startAdvancedBackup() {
        if (!this.progressManager) {
            log('❌ Progress manager not initialized', 'error');
            return;
        }

        const sessionId = `advanced-session-${Date.now()}`;
        this.activeSessionId = sessionId;

        // Create session with advanced configuration
        const session = this.progressManager.createBackupSession(sessionId, {
            type: 'manual',
            includeContacts: true,
            includeMessages: true,
            includePhotos: true,
            simulateErrors: true,
            simulateNetworkIssues: true
        });

        // Set up progress listener
        this.progressManager.addProgressListener(sessionId, (event, data) => {
            this.handleProgressEvent(event, data);
        });

        // Update UI
        this.renderBackupSession(session);
        this.updateControlButtons(true);

        // Start the backup
        await this.progressManager.startBackupSession(sessionId);
    }

    handleProgressEvent(event, data) {
        switch (event) {
            case 'session_started':
                this.updateSessionStatus(data, 'Running');
                break;
            case 'progress_updated':
                this.updateProgressBars(data);
                break;
            case 'item_progress':
                this.updateCurrentItem(data);
                break;
            case 'detailed_item_progress':
                this.updateDetailedCurrentItem(data);
                break;
            case 'error_occurred':
                this.displayError(data);
                break;
            case 'network_issue':
                this.displayNetworkIssue(data);
                break;
            case 'network_recovered':
                this.displayNetworkRecovery(data);
                break;
            case 'session_completed':
                this.handleSessionCompleted(data);
                break;
            case 'session_paused':
                this.updateSessionStatus(data, 'Paused');
                break;
            case 'session_resumed':
                this.updateSessionStatus(data, 'Running');
                break;
            case 'session_cancelled':
                this.handleSessionCancelled(data);
                break;
        }
    }

    renderBackupSession(session) {
        const consoleBody = document.getElementById('console-body');
        consoleBody.innerHTML = `
            <div class="session-overview">
                <div class="session-header">
                    <h4>Session: ${session.id}</h4>
                    <div class="session-status" id="session-status">Initializing...</div>
                </div>
                
                <div class="overall-progress">
                    <div class="progress-section">
                        <div class="progress-header">
                            <span>Overall Progress</span>
                            <span id="overall-percentage">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="overall-progress"></div>
                            </div>
                        </div>
                        <div class="progress-details">
                            <span id="overall-items">0 / 0 items</span>
                            <span id="overall-eta">ETA: Calculating...</span>
                            <span id="overall-speed">Speed: 0 B/s</span>
                        </div>
                    </div>
                </div>
                
                <div class="phase-progress">
                    <div class="phase-section" id="contacts-section">
                        <div class="phase-header">
                            <span>📞 Contacts</span>
                            <span id="contacts-percentage">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="contacts-progress"></div>
                            </div>
                        </div>
                        <div class="phase-details">
                            <span id="contacts-items">0 / 0</span>
                            <span id="contacts-current">Ready</span>
                            <span id="contacts-speed">0 B/s</span>
                        </div>
                    </div>
                    
                    <div class="phase-section" id="messages-section">
                        <div class="phase-header">
                            <span>💬 Messages</span>
                            <span id="messages-percentage">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="messages-progress"></div>
                            </div>
                        </div>
                        <div class="phase-details">
                            <span id="messages-items">0 / 0</span>
                            <span id="messages-current">Ready</span>
                            <span id="messages-speed">0 B/s</span>
                        </div>
                    </div>
                    
                    <div class="phase-section" id="photos-section">
                        <div class="phase-header">
                            <span>📸 Photos</span>
                            <span id="photos-percentage">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="photos-progress"></div>
                            </div>
                        </div>
                        <div class="phase-details">
                            <span id="photos-items">0 / 0</span>
                            <span id="photos-current">Ready</span>
                            <span id="photos-speed">0 B/s</span>
                        </div>
                    </div>
                </div>
                
                <div class="current-operation" id="current-operation">
                    <div class="operation-header">Current Operation</div>
                    <div class="operation-details">
                        <div id="current-item">Initializing...</div>
                        <div class="operation-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="current-item-progress"></div>
                            </div>
                            <span id="current-item-percentage">0%</span>
                        </div>
                    </div>
                    <div class="operation-details" style="margin-top: 10px; font-size: 0.85rem; color: #666;">
                        <div>Phase: <span id="current-phase">-</span></div>
                        <div>File Size: <span id="current-file-size">-</span></div>
                        <div>Transferred: <span id="current-file-transferred">-</span></div>
                    </div>
                    <div class="operation-details" style="margin-top: 8px; font-size: 0.8rem; color: #666;">
                        <div>Processing Time: <span id="current-processing-time">-</span></div>
                        <div>Speed: <span id="current-speed-detailed">-</span></div>
                        <div>ETA: <span id="current-eta">-</span></div>
                    </div>
                    <div class="operation-details" style="margin-top: 8px; font-size: 0.8rem; color: #666;">
                        <div>Compression: <span id="current-compression">-</span></div>
                        <div>Encryption: <span id="current-encryption">-</span></div>
                    </div>
                </div>
                
                <div class="performance-metrics">
                    <div class="metrics-grid">
                        <div class="metric">
                            <div class="metric-label">Data Transferred</div>
                            <div class="metric-value" id="data-transferred">0 B</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Average Speed</div>
                            <div class="metric-value" id="avg-speed">0 B/s</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Peak Speed</div>
                            <div class="metric-value" id="peak-speed">0 B/s</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Errors</div>
                            <div class="metric-value" id="error-count">0</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Retries</div>
                            <div class="metric-value" id="retry-count">0</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Queue</div>
                            <div class="metric-value" id="queue-count">0</div>
                        </div>
                    </div>
                </div>
                
                <div class="error-log" id="error-log" style="display: none;">
                    <div class="error-header">Error Log</div>
                    <div class="error-list" id="error-list"></div>
                </div>
            </div>
        `;
    }

    updateSessionStatus(session, status) {
        const statusElement = document.getElementById('session-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `session-status ${status.toLowerCase()}`;
        }
    }

    updateProgressBars(session) {
        // Update overall progress
        this.updateElement('overall-percentage', `${session.overall.percentage}%`);
        this.updateProgressBar('overall-progress', session.overall.percentage);
        this.updateElement('overall-items', `${session.overall.completed} / ${session.overall.total} items`);
        this.updateElement('overall-eta', `ETA: ${this.formatTime(session.overall.estimatedTimeRemaining)}`);
        this.updateElement('overall-speed', `Speed: ${this.formatBytes(session.overall.transferRate)}/s`);

        // Update phase progress
        ['contacts', 'messages', 'photos'].forEach(phase => {
            const phaseData = session[phase];
            this.updateElement(`${phase}-percentage`, `${phaseData.percentage}%`);
            this.updateProgressBar(`${phase}-progress`, phaseData.percentage);
            this.updateElement(`${phase}-items`, `${phaseData.completed} / ${phaseData.total}`);
            this.updateElement(`${phase}-current`, phaseData.currentItem || phaseData.status);
            this.updateElement(`${phase}-speed`, `${this.formatBytes(phaseData.transferRate)}/s`);
        });

        // Update performance metrics
        this.updateElement('data-transferred', this.formatBytes(session.performance.bytesTransferred));
        this.updateElement('avg-speed', `${this.formatBytes(session.performance.averageSpeed)}/s`);
        this.updateElement('peak-speed', `${this.formatBytes(session.performance.peakSpeed)}/s`);
        this.updateElement('error-count', session.performance.errorCount.toString());
        this.updateElement('retry-count', session.performance.retryCount.toString());
        
        // Update queue information if available
        if (this.progressManager) {
            const queueLength = this.progressManager.sessionQueue ? this.progressManager.sessionQueue.length : 0;
            this.updateElement('queue-count', queueLength.toString());
        }
    }

    updateCurrentItem(data) {
        this.updateElement('current-item', `${data.phase}: ${data.item}`);
        this.updateProgressBar('current-item-progress', data.progress);
        this.updateElement('current-item-percentage', `${data.progress}%`);
        
        // Update detailed file information
        if (data.fileDetails) {
            this.updateElement('current-phase', data.currentPhase || '-');
            this.updateElement('current-file-size', this.formatBytes(data.fileDetails.currentFileSize));
            this.updateElement('current-file-transferred', this.formatBytes(data.fileDetails.currentFileTransferred));
        }
    }

    updateDetailedCurrentItem(data) {
        // Enhanced update with detailed progress information
        this.updateElement('current-item', `${data.phase}: ${data.item}`);
        this.updateProgressBar('current-item-progress', data.progress);
        this.updateElement('current-item-percentage', `${data.progress}%`);
        
        // Update phase information with description
        if (data.phaseDescription) {
            this.updateElement('current-phase', `${data.currentPhase} - ${data.phaseDescription}`);
        } else {
            this.updateElement('current-phase', data.currentPhase || '-');
        }
        
        // Update detailed file information
        if (data.fileDetails) {
            this.updateElement('current-file-size', this.formatBytes(data.fileDetails.currentFileSize));
            this.updateElement('current-file-transferred', this.formatBytes(data.fileDetails.currentFileTransferred));
            
            // Add processing time if available
            if (data.timing && data.timing.elapsed) {
                const processingTime = Math.round(data.timing.elapsed / 1000);
                this.updateElement('current-processing-time', `${processingTime}s`);
            }
        }
        
        // Update speed information with multiple metrics
        if (data.speeds) {
            const speedText = `${this.formatBytes(data.speeds.smoothed)}/s (peak: ${this.formatBytes(data.speeds.peak)}/s)`;
            this.updateElement('current-speed-detailed', speedText);
        }
        
        // Update ETA information
        if (data.timing && data.timing.estimatedCompletion) {
            const eta = Math.round(data.timing.estimatedCompletion / 1000);
            this.updateElement('current-eta', `${eta}s remaining`);
        }
        
        // Update compression and encryption info
        if (data.transferStats) {
            if (data.transferStats.compressionRatio) {
                const compressionPercent = Math.round((1 - data.transferStats.compressionRatio) * 100);
                this.updateElement('current-compression', `${compressionPercent}% compressed`);
            }
            
            if (data.transferStats.encryptionOverhead) {
                const overheadPercent = Math.round(data.transferStats.encryptionOverhead * 100);
                this.updateElement('current-encryption', `+${overheadPercent}% encryption overhead`);
            }
        }
    }

    displayError(data) {
        const errorLog = document.getElementById('error-log');
        const errorList = document.getElementById('error-list');
        
        if (errorLog && errorList) {
            errorLog.style.display = 'block';
            
            const errorItem = document.createElement('div');
            errorItem.className = 'error-item';
            errorItem.innerHTML = `
                <div class="error-time">${new Date().toLocaleTimeString()}</div>
                <div class="error-phase">${data.phase}</div>
                <div class="error-message">${data.error.error}</div>
                <div class="error-item-name">${data.error.item}</div>
            `;
            
            errorList.appendChild(errorItem);
            errorList.scrollTop = errorList.scrollHeight;
        }
    }

    displayNetworkIssue(data) {
        log(`🌐 Network issue: ${data.message}`, 'error');
        // Could add visual indicator for network issues
    }

    displayNetworkRecovery(data) {
        log(`🌐 ${data.message}`, 'success');
    }

    handleSessionCompleted(session) {
        this.updateSessionStatus(session, 'Completed');
        this.updateControlButtons(false);
        
        const duration = session.endTime - session.startTime;
        log(`🎉 Backup completed in ${Math.round(duration / 1000)} seconds`, 'success');
        
        // Show completion summary
        setTimeout(() => {
            this.showCompletionSummary(session);
        }, 1000);
    }

    handleSessionCancelled(session) {
        this.updateSessionStatus(session, 'Cancelled');
        this.updateControlButtons(false);
        log('🛑 Backup session was cancelled', 'info');
    }

    showCompletionSummary(session) {
        const summary = `
            <div class="completion-summary">
                <h4>🎉 Backup Completed Successfully!</h4>
                <div class="summary-stats">
                    <div>Total Items: ${session.overall.completed}</div>
                    <div>Data Transferred: ${this.formatBytes(session.performance.bytesTransferred)}</div>
                    <div>Duration: ${Math.round((session.endTime - session.startTime) / 1000)}s</div>
                    <div>Average Speed: ${this.formatBytes(session.performance.averageSpeed)}/s</div>
                    <div>Errors: ${session.performance.errorCount}</div>
                </div>
                <button class="btn" onclick="backupConsole.resetConsole()">Start New Backup</button>
            </div>
        `;
        
        document.getElementById('console-body').innerHTML += summary;
    }

    pauseBackup() {
        if (this.activeSessionId && this.progressManager) {
            this.progressManager.pauseBackupSession(this.activeSessionId);
            document.getElementById('pause-btn').disabled = true;
            document.getElementById('resume-btn').disabled = false;
        }
    }

    resumeBackup() {
        if (this.activeSessionId && this.progressManager) {
            this.progressManager.resumeBackupSession(this.activeSessionId);
            document.getElementById('pause-btn').disabled = false;
            document.getElementById('resume-btn').disabled = true;
        }
    }

    cancelBackup() {
        if (this.activeSessionId && this.progressManager) {
            this.progressManager.cancelBackupSession(this.activeSessionId);
        }
    }

    resetConsole() {
        this.activeSessionId = null;
        this.initializeConsole();
    }

    // Start multiple concurrent backup sessions
    async startConcurrentBackups() {
        if (!this.progressManager) {
            log('❌ Progress manager not initialized', 'error');
            return;
        }

        log('🚀 Starting multiple concurrent backup sessions...', 'info');

        // Create 3 different backup sessions with different configurations
        const sessions = [
            {
                id: `concurrent-contacts-${Date.now()}`,
                config: {
                    type: 'manual',
                    includeContacts: true,
                    includeMessages: false,
                    includePhotos: false,
                    simulateErrors: false
                }
            },
            {
                id: `concurrent-messages-${Date.now() + 1}`,
                config: {
                    type: 'manual',
                    includeContacts: false,
                    includeMessages: true,
                    includePhotos: false,
                    simulateErrors: true
                }
            },
            {
                id: `concurrent-photos-${Date.now() + 2}`,
                config: {
                    type: 'manual',
                    includeContacts: false,
                    includeMessages: false,
                    includePhotos: true,
                    simulateErrors: false
                }
            }
        ];

        // Start sessions (some may be queued if concurrent limit is reached)
        for (const sessionConfig of sessions) {
            const session = this.progressManager.queueSession(sessionConfig.id, sessionConfig.config);
            
            if (session) {
                // Set up progress listener
                this.progressManager.addProgressListener(sessionConfig.id, (event, data) => {
                    this.handleConcurrentSessionEvent(sessionConfig.id, event, data);
                });
                
                // Start the session
                setTimeout(() => {
                    this.progressManager.startBackupSession(sessionConfig.id);
                }, Math.random() * 1000); // Stagger starts
            }
        }

        // Update UI to show concurrent sessions
        this.renderConcurrentSessions();
    }

    // Handle events from concurrent sessions
    handleConcurrentSessionEvent(sessionId, event, data) {
        // Update session-specific UI elements
        this.updateConcurrentSessionDisplay(sessionId, event, data);
        
        // Update global statistics
        this.updateGlobalSessionStats();
    }

    // Render UI for concurrent sessions
    renderConcurrentSessions() {
        const consoleBody = document.getElementById('console-body');
        consoleBody.innerHTML = `
            <div class="concurrent-sessions-overview">
                <h4>🔄 Concurrent Backup Sessions</h4>
                <div class="global-stats">
                    <div class="metrics-grid">
                        <div class="metric">
                            <div class="metric-label">Active Sessions</div>
                            <div class="metric-value" id="global-active-sessions">0</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Queued Sessions</div>
                            <div class="metric-value" id="global-queued-sessions">0</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Total Data</div>
                            <div class="metric-value" id="global-data-transferred">0 B</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Combined Speed</div>
                            <div class="metric-value" id="global-transfer-speed">0 B/s</div>
                        </div>
                    </div>
                </div>
                <div id="concurrent-sessions-list" class="concurrent-sessions-list">
                    <div class="session-placeholder">
                        <p>Starting concurrent backup sessions...</p>
                    </div>
                </div>
            </div>
        `;
    }

    // Update concurrent session display
    updateConcurrentSessionDisplay(sessionId, event, data) {
        const sessionsList = document.getElementById('concurrent-sessions-list');
        if (!sessionsList) return;

        let sessionElement = document.getElementById(`session-${sessionId}`);
        
        if (!sessionElement && (event === 'session_started' || event === 'session_created')) {
            // Create new session element
            sessionElement = document.createElement('div');
            sessionElement.id = `session-${sessionId}`;
            sessionElement.className = 'concurrent-session-item';
            sessionElement.innerHTML = `
                <div class="session-header">
                    <h5>${sessionId}</h5>
                    <div class="session-status" id="status-${sessionId}">Starting...</div>
                </div>
                <div class="session-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-${sessionId}"></div>
                    </div>
                    <div class="session-details">
                        <span id="details-${sessionId}">Initializing...</span>
                        <span id="speed-${sessionId}">0 B/s</span>
                    </div>
                </div>
            `;
            
            // Replace placeholder or append
            const placeholder = sessionsList.querySelector('.session-placeholder');
            if (placeholder) {
                sessionsList.removeChild(placeholder);
            }
            sessionsList.appendChild(sessionElement);
        }

        if (sessionElement) {
            // Update session based on event
            switch (event) {
                case 'progress_updated':
                    this.updateElement(`progress-${sessionId}`, '', data.overall.percentage);
                    this.updateProgressBar(`progress-${sessionId}`, data.overall.percentage);
                    this.updateElement(`details-${sessionId}`, `${data.overall.completed}/${data.overall.total} items`);
                    this.updateElement(`speed-${sessionId}`, `${this.formatBytes(data.overall.transferRate)}/s`);
                    break;
                case 'session_completed':
                    this.updateElement(`status-${sessionId}`, 'Completed');
                    document.getElementById(`status-${sessionId}`).className = 'session-status completed';
                    break;
                case 'session_paused':
                    this.updateElement(`status-${sessionId}`, 'Paused');
                    document.getElementById(`status-${sessionId}`).className = 'session-status paused';
                    break;
            }
        }
    }

    // Update global session statistics
    updateGlobalSessionStats() {
        if (!this.progressManager) return;

        const globalStats = this.progressManager.getGlobalStats();
        const activeSessions = this.progressManager.getActiveSessions();
        
        // Update header stats
        this.updateElement('active-sessions-count', globalStats.activeSessions.toString());
        this.updateElement('session-queue-count', this.progressManager.sessionQueue.length.toString());
        
        // Update global metrics if in concurrent view
        this.updateElement('global-active-sessions', globalStats.activeSessions.toString());
        this.updateElement('global-queued-sessions', this.progressManager.sessionQueue.length.toString());
        this.updateElement('global-data-transferred', this.formatBytes(globalStats.totalBytesTransferred));
        
        // Calculate combined transfer speed
        const combinedSpeed = activeSessions.reduce((total, session) => {
            return total + (session.overall.transferRate || 0);
        }, 0);
        this.updateElement('global-transfer-speed', `${this.formatBytes(combinedSpeed)}/s`);
    }

    updateControlButtons(active) {
        document.getElementById('pause-btn').disabled = !active;
        document.getElementById('resume-btn').disabled = true;
        document.getElementById('cancel-btn').disabled = !active;
    }

    updateElement(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    updateProgressBar(id, percentage) {
        const element = document.getElementById(id);
        if (element) {
            element.style.width = `${Math.max(0, Math.min(100, percentage))}%`;
        }
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatTime(ms) {
        if (!ms || ms <= 0) return 'Calculating...';
        
        const seconds = Math.floor(ms / 1000);
        if (seconds < 60) return `${seconds}s`;
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}m ${remainingSeconds}s`;
    }
}

// Export for use in main app
window.BackupConsole = BackupConsole;