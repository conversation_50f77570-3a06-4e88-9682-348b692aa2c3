import { Platform, Alert, Linking } from 'react-native';
import { 
  PERMISSIONS, 
  RESULTS, 
  request, 
  check, 
  openSettings,
  Permission 
} from 'react-native-permissions';

export interface PermissionStatus {
  granted: boolean;
  denied: boolean;
  blocked: boolean;
  unavailable: boolean;
}

export interface PermissionExplanation {
  title: string;
  message: string;
  icon: string;
  benefits: string[];
}

class PermissionService {
  // Permission mappings for different platforms
  private getPermissions() {
    return {
      photos: Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.PHOTO_LIBRARY 
        : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
      contacts: Platform.OS === 'ios'
        ? PERMISSIONS.IOS.CONTACTS
        : PERMISSIONS.ANDROID.READ_CONTACTS,
      sms: Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.READ_SMS
        : null, // iOS doesn't allow SMS access
      camera: Platform.OS === 'ios'
        ? PERMISSIONS.IOS.CAMERA
        : PERMISSIONS.ANDROID.CAMERA,
    };
  }

  // Grandparent-friendly explanations for each permission
  private getPermissionExplanations(): Record<string, PermissionExplanation> {
    return {
      photos: {
        title: "Access Your Photos",
        message: "SafeKeep needs to see your photos so we can back them up safely to the cloud. This keeps your precious memories safe forever!",
        icon: "📸",
        benefits: [
          "Backup all your family photos automatically",
          "Never lose precious memories again", 
          "Access photos from any device",
          "Share photos easily with family"
        ]
      },
      contacts: {
        title: "Access Your Contacts",
        message: "SafeKeep needs to see your contacts so we can back them up. This way you'll never lose important phone numbers!",
        icon: "📞",
        benefits: [
          "Backup all your important phone numbers",
          "Never lose contact information",
          "Restore contacts if you get a new phone",
          "Keep family and friends' numbers safe"
        ]
      },
      sms: {
        title: "Access Your Text Messages",
        message: "SafeKeep can backup your text messages to save important conversations with family and friends.",
        icon: "💬",
        benefits: [
          "Save important family conversations",
          "Backup messages from loved ones",
          "Never lose precious text memories",
          "Keep important information safe"
        ]
      },
      camera: {
        title: "Access Your Camera",
        message: "SafeKeep needs camera access so you can take new photos that will be automatically backed up.",
        icon: "📷",
        benefits: [
          "Take photos that backup automatically",
          "Never worry about losing new pictures",
          "Instant backup of new memories",
          "Peace of mind for new photos"
        ]
      }
    };
  }

  // Check current permission status
  async checkPermission(permissionType: keyof ReturnType<typeof this.getPermissions>): Promise<PermissionStatus> {
    const permissions = this.getPermissions();
    const permission = permissions[permissionType];
    
    if (!permission) {
      return { granted: false, denied: false, blocked: false, unavailable: true };
    }

    try {
      const result = await check(permission);
      
      return {
        granted: result === RESULTS.GRANTED,
        denied: result === RESULTS.DENIED,
        blocked: result === RESULTS.BLOCKED,
        unavailable: result === RESULTS.UNAVAILABLE
      };
    } catch (error) {
      console.error(`Error checking ${permissionType} permission:`, error);
      return { granted: false, denied: true, blocked: false, unavailable: false };
    }
  }

  // Request permission with grandparent-friendly explanation
  async requestPermission(permissionType: keyof ReturnType<typeof this.getPermissions>): Promise<PermissionStatus> {
    const permissions = this.getPermissions();
    const permission = permissions[permissionType];
    const explanation = this.getPermissionExplanations()[permissionType];
    
    if (!permission) {
      return { granted: false, denied: false, blocked: false, unavailable: true };
    }

    // First, show our friendly explanation
    const userWantsToGrant = await this.showPermissionExplanation(explanation);
    
    if (!userWantsToGrant) {
      return { granted: false, denied: true, blocked: false, unavailable: false };
    }

    try {
      const result = await request(permission);
      
      const status = {
        granted: result === RESULTS.GRANTED,
        denied: result === RESULTS.DENIED,
        blocked: result === RESULTS.BLOCKED,
        unavailable: result === RESULTS.UNAVAILABLE
      };

      // Handle blocked permissions with helpful guidance
      if (status.blocked) {
        await this.showBlockedPermissionHelp(explanation);
      }

      return status;
    } catch (error) {
      console.error(`Error requesting ${permissionType} permission:`, error);
      return { granted: false, denied: true, blocked: false, unavailable: false };
    }
  }

  // Show grandparent-friendly permission explanation
  private showPermissionExplanation(explanation: PermissionExplanation): Promise<boolean> {
    return new Promise((resolve) => {
      const benefitsList = explanation.benefits.map((benefit, index) => `${index + 1}. ${benefit}`).join('\n');
      
      Alert.alert(
        `${explanation.icon} ${explanation.title}`,
        `${explanation.message}\n\nHere's what this helps you do:\n${benefitsList}`,
        [
          {
            text: "Not Now",
            style: "cancel",
            onPress: () => resolve(false)
          },
          {
            text: "Allow Access",
            style: "default",
            onPress: () => resolve(true)
          }
        ],
        { cancelable: false }
      );
    });
  }

  // Help users with blocked permissions
  private showBlockedPermissionHelp(explanation: PermissionExplanation): Promise<void> {
    return new Promise((resolve) => {
      Alert.alert(
        "Permission Needed",
        `To use ${explanation.title.toLowerCase()}, please:\n\n1. Tap "Open Settings" below\n2. Find "SafeKeep" in the list\n3. Turn ON the permission\n4. Come back to SafeKeep\n\nDon't worry - we'll guide you through it!`,
        [
          {
            text: "Maybe Later",
            style: "cancel",
            onPress: () => resolve()
          },
          {
            text: "Open Settings",
            style: "default",
            onPress: () => {
              openSettings().catch(() => {
                // Fallback to general settings if app settings fail
                Linking.openSettings();
              });
              resolve();
            }
          }
        ]
      );
    });
  }

  // Check all required permissions at once
  async checkAllPermissions(): Promise<Record<string, PermissionStatus>> {
    const permissionTypes = ['photos', 'contacts', 'sms'] as const;
    const results: Record<string, PermissionStatus> = {};

    for (const type of permissionTypes) {
      results[type] = await this.checkPermission(type);
    }

    return results;
  }

  // Request all required permissions with proper flow
  async requestAllPermissions(): Promise<Record<string, PermissionStatus>> {
    const permissionTypes = ['photos', 'contacts', 'sms'] as const;
    const results: Record<string, PermissionStatus> = {};

    // Request permissions one by one to avoid overwhelming users
    for (const type of permissionTypes) {
      // Skip SMS on iOS since it's not available
      if (type === 'sms' && Platform.OS === 'ios') {
        results[type] = { granted: false, denied: false, blocked: false, unavailable: true };
        continue;
      }

      results[type] = await this.requestPermission(type);
      
      // Small delay between requests to make it less overwhelming
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return results;
  }

  // Enhanced permission request flow with retry and recovery
  async requestPermissionWithRecovery(
    permissionType: keyof ReturnType<typeof this.getPermissions>,
    onSuccess?: () => Promise<void>,
    onFailure?: (reason: 'denied' | 'blocked' | 'unavailable') => Promise<void>,
    maxRetries: number = 2
  ): Promise<PermissionStatus> {
    console.log(`🔐 Requesting ${permissionType} permission with recovery flow`);

    let attempts = 0;
    let lastStatus: PermissionStatus;

    while (attempts < maxRetries) {
      attempts++;
      console.log(`🔐 Permission request attempt ${attempts}/${maxRetries} for ${permissionType}`);

      // Check current status first
      const currentStatus = await this.checkPermission(permissionType);
      
      if (currentStatus.granted) {
        console.log(`✅ ${permissionType} permission already granted`);
        if (onSuccess) {
          await onSuccess();
        }
        return currentStatus;
      }

      // If blocked, guide user to settings
      if (currentStatus.blocked) {
        console.log(`🚫 ${permissionType} permission is blocked, guiding to settings`);
        const unblocked = await this.handleBlockedPermission(permissionType);
        
        if (unblocked) {
          // Check again after user returns from settings
          const newStatus = await this.checkPermission(permissionType);
          if (newStatus.granted) {
            if (onSuccess) {
              await onSuccess();
            }
            return newStatus;
          }
        }
        
        lastStatus = currentStatus;
        break; // Don't retry blocked permissions
      }

      // If unavailable, handle gracefully
      if (currentStatus.unavailable) {
        console.log(`❌ ${permissionType} permission is unavailable on this platform`);
        if (onFailure) {
          await onFailure('unavailable');
        }
        return currentStatus;
      }

      // Request permission with explanation
      const requestResult = await this.requestPermission(permissionType);
      lastStatus = requestResult;

      if (requestResult.granted) {
        console.log(`✅ ${permissionType} permission granted on attempt ${attempts}`);
        if (onSuccess) {
          await onSuccess();
        }
        return requestResult;
      }

      // If denied, show encouragement for retry (except on last attempt)
      if (requestResult.denied && attempts < maxRetries) {
        const shouldRetry = await this.showRetryEncouragement(permissionType);
        if (!shouldRetry) {
          break;
        }
      }
    }

    // All attempts failed
    console.log(`❌ ${permissionType} permission request failed after ${attempts} attempts`);
    
    if (onFailure) {
      const reason = lastStatus!.blocked ? 'blocked' : lastStatus!.unavailable ? 'unavailable' : 'denied';
      await onFailure(reason);
    }

    return lastStatus!;
  }

  // Handle blocked permission by guiding user to settings
  private async handleBlockedPermission(permissionType: keyof ReturnType<typeof this.getPermissions>): Promise<boolean> {
    const explanation = this.getPermissionExplanations()[permissionType];
    
    return new Promise((resolve) => {
      Alert.alert(
        "Permission Settings",
        `To enable ${explanation.title.toLowerCase()}, please:\n\n1. Tap "Open Settings" below\n2. Find "SafeKeep" in the app list\n3. Tap on "SafeKeep"\n4. Turn ON the ${explanation.title.toLowerCase()} permission\n5. Return to SafeKeep\n\nThis will help you ${explanation.benefits[0].toLowerCase()}.`,
        [
          {
            text: "Skip for Now",
            style: "cancel",
            onPress: () => resolve(false)
          },
          {
            text: "Open Settings",
            style: "default",
            onPress: () => {
              openSettings().catch(() => {
                Linking.openSettings();
              });
              
              // Give user time to change settings
              setTimeout(() => {
                resolve(true);
              }, 3000);
            }
          }
        ]
      );
    });
  }

  // Show encouragement for permission retry
  private async showRetryEncouragement(permissionType: keyof ReturnType<typeof this.getPermissions>): Promise<boolean> {
    const explanation = this.getPermissionExplanations()[permissionType];
    
    return new Promise((resolve) => {
      Alert.alert(
        "Permission Needed",
        `${explanation.icon} We really need access to ${explanation.title.toLowerCase()} to help you backup your data safely.\n\nWithout this permission, we can't:\n${explanation.benefits.map(b => `• ${b}`).join('\n')}\n\nWould you like to try again?`,
        [
          {
            text: "Skip This",
            style: "cancel",
            onPress: () => resolve(false)
          },
          {
            text: "Try Again",
            style: "default",
            onPress: () => resolve(true)
          }
        ]
      );
    });
  }

  // Request permissions for specific backup configuration
  async requestPermissionsForBackup(configuration: {
    includeContacts: boolean;
    includeMessages: boolean;
    includePhotos: boolean;
  }): Promise<{
    allGranted: boolean;
    grantedPermissions: string[];
    deniedPermissions: string[];
    results: Record<string, PermissionStatus>;
  }> {
    console.log('🔐 Requesting permissions for backup configuration:', configuration);

    const results: Record<string, PermissionStatus> = {};
    const grantedPermissions: string[] = [];
    const deniedPermissions: string[] = [];

    // Request contacts permission
    if (configuration.includeContacts) {
      const contactsResult = await this.requestPermissionWithRecovery(
        'contacts',
        async () => {
          console.log('✅ Contacts permission granted - backup enabled');
        },
        async (reason) => {
          console.log(`❌ Contacts permission ${reason} - backup disabled`);
        }
      );
      
      results.contacts = contactsResult;
      
      if (contactsResult.granted) {
        grantedPermissions.push('contacts');
      } else {
        deniedPermissions.push('contacts');
      }
    }

    // Request messages permission (Android only)
    if (configuration.includeMessages) {
      if (Platform.OS === 'android') {
        const messagesResult = await this.requestPermissionWithRecovery(
          'sms',
          async () => {
            console.log('✅ Messages permission granted - backup enabled');
          },
          async (reason) => {
            console.log(`❌ Messages permission ${reason} - backup disabled`);
          }
        );
        
        results.sms = messagesResult;
        
        if (messagesResult.granted) {
          grantedPermissions.push('messages');
        } else {
          deniedPermissions.push('messages');
        }
      } else {
        // iOS doesn't support SMS access
        results.sms = { granted: false, denied: false, blocked: false, unavailable: true };
        deniedPermissions.push('messages');
      }
    }

    // Request photos permission
    if (configuration.includePhotos) {
      const photosResult = await this.requestPermissionWithRecovery(
        'photos',
        async () => {
          console.log('✅ Photos permission granted - backup enabled');
        },
        async (reason) => {
          console.log(`❌ Photos permission ${reason} - backup disabled`);
        }
      );
      
      results.photos = photosResult;
      
      if (photosResult.granted) {
        grantedPermissions.push('photos');
      } else {
        deniedPermissions.push('photos');
      }
    }

    const allGranted = deniedPermissions.length === 0;
    
    console.log(`🔐 Permission request completed: ${grantedPermissions.length} granted, ${deniedPermissions.length} denied`);

    return {
      allGranted,
      grantedPermissions,
      deniedPermissions,
      results
    };
  }

  // Show permission summary after backup configuration
  async showPermissionSummary(
    grantedPermissions: string[],
    deniedPermissions: string[],
    onContinue?: () => Promise<void>,
    onRetry?: () => Promise<void>
  ): Promise<void> {
    if (deniedPermissions.length === 0) {
      // All permissions granted
      Alert.alert(
        "🎉 All Set!",
        `Great! You've granted all the permissions we need:\n\n${grantedPermissions.map(p => `✅ ${p.charAt(0).toUpperCase() + p.slice(1)}`).join('\n')}\n\nYour backup will include all your selected data types.`,
        [
          {
            text: "Continue",
            style: "default",
            onPress: onContinue
          }
        ]
      );
    } else if (grantedPermissions.length > 0) {
      // Partial permissions
      Alert.alert(
        "⚠️ Partial Permissions",
        `We have some permissions, but not all:\n\n✅ Granted:\n${grantedPermissions.map(p => `• ${p.charAt(0).toUpperCase() + p.slice(1)}`).join('\n')}\n\n❌ Missing:\n${deniedPermissions.map(p => `• ${p.charAt(0).toUpperCase() + p.slice(1)}`).join('\n')}\n\nYou can continue with partial backup or try again for full backup.`,
        [
          {
            text: "Continue Partial",
            style: "default",
            onPress: onContinue
          },
          {
            text: "Try Again",
            style: "cancel",
            onPress: onRetry
          }
        ]
      );
    } else {
      // No permissions granted
      Alert.alert(
        "❌ No Permissions",
        `We couldn't get any permissions for backup:\n\n${deniedPermissions.map(p => `• ${p.charAt(0).toUpperCase() + p.slice(1)}`).join('\n')}\n\nWithout permissions, we can't backup your data. Would you like to try again?`,
        [
          {
            text: "Cancel",
            style: "cancel"
          },
          {
            text: "Try Again",
            style: "default",
            onPress: onRetry
          }
        ]
      );
    }
  }

  // Get summary of permission status for UI
  getPermissionSummary(permissions: Record<string, PermissionStatus>): {
    allGranted: boolean;
    grantedCount: number;
    totalCount: number;
    missingPermissions: string[];
  } {
    const entries = Object.entries(permissions);
    const availableEntries = entries.filter(([_, status]) => !status.unavailable);
    const grantedEntries = availableEntries.filter(([_, status]) => status.granted);
    
    return {
      allGranted: grantedEntries.length === availableEntries.length,
      grantedCount: grantedEntries.length,
      totalCount: availableEntries.length,
      missingPermissions: availableEntries
        .filter(([_, status]) => !status.granted)
        .map(([type]) => type)
    };
  }
}

export default new PermissionService();
