// File Metadata Indexer for SafeKeep
// Provides functions for indexing and organizing file metadata

import { supabase } from '../config/supabase';
import { getCurrentUserId } from './supabaseHelpers';
import { FileMetadata, getFileMetadata } from './fileMetadataManager';
import { generateFileHash } from './fileDeduplication';

export interface IndexingProgress {
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  status: 'idle' | 'running' | 'completed' | 'failed';
  error?: string;
}

/**
 * Index files in a storage bucket to create or update metadata
 */
export const indexStorageBucket = async (
  bucketName: string,
  folderPath?: string,
  progressCallback?: (progress: IndexingProgress) => void
): Promise<{ 
  success: boolean; 
  indexed: number;
  updated: number;
  failed: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, indexed: 0, updated: 0, failed: 0, error: 'User not authenticated' };
    }

    // Initialize progress
    const progress: IndexingProgress = {
      totalFiles: 0,
      processedFiles: 0,
      failedFiles: 0,
      status: 'running'
    };

    // List files in the bucket/folder
    const path = folderPath ? `${userId}/${folderPath}` : userId;
    const { data: files, error: listError } = await supabase.storage
      .from(bucketName)
      .list(path);

    if (listError) {
      progress.status = 'failed';
      progress.error = listError.message;
      if (progressCallback) progressCallback(progress);
      return { success: false, indexed: 0, updated: 0, failed: 0, error: listError.message };
    }

    // Filter out folders
    const fileObjects = files?.filter(item => !item.metadata) || [];
    progress.totalFiles = fileObjects.length;
    if (progressCallback) progressCallback(progress);

    // Process files in batches
    const batchSize = 5;
    let indexed = 0;
    let updated = 0;
    let failed = 0;

    for (let i = 0; i < fileObjects.length; i += batchSize) {
      const batch = fileObjects.slice(i, i + batchSize);
      
      // Process each file in the batch
      const batchPromises = batch.map(async (file) => {
        try {
          // Get file path
          const filePath = folderPath ? `${path}/${file.name}` : `${userId}/${file.name}`;
          
          // Check if metadata already exists for this path
          const { data: existingFiles, error: queryError } = await supabase
            .from('file_metadata')
            .select('*')
            .eq('user_id', userId)
            .eq('storage_path', filePath);
            
          if (queryError) {
            progress.failedFiles++;
            failed++;
            return;
          }
          
          if (existingFiles && existingFiles.length > 0) {
            // Metadata exists, update if needed
            const existingFile = existingFiles[0];
            
            // Check if size matches
            if (existingFile.encrypted_size !== file.metadata?.size) {
              // Update metadata
              const { error: updateError } = await supabase
                .from('file_metadata')
                .update({
                  encrypted_size: file.metadata?.size,
                  last_modified: new Date().toISOString()
                })
                .eq('id', existingFile.id);
                
              if (updateError) {
                progress.failedFiles++;
                failed++;
              } else {
                updated++;
              }
            }
          } else {
            // No metadata exists, create new entry
            
            // Download file to generate hash (only small files)
            if (file.metadata?.size && file.metadata.size < 5 * 1024 * 1024) { // 5MB limit
              const { data: fileData, error: downloadError } = await supabase.storage
                .from(bucketName)
                .download(filePath);
                
              if (downloadError || !fileData) {
                progress.failedFiles++;
                failed++;
                return;
              }
              
              // Generate hash
              const hash = await generateFileHash(await fileData.arrayBuffer());
              
              // Determine category from path or mime type
              let category: 'photo' | 'contact' | 'message' = 'photo'; // Default
              const mimeType = file.metadata?.mimetype || '';
              
              if (mimeType.startsWith('image/')) {
                category = 'photo';
              } else if (mimeType.includes('vcard') || filePath.includes('/contacts/')) {
                category = 'contact';
              } else if (mimeType.includes('message') || filePath.includes('/messages/')) {
                category = 'message';
              }
              
              // Create metadata
              const { error: insertError } = await supabase
                .from('file_metadata')
                .insert({
                  user_id: userId,
                  original_name: file.name,
                  encrypted_name: file.name,
                  mime_type: file.metadata?.mimetype || 'application/octet-stream',
                  size: file.metadata?.size || 0,
                  encrypted_size: file.metadata?.size || 0,
                  category,
                  hash,
                  encryption_iv: 'unknown', // Can't determine from storage
                  encryption_salt: 'unknown', // Can't determine from storage
                  storage_path: filePath,
                  is_backed_up: true,
                  uploaded_at: file.created_at || new Date().toISOString(),
                  last_modified: file.updated_at || new Date().toISOString(),
                  sync_status: 'synced'
                });
                
              if (insertError) {
                progress.failedFiles++;
                failed++;
              } else {
                indexed++;
              }
            } else {
              // File too large to download for hashing
              // Create metadata with placeholder hash
              const placeholderHash = `size-${file.metadata?.size}-name-${file.name}`;
              
              // Determine category from path or mime type
              let category: 'photo' | 'contact' | 'message' = 'photo'; // Default
              const mimeType = file.metadata?.mimetype || '';
              
              if (mimeType.startsWith('image/')) {
                category = 'photo';
              } else if (mimeType.includes('vcard') || filePath.includes('/contacts/')) {
                category = 'contact';
              } else if (mimeType.includes('message') || filePath.includes('/messages/')) {
                category = 'message';
              }
              
              // Create metadata
              const { error: insertError } = await supabase
                .from('file_metadata')
                .insert({
                  user_id: userId,
                  original_name: file.name,
                  encrypted_name: file.name,
                  mime_type: file.metadata?.mimetype || 'application/octet-stream',
                  size: file.metadata?.size || 0,
                  encrypted_size: file.metadata?.size || 0,
                  category,
                  hash: placeholderHash,
                  encryption_iv: 'unknown', // Can't determine from storage
                  encryption_salt: 'unknown', // Can't determine from storage
                  storage_path: filePath,
                  is_backed_up: true,
                  uploaded_at: file.created_at || new Date().toISOString(),
                  last_modified: file.updated_at || new Date().toISOString(),
                  sync_status: 'synced'
                });
                
              if (insertError) {
                progress.failedFiles++;
                failed++;
              } else {
                indexed++;
              }
            }
          }
        } catch (error) {
          console.error('Error processing file:', error);
          progress.failedFiles++;
          failed++;
        }
        
        // Update progress
        progress.processedFiles++;
        if (progressCallback) progressCallback(progress);
      });
      
      await Promise.all(batchPromises);
    }
    
    // Complete progress
    progress.status = 'completed';
    if (progressCallback) progressCallback(progress);
    
    return { 
      success: true, 
      indexed, 
      updated, 
      failed, 
      error: null 
    };
  } catch (error) {
    console.error('Error indexing storage bucket:', error);
    return { 
      success: false, 
      indexed: 0, 
      updated: 0, 
      failed: 0, 
      error: (error as Error).message 
    };
  }
};

/**
 * Sync file metadata with storage
 * Removes metadata for files that no longer exist in storage
 */
export const syncFileMetadataWithStorage = async (
  bucketName: string,
  progressCallback?: (progress: IndexingProgress) => void
): Promise<{ 
  success: boolean; 
  removed: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, removed: 0, error: 'User not authenticated' };
    }

    // Initialize progress
    const progress: IndexingProgress = {
      totalFiles: 0,
      processedFiles: 0,
      failedFiles: 0,
      status: 'running'
    };

    // Get all file metadata
    const { data: metadata, error: queryError } = await supabase
      .from('file_metadata')
      .select('id, storage_path')
      .eq('user_id', userId);
      
    if (queryError) {
      progress.status = 'failed';
      progress.error = queryError.message;
      if (progressCallback) progressCallback(progress);
      return { success: false, removed: 0, error: queryError.message };
    }
    
    progress.totalFiles = metadata?.length || 0;
    if (progressCallback) progressCallback(progress);
    
    // Process files in batches
    const batchSize = 10;
    let removed = 0;
    
    for (let i = 0; i < (metadata?.length || 0); i += batchSize) {
      const batch = metadata?.slice(i, i + batchSize) || [];
      
      // Process each file in the batch
      const batchPromises = batch.map(async (file) => {
        try {
          // Check if file exists in storage
          const { data, error } = await supabase.storage
            .from(bucketName)
            .download(file.storage_path);
            
          if (error) {
            // File doesn't exist in storage, remove metadata
            const { error: deleteError } = await supabase
              .from('file_metadata')
              .delete()
              .eq('id', file.id);
              
            if (deleteError) {
              progress.failedFiles++;
            } else {
              removed++;
            }
          }
        } catch (error) {
          console.error('Error checking file existence:', error);
          progress.failedFiles++;
        }
        
        // Update progress
        progress.processedFiles++;
        if (progressCallback) progressCallback(progress);
      });
      
      await Promise.all(batchPromises);
    }
    
    // Complete progress
    progress.status = 'completed';
    if (progressCallback) progressCallback(progress);
    
    return { 
      success: true, 
      removed, 
      error: null 
    };
  } catch (error) {
    console.error('Error syncing file metadata with storage:', error);
    return { 
      success: false, 
      removed: 0, 
      error: (error as Error).message 
    };
  }
};

/**
 * Rebuild file metadata from storage
 * This is a destructive operation that removes all existing metadata
 * and rebuilds it from storage
 */
export const rebuildFileMetadataFromStorage = async (
  bucketName: string,
  progressCallback?: (progress: IndexingProgress) => void
): Promise<{ 
  success: boolean; 
  indexed: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, indexed: 0, error: 'User not authenticated' };
    }

    // Initialize progress
    const progress: IndexingProgress = {
      totalFiles: 0,
      processedFiles: 0,
      failedFiles: 0,
      status: 'running'
    };

    // Delete all existing metadata
    const { error: deleteError } = await supabase
      .from('file_metadata')
      .delete()
      .eq('user_id', userId);
      
    if (deleteError) {
      progress.status = 'failed';
      progress.error = deleteError.message;
      if (progressCallback) progressCallback(progress);
      return { success: false, indexed: 0, error: deleteError.message };
    }
    
    // Index storage bucket
    const { indexed, error } = await indexStorageBucket(bucketName, undefined, progressCallback);
    
    return { 
      success: !error, 
      indexed, 
      error 
    };
  } catch (error) {
    console.error('Error rebuilding file metadata:', error);
    return { 
      success: false, 
      indexed: 0, 
      error: (error as Error).message 
    };
  }
};