// Backup tracking types for Supabase database schema

export interface BackupSession {
  id: string;
  user_id: string;
  start_time: string; // ISO timestamp
  end_time?: string; // ISO timestamp
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  total_items: number;
  completed_items: number;
  configuration: BackupConfiguration;
  error_message?: string;
  created_at: string; // ISO timestamp
  updated_at: string; // ISO timestamp
}

export interface BackupItem {
  id: string;
  session_id: string;
  item_type: 'contact' | 'message' | 'photo';
  original_id: string;
  encrypted_data: Uint8Array; // BYTEA in PostgreSQL
  metadata: Record<string, any>;
  file_path?: string;
  checksum?: string;
  size_bytes?: number;
  status: 'pending' | 'completed' | 'failed';
  error_message?: string;
  created_at: string; // ISO timestamp
  updated_at: string; // ISO timestamp
}

export interface BackupProgress {
  session_id: string;
  item_type: 'contact' | 'message' | 'photo';
  total_count: number;
  completed_count: number;
  failed_count: number;
  last_updated: string; // ISO timestamp
}

export interface BackupConfiguration {
  autoBackup: boolean;
  wifiOnly: boolean;
  includeContacts: boolean;
  includeMessages: boolean;
  includePhotos: boolean;
  compressionLevel: 'none' | 'low' | 'medium' | 'high';
}

export interface BackupSessionSummary {
  session_id: string;
  status: string;
  start_time: string;
  end_time?: string;
  total_items: number;
  completed_items: number;
  contacts_total: number;
  contacts_completed: number;
  contacts_failed: number;
  messages_total: number;
  messages_completed: number;
  messages_failed: number;
  photos_total: number;
  photos_completed: number;
  photos_failed: number;
}

// Database function parameter types
export interface InitializeBackupProgressParams {
  session_id: string;
  contacts_total?: number;
  messages_total?: number;
  photos_total?: number;
}

export interface UpdateBackupProgressParams {
  session_id: string;
  item_type: 'contact' | 'message' | 'photo';
  completed_increment?: number;
  failed_increment?: number;
}

// Error types for backup operations
export interface BackupError {
  id: string;
  type: 'permission' | 'network' | 'storage' | 'encryption' | 'platform';
  message: string;
  timestamp: Date;
  retryable: boolean;
  retryCount?: number;
  maxRetries?: number;
  item_id?: string;
  item_type?: 'contact' | 'message' | 'photo';
  context?: Record<string, any>;
}

// Enhanced error handling types
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
  jitter: boolean;
}

export interface ErrorRecoveryAction {
  id: string;
  label: string;
  action: () => Promise<void>;
  primary?: boolean;
}

export interface UserNotification {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  persistent?: boolean;
  actions?: ErrorRecoveryAction[];
  context?: Record<string, any>;
}

export interface BackupRecoveryState {
  sessionId: string;
  resumable: boolean;
  lastCheckpoint: {
    dataType: 'contacts' | 'messages' | 'photos';
    itemIndex: number;
    timestamp: Date;
  };
  failedItems: BackupError[];
  retryableErrors: BackupError[];
}

// Backup statistics for UI display
export interface BackupStatistics {
  totalSessions: number;
  successfulSessions: number;
  failedSessions: number;
  totalItemsBackedUp: number;
  lastBackupTime?: string;
  averageBackupDuration?: number; // in milliseconds
}

// Real-time backup status for UI updates
export interface BackupStatus {
  isActive: boolean;
  currentSession?: BackupSession;
  progress: {
    contacts: BackupProgress;
    messages: BackupProgress;
    photos: BackupProgress;
  };
  overallProgress: {
    percentage: number;
    itemsCompleted: number;
    totalItems: number;
    estimatedTimeRemaining?: number; // in milliseconds
  };
}

// Supabase table names as constants
export const BACKUP_TABLES = {
  SESSIONS: 'backup_sessions',
  ITEMS: 'backup_items',
  PROGRESS: 'backup_progress',
} as const;

// Supabase function names as constants
export const BACKUP_FUNCTIONS = {
  INITIALIZE_PROGRESS: 'initialize_backup_progress',
  UPDATE_PROGRESS: 'update_backup_progress',
  GET_SESSION_SUMMARY: 'get_backup_session_summary',
  CLEANUP_OLD_SESSIONS: 'cleanup_old_backup_sessions',
} as const;

// Service result types
export interface BackupServiceResult {
  success: boolean;
  itemsProcessed: number;
  errors: BackupError[];
  data?: Record<string, any>;
}

// Manager configuration
export interface BackupManagerConfig {
  maxRetries: number;
  retryDelay: number;
  chunkSize: number;
  maxConcurrentUploads: number;
  batteryThreshold: number;
}

// Local backup state for persistence
export interface LocalBackupState {
  lastBackupTime: string;
  backupConfiguration: BackupConfiguration;
  pendingSessions: string[];
  failedItems: BackupError[];
  backupStatistics: {
    totalBackups: number;
    totalItems: number;
    lastSuccessfulBackup: string;
  };
}

// Media file types for photo backup
export interface MediaFile {
  uri: string;
  filename: string;
  type: string;
  fileSize: number;
  timestamp: number;
}

export interface FilteredMediaResult {
  photos: MediaFile[];
  excludedVideos: MediaFile[];
  totalScanned: number;
}

// Video file constants for filtering
export const VIDEO_MIME_TYPES = [
  'video/mp4',
  'video/quicktime',
  'video/x-msvideo',
  'video/webm',
  'video/3gpp',
  'video/x-ms-wmv'
] as const;

export const VIDEO_FILE_EXTENSIONS = [
  '.mp4',
  '.mov',
  '.avi',
  '.webm',
  '.3gp',
  '.wmv',
  '.mkv',
  '.flv'
] as const;