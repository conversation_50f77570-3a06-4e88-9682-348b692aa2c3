/**
 * Test Suite for Subscription Management Dashboard
 * Comprehensive testing of subscription management functionality
 */

class SubscriptionManagementTest {
    constructor() {
        this.testResults = [];
        this.dashboard = null;
        this.mockStripeManager = null;
        this.mockTierConfig = null;
        this.mockUsageManager = null;
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting Subscription Management Dashboard Tests...');
        
        try {
            // Setup test environment
            await this.setupTestEnvironment();
            
            // Run test suites
            await this.testDashboardInitialization();
            await this.testSubscriptionStatusDisplay();
            await this.testUsageTracking();
            await this.testBillingHistory();
            await this.testPaymentMethods();
            await this.testSubscriptionModification();
            await this.testErrorHandling();
            await this.testResponsiveDesign();
            
            // Display results
            this.displayTestResults();
            
        } catch (error) {
            console.error('❌ Test suite failed:', error);
            this.addTestResult('Test Suite', false, `Failed to run tests: ${error.message}`);
        }
    }

    /**
     * Setup test environment
     */
    async setupTestEnvironment() {
        console.log('🔧 Setting up test environment...');
        
        // Clear localStorage
        localStorage.clear();
        
        // Create mock managers
        this.createMockManagers();
        
        // Initialize dashboard
        this.dashboard = new SubscriptionManagementDashboard(
            this.mockStripeManager,
            this.mockTierConfig,
            this.mockUsageManager
        );
        
        // Wait for initialization
        await new Promise(resolve => setTimeout(resolve, 100));
        
        console.log('✅ Test environment setup complete');
    }

    /**
     * Create mock managers for testing
     */
    createMockManagers() {
        // Mock Stripe Manager
        this.mockStripeManager = {
            createCustomer: async (data) => ({ id: 'cus_test_123', ...data }),
            createSubscription: async (data) => ({ id: 'sub_test_123', ...data }),
            getCustomer: async (id) => ({ id, email: '<EMAIL>' }),
            getSubscription: async (id) => ({ id, status: 'active' }),
            createPaymentIntent: async (data) => ({ id: 'pi_test_123', ...data }),
            addPaymentMethod: async (customerId, data) => ({ id: 'pm_test_123', ...data }),
            getCustomerPaymentMethods: async (customerId) => [
                { id: 'pm_test_123', card: { brand: 'visa', last4: '4242' } }
            ]
        };
        
        // Mock Tier Config
        this.mockTierConfig = {
            getTier: (id) => ({
                id,
                name: id.charAt(0).toUpperCase() + id.slice(1),
                price: id === 'free' ? 0 : id === 'basic' ? 299 : 999,
                limits: {
                    maxStorageGB: id === 'free' ? 1 : id === 'basic' ? 10 : 100,
                    maxBackupsPerMonth: id === 'free' ? 5 : id === 'basic' ? 50 : -1,
                    maxRestoresPerMonth: id === 'free' ? 2 : id === 'basic' ? 20 : -1
                },
                features: {
                    advanced_encryption: id !== 'free',
                    priority_support: id === 'premium',
                    multi_device_sync: id === 'premium'
                }
            }),
            getAllTiers: () => [
                { id: 'free', name: 'Free', price: 0 },
                { id: 'basic', name: 'Basic', price: 299 },
                { id: 'premium', name: 'Premium', price: 999 }
            ],
            getTierPricing: (id) => ({
                id,
                formattedPrice: id === 'free' ? 'Free' : `$${id === 'basic' ? '2.99' : '9.99'}/month`
            }),
            getUpgradePath: (currentTier) => {
                const paths = {
                    free: [{ id: 'basic', name: 'Basic' }, { id: 'premium', name: 'Premium' }],
                    basic: [{ id: 'premium', name: 'Premium' }],
                    premium: []
                };
                return paths[currentTier] || [];
            },
            getDowngradePath: (currentTier) => {
                const paths = {
                    free: [],
                    basic: [{ id: 'free', name: 'Free' }],
                    premium: [{ id: 'basic', name: 'Basic' }, { id: 'free', name: 'Free' }]
                };
                return paths[currentTier] || [];
            }
        };
        
        // Mock Usage Manager
        this.mockUsageManager = {
            getUsageStatistics: (userId) => ({
                current: {
                    storageUsed: 0.5,
                    backupCount: 3,
                    restoreCount: 1,
                    periodStart: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                    periodEnd: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)
                },
                limits: {
                    storage: { current: 0.5, limit: 1, percentage: 50, allowed: true },
                    backup: { current: 3, limit: 5, percentage: 60, allowed: true },
                    restore: { current: 1, limit: 2, percentage: 50, allowed: true }
                },
                warnings: [],
                recommendations: []
            }),
            addUsageListener: (callback) => {},
            trackUsage: (userId, type, amount) => true
        };
    }

    /**
     * Test dashboard initialization
     */
    async testDashboardInitialization() {
        console.log('🧪 Testing dashboard initialization...');
        
        try {
            // Test dashboard container creation
            const container = this.dashboard.getDashboardContainer();
            this.addTestResult(
                'Dashboard Container Creation',
                container && container.className === 'subscription-dashboard',
                'Dashboard container should be created with correct class'
            );
            
            // Test section elements
            const sections = [
                'current-plan-section',
                'usage-tracking-section',
                'billing-history-section',
                'payment-methods-section',
                'subscription-modification-section'
            ];
            
            sections.forEach(sectionId => {
                const section = container.querySelector(`#${sectionId}`);
                this.addTestResult(
                    `Section ${sectionId}`,
                    section !== null,
                    `Section ${sectionId} should exist in dashboard`
                );
            });
            
            // Test initial data loading
            this.addTestResult(
                'Current User Data',
                this.dashboard.currentUser !== null,
                'Current user data should be loaded'
            );
            
            this.addTestResult(
                'Current Subscription Data',
                this.dashboard.currentSubscription !== null,
                'Current subscription data should be loaded'
            );
            
        } catch (error) {
            this.addTestResult('Dashboard Initialization', false, error.message);
        }
    }

    /**
     * Test subscription status display
     */
    async testSubscriptionStatusDisplay() {
        console.log('🧪 Testing subscription status display...');
        
        try {
            const container = this.dashboard.getDashboardContainer();
            
            // Test status badge
            const statusBadge = container.querySelector('#subscription-status-badge');
            this.addTestResult(
                'Status Badge Exists',
                statusBadge !== null,
                'Status badge should exist'
            );
            
            // Test current plan display
            const currentPlanContent = container.querySelector('#current-plan-content');
            this.addTestResult(
                'Current Plan Content',
                currentPlanContent && currentPlanContent.innerHTML.includes('Plan'),
                'Current plan content should be rendered'
            );
            
            // Test plan features display
            const featureList = container.querySelector('.feature-list');
            this.addTestResult(
                'Feature List',
                featureList && featureList.children.length > 0,
                'Feature list should contain features'
            );
            
            // Test plan pricing display
            const planPrice = container.querySelector('.plan-price');
            this.addTestResult(
                'Plan Price Display',
                planPrice && planPrice.textContent.length > 0,
                'Plan price should be displayed'
            );
            
        } catch (error) {
            this.addTestResult('Subscription Status Display', false, error.message);
        }
    }

    /**
     * Test usage tracking functionality
     */
    async testUsageTracking() {
        console.log('🧪 Testing usage tracking...');
        
        try {
            const container = this.dashboard.getDashboardContainer();
            
            // Test usage metrics display
            const usageMetrics = container.querySelector('.usage-metrics');
            this.addTestResult(
                'Usage Metrics Container',
                usageMetrics !== null,
                'Usage metrics container should exist'
            );
            
            // Test individual usage metrics
            const metrics = container.querySelectorAll('.usage-metric');
            this.addTestResult(
                'Usage Metrics Count',
                metrics.length >= 3,
                'Should display at least 3 usage metrics (storage, backup, restore)'
            );
            
            // Test progress bars
            const progressBars = container.querySelectorAll('.progress-bar');
            this.addTestResult(
                'Progress Bars',
                progressBars.length >= 3,
                'Should display progress bars for usage metrics'
            );
            
            // Test usage period display
            const usagePeriod = container.querySelector('.usage-period');
            this.addTestResult(
                'Usage Period Display',
                usagePeriod && usagePeriod.textContent.includes('Current Billing Period'),
                'Usage period should be displayed'
            );
            
            // Test metric values
            const metricValues = container.querySelectorAll('.metric-value');
            this.addTestResult(
                'Metric Values',
                metricValues.length > 0 && Array.from(metricValues).every(el => el.textContent.length > 0),
                'All metric values should have content'
            );
            
        } catch (error) {
            this.addTestResult('Usage Tracking', false, error.message);
        }
    }

    /**
     * Test billing history functionality
     */
    async testBillingHistory() {
        console.log('🧪 Testing billing history...');
        
        try {
            const container = this.dashboard.getDashboardContainer();
            
            // Test billing overview
            const billingOverview = container.querySelector('.billing-overview');
            this.addTestResult(
                'Billing Overview',
                billingOverview !== null,
                'Billing overview should exist'
            );
            
            // Test billing summary
            const billingSummary = container.querySelector('.billing-summary');
            this.addTestResult(
                'Billing Summary',
                billingSummary !== null,
                'Billing summary should exist'
            );
            
            // Test invoice list
            const invoiceList = container.querySelector('.invoice-list');
            this.addTestResult(
                'Invoice List',
                invoiceList !== null,
                'Invoice list should exist'
            );
            
            // Test invoice header
            const invoiceHeader = container.querySelector('.invoice-header');
            this.addTestResult(
                'Invoice Header',
                invoiceHeader && invoiceHeader.children.length === 5,
                'Invoice header should have 5 columns'
            );
            
            // Test billing actions
            const billingActions = container.querySelector('.billing-actions');
            this.addTestResult(
                'Billing Actions',
                billingActions && billingActions.querySelectorAll('.btn').length > 0,
                'Billing actions should contain buttons'
            );
            
            // Test total paid calculation
            const totalPaid = this.dashboard.calculateTotalPaid();
            this.addTestResult(
                'Total Paid Calculation',
                typeof totalPaid === 'number' && totalPaid >= 0,
                'Total paid should be calculated correctly'
            );
            
        } catch (error) {
            this.addTestResult('Billing History', false, error.message);
        }
    }

    /**
     * Test payment methods functionality
     */
    async testPaymentMethods() {
        console.log('🧪 Testing payment methods...');
        
        try {
            const container = this.dashboard.getDashboardContainer();
            
            // Test payment methods overview
            const paymentOverview = container.querySelector('.payment-methods-overview');
            this.addTestResult(
                'Payment Methods Overview',
                paymentOverview !== null,
                'Payment methods overview should exist'
            );
            
            // Test payment methods list
            const paymentList = container.querySelector('.payment-methods-list');
            this.addTestResult(
                'Payment Methods List',
                paymentList !== null,
                'Payment methods list should exist'
            );
            
            // Test add payment method functionality
            const addButton = container.querySelector('button[onclick*="addPaymentMethod"]');
            this.addTestResult(
                'Add Payment Method Button',
                addButton !== null,
                'Add payment method button should exist'
            );
            
            // Test payment method display
            if (this.dashboard.paymentMethods.length > 0) {
                const paymentMethodItems = container.querySelectorAll('.payment-method-item');
                this.addTestResult(
                    'Payment Method Items',
                    paymentMethodItems.length === this.dashboard.paymentMethods.length,
                    'Should display all payment methods'
                );
                
                // Test default payment method indication
                const defaultMethod = container.querySelector('.payment-method-item.default');
                this.addTestResult(
                    'Default Payment Method',
                    defaultMethod !== null,
                    'Default payment method should be indicated'
                );
            }
            
        } catch (error) {
            this.addTestResult('Payment Methods', false, error.message);
        }
    }

    /**
     * Test subscription modification functionality
     */
    async testSubscriptionModification() {
        console.log('🧪 Testing subscription modification...');
        
        try {
            const container = this.dashboard.getDashboardContainer();
            
            // Test subscription actions section
            const subscriptionActions = container.querySelector('.subscription-actions');
            this.addTestResult(
                'Subscription Actions',
                subscriptionActions !== null,
                'Subscription actions section should exist'
            );
            
            // Test plan options
            const planOptions = container.querySelector('.plan-options');
            this.addTestResult(
                'Plan Options',
                planOptions !== null,
                'Plan options should exist'
            );
            
            // Test upgrade options
            const upgradeOptions = container.querySelector('.upgrade-options');
            const currentTier = this.dashboard.currentSubscription.tierId;
            const hasUpgrades = this.mockTierConfig.getUpgradePath(currentTier).length > 0;
            
            this.addTestResult(
                'Upgrade Options Display',
                hasUpgrades ? upgradeOptions !== null : upgradeOptions === null,
                'Upgrade options should be displayed only when available'
            );
            
            // Test management actions
            const managementActions = container.querySelector('.management-actions');
            this.addTestResult(
                'Management Actions',
                managementActions !== null,
                'Management actions should exist'
            );
            
            // Test cancel subscription button
            const cancelButton = container.querySelector('button[onclick*="cancelSubscription"]');
            this.addTestResult(
                'Cancel Subscription Button',
                cancelButton !== null,
                'Cancel subscription button should exist'
            );
            
            // Test pause subscription button
            const pauseButton = container.querySelector('button[onclick*="pauseSubscription"]');
            this.addTestResult(
                'Pause Subscription Button',
                pauseButton !== null,
                'Pause subscription button should exist'
            );
            
        } catch (error) {
            this.addTestResult('Subscription Modification', false, error.message);
        }
    }

    /**
     * Test error handling
     */
    async testErrorHandling() {
        console.log('🧪 Testing error handling...');
        
        try {
            // Test with invalid subscription data
            const originalSubscription = this.dashboard.currentSubscription;
            this.dashboard.currentSubscription = null;
            
            try {
                this.dashboard.renderCurrentPlan();
                this.addTestResult(
                    'Null Subscription Handling',
                    true,
                    'Should handle null subscription gracefully'
                );
            } catch (error) {
                this.addTestResult(
                    'Null Subscription Handling',
                    false,
                    'Should not throw error with null subscription'
                );
            }
            
            // Restore original subscription
            this.dashboard.currentSubscription = originalSubscription;
            
            // Test error notification system
            this.dashboard.showError('Test Error', 'This is a test error message');
            
            setTimeout(() => {
                const notification = document.querySelector('.notification.error');
                this.addTestResult(
                    'Error Notification Display',
                    notification !== null,
                    'Error notifications should be displayed'
                );
            }, 100);
            
            // Test loading state
            this.dashboard.showLoading('Test loading...');
            const loadingOverlay = document.getElementById('dashboard-loading');
            this.addTestResult(
                'Loading Overlay',
                loadingOverlay !== null && loadingOverlay.style.display !== 'none',
                'Loading overlay should be displayed'
            );
            
            this.dashboard.hideLoading();
            
        } catch (error) {
            this.addTestResult('Error Handling', false, error.message);
        }
    }

    /**
     * Test responsive design
     */
    async testResponsiveDesign() {
        console.log('🧪 Testing responsive design...');
        
        try {
            const container = this.dashboard.getDashboardContainer();
            
            // Test container responsiveness
            this.addTestResult(
                'Dashboard Container Class',
                container.className.includes('subscription-dashboard'),
                'Dashboard should have responsive class'
            );
            
            // Test grid layouts
            const dashboardContent = container.querySelector('.dashboard-content');
            this.addTestResult(
                'Dashboard Content Grid',
                dashboardContent !== null,
                'Dashboard content should use grid layout'
            );
            
            // Test mobile-friendly elements
            const buttons = container.querySelectorAll('.btn');
            this.addTestResult(
                'Button Elements',
                buttons.length > 0,
                'Should contain interactive button elements'
            );
            
            // Test overflow handling
            const invoiceList = container.querySelector('.invoice-list');
            if (invoiceList) {
                this.addTestResult(
                    'Invoice List Overflow',
                    true,
                    'Invoice list should handle overflow properly'
                );
            }
            
        } catch (error) {
            this.addTestResult('Responsive Design', false, error.message);
        }
    }

    /**
     * Test subscription upgrade flow
     */
    async testUpgradeFlow() {
        console.log('🧪 Testing subscription upgrade flow...');
        
        try {
            const originalTier = this.dashboard.currentSubscription.tierId;
            
            // Mock confirm dialog
            const originalConfirm = window.confirm;
            window.confirm = () => true;
            
            // Test upgrade to basic (if currently free)
            if (originalTier === 'free') {
                await this.dashboard.upgradePlan('basic');
                
                this.addTestResult(
                    'Upgrade Plan Execution',
                    this.dashboard.currentSubscription.tierId === 'basic',
                    'Should upgrade plan successfully'
                );
                
                // Test localStorage persistence
                const savedSubscription = JSON.parse(localStorage.getItem('demo_subscription'));
                this.addTestResult(
                    'Upgrade Persistence',
                    savedSubscription && savedSubscription.tierId === 'basic',
                    'Upgraded subscription should be saved to localStorage'
                );
            }
            
            // Restore original confirm
            window.confirm = originalConfirm;
            
        } catch (error) {
            this.addTestResult('Upgrade Flow', false, error.message);
        }
    }

    /**
     * Test payment method management
     */
    async testPaymentMethodManagement() {
        console.log('🧪 Testing payment method management...');
        
        try {
            const originalPrompt = window.prompt;
            const originalConfirm = window.confirm;
            
            // Mock user input
            window.prompt = (message) => {
                if (message.includes('card number')) return '****************';
                if (message.includes('expiry')) return '12/25';
                return null;
            };
            window.confirm = () => true;
            
            const initialCount = this.dashboard.paymentMethods.length;
            
            // Test adding payment method
            await this.dashboard.addPaymentMethod();
            
            this.addTestResult(
                'Add Payment Method',
                this.dashboard.paymentMethods.length === initialCount + 1,
                'Should add new payment method'
            );
            
            // Test setting default payment method
            if (this.dashboard.paymentMethods.length > 1) {
                const newMethodId = this.dashboard.paymentMethods[this.dashboard.paymentMethods.length - 1].id;
                await this.dashboard.setDefaultPaymentMethod(newMethodId);
                
                const newDefault = this.dashboard.paymentMethods.find(m => m.id === newMethodId);
                this.addTestResult(
                    'Set Default Payment Method',
                    newDefault && newDefault.isDefault,
                    'Should set new default payment method'
                );
            }
            
            // Test removing payment method
            if (this.dashboard.paymentMethods.length > 0) {
                const methodToRemove = this.dashboard.paymentMethods[this.dashboard.paymentMethods.length - 1];
                await this.dashboard.removePaymentMethod(methodToRemove.id);
                
                const stillExists = this.dashboard.paymentMethods.find(m => m.id === methodToRemove.id);
                this.addTestResult(
                    'Remove Payment Method',
                    !stillExists,
                    'Should remove payment method'
                );
            }
            
            // Restore original functions
            window.prompt = originalPrompt;
            window.confirm = originalConfirm;
            
        } catch (error) {
            this.addTestResult('Payment Method Management', false, error.message);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, description) {
        this.testResults.push({
            name: testName,
            passed: passed,
            description: description,
            timestamp: new Date()
        });
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${description}`);
    }

    /**
     * Display test results
     */
    displayTestResults() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n📊 Test Results Summary:');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(result => {
                    console.log(`- ${result.name}: ${result.description}`);
                });
        }
        
        // Create visual test results
        this.createTestResultsDisplay();
    }

    /**
     * Create visual test results display
     */
    createTestResultsDisplay() {
        const resultsContainer = document.createElement('div');
        resultsContainer.id = 'test-results';
        resultsContainer.className = 'test-results-container';
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const successRate = ((passedTests / totalTests) * 100).toFixed(1);
        
        resultsContainer.innerHTML = `
            <div class="test-results-header">
                <h3>Subscription Management Dashboard Test Results</h3>
                <div class="test-summary">
                    <div class="summary-item">
                        <span class="label">Total Tests:</span>
                        <span class="value">${totalTests}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Passed:</span>
                        <span class="value passed">${passedTests}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Failed:</span>
                        <span class="value failed">${totalTests - passedTests}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Success Rate:</span>
                        <span class="value">${successRate}%</span>
                    </div>
                </div>
            </div>
            
            <div class="test-results-list">
                ${this.testResults.map(result => `
                    <div class="test-result-item ${result.passed ? 'passed' : 'failed'}">
                        <div class="test-status">${result.passed ? '✅' : '❌'}</div>
                        <div class="test-details">
                            <div class="test-name">${result.name}</div>
                            <div class="test-description">${result.description}</div>
                        </div>
                        <div class="test-timestamp">${result.timestamp.toLocaleTimeString()}</div>
                    </div>
                `).join('')}
            </div>
        `;
        
        // Add styles
        const styles = document.createElement('style');
        styles.textContent = `
            .test-results-container {
                background: white;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
                border: 1px solid #e9ecef;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            
            .test-results-header h3 {
                color: #333;
                margin: 0 0 15px 0;
            }
            
            .test-summary {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
            }
            
            .summary-item {
                text-align: center;
            }
            
            .summary-item .label {
                display: block;
                font-size: 0.9rem;
                color: #666;
                margin-bottom: 5px;
            }
            
            .summary-item .value {
                display: block;
                font-size: 1.2rem;
                font-weight: 600;
                color: #333;
            }
            
            .summary-item .value.passed {
                color: #28a745;
            }
            
            .summary-item .value.failed {
                color: #dc3545;
            }
            
            .test-results-list {
                display: grid;
                gap: 10px;
            }
            
            .test-result-item {
                display: grid;
                grid-template-columns: auto 1fr auto;
                gap: 15px;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                align-items: center;
            }
            
            .test-result-item.passed {
                background: #f8fff9;
                border-color: #c3e6cb;
            }
            
            .test-result-item.failed {
                background: #fff5f5;
                border-color: #f5c6cb;
            }
            
            .test-status {
                font-size: 1.2rem;
            }
            
            .test-name {
                font-weight: 600;
                color: #333;
                margin-bottom: 5px;
            }
            
            .test-description {
                font-size: 0.9rem;
                color: #666;
            }
            
            .test-timestamp {
                font-size: 0.8rem;
                color: #999;
            }
        `;
        
        document.head.appendChild(styles);
        document.body.appendChild(resultsContainer);
    }

    /**
     * Cleanup test environment
     */
    cleanup() {
        // Remove test results display
        const testResults = document.getElementById('test-results');
        if (testResults) {
            testResults.remove();
        }
        
        // Cleanup dashboard
        if (this.dashboard) {
            this.dashboard.cleanup();
        }
        
        console.log('🧹 Test environment cleaned up');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.SubscriptionManagementTest = SubscriptionManagementTest;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionManagementTest;
}