/**
 * Comprehensive Demo Tutorial System
 * Provides interactive tutorials, contextual help, and guided walkthroughs
 */

class TutorialSystem {
    constructor() {
        this.currentTutorial = null;
        this.currentStep = 0;
        this.tutorialProgress = {};
        this.isActive = false;
        this.overlayElement = null;
        this.tooltipElement = null;
        this.progressTracker = new TutorialProgressTracker();
        this.contextualHelp = new ContextualHelpManager();
        
        this.init();
    }

    init() {
        this.createOverlaySystem();
        this.createTooltipSystem();
        this.createProgressIndicator();
        this.createTutorialMenu();
        this.loadProgress();
        this.setupEventListeners();
        
        console.log('Tutorial System initialized');
    }

    createOverlaySystem() {
        // Create main overlay container
        this.overlayElement = document.createElement('div');
        this.overlayElement.id = 'tutorial-overlay';
        this.overlayElement.className = 'tutorial-overlay';
        this.overlayElement.innerHTML = `
            <div class="tutorial-backdrop"></div>
            <div class="tutorial-spotlight"></div>
            <div class="tutorial-content">
                <div class="tutorial-header">
                    <h3 class="tutorial-title"></h3>
                    <button class="tutorial-close" aria-label="Close tutorial">×</button>
                </div>
                <div class="tutorial-body">
                    <div class="tutorial-description"></div>
                    <div class="tutorial-media"></div>
                </div>
                <div class="tutorial-footer">
                    <div class="tutorial-progress">
                        <span class="step-counter"></span>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                    <div class="tutorial-controls">
                        <button class="btn-tutorial btn-skip">Skip Tutorial</button>
                        <button class="btn-tutorial btn-prev">Previous</button>
                        <button class="btn-tutorial btn-next">Next</button>
                        <button class="btn-tutorial btn-finish" style="display: none;">Finish</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.overlayElement);
    }

    createTooltipSystem() {
        this.tooltipElement = document.createElement('div');
        this.tooltipElement.id = 'tutorial-tooltip';
        this.tooltipElement.className = 'tutorial-tooltip';
        this.tooltipElement.innerHTML = `
            <div class="tooltip-content">
                <div class="tooltip-title"></div>
                <div class="tooltip-description"></div>
                <div class="tooltip-actions">
                    <button class="btn-tooltip btn-got-it">Got it!</button>
                    <button class="btn-tooltip btn-learn-more">Learn More</button>
                </div>
            </div>
            <div class="tooltip-arrow"></div>
        `;
        
        document.body.appendChild(this.tooltipElement);
    }

    createProgressIndicator() {
        const progressIndicator = document.createElement('div');
        progressIndicator.id = 'tutorial-progress-indicator';
        progressIndicator.className = 'tutorial-progress-indicator';
        progressIndicator.innerHTML = `
            <div class="progress-circle">
                <svg class="progress-ring" width="60" height="60">
                    <circle class="progress-ring-background" cx="30" cy="30" r="25"></circle>
                    <circle class="progress-ring-progress" cx="30" cy="30" r="25"></circle>
                </svg>
                <div class="progress-percentage">0%</div>
            </div>
            <div class="progress-label">Tutorial Progress</div>
        `;
        
        document.body.appendChild(progressIndicator);
    }

    createTutorialMenu() {
        const tutorialMenu = document.createElement('div');
        tutorialMenu.id = 'tutorial-menu';
        tutorialMenu.className = 'tutorial-menu';
        tutorialMenu.innerHTML = `
            <button class="tutorial-menu-toggle" aria-label="Open tutorial menu">
                <span class="tutorial-icon">🎓</span>
                <span class="tutorial-badge" id="tutorial-badge"></span>
            </button>
            <div class="tutorial-menu-content">
                <div class="tutorial-menu-header">
                    <h4>Available Tutorials</h4>
                    <button class="tutorial-menu-close">×</button>
                </div>
                <div class="tutorial-menu-body">
                    <div class="tutorial-categories"></div>
                </div>
                <div class="tutorial-menu-footer">
                    <button class="btn-tutorial btn-reset-progress">Reset All Progress</button>
                    <button class="btn-tutorial btn-demo-reset">Reset Demo State</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(tutorialMenu);
        this.populateTutorialMenu();
    }

    setupEventListeners() {
        // Overlay controls
        this.overlayElement.querySelector('.tutorial-close').addEventListener('click', () => this.closeTutorial());
        this.overlayElement.querySelector('.btn-skip').addEventListener('click', () => this.skipTutorial());
        this.overlayElement.querySelector('.btn-prev').addEventListener('click', () => this.previousStep());
        this.overlayElement.querySelector('.btn-next').addEventListener('click', () => this.nextStep());
        this.overlayElement.querySelector('.btn-finish').addEventListener('click', () => this.finishTutorial());

        // Tooltip controls
        this.tooltipElement.querySelector('.btn-got-it').addEventListener('click', () => this.hideTooltip());
        this.tooltipElement.querySelector('.btn-learn-more').addEventListener('click', () => this.showDetailedHelp());

        // Menu controls
        const menuToggle = document.querySelector('.tutorial-menu-toggle');
        const menuClose = document.querySelector('.tutorial-menu-close');
        const resetButton = document.querySelector('.btn-reset-progress');
        const demoResetButton = document.querySelector('.btn-demo-reset');
        
        menuToggle.addEventListener('click', () => this.toggleTutorialMenu());
        menuClose.addEventListener('click', () => this.closeTutorialMenu());
        resetButton.addEventListener('click', () => this.resetAllProgress());
        demoResetButton.addEventListener('click', () => this.resetDemoState());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));

        // Auto-show contextual help
        document.addEventListener('mouseover', (e) => this.handleMouseOver(e));
        document.addEventListener('mouseout', (e) => this.handleMouseOut(e));
    }

    // Tutorial definitions
    getTutorials() {
        return {
            'getting-started': {
                title: 'Getting Started with SafeKeep',
                description: 'Learn the basics of SafeKeep and how to navigate the demo',
                category: 'Basics',
                difficulty: 'Beginner',
                estimatedTime: '5 minutes',
                steps: [
                    {
                        title: 'Welcome to SafeKeep',
                        description: 'SafeKeep is a secure media backup solution that protects your photos, messages, and contacts with end-to-end encryption.',
                        target: '.header',
                        position: 'bottom',
                        action: 'highlight'
                    },
                    {
                        title: 'Authentication System',
                        description: 'First, let\'s sign up or sign in to create your secure account.',
                        target: '.auth-section',
                        position: 'right',
                        action: 'focus',
                        interactive: true,
                        validation: () => window.currentUser !== null
                    },
                    {
                        title: 'Dashboard Overview',
                        description: 'This is your main dashboard where you can monitor backup status and system health.',
                        target: '.status-section',
                        position: 'bottom',
                        action: 'highlight'
                    },
                    {
                        title: 'Demo Features',
                        description: 'Explore different features using these demo cards. Each one showcases a different aspect of SafeKeep.',
                        target: '.demo-grid',
                        position: 'top',
                        action: 'highlight'
                    }
                ]
            },
            'backup-process': {
                title: 'Understanding the Backup Process',
                description: 'Learn how SafeKeep backs up your data with real-time progress tracking',
                category: 'Core Features',
                difficulty: 'Intermediate',
                estimatedTime: '8 minutes',
                steps: [
                    {
                        title: 'Starting a Backup',
                        description: 'Click the "Start Real-time Backup Demo" button to begin a simulated backup process.',
                        target: '[onclick*="startRealtimeBackupDemo"]',
                        position: 'top',
                        action: 'pulse',
                        interactive: true,
                        validation: () => document.querySelector('.backup-console') && document.querySelector('.backup-console').style.display !== 'none'
                    },
                    {
                        title: 'Real-time Progress',
                        description: 'Watch as SafeKeep backs up different types of data with live progress updates.',
                        target: '.backup-console',
                        position: 'right',
                        action: 'highlight'
                    },
                    {
                        title: 'Progress Details',
                        description: 'Each data type (contacts, messages, photos) is backed up separately with detailed progress tracking.',
                        target: '.phase-progress',
                        position: 'left',
                        action: 'highlight'
                    },
                    {
                        title: 'Performance Metrics',
                        description: 'Monitor transfer rates, file counts, and other performance indicators in real-time.',
                        target: '.performance-metrics',
                        position: 'top',
                        action: 'highlight'
                    }
                ]
            },
            'encryption-security': {
                title: 'Encryption and Security',
                description: 'Understand how SafeKeep protects your data with advanced encryption',
                category: 'Security',
                difficulty: 'Advanced',
                estimatedTime: '10 minutes',
                steps: [
                    {
                        title: 'Encryption Demo',
                        description: 'Start the encryption demonstration to see how your data is protected.',
                        target: '[onclick*="startEncryptionDemo"]',
                        position: 'top',
                        action: 'pulse',
                        interactive: true
                    },
                    {
                        title: 'Algorithm Selection',
                        description: 'SafeKeep uses industry-standard encryption algorithms. Try different options to see how they compare.',
                        target: '.algorithm-selector',
                        position: 'right',
                        action: 'highlight'
                    },
                    {
                        title: 'Key Generation',
                        description: 'Encryption keys are generated securely and never stored in plain text.',
                        target: '.key-section',
                        position: 'left',
                        action: 'highlight'
                    },
                    {
                        title: 'Data Comparison',
                        description: 'See the difference between your original data and the encrypted version.',
                        target: '.comparison-section',
                        position: 'top',
                        action: 'highlight'
                    }
                ]
            },
            'restore-process': {
                title: 'Data Restoration',
                description: 'Learn how to restore your backed-up data',
                category: 'Core Features',
                difficulty: 'Intermediate',
                estimatedTime: '6 minutes',
                steps: [
                    {
                        title: 'Backup History',
                        description: 'View your backup history to select which backup to restore from.',
                        target: '[onclick*="showBackupHistory"]',
                        position: 'top',
                        action: 'pulse',
                        interactive: true
                    },
                    {
                        title: 'Restore Selection',
                        description: 'Choose which types of data you want to restore and from which backup session.',
                        target: '.restore-selection',
                        position: 'right',
                        action: 'highlight'
                    },
                    {
                        title: 'Decryption Process',
                        description: 'Watch as SafeKeep securely decrypts your data during restoration.',
                        target: '.decryption-progress',
                        position: 'left',
                        action: 'highlight'
                    },
                    {
                        title: 'Data Preview',
                        description: 'Preview your restored data to verify everything was recovered correctly.',
                        target: '.restored-data-preview',
                        position: 'top',
                        action: 'highlight'
                    }
                ]
            },
            'scheduling': {
                title: 'Backup Scheduling',
                description: 'Set up automated backups with advanced scheduling options',
                category: 'Automation',
                difficulty: 'Intermediate',
                estimatedTime: '7 minutes',
                steps: [
                    {
                        title: 'Schedule Configuration',
                        description: 'Open the backup scheduling interface to set up automated backups.',
                        target: '[onclick*="showScheduleDemo"]',
                        position: 'top',
                        action: 'pulse',
                        interactive: true
                    },
                    {
                        title: 'Frequency Options',
                        description: 'Choose how often you want backups to run - daily, weekly, monthly, or custom intervals.',
                        target: '.schedule-frequency',
                        position: 'right',
                        action: 'highlight'
                    },
                    {
                        title: 'Conditions',
                        description: 'Set conditions like WiFi-only, battery level, and storage thresholds for smart scheduling.',
                        target: '.schedule-conditions',
                        position: 'left',
                        action: 'highlight'
                    },
                    {
                        title: 'Schedule Preview',
                        description: 'See when your next backups are scheduled and test the scheduling logic.',
                        target: '.schedule-preview',
                        position: 'top',
                        action: 'highlight'
                    }
                ]
            },
            'subscription-features': {
                title: 'Subscription and Premium Features',
                description: 'Explore premium features and subscription options',
                category: 'Premium',
                difficulty: 'Beginner',
                estimatedTime: '8 minutes',
                steps: [
                    {
                        title: 'Subscription Tiers',
                        description: 'Compare different subscription tiers and their features.',
                        target: '.subscription-tiers',
                        position: 'top',
                        action: 'highlight'
                    },
                    {
                        title: 'Feature Comparison',
                        description: 'See what additional features you get with premium subscriptions.',
                        target: '.feature-comparison',
                        position: 'right',
                        action: 'highlight'
                    },
                    {
                        title: 'Payment Process',
                        description: 'Experience the secure payment flow powered by Stripe.',
                        target: '.payment-interface',
                        position: 'left',
                        action: 'highlight'
                    },
                    {
                        title: 'Subscription Management',
                        description: 'Manage your subscription, view billing history, and update payment methods.',
                        target: '.subscription-dashboard',
                        position: 'top',
                        action: 'highlight'
                    }
                ]
            }
        };
    }

    populateTutorialMenu() {
        const categoriesContainer = document.querySelector('.tutorial-categories');
        const tutorials = this.getTutorials();
        const categories = {};

        // Group tutorials by category
        Object.entries(tutorials).forEach(([id, tutorial]) => {
            if (!categories[tutorial.category]) {
                categories[tutorial.category] = [];
            }
            categories[tutorial.category].push({ id, ...tutorial });
        });

        // Create category sections
        Object.entries(categories).forEach(([category, tutorialList]) => {
            const categorySection = document.createElement('div');
            categorySection.className = 'tutorial-category';
            categorySection.innerHTML = `
                <h5 class="category-title">${category}</h5>
                <div class="category-tutorials">
                    ${tutorialList.map(tutorial => `
                        <div class="tutorial-item ${this.tutorialProgress[tutorial.id]?.completed ? 'completed' : ''}" 
                             data-tutorial-id="${tutorial.id}">
                            <div class="tutorial-info">
                                <h6>${tutorial.title}</h6>
                                <p>${tutorial.description}</p>
                                <div class="tutorial-meta">
                                    <span class="difficulty ${tutorial.difficulty.toLowerCase()}">${tutorial.difficulty}</span>
                                    <span class="duration">${tutorial.estimatedTime}</span>
                                </div>
                            </div>
                            <div class="tutorial-actions">
                                <button class="btn-start-tutorial" data-tutorial-id="${tutorial.id}">
                                    ${this.tutorialProgress[tutorial.id]?.completed ? 'Restart' : 'Start'}
                                </button>
                                ${this.tutorialProgress[tutorial.id]?.progress > 0 && !this.tutorialProgress[tutorial.id]?.completed ? 
                                    `<button class="btn-continue-tutorial" data-tutorial-id="${tutorial.id}">Continue</button>` : ''}
                            </div>
                            ${this.tutorialProgress[tutorial.id]?.completed ? 
                                '<div class="completion-badge">✓</div>' : ''}
                        </div>
                    `).join('')}
                </div>
            `;
            categoriesContainer.appendChild(categorySection);
        });

        // Add event listeners for tutorial buttons
        categoriesContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-start-tutorial')) {
                const tutorialId = e.target.dataset.tutorialId;
                this.startTutorial(tutorialId);
            } else if (e.target.classList.contains('btn-continue-tutorial')) {
                const tutorialId = e.target.dataset.tutorialId;
                this.continueTutorial(tutorialId);
            }
        });

        this.updateTutorialBadge();
    }

    startTutorial(tutorialId) {
        const tutorials = this.getTutorials();
        const tutorial = tutorials[tutorialId];
        
        if (!tutorial) {
            console.error('Tutorial not found:', tutorialId);
            return;
        }

        this.currentTutorial = tutorialId;
        this.currentStep = 0;
        this.isActive = true;

        this.closeTutorialMenu();
        this.showTutorialStep();
        this.updateProgress();

        console.log('Started tutorial:', tutorialId);
    }

    continueTutorial(tutorialId) {
        const progress = this.tutorialProgress[tutorialId];
        if (progress) {
            this.currentTutorial = tutorialId;
            this.currentStep = progress.currentStep || 0;
            this.isActive = true;
            
            this.closeTutorialMenu();
            this.showTutorialStep();
            this.updateProgress();
        }
    }

    showTutorialStep() {
        if (!this.currentTutorial) return;

        const tutorials = this.getTutorials();
        const tutorial = tutorials[this.currentTutorial];
        const step = tutorial.steps[this.currentStep];

        if (!step) return;

        // Update overlay content
        this.overlayElement.querySelector('.tutorial-title').textContent = step.title;
        this.overlayElement.querySelector('.tutorial-description').textContent = step.description;
        this.overlayElement.querySelector('.step-counter').textContent = 
            `Step ${this.currentStep + 1} of ${tutorial.steps.length}`;

        // Update progress bar
        const progressPercentage = ((this.currentStep + 1) / tutorial.steps.length) * 100;
        this.overlayElement.querySelector('.progress-fill').style.width = `${progressPercentage}%`;

        // Update button states
        const prevBtn = this.overlayElement.querySelector('.btn-prev');
        const nextBtn = this.overlayElement.querySelector('.btn-next');
        const finishBtn = this.overlayElement.querySelector('.btn-finish');

        prevBtn.style.display = this.currentStep > 0 ? 'inline-block' : 'none';
        
        if (this.currentStep === tutorial.steps.length - 1) {
            nextBtn.style.display = 'none';
            finishBtn.style.display = 'inline-block';
        } else {
            nextBtn.style.display = 'inline-block';
            finishBtn.style.display = 'none';
        }

        // Handle target element highlighting
        this.highlightTarget(step);

        // Show overlay
        this.overlayElement.style.display = 'flex';
        
        // Handle interactive steps
        if (step.interactive) {
            this.setupInteractiveStep(step);
        }
    }

    highlightTarget(step) {
        // Remove previous highlights
        document.querySelectorAll('.tutorial-highlight').forEach(el => {
            el.classList.remove('tutorial-highlight');
        });

        if (step.target) {
            const targetElement = document.querySelector(step.target);
            if (targetElement) {
                targetElement.classList.add('tutorial-highlight');
                
                // Position spotlight
                const rect = targetElement.getBoundingClientRect();
                const spotlight = this.overlayElement.querySelector('.tutorial-spotlight');
                
                spotlight.style.left = `${rect.left - 10}px`;
                spotlight.style.top = `${rect.top - 10}px`;
                spotlight.style.width = `${rect.width + 20}px`;
                spotlight.style.height = `${rect.height + 20}px`;

                // Position tutorial content
                this.positionTutorialContent(step, rect);

                // Apply action effects
                this.applyStepAction(targetElement, step.action);

                // Scroll into view
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }

    positionTutorialContent(step, targetRect) {
        const content = this.overlayElement.querySelector('.tutorial-content');
        const position = step.position || 'bottom';
        
        content.className = `tutorial-content position-${position}`;
        
        switch (position) {
            case 'top':
                content.style.top = `${targetRect.top - content.offsetHeight - 20}px`;
                content.style.left = `${targetRect.left + (targetRect.width / 2) - (content.offsetWidth / 2)}px`;
                break;
            case 'bottom':
                content.style.top = `${targetRect.bottom + 20}px`;
                content.style.left = `${targetRect.left + (targetRect.width / 2) - (content.offsetWidth / 2)}px`;
                break;
            case 'left':
                content.style.top = `${targetRect.top + (targetRect.height / 2) - (content.offsetHeight / 2)}px`;
                content.style.left = `${targetRect.left - content.offsetWidth - 20}px`;
                break;
            case 'right':
                content.style.top = `${targetRect.top + (targetRect.height / 2) - (content.offsetHeight / 2)}px`;
                content.style.left = `${targetRect.right + 20}px`;
                break;
        }

        // Ensure content stays within viewport
        const contentRect = content.getBoundingClientRect();
        if (contentRect.right > window.innerWidth) {
            content.style.left = `${window.innerWidth - content.offsetWidth - 20}px`;
        }
        if (contentRect.left < 0) {
            content.style.left = '20px';
        }
        if (contentRect.bottom > window.innerHeight) {
            content.style.top = `${window.innerHeight - content.offsetHeight - 20}px`;
        }
        if (contentRect.top < 0) {
            content.style.top = '20px';
        }
    }

    applyStepAction(element, action) {
        // Remove previous action classes
        element.classList.remove('tutorial-pulse', 'tutorial-shake', 'tutorial-glow');
        
        switch (action) {
            case 'pulse':
                element.classList.add('tutorial-pulse');
                break;
            case 'shake':
                element.classList.add('tutorial-shake');
                break;
            case 'glow':
                element.classList.add('tutorial-glow');
                break;
            case 'focus':
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.focus();
                }
                break;
        }
    }

    setupInteractiveStep(step) {
        if (step.validation) {
            const checkValidation = () => {
                if (step.validation()) {
                    this.nextStep();
                } else {
                    setTimeout(checkValidation, 1000);
                }
            };
            setTimeout(checkValidation, 1000);
        }
    }

    nextStep() {
        if (!this.currentTutorial) return;

        const tutorials = this.getTutorials();
        const tutorial = tutorials[this.currentTutorial];
        
        if (this.currentStep < tutorial.steps.length - 1) {
            this.currentStep++;
            this.showTutorialStep();
            this.updateProgress();
        }
    }

    previousStep() {
        if (this.currentStep > 0) {
            this.currentStep--;
            this.showTutorialStep();
            this.updateProgress();
        }
    }

    skipTutorial() {
        if (confirm('Are you sure you want to skip this tutorial?')) {
            this.closeTutorial();
        }
    }

    finishTutorial() {
        if (!this.currentTutorial) return;

        // Mark tutorial as completed
        this.tutorialProgress[this.currentTutorial] = {
            completed: true,
            completedAt: new Date().toISOString(),
            progress: 100,
            currentStep: 0
        };

        this.saveProgress();
        this.closeTutorial();
        this.showCompletionMessage();
        this.updateTutorialBadge();
    }

    closeTutorial() {
        this.isActive = false;
        this.overlayElement.style.display = 'none';
        
        // Remove highlights
        document.querySelectorAll('.tutorial-highlight').forEach(el => {
            el.classList.remove('tutorial-highlight');
        });
        
        // Save current progress
        if (this.currentTutorial) {
            this.tutorialProgress[this.currentTutorial] = {
                ...this.tutorialProgress[this.currentTutorial],
                currentStep: this.currentStep,
                progress: (this.currentStep / this.getTutorials()[this.currentTutorial].steps.length) * 100
            };
            this.saveProgress();
        }
        
        this.currentTutorial = null;
        this.currentStep = 0;
    }

    showTooltip(element, title, description, position = 'top') {
        const rect = element.getBoundingClientRect();
        
        this.tooltipElement.querySelector('.tooltip-title').textContent = title;
        this.tooltipElement.querySelector('.tooltip-description').textContent = description;
        
        // Position tooltip
        this.tooltipElement.className = `tutorial-tooltip position-${position}`;
        
        switch (position) {
            case 'top':
                this.tooltipElement.style.left = `${rect.left + rect.width / 2}px`;
                this.tooltipElement.style.top = `${rect.top - 10}px`;
                break;
            case 'bottom':
                this.tooltipElement.style.left = `${rect.left + rect.width / 2}px`;
                this.tooltipElement.style.top = `${rect.bottom + 10}px`;
                break;
            case 'left':
                this.tooltipElement.style.left = `${rect.left - 10}px`;
                this.tooltipElement.style.top = `${rect.top + rect.height / 2}px`;
                break;
            case 'right':
                this.tooltipElement.style.left = `${rect.right + 10}px`;
                this.tooltipElement.style.top = `${rect.top + rect.height / 2}px`;
                break;
        }
        
        this.tooltipElement.style.display = 'block';
    }

    hideTooltip() {
        this.tooltipElement.style.display = 'none';
    }

    showDetailedHelp() {
        // Implementation for detailed help modal
        console.log('Show detailed help');
    }

    toggleTutorialMenu() {
        const menu = document.querySelector('#tutorial-menu');
        menu.classList.toggle('open');
    }

    closeTutorialMenu() {
        const menu = document.querySelector('#tutorial-menu');
        menu.classList.remove('open');
    }

    updateProgress() {
        if (!this.currentTutorial) return;

        const tutorials = this.getTutorials();
        const tutorial = tutorials[this.currentTutorial];
        const progress = ((this.currentStep + 1) / tutorial.steps.length) * 100;

        // Update progress indicator
        const progressIndicator = document.querySelector('#tutorial-progress-indicator');
        const progressRing = progressIndicator.querySelector('.progress-ring-progress');
        const progressPercentage = progressIndicator.querySelector('.progress-percentage');
        
        const circumference = 2 * Math.PI * 25;
        const offset = circumference - (progress / 100) * circumference;
        
        progressRing.style.strokeDasharray = circumference;
        progressRing.style.strokeDashoffset = offset;
        progressPercentage.textContent = `${Math.round(progress)}%`;
    }

    updateTutorialBadge() {
        const badge = document.querySelector('#tutorial-badge');
        const completedCount = Object.values(this.tutorialProgress).filter(p => p.completed).length;
        const totalCount = Object.keys(this.getTutorials()).length;
        
        if (completedCount > 0) {
            badge.textContent = `${completedCount}/${totalCount}`;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }

    showCompletionMessage() {
        const message = document.createElement('div');
        message.className = 'tutorial-completion-message';
        message.innerHTML = `
            <div class="completion-content">
                <div class="completion-icon">🎉</div>
                <h3>Tutorial Completed!</h3>
                <p>Great job! You've successfully completed the tutorial.</p>
                <button class="btn-tutorial btn-close-completion">Continue</button>
            </div>
        `;
        
        document.body.appendChild(message);
        
        message.querySelector('.btn-close-completion').addEventListener('click', () => {
            message.remove();
        });
        
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 5000);
    }

    resetAllProgress() {
        if (confirm('Are you sure you want to reset all tutorial progress? This cannot be undone.')) {
            this.tutorialProgress = {};
            this.saveProgress();
            this.populateTutorialMenu();
            this.updateTutorialBadge();
        }
    }

    resetDemoState() {
        if (confirm('Are you sure you want to reset the demo state? This will clear all demo data and restart the experience.')) {
            // Clear demo-related localStorage
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith('safekeep_demo_') || key.startsWith('tutorial_')) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            // Reload the page to reset state
            window.location.reload();
        }
    }

    handleKeyboardShortcuts(e) {
        if (!this.isActive) return;

        switch (e.key) {
            case 'Escape':
                this.closeTutorial();
                break;
            case 'ArrowRight':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.nextStep();
                }
                break;
            case 'ArrowLeft':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.previousStep();
                }
                break;
        }
    }

    handleMouseOver(e) {
        if (this.isActive) return;

        const element = e.target;
        const helpData = this.contextualHelp.getHelpForElement(element);
        
        if (helpData) {
            this.showTooltip(element, helpData.title, helpData.description, helpData.position);
        }
    }

    handleMouseOut(e) {
        if (this.isActive) return;
        
        // Hide tooltip after a delay
        setTimeout(() => {
            if (!this.tooltipElement.matches(':hover')) {
                this.hideTooltip();
            }
        }, 500);
    }

    loadProgress() {
        const saved = localStorage.getItem('tutorial_progress');
        if (saved) {
            try {
                this.tutorialProgress = JSON.parse(saved);
            } catch (e) {
                console.error('Failed to load tutorial progress:', e);
                this.tutorialProgress = {};
            }
        }
    }

    saveProgress() {
        localStorage.setItem('tutorial_progress', JSON.stringify(this.tutorialProgress));
    }
}

// Tutorial Progress Tracker
class TutorialProgressTracker {
    constructor() {
        this.sessionData = {
            startTime: Date.now(),
            interactions: [],
            completedSteps: 0,
            totalTime: 0
        };
    }

    trackInteraction(type, data) {
        this.sessionData.interactions.push({
            type,
            data,
            timestamp: Date.now()
        });
    }

    getAnalytics() {
        return {
            ...this.sessionData,
            totalTime: Date.now() - this.sessionData.startTime
        };
    }
}

// Contextual Help Manager
class ContextualHelpManager {
    constructor() {
        this.helpData = {
            '.auth-section': {
                title: 'Authentication',
                description: 'Sign up or sign in to access personalized demo features',
                position: 'right'
            },
            '.backup-console': {
                title: 'Backup Console',
                description: 'Monitor real-time backup progress and performance metrics',
                position: 'top'
            },
            '.encryption-demo': {
                title: 'Encryption Demo',
                description: 'See how your data is encrypted and protected',
                position: 'left'
            },
            '.subscription-dashboard': {
                title: 'Subscription Management',
                description: 'Manage your subscription and billing information',
                position: 'top'
            }
        };
    }

    getHelpForElement(element) {
        for (const [selector, helpData] of Object.entries(this.helpData)) {
            if (element.matches(selector) || element.closest(selector)) {
                return helpData;
            }
        }
        return null;
    }

    addHelpData(selector, data) {
        this.helpData[selector] = data;
    }
}

// Initialize tutorial system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.tutorialSystem = new TutorialSystem();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TutorialSystem, TutorialProgressTracker, ContextualHelpManager };
}