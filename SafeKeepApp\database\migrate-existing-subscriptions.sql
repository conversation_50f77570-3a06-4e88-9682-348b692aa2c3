-- SafeKeep Existing Subscriptions Migration Script
-- This script migrates existing subscriptions from the old tier system to the new modular pricing
-- Run this AFTER the modular-pricing-migration.sql script

-- ============================================================================
-- STEP 1: Backup existing subscription data
-- ============================================================================

-- Create a backup table for existing subscriptions
CREATE TABLE IF NOT EXISTS public.subscription_migration_backup AS
SELECT * FROM public.user_subscriptions;

-- ============================================================================
-- STEP 2: Map old tiers to new plans
-- ============================================================================

-- Update existing subscriptions to map old tiers to new plans
-- This provides backward compatibility while transitioning to the new system

UPDATE public.user_subscriptions 
SET 
    legacy_tier_id = tier_id,
    plan_id = CASE 
        WHEN tier_id = 'free' THEN 'contacts-only'  -- Free users get contacts only
        WHEN tier_id = 'basic' THEN 'contacts-messages'  -- Basic users get contacts + messages
        WHEN tier_id = 'premium' THEN 'complete-backup'  -- Premium users get everything
        ELSE 'contacts-only'  -- Default fallback
    END,
    total_price_cents = CASE 
        WHEN tier_id = 'free' THEN 0
        WHEN tier_id = 'basic' THEN 249
        WHEN tier_id = 'premium' THEN 699
        ELSE 0
    END,
    service_combination = CASE 
        WHEN tier_id = 'free' THEN '["contacts"]'
        WHEN tier_id = 'basic' THEN '["contacts", "messages"]'
        WHEN tier_id = 'premium' THEN '["contacts", "messages", "photos"]'
        ELSE '["contacts"]'
    END,
    updated_at = NOW()
WHERE plan_id IS NULL;

-- ============================================================================
-- STEP 3: Create user service selections based on existing subscriptions
-- ============================================================================

-- Insert service selections for users based on their current subscription
INSERT INTO public.user_service_selections (user_id, service_type_id, is_active, activated_at)
SELECT DISTINCT
    us.user_id,
    'contacts' as service_type_id,
    TRUE as is_active,
    us.created_at as activated_at
FROM public.user_subscriptions us
WHERE us.status = 'active'
ON CONFLICT (user_id, service_type_id) DO NOTHING;

-- Add messages service for basic and premium users
INSERT INTO public.user_service_selections (user_id, service_type_id, is_active, activated_at)
SELECT DISTINCT
    us.user_id,
    'messages' as service_type_id,
    TRUE as is_active,
    us.created_at as activated_at
FROM public.user_subscriptions us
WHERE us.status = 'active' 
AND us.legacy_tier_id IN ('basic', 'premium')
ON CONFLICT (user_id, service_type_id) DO NOTHING;

-- Add photos service for premium users
INSERT INTO public.user_service_selections (user_id, service_type_id, is_active, activated_at)
SELECT DISTINCT
    us.user_id,
    'photos' as service_type_id,
    TRUE as is_active,
    us.created_at as activated_at
FROM public.user_subscriptions us
WHERE us.status = 'active' 
AND us.legacy_tier_id = 'premium'
ON CONFLICT (user_id, service_type_id) DO NOTHING;

-- ============================================================================
-- STEP 4: Update storage usage tracking with service-specific data
-- ============================================================================

-- Update storage_usage table with service-specific counts and sizes
-- This assumes existing file_metadata has category field

UPDATE public.storage_usage 
SET 
    contacts_count = COALESCE((
        SELECT COUNT(*) 
        FROM public.file_metadata fm 
        WHERE fm.user_id = storage_usage.user_id 
        AND fm.category = 'contact'
    ), 0),
    contacts_size_bytes = COALESCE((
        SELECT SUM(encrypted_size) 
        FROM public.file_metadata fm 
        WHERE fm.user_id = storage_usage.user_id 
        AND fm.category = 'contact'
    ), 0),
    messages_count = COALESCE((
        SELECT COUNT(*) 
        FROM public.file_metadata fm 
        WHERE fm.user_id = storage_usage.user_id 
        AND fm.category = 'message'
    ), 0),
    messages_size_bytes = COALESCE((
        SELECT SUM(encrypted_size) 
        FROM public.file_metadata fm 
        WHERE fm.user_id = storage_usage.user_id 
        AND fm.category = 'message'
    ), 0),
    photos_count = COALESCE((
        SELECT COUNT(*) 
        FROM public.file_metadata fm 
        WHERE fm.user_id = storage_usage.user_id 
        AND fm.category = 'photo'
    ), 0),
    photos_size_bytes = COALESCE((
        SELECT SUM(encrypted_size) 
        FROM public.file_metadata fm 
        WHERE fm.user_id = storage_usage.user_id 
        AND fm.category = 'photo'
    ), 0),
    last_updated = NOW()
WHERE category IN ('photo', 'contact', 'message');

-- ============================================================================
-- STEP 5: Update users table with new storage quotas based on their plan
-- ============================================================================

UPDATE public.users 
SET 
    storage_quota = CASE 
        WHEN id IN (
            SELECT user_id FROM public.user_subscriptions 
            WHERE plan_id = 'contacts-only' AND status = 'active'
        ) THEN 1073741824  -- 1GB in bytes
        WHEN id IN (
            SELECT user_id FROM public.user_subscriptions 
            WHERE plan_id = 'messages-only' AND status = 'active'
        ) THEN 2147483648  -- 2GB in bytes
        WHEN id IN (
            SELECT user_id FROM public.user_subscriptions 
            WHERE plan_id = 'photos-only' AND status = 'active'
        ) THEN 10737418240  -- 10GB in bytes
        WHEN id IN (
            SELECT user_id FROM public.user_subscriptions 
            WHERE plan_id = 'contacts-messages' AND status = 'active'
        ) THEN 3221225472  -- 3GB in bytes
        WHEN id IN (
            SELECT user_id FROM public.user_subscriptions 
            WHERE plan_id = 'contacts-photos' AND status = 'active'
        ) THEN 11811160064  -- 11GB in bytes
        WHEN id IN (
            SELECT user_id FROM public.user_subscriptions 
            WHERE plan_id = 'messages-photos' AND status = 'active'
        ) THEN 12884901888  -- 12GB in bytes
        WHEN id IN (
            SELECT user_id FROM public.user_subscriptions 
            WHERE plan_id = 'complete-backup' AND status = 'active'
        ) THEN 16106127360  -- 15GB in bytes
        ELSE storage_quota  -- Keep existing quota for users without active subscriptions
    END,
    updated_at = NOW()
WHERE id IN (SELECT user_id FROM public.user_subscriptions WHERE status = 'active');

-- ============================================================================
-- STEP 6: Create helper functions for pricing calculations
-- ============================================================================

-- Function to calculate total price for a user's selected services
CREATE OR REPLACE FUNCTION public.calculate_user_subscription_price(user_uuid UUID)
RETURNS INTEGER AS $
DECLARE
    total_price INTEGER := 0;
    service_count INTEGER := 0;
BEGIN
    -- Count active services for the user
    SELECT COUNT(*) INTO service_count
    FROM public.user_service_selections uss
    WHERE uss.user_id = user_uuid AND uss.is_active = TRUE;
    
    -- If user has multiple services, find the best combination plan
    IF service_count > 1 THEN
        -- Check if user has all three services (complete backup)
        IF EXISTS (
            SELECT 1 FROM public.user_service_selections uss
            WHERE uss.user_id = user_uuid AND uss.is_active = TRUE
            AND uss.service_type_id IN ('contacts', 'messages', 'photos')
            GROUP BY uss.user_id
            HAVING COUNT(DISTINCT uss.service_type_id) = 3
        ) THEN
            total_price := 699; -- Complete backup price
        -- Check for two-service combinations
        ELSIF EXISTS (
            SELECT 1 FROM public.user_service_selections uss
            WHERE uss.user_id = user_uuid AND uss.is_active = TRUE
            AND uss.service_type_id IN ('messages', 'photos')
            GROUP BY uss.user_id
            HAVING COUNT(DISTINCT uss.service_type_id) = 2
        ) THEN
            total_price := 649; -- Messages + Photos
        ELSIF EXISTS (
            SELECT 1 FROM public.user_service_selections uss
            WHERE uss.user_id = user_uuid AND uss.is_active = TRUE
            AND uss.service_type_id IN ('contacts', 'photos')
            GROUP BY uss.user_id
            HAVING COUNT(DISTINCT uss.service_type_id) = 2
        ) THEN
            total_price := 549; -- Contacts + Photos
        ELSIF EXISTS (
            SELECT 1 FROM public.user_service_selections uss
            WHERE uss.user_id = user_uuid AND uss.is_active = TRUE
            AND uss.service_type_id IN ('contacts', 'messages')
            GROUP BY uss.user_id
            HAVING COUNT(DISTINCT uss.service_type_id) = 2
        ) THEN
            total_price := 249; -- Contacts + Messages
        END IF;
    ELSE
        -- Single service pricing
        SELECT COALESCE(st.base_price_cents, 0) INTO total_price
        FROM public.user_service_selections uss
        JOIN public.service_types st ON uss.service_type_id = st.id
        WHERE uss.user_id = user_uuid AND uss.is_active = TRUE
        LIMIT 1;
    END IF;
    
    RETURN COALESCE(total_price, 0);
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get the appropriate plan ID for a user's service combination
CREATE OR REPLACE FUNCTION public.get_user_plan_id(user_uuid UUID)
RETURNS VARCHAR(50) AS $
DECLARE
    plan_id VARCHAR(50);
    service_count INTEGER := 0;
    has_contacts BOOLEAN := FALSE;
    has_messages BOOLEAN := FALSE;
    has_photos BOOLEAN := FALSE;
BEGIN
    -- Check which services the user has
    SELECT 
        COUNT(*),
        BOOL_OR(service_type_id = 'contacts'),
        BOOL_OR(service_type_id = 'messages'),
        BOOL_OR(service_type_id = 'photos')
    INTO service_count, has_contacts, has_messages, has_photos
    FROM public.user_service_selections
    WHERE user_id = user_uuid AND is_active = TRUE;
    
    -- Determine the appropriate plan
    IF service_count = 3 THEN
        plan_id := 'complete-backup';
    ELSIF service_count = 2 THEN
        IF has_messages AND has_photos THEN
            plan_id := 'messages-photos';
        ELSIF has_contacts AND has_photos THEN
            plan_id := 'contacts-photos';
        ELSIF has_contacts AND has_messages THEN
            plan_id := 'contacts-messages';
        END IF;
    ELSIF service_count = 1 THEN
        IF has_contacts THEN
            plan_id := 'contacts-only';
        ELSIF has_messages THEN
            plan_id := 'messages-only';
        ELSIF has_photos THEN
            plan_id := 'photos-only';
        END IF;
    ELSE
        plan_id := 'contacts-only'; -- Default fallback
    END IF;
    
    RETURN plan_id;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 7: Verification queries
-- ============================================================================

-- Verify migration results
SELECT 'Migration Summary:' as info;

SELECT 
    'Total subscriptions migrated:' as metric,
    COUNT(*) as value
FROM public.user_subscriptions 
WHERE plan_id IS NOT NULL;

SELECT 
    'Service selections created:' as metric,
    COUNT(*) as value
FROM public.user_service_selections;

SELECT 
    'Plan distribution:' as metric,
    plan_id,
    COUNT(*) as user_count
FROM public.user_subscriptions 
WHERE status = 'active' AND plan_id IS NOT NULL
GROUP BY plan_id
ORDER BY user_count DESC;

SELECT 'Existing Subscriptions Migration Complete!' as status;