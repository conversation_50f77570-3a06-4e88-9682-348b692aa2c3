/**
 * Manual Component Initialization
 * Force initialize components if auto-init fails
 */

async function manualInitComponents() {
    console.log('🔧 Manual Component Initialization...');
    
    try {
        // Check if Supabase is available
        if (typeof supabase === 'undefined') {
            console.log('❌ Supabase not available, cannot initialize components');
            return;
        }
        
        console.log('✅ Supabase available, proceeding with initialization...');
        
        // Initialize Schedule Manager if not already done
        if (!window.scheduleManager && window.ScheduleManager) {
            console.log('🕒 Initializing Schedule Manager...');
            window.scheduleManager = new window.ScheduleManager(supabase, adminSupabase);
            await window.scheduleManager.initialize();
            console.log('✅ Schedule Manager initialized');
        }
        
        // Initialize Schedule Console if not already done
        if (!window.scheduleConsole && window.ScheduleConsole) {
            console.log('🖥️ Initializing Schedule Console...');
            window.scheduleConsole = new window.ScheduleConsole('schedule-console-container');
            if (window.scheduleManager) {
                window.scheduleConsole.setScheduleManager(window.scheduleManager);
            }
            console.log('✅ Schedule Console initialized');
        }
        
        // Initialize Backup History Dashboard if not already done
        if (!window.backupHistoryDashboard && window.BackupHistoryDashboard) {
            console.log('📊 Initializing Backup History Dashboard...');
            if (!window.backupHistoryManager && window.BackupHistoryManager) {
                window.backupHistoryManager = new window.BackupHistoryManager(supabase, adminSupabase);
            }
            window.backupHistoryDashboard = new window.BackupHistoryDashboard('backup-history-dashboard-container');
            if (window.backupHistoryManager) {
                window.backupHistoryDashboard.setHistoryManager(window.backupHistoryManager);
            }
            console.log('✅ Backup History Dashboard initialized');
        }
        
        // Initialize Restore Simulation if not already done
        if (!window.restoreSimulation && window.RestoreSimulation) {
            console.log('🔄 Initializing Restore Simulation...');
            if (!window.restoreManager && window.RestoreManager) {
                window.restoreManager = new window.RestoreManager(supabase, adminSupabase);
            }
            window.restoreSimulation = new window.RestoreSimulation('restore-simulation-container');
            if (window.restoreManager) {
                window.restoreSimulation.setRestoreManager(window.restoreManager);
            }
            if (window.backupHistoryManager) {
                window.restoreSimulation.setHistoryManager(window.backupHistoryManager);
            }
            console.log('✅ Restore Simulation initialized');
        }
        
        // Initialize Encryption Demo if not already done
        if (!window.encryptionDemo && window.EncryptionDemo) {
            console.log('🔐 Initializing Encryption Demo...');
            window.encryptionDemo = new window.EncryptionDemo('encryption-demo-container');
            console.log('✅ Encryption Demo initialized');
        }
        
        // Initialize Backup Console if not already done
        if (!window.backupConsole && window.BackupConsole) {
            console.log('⚡ Initializing Backup Console...');
            if (!window.progressManager && window.RealtimeProgressManager) {
                window.progressManager = new window.RealtimeProgressManager();
            }
            window.backupConsole = new window.BackupConsole('backup-console-container');
            if (window.progressManager) {
                window.backupConsole.setProgressManager(window.progressManager);
            }
            console.log('✅ Backup Console initialized');
        }
        
        console.log('🎉 Manual initialization complete!');
        console.log('💡 Try using the demo features now');
        
        return {
            success: true,
            components: {
                scheduleManager: !!window.scheduleManager,
                scheduleConsole: !!window.scheduleConsole,
                backupHistoryDashboard: !!window.backupHistoryDashboard,
                restoreSimulation: !!window.restoreSimulation,
                encryptionDemo: !!window.encryptionDemo,
                backupConsole: !!window.backupConsole
            }
        };
        
    } catch (error) {
        console.error('❌ Manual initialization failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Add button to manually trigger initialization
function addManualInitButton() {
    const container = document.querySelector('.main-content');
    if (container && !document.getElementById('manual-init-button')) {
        const button = document.createElement('button');
        button.id = 'manual-init-button';
        button.className = 'btn';
        button.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 1000; background: #ff6b6b;';
        button.textContent = '🔧 Manual Init';
        button.onclick = manualInitComponents;
        document.body.appendChild(button);
    }
}

// Auto-add button when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addManualInitButton);
} else {
    addManualInitButton();
}

// Export for manual use
window.manualInitComponents = manualInitComponents;