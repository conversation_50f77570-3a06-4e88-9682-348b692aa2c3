import { Request, Response } from 'express';
import { BillingController } from '../BillingController';
import { BillingIntegration } from '../../services/BillingIntegration';

// Mock the BillingIntegration
jest.mock('../../services/BillingIntegration');

describe('BillingController', () => {
  let billingController: BillingController;
  let mockBillingIntegration: jest.Mocked<BillingIntegration>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock response
    mockResponse = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    };

    // Create controller instance
    billingController = new BillingController();
    
    // Get the mocked BillingIntegration instance
    mockBillingIntegration = (billingController as any).billingIntegration;
  });

  describe('createPaymentIntent', () => {
    it('should create payment intent successfully', async () => {
      mockRequest = {
        body: {
          amount: 1999,
          serviceIds: ['contacts', 'messages'],
          userId: 'user-123',
          planId: 'standard',
          planName: 'Standard Plan'
        }
      };

      const mockPaymentResult = {
        success: true,
        paymentIntentId: 'pi_123',
        subscriptionId: 'sub_123'
      };

      mockBillingIntegration.createPaymentIntent.mockResolvedValue(mockPaymentResult);

      await billingController.createPaymentIntent(mockRequest as Request, mockResponse as Response);

      expect(mockBillingIntegration.createPaymentIntent).toHaveBeenCalledWith(1999, {
        userId: 'user-123',
        serviceIds: ['contacts', 'messages'],
        planId: 'standard',
        planName: 'Standard Plan'
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockPaymentResult
      });
    });

    it('should use default planId and planName when not provided', async () => {
      mockRequest = {
        body: {
          amount: 999,
          serviceIds: ['contacts'],
          userId: 'user-123'
        }
      };

      const mockPaymentResult = {
        success: true,
        paymentIntentId: 'pi_123'
      };

      mockBillingIntegration.createPaymentIntent.mockResolvedValue(mockPaymentResult);

      await billingController.createPaymentIntent(mockRequest as Request, mockResponse as Response);

      expect(mockBillingIntegration.createPaymentIntent).toHaveBeenCalledWith(999, {
        userId: 'user-123',
        serviceIds: ['contacts'],
        planId: 'custom',
        planName: 'Custom Plan'
      });
    });

    it('should return 400 error when amount is invalid', async () => {
      mockRequest = {
        body: {
          amount: -100,
          serviceIds: ['contacts'],
          userId: 'user-123'
        }
      };

      await billingController.createPaymentIntent(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid amount (positive number) is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds is empty', async () => {
      mockRequest = {
        body: {
          amount: 1999,
          serviceIds: [],
          userId: 'user-123'
        }
      };

      await billingController.createPaymentIntent(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when userId is invalid', async () => {
      mockRequest = {
        body: {
          amount: 1999,
          serviceIds: ['contacts'],
          userId: '   '
        }
      };

      await billingController.createPaymentIntent(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds contains invalid values', async () => {
      mockRequest = {
        body: {
          amount: 1999,
          serviceIds: ['contacts', '', 'messages'],
          userId: 'user-123'
        }
      };

      await billingController.createPaymentIntent(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'All service IDs must be non-empty strings',
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle errors when creating payment intent', async () => {
      mockRequest = {
        body: {
          amount: 1999,
          serviceIds: ['contacts'],
          userId: 'user-123'
        }
      };

      const error = new Error('Stripe API error');
      mockBillingIntegration.createPaymentIntent.mockRejectedValue(error);

      await billingController.createPaymentIntent(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'PAYMENT_INTENT_ERROR',
          message: 'Failed to create payment intent',
          details: { message: 'Stripe API error' },
          timestamp: expect.any(String)
        }
      });
    });
  });

  describe('handleWebhook', () => {
    it('should handle webhook successfully', async () => {
      mockRequest = {
        headers: {
          'stripe-signature': 'whsec_test_signature'
        },
        body: 'webhook_payload'
      };

      const mockEvent = {
        id: 'evt_123',
        type: 'payment_intent.succeeded',
        data: { object: { id: 'pi_123' } },
        object: 'event',
        api_version: '2020-08-27',
        created: Date.now(),
        livemode: false,
        pending_webhooks: 1,
        request: { id: null, idempotency_key: null }
      } as any;

      const mockWebhookResult = {
        processed: true,
        action: 'payment_processed'
      };

      mockBillingIntegration.verifyWebhookSignature.mockReturnValue(mockEvent);
      mockBillingIntegration.handleWebhook.mockResolvedValue(mockWebhookResult);

      await billingController.handleWebhook(mockRequest as Request, mockResponse as Response);

      expect(mockBillingIntegration.verifyWebhookSignature).toHaveBeenCalledWith('webhook_payload', 'whsec_test_signature', 'whsec_test_secret');
      expect(mockBillingIntegration.handleWebhook).toHaveBeenCalledWith(mockEvent);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockWebhookResult
      });
    });

    it('should return 400 error when signature is missing', async () => {
      mockRequest = {
        headers: {},
        body: 'webhook_payload'
      };

      await billingController.handleWebhook(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_SIGNATURE',
          message: 'Stripe signature is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when payload is missing', async () => {
      mockRequest = {
        headers: {
          'stripe-signature': 'whsec_test_signature'
        },
        body: null
      };

      await billingController.handleWebhook(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_PAYLOAD',
          message: 'Webhook payload is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when signature verification fails', async () => {
      mockRequest = {
        headers: {
          'stripe-signature': 'invalid_signature'
        },
        body: 'webhook_payload'
      };

      const error = new Error('Invalid webhook signature');
      mockBillingIntegration.verifyWebhookSignature.mockImplementation(() => {
        throw error;
      });

      await billingController.handleWebhook(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_SIGNATURE',
          message: 'Invalid webhook signature',
          details: { message: 'Invalid webhook signature' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle general webhook processing errors', async () => {
      mockRequest = {
        headers: {
          'stripe-signature': 'whsec_test_signature'
        },
        body: 'webhook_payload'
      };

      const mockEvent = {
        id: 'evt_123',
        type: 'payment_intent.succeeded',
        data: { object: { id: 'pi_123' } },
        object: 'event',
        api_version: '2020-08-27',
        created: Date.now(),
        livemode: false,
        pending_webhooks: 1,
        request: { id: null, idempotency_key: null }
      } as any;

      const error = new Error('Database connection failed');
      mockBillingIntegration.verifyWebhookSignature.mockReturnValue(mockEvent);
      mockBillingIntegration.handleWebhook.mockRejectedValue(error);

      await billingController.handleWebhook(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'WEBHOOK_ERROR',
          message: 'Failed to process webhook',
          details: { message: 'Database connection failed' },
          timestamp: expect.any(String)
        }
      });
    });
  });

  describe('getBillingStatus', () => {
    it('should get billing status successfully', async () => {
      mockRequest = {
        params: { userId: 'user-123' }
      };

      const mockCustomer = {
        id: 'cus_123',
        email: '<EMAIL>',
        metadata: { userId: 'user-123' },
        object: 'customer',
        balance: 0,
        created: Date.now(),
        default_source: null,
        delinquent: false,
        description: null,
        discount: null,
        invoice_prefix: null,
        livemode: false
      } as any;

      mockBillingIntegration.getCustomerByUserId.mockResolvedValue(mockCustomer);

      await billingController.getBillingStatus(mockRequest as Request, mockResponse as Response);

      expect(mockBillingIntegration.getCustomerByUserId).toHaveBeenCalledWith('user-123');
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          userId: 'user-123',
          customerId: 'cus_123',
          email: '<EMAIL>',
          status: 'active',
          nextPaymentDate: expect.any(Date),
          lastPaymentDate: expect.any(Date),
          currentPriceCents: 0
        }
      });
    });

    it('should return 400 error when userId is missing', async () => {
      mockRequest = { params: {} };

      await billingController.getBillingStatus(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 404 error when customer not found', async () => {
      mockRequest = {
        params: { userId: 'user-123' }
      };

      mockBillingIntegration.getCustomerByUserId.mockResolvedValue(null);

      await billingController.getBillingStatus(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'CUSTOMER_NOT_FOUND',
          message: 'No billing information found for this user',
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle errors when getting billing status', async () => {
      mockRequest = {
        params: { userId: 'user-123' }
      };

      const error = new Error('Stripe API error');
      mockBillingIntegration.getCustomerByUserId.mockRejectedValue(error);

      await billingController.getBillingStatus(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'BILLING_STATUS_ERROR',
          message: 'Failed to retrieve billing status',
          details: { message: 'Stripe API error' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should trim whitespace from userId', async () => {
      mockRequest = {
        params: { userId: '  user-123  ' }
      };

      const mockCustomer = {
        id: 'cus_123',
        email: '<EMAIL>',
        metadata: { userId: 'user-123' },
        object: 'customer',
        balance: 0,
        created: Date.now(),
        default_source: null,
        delinquent: false,
        description: null,
        discount: null,
        invoice_prefix: null,
        livemode: false
      } as any;

      mockBillingIntegration.getCustomerByUserId.mockResolvedValue(mockCustomer);

      await billingController.getBillingStatus(mockRequest as Request, mockResponse as Response);

      expect(mockBillingIntegration.getCustomerByUserId).toHaveBeenCalledWith('user-123');
    });
  });
});