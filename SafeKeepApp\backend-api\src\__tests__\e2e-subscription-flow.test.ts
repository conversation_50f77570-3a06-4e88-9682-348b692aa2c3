import request from 'supertest';
import app from '../app';
import { supabase } from '../utils/database';

describe('End-to-End Subscription Flow', () => {
  let testUserId: string;
  let testSubscriptionId: string;
  let testPaymentMethodId: string;
  let authToken: string;

  beforeAll(async () => {
    // Setup test user and authentication
    testUserId = 'test-user-e2e-' + Date.now();
    testPaymentMethodId = 'pm_card_visa';
    authToken = 'Bearer test-jwt-token';

    // Mock authentication middleware to accept our test token
    jest.spyOn(require('../middleware/auth'), 'authenticateToken').mockImplementation((req: any, res: any, next: any) => {
      req.user = { id: testUserId, email: '<EMAIL>' };
      next();
    });
  });

  afterAll(async () => {
    // Cleanup test data
    if (testSubscriptionId) {
      await request(app)
        .delete(`/api/subscriptions/${testSubscriptionId}`)
        .set('Authorization', authToken);
    }
  });

  describe('Complete Subscription Flow', () => {
    it('should complete the full subscription journey', async () => {
      // Step 1: User gets available service combinations
      const combinationsResponse = await request(app)
        .get('/api/pricing/combinations')
        .set('Authorization', authToken)
        .expect(200);

      expect(combinationsResponse.body).toHaveProperty('combinations');
      expect(Array.isArray(combinationsResponse.body.combinations)).toBe(true);
      expect(combinationsResponse.body.combinations.length).toBeGreaterThan(0);

      // Step 2: User selects services and gets pricing calculation
      const selectedServices = ['contacts', 'messages'];
      const pricingResponse = await request(app)
        .post('/api/pricing/calculate')
        .set('Authorization', authToken)
        .send({ serviceIds: selectedServices })
        .expect(200);

      expect(pricingResponse.body).toHaveProperty('recommendedPlanId');
      expect(pricingResponse.body).toHaveProperty('priceCents');
      expect(pricingResponse.body.priceCents).toBeGreaterThan(0);

      const recommendedPlan = pricingResponse.body;

      // Step 3: User validates service combination
      const validationResponse = await request(app)
        .post('/api/services/validate')
        .set('Authorization', authToken)
        .send({ serviceIds: selectedServices })
        .expect(200);

      expect(validationResponse.body.isValid).toBe(true);
      expect(validationResponse.body.errors).toHaveLength(0);

      // Step 4: User creates subscription with payment processing
      const subscriptionResponse = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: selectedServices,
          paymentMethodId: testPaymentMethodId
        })
        .expect(201);

      expect(subscriptionResponse.body).toHaveProperty('subscriptionId');
      expect(subscriptionResponse.body).toHaveProperty('status', 'active');
      expect(subscriptionResponse.body.services).toEqual(expect.arrayContaining(selectedServices));

      testSubscriptionId = subscriptionResponse.body.subscriptionId;

      // Step 5: Verify subscription details
      const subscriptionDetailsResponse = await request(app)
        .get(`/api/subscriptions/${testUserId}`)
        .set('Authorization', authToken)
        .expect(200);

      expect(subscriptionDetailsResponse.body).toHaveProperty('subscriptionId', testSubscriptionId);
      expect(subscriptionDetailsResponse.body).toHaveProperty('planId', recommendedPlan.recommendedPlanId);
      expect(subscriptionDetailsResponse.body.services).toEqual(expect.arrayContaining(selectedServices));

      // Step 6: Verify service access validation after subscription activation
      for (const service of selectedServices) {
        const accessResponse = await request(app)
          .get(`/api/services/access/${testUserId}/${service}`)
          .set('Authorization', authToken)
          .expect(200);

        expect(accessResponse.body.hasAccess).toBe(true);
        expect(accessResponse.body).toHaveProperty('planName');
        expect(accessResponse.body).toHaveProperty('expiresAt');
      }

      // Step 7: Verify user's active services
      const userServicesResponse = await request(app)
        .get(`/api/services/user/${testUserId}`)
        .set('Authorization', authToken)
        .expect(200);

      expect(userServicesResponse.body.services).toEqual(expect.arrayContaining(selectedServices));
      expect(userServicesResponse.body.services).toHaveLength(selectedServices.length);

      // Step 8: Test subscription updates (service changes)
      const updatedServices = ['contacts', 'messages', 'photos'];
      const updateResponse = await request(app)
        .put(`/api/subscriptions/${testSubscriptionId}`)
        .set('Authorization', authToken)
        .send({ serviceIds: updatedServices })
        .expect(200);

      expect(updateResponse.body.services).toEqual(expect.arrayContaining(updatedServices));
      expect(updateResponse.body.services).toHaveLength(updatedServices.length);

      // Step 9: Verify updated service access
      const photosAccessResponse = await request(app)
        .get(`/api/services/access/${testUserId}/photos`)
        .set('Authorization', authToken)
        .expect(200);

      expect(photosAccessResponse.body.hasAccess).toBe(true);

      // Step 10: Verify billing status
      const billingStatusResponse = await request(app)
        .get(`/api/billing/status/${testUserId}`)
        .set('Authorization', authToken)
        .expect(200);

      expect(billingStatusResponse.body).toHaveProperty('status');
      expect(billingStatusResponse.body).toHaveProperty('nextBillingDate');
      expect(billingStatusResponse.body).toHaveProperty('currentAmount');
    });

    it('should handle pricing recommendations correctly', async () => {
      // Test pricing recommendations for existing user
      const recommendationsResponse = await request(app)
        .get(`/api/pricing/recommendations/${testUserId}`)
        .set('Authorization', authToken)
        .expect(200);

      expect(recommendationsResponse.body).toHaveProperty('recommendations');
      expect(Array.isArray(recommendationsResponse.body.recommendations)).toBe(true);
    });

    it('should handle payment intent creation for service combinations', async () => {
      const paymentIntentResponse = await request(app)
        .post('/api/billing/payment-intent')
        .set('Authorization', authToken)
        .send({
          serviceIds: ['contacts', 'messages'],
          amount: 999
        })
        .expect(200);

      expect(paymentIntentResponse.body).toHaveProperty('clientSecret');
      expect(paymentIntentResponse.body).toHaveProperty('paymentIntentId');
      expect(paymentIntentResponse.body.metadata).toHaveProperty('serviceIds');
      expect(paymentIntentResponse.body.metadata.serviceIds).toContain('contacts');
      expect(paymentIntentResponse.body.metadata.serviceIds).toContain('messages');
    });
  });

  describe('Service Access Validation Flow', () => {
    it('should properly validate service access throughout subscription lifecycle', async () => {
      // Create a new subscription for this test
      const services = ['contacts'];
      const subscriptionResponse = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: services,
          paymentMethodId: testPaymentMethodId
        })
        .expect(201);

      const subscriptionId = subscriptionResponse.body.subscriptionId;

      // Verify access is granted
      const accessResponse = await request(app)
        .get(`/api/services/access/${testUserId}/contacts`)
        .set('Authorization', authToken)
        .expect(200);

      expect(accessResponse.body.hasAccess).toBe(true);

      // Test access to non-subscribed service
      const noAccessResponse = await request(app)
        .get(`/api/services/access/${testUserId}/photos`)
        .set('Authorization', authToken)
        .expect(200);

      expect(noAccessResponse.body.hasAccess).toBe(false);

      // Cancel subscription
      await request(app)
        .delete(`/api/subscriptions/${subscriptionId}`)
        .set('Authorization', authToken)
        .expect(200);

      // Verify access is revoked after cancellation
      const revokedAccessResponse = await request(app)
        .get(`/api/services/access/${testUserId}/contacts`)
        .set('Authorization', authToken)
        .expect(200);

      expect(revokedAccessResponse.body.hasAccess).toBe(false);
    });
  });

  describe('Subscription Management Flow', () => {
    it('should handle subscription upgrades and downgrades', async () => {
      // Start with basic service
      const initialServices = ['contacts'];
      const subscriptionResponse = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: initialServices,
          paymentMethodId: testPaymentMethodId
        })
        .expect(201);

      const subscriptionId = subscriptionResponse.body.subscriptionId;

      // Upgrade to more services
      const upgradeServices = ['contacts', 'messages', 'photos'];
      const upgradeResponse = await request(app)
        .put(`/api/subscriptions/${subscriptionId}`)
        .set('Authorization', authToken)
        .send({ serviceIds: upgradeServices })
        .expect(200);

      expect(upgradeResponse.body.services).toEqual(expect.arrayContaining(upgradeServices));

      // Verify all services are accessible
      for (const service of upgradeServices) {
        const accessResponse = await request(app)
          .get(`/api/services/access/${testUserId}/${service}`)
          .set('Authorization', authToken)
          .expect(200);

        expect(accessResponse.body.hasAccess).toBe(true);
      }

      // Downgrade to fewer services
      const downgradeServices = ['contacts'];
      const downgradeResponse = await request(app)
        .put(`/api/subscriptions/${subscriptionId}`)
        .set('Authorization', authToken)
        .send({ serviceIds: downgradeServices })
        .expect(200);

      expect(downgradeResponse.body.services).toEqual(downgradeServices);

      // Verify downgraded services are no longer accessible
      const noAccessResponse = await request(app)
        .get(`/api/services/access/${testUserId}/photos`)
        .set('Authorization', authToken)
        .expect(200);

      expect(noAccessResponse.body.hasAccess).toBe(false);

      // Cleanup
      await request(app)
        .delete(`/api/subscriptions/${subscriptionId}`)
        .set('Authorization', authToken)
        .expect(200);
    });
  });
});