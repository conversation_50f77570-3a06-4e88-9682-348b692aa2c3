/**
 * Progressive Web App Manager for SafeKeep Web Demo
 * Handles service worker, offline functionality, app installation, and PWA features
 */

class PWAManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.installPrompt = null;
        this.swRegistration = null;
        this.updateAvailable = false;
        
        this.init();
    }
    
    async init() {
        this.setupOnlineOfflineHandling();
        await this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupUpdateHandling();
        this.createPWAUI();
        this.setupNotifications();
        this.setupBackgroundSync();
        
        // Check if app is running as PWA
        this.detectPWAMode();
    }
    
    setupOnlineOfflineHandling() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStatus(true);
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOnlineStatus(false);
        });
        
        // Initial status
        this.handleOnlineStatus(this.isOnline);
    }
    
    handleOnlineStatus(isOnline) {
        const statusIndicator = this.getOrCreateStatusIndicator();
        
        if (isOnline) {
            statusIndicator.className = 'pwa-status online';
            statusIndicator.textContent = 'Online';
            statusIndicator.setAttribute('aria-label', 'Application is online');
            
            // Sync any pending data
            this.syncPendingData();
            
            // Announce to screen readers
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('Connection restored. Application is now online.', 'polite');
            }
        } else {
            statusIndicator.className = 'pwa-status offline';
            statusIndicator.textContent = 'Offline';
            statusIndicator.setAttribute('aria-label', 'Application is offline');
            
            // Show offline capabilities
            this.showOfflineCapabilities();
            
            // Announce to screen readers
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('Connection lost. Application is now in offline mode.', 'assertive');
            }
        }
        
        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('onlinestatuschange', {
            detail: { isOnline }
        }));
    }
    
    getOrCreateStatusIndicator() {
        let indicator = document.getElementById('pwa-status-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'pwa-status-indicator';
            indicator.className = 'pwa-status';
            indicator.setAttribute('role', 'status');
            indicator.setAttribute('aria-live', 'polite');
            
            // Position in header or create floating indicator
            const header = document.querySelector('.header');
            if (header) {
                header.appendChild(indicator);
            } else {
                indicator.style.cssText = `
                    position: fixed;
                    top: 10px;
                    left: 50%;
                    transform: translateX(-50%);
                    z-index: 1000;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 600;
                `;
                document.body.appendChild(indicator);
            }
        }
        return indicator;
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.swRegistration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });
                
                console.log('Service Worker registered successfully:', this.swRegistration);
                
                // Listen for service worker updates
                this.swRegistration.addEventListener('updatefound', () => {
                    this.handleServiceWorkerUpdate();
                });
                
                // Check for existing service worker
                if (this.swRegistration.active) {
                    this.setupMessageChannel();
                }
                
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }
    
    handleServiceWorkerUpdate() {
        const newWorker = this.swRegistration.installing;
        
        newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                this.updateAvailable = true;
                this.showUpdateNotification();
            }
        });
    }
    
    showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'pwa-update-notification';
        notification.innerHTML = `
            <div class="update-content">
                <span>A new version is available!</span>
                <button class="btn btn-sm btn-primary" id="update-app">Update</button>
                <button class="btn btn-sm btn-secondary" id="dismiss-update">Later</button>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            max-width: 400px;
        `;
        
        document.body.appendChild(notification);
        
        // Event listeners
        notification.querySelector('#update-app').addEventListener('click', () => {
            this.applyUpdate();
            notification.remove();
        });
        
        notification.querySelector('#dismiss-update').addEventListener('click', () => {
            notification.remove();
        });
        
        // Auto-dismiss after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }
    
    async applyUpdate() {
        if (this.swRegistration && this.swRegistration.waiting) {
            this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            
            // Reload the page to activate the new service worker
            window.location.reload();
        }
    }
    
    setupMessageChannel() {
        navigator.serviceWorker.addEventListener('message', (event) => {
            const { type, payload } = event.data;
            
            switch (type) {
                case 'CACHE_UPDATED':
                    console.log('Cache updated:', payload);
                    break;
                case 'OFFLINE_FALLBACK':
                    this.handleOfflineFallback(payload);
                    break;
                case 'BACKGROUND_SYNC':
                    this.handleBackgroundSync(payload);
                    break;
            }
        });
    }
    
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.installPrompt = e;
            this.showInstallButton();
        });
        
        // Listen for app installation
        window.addEventListener('appinstalled', () => {
            this.installPrompt = null;
            this.hideInstallButton();
            
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('SafeKeep app installed successfully', 'polite');
            }
            
            // Track installation
            this.trackInstallation();
        });
    }
    
    showInstallButton() {
        let installButton = document.getElementById('pwa-install-button');
        if (!installButton) {
            installButton = document.createElement('button');
            installButton.id = 'pwa-install-button';
            installButton.className = 'btn btn-primary btn-sm';
            installButton.innerHTML = '📱 Install App';
            installButton.setAttribute('aria-label', 'Install SafeKeep as an app');
            installButton.title = 'Install SafeKeep as a Progressive Web App';
            
            installButton.addEventListener('click', () => {
                this.promptInstall();
            });
            
            // Add to header or create floating button
            const header = document.querySelector('.header');
            if (header) {
                const installContainer = document.createElement('div');
                installContainer.className = 'install-button-container';
                installContainer.style.cssText = `
                    position: absolute;
                    top: 20px;
                    left: 20px;
                    z-index: 1000;
                `;
                installContainer.appendChild(installButton);
                header.style.position = 'relative';
                header.appendChild(installContainer);
            } else {
                installButton.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    z-index: 1000;
                    box-shadow: var(--shadow-lg);
                `;
                document.body.appendChild(installButton);
            }
        }
        
        installButton.style.display = 'block';
    }
    
    hideInstallButton() {
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.style.display = 'none';
        }
    }
    
    async promptInstall() {
        if (this.installPrompt) {
            this.installPrompt.prompt();
            
            const result = await this.installPrompt.userChoice;
            
            if (result.outcome === 'accepted') {
                console.log('User accepted the install prompt');
            } else {
                console.log('User dismissed the install prompt');
            }
            
            this.installPrompt = null;
        }
    }
    
    setupUpdateHandling() {
        // Check for updates periodically
        setInterval(() => {
            if (this.swRegistration) {
                this.swRegistration.update();
            }
        }, 60000); // Check every minute
        
        // Check for updates when app becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.swRegistration) {
                this.swRegistration.update();
            }
        });
    }
    
    createPWAUI() {
        // Create PWA controls panel
        const panel = document.createElement('div');
        panel.id = 'pwa-controls';
        panel.className = 'pwa-controls-panel';
        panel.innerHTML = `
            <div class="pwa-controls-header">
                <h4>App Controls</h4>
                <button class="btn btn-sm" id="toggle-pwa-controls" aria-label="Toggle PWA controls">−</button>
            </div>
            <div class="pwa-controls-content">
                <div class="pwa-control-item">
                    <label>
                        <input type="checkbox" id="enable-notifications"> 
                        Enable Notifications
                    </label>
                </div>
                <div class="pwa-control-item">
                    <label>
                        <input type="checkbox" id="enable-background-sync"> 
                        Background Sync
                    </label>
                </div>
                <div class="pwa-control-item">
                    <button class="btn btn-sm btn-outline" id="clear-cache">Clear Cache</button>
                </div>
                <div class="pwa-control-item">
                    <button class="btn btn-sm btn-outline" id="force-update">Check for Updates</button>
                </div>
            </div>
        `;
        
        panel.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 250px;
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            display: none;
        `;
        
        document.body.appendChild(panel);
        
        this.setupPWAControlsEvents(panel);
        this.pwaPanelVisible = false;
    }
    
    setupPWAControlsEvents(panel) {
        const toggleButton = panel.querySelector('#toggle-pwa-controls');
        const content = panel.querySelector('.pwa-controls-content');
        
        toggleButton.addEventListener('click', () => {
            const isExpanded = content.style.display !== 'none';
            content.style.display = isExpanded ? 'none' : 'block';
            toggleButton.textContent = isExpanded ? '+' : '−';
            toggleButton.setAttribute('aria-expanded', !isExpanded);
        });
        
        panel.querySelector('#enable-notifications').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.requestNotificationPermission();
            }
        });
        
        panel.querySelector('#enable-background-sync').addEventListener('change', (e) => {
            this.toggleBackgroundSync(e.target.checked);
        });
        
        panel.querySelector('#clear-cache').addEventListener('click', () => {
            this.clearCache();
        });
        
        panel.querySelector('#force-update').addEventListener('click', () => {
            this.checkForUpdates();
        });
        
        // Show panel on Alt + P
        document.addEventListener('keydown', (e) => {
            if (e.altKey && e.key === 'p') {
                e.preventDefault();
                this.togglePWAPanel();
            }
        });
    }
    
    togglePWAPanel() {
        const panel = document.getElementById('pwa-controls');
        this.pwaPanelVisible = !this.pwaPanelVisible;
        panel.style.display = this.pwaPanelVisible ? 'block' : 'none';
        
        if (this.pwaPanelVisible) {
            panel.querySelector('#toggle-pwa-controls').focus();
        }
    }
    
    async setupNotifications() {
        if ('Notification' in window) {
            const permission = await Notification.permission;
            
            if (permission === 'granted') {
                document.getElementById('enable-notifications').checked = true;
            }
        }
    }
    
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                this.showNotification('Notifications enabled', 'You will now receive updates from SafeKeep');
            } else {
                document.getElementById('enable-notifications').checked = false;
            }
        }
    }
    
    showNotification(title, body, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body,
                icon: '/icon-192x192.png',
                badge: '/icon-72x72.png',
                tag: 'safekeep-notification',
                requireInteraction: false,
                ...options
            });
            
            notification.onclick = () => {
                window.focus();
                notification.close();
            };
            
            // Auto-close after 5 seconds
            setTimeout(() => {
                notification.close();
            }, 5000);
            
            return notification;
        }
    }
    
    setupBackgroundSync() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            // Background sync is available
            console.log('Background sync is supported');
        }
    }
    
    toggleBackgroundSync(enabled) {
        if (enabled && this.swRegistration) {
            // Register background sync
            this.swRegistration.sync.register('background-sync');
        }
        
        // Store preference
        localStorage.setItem('background-sync-enabled', enabled);
    }
    
    async clearCache() {
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            await Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
            
            this.showNotification('Cache cleared', 'Application cache has been cleared');
            
            if (window.accessibilityManager) {
                window.accessibilityManager.announce('Application cache cleared', 'polite');
            }
        }
    }
    
    async checkForUpdates() {
        if (this.swRegistration) {
            await this.swRegistration.update();
            
            if (!this.updateAvailable) {
                this.showNotification('No updates', 'You are running the latest version');
            }
        }
    }
    
    detectPWAMode() {
        const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                     window.navigator.standalone ||
                     document.referrer.includes('android-app://');
        
        if (isPWA) {
            document.body.classList.add('pwa-mode');
            
            // Hide install button if running as PWA
            this.hideInstallButton();
            
            // Add PWA-specific styles
            this.addPWAStyles();
        }
        
        return isPWA;
    }
    
    addPWAStyles() {
        const pwaStyles = `
            <style>
            .pwa-mode {
                /* Remove browser chrome compensation */
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
                padding-left: env(safe-area-inset-left);
                padding-right: env(safe-area-inset-right);
            }
            
            .pwa-mode .container {
                border-radius: 0;
                margin: 0;
                min-height: 100vh;
            }
            
            .pwa-mode .header {
                position: sticky;
                top: 0;
                z-index: 100;
            }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', pwaStyles);
    }
    
    showOfflineCapabilities() {
        const offlineMessage = document.createElement('div');
        offlineMessage.className = 'offline-capabilities';
        offlineMessage.innerHTML = `
            <div class="offline-content">
                <h4>You're offline</h4>
                <p>Don't worry! You can still:</p>
                <ul>
                    <li>View previously loaded content</li>
                    <li>Use the demo features</li>
                    <li>Save data locally</li>
                </ul>
                <p>Your changes will sync when you're back online.</p>
            </div>
        `;
        
        offlineMessage.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            z-index: 1000;
            max-width: 400px;
            text-align: center;
        `;
        
        document.body.appendChild(offlineMessage);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (offlineMessage.parentNode) {
                offlineMessage.remove();
            }
        }, 5000);
    }
    
    syncPendingData() {
        // Sync any data that was stored while offline
        const pendingData = this.getPendingData();
        
        if (pendingData.length > 0) {
            this.showNotification('Syncing data', `Syncing ${pendingData.length} pending items`);
            
            // Process pending data
            pendingData.forEach(item => {
                this.processPendingItem(item);
            });
            
            // Clear pending data
            this.clearPendingData();
        }
    }
    
    getPendingData() {
        try {
            const data = localStorage.getItem('pwa-pending-data');
            return data ? JSON.parse(data) : [];
        } catch (e) {
            return [];
        }
    }
    
    addPendingData(item) {
        const pendingData = this.getPendingData();
        pendingData.push({
            ...item,
            timestamp: new Date().toISOString()
        });
        
        try {
            localStorage.setItem('pwa-pending-data', JSON.stringify(pendingData));
        } catch (e) {
            console.error('Failed to store pending data:', e);
        }
    }
    
    clearPendingData() {
        try {
            localStorage.removeItem('pwa-pending-data');
        } catch (e) {
            console.error('Failed to clear pending data:', e);
        }
    }
    
    processPendingItem(item) {
        // Process individual pending items
        console.log('Processing pending item:', item);
        
        // Dispatch event for other components to handle
        window.dispatchEvent(new CustomEvent('pendingdataprocessed', {
            detail: item
        }));
    }
    
    handleOfflineFallback(payload) {
        console.log('Offline fallback triggered:', payload);
        
        // Show offline message
        if (window.accessibilityManager) {
            window.accessibilityManager.announce('Content loaded from cache due to offline status', 'polite');
        }
    }
    
    handleBackgroundSync(payload) {
        console.log('Background sync completed:', payload);
        
        if (payload.success) {
            this.showNotification('Sync complete', 'Your data has been synchronized');
        }
    }
    
    trackInstallation() {
        // Track PWA installation for analytics
        console.log('PWA installed');
        
        // Store installation timestamp
        try {
            localStorage.setItem('pwa-installed', new Date().toISOString());
        } catch (e) {
            console.error('Failed to track installation:', e);
        }
    }
    
    // Utility methods
    isOnlineMode() {
        return this.isOnline;
    }
    
    isPWAInstalled() {
        return this.detectPWAMode();
    }
    
    canInstall() {
        return this.installPrompt !== null;
    }
    
    // Event listeners for other components
    onOnlineStatusChange(callback) {
        window.addEventListener('onlinestatuschange', callback);
        return () => window.removeEventListener('onlinestatuschange', callback);
    }
    
    onPendingDataProcessed(callback) {
        window.addEventListener('pendingdataprocessed', callback);
        return () => window.removeEventListener('pendingdataprocessed', callback);
    }
}

// Add PWA-specific CSS
const pwaStyles = `
<style>
/* PWA Status Indicator */
.pwa-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pwa-status.online {
    background: var(--success-bg);
    color: var(--success-color);
    border: 1px solid var(--success-border);
}

.pwa-status.offline {
    background: var(--warning-bg);
    color: var(--warning-color);
    border: 1px solid var(--warning-border);
}

/* PWA Controls Panel */
.pwa-controls-panel {
    font-size: 14px;
}

.pwa-controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.pwa-controls-header h4 {
    margin: 0;
    font-size: 16px;
}

.pwa-controls-content {
    padding: var(--spacing-md);
}

.pwa-control-item {
    margin-bottom: var(--spacing-md);
}

.pwa-control-item:last-child {
    margin-bottom: 0;
}

.pwa-control-item label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.pwa-control-item input[type="checkbox"] {
    margin: 0;
}

/* Update Notification */
.pwa-update-notification {
    animation: slideUp 0.3s ease-out;
}

.update-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.update-content span {
    flex: 1;
    min-width: 150px;
}

/* Offline Capabilities */
.offline-capabilities {
    animation: fadeIn 0.3s ease-out;
}

.offline-content h4 {
    color: var(--warning-color);
    margin-bottom: var(--spacing-md);
}

.offline-content ul {
    text-align: left;
    margin: var(--spacing-md) 0;
}

.offline-content li {
    margin-bottom: var(--spacing-sm);
}

/* Install Button */
#pwa-install-button {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* PWA Mode Specific Styles */
.pwa-mode .skip-link {
    top: env(safe-area-inset-top, 6px);
}

.pwa-mode .theme-toggle-container {
    top: calc(20px + env(safe-area-inset-top, 0px));
}

.pwa-mode .install-button-container {
    top: calc(20px + env(safe-area-inset-top, 0px));
}

/* Responsive PWA adjustments */
@media (max-width: 576px) {
    .pwa-controls-panel {
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
    }
    
    .update-content {
        flex-direction: column;
        text-align: center;
    }
    
    .offline-capabilities {
        width: calc(100vw - 40px);
        left: 20px;
        right: 20px;
        transform: translate(0, -50%);
    }
}

/* Print styles for PWA */
@media print {
    .pwa-status,
    .pwa-controls-panel,
    .pwa-update-notification,
    .offline-capabilities,
    #pwa-install-button {
        display: none !important;
    }
}
</style>
`;

// Inject PWA styles
document.head.insertAdjacentHTML('beforeend', pwaStyles);

// Initialize PWA manager when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.pwaManager = new PWAManager();
    });
} else {
    window.pwaManager = new PWAManager();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAManager;
}