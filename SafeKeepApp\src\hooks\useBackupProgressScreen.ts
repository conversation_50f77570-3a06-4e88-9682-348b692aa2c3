import { useState } from 'react';

interface ProgressItem {
  label: string;
  progress: number;
  currentItem?: string;
  estimatedTime?: string;
  error?: string;
  retry?: () => void;
}

export function useBackupProgressScreen() {
  const [progressItems, setProgressItems] = useState<ProgressItem[]>(
    [
      {
        label: 'Contacts',
        progress: 0.5,
        currentItem: '<PERSON>',
        estimatedTime: '1 min',
      },
      {
        label: 'Messages',
        progress: 0.2,
        currentItem: 'SMS #12',
        estimatedTime: '3 min',
        error: undefined,
        retry: undefined,
      },
      {
        label: 'Photos',
        progress: 0.8,
        currentItem: 'IMG_1234.jpg',
        estimatedTime: '30 sec',
      },
    ]
  );
  const [isBackupInProgress, setIsBackupInProgress] = useState(true);

  const handleCancel = async () => {
    setIsBackupInProgress(false);
    // Add any additional cancellation logic here
  };

  return {
    progressItems,
    handleCancel,
    isBackupInProgress,
  };
}
