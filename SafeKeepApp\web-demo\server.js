/**
 * Simple HTTP Server for SafeKeep Web Demo
 * Serves the web demo on localhost
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

let PORT = 3000;

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    
    // Parse URL and remove query parameters
    let filePath = req.url.split('?')[0];
    
    // Default to index.html for root requests
    if (filePath === '/') {
        filePath = '/index.html';
    }
    
    // Construct full file path
    const fullPath = path.join(__dirname, filePath);
    
    // Get file extension for MIME type
    const ext = path.extname(filePath).toLowerCase();
    const contentType = mimeTypes[ext] || 'text/plain';
    
    // Check if file exists and serve it
    fs.readFile(fullPath, (err, data) => {
        if (err) {
            if (err.code === 'ENOENT') {
                // File not found
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end(`
                    <html>
                        <head><title>404 - Not Found</title></head>
                        <body>
                            <h1>404 - File Not Found</h1>
                            <p>The requested file <code>${filePath}</code> was not found.</p>
                            <p><a href="/">Go back to SafeKeep Demo</a></p>
                        </body>
                    </html>
                `);
            } else {
                // Server error
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('Internal Server Error');
            }
            console.error(`Error serving ${filePath}:`, err.message);
        } else {
            // Serve the file
            res.writeHead(200, { 
                'Content-Type': contentType,
                'Cache-Control': 'no-cache' // Disable caching for development
            });
            res.end(data);
        }
    });
});

server.listen(PORT, () => {
    console.log('🚀 SafeKeep Web Demo Server Started!');
    console.log('=' .repeat(50));
    console.log(`📡 Server running at: http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${__dirname}`);
    console.log('🔗 Open your browser and navigate to the URL above');
    console.log('=' .repeat(50));
    console.log('\n🎯 Demo Features:');
    console.log('• Test Supabase database connection');
    console.log('• Test storage bucket access');
    console.log('• Simulate user authentication');
    console.log('• Demo contact backup functionality');
    console.log('• Demo message backup functionality');
    console.log('• Demo photo backup functionality');
    console.log('• Run full backup simulation');
    console.log('\n📝 Activity will be logged here...\n');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use.`);
        console.log('💡 Try one of these solutions:');
        console.log(`   • Use a different port: node server.js --port 3001`);
        console.log(`   • Stop the process using port ${PORT}`);
        console.log(`   • Wait a moment and try again`);
    } else {
        console.error('❌ Server error:', err.message);
    }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down SafeKeep Web Demo Server...');
    server.close(() => {
        console.log('✅ Server closed successfully');
        process.exit(0);
    });
});

// Handle command line arguments for custom port
const args = process.argv.slice(2);
const portIndex = args.indexOf('--port');
if (portIndex !== -1 && args[portIndex + 1]) {
    const customPort = parseInt(args[portIndex + 1]);
    if (!isNaN(customPort)) {
        PORT = customPort;
    }
}