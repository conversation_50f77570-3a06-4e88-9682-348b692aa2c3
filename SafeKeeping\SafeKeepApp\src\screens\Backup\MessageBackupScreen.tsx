import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { 
  <PERSON>ton, 
  Card, 
  Text, 
  ProgressBar, 
  Chip, 
  ActivityIndicator 
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import MessageBackupService, { MessageData, MessageBackupProgress, MessageBackupResult, MessageThread } from '../../services/MessageBackupService';
import { COLORS, SPACING } from '../../utils/constants';

const MessageBackupScreen = () => {
  const [messages, setMessages] = useState<MessageData[]>([]);
  const [threads, setThreads] = useState<MessageThread[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [backupProgress, setBackupProgress] = useState<MessageBackupProgress | null>(null);
  const [backupResult, setBackupResult] = useState<MessageBackupResult | null>(null);
  const [backupStats, setBackupStats] = useState<any>(null);
  const [scanComplete, setScanComplete] = useState(false);

  useEffect(() => {
    loadBackupStats();
    handleScanMessages();
  }, []);

  const loadBackupStats = async () => {
    try {
      const stats = await MessageBackupService.getBackupStats();
      setBackupStats(stats);
    } catch (error) {
      console.error('Failed to load backup stats:', error);
    }
  };

  const handleScanMessages = async () => {
    setIsScanning(true);
    setScanComplete(false);
    setBackupResult(null);
    
    try {
      console.log('💬 Starting message scan...');
      
      if (Platform.OS === 'ios') {
        Alert.alert(
          'iOS Limitation',
          'iOS does not allow third-party apps to access SMS messages for privacy reasons.\n\nFor this demo, we\'ll show simulated message data.',
          [{ text: 'Continue with Demo' }]
        );
      }
      
      const scannedMessages = await MessageBackupService.scanMessages();
      const messageThreads = MessageBackupService.groupMessagesByThread(scannedMessages);
      
      setMessages(scannedMessages);
      setThreads(messageThreads);
      setScanComplete(true);
      
      Alert.alert(
        '💬 Messages Found!',
        `Found ${scannedMessages.length} messages in ${messageThreads.length} conversations.\n\nReady to backup your important text messages?`,
        [{ text: 'Great!' }]
      );
    } catch (error) {
      console.error('Message scan error:', error);
      Alert.alert(
        'Scan Error',
        'Could not access your messages. Please check permissions and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Try Again', onPress: handleScanMessages }
        ]
      );
    } finally {
      setIsScanning(false);
    }
  };

  const handleStartBackup = async () => {
    if (messages.length === 0) {
      Alert.alert(
        'No Messages Found',
        'Please scan for messages first before starting backup.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      '🚀 Start Message Backup?',
      `This will backup ${messages.length} messages from ${threads.length} conversations.\n\n• All conversations will be preserved\n• Messages will be encrypted for security\n• You can restore on any device\n• Conversation history maintained\n\nStart backup now?`,
      [
        { text: 'Not Now', style: 'cancel' },
        { text: 'Start Backup', onPress: startBackupProcess }
      ]
    );
  };

  const startBackupProcess = async () => {
    setIsBackingUp(true);
    setBackupResult(null);
    
    try {
      const result = await MessageBackupService.backupMessages(
        messages,
        (progress: MessageBackupProgress) => {
          setBackupProgress(progress);
        }
      );
      
      setBackupResult(result);
      await loadBackupStats(); // Refresh stats
      showBackupCompleteDialog(result);
    } catch (error) {
      console.error('Backup error:', error);
      Alert.alert(
        'Backup Error',
        'Something went wrong during backup. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsBackingUp(false);
      setBackupProgress(null);
    }
  };

  const showBackupCompleteDialog = (result: MessageBackupResult) => {
    const minutes = Math.round(result.duration / 60000);
    const seconds = Math.round((result.duration % 60000) / 1000);
    const timeString = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;

    if (result.success) {
      Alert.alert(
        '🎉 Backup Complete!',
        `Successfully backed up ${result.backedUpMessages} messages in ${timeString}!\n\n` +
        `• ${result.totalThreads} conversations preserved\n` +
        `• All your messages are now safely stored in the cloud\n` +
        `• You can restore them on any device!`,
        [{ text: 'Wonderful!' }]
      );
    } else {
      Alert.alert(
        '⚠️ Backup Had Issues',
        `Backup completed with some problems.\n\n` +
        `• ${result.skippedMessages} messages had errors\n` +
        `• Time taken: ${timeString}\n\n` +
        `Would you like to try again?`,
        [
          { text: 'Later', style: 'cancel' },
          { text: 'Try Again', onPress: handleStartBackup }
        ]
      );
    }
  };

  const handleRestoreMessages = async () => {
    Alert.alert(
      '💬 Restore Messages?',
      'This will download your latest message backup from the cloud.\n\nNote: This will show you the conversations for review.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Restore', onPress: performRestore }
      ]
    );
  };

  const performRestore = async () => {
    try {
      const result = await MessageBackupService.restoreMessages();
      
      if (result.success) {
        Alert.alert(
          '✅ Restore Complete!',
          `Successfully restored ${result.threads?.length || 0} conversation threads from your backup.\n\nYou can now review your message history.`,
          [{ text: 'Great!' }]
        );
      } else {
        Alert.alert(
          'Restore Failed',
          result.error || 'Could not restore messages from backup.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Restore Error',
        'Something went wrong during restore. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return COLORS.success;
      case 'error': return COLORS.error;
      default: return COLORS.primary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scanning': return 'magnify';
      case 'processing': return 'cog';
      case 'uploading': return 'cloud-upload';
      case 'completed': return 'check-circle';
      case 'error': return 'alert-circle';
      default: return 'information';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Message Backup',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        {/* Message Library Status */}
        <Card style={styles.statusCard}>
          <Card.Content>
            <View style={styles.statusHeader}>
              <Icon name="message-text" size={32} color={COLORS.primary} />
              <Text variant="titleLarge" style={styles.statusTitle}>
                Message Library
              </Text>
            </View>

            {Platform.OS === 'ios' && (
              <View style={styles.iosWarning}>
                <Icon name="information" size={16} color={COLORS.warning} />
                <Text style={styles.iosWarningText}>
                  iOS Demo Mode: Showing simulated message data
                </Text>
              </View>
            )}

            {isScanning ? (
              <View style={styles.scanningContainer}>
                <ActivityIndicator size="large" color={COLORS.primary} />
                <Text style={styles.scanningText}>Scanning your messages...</Text>
              </View>
            ) : (
              <View style={styles.messageStats}>
                <View style={styles.statRow}>
                  <Text style={styles.statLabel}>Messages Found:</Text>
                  <Chip style={styles.statChip}>{messages.length}</Chip>
                </View>
                
                {scanComplete && (
                  <>
                    <View style={styles.statRow}>
                      <Text style={styles.statLabel}>Conversations:</Text>
                      <Text style={styles.statValue}>{threads.length}</Text>
                    </View>
                    
                    <View style={styles.statRow}>
                      <Text style={styles.statLabel}>Most Recent:</Text>
                      <Text style={styles.statValue}>
                        {threads.length > 0 ? new Date(threads[0].lastMessageDate).toLocaleDateString() : 'N/A'}
                      </Text>
                    </View>
                    
                    <Button
                      mode="outlined"
                      onPress={handleScanMessages}
                      style={styles.rescanButton}
                      icon="refresh"
                    >
                      Rescan Messages
                    </Button>
                  </>
                )}
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Backup Progress */}
        {(isBackingUp || backupProgress) && (
          <Card style={styles.progressCard}>
            <Card.Content>
              <View style={styles.progressHeader}>
                <Icon 
                  name={getStatusIcon(backupProgress?.status || 'uploading')} 
                  size={24} 
                  color={getStatusColor(backupProgress?.status || 'uploading')} 
                />
                <Text variant="titleMedium" style={styles.progressTitle}>
                  {backupProgress?.status === 'scanning' && 'Scanning Messages...'}
                  {backupProgress?.status === 'processing' && 'Processing Conversations...'}
                  {backupProgress?.status === 'uploading' && 'Uploading Messages...'}
                  {backupProgress?.status === 'completed' && 'Backup Complete!'}
                  {backupProgress?.status === 'error' && 'Backup Error'}
                </Text>
              </View>

              {backupProgress && (
                <>
                  <Text style={styles.currentThread}>
                    {backupProgress.currentThread}
                  </Text>

                  <ProgressBar 
                    progress={backupProgress.percentage / 100} 
                    color={getStatusColor(backupProgress.status)}
                    style={styles.progressBar}
                  />

                  <View style={styles.progressStats}>
                    <Text style={styles.progressText}>
                      {backupProgress.processedMessages} of {backupProgress.totalMessages} messages
                    </Text>
                    <Text style={styles.progressText}>
                      {backupProgress.percentage}%
                    </Text>
                  </View>
                </>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Backup Statistics */}
        {backupStats && backupStats.totalBackups > 0 && (
          <Card style={styles.statsCard}>
            <Card.Content>
              <View style={styles.statsHeader}>
                <Icon name="chart-line" size={24} color={COLORS.primary} />
                <Text variant="titleMedium" style={styles.statsTitle}>
                  Backup History
                </Text>
              </View>

              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{backupStats.totalBackups}</Text>
                  <Text style={styles.statLabel}>Total Backups</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{backupStats.totalMessages}</Text>
                  <Text style={styles.statLabel}>Messages Backed Up</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{backupStats.totalThreads}</Text>
                  <Text style={styles.statLabel}>Conversations</Text>
                </View>
              </View>

              {backupStats.lastBackupDate && (
                <Text style={styles.lastBackupText}>
                  Last backup: {new Date(backupStats.lastBackupDate).toLocaleDateString()}
                </Text>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Action Buttons */}
        <Card style={styles.actionCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.actionTitle}>
              Ready to Backup Your Messages?
            </Text>
            
            <Text style={styles.actionDescription}>
              Your text messages and conversations will be safely encrypted and stored in the cloud. 
              You can restore them on any device anytime.
            </Text>

            <View style={styles.actionButtons}>
              {!isBackingUp && scanComplete && (
                <Button
                  mode="contained"
                  onPress={handleStartBackup}
                  disabled={messages.length === 0}
                  style={styles.primaryButton}
                  icon="cloud-upload"
                >
                  Start Message Backup
                </Button>
              )}

              {/* Always show restore button - it should be available at all times */}
              <Button
                mode="outlined"
                onPress={handleRestoreMessages}
                style={styles.secondaryButton}
                icon="cloud-download"
              >
                Restore from Backup
              </Button>

              {!scanComplete && (
                <Button
                  mode="outlined"
                  onPress={handleScanMessages}
                  disabled={isScanning}
                  loading={isScanning}
                  style={styles.secondaryButton}
                  icon="magnify"
                >
                  Scan for Messages
                </Button>
              )}
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  statusCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statusTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  iosWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    padding: SPACING.sm,
    borderRadius: 6,
    marginBottom: SPACING.md,
  },
  iosWarningText: {
    marginLeft: SPACING.xs,
    color: COLORS.warning,
    fontSize: 12,
  },
  scanningContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  scanningText: {
    marginTop: SPACING.sm,
    color: COLORS.textSecondary,
  },
  messageStats: {
    gap: SPACING.sm,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    color: COLORS.text,
    fontSize: 16,
  },
  statValue: {
    color: COLORS.textSecondary,
    fontSize: 16,
  },
  statChip: {
    backgroundColor: COLORS.primary,
  },
  rescanButton: {
    marginTop: SPACING.sm,
    borderColor: COLORS.primary,
  },
  progressCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  progressTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  currentThread: {
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    fontSize: 14,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: SPACING.sm,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressText: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  statsCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statsTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  lastBackupText: {
    textAlign: 'center',
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  actionCard: {
    elevation: 4,
  },
  actionTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  actionDescription: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  actionButtons: {
    gap: SPACING.sm,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    borderColor: COLORS.primary,
  },
});

export default MessageBackupScreen;
