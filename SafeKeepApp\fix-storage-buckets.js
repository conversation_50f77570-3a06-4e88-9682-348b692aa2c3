/**
 * Fix Storage Buckets
 * Creates the required storage buckets for the SafeKeep app
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const serviceKey = process.env.REACT_APP_SUPABASE_SERVICE_KEY;

// Use service key for admin operations
const supabase = createClient(supabaseUrl, serviceKey);

async function createStorageBuckets() {
  console.log('🗄️  Creating Storage Buckets...\n');

  const bucketsToCreate = [
    {
      name: 'user-data',
      options: {
        public: false,
        fileSizeLimit: 52428800, // 50MB
        allowedMimeTypes: ['image/*', 'text/*', 'application/json']
      }
    },
    {
      name: 'thumbnails',
      options: {
        public: true,
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: ['image/*']
      }
    }
  ];

  // First, check existing buckets
  const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();
  
  if (listError) {
    console.error('❌ Failed to list existing buckets:', listError.message);
    return;
  }

  console.log('📦 Existing buckets:', existingBuckets.map(b => b.name));

  for (const bucket of bucketsToCreate) {
    const exists = existingBuckets.some(b => b.name === bucket.name);
    
    if (exists) {
      console.log(`✅ Bucket '${bucket.name}' already exists`);
      continue;
    }

    console.log(`🔧 Creating bucket '${bucket.name}'...`);
    
    const { data, error } = await supabase.storage.createBucket(bucket.name, bucket.options);
    
    if (error) {
      console.error(`❌ Failed to create bucket '${bucket.name}':`, error.message);
    } else {
      console.log(`✅ Created bucket '${bucket.name}' successfully`);
    }
  }

  // Create storage policies for the buckets
  console.log('\n🔒 Setting up storage policies...');
  
  const policies = [
    // User data bucket policies
    {
      bucket: 'user-data',
      policy: `
        CREATE POLICY "Users can upload their own files" ON storage.objects
        FOR INSERT WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);
      `
    },
    {
      bucket: 'user-data',
      policy: `
        CREATE POLICY "Users can view their own files" ON storage.objects
        FOR SELECT USING (auth.uid()::text = (storage.foldername(name))[1]);
      `
    },
    {
      bucket: 'user-data',
      policy: `
        CREATE POLICY "Users can delete their own files" ON storage.objects
        FOR DELETE USING (auth.uid()::text = (storage.foldername(name))[1]);
      `
    },
    // Thumbnails bucket policies (public read)
    {
      bucket: 'thumbnails',
      policy: `
        CREATE POLICY "Anyone can view thumbnails" ON storage.objects
        FOR SELECT USING (bucket_id = 'thumbnails');
      `
    },
    {
      bucket: 'thumbnails',
      policy: `
        CREATE POLICY "Users can upload thumbnails" ON storage.objects
        FOR INSERT WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);
      `
    }
  ];

  for (const policyDef of policies) {
    try {
      console.log(`🔧 Creating policy for ${policyDef.bucket}...`);
      
      // Note: In a real implementation, you'd execute these policies via SQL
      // For now, we'll just log them
      console.log(`ℹ️  Policy for ${policyDef.bucket} (execute in Supabase SQL Editor):`);
      console.log(policyDef.policy.trim());
      
    } catch (error) {
      console.log(`⚠️  Policy creation note: Execute policies manually in Supabase Dashboard`);
    }
  }

  // Verify buckets were created
  console.log('\n🔍 Verifying bucket creation...');
  const { data: finalBuckets, error: finalError } = await supabase.storage.listBuckets();
  
  if (finalError) {
    console.error('❌ Failed to verify buckets:', finalError.message);
    return;
  }

  const requiredBuckets = ['user-data', 'thumbnails'];
  const availableBuckets = finalBuckets.map(b => b.name);
  const missingBuckets = requiredBuckets.filter(name => !availableBuckets.includes(name));

  console.log('📦 Final bucket list:', availableBuckets);
  
  if (missingBuckets.length === 0) {
    console.log('✅ All required buckets are now available!');
  } else {
    console.log(`❌ Still missing buckets: ${missingBuckets.join(', ')}`);
  }

  console.log('\n📋 Next Steps:');
  console.log('1. ✅ Storage buckets created');
  console.log('2. 🔧 Set up storage policies in Supabase Dashboard > Storage > Policies');
  console.log('3. 🧪 Re-run integration tests');
}

createStorageBuckets().catch(console.error);