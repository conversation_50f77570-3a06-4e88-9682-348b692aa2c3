import { BillingMetadata, PaymentResult, WebhookResult } from '../types/modular-pricing';
export declare class BillingIntegration {
    createPaymentIntent(amount: number, metadata: BillingMetadata): Promise<PaymentResult>;
    processSubscriptionPayment(subscriptionData: any): Promise<PaymentResult>;
    handleWebhook(event: any): Promise<WebhookResult>;
}
//# sourceMappingURL=BillingIntegration.d.ts.map