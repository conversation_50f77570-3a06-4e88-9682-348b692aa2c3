/**
 * Subscription Management Dashboard Styles
 * Comprehensive styling for subscription management interface
 */

/* Dashboard Container */
.subscription-dashboard {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.subscription-header h3 {
    color: #4facfe;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.subscription-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Status Badges */
.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    color: white;
    text-transform: capitalize;
}

.status-badge.free {
    background: #6c757d;
}

.status-badge.basic {
    background: #28a745;
}

.status-badge.premium {
    background: #007bff;
}

.status-badge.enterprise {
    background: #6f42c1;
}

/* Dashboard Content */
.dashboard-content {
    display: grid;
    gap: 25px;
}

.dashboard-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.dashboard-section h4 {
    color: #333;
    margin: 0 0 20px 0;
    font-size: 1.2rem;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #f8f9fa;
}

/* Current Plan Section */
.plan-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.plan-card.current {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.plan-header h5 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.plan-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: #4facfe;
}

.plan-details {
    display: grid;
    gap: 20px;
}

.plan-info {
    display: grid;
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-item .label {
    font-weight: 600;
    color: #666;
}

.info-item .value {
    color: #333;
}

.info-item .value.warning {
    color: #dc3545;
    font-weight: 600;
}

.info-item .value.status-active {
    color: #28a745;
    font-weight: 600;
    text-transform: capitalize;
}

.info-item .value.status-canceled {
    color: #dc3545;
    font-weight: 600;
    text-transform: capitalize;
}

.info-item .value.status-paused {
    color: #ffc107;
    font-weight: 600;
    text-transform: capitalize;
}

/* Plan Features */
.plan-features h6 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.feature-list {
    display: grid;
    gap: 8px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 0.9rem;
}

.feature-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Usage Tracking Section */
.usage-overview {
    display: grid;
    gap: 20px;
}

.usage-period {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4facfe;
}

.period-label {
    font-weight: 600;
    color: #333;
}

.period-dates {
    color: #666;
    font-size: 0.9rem;
}

.usage-metrics {
    display: grid;
    gap: 20px;
}

.usage-metric {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.metric-label {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.metric-value {
    font-weight: 600;
    color: #4facfe;
    font-size: 1.1rem;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    border-radius: 6px;
    transition: width 0.3s ease;
}

.metric-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #666;
    margin-top: 10px;
}

/* Usage Warnings and Recommendations */
.usage-warnings, .usage-recommendations {
    margin-top: 20px;
}

.usage-warnings h6, .usage-recommendations h6 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.warning-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.warning-item.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.warning-item.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.warning-icon {
    font-size: 1.1rem;
}

.recommendation-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    margin-bottom: 10px;
}

.rec-icon {
    font-size: 1.2rem;
}

.rec-message {
    flex: 1;
    color: #1976d2;
    font-size: 0.9rem;
}

.recommendation-item .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    margin: 0;
}

/* Billing History Section */
.billing-overview {
    display: grid;
    gap: 20px;
    margin-bottom: 25px;
}

.billing-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.summary-item {
    text-align: center;
}

.summary-label {
    display: block;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 5px;
}

.summary-value {
    display: block;
    font-size: 1.3rem;
    font-weight: 600;
    color: #4facfe;
}

.billing-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.billing-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Invoice List */
.invoice-list {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.invoice-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
    gap: 15px;
    padding: 15px 20px;
    background: #e9ecef;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.invoice-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
    gap: 15px;
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
    font-size: 0.9rem;
}

.invoice-item:last-child {
    border-bottom: none;
}

.invoice-number {
    font-weight: 600;
    color: #333;
}

.invoice-date {
    color: #666;
}

.invoice-amount {
    font-weight: 600;
    color: #4facfe;
}

.invoice-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: capitalize;
}

.invoice-status.status-paid {
    background: #d4edda;
    color: #155724;
}

.invoice-status.status-pending {
    background: #fff3cd;
    color: #856404;
}

.invoice-status.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.invoice-actions {
    display: flex;
    gap: 8px;
}

.invoice-actions .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
}

.no-invoices {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-invoices p {
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.no-invoices small {
    font-size: 0.85rem;
    color: #999;
}

/* Payment Methods Section */
.payment-methods-overview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.payment-summary {
    color: #666;
    font-size: 0.9rem;
}

.payment-methods-list {
    display: grid;
    gap: 15px;
}

.payment-method-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.payment-method-item.default {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
}

.payment-method-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-brand {
    font-weight: 600;
    color: #4facfe;
    font-size: 0.9rem;
}

.card-number {
    font-family: 'Courier New', monospace;
    color: #333;
    font-size: 0.9rem;
}

.card-expiry {
    color: #666;
    font-size: 0.85rem;
}

.default-badge {
    padding: 4px 8px;
    background: #28a745;
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.payment-method-actions {
    display: flex;
    gap: 8px;
}

.payment-method-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.no-payment-methods {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-payment-methods p {
    margin: 0 0 10px 0;
    font-size: 1rem;
}

.no-payment-methods small {
    display: block;
    font-size: 0.85rem;
    color: #999;
    margin-bottom: 20px;
}

/* Subscription Modification Section */
.subscription-actions {
    display: grid;
    gap: 30px;
}

.action-section h6 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #f8f9fa;
}

/* Plan Options */
.plan-options {
    display: grid;
    gap: 25px;
}

.upgrade-options h7, .downgrade-options h7 {
    display: block;
    margin: 0 0 15px 0;
    color: #666;
    font-size: 1rem;
    font-weight: 600;
}

.plan-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.2s ease;
}

.plan-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.plan-option.upgrade {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
}

.plan-option.downgrade {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
}

.plan-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.plan-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.plan-price {
    font-weight: 600;
    color: #4facfe;
    font-size: 1rem;
}

.plan-benefits, .plan-limitations {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin: 0 20px;
    font-size: 0.9rem;
}

.plan-benefits span {
    color: #28a745;
}

.plan-limitations span {
    color: #dc3545;
}

.plan-option .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
    white-space: nowrap;
}

/* Management Actions */
.management-actions {
    display: grid;
    gap: 15px;
}

.action-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.action-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.action-title {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.action-description {
    color: #666;
    font-size: 0.85rem;
}

.action-item .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
    white-space: nowrap;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    color: #333;
    font-weight: 600;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #28a745;
}

.notification.error {
    background: #dc3545;
}

.notification.warning {
    background: #ffc107;
    color: #333;
}

.notification.info {
    background: #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .subscription-dashboard {
        padding: 15px;
        margin: 10px 0;
    }
    
    .subscription-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .plan-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .billing-summary {
        grid-template-columns: 1fr;
    }
    
    .invoice-header, .invoice-item {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .invoice-header {
        display: none;
    }
    
    .invoice-item {
        display: block;
        padding: 15px;
    }
    
    .invoice-item > * {
        display: block;
        margin-bottom: 8px;
    }
    
    .invoice-actions {
        margin-top: 10px;
    }
    
    .payment-method-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .plan-option {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .action-item {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .billing-actions {
        justify-content: center;
    }
    
    .payment-methods-overview {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .subscription-dashboard {
        padding: 10px;
        margin: 5px 0;
    }
    
    .dashboard-section {
        padding: 15px;
    }
    
    .plan-card {
        padding: 15px;
    }
    
    .usage-metric {
        padding: 15px;
    }
    
    .invoice-item, .payment-method-item, .plan-option, .action-item {
        padding: 15px;
    }
    
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Print Styles */
@media print {
    .subscription-dashboard {
        box-shadow: none;
        background: white;
    }
    
    .btn, .loading-overlay, .notification {
        display: none !important;
    }
    
    .dashboard-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}