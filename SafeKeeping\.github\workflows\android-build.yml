name: Android Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
        cache: gradle
    
    - name: <PERSON> execute permission for gradlew
      run: chmod +x SafeKeepApp/android/gradlew
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd SafeKeepApp
        npm ci
    
    - name: Build Android
      run: |
        cd SafeKeepApp
        npm run build:android
      env:
        CI: true