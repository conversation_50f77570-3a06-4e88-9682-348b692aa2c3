#!/usr/bin/env python3
"""
Simple Stripe MCP Server
Provides basic Stripe API functionality through MCP protocol
"""

import asyncio
import json
import os
import sys
from typing import Any, Dict, List, Optional

import stripe
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Initialize Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

if not stripe.api_key:
    print("Error: STRIPE_SECRET_KEY environment variable is required", file=sys.stderr)
    sys.exit(1)

if not stripe.api_key.startswith("sk_test_"):
    print("Warning: Using live Stripe key. Consider using test key for development.", file=sys.stderr)

server = Server("stripe-mcp-server")

@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available Stripe tools."""
    return [
        Tool(
            name="list_customers",
            description="List Stripe customers",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "number",
                        "description": "Number of customers to retrieve (max 100)",
                        "default": 10
                    },
                    "starting_after": {
                        "type": "string",
                        "description": "Cursor for pagination"
                    }
                }
            }
        ),
        Tool(
            name="get_customer",
            description="Get a specific Stripe customer",
            inputSchema={
                "type": "object",
                "properties": {
                    "customer_id": {
                        "type": "string",
                        "description": "The Stripe customer ID",
                        "required": True
                    }
                },
                "required": ["customer_id"]
            }
        ),
        Tool(
            name="list_payment_intents",
            description="List Stripe payment intents",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "number",
                        "description": "Number of payment intents to retrieve (max 100)",
                        "default": 10
                    },
                    "customer": {
                        "type": "string",
                        "description": "Filter by customer ID"
                    }
                }
            }
        ),
        Tool(
            name="get_payment_intent",
            description="Get a specific Stripe payment intent",
            inputSchema={
                "type": "object",
                "properties": {
                    "payment_intent_id": {
                        "type": "string",
                        "description": "The Stripe payment intent ID",
                        "required": True
                    }
                },
                "required": ["payment_intent_id"]
            }
        ),
        Tool(
            name="list_subscriptions",
            description="List Stripe subscriptions",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "number",
                        "description": "Number of subscriptions to retrieve (max 100)",
                        "default": 10
                    },
                    "customer": {
                        "type": "string",
                        "description": "Filter by customer ID"
                    },
                    "status": {
                        "type": "string",
                        "description": "Filter by subscription status",
                        "enum": ["active", "past_due", "unpaid", "canceled", "incomplete", "incomplete_expired", "trialing"]
                    }
                }
            }
        ),
        Tool(
            name="get_subscription",
            description="Get a specific Stripe subscription",
            inputSchema={
                "type": "object",
                "properties": {
                    "subscription_id": {
                        "type": "string",
                        "description": "The Stripe subscription ID",
                        "required": True
                    }
                },
                "required": ["subscription_id"]
            }
        ),
        Tool(
            name="list_products",
            description="List Stripe products",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "number",
                        "description": "Number of products to retrieve (max 100)",
                        "default": 10
                    },
                    "active": {
                        "type": "boolean",
                        "description": "Filter by active status"
                    }
                }
            }
        ),
        Tool(
            name="get_product",
            description="Get a specific Stripe product",
            inputSchema={
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "The Stripe product ID",
                        "required": True
                    }
                },
                "required": ["product_id"]
            }
        ),
        Tool(
            name="list_prices",
            description="List Stripe prices",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "number",
                        "description": "Number of prices to retrieve (max 100)",
                        "default": 10
                    },
                    "product": {
                        "type": "string",
                        "description": "Filter by product ID"
                    },
                    "active": {
                        "type": "boolean",
                        "description": "Filter by active status"
                    }
                }
            }
        ),
        Tool(
            name="get_price",
            description="Get a specific Stripe price",
            inputSchema={
                "type": "object",
                "properties": {
                    "price_id": {
                        "type": "string",
                        "description": "The Stripe price ID",
                        "required": True
                    }
                },
                "required": ["price_id"]
            }
        ),
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls."""
    try:
        if name == "list_customers":
            limit = arguments.get("limit", 10)
            starting_after = arguments.get("starting_after")
            
            params = {"limit": min(limit, 100)}
            if starting_after:
                params["starting_after"] = starting_after
                
            customers = stripe.Customer.list(**params)
            return [TextContent(type="text", text=json.dumps(customers.data, indent=2, default=str))]
            
        elif name == "get_customer":
            customer_id = arguments["customer_id"]
            customer = stripe.Customer.retrieve(customer_id)
            return [TextContent(type="text", text=json.dumps(customer, indent=2, default=str))]
            
        elif name == "list_payment_intents":
            limit = arguments.get("limit", 10)
            customer = arguments.get("customer")
            
            params = {"limit": min(limit, 100)}
            if customer:
                params["customer"] = customer
                
            payment_intents = stripe.PaymentIntent.list(**params)
            return [TextContent(type="text", text=json.dumps(payment_intents.data, indent=2, default=str))]
            
        elif name == "get_payment_intent":
            payment_intent_id = arguments["payment_intent_id"]
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            return [TextContent(type="text", text=json.dumps(payment_intent, indent=2, default=str))]
            
        elif name == "list_subscriptions":
            limit = arguments.get("limit", 10)
            customer = arguments.get("customer")
            status = arguments.get("status")
            
            params = {"limit": min(limit, 100)}
            if customer:
                params["customer"] = customer
            if status:
                params["status"] = status
                
            subscriptions = stripe.Subscription.list(**params)
            return [TextContent(type="text", text=json.dumps(subscriptions.data, indent=2, default=str))]
            
        elif name == "get_subscription":
            subscription_id = arguments["subscription_id"]
            subscription = stripe.Subscription.retrieve(subscription_id)
            return [TextContent(type="text", text=json.dumps(subscription, indent=2, default=str))]
            
        elif name == "list_products":
            limit = arguments.get("limit", 10)
            active = arguments.get("active")
            
            params = {"limit": min(limit, 100)}
            if active is not None:
                params["active"] = active
                
            products = stripe.Product.list(**params)
            return [TextContent(type="text", text=json.dumps(products.data, indent=2, default=str))]
            
        elif name == "get_product":
            product_id = arguments["product_id"]
            product = stripe.Product.retrieve(product_id)
            return [TextContent(type="text", text=json.dumps(product, indent=2, default=str))]
            
        elif name == "list_prices":
            limit = arguments.get("limit", 10)
            product = arguments.get("product")
            active = arguments.get("active")
            
            params = {"limit": min(limit, 100)}
            if product:
                params["product"] = product
            if active is not None:
                params["active"] = active
                
            prices = stripe.Price.list(**params)
            return [TextContent(type="text", text=json.dumps(prices.data, indent=2, default=str))]
            
        elif name == "get_price":
            price_id = arguments["price_id"]
            price = stripe.Price.retrieve(price_id)
            return [TextContent(type="text", text=json.dumps(price, indent=2, default=str))]
            
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
            
    except stripe.error.StripeError as e:
        return [TextContent(type="text", text=f"Stripe API Error: {str(e)}")]
    except Exception as e:
        return [TextContent(type="text", text=f"Error: {str(e)}")]

async def main():
    """Main entry point."""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="stripe-mcp-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())