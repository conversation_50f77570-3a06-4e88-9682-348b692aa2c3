{"mcpServers": {"supabase": {"command": "python", "args": ["-m", "uv", "tool", "run", "--no-env-file", "--from", "supabase-mcp-server", "supabase-mcp-server"], "env": {"SUPABASE_URL": "https://babgywcvqyclvxdkckkd.supabase.co", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzMDc1MSwiZXhwIjoyMDY3MjA2NzUxfQ.DUKouq8tLc1QwyWKw9pze3tHXIiN-L9SNpdvhEQw3cs"}, "disabled": false, "autoApprove": ["execute_sql", "list_tables", "describe_table", "create_bucket", "list_buckets", "upload_file", "delete_file"]}, "stripe": {"command": "python", "args": ["stripe-mcp-server.py"], "env": {"STRIPE_SECRET_KEY": "sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP", "PYTHONPATH": "."}, "disabled": false, "autoApprove": ["list_customers", "get_customer", "list_payment_intents", "get_payment_intent", "list_subscriptions", "get_subscription", "list_products", "get_product", "list_prices", "get_price"]}}}