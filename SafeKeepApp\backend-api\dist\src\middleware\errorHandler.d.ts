import { Request, Response, NextFunction } from 'express';
export declare const errorHandler: (error: any, req: Request, res: Response, next: NextFunction) => void;
export declare const handleValidationError: (errors: string[]) => never;
export declare const handleStripeError: (stripeError: any) => never;
export declare const notFoundHandler: (req: Request, res: Response) => void;
//# sourceMappingURL=errorHandler.d.ts.map