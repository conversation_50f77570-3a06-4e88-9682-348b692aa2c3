# End-to-End Testing Guide for Modular Pricing Backend API

## Overview

This guide covers the comprehensive end-to-end testing suite for the SafeKeep modular pricing backend API. The tests validate the complete user journey from service selection to subscription management.

## Test Structure

### 1. Complete Subscription Flow Tests (`e2e-subscription-flow.test.ts`)

Tests the full user journey:
- Service combination discovery
- Pricing calculations
- Service validation
- Subscription creation with payment
- Service access verification
- Subscription updates
- Billing status checks

### 2. Error Scenarios and Edge Cases (`e2e-error-scenarios.test.ts`)

Tests error handling and edge cases:
- Invalid service combinations
- Payment failures and rollbacks
- Service access denial
- Webhook failure handling
- Authentication/authorization errors
- Rate limiting

## Running the Tests

### Quick Start
```bash
# Run all end-to-end tests
node run-e2e-tests.js

# Run specific test file
npm test -- e2e-subscription-flow.test.ts

# Run with coverage
npm run test:coverage -- --testPathPattern="e2e-.*\.test\.ts$"
```

### Prerequisites
- Node.js and npm installed
- Test environment variables configured
- Stripe test keys available
- Database test setup completed

## Test Environment Setup

### Environment Variables
Create `.env.test` file:
```
STRIPE_SECRET_KEY=sk_test_your_test_key
SUPABASE_URL=your_test_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_test_service_key
NODE_ENV=test
```

### Database Setup
The tests use mocked database connections by default. For integration testing with real database:
1. Set up test database instance
2. Run migration scripts
3. Configure test data cleanup

## Test Scenarios Covered

### Happy Path Scenarios
- ✅ Complete subscription creation flow
- ✅ Service access validation
- ✅ Subscription updates and changes
- ✅ Payment processing
- ✅ Billing status verification

### Error Scenarios
- ❌ Invalid service combinations
- ❌ Payment failures
- ❌ Authentication errors
- ❌ Rate limiting
- ❌ Webhook processing failures

## Key Test Features

### Mocking Strategy
- Stripe API calls are mocked for predictable testing
- Database operations use test doubles
- Authentication middleware is mocked for test users

### Test Data Management
- Unique test user IDs generated per test run
- Automatic cleanup of test subscriptions
- Isolated test environments

### Assertions
- Comprehensive response validation
- Service access verification
- Error message validation
- Status code verification

## Troubleshooting

### Common Issues
1. **Test timeouts**: Increase timeout in jest config
2. **Database connection errors**: Check test environment setup
3. **Stripe API errors**: Verify test keys are configured
4. **Authentication failures**: Check JWT token mocking

### Debug Mode
```bash
# Run tests with debug output
DEBUG=* node run-e2e-tests.js

# Run single test with verbose output
npm test -- --verbose e2e-subscription-flow.test.ts
```

## Continuous Integration

### GitHub Actions Integration
```yaml
- name: Run E2E Tests
  run: |
    npm install
    node run-e2e-tests.js
  env:
    STRIPE_SECRET_KEY: ${{ secrets.STRIPE_TEST_KEY }}
```

### Test Reports
- Coverage reports generated in `coverage/` directory
- Test results exported in JUnit format
- Performance metrics tracked

## Best Practices

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

### Data Management
- Clean up test data after each test
- Use unique identifiers for test data
- Avoid dependencies between tests

### Error Testing
- Test both expected and unexpected errors
- Verify error messages and codes
- Test error recovery scenarios

## Extending the Tests

### Adding New Test Cases
1. Identify the user scenario to test
2. Create test data setup
3. Execute API calls in sequence
4. Verify expected outcomes
5. Clean up test data

### Custom Matchers
```javascript
expect(response.body).toHaveValidSubscription();
expect(response.body).toHaveServiceAccess('contacts');
```

## Performance Considerations

### Test Execution Time
- Parallel test execution where possible
- Efficient test data setup
- Optimized API call sequences

### Resource Usage
- Memory usage monitoring
- Database connection pooling
- Cleanup of test resources

## Security Testing

### Authentication Tests
- Invalid token handling
- Expired token scenarios
- Missing authentication headers

### Authorization Tests
- Cross-user data access prevention
- Role-based access control
- Service-specific permissions

## Monitoring and Alerts

### Test Failure Notifications
- Slack/email notifications for CI failures
- Test result dashboards
- Performance regression alerts

### Metrics Tracking
- Test execution time trends
- Success/failure rates
- Coverage percentage tracking