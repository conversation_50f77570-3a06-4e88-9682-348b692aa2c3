#!/usr/bin/env node

/**
 * Integration Test Runner for Backup Flow
 * 
 * This script runs comprehensive integration tests for the backup functionality,
 * covering end-to-end flows, network conditions, interruption/resume, and error recovery.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(message, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSubHeader(message) {
  log('\n' + '-'.repeat(40), 'blue');
  log(message, 'blue');
  log('-'.repeat(40), 'blue');
}

function runCommand(command, description) {
  log(`\n🔄 ${description}...`, 'yellow');
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: path.resolve(__dirname, '../..')
    });
    log(`✅ ${description} completed successfully`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} failed:`, 'red');
    log(error.stdout || error.message, 'red');
    return { success: false, error: error.stdout || error.message };
  }
}

function checkTestFiles() {
  const testFiles = [
    'BackupFlowIntegration.test.ts',
    'NetworkConditionIntegration.test.ts',
    'BackupInterruptionIntegration.test.ts',
    'ErrorRecoveryIntegration.test.ts'
  ];

  const missingFiles = [];
  
  testFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  });

  if (missingFiles.length > 0) {
    log(`❌ Missing test files: ${missingFiles.join(', ')}`, 'red');
    return false;
  }

  log(`✅ All integration test files found`, 'green');
  return true;
}

function generateTestReport(results) {
  logHeader('TEST EXECUTION SUMMARY');
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  
  results.forEach(result => {
    if (result.success) {
      // Parse Jest output to extract test counts
      const output = result.output;
      const testMatch = output.match(/(\d+) passing/);
      const failMatch = output.match(/(\d+) failing/);
      
      if (testMatch) {
        const passed = parseInt(testMatch[1]);
        passedTests += passed;
        totalTests += passed;
      }
      
      if (failMatch) {
        const failed = parseInt(failMatch[1]);
        failedTests += failed;
        totalTests += failed;
      }
    } else {
      failedTests += 1;
      totalTests += 1;
    }
  });

  log(`\n📊 Test Results:`, 'bright');
  log(`   Total Tests: ${totalTests}`, 'blue');
  log(`   Passed: ${passedTests}`, 'green');
  log(`   Failed: ${failedTests}`, failedTests > 0 ? 'red' : 'green');
  log(`   Success Rate: ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%`, 
      failedTests === 0 ? 'green' : 'yellow');

  if (failedTests === 0) {
    log('\n🎉 All integration tests passed!', 'green');
  } else {
    log(`\n⚠️  ${failedTests} test(s) failed. Check the output above for details.`, 'yellow');
  }
}

async function main() {
  logHeader('BACKUP INTEGRATION TEST RUNNER');
  
  log('This script runs comprehensive integration tests for the backup functionality:', 'blue');
  log('• End-to-end backup flows from UI to storage', 'blue');
  log('• Video exclusion functionality with mixed media libraries', 'blue');
  log('• Network condition handling and user warnings', 'blue');
  log('• Backup interruption and resume functionality', 'blue');
  log('• Error scenarios and recovery mechanisms', 'blue');

  // Check if test files exist
  logSubHeader('Checking Test Files');
  if (!checkTestFiles()) {
    process.exit(1);
  }

  // Check if Jest is available
  logSubHeader('Checking Dependencies');
  const jestCheck = runCommand('npx jest --version', 'Checking Jest installation');
  if (!jestCheck.success) {
    log('❌ Jest is not installed. Please run: npm install', 'red');
    process.exit(1);
  }

  const results = [];

  // Run individual integration test suites
  logSubHeader('Running Integration Test Suites');

  // 1. End-to-End Backup Flow Tests
  log('\n🧪 Running End-to-End Backup Flow Tests...', 'magenta');
  const backupFlowResult = runCommand(
    'npx jest __tests__/integration/BackupFlowIntegration.test.ts --verbose --no-cache',
    'End-to-End Backup Flow Tests'
  );
  results.push(backupFlowResult);

  // 2. Network Condition Tests
  log('\n🌐 Running Network Condition Tests...', 'magenta');
  const networkResult = runCommand(
    'npx jest __tests__/integration/NetworkConditionIntegration.test.ts --verbose --no-cache',
    'Network Condition Tests'
  );
  results.push(networkResult);

  // 3. Backup Interruption and Resume Tests
  log('\n⏸️ Running Backup Interruption and Resume Tests...', 'magenta');
  const interruptionResult = runCommand(
    'npx jest __tests__/integration/BackupInterruptionIntegration.test.ts --verbose --no-cache',
    'Backup Interruption and Resume Tests'
  );
  results.push(interruptionResult);

  // 4. Error Recovery Tests
  log('\n🔧 Running Error Recovery Tests...', 'magenta');
  const errorRecoveryResult = runCommand(
    'npx jest __tests__/integration/ErrorRecoveryIntegration.test.ts --verbose --no-cache',
    'Error Recovery Tests'
  );
  results.push(errorRecoveryResult);

  // Run all integration tests together
  logSubHeader('Running All Integration Tests Together');
  const allTestsResult = runCommand(
    'npx jest __tests__/integration/ --verbose --no-cache --coverage',
    'All Integration Tests with Coverage'
  );
  results.push(allTestsResult);

  // Generate test report
  generateTestReport(results);

  // Check if coverage report was generated
  const coverageDir = path.resolve(__dirname, '../../coverage');
  if (fs.existsSync(coverageDir)) {
    log('\n📈 Coverage report generated in: coverage/', 'cyan');
    log('   Open coverage/lcov-report/index.html in your browser to view detailed coverage', 'cyan');
  }

  // Exit with appropriate code
  const hasFailures = results.some(result => !result.success);
  process.exit(hasFailures ? 1 : 0);
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log(`\n❌ Uncaught Exception: ${error.message}`, 'red');
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log(`\n❌ Unhandled Rejection at: ${promise}`, 'red');
  log(`Reason: ${reason}`, 'red');
  process.exit(1);
});

// Run the main function
main().catch(error => {
  log(`\n❌ Test runner failed: ${error.message}`, 'red');
  console.error(error.stack);
  process.exit(1);
});