<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Secure Media Backup Demo</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4facfe">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SafeKeep Demo">
    <meta name="msapplication-TileColor" content="#4facfe">
    <meta name="msapplication-config" content="browserconfig.xml">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="icon-180x180.png">
    <link rel="mask-icon" href="safari-pinned-tab.svg" color="#4facfe">
    
    <!-- External Dependencies -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://js.stripe.com/v3/"></script>
    
    <!-- Enhanced UI Framework -->
    <link rel="stylesheet" href="enhanced-ui-framework.css">
    <link rel="stylesheet" href="payment-interface-styles.css">
    <link rel="stylesheet" href="subscription-management-styles.css">
    <link rel="stylesheet" href="error-handler-styles.css">
    <link rel="stylesheet" href="tutorial-system-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .status-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #4facfe;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .status-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ddd;
        }

        .status-dot.connected {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-dot.error {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .demo-section {
            margin-bottom: 30px;
        }

        .demo-section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4facfe;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .demo-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
        }

        .demo-card h3 {
            color: #4facfe;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .demo-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.2s ease;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .btn.secondary {
            background: #6c757d;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.danger {
            background: #dc3545;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-section {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.error {
            color: #ff6b6b;
        }

        .log-entry.success {
            color: #51cf66;
        }

        .log-entry.info {
            color: #74c0fc;
        }

        .auth-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .auth-form {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .auth-form input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4facfe;
            display: block;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .auth-info {
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 0.9rem;
            margin-top: 10px;
        }

        .auth-info.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .auth-info.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .auth-info.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .auth-form input {
            transition: border-color 0.2s ease;
        }

        .auth-form input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
        }

        /* Real-time Backup Console Styles */
        .backup-console {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .console-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .console-controls {
            display: flex;
            gap: 10px;
        }

        .console-controls .btn {
            padding: 8px 16px;
            font-size: 0.9rem;
        }

        .console-body {
            min-height: 200px;
        }

        .console-placeholder {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .session-overview {
            background: white;
            border-radius: 10px;
            padding: 20px;
        }

        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .session-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .session-status.running {
            background: #d4edda;
            color: #155724;
        }

        .session-status.paused {
            background: #fff3cd;
            color: #856404;
        }

        .session-status.completed {
            background: #d1ecf1;
            color: #0c5460;
        }

        .session-status.cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .overall-progress {
            margin-bottom: 25px;
        }

        .progress-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .progress-bar-container {
            margin: 10px 0;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 6px;
        }

        .progress-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #666;
            margin-top: 8px;
        }

        .phase-progress {
            display: grid;
            gap: 15px;
            margin-bottom: 25px;
        }

        .phase-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #4facfe;
        }

        .phase-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .phase-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #666;
            margin-top: 8px;
        }

        .current-operation {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .operation-header {
            font-weight: 600;
            margin-bottom: 10px;
            color: #1976d2;
        }

        .operation-details {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .operation-progress {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .performance-metrics {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .metric {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .metric-label {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #4facfe;
        }

        .error-log {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .error-header {
            font-weight: 600;
            color: #c53030;
            margin-bottom: 10px;
        }

        .error-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .error-item {
            display: grid;
            grid-template-columns: auto auto 1fr auto;
            gap: 10px;
            padding: 8px;
            background: white;
            border-radius: 4px;
            margin-bottom: 5px;
            font-size: 0.85rem;
        }

        .error-time {
            color: #666;
        }

        .error-phase {
            font-weight: 600;
            color: #4facfe;
        }

        .error-message {
            color: #c53030;
        }

        .error-item-name {
            color: #666;
            font-style: italic;
        }

        .completion-summary {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .completion-summary h4 {
            color: #155724;
            margin-bottom: 15px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
            font-size: 0.9rem;
        }

        .summary-stats div {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }

        /* Concurrent Sessions Styles */
        .concurrent-sessions-overview {
            background: white;
            border-radius: 10px;
            padding: 20px;
        }

        .global-stats {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .concurrent-sessions-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .concurrent-session-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #4facfe;
        }

        .concurrent-session-item .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .concurrent-session-item h5 {
            margin: 0;
            font-size: 0.9rem;
            color: #333;
        }

        .concurrent-session-item .session-progress {
            margin-top: 10px;
        }

        .concurrent-session-item .session-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        .session-placeholder {
            text-align: center;
            padding: 20px;
            color: #666;
            font-style: italic;
        }

        /* Encryption Demo Styles */
        .encryption-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .demo-header h3 {
            color: #4facfe;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .demo-header p {
            color: #666;
            font-size: 1rem;
        }

        .algorithm-section, .key-section, .data-section, .security-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .algorithm-section h4, .key-section h4, .data-section h4, .security-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .algorithm-selector {
            display: grid;
            gap: 10px;
            margin-bottom: 20px;
        }

        .algorithm-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .algorithm-option:hover {
            border-color: #4facfe;
            background: #f8f9ff;
        }

        .algorithm-option input[type="radio"] {
            margin-right: 15px;
            transform: scale(1.2);
        }

        .algorithm-option input[type="radio"]:checked + .algorithm-label {
            color: #4facfe;
        }

        .algorithm-label {
            flex: 1;
        }

        .algorithm-label strong {
            display: block;
            margin-bottom: 5px;
        }

        .algorithm-label small {
            color: #666;
            font-size: 0.85rem;
        }

        .algorithm-details, .security-details {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .algorithm-badge, .security-badge {
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .algorithm-specs, .security-specs {
            flex: 1;
            display: grid;
            gap: 5px;
            font-size: 0.9rem;
        }

        .security-explanation {
            margin-top: 15px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .security-explanation p {
            margin: 0;
            color: #155724;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .key-controls, .data-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .key-controls .btn, .data-controls .btn {
            padding: 8px 16px;
            font-size: 0.9rem;
        }

        .key-display {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .key-details {
            display: grid;
            gap: 15px;
        }

        .key-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            font-size: 0.9rem;
        }

        .key-values {
            display: grid;
            gap: 15px;
        }

        .key-value label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .hex-display {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            word-break: break-all;
            line-height: 1.4;
        }

        .key-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            font-size: 0.85rem;
            color: #856404;
        }

        .data-input textarea {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            resize: vertical;
        }

        .data-input textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
        }

        .encryption-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .encryption-controls .btn {
            padding: 12px 24px;
            font-size: 1rem;
            min-width: 150px;
        }

        .progress-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-weight: 600;
            color: #333;
        }

        .comparison-section, .decryption-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .comparison-section h4, .decryption-section h4 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .comparison-panel {
            border-radius: 8px;
            overflow: hidden;
        }

        .comparison-panel.original {
            border: 2px solid #28a745;
        }

        .comparison-panel.encrypted {
            border: 2px solid #dc3545;
        }

        .comparison-panel h5 {
            padding: 15px;
            margin: 0;
            font-size: 1rem;
            color: white;
        }

        .comparison-panel.original h5 {
            background: #28a745;
        }

        .comparison-panel.encrypted h5 {
            background: #dc3545;
        }

        .data-preview {
            padding: 15px;
            background: #f8f9fa;
            min-height: 150px;
            max-height: 300px;
            overflow-y: auto;
        }

        .data-content.readable pre {
            margin: 0;
            font-size: 0.85rem;
            line-height: 1.4;
            color: #333;
        }

        .data-content.encrypted {
            font-family: 'Courier New', monospace;
        }

        .data-stats {
            padding: 10px 15px;
            background: #e9ecef;
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #666;
        }

        .decryption-panel {
            border: 2px solid #17a2b8;
            border-radius: 8px;
            overflow: hidden;
        }

        .decryption-panel h5 {
            background: #17a2b8;
            color: white;
            padding: 15px;
            margin: 0;
            font-size: 1rem;
        }

        .verification-status {
            padding: 15px;
            text-align: center;
        }

        .verification-badge {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .verification-badge.verified {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .verification-badge.failed {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .metrics-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .metrics-section h4 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .metric-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #4facfe;
        }

        .error-section {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .error-message {
            color: #721c24;
            font-weight: 600;
        }

        /* Responsive design for encryption demo */
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .encryption-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .key-controls, .data-controls {
                justify-content: center;
            }
            
            .algorithm-details, .security-details {
                flex-direction: column;
                text-align: center;
            }
        }

        /* Subscription Management Styles */
        .subscription-dashboard {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .subscription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .subscription-header h3 {
            color: #4facfe;
            margin: 0;
            font-size: 1.5rem;
        }

        .subscription-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            color: white;
        }

        .status-badge.free {
            background: #6c757d;
        }

        .status-badge.basic {
            background: #28a745;
        }

        .status-badge.premium {
            background: #007bff;
        }

        .current-plan-section, .available-plans-section, .billing-section, .payment-methods-section {
            margin-bottom: 30px;
        }

        .current-plan-section h4, .available-plans-section h4, .billing-section h4, .payment-methods-section h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .plan-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .plan-card:hover {
            border-color: #4facfe;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.2);
        }

        .plan-card.current {
            border-color: #28a745;
            background: #f8fff9;
        }

        .plan-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .plan-header h5 {
            margin: 0;
            color: #333;
            font-size: 1.1rem;
        }

        .plan-price {
            font-size: 1.2rem;
            font-weight: 600;
            color: #4facfe;
        }

        .feature-list {
            display: grid;
            gap: 8px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
        }

        .feature-icon {
            font-size: 1rem;
        }

        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .plan-actions {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .usage-section {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .usage-section h5 {
            color: #333;
            margin-bottom: 15px;
        }

        .usage-item {
            margin-bottom: 15px;
        }

        .usage-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: #666;
        }

        .usage-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.3s ease;
        }

        .billing-details {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .billing-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .billing-label {
            color: #666;
        }

        .billing-value {
            font-weight: 600;
            color: #333;
        }

        .billing-value.status-active {
            color: #28a745;
        }

        .billing-value.status-canceled {
            color: #dc3545;
        }

        .billing-actions {
            display: flex;
            gap: 10px;
        }

        /* Payment Modal Styles */
        .payment-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-header h4 {
            margin: 0;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #333;
        }

        .modal-body {
            padding: 20px;
        }

        .selected-plan-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .selected-plan-details h5 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .selected-plan-details .plan-price {
            font-size: 1.3rem;
            font-weight: 600;
            color: #4facfe;
            margin-bottom: 10px;
        }

        .plan-description {
            color: #666;
            font-size: 0.9rem;
        }

        .payment-form {
            margin-top: 20px;
        }

        #card-element {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            margin-bottom: 10px;
        }

        #card-errors {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 10px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        /* Responsive design for subscription management */
        @media (max-width: 768px) {
            .subscription-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .plans-grid {
                grid-template-columns: 1fr;
            }
            
            .billing-item {
                flex-direction: column;
                gap: 5px;
            }
            
            .modal-content {
                width: 95%;
                margin: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header" role="banner">
            <h1>🔒 SafeKeep</h1>
            <p>Secure Media Backup - Web Demo</p>
        </header>

        <main class="main-content" id="main-content" role="main">
            <!-- Connection Status -->
            <div class="status-section">
                <h2>🔗 Connection Status</h2>
                <div class="status-grid">
                    <div class="status-card">
                        <h3>Database</h3>
                        <div class="status-indicator">
                            <div class="status-dot" id="db-status"></div>
                            <span id="db-text">Checking...</span>
                        </div>
                        <small id="db-details">Connecting to Supabase...</small>
                    </div>
                    <div class="status-card">
                        <h3>Storage</h3>
                        <div class="status-indicator">
                            <div class="status-dot" id="storage-status"></div>
                            <span id="storage-text">Checking...</span>
                        </div>
                        <small id="storage-details">Verifying buckets...</small>
                    </div>
                    <div class="status-card">
                        <h3>Authentication</h3>
                        <div class="status-indicator">
                            <div class="status-dot" id="auth-status"></div>
                            <span id="auth-text">Checking...</span>
                        </div>
                        <small id="auth-details">Testing auth service...</small>
                    </div>
                </div>
            </div>

            <!-- Authentication -->
            <div class="auth-section">
                <h3>👤 User Authentication</h3>
                <p>Sign up or sign in to test the backup functionality:</p>
                <div class="auth-form">
                    <input type="email" id="email" placeholder="Enter your email">
                    <input type="password" id="password" placeholder="Enter password (min 8 chars)">
                    <button class="btn" onclick="signUp()">Sign Up</button>
                    <button class="btn secondary" onclick="signIn()">Sign In</button>
                    <button class="btn danger" onclick="signOut()">Sign Out</button>
                </div>
                <div class="auth-form" style="margin-top: 10px; gap: 5px;">
                    <button class="btn" style="background: #ff9800;" onclick="createDemoUser()">Create Demo User</button>
                    <button class="btn secondary" onclick="useDemoCredentials()">Use Demo Credentials</button>
                </div>
                <div id="auth-info" class="auth-info info" style="margin-top: 10px; font-size: 0.9rem;">Enhanced authentication ready - create account or use demo!</div>
                <div style="margin-top: 10px; padding: 10px; background: #e8f5e8; border-radius: 5px; font-size: 0.85rem;">
                    <strong>✅ Enhanced Features:</strong> 
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>Robust user signup with error recovery</li>
                        <li>Demo user creation for easy testing</li>
                        <li>User profile and subscription management</li>
                        <li>Persistent authentication state</li>
                    </ul>
                </div>
            </div>

            <!-- Demo Features -->
            <div class="demo-section">
                <h2>📱 Backup Features Demo</h2>
                <div class="demo-grid">
                    <div class="demo-card">
                        <h3>📞 Contact Backup</h3>
                        <p>Simulate backing up device contacts with encryption and secure storage.</p>
                        <button class="btn" onclick="simulateContactBackup()">Backup Contacts</button>
                        <div class="progress-bar">
                            <div class="progress-fill" id="contact-progress"></div>
                        </div>
                        <div id="contact-status">Ready to backup</div>
                    </div>

                    <div class="demo-card">
                        <h3>💬 Message Backup</h3>
                        <p>Simulate backing up SMS messages with end-to-end encryption.</p>
                        <button class="btn" onclick="simulateMessageBackup()">Backup Messages</button>
                        <div class="progress-bar">
                            <div class="progress-fill" id="message-progress"></div>
                        </div>
                        <div id="message-status">Ready to backup</div>
                    </div>

                    <div class="demo-card">
                        <h3>📸 Photo Backup</h3>
                        <p>Simulate backing up photos (excluding videos) with metadata preservation.</p>
                        <button class="btn" onclick="simulatePhotoBackup()">Backup Photos</button>
                        <div class="progress-bar">
                            <div class="progress-fill" id="photo-progress"></div>
                        </div>
                        <div id="photo-status">Ready to backup</div>
                    </div>

                    <div class="demo-card">
                        <h3>🔄 Full Backup</h3>
                        <p>Run a complete backup session including all data types.</p>
                        <button class="btn success" onclick="runFullBackup()">Start Full Backup</button>
                        <div class="progress-bar">
                            <div class="progress-fill" id="full-progress"></div>
                        </div>
                        <div id="full-status">Ready for full backup</div>
                    </div>

                    <div class="demo-card">
                        <h3>📁 Storage Management</h3>
                        <p>View and manage files in Supabase storage.</p>
                        <button class="btn secondary" onclick="listStorageFiles()">List Storage Files</button>
                        <button class="btn danger" onclick="cleanupDemoFiles()">Cleanup Demo Files</button>
                        <div style="margin-top: 10px; font-size: 0.9rem;">
                            Files are kept for 2 minutes after upload so you can see them in Supabase Dashboard.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Backup Console -->
            <div class="demo-section">
                <h2>⚡ Real-time Backup Console</h2>
                <div id="backup-console-container"></div>
            </div>

            <!-- Encryption Demonstration -->
            <div class="demo-section">
                <h2>🔐 Encryption Demonstration</h2>
                <div id="encryption-demo-container"></div>
            </div>

            <!-- Backup History and Analytics Dashboard -->
            <div class="demo-section">
                <h2>📊 Backup History & Analytics</h2>
                <div id="backup-history-dashboard-container"></div>
            </div>

            <!-- Data Restore Simulation -->
            <div class="demo-section">
                <h2>🔄 Data Restore Simulation</h2>
                <div id="restore-simulation-container"></div>
            </div>

            <!-- Advanced Backup Scheduling System -->
            <div class="demo-section">
                <h2>🕒 Advanced Backup Scheduling</h2>
                <div style="margin-bottom: 15px;">
                    <button class="btn" onclick="testScheduleSystem()" id="test-schedule-btn">🧪 Test Schedule System</button>
                    <button class="btn secondary" onclick="manualInitComponents()" id="manual-init-btn">🔧 Manual Init</button>
                </div>
                <div id="schedule-console-container">
                    <div style="padding: 20px; text-align: center; color: #666;">
                        Schedule console will load here automatically, or click "Manual Init" if needed.
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="demo-section">
                <h2>📊 Backup Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number" id="total-backups">0</span>
                        <div class="stat-label">Total Backups</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number" id="total-files">0</span>
                        <div class="stat-label">Files Backed Up</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number" id="total-size">0 MB</span>
                        <div class="stat-label">Data Backed Up</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number" id="success-rate">100%</span>
                        <div class="stat-label">Success Rate</div>
                    </div>
                </div>
            </div>

            <!-- Subscription Management Dashboard -->
            <div class="demo-section">
                <h2>💳 Subscription Management</h2>
                <div class="demo-card">
                    <h3>Subscription Dashboard</h3>
                    <p>Comprehensive subscription management with billing history, payment methods, and usage tracking.</p>
                    <button class="btn" onclick="initializeSubscriptionDashboard()">🚀 Launch Dashboard</button>
                    <button class="btn secondary" onclick="testSubscriptionManagement()">🧪 Run Tests</button>
                    <div id="subscription-dashboard-container" style="margin-top: 20px;"></div>
                </div>
            </div>

            <!-- Activity Log -->
            <div class="demo-section">
                <h2>📝 Activity Log</h2>
                <div class="log-section" id="activity-log">
                    <div class="log-entry info">🚀 SafeKeep Web Demo initialized</div>
                    <div class="log-entry info">🔗 Connecting to Supabase...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="auth-manager.js"></script>
    <script src="realtime-progress-manager.js"></script>
    <script src="backup-console.js"></script>
    <script src="encryption-manager.js"></script>
    <script src="encryption-demo.js"></script>
    <script src="backup-history-manager.js"></script>
    <script src="backup-history-dashboard.js"></script>
    <script src="restore-manager.js"></script>
    <script src="restore-simulation.js"></script>
    <script src="schedule-manager.js"></script>
    <script src="schedule-console.js"></script>
    <script src="verify-schedule-system.js"></script>
    <script src="stripe-config.js"></script>
    <script src="stripe-manager.js"></script>
    <script src="subscription-tier-config.js"></script>
    <script src="usage-quota-manager.js"></script>
    <script src="subscription-manager.js"></script>
    <script src="payment-processor.js"></script>
    <script src="payment-methods-manager.js"></script>
    <script src="subscription-flow-manager.js"></script>
    <script src="test-subscription-system.js"></script>
    <script src="test-payment-interface.js"></script>
    <script src="verify-subscription-system.js"></script>
    <script src="verify-payment-interface.js"></script>
    <script src="subscription-management-dashboard.js"></script>
    <script src="test-subscription-management.js"></script>
    <script src="verify-subscription-management.js"></script>
    <script src="debug-components.js"></script>
    <script src="manual-init.js"></script>
    <!-- Enhanced UI Components -->
    <script src="theme-manager.js"></script>
    <script src="responsive-layout-manager.js"></script>
    <script src="accessibility-manager.js"></script>
    <script src="pwa-manager.js"></script>
    <script src="enhanced-ui-integration.js"></script>

    <!-- Error Handling System -->
    <script src="error-handler.js"></script>
    <script src="network-queue-manager.js"></script>
    <script src="offline-manager.js"></script>
    <script src="demo-state-manager.js"></script>
    <script src="error-handling-integration.js"></script>
    <script src="verify-error-handling.js"></script>
    
    <!-- Tutorial System -->
    <script src="tutorial-system.js"></script>
    
    <!-- Main Application -->
    <script src="app.js"></script>
    
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
</body>
</html>