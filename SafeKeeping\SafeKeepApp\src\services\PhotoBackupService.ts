import { Platform } from 'react-native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import CryptoJS from 'crypto-js';
import CloudStorageService from './CloudStorageService';
import AuthService from './AuthService';

export interface PhotoAsset {
  id?: string;
  uri: string;
  filename: string;
  timestamp: number;
  type: string;
  fileSize: number;
  width: number;
  height: number;
  hash?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
}

export interface BackupProgress {
  totalPhotos: number;
  processedPhotos: number;
  currentPhoto: string;
  bytesUploaded: number;
  totalBytes: number;
  percentage: number;
  status: 'scanning' | 'processing' | 'uploading' | 'completed' | 'error' | 'paused';
  error?: string;
}

export interface BackupResult {
  success: boolean;
  totalPhotos: number;
  backedUpPhotos: number;
  skippedPhotos: number;
  duplicatesFound: number;
  errors: string[];
  duration: number;
}

class PhotoBackupService {
  private isBackupRunning = false;
  private shouldPauseBackup = false;
  private progressCallback?: (progress: BackupProgress) => void;
  private duplicateHashes = new Set<string>();
  private readonly CHUNK_SIZE = 1024 * 1024; // 1MB chunks for upload

  // Scan device photo library
  async scanPhotoLibrary(limit: number = 1000): Promise<PhotoAsset[]> {
    try {
      console.log('📷 Starting photo library scan...');

      const photos = await CameraRoll.getPhotos({
        first: limit,
        assetType: 'Photos',
        include: ['filename', 'fileSize', 'imageSize', 'location'],
      });

      const photoAssets: PhotoAsset[] = photos.edges.map(edge => {
        // Create a base photo asset without location
        const photoAsset: PhotoAsset = {
          uri: edge.node.image.uri,
          filename: edge.node.image.filename || `photo_${Date.now()}.jpg`,
          timestamp: new Date(edge.node.timestamp).getTime(),
          type: edge.node.type,
          fileSize: edge.node.image.fileSize || 0,
          width: edge.node.image.width,
          height: edge.node.image.height,
        };

        // Add location only if both latitude and longitude are defined
        if (edge.node.location &&
          typeof edge.node.location.latitude === 'number' &&
          typeof edge.node.location.longitude === 'number') {
          photoAsset.location = {
            latitude: edge.node.location.latitude,
            longitude: edge.node.location.longitude,
          };
        }

        return photoAsset;
      });

      console.log(`📷 Found ${photoAssets.length} photos in library`);
      return photoAssets;
    } catch (error) {
      console.error('Error scanning photo library:', error);
      throw new Error('Failed to scan photo library. Please check permissions.');
    }
  }

  // Generate hash for duplicate detection
  async generatePhotoHash(photoUri: string): Promise<string> {
    try {
      // For React Native, we'll use file stats and a portion of the file for hashing
      const fileInfo = await RNFS.stat(photoUri);
      const fileSize = fileInfo.size;
      const modificationTime = fileInfo.mtime;

      // Read first 1KB of file for content-based hashing
      const sampleData = await RNFS.read(photoUri, 1024, 0, 'base64');

      // Create hash from file size, modification time, and sample content
      const hashInput = `${fileSize}_${modificationTime}_${sampleData}`;
      const hash = CryptoJS.SHA256(hashInput).toString();

      return hash;
    } catch (error) {
      console.error('Error generating photo hash:', error);
      // Fallback to filename + size based hash
      const fallbackHash = CryptoJS.SHA256(`${photoUri}_${Date.now()}`).toString();
      return fallbackHash;
    }
  }

  // Detect duplicate photos
  async detectDuplicates(photos: PhotoAsset[]): Promise<PhotoAsset[]> {
    console.log('🔍 Starting duplicate detection...');
    const uniquePhotos: PhotoAsset[] = [];
    const duplicateHashes = new Set<string>();

    for (let i = 0; i < photos.length; i++) {
      const photo = photos[i];

      try {
        const hash = await this.generatePhotoHash(photo.uri);
        photo.hash = hash;

        if (!duplicateHashes.has(hash)) {
          duplicateHashes.add(hash);
          uniquePhotos.push(photo);
        } else {
          console.log(`📷 Duplicate found: ${photo.filename}`);
        }

        // Update progress for duplicate detection
        if (this.progressCallback) {
          this.progressCallback({
            totalPhotos: photos.length,
            processedPhotos: i + 1,
            currentPhoto: photo.filename,
            bytesUploaded: 0,
            totalBytes: 0,
            percentage: Math.round(((i + 1) / photos.length) * 100),
            status: 'processing'
          });
        }
      } catch (error) {
        console.error(`Error processing photo ${photo.filename}:`, error);
        // Include photo anyway if hash generation fails
        uniquePhotos.push(photo);
      }
    }

    this.duplicateHashes = duplicateHashes;
    console.log(`🔍 Duplicate detection complete. ${uniquePhotos.length} unique photos found.`);
    return uniquePhotos;
  }

  // Upload photo using CloudStorageService
  async uploadPhoto(photo: PhotoAsset): Promise<boolean> {
    try {
      if (this.shouldPauseBackup) {
        throw new Error('Backup paused by user');
      }

      console.log(`📤 Starting upload for ${photo.filename}`);

      // Read file data
      const fileData = await RNFS.readFile(photo.uri, 'base64');

      // Upload to cloud storage with encryption
      const result = await CloudStorageService.uploadFile(
        fileData,
        photo.filename,
        photo.type,
        'photo',
        (progress) => {
          // Update progress callback
          if (this.progressCallback) {
            this.progressCallback({
              totalPhotos: 1,
              processedPhotos: 0,
              currentPhoto: photo.filename,
              bytesUploaded: progress.bytesTransferred,
              totalBytes: progress.totalBytes,
              percentage: progress.percentage,
              status: 'uploading'
            });
          }
        }
      );

      if (result.success) {
        console.log(`✅ Successfully uploaded ${photo.filename}`);
        return true;
      } else {
        console.error(`❌ Failed to upload ${photo.filename}: ${result.error}`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Failed to upload ${photo.filename}:`, error);
      return false;
    }
  }

  // Check if user is authenticated before backup
  private async checkAuthentication(): Promise<boolean> {
    const user = AuthService.getCurrentUser();
    if (!user) {
      console.error('❌ User not authenticated');
      return false;
    }
    return true;
  }

  // Main backup function with retry logic
  async backupPhotos(
    photos: PhotoAsset[],
    onProgress?: (progress: BackupProgress) => void,
    maxRetries: number = 3
  ): Promise<BackupResult> {
    if (this.isBackupRunning) {
      throw new Error('Backup is already running');
    }

    this.isBackupRunning = true;
    this.shouldPauseBackup = false;
    this.progressCallback = onProgress;

    const startTime = Date.now();
    let backedUpPhotos = 0;
    let skippedPhotos = 0;
    const errors: string[] = [];

    try {
      // Check authentication first
      if (!(await this.checkAuthentication())) {
        throw new Error('User authentication required for backup');
      }

      console.log(`🚀 Starting backup of ${photos.length} photos...`);

      // Step 1: Detect duplicates
      if (onProgress) {
        onProgress({
          totalPhotos: photos.length,
          processedPhotos: 0,
          currentPhoto: 'Detecting duplicates...',
          bytesUploaded: 0,
          totalBytes: 0,
          percentage: 0,
          status: 'processing'
        });
      }

      const uniquePhotos = await this.detectDuplicates(photos);
      const duplicatesFound = photos.length - uniquePhotos.length;

      // Step 2: Upload unique photos
      const totalBytes = uniquePhotos.reduce((sum, photo) => sum + photo.fileSize, 0);
      let processedBytes = 0;

      for (let i = 0; i < uniquePhotos.length; i++) {
        if (this.shouldPauseBackup) {
          break;
        }

        const photo = uniquePhotos[i];
        let success = false;
        let retryCount = 0;

        // Retry logic
        while (!success && retryCount < maxRetries) {
          try {
            if (onProgress) {
              onProgress({
                totalPhotos: uniquePhotos.length,
                processedPhotos: i,
                currentPhoto: photo.filename,
                bytesUploaded: processedBytes,
                totalBytes: totalBytes,
                percentage: Math.round((i / uniquePhotos.length) * 100),
                status: 'uploading'
              });
            }

            success = await this.uploadPhoto(photo);

            if (success) {
              backedUpPhotos++;
              processedBytes += photo.fileSize;
            }
          } catch (error: unknown) {
            retryCount++;
            console.error(`Retry ${retryCount}/${maxRetries} for ${photo.filename}:`, error);

            if (retryCount >= maxRetries) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              errors.push(`Failed to backup ${photo.filename}: ${errorMessage}`);
              skippedPhotos++;
            } else {
              // Wait before retry
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
          }
        }
      }

      // Final progress update
      if (onProgress) {
        onProgress({
          totalPhotos: uniquePhotos.length,
          processedPhotos: uniquePhotos.length,
          currentPhoto: 'Backup completed!',
          bytesUploaded: processedBytes,
          totalBytes: totalBytes,
          percentage: 100,
          status: 'completed'
        });
      }

      const duration = Date.now() - startTime;
      const result: BackupResult = {
        success: errors.length === 0,
        totalPhotos: photos.length,
        backedUpPhotos,
        skippedPhotos,
        duplicatesFound,
        errors,
        duration
      };

      console.log('🎉 Backup completed:', result);
      return result;

    } catch (error: unknown) {
      console.error('❌ Backup failed:', error);

      const errorMessage = error instanceof Error ? error.message : String(error);

      if (onProgress) {
        onProgress({
          totalPhotos: photos.length,
          processedPhotos: 0,
          currentPhoto: '',
          bytesUploaded: 0,
          totalBytes: 0,
          percentage: 0,
          status: 'error',
          error: errorMessage
        });
      }

      return {
        success: false,
        totalPhotos: photos.length,
        backedUpPhotos,
        skippedPhotos,
        duplicatesFound: photos.length - (await this.detectDuplicates(photos)).length,
        errors: [errorMessage],
        duration: Date.now() - startTime
      };
    } finally {
      this.isBackupRunning = false;
      this.progressCallback = undefined;
    }
  }

  // Control functions
  pauseBackup(): void {
    this.shouldPauseBackup = true;
    console.log('⏸️ Backup paused by user');
  }

  resumeBackup(): void {
    this.shouldPauseBackup = false;
    console.log('▶️ Backup resumed by user');
  }

  isRunning(): boolean {
    return this.isBackupRunning;
  }

  // Restore photos from backup
  async restorePhotos(): Promise<{ success: boolean; photos?: PhotoAsset[]; error?: string }> {
    try {
      console.log('📸 Starting photo restore...');

      // Get user's photo backup files
      const files = await CloudStorageService.getUserFiles('photo');

      if (files.length === 0) {
        return { success: false, error: 'No photo backup found. Please create a backup first by using the "Start Photo Backup" button.' };
      }

      console.log(`📸 Found ${files.length} photo backups`);

      // For demo purposes, we'll simulate photo restoration
      // In a real implementation, this would download and decrypt each photo
      const restoredPhotos: PhotoAsset[] = files.map((file, index) => ({
        id: file.id,
        uri: `restored_photo_${index}`, // Placeholder URI
        filename: file.original_name,
        width: 1920, // Placeholder dimensions
        height: 1080,
        fileSize: file.size,
        timestamp: new Date(file.uploaded_at).getTime(),
        type: file.mime_type
      }));

      console.log(`✅ Successfully restored ${restoredPhotos.length} photos`);
      return { success: true, photos: restoredPhotos };

    } catch (error: unknown) {
      console.error('❌ Photo restore failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  // Get comprehensive backup statistics
  async getBackupStatsFromCloud(): Promise<{
    totalBackups: number;
    lastBackupDate?: Date;
    totalPhotos: number;
  }> {
    try {
      const files = await CloudStorageService.getUserFiles('photo');

      if (files.length === 0) {
        return { totalBackups: 0, totalPhotos: 0 };
      }

      const latestBackup = files.sort((a, b) =>
        new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
      )[0];

      return {
        totalBackups: files.length,
        lastBackupDate: new Date(latestBackup.uploaded_at),
        totalPhotos: files.length
      };
    } catch (error: unknown) {
      console.error('Failed to get backup stats:', error);
      return { totalBackups: 0, totalPhotos: 0 };
    }
  }

  // Get backup statistics
  getBackupStats(): { duplicateHashes: number } {
    return {
      duplicateHashes: this.duplicateHashes.size
    };
  }
}

export default new PhotoBackupService();
