import { SubscriptionManager } from '../SubscriptionManager';
import { ServiceValidator } from '../ServiceValidator';
import { PricingEngine } from '../PricingEngine';
import { supabase } from '../../utils/database';
import { SubscriptionRequest, ModularSubscription } from '../../types/modular-pricing';

// Mock dependencies
jest.mock('../ServiceValidator');
jest.mock('../PricingEngine');
jest.mock('../../utils/database', () => ({
  supabase: {
    from: jest.fn(),
    rpc: jest.fn()
  }
}));
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-subscription-id')
}));

describe('SubscriptionManager', () => {
  let subscriptionManager: SubscriptionManager;
  let mockServiceValidator: jest.Mocked<ServiceValidator>;
  let mockPricingEngine: jest.Mocked<PricingEngine>;
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup mocks
    mockServiceValidator = new ServiceValidator() as jest.Mocked<ServiceValidator>;
    mockPricingEngine = new PricingEngine() as jest.Mocked<PricingEngine>;
    mockSupabase = supabase as any;
    
    subscriptionManager = new SubscriptionManager();
    
    // Replace instances with mocks
    (subscriptionManager as any).serviceValidator = mockServiceValidator;
    (subscriptionManager as any).pricingEngine = mockPricingEngine;
  });

  describe('createSubscription', () => {
    const validSubscriptionRequest: SubscriptionRequest = {
      userId: 'user-123',
      serviceIds: ['contacts', 'messages'],
      customerId: 'cus_123'
    };

    const mockPricingResult = {
      recommendedPlanId: 'contacts-messages',
      recommendedPlanName: 'Contacts + Messages',
      priceCents: 249,
      savingsCents: 50,
      individualTotalCents: 299
    };

    const mockSubscriptionData = {
      id: 'test-subscription-id',
      user_id: 'user-123',
      plan_id: 'contacts-messages',
      total_price_cents: 249,
      status: 'active',
      stripe_subscription_id: null,
      stripe_customer_id: 'cus_123',
      current_period_start: '2024-01-01T00:00:00.000Z',
      current_period_end: '2024-02-01T00:00:00.000Z',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    };

    beforeEach(() => {
      mockServiceValidator.validateServiceCombination.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: []
      });
      
      mockPricingEngine.calculateOptimalPrice.mockResolvedValue(mockPricingResult);
    });

    it('should create a subscription successfully with valid service combination', async () => {
      // Mock no existing subscription
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' } // No rows found
              })
            })
          })
        })
      });

      // Mock subscription creation
      const mockInsert = {
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockSubscriptionData,
              error: null
            })
          })
        })
      };

      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' }
              })
            })
          })
        })
      }).mockReturnValueOnce(mockInsert);

      // Mock service update
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null
      });

      const result = await subscriptionManager.createSubscription(validSubscriptionRequest);

      expect(result).toEqual({
        id: 'test-subscription-id',
        userId: 'user-123',
        planId: 'contacts-messages',
        serviceIds: ['contacts', 'messages'],
        totalPriceCents: 249,
        status: 'active',
        stripeSubscriptionId: null,
        stripeCustomerId: 'cus_123',
        currentPeriodStart: new Date('2024-01-01T00:00:00.000Z'),
        currentPeriodEnd: new Date('2024-02-01T00:00:00.000Z'),
        createdAt: new Date('2024-01-01T00:00:00.000Z'),
        updatedAt: new Date('2024-01-01T00:00:00.000Z')
      });

      expect(mockServiceValidator.validateServiceCombination).toHaveBeenCalledWith(['contacts', 'messages']);
      expect(mockPricingEngine.calculateOptimalPrice).toHaveBeenCalledWith(['contacts', 'messages']);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_user_services', {
        user_uuid: 'user-123',
        new_service_ids: ['contacts', 'messages']
      });
    });

    it('should throw error when user ID is missing', async () => {
      const invalidRequest = { ...validSubscriptionRequest, userId: '' };

      await expect(subscriptionManager.createSubscription(invalidRequest))
        .rejects.toThrow('User ID is required');
    });

    it('should throw error when service IDs are missing', async () => {
      const invalidRequest = { ...validSubscriptionRequest, serviceIds: [] };

      await expect(subscriptionManager.createSubscription(invalidRequest))
        .rejects.toThrow('Service IDs are required');
    });

    it('should throw error when service combination is invalid', async () => {
      mockServiceValidator.validateServiceCombination.mockResolvedValue({
        isValid: false,
        errors: ['Invalid service combination'],
        warnings: []
      });

      await expect(subscriptionManager.createSubscription(validSubscriptionRequest))
        .rejects.toThrow('Invalid service combination: Invalid service combination');
    });

    it('should throw error when user already has active subscription', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { id: 'existing-sub', status: 'active' },
                error: null
              })
            })
          })
        })
      });

      await expect(subscriptionManager.createSubscription(validSubscriptionRequest))
        .rejects.toThrow('User already has an active subscription. Use updateSubscription to modify services.');
    });

    it('should rollback subscription creation if service update fails', async () => {
      // Mock no existing subscription
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' }
              })
            })
          })
        })
      });

      // Mock successful subscription creation
      const mockDelete = jest.fn().mockReturnValue({
        eq: jest.fn().mockResolvedValue({ data: null, error: null })
      });

      mockSupabase.from.mockReturnValueOnce({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockSubscriptionData,
              error: null
            })
          })
        })
      }).mockReturnValueOnce({
        delete: mockDelete
      });

      // Mock service update failure
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Service update failed' }
      });

      await expect(subscriptionManager.createSubscription(validSubscriptionRequest))
        .rejects.toThrow('Failed to update user services: Service update failed');

      expect(mockDelete).toHaveBeenCalled();
    });
  });

  describe('updateSubscription', () => {
    const subscriptionId = 'sub-123';
    const newServiceIds = ['contacts', 'photos'];
    
    const mockExistingSubscription = {
      id: 'sub-123',
      user_id: 'user-123',
      plan_id: 'contacts-messages',
      total_price_cents: 249,
      status: 'active',
      stripe_subscription_id: 'sub_stripe_123',
      stripe_customer_id: 'cus_123',
      current_period_start: '2024-01-01T00:00:00.000Z',
      current_period_end: '2024-02-01T00:00:00.000Z',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z'
    };

    const mockPricingResult = {
      recommendedPlanId: 'contacts-photos',
      recommendedPlanName: 'Contacts + Photos',
      priceCents: 549,
      savingsCents: 100,
      individualTotalCents: 649
    };

    beforeEach(() => {
      mockServiceValidator.validateServiceCombination.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: []
      });
      
      mockPricingEngine.calculateOptimalPrice.mockResolvedValue(mockPricingResult);
    });

    it('should update subscription successfully', async () => {
      // Mock fetch existing subscription
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockExistingSubscription,
              error: null
            })
          })
        })
      });

      // Mock subscription update
      const updatedSubscription = {
        ...mockExistingSubscription,
        plan_id: 'contacts-photos',
        total_price_cents: 549,
        updated_at: '2024-01-02T00:00:00.000Z'
      };

      mockSupabase.from.mockReturnValueOnce({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: updatedSubscription,
                error: null
              })
            })
          })
        })
      });

      // Mock service update
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null
      });

      const result = await subscriptionManager.updateSubscription(subscriptionId, newServiceIds);

      expect(result.planId).toBe('contacts-photos');
      expect(result.totalPriceCents).toBe(549);
      expect(result.serviceIds).toEqual(['contacts', 'photos']);
      
      expect(mockServiceValidator.validateServiceCombination).toHaveBeenCalledWith(newServiceIds);
      expect(mockPricingEngine.calculateOptimalPrice).toHaveBeenCalledWith(newServiceIds);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_user_services', {
        user_uuid: 'user-123',
        new_service_ids: newServiceIds
      });
    });

    it('should throw error when subscription ID is missing', async () => {
      await expect(subscriptionManager.updateSubscription('', newServiceIds))
        .rejects.toThrow('Subscription ID is required');
    });

    it('should throw error when service IDs are missing', async () => {
      await expect(subscriptionManager.updateSubscription(subscriptionId, []))
        .rejects.toThrow('Service IDs are required');
    });

    it('should throw error when service combination is invalid', async () => {
      mockServiceValidator.validateServiceCombination.mockResolvedValue({
        isValid: false,
        errors: ['Invalid service combination'],
        warnings: []
      });

      await expect(subscriptionManager.updateSubscription(subscriptionId, newServiceIds))
        .rejects.toThrow('Invalid service combination: Invalid service combination');
    });

    it('should throw error when subscription is not found', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Subscription not found' }
            })
          })
        })
      });

      await expect(subscriptionManager.updateSubscription(subscriptionId, newServiceIds))
        .rejects.toThrow('Subscription not found: Subscription not found');
    });

    it('should throw error when subscription is not active', async () => {
      const inactiveSubscription = { ...mockExistingSubscription, status: 'cancelled' };
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: inactiveSubscription,
              error: null
            })
          })
        })
      });

      await expect(subscriptionManager.updateSubscription(subscriptionId, newServiceIds))
        .rejects.toThrow('Cannot update inactive subscription');
    });

    it('should rollback subscription update if service update fails', async () => {
      // Mock fetch existing subscription
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockExistingSubscription,
              error: null
            })
          })
        })
      });

      // Mock successful subscription update
      const updatedSubscription = {
        ...mockExistingSubscription,
        plan_id: 'contacts-photos',
        total_price_cents: 549
      };

      const mockUpdate = jest.fn().mockReturnValue({
        eq: jest.fn().mockResolvedValue({ data: null, error: null })
      });

      mockSupabase.from.mockReturnValueOnce({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: updatedSubscription,
                error: null
              })
            })
          })
        })
      }).mockReturnValueOnce({
        update: mockUpdate
      });

      // Mock service update failure
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Service update failed' }
      });

      await expect(subscriptionManager.updateSubscription(subscriptionId, newServiceIds))
        .rejects.toThrow('Failed to update user services: Service update failed');

      expect(mockUpdate).toHaveBeenCalled();
    });
  });

  describe('getSubscriptionDetails', () => {
    const userId = 'user-123';
    
    const mockSubscriptionDetails = {
      subscription_id: 'sub-123',
      plan_id: 'contacts-messages',
      plan_name: 'Contacts + Messages',
      current_price_cents: 249,
      status: 'active',
      services: ['contacts', 'messages'],
      storage_quota_gb: 50,
      storage_used_gb: 12.5,
      next_billing_date: '2024-02-01T00:00:00.000Z'
    };

    it('should return subscription details successfully', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [mockSubscriptionDetails],
        error: null
      });

      const result = await subscriptionManager.getSubscriptionDetails(userId);

      expect(result).toEqual({
        subscriptionId: 'sub-123',
        planId: 'contacts-messages',
        planName: 'Contacts + Messages',
        currentPriceCents: 249,
        status: 'active',
        services: ['contacts', 'messages'],
        storageQuotaGb: 50,
        storageUsedGb: 12.5,
        nextBillingDate: new Date('2024-02-01T00:00:00.000Z')
      });

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_user_subscription_details', {
        user_uuid: userId
      });
    });

    it('should return null when no subscription found', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null
      });

      const result = await subscriptionManager.getSubscriptionDetails(userId);

      expect(result).toBeNull();
    });

    it('should throw error when user ID is missing', async () => {
      await expect(subscriptionManager.getSubscriptionDetails(''))
        .rejects.toThrow('User ID is required');
    });

    it('should throw error when database call fails', async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      });

      await expect(subscriptionManager.getSubscriptionDetails(userId))
        .rejects.toThrow('Failed to get subscription details: Database error');
    });
  });

  describe('cancelSubscription', () => {
    const subscriptionId = 'sub-123';
    
    const mockExistingSubscription = {
      id: 'sub-123',
      user_id: 'user-123',
      status: 'active'
    };

    it('should cancel subscription successfully', async () => {
      // Mock fetch existing subscription
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockExistingSubscription,
              error: null
            })
          })
        })
      });

      // Mock service deactivation
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null
      });

      // Mock subscription status update
      mockSupabase.from.mockReturnValueOnce({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null
          })
        })
      });

      const result = await subscriptionManager.cancelSubscription(subscriptionId);

      expect(result).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_user_services', {
        user_uuid: 'user-123',
        new_service_ids: []
      });
    });

    it('should return true if subscription is already cancelled', async () => {
      const cancelledSubscription = { ...mockExistingSubscription, status: 'cancelled' };
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: cancelledSubscription,
              error: null
            })
          })
        })
      });

      const result = await subscriptionManager.cancelSubscription(subscriptionId);

      expect(result).toBe(true);
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
    });

    it('should throw error when subscription ID is missing', async () => {
      await expect(subscriptionManager.cancelSubscription(''))
        .rejects.toThrow('Subscription ID is required');
    });

    it('should throw error when subscription is not found', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Subscription not found' }
            })
          })
        })
      });

      await expect(subscriptionManager.cancelSubscription(subscriptionId))
        .rejects.toThrow('Subscription not found: Subscription not found');
    });

    it('should throw error when service deactivation fails', async () => {
      // Mock fetch existing subscription
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockExistingSubscription,
              error: null
            })
          })
        })
      });

      // Mock service deactivation failure
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Service deactivation failed' }
      });

      await expect(subscriptionManager.cancelSubscription(subscriptionId))
        .rejects.toThrow('Failed to deactivate services: Service deactivation failed');
    });

    it('should throw error when subscription status update fails', async () => {
      // Mock fetch existing subscription
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockExistingSubscription,
              error: null
            })
          })
        })
      });

      // Mock successful service deactivation
      mockSupabase.rpc.mockResolvedValue({
        data: true,
        error: null
      });

      // Mock subscription status update failure
      mockSupabase.from.mockReturnValueOnce({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Update failed' }
          })
        })
      });

      await expect(subscriptionManager.cancelSubscription(subscriptionId))
        .rejects.toThrow('Failed to cancel subscription: Update failed');
    });
  });
});