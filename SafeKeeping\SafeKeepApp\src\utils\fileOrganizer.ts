// File Organizer Utility for SafeKeep
// Provides functions for organizing files into virtual folders and managing tags

import { supabase } from '../config/supabase';
import { getCurrentUserId } from './supabaseHelpers';
import { FileMetadata, getFileMetadata, updateFileMetadata } from './fileMetadataManager';

export interface VirtualFolder {
  id: string;
  name: string;
  path: string;
  parentId?: string;
  fileCount: number;
}

/**
 * Move a file to a different virtual folder by updating its storage path
 */
export const moveFileToFolder = async (
  fileId: string,
  newFolderPath: string
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Get current file metadata
    const { data: file, error: fetchError } = await getFileMetadata(fileId);
    if (fetchError || !file) {
      return { success: false, error: fetchError || 'File not found' };
    }

    // Verify user owns this file
    if (file.user_id !== userId) {
      return { success: false, error: 'Access denied' };
    }

    // Extract the filename from the current path
    const pathParts = file.storage_path.split('/');
    const fileName = pathParts[pathParts.length - 1];

    // Build new storage path
    let newPath: string;
    if (newFolderPath) {
      // Ensure the folder path starts with the user ID
      if (!newFolderPath.startsWith(userId)) {
        newPath = `${userId}/${newFolderPath}/${fileName}`;
      } else {
        newPath = `${newFolderPath}/${fileName}`;
      }
    } else {
      // If no folder path, place directly under user ID
      newPath = `${userId}/${fileName}`;
    }

    // Update the storage path in metadata
    const { success, error: updateError } = await updateFileMetadata(fileId, {
      storage_path: newPath
    });

    if (!success) {
      return { success: false, error: updateError };
    }

    // Note: This doesn't physically move the file in storage
    // It only updates the metadata path for organization purposes
    // The actual file remains at its original storage location

    return { success: true, error: null };
  } catch (error) {
    console.error('Error moving file to folder:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Create a new virtual folder structure
 */
export const createVirtualFolder = async (
  folderPath: string
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Virtual folders are just path prefixes in the metadata
    // No actual folder creation is needed in storage
    // This function just validates the folder path

    // Ensure folder path doesn't contain invalid characters
    const invalidChars = /[<>:"\\|?*]/;
    if (invalidChars.test(folderPath)) {
      return { 
        success: false, 
        error: 'Folder name contains invalid characters' 
      };
    }

    // Normalize path (remove leading/trailing slashes)
    const normalizedPath = folderPath.replace(/^\/+|\/+$/g, '');

    // Store folder in user preferences if needed
    // This is optional as folders are virtual and derived from file paths
    const { error } = await supabase.rpc('add_virtual_folder', {
      user_id_param: userId,
      folder_path_param: normalizedPath
    });

    // If RPC doesn't exist, just return success
    // Virtual folders don't require actual storage
    
    return { success: true, error: null };
  } catch (error) {
    console.error('Error creating virtual folder:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all virtual folders for the current user
 */
export const getVirtualFolders = async (): Promise<{ 
  folders: VirtualFolder[]; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { folders: [], error: 'User not authenticated' };
    }

    // Try to get folders from user preferences first
    try {
      const { data, error } = await supabase.rpc('get_virtual_folders', {
        user_id_param: userId
      });

      if (!error && data) {
        return { folders: data, error: null };
      }
    } catch (rpcError) {
      // RPC might not exist, fall back to deriving from file paths
      console.warn('RPC not available, deriving folders from file paths');
    }

    // Derive virtual folders from file paths
    const { data: files, error } = await supabase
      .from('file_metadata')
      .select('storage_path')
      .eq('user_id', userId);

    if (error) {
      return { folders: [], error: error.message };
    }

    // Extract folder paths and count files in each
    const folderCounts: Record<string, number> = {};
    
    files.forEach(file => {
      const path = file.storage_path;
      const parts = path.split('/');
      
      // Skip the first part (user ID) and the last part (filename)
      if (parts.length <= 2) return; // No folder structure
      
      // Build folder paths at each level
      for (let i = 1; i < parts.length - 1; i++) {
        const folderPath = parts.slice(0, i + 1).join('/');
        folderCounts[folderPath] = (folderCounts[folderPath] || 0) + 1;
      }
    });

    // Convert to folder objects
    const folders: VirtualFolder[] = Object.entries(folderCounts).map(([path, count], index) => {
      const parts = path.split('/');
      const name = parts[parts.length - 1];
      const parentPath = parts.slice(0, -1).join('/');
      
      return {
        id: `folder-${index}`,
        name,
        path,
        parentId: parentPath || undefined,
        fileCount: count
      };
    });

    return { folders, error: null };
  } catch (error) {
    console.error('Error getting virtual folders:', error);
    return { folders: [], error: error.message };
  }
};

/**
 * Get files in a specific virtual folder
 */
export const getFilesInFolder = async (
  folderPath: string
): Promise<{ files: FileMetadata[]; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { files: [], error: 'User not authenticated' };
    }

    // Ensure folder path starts with user ID for security
    const securePath = folderPath.startsWith(userId) 
      ? folderPath 
      : `${userId}/${folderPath}`;

    // Query files with storage path starting with the folder path
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId) // Security check
      .ilike('storage_path', `${securePath}/%`);

    if (error) {
      return { files: [], error: error.message };
    }

    return { files: data as FileMetadata[], error: null };
  } catch (error) {
    console.error('Error getting files in folder:', error);
    return { files: [], error: error.message };
  }
};

/**
 * Rename a virtual folder by updating all file paths
 */
export const renameVirtualFolder = async (
  oldPath: string,
  newName: string
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Ensure folder path starts with user ID for security
    const securePath = oldPath.startsWith(userId) 
      ? oldPath 
      : `${userId}/${oldPath}`;

    // Get all files in this folder
    const { files, error: fetchError } = await getFilesInFolder(oldPath);
    if (fetchError) {
      return { success: false, error: fetchError };
    }

    // Create the new path
    const pathParts = securePath.split('/');
    pathParts[pathParts.length - 1] = newName;
    const newPath = pathParts.join('/');

    // Update each file's storage path
    for (const file of files) {
      const newFilePath = file.storage_path.replace(securePath, newPath);
      
      const { success, error } = await updateFileMetadata(file.id!, {
        storage_path: newFilePath
      });
      
      if (!success) {
        console.error(`Failed to update path for file ${file.id}:`, error);
        // Continue with other files
      }
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error renaming virtual folder:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Delete a virtual folder by removing all files in it
 */
export const deleteVirtualFolder = async (
  folderPath: string,
  deleteFiles: boolean = false
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Get all files in this folder
    const { files, error: fetchError } = await getFilesInFolder(folderPath);
    if (fetchError) {
      return { success: false, error: fetchError };
    }

    if (deleteFiles) {
      // Delete all files in the folder
      for (const file of files) {
        const { error } = await supabase
          .from('file_metadata')
          .delete()
          .eq('id', file.id)
          .eq('user_id', userId); // Security check
          
        if (error) {
          console.error(`Failed to delete file ${file.id}:`, error);
          // Continue with other files
        }
      }
    } else {
      // Move files to root folder
      for (const file of files) {
        const fileName = file.storage_path.split('/').pop();
        const newPath = `${userId}/${fileName}`;
        
        const { success, error } = await updateFileMetadata(file.id!, {
          storage_path: newPath
        });
        
        if (!success) {
          console.error(`Failed to move file ${file.id}:`, error);
          // Continue with other files
        }
      }
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error deleting virtual folder:', error);
    return { success: false, error: error.message };
  }
};/
**
 * Batch move multiple files to a folder
 */
export const batchMoveFilesToFolder = async (
  fileIds: string[],
  newFolderPath: string
): Promise<{ 
  success: boolean; 
  movedCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, movedCount: 0, error: 'User not authenticated' };
    }

    // Ensure folder path starts with user ID for security
    const securePath = newFolderPath.startsWith(userId) 
      ? newFolderPath 
      : `${userId}/${newFolderPath}`;

    let movedCount = 0;
    
    // Process files in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < fileIds.length; i += batchSize) {
      const batch = fileIds.slice(i, i + batchSize);
      
      for (const fileId of batch) {
        // Get current file metadata
        const { data: file, error: fetchError } = await supabase
          .from('file_metadata')
          .select('storage_path')
          .eq('id', fileId)
          .eq('user_id', userId) // Security check
          .single();
          
        if (fetchError || !file) {
          console.error(`Error fetching file ${fileId}:`, fetchError);
          continue;
        }
        
        // Extract the filename from the current path
        const fileName = file.storage_path.split('/').pop();
        
        // Build new storage path
        const newPath = `${securePath}/${fileName}`;
        
        // Update the storage path
        const { error: updateError } = await supabase
          .from('file_metadata')
          .update({ storage_path: newPath })
          .eq('id', fileId)
          .eq('user_id', userId); // Security check
          
        if (updateError) {
          console.error(`Error updating path for file ${fileId}:`, updateError);
          continue;
        }
        
        movedCount++;
      }
    }
    
    return { 
      success: movedCount > 0, 
      movedCount, 
      error: movedCount === fileIds.length ? null : `Only moved ${movedCount} of ${fileIds.length} files` 
    };
  } catch (error) {
    console.error('Error in batch move files to folder:', error);
    return { success: false, movedCount: 0, error: (error as Error).message };
  }
};

/**
 * Create nested folder structure
 */
export const createNestedFolders = async (
  folderPath: string
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Normalize path (remove leading/trailing slashes)
    const normalizedPath = folderPath.replace(/^\/+|\/+$/g, '');
    
    // Split path into segments
    const segments = normalizedPath.split('/');
    
    // Create each folder level
    let currentPath = '';
    for (let i = 0; i < segments.length; i++) {
      if (i === 0) {
        currentPath = segments[i];
      } else {
        currentPath += '/' + segments[i];
      }
      
      // Add this folder level to user preferences
      const { error } = await supabase.rpc('add_virtual_folder', {
        user_id_param: userId,
        folder_path_param: currentPath
      });
      
      if (error) {
        console.warn(`Error adding folder ${currentPath} to preferences:`, error);
        // Continue anyway since folders are virtual
      }
    }
    
    return { success: true, error: null };
  } catch (error) {
    console.error('Error creating nested folders:', error);
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Search files in a specific folder
 */
export const searchFilesInFolder = async (
  folderPath: string,
  searchTerm?: string,
  category?: 'photo' | 'contact' | 'message'
): Promise<{ files: FileMetadata[]; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { files: [], error: 'User not authenticated' };
    }

    // Ensure folder path starts with user ID for security
    const securePath = folderPath.startsWith(userId) 
      ? folderPath 
      : `${userId}/${folderPath}`;

    // Build query
    let query = supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId) // Security check
      .ilike('storage_path', `${securePath}/%`);
      
    // Add category filter if specified
    if (category) {
      query = query.eq('category', category);
    }
    
    // Add search term filter if specified
    if (searchTerm) {
      query = query.or(`original_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }
    
    const { data, error } = await query;
    
    if (error) {
      return { files: [], error: error.message };
    }
    
    return { files: data as FileMetadata[], error: null };
  } catch (error) {
    console.error('Error searching files in folder:', error);
    return { files: [], error: (error as Error).message };
  }
};

/**
 * Get folder statistics
 */
export const getFolderStatistics = async (
  folderPath: string
): Promise<{ 
  fileCount: number;
  totalSize: number;
  categories: Record<string, number>;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        fileCount: 0, 
        totalSize: 0, 
        categories: {}, 
        error: 'User not authenticated' 
      };
    }

    // Ensure folder path starts with user ID for security
    const securePath = folderPath.startsWith(userId) 
      ? folderPath 
      : `${userId}/${folderPath}`;

    // Get files in folder
    const { files, error } = await getFilesInFolder(folderPath);
    
    if (error) {
      return { 
        fileCount: 0, 
        totalSize: 0, 
        categories: {}, 
        error 
      };
    }
    
    // Calculate statistics
    const fileCount = files.length;
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    
    // Count files by category
    const categories: Record<string, number> = {};
    files.forEach(file => {
      if (!categories[file.category]) {
        categories[file.category] = 0;
      }
      categories[file.category]++;
    });
    
    return {
      fileCount,
      totalSize,
      categories,
      error: null
    };
  } catch (error) {
    console.error('Error getting folder statistics:', error);
    return { 
      fileCount: 0, 
      totalSize: 0, 
      categories: {}, 
      error: (error as Error).message 
    };
  }
};

/**
 * Get folder hierarchy
 */
export const getFolderHierarchy = async (): Promise<{ 
  hierarchy: { 
    id: string; 
    name: string; 
    path: string; 
    children: string[]; 
    fileCount: number;
  }[]; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { hierarchy: [], error: 'User not authenticated' };
    }

    // Get all virtual folders
    const { folders, error } = await getVirtualFolders();
    
    if (error) {
      return { hierarchy: [], error };
    }
    
    // Build hierarchy
    const folderMap: Record<string, { 
      id: string; 
      name: string; 
      path: string; 
      children: string[]; 
      fileCount: number;
    }> = {};
    
    // First pass: create folder objects
    folders.forEach(folder => {
      folderMap[folder.path] = {
        id: folder.id,
        name: folder.name,
        path: folder.path,
        children: [],
        fileCount: folder.fileCount
      };
    });
    
    // Second pass: build parent-child relationships
    folders.forEach(folder => {
      const pathParts = folder.path.split('/');
      
      if (pathParts.length > 1) {
        // This folder has a parent
        const parentPath = pathParts.slice(0, -1).join('/');
        
        if (folderMap[parentPath]) {
          folderMap[parentPath].children.push(folder.path);
        }
      }
    });
    
    // Convert to array
    const hierarchy = Object.values(folderMap);
    
    return { hierarchy, error: null };
  } catch (error) {
    console.error('Error getting folder hierarchy:', error);
    return { hierarchy: [], error: (error as Error).message };
  }
};

/**
 * Organize files by date
 */
export const organizeFilesByDate = async (
  fileIds: string[],
  dateFormat: 'year' | 'year-month' | 'year-month-day' = 'year-month'
): Promise<{ 
  success: boolean; 
  organizedCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, organizedCount: 0, error: 'User not authenticated' };
    }

    let organizedCount = 0;
    
    // Process files in batches
    const batchSize = 10;
    for (let i = 0; i < fileIds.length; i += batchSize) {
      const batch = fileIds.slice(i, i + batchSize);
      
      for (const fileId of batch) {
        // Get file metadata
        const { data: file, error: fetchError } = await supabase
          .from('file_metadata')
          .select('*')
          .eq('id', fileId)
          .eq('user_id', userId) // Security check
          .single();
          
        if (fetchError || !file) {
          console.error(`Error fetching file ${fileId}:`, fetchError);
          continue;
        }
        
        // Extract date from uploaded_at
        const uploadDate = new Date(file.uploaded_at);
        const year = uploadDate.getFullYear();
        const month = (uploadDate.getMonth() + 1).toString().padStart(2, '0');
        const day = uploadDate.getDate().toString().padStart(2, '0');
        
        // Build folder path based on date format
        let folderPath: string;
        switch (dateFormat) {
          case 'year':
            folderPath = `${userId}/${file.category}s/${year}`;
            break;
          case 'year-month':
            folderPath = `${userId}/${file.category}s/${year}/${month}`;
            break;
          case 'year-month-day':
            folderPath = `${userId}/${file.category}s/${year}/${month}/${day}`;
            break;
        }
        
        // Extract filename
        const fileName = file.storage_path.split('/').pop();
        
        // Build new storage path
        const newPath = `${folderPath}/${fileName}`;
        
        // Update storage path
        const { error: updateError } = await supabase
          .from('file_metadata')
          .update({ storage_path: newPath })
          .eq('id', fileId)
          .eq('user_id', userId); // Security check
          
        if (updateError) {
          console.error(`Error updating path for file ${fileId}:`, updateError);
          continue;
        }
        
        // Add virtual folder to user preferences
        await supabase.rpc('add_virtual_folder', {
          user_id_param: userId,
          folder_path_param: folderPath.substring(userId.length + 1) // Remove user ID prefix
        }).catch(error => {
          console.warn(`Error adding folder ${folderPath} to preferences:`, error);
          // Continue anyway since folders are virtual
        });
        
        organizedCount++;
      }
    }
    
    return { 
      success: organizedCount > 0, 
      organizedCount, 
      error: organizedCount === fileIds.length ? null : `Only organized ${organizedCount} of ${fileIds.length} files` 
    };
  } catch (error) {
    console.error('Error organizing files by date:', error);
    return { success: false, organizedCount: 0, error: (error as Error).message };
  }
};

/**
 * Organize files by category
 */
export const organizeFilesByCategory = async (
  fileIds: string[]
): Promise<{ 
  success: boolean; 
  organizedCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, organizedCount: 0, error: 'User not authenticated' };
    }

    let organizedCount = 0;
    
    // Process files in batches
    const batchSize = 10;
    for (let i = 0; i < fileIds.length; i += batchSize) {
      const batch = fileIds.slice(i, i + batchSize);
      
      for (const fileId of batch) {
        // Get file metadata
        const { data: file, error: fetchError } = await supabase
          .from('file_metadata')
          .select('*')
          .eq('id', fileId)
          .eq('user_id', userId) // Security check
          .single();
          
        if (fetchError || !file) {
          console.error(`Error fetching file ${fileId}:`, fetchError);
          continue;
        }
        
        // Build folder path based on category
        const folderPath = `${userId}/${file.category}s`;
        
        // Extract filename
        const fileName = file.storage_path.split('/').pop();
        
        // Build new storage path
        const newPath = `${folderPath}/${fileName}`;
        
        // Update storage path
        const { error: updateError } = await supabase
          .from('file_metadata')
          .update({ storage_path: newPath })
          .eq('id', fileId)
          .eq('user_id', userId); // Security check
          
        if (updateError) {
          console.error(`Error updating path for file ${fileId}:`, updateError);
          continue;
        }
        
        // Add virtual folder to user preferences
        await supabase.rpc('add_virtual_folder', {
          user_id_param: userId,
          folder_path_param: file.category + 's' // Remove user ID prefix
        }).catch(error => {
          console.warn(`Error adding folder ${folderPath} to preferences:`, error);
          // Continue anyway since folders are virtual
        });
        
        organizedCount++;
      }
    }
    
    return { 
      success: organizedCount > 0, 
      organizedCount, 
      error: organizedCount === fileIds.length ? null : `Only organized ${organizedCount} of ${fileIds.length} files` 
    };
  } catch (error) {
    console.error('Error organizing files by category:', error);
    return { success: false, organizedCount: 0, error: (error as Error).message };
  }
};