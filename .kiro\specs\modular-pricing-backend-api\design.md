# Design Document

## Overview

This design document outlines the backend API updates needed to support SafeKeep's new modular pricing system. The system will extend the existing backend API to handle service-specific subscriptions, optimal pricing calculations, and service access validation. The design leverages the existing database functions and Stripe integration while adding new endpoints and business logic for modular pricing.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Frontend Client] --> B[Backend API]
    B --> C[Pricing Engine]
    B --> D[Subscription Manager]
    B --> E[Service Validator]
    B --> F[Stripe Integration]
    B --> G[Database Layer]
    
    C --> G
    D --> G
    E --> G
    F --> H[Stripe API]
    
    G --> I[Supabase Database]
    I --> J[Pricing Functions]
    I --> K[User Subscriptions]
    I --> L[Service Selections]
```

### API Layer Structure

The backend API will be organized into the following modules:

1. **Pricing Controller** - Handles pricing calculations and recommendations
2. **Subscription Controller** - Manages subscription creation and updates
3. **Service Controller** - Validates service access and combinations
4. **Billing Controller** - Handles billing logic and Stripe integration

## Components and Interfaces

### 1. Pricing Engine

**Purpose**: Calculate optimal pricing for service combinations

**Key Methods**:
- `calculateOptimalPrice(serviceIds: string[]): PricingResult`
- `getAvailableServiceCombinations(): ServiceCombination[]`
- `getPlanRecommendations(userId: string): PlanRecommendation[]`

**Interface**:
```typescript
interface PricingResult {
  recommendedPlanId: string;
  recommendedPlanName: string;
  priceCents: number;
  savingsCents: number;
  individualTotalCents: number;
}

interface ServiceCombination {
  planId: string;
  planName: string;
  priceCents: number;
  services: string[];
  storageGb: number;
  isPopular: boolean;
}
```

### 2. Subscription Manager

**Purpose**: Handle subscription lifecycle with service combinations

**Key Methods**:
- `createSubscription(userId: string, serviceIds: string[], paymentMethodId: string): Subscription`
- `updateSubscription(subscriptionId: string, serviceIds: string[]): Subscription`
- `getSubscriptionDetails(userId: string): SubscriptionDetails`

**Interface**:
```typescript
interface SubscriptionRequest {
  userId: string;
  serviceIds: string[];
  paymentMethodId?: string;
  customerId?: string;
}

interface SubscriptionDetails {
  subscriptionId: string;
  planId: string;
  planName: string;
  currentPriceCents: number;
  status: string;
  services: string[];
  storageQuotaGb: number;
  storageUsedGb: number;
  nextBillingDate: Date;
}
```

### 3. Service Validator

**Purpose**: Validate service combinations and user access

**Key Methods**:
- `validateServiceCombination(serviceIds: string[]): ValidationResult`
- `checkServiceAccess(userId: string, serviceType: string): AccessResult`
- `getUserServices(userId: string): UserService[]`

**Interface**:
```typescript
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface AccessResult {
  hasAccess: boolean;
  expiresAt?: Date;
  planName?: string;
}
```

### 4. Billing Integration

**Purpose**: Handle Stripe integration for modular pricing

**Key Methods**:
- `createPaymentIntent(amount: number, metadata: BillingMetadata): PaymentIntent`
- `processSubscriptionPayment(subscriptionData: SubscriptionRequest): PaymentResult`
- `handleWebhook(event: StripeEvent): WebhookResult`

## Data Models

### Enhanced Subscription Model

```typescript
interface ModularSubscription {
  id: string;
  userId: string;
  planId: string;
  serviceIds: string[];
  totalPriceCents: number;
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  stripeSubscriptionId?: string;
  stripeCustomerId?: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Service Selection Model

```typescript
interface ServiceSelection {
  userId: string;
  serviceTypeId: string;
  isActive: boolean;
  activatedAt: Date;
  deactivatedAt?: Date;
}
```

### Pricing Calculation Model

```typescript
interface PricingCalculation {
  requestedServices: string[];
  optimalPlan: {
    planId: string;
    planName: string;
    priceCents: number;
  };
  alternatives: Array<{
    planId: string;
    planName: string;
    priceCents: number;
    savingsCents: number;
  }>;
  individualTotal: number;
  calculatedAt: Date;
}
```

## Error Handling

### Error Types

1. **Validation Errors** (400)
   - Invalid service combination
   - Missing required fields
   - Invalid service IDs

2. **Authentication Errors** (401)
   - Invalid user token
   - Expired session

3. **Authorization Errors** (403)
   - Insufficient permissions
   - Service access denied

4. **Business Logic Errors** (422)
   - Subscription already exists
   - Payment method required
   - Service not available

5. **External Service Errors** (502/503)
   - Stripe API failures
   - Database connection issues

### Error Response Format

```typescript
interface ErrorResponse {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
  };
}
```

## Testing Strategy

### Unit Testing

1. **Pricing Engine Tests**
   - Test optimal pricing calculations
   - Test service combination validation
   - Test edge cases (empty services, invalid services)

2. **Subscription Manager Tests**
   - Test subscription creation flow
   - Test subscription updates
   - Test subscription cancellation

3. **Service Validator Tests**
   - Test service combination validation
   - Test access control logic
   - Test user service retrieval

### Integration Testing

1. **Database Integration**
   - Test database function calls
   - Test transaction handling
   - Test data consistency

2. **Stripe Integration**
   - Test payment intent creation
   - Test webhook processing
   - Test subscription management

3. **API Endpoint Testing**
   - Test all new endpoints
   - Test error handling
   - Test authentication/authorization

### End-to-End Testing

1. **Complete Subscription Flow**
   - User selects services
   - System calculates pricing
   - User completes payment
   - Subscription is activated
   - Services are accessible

2. **Subscription Management Flow**
   - User upgrades/downgrades services
   - Billing is updated
   - Access permissions are updated

## API Endpoints

### Pricing Endpoints

```
GET /api/pricing/combinations
- Returns available service combinations with pricing

POST /api/pricing/calculate
- Body: { serviceIds: string[] }
- Returns optimal pricing for selected services

GET /api/pricing/recommendations/:userId
- Returns upgrade/downgrade recommendations for user
```

### Subscription Endpoints

```
POST /api/subscriptions
- Body: { userId: string, serviceIds: string[], paymentMethodId?: string }
- Creates new subscription with service combination

PUT /api/subscriptions/:subscriptionId
- Body: { serviceIds: string[] }
- Updates existing subscription services

GET /api/subscriptions/:userId
- Returns user's current subscription details

DELETE /api/subscriptions/:subscriptionId
- Cancels subscription
```

### Service Endpoints

```
GET /api/services/user/:userId
- Returns user's active services

POST /api/services/validate
- Body: { serviceIds: string[] }
- Validates service combination

GET /api/services/access/:userId/:serviceType
- Checks if user has access to specific service
```

### Billing Endpoints

```
POST /api/billing/payment-intent
- Body: { amount: number, serviceIds: string[], userId: string }
- Creates Stripe payment intent for service combination

POST /api/billing/webhook
- Handles Stripe webhook events

GET /api/billing/status/:userId
- Returns billing status and next payment date
```

## Security Considerations

### Authentication
- All endpoints require valid JWT token
- User ID extracted from token, not request body

### Authorization
- Users can only access their own data
- Admin endpoints require admin role

### Data Validation
- All input validated against schemas
- Service IDs validated against database
- Pricing calculations verified

### Rate Limiting
- Pricing calculation endpoints limited to prevent abuse
- Webhook endpoints have separate rate limits

## Performance Considerations

### Caching Strategy
- Cache service combinations for 1 hour
- Cache user subscription details for 15 minutes
- Cache pricing calculations for 5 minutes

### Database Optimization
- Use existing database indexes
- Batch service validation queries
- Optimize pricing calculation functions

### Monitoring
- Track API response times
- Monitor database query performance
- Alert on Stripe API failures