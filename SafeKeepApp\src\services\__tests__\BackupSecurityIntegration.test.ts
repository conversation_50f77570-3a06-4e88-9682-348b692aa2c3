import EncryptionService from '../EncryptionService';
import EncryptionValidationService from '../EncryptionValidationService';
import SecureKeyManagementService from '../SecureKeyManagementService';
import DataIntegrityService from '../DataIntegrityService';
import { supabase } from '../../utils/supabaseClient';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  getAllKeys: jest.fn(),
}));

// Mock Supabase client
jest.mock('../../utils/supabaseClient', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null }))
        }))
      })),
      insert: jest.fn(() => Promise.resolve({ data: null, error: null })),
      update: jest.fn(() => Promise.resolve({ data: null, error: null })),
      delete: jest.fn(() => Promise.resolve({ data: null, error: null }))
    })),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(() => Promise.resolve({ data: { path: 'test-path' }, error: null })),
        download: jest.fn(() => Promise.resolve({ data: new Blob(), error: null }))
      }))
    }
  }
}));

describe('Backup Security Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('End-to-End Encryption Security Flow', () => {
    it('should validate complete encryption flow for all data types', async () => {
      // Initialize secure key management
      const keyResult = await SecureKeyManagementService.generateSecureMasterKey();
      await SecureKeyManagementService.storeSecureMasterKey(
        keyResult.masterKey,
        keyResult.metadata
      );

      // Test data for all backup types
      const testData = {
        contacts: [
          {
            recordID: '123',
            displayName: 'John Doe',
            phoneNumbers: [{ label: 'mobile', number: '+1234567890' }],
            emailAddresses: [{ label: 'work', email: '<EMAIL>' }]
          }
        ],
        messages: [
          {
            id: '456',
            threadId: 'thread123',
            address: '+1234567890',
            body: 'Sensitive message content',
            date: Date.now(),
            type: 1
          }
        ],
        photos: [
          {
            uri: 'file://photo.jpg',
            filename: 'vacation.jpg',
            timestamp: Date.now(),
            type: 'image/jpeg',
            fileSize: 2048000
          }
        ]
      };

      // Step 1: Validate encryption for all data types
      const encryptionValidation = await EncryptionValidationService.validateEncryptionForAllDataTypes(testData);
      
      expect(encryptionValidation.success).toBe(true);
      expect(encryptionValidation.validationResults.contacts.encrypted).toBe(true);
      expect(encryptionValidation.validationResults.messages.encrypted).toBe(true);
      expect(encryptionValidation.validationResults.photos.encrypted).toBe(true);

      // Step 2: Test individual data type encryption
      for (const [dataType, items] of Object.entries(testData)) {
        if (items.length > 0) {
          const serializedData = JSON.stringify(items[0]);
          
          // Encrypt
          const encrypted = await EncryptionService.encryptData(serializedData);
          expect(encrypted.encryptedData).toBeDefined();
          expect(encrypted.iv).toBeDefined();
          expect(encrypted.salt).toBeDefined();
          
          // Verify data is actually encrypted
          expect(encrypted.encryptedData).not.toContain(JSON.stringify(items[0]));
          
          // Decrypt and verify
          const decrypted = await EncryptionService.decryptData(
            encrypted.encryptedData,
            encrypted.iv,
            encrypted.salt
          );
          
          expect(decrypted.success).toBe(true);
          expect(decrypted.data).toBe(serializedData);
          
          // Validate integrity
          const integrityResult = await DataIntegrityService.validateEncryptedDataIntegrity(
            encrypted.encryptedData,
            encrypted.iv,
            encrypted.salt,
            DataIntegrityService.generateChecksum(serializedData)
          );
          
          expect(integrityResult.isValid).toBe(true);
        }
      }
    });

    it('should validate transmission security for backup endpoints', async () => {
      const endpoints = [
        'https://api.supabase.co',
        'https://backup-service.safekeep.app',
        'https://secure-storage.example.com'
      ];

      for (const endpoint of endpoints) {
        const transmissionResult = await EncryptionValidationService.validateTransmissionSecurity(endpoint);
        
        expect(transmissionResult.isSecure).toBe(true);
        expect(transmissionResult.protocol).toBe('https');
        expect(transmissionResult.tlsVersion).toBeDefined();
        expect(transmissionResult.certificateValid).toBe(true);
        expect(transmissionResult.securityIssues).toHaveLength(0);
      }
    });

    it('should validate encryption at rest in storage', async () => {
      const testData = 'sensitive backup data for storage test';
      const encrypted = await EncryptionService.encryptData(testData);
      const checksum = DataIntegrityService.generateChecksum(encrypted.encryptedData);

      const storageData = {
        encryptedData: encrypted.encryptedData,
        iv: encrypted.iv,
        salt: encrypted.salt,
        checksum
      };

      const storageResult = await EncryptionValidationService.validateEncryptionAtRest(storageData);
      
      expect(storageResult.encrypted).toBe(true);
      expect(storageResult.storageSecure).toBe(true);
      expect(storageResult.checksumValid).toBe(true);
      expect(storageResult.encryptionAlgorithm).toBe('AES-256-CBC');
    });
  });

  describe('Key Management Security Integration', () => {
    it('should validate secure key lifecycle management', async () => {
      // Generate initial key
      const initialKey = await SecureKeyManagementService.generateSecureMasterKey();
      await SecureKeyManagementService.storeSecureMasterKey(
        initialKey.masterKey,
        initialKey.metadata
      );

      // Validate initial key security
      let validation = await SecureKeyManagementService.validateKeySecurity();
      expect(validation.isValid).toBe(true);
      expect(validation.securityLevel).toBe('high');
      expect(validation.needsRotation).toBe(false);

      // Test key integrity
      let integrity = await SecureKeyManagementService.verifyKeyIntegrity();
      expect(integrity.isIntact).toBe(true);
      expect(integrity.tamperingDetected).toBe(false);

      // Rotate key
      const rotationResult = await SecureKeyManagementService.rotateKey();
      expect(rotationResult.success).toBe(true);
      expect(rotationResult.newKeyId).not.toBe(initialKey.keyId);

      // Validate new key
      validation = await SecureKeyManagementService.validateKeySecurity();
      expect(validation.isValid).toBe(true);
      expect(validation.securityLevel).toBe('high');

      // Test encryption with new key
      const testData = 'test data with rotated key';
      const encrypted = await EncryptionService.encryptData(testData);
      const decrypted = await EncryptionService.decryptData(
        encrypted.encryptedData,
        encrypted.iv,
        encrypted.salt
      );

      expect(decrypted.success).toBe(true);
      expect(decrypted.data).toBe(testData);
    });

    it('should validate passphrase-protected key security', async () => {
      const passphrase = 'secure_user_passphrase_123!';
      
      // Generate and store key with passphrase
      const keyResult = await SecureKeyManagementService.generateSecureMasterKey();
      await SecureKeyManagementService.storeSecureMasterKey(
        keyResult.masterKey,
        keyResult.metadata,
        passphrase
      );

      // Retrieve with correct passphrase
      const retrieved = await SecureKeyManagementService.retrieveSecureMasterKey(passphrase);
      expect(retrieved).toBeDefined();
      expect(retrieved?.masterKey).toBe(keyResult.masterKey);

      // Test encryption with passphrase-protected key
      const testData = 'passphrase protected encryption test';
      const encrypted = await EncryptionService.encryptData(testData);
      const decrypted = await EncryptionService.decryptData(
        encrypted.encryptedData,
        encrypted.iv,
        encrypted.salt
      );

      expect(decrypted.success).toBe(true);
      expect(decrypted.data).toBe(testData);

      // Verify wrong passphrase fails
      const wrongRetrieval = await SecureKeyManagementService.retrieveSecureMasterKey('wrong_passphrase');
      expect(wrongRetrieval).toBeNull();
    });

    it('should validate key backup and recovery', async () => {
      const keyResult = await SecureKeyManagementService.generateSecureMasterKey();
      
      // Create backup
      await SecureKeyManagementService.createKeyBackup(
        keyResult.masterKey,
        keyResult.metadata,
        'Integration test backup'
      );

      // Verify backup was created (would be stored in AsyncStorage)
      // In a real scenario, we'd verify the backup can be used for recovery
      
      // Test that original key still works after backup
      const testData = 'test data after backup creation';
      const encrypted = await EncryptionService.encryptData(testData);
      const decrypted = await EncryptionService.decryptData(
        encrypted.encryptedData,
        encrypted.iv,
        encrypted.salt
      );

      expect(decrypted.success).toBe(true);
      expect(decrypted.data).toBe(testData);
    });
  });

  describe('Data Integrity Validation Integration', () => {
    it('should validate complete backup session integrity', async () => {
      const sessionId = 'integration-test-session';
      
      // Prepare test data
      const contactData = JSON.stringify({
        recordID: '123',
        displayName: 'Test Contact',
        phoneNumbers: [{ label: 'mobile', number: '+1234567890' }]
      });
      
      const messageData = JSON.stringify({
        id: '456',
        body: 'Test message content',
        address: '+1234567890',
        date: Date.now()
      });

      // Encrypt data
      const encryptedContact = await EncryptionService.encryptData(contactData);
      const encryptedMessage = await EncryptionService.encryptData(messageData);

      // Generate checksums
      const contactChecksum = DataIntegrityService.generateChecksum(contactData);
      const messageChecksum = DataIntegrityService.generateChecksum(messageData);

      // Create backup items
      const backupItems = [
        {
          id: 'contact-item-1',
          type: 'contact' as const,
          encryptedData: encryptedContact.encryptedData,
          iv: encryptedContact.iv,
          salt: encryptedContact.salt,
          checksum: contactChecksum
        },
        {
          id: 'message-item-1',
          type: 'message' as const,
          encryptedData: encryptedMessage.encryptedData,
          iv: encryptedMessage.iv,
          salt: encryptedMessage.salt,
          checksum: messageChecksum
        }
      ];

      // Validate session integrity
      const integrityReport = await DataIntegrityService.validateBackupSessionIntegrity(
        sessionId,
        backupItems
      );

      expect(integrityReport.sessionId).toBe(sessionId);
      expect(integrityReport.overallIntegrity).toBe(true);
      expect(integrityReport.itemsChecked).toBe(2);
      expect(integrityReport.itemsValid).toBe(2);
      expect(integrityReport.itemsCorrupted).toBe(0);
      expect(integrityReport.corruptedItems).toHaveLength(0);

      // Generate integrity manifest
      const manifest = await DataIntegrityService.generateIntegrityManifest(
        sessionId,
        [
          {
            id: 'contact-item-1',
            type: 'contact',
            originalData: contactData,
            encryptedData: encryptedContact.encryptedData,
            iv: encryptedContact.iv,
            salt: encryptedContact.salt
          },
          {
            id: 'message-item-1',
            type: 'message',
            originalData: messageData,
            encryptedData: encryptedMessage.encryptedData,
            iv: encryptedMessage.iv,
            salt: encryptedMessage.salt
          }
        ]
      );

      expect(manifest.sessionId).toBe(sessionId);
      expect(manifest.totalItems).toBe(2);
      expect(manifest.items).toHaveLength(2);
      expect(manifest.sessionChecksum).toBeDefined();
    });

    it('should detect and report data corruption', async () => {
      const sessionId = 'corruption-test-session';
      const originalData = 'original test data';
      const encrypted = await EncryptionService.encryptData(originalData);
      
      // Create corrupted backup item
      const corruptedItems = [
        {
          id: 'corrupted-item',
          type: 'contact' as const,
          encryptedData: encrypted.encryptedData.slice(0, -10) + 'corrupted', // Corrupt data
          iv: encrypted.iv,
          salt: encrypted.salt,
          checksum: DataIntegrityService.generateChecksum(originalData)
        }
      ];

      const integrityReport = await DataIntegrityService.validateBackupSessionIntegrity(
        sessionId,
        corruptedItems
      );

      expect(integrityReport.overallIntegrity).toBe(false);
      expect(integrityReport.itemsCorrupted).toBe(1);
      expect(integrityReport.corruptedItems).toHaveLength(1);
      expect(integrityReport.corruptedItems[0].itemId).toBe('corrupted-item');

      // Analyze corruption patterns
      const patterns = DataIntegrityService.detectCorruptionPatterns(integrityReport.corruptedItems);
      expect(patterns.patterns.length).toBeGreaterThan(0);
      expect(patterns.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Comprehensive Security Report Generation', () => {
    it('should generate complete security assessment', async () => {
      // Initialize key management
      const keyResult = await SecureKeyManagementService.generateSecureMasterKey();
      await SecureKeyManagementService.storeSecureMasterKey(
        keyResult.masterKey,
        keyResult.metadata
      );

      // Test data
      const testData = {
        contacts: [{ recordID: '123', displayName: 'Test User' }],
        messages: [{ id: '456', body: 'Test message' }],
        photos: [{ uri: 'file://test.jpg', filename: 'test.jpg' }]
      };

      // Generate comprehensive security report
      const securityReport = await EncryptionValidationService.generateSecurityReport(testData);

      expect(securityReport.overallSecurity).toBeDefined();
      expect(['secure', 'warning', 'insecure']).toContain(securityReport.overallSecurity);
      
      expect(securityReport.encryptionValidation).toBeDefined();
      expect(securityReport.encryptionValidation.success).toBe(true);
      
      expect(securityReport.transmissionSecurity).toBeDefined();
      expect(securityReport.transmissionSecurity.isSecure).toBe(true);
      
      expect(securityReport.storageEncryption).toBeDefined();
      expect(securityReport.storageEncryption.encrypted).toBe(true);
      
      expect(securityReport.recommendations).toBeDefined();
      expect(securityReport.timestamp).toBeInstanceOf(Date);

      // Validate key management status
      const keyStatus = await SecureKeyManagementService.getKeyManagementStatus();
      expect(keyStatus.hasKey).toBe(true);
      expect(keyStatus.securityLevel).toBe('high');
      expect(keyStatus.needsRotation).toBe(false);
    });

    it('should identify security issues and provide recommendations', async () => {
      // Simulate security issues by mocking failures
      jest.spyOn(EncryptionValidationService, 'validateTransmissionSecurity')
        .mockResolvedValueOnce({
          isSecure: false,
          protocol: 'http',
          securityIssues: ['Insecure HTTP protocol detected']
        });

      const testData = {
        contacts: [{ recordID: '123', displayName: 'Test User' }],
        messages: [],
        photos: []
      };

      const securityReport = await EncryptionValidationService.generateSecurityReport(testData);

      expect(securityReport.overallSecurity).toBe('insecure');
      expect(securityReport.transmissionSecurity.isSecure).toBe(false);
      expect(securityReport.recommendations).toContain('Ensure all data transmission uses HTTPS with TLS 1.2+');
    });
  });

  describe('Performance and Scalability Security Tests', () => {
    it('should maintain security with large datasets', async () => {
      // Generate large dataset
      const largeContactData = Array.from({ length: 100 }, (_, i) => ({
        recordID: `contact_${i}`,
        displayName: `Test User ${i}`,
        phoneNumbers: [{ label: 'mobile', number: `+123456789${i}` }]
      }));

      const testData = {
        contacts: largeContactData,
        messages: [],
        photos: []
      };

      const startTime = Date.now();
      const validationResult = await EncryptionValidationService.validateEncryptionForAllDataTypes(testData);
      const endTime = Date.now();

      expect(validationResult.success).toBe(true);
      expect(validationResult.validationResults.contacts.encrypted).toBe(true);
      expect(validationResult.validationResults.contacts.integrityValid).toBe(true);
      
      // Ensure reasonable performance (should complete within 10 seconds)
      expect(endTime - startTime).toBeLessThan(10000);
    });

    it('should handle concurrent encryption operations securely', async () => {
      const testDataItems = [
        'test data item 1',
        'test data item 2',
        'test data item 3',
        'test data item 4',
        'test data item 5'
      ];

      // Encrypt all items concurrently
      const encryptionPromises = testDataItems.map(data => 
        EncryptionService.encryptData(data)
      );

      const encryptionResults = await Promise.all(encryptionPromises);

      // Verify all encryptions succeeded and are unique
      expect(encryptionResults).toHaveLength(5);
      encryptionResults.forEach((result, index) => {
        expect(result.encryptedData).toBeDefined();
        expect(result.iv).toBeDefined();
        expect(result.salt).toBeDefined();
        
        // Ensure each encryption is unique
        const otherResults = encryptionResults.filter((_, i) => i !== index);
        otherResults.forEach(otherResult => {
          expect(result.encryptedData).not.toBe(otherResult.encryptedData);
          expect(result.iv).not.toBe(otherResult.iv);
          expect(result.salt).not.toBe(otherResult.salt);
        });
      });

      // Decrypt all items concurrently
      const decryptionPromises = encryptionResults.map((encrypted, index) =>
        EncryptionService.decryptData(encrypted.encryptedData, encrypted.iv, encrypted.salt)
      );

      const decryptionResults = await Promise.all(decryptionPromises);

      // Verify all decryptions succeeded and match original data
      decryptionResults.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.data).toBe(testDataItems[index]);
      });
    });
  });

  describe('Error Handling and Recovery Security', () => {
    it('should handle encryption failures securely', async () => {
      // Mock encryption failure
      const originalEncrypt = EncryptionService.encryptData;
      EncryptionService.encryptData = jest.fn().mockRejectedValue(new Error('Encryption service unavailable'));

      try {
        const testData = {
          contacts: [{ recordID: '123', displayName: 'Test' }],
          messages: [],
          photos: []
        };

        const result = await EncryptionValidationService.validateEncryptionForAllDataTypes(testData);

        expect(result.success).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors[0]).toContain('Contact encryption validation failed');
      } finally {
        // Restore original function
        EncryptionService.encryptData = originalEncrypt;
      }
    });

    it('should handle key management failures gracefully', async () => {
      // Mock key retrieval failure
      jest.spyOn(SecureKeyManagementService, 'retrieveSecureMasterKey')
        .mockResolvedValueOnce(null);

      const validation = await SecureKeyManagementService.validateKeySecurity();

      expect(validation.isValid).toBe(false);
      expect(validation.needsRotation).toBe(true);
      expect(validation.securityLevel).toBe('low');
      expect(validation.issues).toContain('No master key found');
    });

    it('should maintain security during partial failures', async () => {
      const sessionId = 'partial-failure-test';
      const validData = 'valid test data';
      const validEncrypted = await EncryptionService.encryptData(validData);

      const backupItems = [
        {
          id: 'valid-item',
          type: 'contact' as const,
          encryptedData: validEncrypted.encryptedData,
          iv: validEncrypted.iv,
          salt: validEncrypted.salt,
          checksum: DataIntegrityService.generateChecksum(validData)
        },
        {
          id: 'invalid-item',
          type: 'message' as const,
          encryptedData: 'invalid_encrypted_data',
          iv: 'invalid_iv',
          salt: 'invalid_salt',
          checksum: 'invalid_checksum'
        }
      ];

      const integrityReport = await DataIntegrityService.validateBackupSessionIntegrity(
        sessionId,
        backupItems
      );

      // Should identify valid items while flagging invalid ones
      expect(integrityReport.itemsValid).toBe(1);
      expect(integrityReport.itemsCorrupted).toBe(1);
      expect(integrityReport.overallIntegrity).toBe(false);
      
      // Valid item should be identified correctly
      expect(integrityReport.corruptedItems[0].itemId).toBe('invalid-item');
    });
  });
});