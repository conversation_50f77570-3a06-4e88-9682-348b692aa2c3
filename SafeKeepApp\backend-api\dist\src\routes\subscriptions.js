"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const SubscriptionController_1 = require("../controllers/SubscriptionController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const subscriptionController = new SubscriptionController_1.SubscriptionController();
router.post('/', auth_1.authenticateToken, (req, res) => {
    subscriptionController.createSubscription(req, res);
});
router.put('/:subscriptionId', auth_1.authenticateToken, (req, res) => {
    subscriptionController.updateSubscription(req, res);
});
router.get('/:userId', auth_1.authenticateToken, (req, res) => {
    subscriptionController.getSubscriptionDetails(req, res);
});
router.delete('/:subscriptionId', auth_1.authenticateToken, (req, res) => {
    subscriptionController.cancelSubscription(req, res);
});
exports.default = router;
//# sourceMappingURL=subscriptions.js.map