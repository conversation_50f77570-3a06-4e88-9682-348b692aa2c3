-- <PERSON>QL script to fix storage usage tracking for file metadata

-- Create a function to update user storage usage
CREATE OR REPLACE FUNCTION update_user_storage_usage()
RETURNS TRIGGER AS $
DECLARE
  old_size BIGINT := 0;
  new_size BIGINT := 0;
  user_id_val UUID;
BEGIN
  -- Determine which user_id to use
  IF TG_OP = 'DELETE' THEN
    user_id_val := OLD.user_id;
    old_size := OLD.size;
  ELSIF TG_OP = 'UPDATE' THEN
    user_id_val := NEW.user_id;
    old_size := OLD.size;
    new_size := NEW.size;
  ELSE -- INSERT
    user_id_val := NEW.user_id;
    new_size := NEW.size;
  END IF;

  -- Update the user's storage usage
  UPDATE users
  SET 
    storage_usage = COALESCE(storage_usage, 0) - old_size + new_size,
    updated_at = NOW()
  WHERE id = user_id_val;

  -- Return the appropriate record
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS update_storage_usage_trigger ON file_metadata;

-- Create trigger to update storage usage on file_metadata changes
CREATE TRIGGER update_storage_usage_trigger
AFTER INSERT OR UPDATE OR DELETE ON file_metadata
FOR EACH ROW
EXECUTE FUNCTION update_user_storage_usage();

-- Create a function to recalculate user storage usage
CREATE OR REPLACE FUNCTION recalculate_user_storage_usage(user_id_param UUID)
RETURNS VOID AS $
DECLARE
  total_usage BIGINT;
BEGIN
  -- Calculate total storage usage for the user
  SELECT COALESCE(SUM(size), 0)
  INTO total_usage
  FROM file_metadata
  WHERE user_id = user_id_param;
  
  -- Update the user's storage usage
  UPDATE users
  SET 
    storage_usage = total_usage,
    updated_at = NOW()
  WHERE id = user_id_param;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION recalculate_user_storage_usage(UUID) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION update_user_storage_usage() IS 'Updates user storage usage when file metadata changes';
COMMENT ON FUNCTION recalculate_user_storage_usage(UUID) IS 'Recalculates total storage usage for a user';