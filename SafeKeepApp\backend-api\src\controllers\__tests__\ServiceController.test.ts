import { Request, Response } from 'express';
import { ServiceController } from '../ServiceController';
import { ServiceValidator } from '../../services/ServiceValidator';

// Mock the ServiceValidator
jest.mock('../../services/ServiceValidator');

describe('ServiceController', () => {
  let serviceController: ServiceController;
  let mockServiceValidator: jest.Mocked<ServiceValidator>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock response
    mockResponse = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    };

    // Create controller instance
    serviceController = new ServiceController();
    
    // Get the mocked ServiceValidator instance
    mockServiceValidator = (serviceController as any).serviceValidator;
  });

  describe('getUserServices', () => {
    it('should get user services successfully', async () => {
      mockRequest = {
        params: { userId: 'user-123' }
      };

      const mockUserServices = [
        {
          serviceId: 'contacts',
          serviceName: 'Contacts Backup',
          isActive: true,
          activatedAt: new Date(),
          expiresAt: new Date()
        },
        {
          serviceId: 'messages',
          serviceName: 'Messages Backup',
          isActive: true,
          activatedAt: new Date()
        }
      ];

      mockServiceValidator.getUserServices.mockResolvedValue(mockUserServices);

      await serviceController.getUserServices(mockRequest as Request, mockResponse as Response);

      expect(mockServiceValidator.getUserServices).toHaveBeenCalledWith('user-123');
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockUserServices
      });
    });

    it('should return 400 error when userId is missing', async () => {
      mockRequest = { params: {} };

      await serviceController.getUserServices(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when userId is empty string', async () => {
      mockRequest = {
        params: { userId: '   ' }
      };

      await serviceController.getUserServices(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle errors when getting user services', async () => {
      mockRequest = {
        params: { userId: 'user-123' }
      };

      const error = new Error('Database connection failed');
      mockServiceValidator.getUserServices.mockRejectedValue(error);

      await serviceController.getUserServices(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'USER_SERVICES_ERROR',
          message: 'Failed to retrieve user services',
          details: { message: 'Database connection failed' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should trim whitespace from userId', async () => {
      mockRequest = {
        params: { userId: '  user-123  ' }
      };

      const mockUserServices: any[] = [];
      mockServiceValidator.getUserServices.mockResolvedValue(mockUserServices);

      await serviceController.getUserServices(mockRequest as Request, mockResponse as Response);

      expect(mockServiceValidator.getUserServices).toHaveBeenCalledWith('user-123');
    });
  });

  describe('validateServiceCombination', () => {
    it('should validate service combination successfully', async () => {
      mockRequest = {
        body: {
          serviceIds: ['contacts', 'messages']
        }
      };

      const mockValidationResult = {
        isValid: true,
        errors: [],
        warnings: []
      };

      mockServiceValidator.validateServiceCombination.mockResolvedValue(mockValidationResult);

      await serviceController.validateServiceCombination(mockRequest as Request, mockResponse as Response);

      expect(mockServiceValidator.validateServiceCombination).toHaveBeenCalledWith(['contacts', 'messages']);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockValidationResult
      });
    });

    it('should return validation errors when service combination is invalid', async () => {
      mockRequest = {
        body: {
          serviceIds: ['invalid-service']
        }
      };

      const mockValidationResult = {
        isValid: false,
        errors: ['Service "invalid-service" does not exist'],
        warnings: []
      };

      mockServiceValidator.validateServiceCombination.mockResolvedValue(mockValidationResult);

      await serviceController.validateServiceCombination(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockValidationResult
      });
    });

    it('should return 400 error when serviceIds is missing', async () => {
      mockRequest = { body: {} };

      await serviceController.validateServiceCombination(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds is empty array', async () => {
      mockRequest = {
        body: {
          serviceIds: []
        }
      };

      await serviceController.validateServiceCombination(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds contains invalid values', async () => {
      mockRequest = {
        body: {
          serviceIds: ['contacts', '', 'messages']
        }
      };

      await serviceController.validateServiceCombination(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'All service IDs must be non-empty strings',
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle errors when validating service combination', async () => {
      mockRequest = {
        body: {
          serviceIds: ['contacts', 'messages']
        }
      };

      const error = new Error('Database error');
      mockServiceValidator.validateServiceCombination.mockRejectedValue(error);

      await serviceController.validateServiceCombination(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Failed to validate service combination',
          details: { message: 'Database error' },
          timestamp: expect.any(String)
        }
      });
    });
  });

  describe('checkServiceAccess', () => {
    it('should check service access successfully', async () => {
      mockRequest = {
        params: {
          userId: 'user-123',
          serviceType: 'contacts'
        }
      };

      const mockAccessResult = {
        hasAccess: true,
        expiresAt: new Date(),
        planName: 'Standard Plan'
      };

      mockServiceValidator.checkServiceAccess.mockResolvedValue(mockAccessResult);

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockServiceValidator.checkServiceAccess).toHaveBeenCalledWith('user-123', 'contacts');
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockAccessResult
      });
    });

    it('should return no access when user does not have access', async () => {
      mockRequest = {
        params: {
          userId: 'user-123',
          serviceType: 'photos'
        }
      };

      const mockAccessResult = {
        hasAccess: false
      };

      mockServiceValidator.checkServiceAccess.mockResolvedValue(mockAccessResult);

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockAccessResult
      });
    });

    it('should return 400 error when userId is missing', async () => {
      mockRequest = {
        params: {
          serviceType: 'contacts'
        }
      };

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceType is missing', async () => {
      mockRequest = {
        params: {
          userId: 'user-123'
        }
      };

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid service type is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when userId is empty string', async () => {
      mockRequest = {
        params: {
          userId: '   ',
          serviceType: 'contacts'
        }
      };

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceType is empty string', async () => {
      mockRequest = {
        params: {
          userId: 'user-123',
          serviceType: '   '
        }
      };

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid service type is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle errors when checking service access', async () => {
      mockRequest = {
        params: {
          userId: 'user-123',
          serviceType: 'contacts'
        }
      };

      const error = new Error('Database connection failed');
      mockServiceValidator.checkServiceAccess.mockRejectedValue(error);

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'ACCESS_CHECK_ERROR',
          message: 'Failed to check service access',
          details: { message: 'Database connection failed' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should trim whitespace from userId and serviceType', async () => {
      mockRequest = {
        params: {
          userId: '  user-123  ',
          serviceType: '  contacts  '
        }
      };

      const mockAccessResult = {
        hasAccess: true
      };

      mockServiceValidator.checkServiceAccess.mockResolvedValue(mockAccessResult);

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockServiceValidator.checkServiceAccess).toHaveBeenCalledWith('user-123', 'contacts');
    });

    it('should handle specific input validation errors', async () => {
      mockRequest = {
        params: {
          userId: 'user-123',
          serviceType: 'contacts'
        }
      };

      const error = new Error('User ID is required');
      mockServiceValidator.checkServiceAccess.mockRejectedValue(error);

      await serviceController.checkServiceAccess(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'User ID is required',
          details: { message: 'User ID is required' },
          timestamp: expect.any(String)
        }
      });
    });
  });
});