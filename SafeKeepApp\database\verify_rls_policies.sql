-- SQL script to verify RLS policies for file_metadata table

-- Function to verify RLS policies
CREATE OR REPLACE FUNCTION verify_rls_policies()
RETURNS TABLE (
  table_name TEXT,
  policy_name TEXT,
  cmd TEXT,
  policy_exists BOOLEAN,
  policy_using TEXT,
  policy_with_check TEXT
) AS $
BEGIN
  RETURN QUERY
  SELECT
    p.tablename::TEXT,
    p.policyname::TEXT,
    p.cmd::TEXT,
    TRUE AS policy_exists,
    p.qual::TEXT AS policy_using,
    p.with_check::TEXT AS policy_with_check
  FROM
    pg_policies p
  WHERE
    p.tablename = 'file_metadata'
  ORDER BY
    p.tablename, p.policyname;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION verify_rls_policies() TO authenticated;

-- Function to test RLS policies
CREATE OR REPLACE FUNCTION test_rls_policies(test_user_id UUID)
RETURNS JSONB AS $
DECLARE
  result JSONB;
  current_user_id UUID;
  can_read <PERSON><PERSON><PERSON><PERSON><PERSON>;
  can_insert <PERSON>OOLEAN;
  can_update <PERSON>O<PERSON>EA<PERSON>;
  can_delete B<PERSON>OLEAN;
BEGIN
  -- Get current user ID
  SELECT auth.uid() INTO current_user_id;
  
  -- Test read access
  BEGIN
    PERFORM id FROM file_metadata WHERE user_id = current_user_id LIMIT 1;
    can_read := TRUE;
  EXCEPTION
    WHEN OTHERS THEN
      can_read := FALSE;
  END;
  
  -- Test insert access
  BEGIN
    -- This is a test that will be rolled back
    SAVEPOINT test_insert;
    
    INSERT INTO file_metadata (
      user_id, original_name, encrypted_name, mime_type, 
      size, encrypted_size, category, hash, 
      encryption_iv, encryption_salt, storage_path
    ) VALUES (
      current_user_id, 'test.txt', 'test.enc', 'text/plain',
      100, 150, 'photo', 'test-hash',
      'test-iv', 'test-salt', current_user_id || '/test.txt'
    );
    
    can_insert := TRUE;
    ROLLBACK TO SAVEPOINT test_insert;
  EXCEPTION
    WHEN OTHERS THEN
      can_insert := FALSE;
      ROLLBACK TO SAVEPOINT test_insert;
  END;
  
  -- Test update access
  BEGIN
    -- This is a test that will be rolled back
    SAVEPOINT test_update;
    
    UPDATE file_metadata 
    SET original_name = 'updated.txt'
    WHERE user_id = current_user_id
    LIMIT 1;
    
    can_update := TRUE;
    ROLLBACK TO SAVEPOINT test_update;
  EXCEPTION
    WHEN OTHERS THEN
      can_update := FALSE;
      ROLLBACK TO SAVEPOINT test_update;
  END;
  
  -- Test delete access
  BEGIN
    -- This is a test that will be rolled back
    SAVEPOINT test_delete;
    
    DELETE FROM file_metadata 
    WHERE user_id = current_user_id
    LIMIT 1;
    
    can_delete := TRUE;
    ROLLBACK TO SAVEPOINT test_delete;
  EXCEPTION
    WHEN OTHERS THEN
      can_delete := FALSE;
      ROLLBACK TO SAVEPOINT test_delete;
  END;
  
  -- Build result
  result := jsonb_build_object(
    'user_id', current_user_id,
    'can_read', can_read,
    'can_insert', can_insert,
    'can_update', can_update,
    'can_delete', can_delete
  );
  
  RETURN result;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION test_rls_policies(UUID) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION verify_rls_policies() IS 'Verifies RLS policies for file_metadata table';
COMMENT ON FUNCTION test_rls_policies(UUID) IS 'Tests RLS policies for file_metadata table';