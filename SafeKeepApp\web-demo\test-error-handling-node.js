/**
 * Node.js Test Runner for Error Handling System
 * Tests the core logic without browser-specific APIs
 */

// Mock browser APIs for Node.js testing
global.window = {
    addEventListener: () => {},
    fetch: () => Promise.resolve({ ok: true, json: () => Promise.resolve({}) }),
    localStorage: {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    },
    sessionStorage: {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    }
};

global.document = {
    readyState: 'complete',
    addEventListener: () => {},
    createElement: () => ({
        className: '',
        innerHTML: '',
        style: {},
        appendChild: () => {},
        remove: () => {}
    }),
    querySelector: () => null,
    querySelectorAll: () => [],
    body: {
        appendChild: () => {}
    },
    head: {
        appendChild: () => {}
    }
};

global.navigator = {
    onLine: true
};

global.console = console;

// Load the error handling classes
const fs = require('fs');
const path = require('path');

// Read and evaluate the error handler
const errorHandlerCode = fs.readFileSync(path.join(__dirname, 'error-handler.js'), 'utf8');
eval(errorHandlerCode);

const networkQueueCode = fs.readFileSync(path.join(__dirname, 'network-queue-manager.js'), 'utf8');
eval(networkQueueCode);

const offlineManagerCode = fs.readFileSync(path.join(__dirname, 'offline-manager.js'), 'utf8');
eval(offlineManagerCode);

const demoStateManagerCode = fs.readFileSync(path.join(__dirname, 'demo-state-manager.js'), 'utf8');
eval(demoStateManagerCode);

// Simple test framework
class SimpleTestFramework {
    constructor() {
        this.tests = [];
        this.results = [];
    }

    test(name, testFn) {
        this.tests.push({ name, testFn });
    }

    async run() {
        console.log('🧪 Running Error Handling Tests (Node.js)...\n');
        
        for (const { name, testFn } of this.tests) {
            try {
                await testFn();
                this.results.push({ name, status: 'PASS', error: null });
                console.log(`✅ ${name}`);
            } catch (error) {
                this.results.push({ name, status: 'FAIL', error: error.message });
                console.log(`❌ ${name}: ${error.message}`);
            }
        }
        
        this.printSummary();
    }

    printSummary() {
        const passed = this.results.filter(r => r.status === 'PASS').length;
        const failed = this.results.filter(r => r.status === 'FAIL').length;
        
        console.log('\n📊 Test Summary:');
        console.log(`Total: ${this.results.length}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`);
        
        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.results
                .filter(r => r.status === 'FAIL')
                .forEach(r => console.log(`  - ${r.name}: ${r.error}`));
        }
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message);
        }
    }
}

// Create test framework instance
const test = new SimpleTestFramework();

// Test SafeKeepErrorHandler
test.test('SafeKeepErrorHandler initialization', () => {
    const errorHandler = new SafeKeepErrorHandler();
    test.assert(errorHandler instanceof SafeKeepErrorHandler, 'Should create instance');
    test.assert(typeof errorHandler.handleError === 'function', 'Should have handleError method');
    test.assert(errorHandler.errorCategories.AUTHENTICATION, 'Should have error categories');
});

test.test('Error categorization', () => {
    const errorHandler = new SafeKeepErrorHandler();
    const categories = errorHandler.errorCategories;
    
    test.assert(categories.AUTHENTICATION, 'Should have AUTHENTICATION category');
    test.assert(categories.NETWORK, 'Should have NETWORK category');
    test.assert(categories.ENCRYPTION, 'Should have ENCRYPTION category');
    test.assert(categories.BACKUP, 'Should have BACKUP category');
    test.assert(categories.RESTORE, 'Should have RESTORE category');
});

test.test('Error logging', async () => {
    const errorHandler = new SafeKeepErrorHandler();
    const testError = new Error('Test error');
    
    await errorHandler.handleError(testError, 'DEMO_STATE', {
        operation: 'test',
        context: 'node_test'
    });
    
    const errorLog = errorHandler.getErrorLog();
    test.assert(errorLog.length > 0, 'Should log errors');
    test.assert(errorLog[0].category === 'DEMO_STATE', 'Should categorize correctly');
});

// Test NetworkQueueManager
test.test('NetworkQueueManager initialization', () => {
    const networkQueue = new NetworkQueueManager();
    test.assert(networkQueue instanceof NetworkQueueManager, 'Should create instance');
    test.assert(typeof networkQueue.enqueue === 'function', 'Should have enqueue method');
    test.assert(typeof networkQueue.getQueueStatus === 'function', 'Should have getQueueStatus method');
});

test.test('Network queue operations', () => {
    const networkQueue = new NetworkQueueManager();
    
    const queueId = networkQueue.queueApiCall('/api/test', { method: 'GET' });
    test.assert(queueId, 'Should return queue ID');
    
    const status = networkQueue.getQueueStatus();
    test.assert(status.queueLength >= 0, 'Should have queue length');
    test.assert(typeof status.isOnline === 'boolean', 'Should have online status');
});

// Test OfflineManager
test.test('OfflineManager initialization', () => {
    const offlineManager = new OfflineManager();
    test.assert(offlineManager instanceof OfflineManager, 'Should create instance');
    test.assert(typeof offlineManager.isFeatureAvailable === 'function', 'Should have isFeatureAvailable method');
    test.assert(typeof offlineManager.cacheData === 'function', 'Should have cacheData method');
});

test.test('Offline caching', () => {
    const offlineManager = new OfflineManager();
    
    offlineManager.cacheData('test_key', { test: 'data' });
    const cachedData = offlineManager.getCachedData('test_key');
    
    test.assert(cachedData, 'Should cache data');
    test.assert(cachedData.test === 'data', 'Should retrieve correct data');
});

// Test DemoStateManager
test.test('DemoStateManager initialization', () => {
    const demoManager = new DemoStateManager();
    test.assert(demoManager instanceof DemoStateManager, 'Should create instance');
    test.assert(typeof demoManager.updateState === 'function', 'Should have updateState method');
    test.assert(typeof demoManager.getState === 'function', 'Should have getState method');
});

test.test('Demo state management', () => {
    const demoManager = new DemoStateManager();
    
    demoManager.updateState('test.value', 'test_data');
    const retrievedValue = demoManager.getState('test.value');
    
    test.assert(retrievedValue === 'test_data', 'Should update and retrieve state');
});

test.test('Demo state features', () => {
    const demoManager = new DemoStateManager();
    
    demoManager.setDemoStep(5);
    const currentStep = demoManager.getState('demo.currentStep');
    test.assert(currentStep === 5, 'Should set demo step');
    
    demoManager.addFeatureExplored('backup');
    const explored = demoManager.getState('demo.featuresExplored');
    test.assert(explored.includes('backup'), 'Should track feature exploration');
});

// Test error recovery strategies
test.test('Recovery strategies', () => {
    const errorHandler = new SafeKeepErrorHandler();
    const strategies = errorHandler.recoveryStrategies;
    
    test.assert(strategies.has('AUTHENTICATION'), 'Should have auth recovery');
    test.assert(strategies.has('NETWORK'), 'Should have network recovery');
    test.assert(strategies.has('BACKUP'), 'Should have backup recovery');
    
    const authStrategies = strategies.get('AUTHENTICATION');
    test.assert(typeof authStrategies.immediate === 'function', 'Should have immediate recovery');
    test.assert(typeof authStrategies.graceful === 'function', 'Should have graceful recovery');
    test.assert(typeof authStrategies.reset === 'function', 'Should have reset recovery');
});

// Test retry logic configuration
test.test('Retry configuration', () => {
    const errorHandler = new SafeKeepErrorHandler();
    
    test.assert(typeof errorHandler.maxRetries === 'number', 'Should have max retries');
    test.assert(typeof errorHandler.baseDelay === 'number', 'Should have base delay');
    test.assert(typeof errorHandler.maxDelay === 'number', 'Should have max delay');
    test.assert(errorHandler.maxRetries > 0, 'Max retries should be positive');
    test.assert(errorHandler.baseDelay > 0, 'Base delay should be positive');
});

// Test utility methods
test.test('Utility methods', () => {
    const errorHandler = new SafeKeepErrorHandler();
    
    const errorId = errorHandler.generateErrorId();
    test.assert(typeof errorId === 'string', 'Should generate error ID');
    test.assert(errorId.startsWith('err_'), 'Error ID should have prefix');
    
    const testError = new Error('Test error');
    const serialized = errorHandler.serializeError(testError);
    test.assert(serialized.name === 'Error', 'Should serialize error name');
    test.assert(serialized.message === 'Test error', 'Should serialize error message');
});

// Run all tests
test.run().then(() => {
    console.log('\n🎉 Node.js tests completed!');
    process.exit(0);
}).catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
});