import { Request, Response } from 'express';
import { ApiResponse, ServiceCombination, PricingResult, PlanRecommendation } from '../types/modular-pricing';
import { PricingEngine } from '../services/PricingEngine';

export class PricingController {
  private pricingEngine: PricingEngine;

  constructor() {
    this.pricingEngine = new PricingEngine();
  }
  /**
   * GET /api/pricing/combinations
   * Returns available service combinations with pricing
   */
  async getServiceCombinations(req: Request, res: Response): Promise<void> {
    try {
      const combinations = await this.pricingEngine.getAvailableServiceCombinations();
      
      const response: ApiResponse<ServiceCombination[]> = {
        success: true,
        data: combinations
      };
      
      res.json(response);
    } catch (error) {
      console.error('PricingController.getServiceCombinations error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'PRICING_ERROR',
          message: 'Failed to retrieve service combinations',
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * POST /api/pricing/calculate
   * Body: { serviceIds: string[] }
   * Returns optimal pricing for selected services
   */
  async calculateOptimalPrice(req: Request, res: Response): Promise<void> {
    try {
      const { serviceIds } = req.body;
      
      // Input validation
      if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Service IDs array is required and cannot be empty',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Validate service IDs are strings
      if (!serviceIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'All service IDs must be non-empty strings',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const pricingResult = await this.pricingEngine.calculateOptimalPrice(serviceIds);
      
      const response: ApiResponse<PricingResult> = {
        success: true,
        data: pricingResult
      };
      
      res.json(response);
    } catch (error) {
      console.error('PricingController.calculateOptimalPrice error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'PRICING_CALCULATION_ERROR',
          message: 'Failed to calculate optimal pricing',
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/pricing/recommendations/:userId
   * Returns upgrade/downgrade recommendations for user
   */
  async getPlanRecommendations(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      
      // Input validation
      if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid User ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const recommendations = await this.pricingEngine.getPlanRecommendations(userId.trim());
      
      const response: ApiResponse<PlanRecommendation[]> = {
        success: true,
        data: recommendations
      };
      
      res.json(response);
    } catch (error) {
      console.error('PricingController.getPlanRecommendations error:', error);
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'RECOMMENDATION_ERROR',
          message: 'Failed to retrieve plan recommendations',
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }
}