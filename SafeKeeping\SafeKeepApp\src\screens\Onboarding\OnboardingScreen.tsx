import React from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button, Card, Text } from 'react-native-paper';
import { Header, ListItem } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { COLORS, FONTS, SPACING } from '../../utils/constants';

type OnboardingNavigationProp = StackNavigationProp<RootStackParamList, 'Onboarding'>;

const OnboardingScreen = () => {
  const navigation = useNavigation<OnboardingNavigationProp>();

  const handleGetStarted = async () => {
    try {
      // Mark onboarding as complete
      await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
      navigation.navigate('PermissionSetup');
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      navigation.navigate('PermissionSetup');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'SafeKeep',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <View style={styles.content}>
        <Card style={styles.welcomeCard}>
          <Card.Content>
            <Text variant="headlineMedium" style={styles.title}>
              Welcome to SafeKeep
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              Your personal data backup solution
            </Text>
            <Text variant="bodyMedium" style={styles.description}>
              Automatically backup your photos, contacts, and messages with
              military-grade encryption. Simple, secure, and reliable.
            </Text>
          </Card.Content>
        </Card>

        <Card style={styles.featuresCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.featuresTitle}>
              Key Features
            </Text>

            <ListItem containerStyle={styles.featureItem}>
              <Icon name="camera" size={24} color={COLORS.primary} />
              <ListItem.Content>
                <ListItem.Title style={styles.featureText}>
                  Automatic photo backup
                </ListItem.Title>
              </ListItem.Content>
            </ListItem>

            <ListItem containerStyle={styles.featureItem}>
              <Icon name="contacts" size={24} color={COLORS.primary} />
              <ListItem.Content>
                <ListItem.Title style={styles.featureText}>
                  Automatic contact synchronization
                </ListItem.Title>
              </ListItem.Content>
            </ListItem>

            <ListItem containerStyle={styles.featureItem}>
              <Icon name="message-text" size={24} color={COLORS.primary} />
              <ListItem.Content>
                <ListItem.Title style={styles.featureText}>
                  Automatic text message backup
                </ListItem.Title>
              </ListItem.Content>
            </ListItem>

            <ListItem containerStyle={styles.featureItem}>
              <Icon name="shield-lock" size={24} color={COLORS.primary} />
              <ListItem.Content>
                <ListItem.Title style={styles.featureText}>
                  End-to-end encryption
                </ListItem.Title>
              </ListItem.Content>
            </ListItem>

            <ListItem containerStyle={styles.featureItem}>
              <Icon name="cloud-upload" size={24} color={COLORS.primary} />
              <ListItem.Content>
                <ListItem.Title style={styles.featureText}>
                  Secure cloud storage
                </ListItem.Title>
              </ListItem.Content>
            </ListItem>
          </Card.Content>
        </Card>
      </View>

      <Button
        mode="contained"
        onPress={handleGetStarted}
        style={styles.button}
        contentStyle={styles.buttonContent}
        labelStyle={styles.buttonText}
        icon={() => <Icon name="arrow-right" size={20} color="#fff" />}
      >
        Get Started
      </Button>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
    justifyContent: 'space-between',
  },
  welcomeCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  title: {
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  description: {
    color: COLORS.text,
    textAlign: 'center',
    lineHeight: 22,
  },
  featuresCard: {
    elevation: 4,
  },
  featuresTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
    fontWeight: 'bold',
  },
  featureItem: {
    backgroundColor: 'transparent',
    paddingVertical: SPACING.xs,
    paddingHorizontal: 0,
  },
  featureText: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.text,
  },
  button: {
    margin: SPACING.lg,
    backgroundColor: COLORS.primary,
  },
  buttonContent: {
    paddingVertical: SPACING.sm,
  },
  buttonText: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
  },
});

export default OnboardingScreen;
