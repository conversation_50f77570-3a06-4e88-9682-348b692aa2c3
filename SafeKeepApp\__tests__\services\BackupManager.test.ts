import BackupManager from '../../src/services/BackupManager';
import ContactBackupService from '../../src/services/ContactBackupService';
import MessageBackupService from '../../src/services/MessageBackupService';
import PhotoBackupService from '../../src/services/PhotoBackupService';
import AuthService from '../../src/services/AuthService';
import PermissionService from '../../src/services/PermissionService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupConfiguration, BackupServiceResult } from '../../src/types/backup';

// Mock all dependencies
jest.mock('../../src/services/ContactBackupService');
jest.mock('../../src/services/MessageBackupService');
jest.mock('../../src/services/PhotoBackupService');
jest.mock('../../src/services/AuthService');
jest.mock('../../src/services/PermissionService');
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));
jest.mock('../../src/utils/helpers', () => ({
  isWiFiConnected: jest.fn().mockResolvedValue(true),
  hasSufficientBattery: jest.fn().mockResolvedValue(true),
  generateId: jest.fn().mockReturnValue('test-session-id'),
  delay: jest.fn().mockResolvedValue(undefined)
}));
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android'
  }
}));

const mockContactBackupService = ContactBackupService as jest.Mocked<typeof ContactBackupService>;
const mockMessageBackupService = MessageBackupService as jest.Mocked<typeof MessageBackupService>;
const mockPhotoBackupService = PhotoBackupService as jest.Mocked<typeof PhotoBackupService>;
const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;
const mockPermissionService = PermissionService as jest.Mocked<typeof PermissionService>;
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('BackupManager', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    aud: 'authenticated',
    role: 'authenticated',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    app_metadata: {},
    user_metadata: {}
  };

  const defaultConfiguration: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockAuthService.getCurrentUser.mockReturnValue(mockUser);
    mockPermissionService.checkAllPermissions.mockResolvedValue({
      contacts: { granted: true, denied: false, blocked: false, unavailable: false },
      sms: { granted: true, denied: false, blocked: false, unavailable: false },
      photos: { granted: true, denied: false, blocked: false, unavailable: false }
    });
    
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await expect(BackupManager.initialize()).resolves.not.toThrow();
    });

    it('should restore previous state if exists', async () => {
      const mockState = {
        lastBackupTime: '2023-01-01T00:00:00Z',
        backupConfiguration: defaultConfiguration,
        pendingSessions: [],
        failedItems: [],
        backupStatistics: {
          totalBackups: 1,
          totalItems: 100,
          lastSuccessfulBackup: '2023-01-01T00:00:00Z'
        }
      };

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(mockState));

      await BackupManager.initialize();

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('backup_manager_state');
    });
  });

  describe('startBackup', () => {
    it('should prevent multiple concurrent backups', async () => {
      // Start first backup
      const firstBackupPromise = BackupManager.startBackup(defaultConfiguration);
      
      // Try to start second backup immediately
      const secondBackupResult = await BackupManager.startBackup(defaultConfiguration);

      expect(secondBackupResult.success).toBe(false);
      expect(secondBackupResult.errors[0].message).toBe('Backup is already running');

      // Wait for first backup to complete
      await firstBackupPromise;
    });

    it('should require user authentication', async () => {
      mockAuthService.getCurrentUser.mockReturnValue(null);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toBe('User authentication required for backup');
    });

    it('should perform pre-flight checks', async () => {
      // Mock permission failure
      mockPermissionService.checkAllPermissions.mockResolvedValue({
        contacts: { granted: false, denied: true, blocked: false, unavailable: false },
        sms: { granted: true, denied: false, blocked: false, unavailable: false },
        photos: { granted: true, denied: false, blocked: false, unavailable: false }
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.type === 'permission')).toBe(true);
    });

    it('should backup contacts when enabled', async () => {
      const mockContacts = [
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ];
      
      mockContactBackupService.scanContacts.mockResolvedValue(mockContacts as any);
      mockContactBackupService.backupContacts.mockResolvedValue({
        success: true,
        itemsProcessed: 1,
        errors: []
      });

      const configuration = { ...defaultConfiguration, includeMessages: false, includePhotos: false };
      const result = await BackupManager.startBackup(configuration);

      expect(mockContactBackupService.scanContacts).toHaveBeenCalled();
      expect(mockContactBackupService.backupContacts).toHaveBeenCalledWith(
        mockContacts,
        expect.any(Function)
      );
      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(1);
    });

    it('should backup messages when enabled on Android', async () => {
      const mockMessages = [
        { id: '1', body: 'Test message', address: '+1234567890', date: Date.now() }
      ];
      
      mockMessageBackupService.scanMessages.mockResolvedValue(mockMessages as any);
      mockMessageBackupService.backupMessages.mockResolvedValue({
        success: true,
        itemsProcessed: 1,
        errors: []
      });

      const configuration = { ...defaultConfiguration, includeContacts: false, includePhotos: false };
      const result = await BackupManager.startBackup(configuration);

      expect(mockMessageBackupService.scanMessages).toHaveBeenCalled();
      expect(mockMessageBackupService.backupMessages).toHaveBeenCalledWith(
        mockMessages,
        expect.any(Function)
      );
      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(1);
    });

    it('should backup photos when enabled', async () => {
      const mockMediaResult = {
        photos: [{ uri: 'photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now() }],
        excludedVideos: [],
        totalScanned: 1
      };
      
      const mockPhotoAssets = [
        { uri: 'photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now(), width: 100, height: 100 }
      ];

      mockPhotoBackupService.scanPhotoLibrary.mockResolvedValue(mockMediaResult as any);
      mockPhotoBackupService.backupPhotos.mockResolvedValue({
        success: true,
        itemsProcessed: 1,
        errors: []
      });

      const configuration = { ...defaultConfiguration, includeContacts: false, includeMessages: false };
      const result = await BackupManager.startBackup(configuration);

      expect(mockPhotoBackupService.scanPhotoLibrary).toHaveBeenCalledWith(1000);
      expect(mockPhotoBackupService.backupPhotos).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            uri: 'photo1.jpg',
            filename: 'photo1.jpg',
            type: 'image/jpeg'
          })
        ]),
        expect.any(Function)
      );
      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(1);
    });

    it('should handle backup failures gracefully', async () => {
      mockContactBackupService.scanContacts.mockRejectedValue(new Error('Scan failed'));

      const configuration = { ...defaultConfiguration, includeMessages: false, includePhotos: false };
      const result = await BackupManager.startBackup(configuration);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should call progress callback during backup', async () => {
      const mockContacts = [{ recordID: '1', displayName: 'John Doe' }];
      mockContactBackupService.scanContacts.mockResolvedValue(mockContacts as any);
      mockContactBackupService.backupContacts.mockImplementation(async (contacts, onProgress) => {
        if (onProgress) {
          onProgress({
            totalContacts: 1,
            processedContacts: 1,
            currentContact: 'John Doe',
            percentage: 100,
            status: 'completed'
          });
        }
        return { success: true, itemsProcessed: 1, errors: [] };
      });

      const progressCallback = jest.fn();
      const configuration = { ...defaultConfiguration, includeMessages: false, includePhotos: false };
      
      await BackupManager.startBackup(configuration, progressCallback);

      expect(progressCallback).toHaveBeenCalled();
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentDataType: 'contacts',
          overallProgress: expect.any(Number)
        })
      );
    });
  });

  describe('network condition checks', () => {
    it('should prevent backup when WiFi-only is enabled but not on WiFi', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('WiFi-only'))).toBe(true);
    });

    it('should allow backup on cellular when WiFi-only is disabled', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      const configuration = { ...defaultConfiguration, wifiOnly: false };
      
      // Mock successful backup services
      mockContactBackupService.scanContacts.mockResolvedValue([]);
      mockContactBackupService.backupContacts.mockResolvedValue({ success: true, itemsProcessed: 0, errors: [] });
      mockMessageBackupService.scanMessages.mockResolvedValue([]);
      mockMessageBackupService.backupMessages.mockResolvedValue({ success: true, itemsProcessed: 0, errors: [] });
      mockPhotoBackupService.scanPhotoLibrary.mockResolvedValue({ photos: [], excludedVideos: [], totalScanned: 0 });
      mockPhotoBackupService.backupPhotos.mockResolvedValue({ success: true, itemsProcessed: 0, errors: [] });

      const result = await BackupManager.startBackup(configuration);

      expect(result.success).toBe(true);
    });
  });

  describe('battery level checks', () => {
    it('should prevent backup when battery is too low', async () => {
      const { hasSufficientBattery } = require('../../src/utils/helpers');
      hasSufficientBattery.mockResolvedValue(false);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('Battery level too low'))).toBe(true);
    });
  });

  describe('pause and resume functionality', () => {
    it('should pause backup', async () => {
      // Start a backup
      const backupPromise = BackupManager.startBackup(defaultConfiguration);
      
      // Pause it
      await BackupManager.pauseBackup();

      expect(mockContactBackupService.pauseBackup).toHaveBeenCalled();
      expect(mockMessageBackupService.pauseBackup).toHaveBeenCalled();
      expect(mockPhotoBackupService.pauseBackup).toHaveBeenCalled();
      expect(BackupManager.isPaused()).toBe(true);

      // Wait for backup to complete
      await backupPromise;
    });

    it('should resume backup', async () => {
      // Start and pause backup
      const backupPromise = BackupManager.startBackup(defaultConfiguration);
      await BackupManager.pauseBackup();
      
      // Resume it
      await BackupManager.resumeBackup();

      expect(mockContactBackupService.resumeBackup).toHaveBeenCalled();
      expect(mockMessageBackupService.resumeBackup).toHaveBeenCalled();
      expect(mockPhotoBackupService.resumeBackup).toHaveBeenCalled();
      expect(BackupManager.isPaused()).toBe(false);

      // Wait for backup to complete
      await backupPromise;
    });

    it('should cancel backup', async () => {
      // Start backup
      const backupPromise = BackupManager.startBackup(defaultConfiguration);
      
      // Cancel it
      await BackupManager.cancelBackup();

      expect(BackupManager.isRunning()).toBe(false);

      // Wait for backup to complete
      await backupPromise;
    });
  });

  describe('session management', () => {
    it('should create and track backup session', async () => {
      mockContactBackupService.scanContacts.mockResolvedValue([]);
      mockContactBackupService.backupContacts.mockResolvedValue({ success: true, itemsProcessed: 0, errors: [] });

      const configuration = { ...defaultConfiguration, includeMessages: false, includePhotos: false };
      await BackupManager.startBackup(configuration);

      const session = BackupManager.getCurrentSession();
      expect(session).toBeTruthy();
      expect(session?.userId).toBe(mockUser.id);
      expect(session?.configuration).toEqual(configuration);
    });

    it('should save session state to AsyncStorage', async () => {
      mockContactBackupService.scanContacts.mockResolvedValue([]);
      mockContactBackupService.backupContacts.mockResolvedValue({ success: true, itemsProcessed: 0, errors: [] });

      const configuration = { ...defaultConfiguration, includeMessages: false, includePhotos: false };
      await BackupManager.startBackup(configuration);

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'backup_manager_state',
        expect.any(String)
      );
    });
  });

  describe('statistics and reporting', () => {
    it('should return backup statistics', async () => {
      const mockState = {
        lastBackupTime: '2023-01-01T00:00:00Z',
        backupConfiguration: defaultConfiguration,
        pendingSessions: [],
        failedItems: [],
        backupStatistics: {
          totalBackups: 5,
          totalItems: 500,
          lastSuccessfulBackup: '2023-01-01T00:00:00Z'
        }
      };

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(mockState));

      const stats = await BackupManager.getBackupStatistics();

      expect(stats.totalSessions).toBe(5);
      expect(stats.totalItemsBackedUp).toBe(500);
      expect(stats.lastBackupDate).toEqual(new Date('2023-01-01T00:00:00Z'));
    });

    it('should return default statistics when no state exists', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const stats = await BackupManager.getBackupStatistics();

      expect(stats.totalSessions).toBe(0);
      expect(stats.totalItemsBackedUp).toBe(0);
      expect(stats.successRate).toBe(0);
    });
  });

  describe('error handling and recovery', () => {
    it('should handle service failures gracefully', async () => {
      mockContactBackupService.scanContacts.mockRejectedValue(new Error('Service unavailable'));

      const configuration = { ...defaultConfiguration, includeMessages: false, includePhotos: false };
      const result = await BackupManager.startBackup(configuration);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0].retryable).toBe(true);
    });

    it('should continue with other data types when one fails', async () => {
      // Contacts fail, but messages succeed
      mockContactBackupService.scanContacts.mockRejectedValue(new Error('Contacts failed'));
      mockMessageBackupService.scanMessages.mockResolvedValue([]);
      mockMessageBackupService.backupMessages.mockResolvedValue({ success: true, itemsProcessed: 0, errors: [] });

      const configuration = { ...defaultConfiguration, includePhotos: false };
      const result = await BackupManager.startBackup(configuration);

      expect(mockMessageBackupService.scanMessages).toHaveBeenCalled();
      expect(result.errors.length).toBeGreaterThan(0); // Has contact errors
    });

    it('should provide retry functionality', async () => {
      const result = await BackupManager.retryFailedItems();
      
      expect(result.success).toBe(true);
      // This is a placeholder test - actual retry logic would be more complex
    });
  });

  describe('cleanup functionality', () => {
    it('should clean up old sessions', async () => {
      await expect(BackupManager.cleanupOldSessions()).resolves.not.toThrow();
    });
  });
});