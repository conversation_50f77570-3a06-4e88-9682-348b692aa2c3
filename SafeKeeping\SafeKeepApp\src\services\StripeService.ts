import Stripe from 'stripe';
import { StripeProvider, useStripe, useConfirmPayment, initStripe } from '@stripe/stripe-react-native';

// IMPORTANT: In React Native, environment variables work differently
// For production, use a secure config management solution
const STRIPE_SECRET_KEY = 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP'; // This should be on your backend only
const STRIPE_PUBLISHABLE_KEY = 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP';

// Note: In production, the secret key should NEVER be in the mobile app
// It should only be on your secure backend server
console.log('⚠️ Using hardcoded Stripe keys for development. In production, use secure config management.');

// Note: Server-side Stripe operations should be handled by your backend
// This service focuses on React Native client-side operations

// Payment Intent interface for SafeKeep subscriptions
export interface PaymentIntentData {
  amount: number; // Amount in cents (e.g., 999 for $9.99)
  currency: string; // e.g., 'usd'
  customerId?: string;
  description?: string;
  metadata?: Record<string, string>;
}

// Subscription plans for SafeKeep
export const SAFEKEEP_PLANS = {
  BASIC: {
    id: 'safekeep_basic',
    name: 'Basic',
    price: 299, // $2.99/month in cents
    currency: 'usd',
    interval: 'month',
    features: [
      '5GB Secure Storage',
      'Photo Backup',
      'Contact Backup',
      'Basic Support',
      'End-to-End Encryption'
    ],
  },
  PREMIUM: {
    id: 'safekeep_premium',
    name: 'Premium',
    price: 999, // $9.99/month in cents
    currency: 'usd',
    interval: 'month',
    features: [
      '50GB Secure Storage',
      'Photo & Video Backup',
      'Contact & Message Backup',
      'Priority Support',
      'Advanced Backup Features',
      'Family Sharing (up to 3 users)',
      'End-to-End Encryption'
    ],
  },
  FAMILY: {
    id: 'safekeep_family',
    name: 'Family',
    price: 1999, // $19.99/month in cents
    currency: 'usd',
    interval: 'month',
    features: [
      '200GB Secure Storage',
      'All Premium Features',
      'Family Sharing (up to 6 users)',
      'Multiple Device Support',
      'Premium Support',
      'Advanced Analytics',
      'Custom Backup Schedules',
      'End-to-End Encryption'
    ],
  },
} as const;

// React Native Stripe Service - Client-side operations only
export const StripeService = {
  // Get publishable key for frontend use
  getPublishableKey: (): string => {
    return STRIPE_PUBLISHABLE_KEY;
  },

  // Get plan by ID
  getPlan: (planId: string) => {
    return Object.values(SAFEKEEP_PLANS).find(plan => plan.id === planId);
  },

  // Get all plans
  getAllPlans: () => {
    return SAFEKEEP_PLANS;
  },

  // Create payment intent via backend API
  createPaymentIntent: async (data: {
    amount: number;
    currency: string;
    description: string;
    planId?: string;
  }) => {
    try {
      console.log('🔄 Creating payment intent via backend...', data);

      const response = await fetch('http://localhost:3000/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const paymentIntent = await response.json();
      console.log('✅ Payment intent created:', paymentIntent.payment_intent_id);
      return paymentIntent;
    } catch (error: unknown) {
      console.error('❌ Failed to create payment intent:', error);
      // Re-throw with proper error handling
      if (error instanceof Error) {
        throw error; // Preserve original Error object if it's already an Error
      } else {
        throw new Error(`Failed to create payment intent: ${String(error)}`);
      }
    }
  },
};

// React Native Stripe initialization
export const initializeStripe = async () => {
  try {
    await initStripe({
      publishableKey: STRIPE_PUBLISHABLE_KEY,
      merchantIdentifier: 'merchant.com.safekeep.app', // For Apple Pay
      urlScheme: 'safekeep', // For redirects
    });
    console.log('✅ Stripe initialized successfully for React Native');
    return true;
  } catch (error: unknown) {
    console.error('❌ Failed to initialize Stripe:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Stripe initialization failed: ${errorMessage}`);
  }
};

// Export React Native Stripe components for frontend use
export { StripeProvider, useStripe, useConfirmPayment, initStripe };

// Export publishable key for frontend initialization
export const STRIPE_PUBLISHABLE_KEY_EXPORT = STRIPE_PUBLISHABLE_KEY;

// Export default service
export default StripeService;
