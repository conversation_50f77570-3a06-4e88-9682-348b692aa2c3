/**
 * End-to-End Test Runner
 * Tests complete user journeys using Puppeteer
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class E2ETestRunner {
    constructor() {
        this.browser = null;
        this.testResults = [];
        this.baseUrl = 'http://localhost:3000';
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('🚀 Initializing E2E Test Environment...');
        
        this.browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        // Ensure test reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
    }

    async runAllTests() {
        await this.initialize();

        const testSuites = [
            this.testUserSignupFlow,
            this.testBackupProgressFlow,
            this.testEncryptionDemoFlow,
            this.testRestoreSimulationFlow,
            this.testSchedulingFlow,
            this.testSubscriptionFlow,
            this.testAnalyticsDashboard,
            this.testErrorRecovery,
            this.testMobileResponsiveness
        ];

        console.log('📋 Running E2E Test Suites...');
        console.log('===============================');

        for (const testSuite of testSuites) {
            await this.runTestSuite(testSuite);
        }

        await this.cleanup();
        this.generateReport();
    }

    async runTestSuite(testFunction) {
        const testName = testFunction.name;
        console.log(`\n🧪 Running ${testName}...`);

        const startTime = Date.now();
        let page = null;

        try {
            page = await this.browser.newPage();
            await page.setViewport({ width: 1280, height: 720 });
            
            const result = await testFunction.call(this, page);
            const duration = Date.now() - startTime;

            this.testResults.push({
                test: testName,
                status: result.success ? 'PASSED' : 'FAILED',
                duration,
                details: result.details,
                errors: result.errors || [],
                screenshots: result.screenshots || []
            });

            console.log(`${result.success ? '✅' : '❌'} ${testName} - ${duration}ms`);

        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`❌ ${testName} failed:`, error.message);
            
            this.testResults.push({
                test: testName,
                status: 'ERROR',
                duration,
                details: 'Test execution failed',
                errors: [error.message],
                screenshots: []
            });
        } finally {
            if (page) {
                await page.close();
            }
        }
    }

    async testUserSignupFlow(page) {
        const steps = [];
        const screenshots = [];

        try {
            // Navigate to demo
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });
            steps.push('✅ Demo page loaded');

            // Test signup process
            await page.click('#signup-btn');
            await page.waitForSelector('#signup-form', { timeout: 3000 });
            steps.push('✅ Signup form displayed');

            await page.type('#email-input', '<EMAIL>');
            await page.type('#password-input', 'testpassword123');
            await page.click('#signup-submit');
            
            // Wait for signup completion
            await page.waitForSelector('#user-dashboard', { timeout: 10000 });
            steps.push('✅ User successfully signed up and logged in');

            // Take screenshot
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'user-dashboard', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'error-state', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testBackupProgressFlow(page) {
        const steps = [];
        const screenshots = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Start backup process
            await page.click('#start-backup-btn');
            await page.waitForSelector('#backup-progress-container', { timeout: 3000 });
            steps.push('✅ Backup progress container displayed');

            // Wait for progress updates
            await page.waitForFunction(
                () => document.querySelector('#overall-progress').textContent !== '0%',
                { timeout: 10000 }
            );
            steps.push('✅ Real-time progress updates working');

            // Check for completion
            await page.waitForFunction(
                () => document.querySelector('#backup-status').textContent === 'Completed',
                { timeout: 30000 }
            );
            steps.push('✅ Backup completed successfully');

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'backup-completed', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'backup-error', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testEncryptionDemoFlow(page) {
        const steps = [];
        const screenshots = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to encryption demo
            await page.click('#encryption-demo-tab');
            await page.waitForSelector('#encryption-demo-container', { timeout: 3000 });
            steps.push('✅ Encryption demo loaded');

            // Test encryption process
            await page.click('#start-encryption-btn');
            await page.waitForSelector('#encryption-progress', { timeout: 3000 });
            steps.push('✅ Encryption process started');

            // Wait for encryption completion
            await page.waitForFunction(
                () => document.querySelector('#encryption-status').textContent === 'Completed',
                { timeout: 15000 }
            );
            steps.push('✅ Encryption completed');

            // Verify before/after comparison
            const beforeData = await page.$eval('#before-encryption', el => el.textContent);
            const afterData = await page.$eval('#after-encryption', el => el.textContent);
            
            if (beforeData !== afterData) {
                steps.push('✅ Before/after data comparison working');
            } else {
                throw new Error('Encryption did not change data');
            }

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'encryption-demo', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'encryption-error', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testRestoreSimulationFlow(page) {
        const steps = [];
        const screenshots = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to restore simulation
            await page.click('#restore-simulation-tab');
            await page.waitForSelector('#restore-container', { timeout: 3000 });
            steps.push('✅ Restore simulation loaded');

            // Select backup to restore
            await page.click('#backup-selection-dropdown');
            await page.click('#backup-option-1');
            steps.push('✅ Backup selected for restore');

            // Start restore process
            await page.click('#start-restore-btn');
            await page.waitForSelector('#restore-progress', { timeout: 3000 });
            steps.push('✅ Restore process started');

            // Wait for restore completion
            await page.waitForFunction(
                () => document.querySelector('#restore-status').textContent === 'Completed',
                { timeout: 20000 }
            );
            steps.push('✅ Restore completed');

            // Verify restored data display
            await page.waitForSelector('#restored-data-preview', { timeout: 3000 });
            steps.push('✅ Restored data preview displayed');

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'restore-completed', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'restore-error', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testSchedulingFlow(page) {
        const steps = [];
        const screenshots = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to scheduling
            await page.click('#scheduling-tab');
            await page.waitForSelector('#schedule-container', { timeout: 3000 });
            steps.push('✅ Scheduling interface loaded');

            // Configure schedule
            await page.select('#frequency-select', 'daily');
            await page.type('#schedule-time', '14:30');
            await page.click('#wifi-only-checkbox');
            steps.push('✅ Schedule configured');

            // Save schedule
            await page.click('#save-schedule-btn');
            await page.waitForSelector('#schedule-confirmation', { timeout: 3000 });
            steps.push('✅ Schedule saved successfully');

            // Test schedule simulation
            await page.click('#simulate-schedule-btn');
            await page.waitForSelector('#next-run-display', { timeout: 3000 });
            steps.push('✅ Schedule simulation working');

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'scheduling-configured', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'scheduling-error', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testSubscriptionFlow(page) {
        const steps = [];
        const screenshots = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to subscription
            await page.click('#subscription-tab');
            await page.waitForSelector('#subscription-tiers', { timeout: 3000 });
            steps.push('✅ Subscription tiers displayed');

            // Select premium tier
            await page.click('#premium-tier-btn');
            await page.waitForSelector('#payment-form', { timeout: 3000 });
            steps.push('✅ Payment form displayed');

            // Fill payment form (test mode)
            await page.type('#card-number', '****************');
            await page.type('#card-expiry', '12/25');
            await page.type('#card-cvc', '123');
            steps.push('✅ Payment details entered');

            // Submit payment (in test mode)
            await page.click('#submit-payment-btn');
            await page.waitForSelector('#payment-success', { timeout: 10000 });
            steps.push('✅ Payment processed successfully');

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'subscription-success', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'subscription-error', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testAnalyticsDashboard(page) {
        const steps = [];
        const screenshots = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to analytics
            await page.click('#analytics-tab');
            await page.waitForSelector('#analytics-dashboard', { timeout: 3000 });
            steps.push('✅ Analytics dashboard loaded');

            // Wait for charts to render
            await page.waitForSelector('#backup-success-chart', { timeout: 5000 });
            await page.waitForSelector('#storage-usage-chart', { timeout: 5000 });
            steps.push('✅ Charts rendered successfully');

            // Test data filtering
            await page.click('#filter-last-30-days');
            await page.waitForFunction(
                () => document.querySelector('#chart-data-points').children.length > 0,
                { timeout: 5000 }
            );
            steps.push('✅ Data filtering working');

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'analytics-dashboard', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'analytics-error', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testErrorRecovery(page) {
        const steps = [];
        const screenshots = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate network error
            await page.setOfflineMode(true);
            await page.click('#start-backup-btn');
            
            // Wait for error handling
            await page.waitForSelector('#error-message', { timeout: 5000 });
            steps.push('✅ Error message displayed for offline mode');

            // Test recovery
            await page.setOfflineMode(false);
            await page.click('#retry-btn');
            
            // Wait for recovery
            await page.waitForSelector('#backup-progress-container', { timeout: 5000 });
            steps.push('✅ Error recovery working');

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'error-recovery', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'error-recovery-failed', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async testMobileResponsiveness(page) {
        const steps = [];
        const screenshots = [];

        try {
            // Test mobile viewport
            await page.setViewport({ width: 375, height: 667 });
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });
            steps.push('✅ Mobile viewport loaded');

            // Test navigation menu
            await page.click('#mobile-menu-btn');
            await page.waitForSelector('#mobile-nav-menu', { timeout: 3000 });
            steps.push('✅ Mobile navigation working');

            // Test responsive components
            const backupBtn = await page.$('#start-backup-btn');
            const btnRect = await backupBtn.boundingBox();
            
            if (btnRect.width > 44) { // Minimum touch target size
                steps.push('✅ Touch targets appropriately sized');
            } else {
                throw new Error('Touch targets too small for mobile');
            }

            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'mobile-responsive', data: screenshot });

            return {
                success: true,
                details: steps.join('\n'),
                screenshots
            };

        } catch (error) {
            const screenshot = await page.screenshot({ encoding: 'base64' });
            screenshots.push({ name: 'mobile-error', data: screenshot });
            
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message],
                screenshots
            };
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    generateReport() {
        const totalDuration = Date.now() - this.startTime;
        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const failed = this.testResults.filter(r => r.status === 'FAILED').length;
        const errors = this.testResults.filter(r => r.status === 'ERROR').length;
        const total = this.testResults.length;

        console.log('\n📊 E2E Test Results');
        console.log('====================');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed} ✅`);
        console.log(`Failed: ${failed} ❌`);
        console.log(`Errors: ${errors} 💥`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        // Generate detailed report
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total,
                passed,
                failed,
                errors,
                successRate: ((passed / total) * 100).toFixed(1),
                totalDuration
            },
            results: this.testResults
        };

        fs.writeFileSync(
            path.join(__dirname, '../test-reports/e2e-test-report.json'),
            JSON.stringify(report, null, 2)
        );

        // Save screenshots
        this.testResults.forEach(result => {
            result.screenshots.forEach(screenshot => {
                const filename = `${result.test}-${screenshot.name}.png`;
                const filepath = path.join(__dirname, '../test-reports/screenshots', filename);
                
                // Ensure screenshots directory exists
                const screenshotsDir = path.dirname(filepath);
                if (!fs.existsSync(screenshotsDir)) {
                    fs.mkdirSync(screenshotsDir, { recursive: true });
                }
                
                fs.writeFileSync(filepath, screenshot.data, 'base64');
            });
        });

        console.log('\n📄 Report saved to test-reports/e2e-test-report.json');
        console.log('📸 Screenshots saved to test-reports/screenshots/');
    }
}

// Run tests if called directly
if (require.main === module) {
    const runner = new E2ETestRunner();
    runner.runAllTests().catch(console.error);
}

module.exports = E2ETestRunner;