export interface Database {
  public: {
    Tables: {
      users: {
        Row: UserProfile;
        Insert: UserProfileInsert;
        Update: UserProfileUpdate;
      };
      file_metadata: {
        Row: FileMetadata;
        Insert: FileMetadataInsert;
        Update: FileMetadataUpdate;
      };
      backup_sessions: {
        Row: BackupSession;
        Insert: BackupSessionInsert;
        Update: BackupSessionUpdate;
      };
      storage_usage: {
        Row: StorageUsage;
        Insert: StorageUsageInsert;
        Update: StorageUsageUpdate;
      };
    };
    Functions: {
      exec_sql: {
        Args: { sql_query: string };
        Returns: void;
      };
      verify_rls_policies: {
        Args: Record<string, never>;
        Returns: Array<{
          table_name: string;
          policy_name: string;
          cmd: string;
          policy_exists: boolean;
          policy_using: string | null;
          policy_with_check: string | null;
        }>;
      };
      test_rls_policies: {
        Args: { test_user_id: string };
        Returns: {
          user_id: string;
          can_read: boolean;
          can_insert: boolean;
          can_update: boolean;
          can_delete: boolean;
        };
      };
    };
  };
}

export interface UserProfile {
  id: string;
  email: string;
  display_name: string;
  created_at: string;
  last_login_at: string;
  storage_used: number;
  storage_quota: number;
  encryption_key_id: string | null;
  subscription_tier: 'basic' | 'premium' | 'family';
  subscription_status: 'active' | 'cancelled' | 'expired';
  subscription_expires_at: string | null;
  backup_settings: {
    auto_backup: boolean;
    wifi_only: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
  };
}

export interface UserProfileInsert extends Omit<UserProfile, 'id' | 'created_at' | 'last_login_at'> {
  id?: string;
}

export interface UserProfileUpdate extends Partial<UserProfile> {}

export interface FileMetadata {
  id: string;
  user_id: string;
  original_name: string;
  encrypted_name: string;
  mime_type: string;
  size: number;
  encrypted_size: number;
  uploaded_at: string;
  last_modified: string;
  category: 'photo' | 'contact' | 'message';
  hash: string;
  encryption_iv: string;
  encryption_salt: string;
  storage_path: string;
  is_backed_up: boolean;
  device_id: string | null;
  sync_status: 'synced' | 'pending' | 'failed';
}

export interface FileMetadataInsert extends Omit<FileMetadata, 'id' | 'uploaded_at'> {
  id?: string;
}

export interface FileMetadataUpdate extends Partial<FileMetadata> {}

export interface BackupSession {
  id: string;
  user_id: string;
  session_type: 'manual' | 'automatic';
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at: string | null;
  total_files: number;
  processed_files: number;
  failed_files: number;
  total_bytes: number;
  processed_bytes: number;
  error_message: string | null;
  device_id: string | null;
}

export interface BackupSessionInsert extends Omit<BackupSession, 'id' | 'started_at'> {
  id?: string;
}

export interface BackupSessionUpdate extends Partial<BackupSession> {}

export interface StorageUsage {
  id: string;
  user_id: string;
  category: 'photo' | 'contact' | 'message' | 'other';
  bytes_used: number;
  file_count: number;
  last_updated: string;
}

export interface StorageUsageInsert extends Omit<StorageUsage, 'id' | 'last_updated'> {
  id?: string;
}

export interface StorageUsageUpdate extends Partial<StorageUsage> {}