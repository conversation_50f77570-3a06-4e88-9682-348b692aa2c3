import RetryService from '../RetryService';
import { BackupError } from '../../types/backup';

// Mock console methods to avoid noise in tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('RetryService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('executeWithRetry', () => {
    it('should succeed on first attempt', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const result = await RetryService.executeWithRetry(mockOperation);
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockResolvedValue('success');
      
      const result = await RetryService.executeWithRetry(mockOperation, {
        maxRetries: 2,
        baseDelay: 10,
        backoffMultiplier: 1,
        jitter: false
      });
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(2);
      expect(mockOperation).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Persistent failure'));
      
      const result = await RetryService.executeWithRetry(mockOperation, {
        maxRetries: 2,
        baseDelay: 10,
        backoffMultiplier: 1,
        jitter: false
      });
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Persistent failure');
      expect(result.attempts).toBe(3); // Initial attempt + 2 retries
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });

    it('should not retry non-retryable errors', async () => {
      const nonRetryableError = new Error('Permission denied');
      (nonRetryableError as any).status = 403;
      
      const mockOperation = jest.fn().mockRejectedValue(nonRetryableError);
      
      const result = await RetryService.executeWithRetry(mockOperation, {
        maxRetries: 3,
        baseDelay: 10
      });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('should apply exponential backoff', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Network error'));
      const startTime = Date.now();
      
      await RetryService.executeWithRetry(mockOperation, {
        maxRetries: 2,
        baseDelay: 100,
        backoffMultiplier: 2,
        jitter: false
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should have waited at least 100ms + 200ms = 300ms
      expect(duration).toBeGreaterThan(250);
      expect(mockOperation).toHaveBeenCalledTimes(3);
    });
  });

  describe('retryBackupError', () => {
    it('should not retry non-retryable errors', async () => {
      const error: BackupError = {
        id: 'test-error',
        type: 'permission',
        message: 'Permission denied',
        timestamp: new Date(),
        retryable: false
      };
      
      const mockOperation = jest.fn();
      
      const result = await RetryService.retryBackupError(error, mockOperation);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(result.attempts).toBe(0);
      expect(mockOperation).not.toHaveBeenCalled();
    });

    it('should retry retryable errors with appropriate config', async () => {
      const error: BackupError = {
        id: 'test-error',
        type: 'network',
        message: 'Network timeout',
        timestamp: new Date(),
        retryable: true
      };
      
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const result = await RetryService.retryBackupError(error, mockOperation);
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });
  });

  describe('retryBatch', () => {
    it('should process multiple operations sequentially', async () => {
      const operations = [
        {
          id: 'op1',
          operation: jest.fn().mockResolvedValue('result1')
        },
        {
          id: 'op2',
          operation: jest.fn().mockResolvedValue('result2')
        },
        {
          id: 'op3',
          operation: jest.fn().mockRejectedValue(new Error('Failed'))
        }
      ];
      
      const results = await RetryService.retryBatch(operations, {
        maxRetries: 1,
        baseDelay: 10
      });
      
      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[0].result).toBe('result1');
      expect(results[1].success).toBe(true);
      expect(results[1].result).toBe('result2');
      expect(results[2].success).toBe(false);
      expect(results[2].error).toBeDefined();
    });

    it('should handle operations with existing errors', async () => {
      const existingError: BackupError = {
        id: 'existing-error',
        type: 'network',
        message: 'Previous failure',
        timestamp: new Date(),
        retryable: true
      };
      
      const operations = [
        {
          id: 'op1',
          operation: jest.fn().mockResolvedValue('success'),
          error: existingError
        }
      ];
      
      const results = await RetryService.retryBatch(operations);
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
      expect(results[0].result).toBe('success');
    });
  });

  describe('error type determination', () => {
    it('should correctly identify network errors', async () => {
      const networkError = new Error('Network request failed');
      (networkError as any).code = 'NETWORK_ERROR';
      
      const mockOperation = jest.fn().mockRejectedValue(networkError);
      
      const result = await RetryService.executeWithRetry(mockOperation, {
        maxRetries: 0
      });
      
      expect(result.error?.type).toBe('network');
    });

    it('should correctly identify permission errors', async () => {
      const permissionError = new Error('Permission denied');
      (permissionError as any).status = 403;
      
      const mockOperation = jest.fn().mockRejectedValue(permissionError);
      
      const result = await RetryService.executeWithRetry(mockOperation, {
        maxRetries: 0
      });
      
      expect(result.error?.type).toBe('permission');
    });

    it('should correctly identify storage errors', async () => {
      const storageError = new Error('Storage quota exceeded');
      (storageError as any).code = 'STORAGE_ERROR';
      
      const mockOperation = jest.fn().mockRejectedValue(storageError);
      
      const result = await RetryService.executeWithRetry(mockOperation, {
        maxRetries: 0
      });
      
      expect(result.error?.type).toBe('storage');
    });
  });

  describe('retry configuration by error type', () => {
    it('should use appropriate retry config for network errors', async () => {
      const networkError: BackupError = {
        id: 'network-error',
        type: 'network',
        message: 'Connection timeout',
        timestamp: new Date(),
        retryable: true
      };
      
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('Still failing'))
        .mockResolvedValue('success');
      
      const result = await RetryService.retryBackupError(networkError, mockOperation);
      
      expect(result.success).toBe(true);
      expect(mockOperation).toHaveBeenCalledTimes(2);
    });

    it('should not retry permission errors', async () => {
      const permissionError: BackupError = {
        id: 'permission-error',
        type: 'permission',
        message: 'Access denied',
        timestamp: new Date(),
        retryable: false
      };
      
      const mockOperation = jest.fn();
      
      const result = await RetryService.retryBackupError(permissionError, mockOperation);
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(0);
      expect(mockOperation).not.toHaveBeenCalled();
    });
  });
});