import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { ApiResponse } from '../types/modular-pricing';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role?: string;
      };
    }
  }
}

interface JWTPayload {
  sub: string; // user ID
  email: string;
  role?: string;
  iat?: number;
  exp?: number;
}

/**
 * JWT token validation middleware
 * Validates JWT tokens and extracts user information
 */
export const authenticateToken = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    const response: ApiResponse = {
      success: false,
      error: {
        code: 'MISSING_TOKEN',
        message: 'Access token is required',
        timestamp: new Date().toISOString()
      }
    };
    res.status(401).json(response);
    return;
  }

  try {
    // Use Supabase JWT secret for verification
    const jwtSecret = process.env.REACT_APP_SUPABASE_SERVICE_KEY || 'fallback-secret-for-development';
    const decoded = jwt.verify(token, jwtSecret) as JWTPayload;
    
    // Extract user information from token
    req.user = {
      id: decoded.sub,
      email: decoded.email,
      role: decoded.role || 'user'
    };
    
    next();
  } catch (error) {
    let errorCode = 'INVALID_TOKEN';
    let errorMessage = 'Invalid access token';
    
    if (error instanceof jwt.TokenExpiredError) {
      errorCode = 'TOKEN_EXPIRED';
      errorMessage = 'Access token has expired';
    } else if (error instanceof jwt.JsonWebTokenError) {
      errorCode = 'MALFORMED_TOKEN';
      errorMessage = 'Malformed access token';
    }
    
    const response: ApiResponse = {
      success: false,
      error: {
        code: errorCode,
        message: errorMessage,
        timestamp: new Date().toISOString()
      }
    };
    res.status(403).json(response);
  }
};

/**
 * Role-based access control middleware
 * Requires specific role for access to endpoints
 */
export const requireRole = (requiredRole: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString()
        }
      };
      res.status(401).json(response);
      return;
    }

    const userRole = req.user.role || 'user';
    
    // Check if user has required role
    if (userRole !== requiredRole && userRole !== 'admin') {
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: `${requiredRole} role required`,
          timestamp: new Date().toISOString()
        }
      };
      res.status(403).json(response);
      return;
    }

    next();
  };
};

/**
 * Admin access control middleware
 * Requires admin role for access
 */
export const requireAdmin = requireRole('admin');

/**
 * User ownership validation middleware
 * Ensures user can only access their own resources
 */
export const requireOwnership = (userIdParam: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: new Date().toISOString()
        }
      };
      res.status(401).json(response);
      return;
    }

    const requestedUserId = req.params[userIdParam] || req.body.userId;
    const authenticatedUserId = req.user.id;
    
    // Admin can access any user's resources
    if (req.user.role === 'admin') {
      next();
      return;
    }
    
    // User can only access their own resources
    if (requestedUserId !== authenticatedUserId) {
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: 'You can only access your own resources',
          timestamp: new Date().toISOString()
        }
      };
      res.status(403).json(response);
      return;
    }

    next();
  };
};

/**
 * Extract user ID from authenticated request
 * Returns null if user is not authenticated
 */
export const extractUserId = (req: Request): string | null => {
  return req.user?.id || null;
};

/**
 * Extract user role from authenticated request
 * Returns 'user' as default if no role is specified
 */
export const extractUserRole = (req: Request): string => {
  return req.user?.role || 'user';
};

/**
 * Check if user is admin
 */
export const isAdmin = (req: Request): boolean => {
  return req.user?.role === 'admin';
};