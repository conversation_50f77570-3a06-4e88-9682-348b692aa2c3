import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { 
  Button, 
  Card, 
  Text, 
  TextInput, 
  SegmentedButtons,
  ActivityIndicator,
  Chip
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

import AuthService from '../../services/AuthService';
import EncryptionService from '../../services/EncryptionService';
import { COLORS, SPACING } from '../../utils/constants';

const AuthScreen = () => {
  const navigation = useNavigation();
  const [mode, setMode] = useState<'signin' | 'signup'>('signin');
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    displayName: ''
  });
  const [encryptionStatus, setEncryptionStatus] = useState({
    isInitialized: false,
    isWorking: false
  });

  useEffect(() => {
    checkEncryptionStatus();
  }, []);

  const checkEncryptionStatus = async () => {
    try {
      const status = await EncryptionService.getEncryptionStatus();
      setEncryptionStatus(status);
    } catch (error) {
      console.error('Failed to check encryption status:', error);
    }
  };

  const validateForm = (): string | null => {
    if (!formData.email.trim()) {
      return 'Please enter your email address';
    }

    if (!formData.email.includes('@')) {
      return 'Please enter a valid email address';
    }

    if (!formData.password) {
      return 'Please enter your password';
    }

    if (formData.password.length < 6) {
      return 'Password must be at least 6 characters long';
    }

    if (mode === 'signup') {
      if (!formData.displayName.trim()) {
        return 'Please enter your name';
      }

      if (formData.password !== formData.confirmPassword) {
        return 'Passwords do not match';
      }
    }

    return null;
  };

  const handleSignIn = async () => {
    const validationError = validateForm();
    if (validationError) {
      Alert.alert('Validation Error', validationError);
      return;
    }

    setIsLoading(true);
    try {
      const result = await AuthService.signInUser(formData.email, formData.password);
      
      if (result.success) {
        Alert.alert(
          '🎉 Welcome Back!',
          'You have been signed in successfully. Your data is ready to sync!',
          [
            {
              text: 'Continue',
              onPress: () => navigation.navigate('Main' as never)
            }
          ]
        );
      } else {
        Alert.alert('Sign In Failed', result.error || 'Please try again');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async () => {
    const validationError = validateForm();
    if (validationError) {
      Alert.alert('Validation Error', validationError);
      return;
    }

    setIsLoading(true);
    try {
      const result = await AuthService.registerUser(
        formData.email,
        formData.password,
        formData.displayName
      );
      
      if (result.success) {
        Alert.alert(
          '🎉 Account Created!',
          'Your SafeKeep account has been created successfully with military-grade encryption. Choose your subscription plan to start backing up your data!',
          [
            {
              text: 'Choose Plan',
              onPress: () => navigation.navigate('Subscription' as never)
            }
          ]
        );
      } else {
        Alert.alert('Registration Failed', result.error || 'Please try again');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!formData.email.trim()) {
      Alert.alert('Email Required', 'Please enter your email address first');
      return;
    }

    try {
      const result = await AuthService.resetPassword(formData.email);
      
      if (result.success) {
        Alert.alert(
          '📧 Reset Email Sent',
          'We have sent a password reset link to your email address. Please check your inbox and follow the instructions.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Reset Failed', result.error || 'Please try again');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to send reset email. Please try again.');
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: mode === 'signin' ? 'Sign In' : 'Create Account',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.content}>
          {/* Welcome Card */}
          <Card style={styles.welcomeCard}>
            <Card.Content>
              <View style={styles.welcomeHeader}>
                <Icon name="shield-lock" size={48} color={COLORS.primary} />
                <Text variant="headlineSmall" style={styles.welcomeTitle}>
                  {mode === 'signin' ? 'Welcome Back' : 'Join SafeKeep'}
                </Text>
              </View>
              
              <Text variant="bodyLarge" style={styles.welcomeText}>
                {mode === 'signin' 
                  ? 'Sign in to access your securely backed up photos, contacts, and messages.'
                  : 'Create your account to start backing up your precious data with military-grade encryption.'
                }
              </Text>

              {/* Encryption Status */}
              <View style={styles.encryptionStatus}>
                <Chip 
                  icon="shield-check" 
                  style={[
                    styles.statusChip,
                    { backgroundColor: encryptionStatus.isWorking ? COLORS.success : COLORS.warning }
                  ]}
                  textStyle={{ color: '#fff' }}
                >
                  AES-256 Encryption {encryptionStatus.isWorking ? 'Active' : 'Ready'}
                </Chip>
              </View>
            </Card.Content>
          </Card>

          {/* Mode Toggle */}
          <View style={styles.modeToggle}>
            <SegmentedButtons
              value={mode}
              onValueChange={(value) => setMode(value as 'signin' | 'signup')}
              buttons={[
                {
                  value: 'signin',
                  label: 'Sign In',
                  icon: 'login',
                },
                {
                  value: 'signup',
                  label: 'Sign Up',
                  icon: 'account-plus',
                },
              ]}
            />
          </View>

          {/* Form Card */}
          <Card style={styles.formCard}>
            <Card.Content>
              {mode === 'signup' && (
                <TextInput
                  label="Your Name"
                  value={formData.displayName}
                  onChangeText={(text) => updateFormData('displayName', text)}
                  mode="outlined"
                  style={styles.input}
                  left={<TextInput.Icon icon="account" />}
                  disabled={isLoading}
                />
              )}

              <TextInput
                label="Email Address"
                value={formData.email}
                onChangeText={(text) => updateFormData('email', text)}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                style={styles.input}
                left={<TextInput.Icon icon="email" />}
                disabled={isLoading}
              />

              <TextInput
                label="Password"
                value={formData.password}
                onChangeText={(text) => updateFormData('password', text)}
                mode="outlined"
                secureTextEntry
                style={styles.input}
                left={<TextInput.Icon icon="lock" />}
                disabled={isLoading}
              />

              {mode === 'signup' && (
                <TextInput
                  label="Confirm Password"
                  value={formData.confirmPassword}
                  onChangeText={(text) => updateFormData('confirmPassword', text)}
                  mode="outlined"
                  secureTextEntry
                  style={styles.input}
                  left={<TextInput.Icon icon="lock-check" />}
                  disabled={isLoading}
                />
              )}

              <View style={styles.actionButtons}>
                <Button
                  mode="contained"
                  onPress={mode === 'signin' ? handleSignIn : handleSignUp}
                  disabled={isLoading}
                  loading={isLoading}
                  style={styles.primaryButton}
                  icon={mode === 'signin' ? 'login' : 'account-plus'}
                >
                  {mode === 'signin' ? 'Sign In' : 'Create Account'}
                </Button>

                {mode === 'signin' && (
                  <Button
                    mode="text"
                    onPress={handleForgotPassword}
                    disabled={isLoading}
                    style={styles.textButton}
                  >
                    Forgot Password?
                  </Button>
                )}
              </View>
            </Card.Content>
          </Card>

          {/* Security Info */}
          <Card style={styles.securityCard}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.securityTitle}>
                🔐 Your Data is Secure
              </Text>
              
              <View style={styles.securityFeatures}>
                <View style={styles.securityFeature}>
                  <Icon name="shield-check" size={20} color={COLORS.success} />
                  <Text style={styles.securityText}>End-to-end encryption</Text>
                </View>
                <View style={styles.securityFeature}>
                  <Icon name="cloud-lock" size={20} color={COLORS.success} />
                  <Text style={styles.securityText}>Secure cloud storage</Text>
                </View>
                <View style={styles.securityFeature}>
                  <Icon name="key" size={20} color={COLORS.success} />
                  <Text style={styles.securityText}>Only you have the keys</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  welcomeCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  welcomeHeader: {
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  welcomeTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginTop: SPACING.sm,
  },
  welcomeText: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.md,
  },
  encryptionStatus: {
    alignItems: 'center',
  },
  statusChip: {
    elevation: 2,
  },
  modeToggle: {
    marginBottom: SPACING.lg,
  },
  formCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  input: {
    marginBottom: SPACING.md,
  },
  actionButtons: {
    gap: SPACING.sm,
    marginTop: SPACING.md,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  textButton: {
    alignSelf: 'center',
  },
  securityCard: {
    elevation: 4,
  },
  securityTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  securityFeatures: {
    gap: SPACING.sm,
  },
  securityFeature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  securityText: {
    color: COLORS.textSecondary,
    flex: 1,
  },
});

export default AuthScreen;
