import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { 
  <PERSON><PERSON>, 
  Card, 
  Text, 
  Switch,
  RadioButton,
  Divider,
  Chip
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation, useRoute } from '@react-navigation/native';

import RecoveryService, { RestoreOptions } from '../../services/RecoveryService';
import { COLORS, SPACING } from '../../utils/constants';

const RestoreSelectionScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [restoreOptions, setRestoreOptions] = useState<RestoreOptions>({
    selectAll: false,
    categories: {
      photos: { selected: true, downloadWifi: true, downloadCellular: false },
      contacts: { selected: true, mergeStrategy: 'smart' },
      messages: { selected: true, format: 'native' },
      documents: { selected: false }
    }
  });

  useEffect(() => {
    // Check if we have preselected options from navigation
    const params = route.params as any;
    if (params?.preselectedOptions) {
      setRestoreOptions(params.preselectedOptions);
    }
  }, [route.params]);

  const updateCategorySelection = (category: keyof typeof restoreOptions.categories, selected: boolean) => {
    setRestoreOptions(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [category]: {
          ...prev.categories[category],
          selected
        }
      }
    }));
  };

  const updateCategoryOption = (
    category: keyof typeof restoreOptions.categories,
    option: string,
    value: any
  ) => {
    setRestoreOptions(prev => ({
      ...prev,
      categories: {
        ...prev.categories,
        [category]: {
          ...prev.categories[category],
          [option]: value
        }
      }
    }));
  };

  const toggleSelectAll = () => {
    const newSelectAll = !restoreOptions.selectAll;
    setRestoreOptions(prev => ({
      ...prev,
      selectAll: newSelectAll,
      categories: {
        photos: { ...prev.categories.photos, selected: newSelectAll },
        contacts: { ...prev.categories.contacts, selected: newSelectAll },
        messages: { ...prev.categories.messages, selected: newSelectAll },
        documents: { ...prev.categories.documents, selected: newSelectAll }
      }
    }));
  };

  const getSelectedCount = (): number => {
    return Object.values(restoreOptions.categories).filter(cat => cat.selected).length;
  };

  const handleStartRestore = () => {
    const selectedCount = getSelectedCount();
    
    if (selectedCount === 0) {
      Alert.alert(
        'No Categories Selected',
        'Please select at least one category to restore.',
        [{ text: 'OK' }]
      );
      return;
    }

    const selectedCategories = Object.entries(restoreOptions.categories)
      .filter(([_, options]) => options.selected)
      .map(([category, _]) => category);

    Alert.alert(
      '🔄 Start Data Restore?',
      `This will restore the following categories:\n\n${selectedCategories.map(cat => `• ${cat.charAt(0).toUpperCase() + cat.slice(1)}`).join('\n')}\n\nThe restore process will begin immediately and may take several minutes.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Start Restore', 
          onPress: () => navigation.navigate('RestoreProgress' as never, { restoreOptions } as never)
        }
      ]
    );
  };

  const getCategoryIcon = (category: string): string => {
    switch (category) {
      case 'photos': return 'camera';
      case 'contacts': return 'contacts';
      case 'messages': return 'message-text';
      case 'documents': return 'file-document';
      default: return 'file';
    }
  };

  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'photos': return '#4CAF50';
      case 'contacts': return '#2196F3';
      case 'messages': return '#FF9800';
      case 'documents': return '#9C27B0';
      default: return COLORS.primary;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Choose Data to Restore',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        leftComponent={{
          icon: 'arrow-back',
          color: '#fff',
          onPress: () => navigation.goBack()
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        {/* Select All Option */}
        <Card style={styles.selectAllCard}>
          <Card.Content>
            <View style={styles.selectAllRow}>
              <View style={styles.selectAllInfo}>
                <Icon name="select-all" size={24} color={COLORS.primary} />
                <Text variant="titleMedium" style={styles.selectAllText}>
                  Select All Categories
                </Text>
              </View>
              <Switch
                value={restoreOptions.selectAll}
                onValueChange={toggleSelectAll}
                color={COLORS.primary}
              />
            </View>
            <Text style={styles.selectAllDescription}>
              Restore all available data types with recommended settings
            </Text>
          </Card.Content>
        </Card>

        {/* Category Selection */}
        <Text variant="titleLarge" style={styles.sectionTitle}>
          Data Categories
        </Text>

        {/* Photos */}
        <Card style={styles.categoryCard}>
          <Card.Content>
            <View style={styles.categoryHeader}>
              <View style={styles.categoryInfo}>
                <Icon name={getCategoryIcon('photos')} size={24} color={getCategoryColor('photos')} />
                <Text variant="titleMedium" style={styles.categoryName}>Photos</Text>
              </View>
              <Switch
                value={restoreOptions.categories.photos.selected}
                onValueChange={(value) => updateCategorySelection('photos', value)}
                color={COLORS.primary}
              />
            </View>

            {restoreOptions.categories.photos.selected && (
              <View style={styles.categoryOptions}>
                <Divider style={styles.divider} />
                <Text style={styles.optionTitle}>Download Options:</Text>
                
                <View style={styles.radioGroup}>
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="wifi"
                      status={restoreOptions.categories.photos.downloadWifi ? 'checked' : 'unchecked'}
                      onPress={() => {
                        updateCategoryOption('photos', 'downloadWifi', true);
                        updateCategoryOption('photos', 'downloadCellular', false);
                      }}
                      color={COLORS.primary}
                    />
                    <Text style={styles.radioLabel}>Wi-Fi Only (Recommended)</Text>
                  </View>
                  
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="cellular"
                      status={restoreOptions.categories.photos.downloadCellular ? 'checked' : 'unchecked'}
                      onPress={() => {
                        updateCategoryOption('photos', 'downloadWifi', false);
                        updateCategoryOption('photos', 'downloadCellular', true);
                      }}
                      color={COLORS.primary}
                    />
                    <Text style={styles.radioLabel}>Wi-Fi + Cellular</Text>
                  </View>
                </View>

                <View style={styles.warningBox}>
                  <Icon name="alert" size={16} color={COLORS.warning} />
                  <Text style={styles.warningText}>
                    Photos may use significant data if downloaded over cellular
                  </Text>
                </View>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Contacts */}
        <Card style={styles.categoryCard}>
          <Card.Content>
            <View style={styles.categoryHeader}>
              <View style={styles.categoryInfo}>
                <Icon name={getCategoryIcon('contacts')} size={24} color={getCategoryColor('contacts')} />
                <Text variant="titleMedium" style={styles.categoryName}>Contacts</Text>
              </View>
              <Switch
                value={restoreOptions.categories.contacts.selected}
                onValueChange={(value) => updateCategorySelection('contacts', value)}
                color={COLORS.primary}
              />
            </View>

            {restoreOptions.categories.contacts.selected && (
              <View style={styles.categoryOptions}>
                <Divider style={styles.divider} />
                <Text style={styles.optionTitle}>Merge Strategy:</Text>
                
                <View style={styles.radioGroup}>
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="smart"
                      status={restoreOptions.categories.contacts.mergeStrategy === 'smart' ? 'checked' : 'unchecked'}
                      onPress={() => updateCategoryOption('contacts', 'mergeStrategy', 'smart')}
                      color={COLORS.primary}
                    />
                    <Text style={styles.radioLabel}>Smart Merge (Recommended)</Text>
                  </View>
                  
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="replace"
                      status={restoreOptions.categories.contacts.mergeStrategy === 'replace' ? 'checked' : 'unchecked'}
                      onPress={() => updateCategoryOption('contacts', 'mergeStrategy', 'replace')}
                      color={COLORS.primary}
                    />
                    <Text style={styles.radioLabel}>Replace All Contacts</Text>
                  </View>
                  
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="merge"
                      status={restoreOptions.categories.contacts.mergeStrategy === 'merge' ? 'checked' : 'unchecked'}
                      onPress={() => updateCategoryOption('contacts', 'mergeStrategy', 'merge')}
                      color={COLORS.primary}
                    />
                    <Text style={styles.radioLabel}>Add to Existing Contacts</Text>
                  </View>
                </View>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Messages */}
        <Card style={styles.categoryCard}>
          <Card.Content>
            <View style={styles.categoryHeader}>
              <View style={styles.categoryInfo}>
                <Icon name={getCategoryIcon('messages')} size={24} color={getCategoryColor('messages')} />
                <Text variant="titleMedium" style={styles.categoryName}>Messages</Text>
              </View>
              <Switch
                value={restoreOptions.categories.messages.selected}
                onValueChange={(value) => updateCategorySelection('messages', value)}
                color={COLORS.primary}
              />
            </View>

            {restoreOptions.categories.messages.selected && (
              <View style={styles.categoryOptions}>
                <Divider style={styles.divider} />
                <Text style={styles.optionTitle}>Format:</Text>
                
                <View style={styles.radioGroup}>
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="native"
                      status={restoreOptions.categories.messages.format === 'native' ? 'checked' : 'unchecked'}
                      onPress={() => updateCategoryOption('messages', 'format', 'native')}
                      color={COLORS.primary}
                    />
                    <Text style={styles.radioLabel}>Native Format (Recommended)</Text>
                  </View>
                  
                  <View style={styles.radioOption}>
                    <RadioButton
                      value="export"
                      status={restoreOptions.categories.messages.format === 'export' ? 'checked' : 'unchecked'}
                      onPress={() => updateCategoryOption('messages', 'format', 'export')}
                      color={COLORS.primary}
                    />
                    <Text style={styles.radioLabel}>Export Format</Text>
                  </View>
                </View>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Documents */}
        <Card style={styles.categoryCard}>
          <Card.Content>
            <View style={styles.categoryHeader}>
              <View style={styles.categoryInfo}>
                <Icon name={getCategoryIcon('documents')} size={24} color={getCategoryColor('documents')} />
                <Text variant="titleMedium" style={styles.categoryName}>Documents</Text>
              </View>
              <Switch
                value={restoreOptions.categories.documents.selected}
                onValueChange={(value) => updateCategorySelection('documents', value)}
                color={COLORS.primary}
              />
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <Card style={styles.actionCard}>
          <Card.Content>
            <View style={styles.selectionSummary}>
              <Text style={styles.summaryText}>
                {getSelectedCount()} of 4 categories selected
              </Text>
              {getSelectedCount() > 0 && (
                <Chip style={styles.readyChip}>
                  <Text style={styles.readyText}>Ready to Restore</Text>
                </Chip>
              )}
            </View>

            <Button
              mode="contained"
              onPress={handleStartRestore}
              disabled={getSelectedCount() === 0}
              style={[styles.startButton, getSelectedCount() === 0 && styles.disabledButton]}
              icon="restore"
              contentStyle={styles.buttonContent}
            >
              Start Data Restore
            </Button>

            <View style={styles.infoBox}>
              <Icon name="information" size={16} color={COLORS.primary} />
              <Text style={styles.infoText}>
                Your data will be decrypted securely on this device. 
                You can pause or cancel the restore process at any time.
              </Text>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  selectAllCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  selectAllRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  selectAllInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  selectAllDescription: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  sectionTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  categoryCard: {
    marginBottom: SPACING.md,
    elevation: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryName: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  categoryOptions: {
    marginTop: SPACING.md,
  },
  divider: {
    marginBottom: SPACING.md,
  },
  optionTitle: {
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  radioGroup: {
    gap: SPACING.xs,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioLabel: {
    marginLeft: SPACING.xs,
    color: COLORS.text,
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    padding: SPACING.sm,
    borderRadius: 6,
    marginTop: SPACING.sm,
  },
  warningText: {
    marginLeft: SPACING.xs,
    color: COLORS.warning,
    fontSize: 12,
    flex: 1,
  },
  actionCard: {
    marginTop: SPACING.md,
    elevation: 4,
  },
  selectionSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  summaryText: {
    color: COLORS.textSecondary,
  },
  readyChip: {
    backgroundColor: COLORS.success,
  },
  readyText: {
    color: 'white',
    fontSize: 12,
  },
  startButton: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.md,
  },
  disabledButton: {
    backgroundColor: COLORS.textSecondary,
  },
  buttonContent: {
    paddingVertical: SPACING.xs,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: COLORS.background,
    padding: SPACING.sm,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.primary,
  },
  infoText: {
    marginLeft: SPACING.xs,
    color: COLORS.textSecondary,
    fontSize: 12,
    lineHeight: 16,
    flex: 1,
  },
});

export default RestoreSelectionScreen;
