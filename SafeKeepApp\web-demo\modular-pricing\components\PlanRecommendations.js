/**
 * PlanRecommendations Component
 * Displays plan options with "Most Popular" badges
 */

class PlanRecommendations {
    constructor(props) {
        this.props = props;
        this.element = null;
        this.planCards = new Map();
        this.render();
    }

    render() {
        const { selectedServices, highlightPopular } = this.props;
        
        this.element = window.ModularPricingUtils.createElement('div', {
            className: 'plan-recommendations'
        });

        const header = this.createHeader();
        const plansContainer = this.createPlansContainer();

        this.element.appendChild(header);
        this.element.appendChild(plansContainer);
    }

    createHeader() {
        const header = window.ModularPricingUtils.createElement('div', {
            className: 'recommendations-header'
        });

        const title = window.ModularPricingUtils.createElement('h3', {
            className: 'recommendations-title'
        }, 'Recommended Plans');

        const subtitle = window.ModularPricingUtils.createElement('p', {
            className: 'recommendations-subtitle'
        }, 'Choose the perfect plan for your backup needs');

        header.appendChild(title);
        header.appendChild(subtitle);

        return header;
    }

    createPlansContainer() {
        const container = window.ModularPricingUtils.createElement('div', {
            className: 'plans-container'
        });

        const plans = this.getRelevantPlans();
        
        plans.forEach(plan => {
            const planCard = this.createPlanCard(plan);
            container.appendChild(planCard);
        });

        return container;
    }

    getRelevantPlans() {
        const { PRICING_PLANS } = window.ModularPricingConfig;
        const { selectedServices } = this.props;
        
        const plans = [];

        // Add individual plans if only one service selected
        if (selectedServices.length === 1) {
            const serviceId = selectedServices[0];
            const individualPlan = PRICING_PLANS.individual[serviceId];
            if (individualPlan) {
                plans.push({
                    id: `individual-${serviceId}`,
                    name: individualPlan.name,
                    price: individualPlan.price,
                    services: [serviceId],
                    isPopular: false,
                    type: 'individual'
                });
            }
        }

        // Add combination plans
        Object.entries(PRICING_PLANS.combinations).forEach(([planKey, plan]) => {
            const planServices = plan.services || [];
            const matchesSelection = selectedServices.length > 0 && 
                selectedServices.every(id => planServices.includes(id));
            
            plans.push({
                id: planKey,
                name: plan.name,
                price: plan.price,
                services: planServices,
                isPopular: plan.isPopular || false,
                savings: plan.savings || 0,
                type: 'combination',
                recommended: matchesSelection
            });
        });

        // Sort plans: recommended first, then by popularity, then by price
        return plans.sort((a, b) => {
            if (a.recommended && !b.recommended) return -1;
            if (!a.recommended && b.recommended) return 1;
            if (a.isPopular && !b.isPopular) return -1;
            if (!a.isPopular && b.isPopular) return 1;
            return a.price - b.price;
        });
    }

    createPlanCard(plan) {
        const { selectedServices, onPlanSelect } = this.props;
        const isCurrentSelection = this.isPlanCurrentSelection(plan);
        
        const card = window.ModularPricingUtils.createElement('div', {
            className: `plan-card ${plan.isPopular ? 'popular' : ''} ${isCurrentSelection ? 'current' : ''} ${plan.recommended ? 'recommended' : ''}`,
            dataset: { planId: plan.id }
        });

        // Popular badge
        if (plan.isPopular) {
            const badge = window.ModularPricingUtils.createElement('div', {
                className: 'popular-badge'
            }, 'Most Popular');
            card.appendChild(badge);
        }

        // Recommended badge
        if (plan.recommended) {
            const badge = window.ModularPricingUtils.createElement('div', {
                className: 'recommended-badge'
            }, 'Matches Your Selection');
            card.appendChild(badge);
        }

        // Plan header
        const header = window.ModularPricingUtils.createElement('div', {
            className: 'plan-header'
        });

        const planName = window.ModularPricingUtils.createElement('h4', {
            className: 'plan-name'
        }, plan.name);

        const planPrice = window.ModularPricingUtils.createElement('div', {
            className: 'plan-price'
        });

        const priceAmount = window.ModularPricingUtils.createElement('span', {
            className: 'price-amount'
        }, window.ModularPricingUtils.formatPrice(plan.price));

        const priceFrequency = window.ModularPricingUtils.createElement('span', {
            className: 'price-frequency'
        }, '/month');

        planPrice.appendChild(priceAmount);
        planPrice.appendChild(priceFrequency);

        header.appendChild(planName);
        header.appendChild(planPrice);

        // Savings indicator
        if (plan.savings > 0) {
            const savings = window.ModularPricingUtils.createElement('div', {
                className: 'plan-savings'
            }, `Save ${window.ModularPricingUtils.formatPrice(plan.savings)}`);
            header.appendChild(savings);
        }

        // Services included
        const servicesSection = this.createServicesSection(plan.services);

        // Action button
        const actionButton = window.ModularPricingUtils.createElement('button', {
            className: `plan-action-btn ${isCurrentSelection ? 'current' : 'select'}`,
            type: 'button'
        }, isCurrentSelection ? 'Current Selection' : 'Select Plan');

        if (!isCurrentSelection && onPlanSelect) {
            actionButton.addEventListener('click', () => {
                onPlanSelect(plan.id, plan.services);
            });
        }

        if (isCurrentSelection) {
            actionButton.disabled = true;
        }

        card.appendChild(header);
        card.appendChild(servicesSection);
        card.appendChild(actionButton);

        // Store reference
        this.planCards.set(plan.id, card);

        return card;
    }

    createServicesSection(serviceIds) {
        const { SERVICE_CONFIG } = window.ModularPricingConfig;
        
        const section = window.ModularPricingUtils.createElement('div', {
            className: 'plan-services'
        });

        const title = window.ModularPricingUtils.createElement('h5', {
            className: 'services-title'
        }, 'Includes:');

        const servicesList = window.ModularPricingUtils.createElement('ul', {
            className: 'services-list'
        });

        serviceIds.forEach(serviceId => {
            const service = SERVICE_CONFIG[serviceId];
            if (service) {
                const listItem = window.ModularPricingUtils.createElement('li', {
                    className: 'service-item'
                });

                const serviceIcon = window.ModularPricingUtils.createElement('span', {
                    className: 'service-icon'
                }, service.icon);

                const serviceName = window.ModularPricingUtils.createElement('span', {
                    className: 'service-name'
                }, service.name);

                const checkIcon = window.ModularPricingUtils.createElement('span', {
                    className: 'check-icon'
                }, '✓');

                listItem.appendChild(checkIcon);
                listItem.appendChild(serviceIcon);
                listItem.appendChild(serviceName);
                servicesList.appendChild(listItem);
            }
        });

        section.appendChild(title);
        section.appendChild(servicesList);

        return section;
    }

    isPlanCurrentSelection(plan) {
        const { selectedServices } = this.props;
        
        if (selectedServices.length === 0) return false;
        
        const planServices = plan.services || [];
        
        return selectedServices.length === planServices.length &&
               selectedServices.every(id => planServices.includes(id));
    }

    updateProps(newProps) {
        const oldProps = this.props;
        this.props = { ...this.props, ...newProps };

        // Check if selected services changed
        if (JSON.stringify(oldProps.selectedServices) !== JSON.stringify(this.props.selectedServices)) {
            // Re-render the component
            this.element.innerHTML = '';
            this.planCards.clear();
            
            const header = this.createHeader();
            const plansContainer = this.createPlansContainer();

            this.element.appendChild(header);
            this.element.appendChild(plansContainer);
        }
    }

    highlightPlan(planId) {
        const planCard = this.planCards.get(planId);
        if (planCard) {
            window.ModularPricingUtils.addAnimatedClass(planCard, 'plan-highlight', 1000);
        }
    }

    getRecommendedPlan() {
        const plans = this.getRelevantPlans();
        return plans.find(plan => plan.recommended) || plans.find(plan => plan.isPopular) || plans[0];
    }

    getElement() {
        return this.element;
    }

    destroy() {
        this.planCards.clear();
        
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        this.element = null;
    }
}

if (typeof window !== 'undefined') {
    window.PlanRecommendations = PlanRecommendations;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = PlanRecommendations;
}