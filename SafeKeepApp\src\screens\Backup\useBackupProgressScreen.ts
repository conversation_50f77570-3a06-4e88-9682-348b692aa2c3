import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { cancelBackupSession, updateDataTypeProgress } from '../../store/slices/backupSlice';

export function useBackupProgressScreen() {
  const dispatch = useDispatch();
  const realTimeProgress = useSelector((state: RootState) => state.backup.realTimeProgress);
  const isBackupInProgress = useSelector((state: RootState) => state.backup.isBackupInProgress);

  // Map Redux progress to UI items
  const progressItems = [
    {
      label: 'Contacts',
      progress: realTimeProgress.contacts.total > 0 ? realTimeProgress.contacts.completed / realTimeProgress.contacts.total : 0,
      currentItem: undefined, // You can wire this up if you track current item in state
      estimatedTime: undefined, // You can wire this up if you track ETA in state
      error: realTimeProgress.contacts.errors[0]?.message,
      retry: realTimeProgress.contacts.errors[0]?.retryable
        ? () => dispatch(updateDataTypeProgress({ dataType: 'contacts', progress: { status: 'in_progress', errors: [] } }))
        : undefined,
    },
    {
      label: 'Messages',
      progress: realTimeProgress.messages.total > 0 ? realTimeProgress.messages.completed / realTimeProgress.messages.total : 0,
      currentItem: undefined,
      estimatedTime: undefined,
      error: realTimeProgress.messages.errors[0]?.message,
      retry: realTimeProgress.messages.errors[0]?.retryable
        ? () => dispatch(updateDataTypeProgress({ dataType: 'messages', progress: { status: 'in_progress', errors: [] } }))
        : undefined,
    },
    {
      label: 'Photos',
      progress: realTimeProgress.photos.total > 0 ? realTimeProgress.photos.completed / realTimeProgress.photos.total : 0,
      currentItem: undefined,
      estimatedTime: undefined,
      error: realTimeProgress.photos.errors[0]?.message,
      retry: realTimeProgress.photos.errors[0]?.retryable
        ? () => dispatch(updateDataTypeProgress({ dataType: 'photos', progress: { status: 'in_progress', errors: [] } }))
        : undefined,
    },
  ];

  const handleCancel = async () => {
    dispatch(cancelBackupSession());
  };

  return {
    progressItems,
    handleCancel,
    isBackupInProgress,
  };
}
