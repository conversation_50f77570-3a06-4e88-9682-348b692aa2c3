import { PricingResult, ServiceCombination, PlanRecommendation } from '../types/modular-pricing';
import { supabase } from '../utils/database';

export class PricingEngine {
  /**
   * Calculate optimal pricing for given service combination
   * Uses database function to find the most cost-effective plan
   */
  async calculateOptimalPrice(serviceIds: string[]): Promise<PricingResult> {
    try {
      // Validate input
      if (!serviceIds || serviceIds.length === 0) {
        throw new Error('Service IDs are required');
      }

      // Call database function to calculate optimal pricing
      const { data, error } = await supabase.rpc('calculate_optimal_price', {
        service_ids: serviceIds
      });

      if (error) {
        console.error('Error calculating optimal price:', error);
        throw new Error(`Failed to calculate optimal price: ${error.message}`);
      }

      if (!data || data.length === 0) {
        throw new Error('No pricing data found for the selected services');
      }

      const result = data[0];
      
      return {
        recommendedPlanId: result.recommended_plan_id || '',
        recommendedPlanName: result.recommended_plan_name || '',
        priceCents: result.price_cents || 0,
        savingsCents: result.savings_cents || 0,
        individualTotalCents: result.individual_total_cents || 0
      };
    } catch (error) {
      console.error('PricingEngine.calculateOptimalPrice error:', error);
      throw error;
    }
  }

  /**
   * Get all available service combinations
   * Retrieves all active subscription plans with their service combinations
   */
  async getAvailableServiceCombinations(): Promise<ServiceCombination[]> {
    try {
      // Call database function to get available service combinations
      const { data, error } = await supabase.rpc('get_available_service_combinations');

      if (error) {
        console.error('Error getting service combinations:', error);
        throw new Error(`Failed to get service combinations: ${error.message}`);
      }

      if (!data) {
        return [];
      }

      // Transform database result to ServiceCombination interface
      return data.map((item: any) => ({
        planId: item.plan_id,
        planName: item.plan_name,
        priceCents: item.price_cents,
        services: item.services || [],
        storageGb: item.storage_gb || 0,
        isPopular: item.is_popular || false
      }));
    } catch (error) {
      console.error('PricingEngine.getAvailableServiceCombinations error:', error);
      throw error;
    }
  }

  /**
   * Get plan recommendations for a specific user
   * Suggests upgrades/downgrades based on usage patterns
   */
  async getPlanRecommendations(userId: string): Promise<PlanRecommendation[]> {
    try {
      // Validate input
      if (!userId) {
        throw new Error('User ID is required');
      }

      // Call database function to get plan recommendations
      const { data, error } = await supabase.rpc('get_plan_recommendations', {
        user_uuid: userId
      });

      if (error) {
        console.error('Error getting plan recommendations:', error);
        throw new Error(`Failed to get plan recommendations: ${error.message}`);
      }

      if (!data) {
        return [];
      }

      // Transform database result to PlanRecommendation interface
      return data.map((item: any) => ({
        planId: item.plan_id,
        planName: item.plan_name,
        priceCents: item.price_cents,
        reason: item.reason || '',
        savingsCents: Math.abs(item.price_difference_cents || 0)
      }));
    } catch (error) {
      console.error('PricingEngine.getPlanRecommendations error:', error);
      throw error;
    }
  }
}