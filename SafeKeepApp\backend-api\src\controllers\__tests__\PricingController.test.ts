import { Request, Response } from 'express';
import { PricingController } from '../PricingController';
import { PricingEngine } from '../../services/PricingEngine';

// Mock the PricingEngine
jest.mock('../../services/PricingEngine');

describe('PricingController', () => {
  let pricingController: PricingController;
  let mockPricingEngine: jest.Mocked<PricingEngine>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock response
    mockResponse = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    };

    // Create controller instance
    pricingController = new PricingController();
    
    // Get the mocked PricingEngine instance
    mockPricingEngine = (pricingController as any).pricingEngine;
  });

  describe('getServiceCombinations', () => {
    beforeEach(() => {
      mockRequest = {};
    });

    it('should return service combinations successfully', async () => {
      const mockCombinations = [
        {
          planId: 'basic',
          planName: 'Basic Plan',
          priceCents: 999,
          services: ['contacts'],
          storageGb: 5,
          isPopular: false
        }
      ];

      mockPricingEngine.getAvailableServiceCombinations.mockResolvedValue(mockCombinations);

      await pricingController.getServiceCombinations(mockRequest as Request, mockResponse as Response);

      expect(mockPricingEngine.getAvailableServiceCombinations).toHaveBeenCalledTimes(1);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockCombinations
      });
    });

    it('should handle errors when getting service combinations', async () => {
      const error = new Error('Database error');
      mockPricingEngine.getAvailableServiceCombinations.mockRejectedValue(error);

      await pricingController.getServiceCombinations(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'PRICING_ERROR',
          message: 'Failed to retrieve service combinations',
          details: { message: 'Database error' },
          timestamp: expect.any(String)
        }
      });
    });
  });

  describe('calculateOptimalPrice', () => {
    it('should calculate optimal price successfully', async () => {
      mockRequest = {
        body: {
          serviceIds: ['contacts', 'messages']
        }
      };

      const mockPricingResult = {
        recommendedPlanId: 'standard',
        recommendedPlanName: 'Standard Plan',
        priceCents: 1999,
        savingsCents: 500,
        individualTotalCents: 2499
      };

      mockPricingEngine.calculateOptimalPrice.mockResolvedValue(mockPricingResult);

      await pricingController.calculateOptimalPrice(mockRequest as Request, mockResponse as Response);

      expect(mockPricingEngine.calculateOptimalPrice).toHaveBeenCalledWith(['contacts', 'messages']);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockPricingResult
      });
    });

    it('should return 400 error when serviceIds is missing', async () => {
      mockRequest = { body: {} };

      await pricingController.calculateOptimalPrice(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds is empty array', async () => {
      mockRequest = {
        body: {
          serviceIds: []
        }
      };

      await pricingController.calculateOptimalPrice(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds contains invalid values', async () => {
      mockRequest = {
        body: {
          serviceIds: ['contacts', '', 'messages']
        }
      };

      await pricingController.calculateOptimalPrice(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'All service IDs must be non-empty strings',
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle errors when calculating optimal price', async () => {
      mockRequest = {
        body: {
          serviceIds: ['contacts', 'messages']
        }
      };

      const error = new Error('Calculation failed');
      mockPricingEngine.calculateOptimalPrice.mockRejectedValue(error);

      await pricingController.calculateOptimalPrice(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'PRICING_CALCULATION_ERROR',
          message: 'Failed to calculate optimal pricing',
          details: { message: 'Calculation failed' },
          timestamp: expect.any(String)
        }
      });
    });
  });

  describe('getPlanRecommendations', () => {
    it('should get plan recommendations successfully', async () => {
      mockRequest = {
        params: {
          userId: 'user-123'
        }
      };

      const mockRecommendations = [
        {
          planId: 'premium',
          planName: 'Premium Plan',
          priceCents: 2999,
          reason: 'Based on your usage patterns',
          savingsCents: 1000
        }
      ];

      mockPricingEngine.getPlanRecommendations.mockResolvedValue(mockRecommendations);

      await pricingController.getPlanRecommendations(mockRequest as Request, mockResponse as Response);

      expect(mockPricingEngine.getPlanRecommendations).toHaveBeenCalledWith('user-123');
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockRecommendations
      });
    });

    it('should return 400 error when userId is missing', async () => {
      mockRequest = { params: {} };

      await pricingController.getPlanRecommendations(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when userId is empty string', async () => {
      mockRequest = {
        params: {
          userId: '   '
        }
      };

      await pricingController.getPlanRecommendations(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle errors when getting plan recommendations', async () => {
      mockRequest = {
        params: {
          userId: 'user-123'
        }
      };

      const error = new Error('Database connection failed');
      mockPricingEngine.getPlanRecommendations.mockRejectedValue(error);

      await pricingController.getPlanRecommendations(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'RECOMMENDATION_ERROR',
          message: 'Failed to retrieve plan recommendations',
          details: { message: 'Database connection failed' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should trim whitespace from userId', async () => {
      mockRequest = {
        params: {
          userId: '  user-123  '
        }
      };

      const mockRecommendations: any[] = [];
      mockPricingEngine.getPlanRecommendations.mockResolvedValue(mockRecommendations);

      await pricingController.getPlanRecommendations(mockRequest as Request, mockResponse as Response);

      expect(mockPricingEngine.getPlanRecommendations).toHaveBeenCalledWith('user-123');
    });
  });
});