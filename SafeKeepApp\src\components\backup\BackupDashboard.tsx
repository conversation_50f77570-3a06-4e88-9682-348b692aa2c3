import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Card,
  Text,
  Button,
  ProgressBar,
  Chip,
  IconButton,
  Surface,
  Divider,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import {
  useBackupState,
  useBackupActions,
  useOverallBackupProgress,
  useLastSuccessfulBackup,
  useRecentBackupSessions,
  useFormattedBackupStatistics,
  useIsBackupInProgress,
} from '../../store/hooks/backupHooks';
import { COLORS, SPACING } from '../../utils/constants';
import BackupManager from '../../services/BackupManager';
import { BackupConfiguration } from '../../types/backup';

interface BackupDashboardProps {
  onNavigateToProgress?: () => void;
  onNavigateToSettings?: () => void;
  onNavigateToHistory?: () => void;
}

const BackupDashboard: React.FC<BackupDashboardProps> = ({
  onNavigateToProgress,
  onNavigateToSettings,
  onNavigateToHistory,
}) => {
  const [refreshing, setRefreshing] = useState(false);
  const [isStartingBackup, setIsStartingBackup] = useState(false);

  // Redux state
  const backupState = useBackupState();
  const backupActions = useBackupActions();
  const overallProgress = useOverallBackupProgress();
  const lastSuccessfulBackup = useLastSuccessfulBackup();
  const recentSessions = useRecentBackupSessions(5);
  const statistics = useFormattedBackupStatistics();
  const isBackupInProgress = useIsBackupInProgress();

  // Load backup history on component mount
  useEffect(() => {
    backupActions.loadHistory();
    backupActions.calculateStats();
  }, []);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await backupActions.loadHistory();
      backupActions.calculateStats();
    } finally {
      setRefreshing(false);
    }
  };

  // Start manual backup
  const handleStartBackup = async () => {
    if (isBackupInProgress) {
      Alert.alert(
        'Backup in Progress',
        'A backup is already running. Please wait for it to complete or cancel it first.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsStartingBackup(true);

    try {
      const config: BackupConfiguration = {
        ...backupState.configuration,
        autoBackup: false, // Manual backup
      };

      // Start backup with progress tracking
      const result = await BackupManager.startBackup(config, (progress) => {
        // Update Redux state with real-time progress
        backupActions.updateSession(progress.session);

        // Update individual data type progress
        Object.entries(progress.session.progress).forEach(([dataType, progressData]) => {
          // Convert progressData to the expected BackupProgress structure
          backupActions.updateProgress(dataType as any, {
            total_count: progressData.total || 0,
            completed_count: progressData.completed || 0,
            failed_count: progressData.failed || 0,
            session_id: progress.session.id,
            item_type: dataType as 'contact' | 'message' | 'photo',
            last_updated: new Date().toISOString()
          });
        });
      });

      if (result.success) {
        Alert.alert(
          'Backup Complete',
          `Successfully backed up ${result.itemsProcessed} items.`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Backup Failed',
          `Backup completed with ${result.errors.length} errors. ${result.itemsProcessed} items were processed.`,
          [
            { text: 'View Details', onPress: onNavigateToProgress },
            { text: 'OK' }
          ]
        );
      }

      // Refresh statistics
      backupActions.calculateStats();

    } catch (error) {
      console.error('Failed to start backup:', error);
      Alert.alert(
        'Backup Error',
        'Failed to start backup. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsStartingBackup(false);
    }
  };

  // Cancel current backup
  const handleCancelBackup = () => {
    Alert.alert(
      'Cancel Backup',
      'Are you sure you want to cancel the current backup?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes',
          style: 'destructive',
          onPress: async () => {
            await BackupManager.cancelBackup();
            backupActions.cancelSession();
          }
        }
      ]
    );
  };

  // Get status color based on backup state
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return COLORS.success;
      case 'failed':
        return COLORS.error;
      case 'in_progress':
        return COLORS.primary;
      case 'cancelled':
        return COLORS.warning;
      default:
        return COLORS.textSecondary;
    }
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp: string | Date | undefined) => {
    if (!timestamp) return 'Never';

    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Current Backup Status */}
      <Card style={styles.statusCard}>
        <Card.Content>
          <View style={styles.statusHeader}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Backup Status
            </Text>
            <IconButton
              icon="cog"
              size={20}
              onPress={onNavigateToSettings}
            />
          </View>

          {isBackupInProgress ? (
            <View style={styles.progressSection}>
              <View style={styles.progressHeader}>
                <Text variant="bodyMedium">
                  Backup in progress... {overallProgress.percentage}%
                </Text>
                <View style={styles.progressActions}>
                  {onNavigateToProgress && (
                    <Button
                      mode="text"
                      compact
                      onPress={onNavigateToProgress}
                    >
                      View Details
                    </Button>
                  )}
                  <Button
                    mode="outlined"
                    compact
                    onPress={handleCancelBackup}
                    textColor={COLORS.error}
                  >
                    Cancel
                  </Button>
                </View>
              </View>

              <ProgressBar
                progress={overallProgress.percentage / 100}
                color={COLORS.primary}
                style={styles.progressBar}
              />

              <Text variant="bodySmall" style={styles.progressText}>
                {overallProgress.completedItems} of {overallProgress.totalItems} items
              </Text>

              {/* Individual data type progress */}
              <View style={styles.dataTypeProgress}>
                {Object.entries(backupState.realTimeProgress).map(([dataType, progress]) => (
                  <View key={dataType} style={styles.dataTypeItem}>
                    <View style={styles.dataTypeHeader}>
                      <MaterialCommunityIcons
                        name={
                          dataType === 'contacts' ? 'contacts' :
                            dataType === 'messages' ? 'message-text' : 'camera'
                        }
                        size={16}
                        color={COLORS.textSecondary}
                      />
                      <Text variant="bodySmall" style={styles.dataTypeLabel}>
                        {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
                      </Text>
                      <Chip
                        mode="outlined"
                        compact
                        textStyle={styles.chipText}
                        style={[styles.statusChip, { borderColor: COLORS.primary }]}
                      >
                        pending
                      </Chip>
                    </View>
                    <ProgressBar
                      progress={progress.total_count > 0 ? progress.completed_count / progress.total_count : 0}
                      color={COLORS.primary}
                      style={styles.dataTypeProgressBar}
                    />
                    <Text variant="bodySmall" style={styles.dataTypeProgressText}>
                      {progress.completed_count} / {progress.total_count}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          ) : (
            <View style={styles.idleSection}>
              <View style={styles.lastBackupInfo}>
                <MaterialCommunityIcons
                  name="backup-restore"
                  size={24}
                  color={COLORS.textSecondary}
                />
                <View style={styles.lastBackupText}>
                  <Text variant="bodyMedium">Last backup</Text>
                  <Text variant="bodySmall" style={styles.lastBackupTime}>
                    {formatTimestamp(lastSuccessfulBackup)}
                  </Text>
                </View>
              </View>

              <Button
                mode="contained"
                onPress={handleStartBackup}
                loading={isStartingBackup}
                disabled={isStartingBackup}
                style={styles.backupButton}
                contentStyle={styles.backupButtonContent}
              >
                Start Backup
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Backup Statistics */}
      <Card style={styles.statisticsCard}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Statistics
          </Text>

          <View style={styles.statsGrid}>
            <Surface style={styles.statItem}>
              <Text variant="headlineSmall" style={styles.statValue}>
                {statistics.totalBackups}
              </Text>
              <Text variant="bodySmall" style={styles.statLabel}>
                Total Backups
              </Text>
            </Surface>

            <Surface style={styles.statItem}>
              <Text variant="headlineSmall" style={styles.statValue}>
                {statistics.totalItems.toLocaleString()}
              </Text>
              <Text variant="bodySmall" style={styles.statLabel}>
                Items Backed Up
              </Text>
            </Surface>

            <Surface style={styles.statItem}>
              <Text variant="headlineSmall" style={styles.statValue}>
                {statistics.formattedDataSize}
              </Text>
              <Text variant="bodySmall" style={styles.statLabel}>
                Data Size
              </Text>
            </Surface>

            <Surface style={styles.statItem}>
              <Text variant="headlineSmall" style={styles.statValue}>
                {statistics.formattedSuccessRate}
              </Text>
              <Text variant="bodySmall" style={styles.statLabel}>
                Success Rate
              </Text>
            </Surface>
          </View>

          {/* Data type breakdown */}
          <Divider style={styles.divider} />

          <Text variant="titleSmall" style={styles.breakdownTitle}>
            Data Breakdown
          </Text>

          <View style={styles.dataBreakdown}>
            {Object.entries(statistics.dataTypeBreakdown).map(([dataType, data]) => (
              <View key={dataType} style={styles.breakdownItem}>
                <View style={styles.breakdownHeader}>
                  <MaterialCommunityIcons
                    name={
                      dataType === 'contacts' ? 'contacts' :
                        dataType === 'messages' ? 'message-text' : 'camera'
                    }
                    size={16}
                    color={COLORS.primary}
                  />
                  <Text variant="bodyMedium" style={styles.breakdownLabel}>
                    {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
                  </Text>
                </View>
                <View style={styles.breakdownStats}>
                  <Text variant="bodySmall" style={styles.breakdownCount}>
                    {data.count.toLocaleString()} items
                  </Text>
                  <Text variant="bodySmall" style={styles.breakdownSize}>
                    {data.formattedSize}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </Card.Content>
      </Card>

      {/* Recent Backup History */}
      <Card style={styles.historyCard}>
        <Card.Content>
          <View style={styles.historyHeader}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Recent Backups
            </Text>
            {onNavigateToHistory ? (
              <Button
                mode="text"
                compact
                onPress={onNavigateToHistory}
              >
                View All
              </Button>
            ) : onNavigateToProgress && (
              <Button
                mode="text"
                compact
                onPress={onNavigateToProgress}
              >
                View Details
              </Button>
            )}
          </View>

          {recentSessions.length === 0 ? (
            <View style={styles.emptyState}>
              <MaterialCommunityIcons
                name="backup-restore"
                size={48}
                color={COLORS.textSecondary}
              />
              <Text variant="bodyMedium" style={styles.emptyStateText}>
                No backup history yet
              </Text>
              <Text variant="bodySmall" style={styles.emptyStateSubtext}>
                Start your first backup to see history here
              </Text>
            </View>
          ) : (
            <View style={styles.historyList}>
              {recentSessions.map((session, index) => (
                <View key={session.id} style={styles.historyItem}>
                  <View style={styles.historyItemHeader}>
                    <View style={styles.historyItemInfo}>
                      <Text variant="bodyMedium">
                        {formatTimestamp(session.start_time)}
                      </Text>
                      <Chip
                        mode="outlined"
                        compact
                        textStyle={styles.chipText}
                        style={[
                          styles.statusChip,
                          { borderColor: getStatusColor(session.status) }
                        ]}
                      >
                        {session.status}
                      </Chip>
                    </View>
                    <Text variant="bodySmall" style={styles.historyItemCount}>
                      {session.completed_items} items
                    </Text>
                  </View>

                  {/* Mini progress indicators */}
                  <View style={styles.miniProgressContainer}>
                    {session.configuration.includeContacts && (
                      <View style={styles.miniProgressItem}>
                        <MaterialCommunityIcons
                          name="contacts"
                          size={12}
                          color={COLORS.textSecondary}
                        />
                        <Text variant="bodySmall" style={styles.miniProgressText}>
                          Contacts
                        </Text>
                      </View>
                    )}
                    {session.configuration.includeMessages && (
                      <View style={styles.miniProgressItem}>
                        <MaterialCommunityIcons
                          name="message-text"
                          size={12}
                          color={COLORS.textSecondary}
                        />
                        <Text variant="bodySmall" style={styles.miniProgressText}>
                          Messages
                        </Text>
                      </View>
                    )}
                    {session.configuration.includePhotos && (
                      <View style={styles.miniProgressItem}>
                        <MaterialCommunityIcons
                          name="camera"
                          size={12}
                          color={COLORS.textSecondary}
                        />
                        <Text variant="bodySmall" style={styles.miniProgressText}>
                          Photos
                        </Text>
                      </View>
                    )}
                  </View>

                  {index < recentSessions.length - 1 && (
                    <Divider style={styles.historyDivider} />
                  )}
                </View>
              ))}
            </View>
          )}
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  statusCard: {
    margin: SPACING.md,
    marginBottom: SPACING.sm,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: COLORS.text,
  },
  progressSection: {
    gap: SPACING.sm,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressActions: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressText: {
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  dataTypeProgress: {
    gap: SPACING.sm,
    marginTop: SPACING.sm,
  },
  dataTypeItem: {
    gap: SPACING.xs,
  },
  dataTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  dataTypeLabel: {
    flex: 1,
    color: COLORS.textSecondary,
  },
  dataTypeProgressBar: {
    height: 4,
    borderRadius: 2,
  },
  dataTypeProgressText: {
    color: COLORS.textSecondary,
    fontSize: 10,
  },
  statusChip: {
    height: 24,
  },
  chipText: {
    fontSize: 10,
  },
  idleSection: {
    alignItems: 'center',
    gap: SPACING.md,
  },
  lastBackupInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  lastBackupText: {
    alignItems: 'center',
  },
  lastBackupTime: {
    color: COLORS.textSecondary,
  },
  backupButton: {
    minWidth: 120,
  },
  backupButtonContent: {
    paddingVertical: SPACING.xs,
  },
  statisticsCard: {
    margin: SPACING.md,
    marginVertical: SPACING.sm,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
    marginTop: SPACING.sm,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    padding: SPACING.md,
    alignItems: 'center',
    borderRadius: 8,
    elevation: 1,
  },
  statValue: {
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statLabel: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  divider: {
    marginVertical: SPACING.md,
  },
  breakdownTitle: {
    marginBottom: SPACING.sm,
    fontWeight: 'bold',
  },
  dataBreakdown: {
    gap: SPACING.sm,
  },
  breakdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  breakdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  breakdownLabel: {
    color: COLORS.text,
  },
  breakdownStats: {
    alignItems: 'flex-end',
  },
  breakdownCount: {
    color: COLORS.text,
  },
  breakdownSize: {
    color: COLORS.textSecondary,
  },
  historyCard: {
    margin: SPACING.md,
    marginTop: SPACING.sm,
    marginBottom: SPACING.xl,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    gap: SPACING.sm,
  },
  emptyStateText: {
    color: COLORS.textSecondary,
  },
  emptyStateSubtext: {
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  historyList: {
    gap: SPACING.sm,
  },
  historyItem: {
    gap: SPACING.xs,
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  historyItemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  historyItemCount: {
    color: COLORS.textSecondary,
  },
  miniProgressContainer: {
    flexDirection: 'row',
    gap: SPACING.md,
    marginLeft: SPACING.sm,
  },
  miniProgressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  miniProgressText: {
    color: COLORS.textSecondary,
    fontSize: 10,
  },
  historyDivider: {
    marginTop: SPACING.sm,
  },
});

export default BackupDashboard;