/**
 * Test Suite for Payment Processing Interface
 * Tests payment processing, billing, and subscription flows
 */

class PaymentInterfaceTester {
    constructor() {
        this.testResults = [];
        this.setupTestEnvironment();
    }

    /**
     * Setup test environment
     */
    setupTestEnvironment() {
        // Initialize components
        this.stripeManager = new StripeManager();
        this.subscriptionManager = new SubscriptionManager(this.stripeManager);
        this.paymentProcessor = new PaymentProcessor(this.stripeManager, this.subscriptionManager);
        this.paymentMethodsManager = new PaymentMethodsManager(this.stripeManager, this.paymentProcessor);
        this.subscriptionFlowManager = new SubscriptionFlowManager(
            this.subscriptionManager, 
            this.paymentProcessor, 
            this.paymentMethodsManager
        );
        this.taxCalculator = new TaxCalculator();
        
        console.log('🧪 Payment Interface test environment initialized');
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting Payment Interface Tests...');
        
        try {
            await this.testPaymentProcessor();
            await this.testTaxCalculation();
            await this.testPaymentMethodsManager();
            await this.testSubscriptionFlows();
            await this.testBillingAddressValidation();
            await this.testPaymentSecurity();
            
            this.displayTestResults();
        } catch (error) {
            console.error('❌ Payment Interface test suite failed:', error);
        }
    }

    /**
     * Test payment processor
     */
    async testPaymentProcessor() {
        console.log('💳 Testing Payment Processor...');
        
        // Test initialization
        await this.stripeManager.initialize();
        await this.paymentProcessor.initialize();
        this.assert(this.paymentProcessor.stripe !== null, 'Payment processor should initialize Stripe');
        
        // Test payment intent creation
        const paymentIntentData = await this.paymentProcessor.createPaymentIntent({
            tierId: 'basic',
            billingAddress: {
                country: 'US',
                state: 'CA'
            }
        });
        
        this.assert(paymentIntentData.client_secret, 'Should create payment intent with client secret');
        this.assert(paymentIntentData.totalAmount > 0, 'Should calculate total amount including tax');
        this.assert(paymentIntentData.tier.id === 'basic', 'Should include tier information');
        
        // Test payment elements setup
        const container = document.createElement('div');
        container.id = 'test-payment-container';
        document.body.appendChild(container);
        
        this.paymentProcessor.mountPaymentElements('test-payment-container', false);
        this.assert(document.getElementById('card-element'), 'Should mount card element');
        this.assert(document.getElementById('payment-errors'), 'Should create error display');
        
        // Cleanup
        container.remove();
        
        console.log('✅ Payment Processor tests passed');
    }

    /**
     * Test tax calculation
     */
    async testTaxCalculation() {
        console.log('🧾 Testing Tax Calculation...');
        
        // Test US tax calculation
        const usTax = await this.taxCalculator.calculateTax(1000, { country: 'US', state: 'CA' });
        this.assert(usTax === 73, 'Should calculate CA sales tax correctly'); // 7.25% of $10.00
        
        // Test international tax
        const ukTax = await this.taxCalculator.calculateTax(1000, { country: 'GB' });
        this.assert(ukTax === 200, 'Should calculate UK VAT correctly'); // 20% of $10.00
        
        // Test no tax country
        const noTax = await this.taxCalculator.calculateTax(1000, { country: 'XX' });
        this.assert(noTax === 0, 'Should return 0 for unknown countries');
        
        // Test tax info display
        const taxInfo = this.taxCalculator.getTaxInfo('US', 'CA');
        this.assert(taxInfo.rate === 0.0725, 'Should return correct tax rate');
        this.assert(taxInfo.description === 'CA Sales Tax', 'Should return correct description');
        this.assert(taxInfo.percentage === '7.25%', 'Should format percentage correctly');
        
        console.log('✅ Tax Calculation tests passed');
    }

    /**
     * Test payment methods manager
     */
    async testPaymentMethodsManager() {
        console.log('💳 Testing Payment Methods Manager...');
        
        // Test initialization
        await this.paymentMethodsManager.initialize('test_customer_123');
        this.assert(this.paymentMethodsManager.currentCustomerId === 'test_customer_123', 'Should set customer ID');
        
        // Test adding payment method
        const testPaymentMethod = {
            id: 'pm_test_123',
            type: 'card',
            card: {
                brand: 'visa',
                last4: '4242',
                exp_month: 12,
                exp_year: 2025
            },
            billing_details: {
                name: 'Test User',
                email: '<EMAIL>'
            }
        };
        
        await this.paymentMethodsManager.addPaymentMethod(testPaymentMethod, true);
        this.assert(this.paymentMethodsManager.paymentMethods.has('pm_test_123'), 'Should add payment method');
        this.assert(this.paymentMethodsManager.defaultPaymentMethod === 'pm_test_123', 'Should set as default');
        
        // Test getting payment methods
        const allMethods = this.paymentMethodsManager.getAllPaymentMethods();
        this.assert(allMethods.length === 1, 'Should return all payment methods');
        
        const defaultMethod = this.paymentMethodsManager.getDefaultPaymentMethod();
        this.assert(defaultMethod.id === 'pm_test_123', 'Should return default payment method');
        
        // Test removing payment method
        await this.paymentMethodsManager.removePaymentMethod('pm_test_123');
        this.assert(!this.paymentMethodsManager.paymentMethods.has('pm_test_123'), 'Should remove payment method');
        this.assert(this.paymentMethodsManager.defaultPaymentMethod === null, 'Should clear default when removed');
        
        console.log('✅ Payment Methods Manager tests passed');
    }

    /**
     * Test subscription flows
     */
    async testSubscriptionFlows() {
        console.log('🔄 Testing Subscription Flows...');
        
        // Initialize subscription manager
        await this.subscriptionManager.initialize('test_flow_user');
        await this.subscriptionFlowManager.initialize();
        
        // Test upgrade flow data setup
        await this.subscriptionFlowManager.startUpgradeFlow('basic');
        this.assert(this.subscriptionFlowManager.currentFlow === 'upgrade', 'Should start upgrade flow');
        this.assert(this.subscriptionFlowManager.flowData.targetTierId === 'basic', 'Should set target tier');
        this.assert(this.subscriptionFlowManager.flowData.currentStep === 1, 'Should start at step 1');
        
        // Test downgrade flow data setup
        await this.subscriptionFlowManager.startDowngradeFlow('free');
        this.assert(this.subscriptionFlowManager.currentFlow === 'downgrade', 'Should start downgrade flow');
        this.assert(this.subscriptionFlowManager.flowData.targetTierId === 'free', 'Should set target tier for downgrade');
        
        // Test flow cancellation
        this.subscriptionFlowManager.cancelFlow();
        this.assert(this.subscriptionFlowManager.currentFlow === null, 'Should cancel flow');
        this.assert(Object.keys(this.subscriptionFlowManager.flowData).length === 0, 'Should clear flow data');
        
        console.log('✅ Subscription Flows tests passed');
    }

    /**
     * Test billing address validation
     */
    async testBillingAddressValidation() {
        console.log('📍 Testing Billing Address Validation...');
        
        // Create test billing form
        const container = document.createElement('div');
        container.id = 'test-billing-container';
        document.body.appendChild(container);
        
        this.paymentProcessor.createBillingAddressForm('test-billing-container');
        
        // Test form creation
        this.assert(document.querySelector('.billing-address-form'), 'Should create billing address form');
        this.assert(document.getElementById('billing-name'), 'Should create name field');
        this.assert(document.getElementById('billing-email'), 'Should create email field');
        this.assert(document.getElementById('billing-country'), 'Should create country field');
        
        // Test validation with missing fields
        let isValid = this.paymentProcessor.validateBillingAddress();
        this.assert(isValid === false, 'Should fail validation with empty fields');
        
        // Fill in test data
        document.getElementById('billing-name').value = 'Test User';
        document.getElementById('billing-email').value = '<EMAIL>';
        document.getElementById('billing-address-line1').value = '123 Test St';
        document.getElementById('billing-city').value = 'Test City';
        document.getElementById('billing-postal-code').value = '12345';
        document.getElementById('billing-country').value = 'US';
        
        // Test validation with complete data
        isValid = this.paymentProcessor.validateBillingAddress();
        this.assert(isValid === true, 'Should pass validation with complete data');
        this.assert(this.paymentProcessor.billingAddress.name === 'Test User', 'Should store billing address');
        
        // Test invalid email
        document.getElementById('billing-email').value = 'invalid-email';
        isValid = this.paymentProcessor.validateBillingAddress();
        this.assert(isValid === false, 'Should fail validation with invalid email');
        
        // Cleanup
        container.remove();
        
        console.log('✅ Billing Address Validation tests passed');
    }

    /**
     * Test payment security
     */
    async testPaymentSecurity() {
        console.log('🔒 Testing Payment Security...');
        
        // Test that sensitive data is not stored
        const paymentIntentData = await this.paymentProcessor.createPaymentIntent({
            tierId: 'basic',
            billingAddress: { country: 'US' }
        });
        
        this.assert(!paymentIntentData.hasOwnProperty('card_number'), 'Should not expose card numbers');
        this.assert(!paymentIntentData.hasOwnProperty('cvv'), 'Should not expose CVV');
        this.assert(paymentIntentData.client_secret.includes('secret'), 'Should use secure client secret');
        
        // Test payment method tokenization
        const testMethod = {
            id: 'pm_secure_123',
            type: 'card',
            card: {
                brand: 'visa',
                last4: '4242', // Only last 4 digits
                exp_month: 12,
                exp_year: 2025
            }
        };
        
        await this.paymentMethodsManager.addPaymentMethod(testMethod);
        const storedMethod = this.paymentMethodsManager.paymentMethods.get('pm_secure_123');
        
        this.assert(storedMethod.card.last4 === '4242', 'Should store only last 4 digits');
        this.assert(!storedMethod.card.hasOwnProperty('number'), 'Should not store full card number');
        this.assert(!storedMethod.hasOwnProperty('cvv'), 'Should not store CVV');
        
        // Test secure transmission simulation
        const secureData = {
            payment_method_id: 'pm_secure_123',
            client_secret: paymentIntentData.client_secret
        };
        
        this.assert(typeof secureData.payment_method_id === 'string', 'Should use tokenized payment method ID');
        this.assert(secureData.client_secret.length > 20, 'Should use secure client secret');
        
        console.log('✅ Payment Security tests passed');
    }

    /**
     * Test payment flow integration
     */
    async testPaymentFlowIntegration() {
        console.log('🔗 Testing Payment Flow Integration...');
        
        // Initialize all components
        await this.stripeManager.initialize();
        await this.subscriptionManager.initialize('integration_test_user');
        await this.paymentProcessor.initialize();
        await this.paymentMethodsManager.initialize('integration_test_customer');
        
        // Test complete upgrade flow simulation
        const upgradeResult = await this.simulateUpgradeFlow();
        this.assert(upgradeResult.success, 'Should complete upgrade flow successfully');
        
        // Test payment method persistence
        const savedMethods = this.paymentMethodsManager.getAllPaymentMethods();
        this.assert(savedMethods.length > 0, 'Should persist payment methods');
        
        // Test subscription state update
        const currentSub = this.subscriptionManager.getCurrentSubscription();
        this.assert(currentSub.tierId !== 'free', 'Should update subscription tier');
        
        console.log('✅ Payment Flow Integration tests passed');
    }

    /**
     * Simulate upgrade flow for testing
     */
    async simulateUpgradeFlow() {
        try {
            // Start upgrade flow
            await this.subscriptionFlowManager.startUpgradeFlow('basic');
            
            // Simulate payment method selection (new method)
            this.subscriptionFlowManager.flowData.selectedPaymentMethod = 'new';
            
            // Simulate billing address
            this.paymentProcessor.billingAddress = {
                name: 'Test User',
                email: '<EMAIL>',
                line1: '123 Test St',
                city: 'Test City',
                state: 'CA',
                postal_code: '12345',
                country: 'US'
            };
            
            // Simulate successful payment processing
            const paymentResult = await this.paymentProcessor.processSubscriptionPayment('basic', this.paymentProcessor.billingAddress);
            
            return paymentResult;
        } catch (error) {
            console.error('Upgrade flow simulation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Test performance benchmarks
     */
    async testPerformance() {
        console.log('⚡ Testing Payment Interface Performance...');
        
        const iterations = 100;
        
        // Test tax calculation performance
        const taxStart = performance.now();
        for (let i = 0; i < iterations; i++) {
            await this.taxCalculator.calculateTax(1000, { country: 'US', state: 'CA' });
        }
        const taxEnd = performance.now();
        const avgTaxTime = (taxEnd - taxStart) / iterations;
        
        console.log(`📈 Tax calculation: ${avgTaxTime.toFixed(3)}ms per operation`);
        this.assert(avgTaxTime < 5, 'Tax calculation should be under 5ms per operation');
        
        // Test payment intent creation performance
        const intentStart = performance.now();
        for (let i = 0; i < 10; i++) { // Fewer iterations for heavier operations
            await this.paymentProcessor.createPaymentIntent({
                tierId: 'basic',
                billingAddress: { country: 'US' }
            });
        }
        const intentEnd = performance.now();
        const avgIntentTime = (intentEnd - intentStart) / 10;
        
        console.log(`📈 Payment intent creation: ${avgIntentTime.toFixed(3)}ms per operation`);
        this.assert(avgIntentTime < 100, 'Payment intent creation should be under 100ms per operation');
        
        console.log('✅ Performance tests passed');
    }

    /**
     * Assert helper
     */
    assert(condition, message) {
        const result = {
            passed: !!condition,
            message: message,
            timestamp: new Date()
        };
        
        this.testResults.push(result);
        
        if (!condition) {
            console.error(`❌ ASSERTION FAILED: ${message}`);
            throw new Error(`Assertion failed: ${message}`);
        } else {
            console.log(`✅ ${message}`);
        }
    }

    /**
     * Display test results
     */
    displayTestResults() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log('\n📊 PAYMENT INTERFACE TEST RESULTS');
        console.log('==================================');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${total - passed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        
        if (passed === total) {
            console.log('🎉 All payment interface tests passed!');
        } else {
            console.log('❌ Some payment interface tests failed');
            this.testResults.filter(r => !r.passed).forEach(result => {
                console.log(`  - ${result.message}`);
            });
        }
    }

    /**
     * Cleanup test environment
     */
    cleanup() {
        this.paymentProcessor.cleanup();
        this.paymentMethodsManager.cleanup();
        this.subscriptionFlowManager.cleanup();
        console.log('🧹 Payment Interface test environment cleaned up');
    }
}

// Auto-run tests when loaded
if (typeof window !== 'undefined') {
    window.PaymentInterfaceTester = PaymentInterfaceTester;
    
    // Auto-run tests in demo environment
    window.addEventListener('load', async () => {
        if (window.location.search.includes('test=payment')) {
            const tester = new PaymentInterfaceTester();
            await tester.runAllTests();
            await tester.testPerformance();
            tester.cleanup();
        }
    });
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PaymentInterfaceTester;
}