/**
 * Network Queue Manager
 * Handles queuing and retrying network operations during connectivity issues
 */

class NetworkQueueManager {
    constructor() {
        this.queue = [];
        this.isOnline = navigator.onLine;
        this.isProcessing = false;
        this.maxQueueSize = 100;
        this.maxRetries = 3;
        this.retryDelay = 1000;
        
        this.setupNetworkListeners();
        this.loadQueueFromStorage();
    }

    setupNetworkListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    }

    loadQueueFromStorage() {
        try {
            const savedQueue = localStorage.getItem('safekeep_network_queue');
            if (savedQueue) {
                this.queue = JSON.parse(savedQueue);
            }
        } catch (error) {
            console.warn('Failed to load network queue from storage:', error);
            this.queue = [];
        }
    }

    saveQueueToStorage() {
        try {
            localStorage.setItem('safekeep_network_queue', JSON.stringify(this.queue));
        } catch (error) {
            console.warn('Failed to save network queue to storage:', error);
        }
    }

    /**
     * Add operation to queue
     */
    enqueue(operation) {
        // Remove oldest items if queue is full
        if (this.queue.length >= this.maxQueueSize) {
            this.queue.shift();
        }

        const queueItem = {
            id: this.generateId(),
            timestamp: Date.now(),
            retries: 0,
            ...operation
        };

        this.queue.push(queueItem);
        this.saveQueueToStorage();

        // Try to process immediately if online
        if (this.isOnline) {
            this.processQueue();
        }

        return queueItem.id;
    }

    /**
     * Process queued operations
     */
    async processQueue() {
        if (this.isProcessing || !this.isOnline || this.queue.length === 0) {
            return;
        }

        this.isProcessing = true;

        try {
            const itemsToProcess = [...this.queue];
            
            for (let i = 0; i < itemsToProcess.length; i++) {
                const item = itemsToProcess[i];
                
                try {
                    await this.processQueueItem(item);
                    
                    // Remove successful item from queue
                    this.queue = this.queue.filter(q => q.id !== item.id);
                    
                } catch (error) {
                    // Increment retry count
                    const queueIndex = this.queue.findIndex(q => q.id === item.id);
                    if (queueIndex !== -1) {
                        this.queue[queueIndex].retries++;
                        
                        // Remove if max retries exceeded
                        if (this.queue[queueIndex].retries >= this.maxRetries) {
                            this.queue.splice(queueIndex, 1);
                            this.notifyOperationFailed(item, error);
                        }
                    }
                }
                
                // Small delay between operations
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            this.saveQueueToStorage();
            
        } finally {
            this.isProcessing = false;
        }
    }

    async processQueueItem(item) {
        switch (item.type) {
            case 'api_call':
                return await this.processApiCall(item);
            case 'file_upload':
                return await this.processFileUpload(item);
            case 'backup_operation':
                return await this.processBackupOperation(item);
            case 'restore_operation':
                return await this.processRestoreOperation(item);
            case 'subscription_update':
                return await this.processSubscriptionUpdate(item);
            default:
                throw new Error(`Unknown queue item type: ${item.type}`);
        }
    }

    async processApiCall(item) {
        const response = await fetch(item.url, {
            method: item.method || 'GET',
            headers: item.headers || {},
            body: item.body
        });

        if (!response.ok) {
            throw new Error(`API call failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        
        // Notify success if callback provided
        if (item.onSuccess) {
            item.onSuccess(result);
        }

        return result;
    }

    async processFileUpload(item) {
        const formData = new FormData();
        
        // Reconstruct file from stored data
        if (item.fileData) {
            const blob = new Blob([item.fileData], { type: item.fileType });
            formData.append('file', blob, item.fileName);
        }

        // Add additional form fields
        if (item.formFields) {
            Object.entries(item.formFields).forEach(([key, value]) => {
                formData.append(key, value);
            });
        }

        const response = await fetch(item.url, {
            method: 'POST',
            headers: item.headers || {},
            body: formData
        });

        if (!response.ok) {
            throw new Error(`File upload failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        
        if (item.onSuccess) {
            item.onSuccess(result);
        }

        return result;
    }

    async processBackupOperation(item) {
        if (window.backupManager) {
            return await window.backupManager.resumeOperation(item.operationId, item.params);
        }
        throw new Error('Backup manager not available');
    }

    async processRestoreOperation(item) {
        if (window.restoreManager) {
            return await window.restoreManager.resumeOperation(item.operationId, item.params);
        }
        throw new Error('Restore manager not available');
    }

    async processSubscriptionUpdate(item) {
        if (window.subscriptionManager) {
            return await window.subscriptionManager.updateSubscription(item.subscriptionData);
        }
        throw new Error('Subscription manager not available');
    }

    notifyOperationFailed(item, error) {
        if (window.errorHandler) {
            window.errorHandler.handleError(error, 'NETWORK', {
                operation: 'queue_processing',
                queueItem: item,
                maxRetriesExceeded: true
            });
        }

        // Call failure callback if provided
        if (item.onFailure) {
            item.onFailure(error);
        }
    }

    /**
     * Public API methods
     */
    
    // Queue an API call
    queueApiCall(url, options = {}) {
        return this.enqueue({
            type: 'api_call',
            url,
            method: options.method || 'GET',
            headers: options.headers,
            body: options.body,
            onSuccess: options.onSuccess,
            onFailure: options.onFailure
        });
    }

    // Queue a file upload
    queueFileUpload(url, file, options = {}) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = () => {
                const queueId = this.enqueue({
                    type: 'file_upload',
                    url,
                    fileName: file.name,
                    fileType: file.type,
                    fileData: reader.result,
                    headers: options.headers,
                    formFields: options.formFields,
                    onSuccess: resolve,
                    onFailure: reject
                });
                
                if (!this.isOnline) {
                    // Notify that file is queued for upload
                    if (window.errorHandler) {
                        window.errorHandler.showUserNotification({
                            type: 'info',
                            title: 'File Queued',
                            message: `${file.name} will be uploaded when connection is restored.`,
                            autoHide: true
                        });
                    }
                }
            };
            
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(file);
        });
    }

    // Queue a backup operation
    queueBackupOperation(operationId, params = {}) {
        return this.enqueue({
            type: 'backup_operation',
            operationId,
            params
        });
    }

    // Queue a restore operation
    queueRestoreOperation(operationId, params = {}) {
        return this.enqueue({
            type: 'restore_operation',
            operationId,
            params
        });
    }

    // Queue a subscription update
    queueSubscriptionUpdate(subscriptionData) {
        return this.enqueue({
            type: 'subscription_update',
            subscriptionData
        });
    }

    // Get queue status
    getQueueStatus() {
        return {
            isOnline: this.isOnline,
            isProcessing: this.isProcessing,
            queueLength: this.queue.length,
            items: this.queue.map(item => ({
                id: item.id,
                type: item.type,
                timestamp: item.timestamp,
                retries: item.retries
            }))
        };
    }

    // Clear the queue
    clear() {
        this.queue = [];
        this.saveQueueToStorage();
    }

    // Remove specific item from queue
    remove(itemId) {
        this.queue = this.queue.filter(item => item.id !== itemId);
        this.saveQueueToStorage();
    }

    // Enable/disable queueing
    enableQueueing() {
        this.queueingEnabled = true;
    }

    disableQueueing() {
        this.queueingEnabled = false;
    }

    // Utility methods
    generateId() {
        return `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Get network status
    getNetworkStatus() {
        return {
            isOnline: this.isOnline,
            connection: this.getConnectionInfo()
        };
    }

    getConnectionInfo() {
        if ('connection' in navigator) {
            const conn = navigator.connection;
            return {
                effectiveType: conn.effectiveType,
                downlink: conn.downlink,
                rtt: conn.rtt,
                saveData: conn.saveData
            };
        }
        return null;
    }

    // Test network connectivity
    async testConnectivity() {
        try {
            const response = await fetch('/api/health', {
                method: 'HEAD',
                cache: 'no-cache'
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }
}

// Initialize global network queue manager
window.NetworkQueueManager = NetworkQueueManager;
window.networkQueue = new NetworkQueueManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NetworkQueueManager;
}