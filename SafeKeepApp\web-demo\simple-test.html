<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Schedule System Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 10px 0; border-radius: 8px; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
        #schedule-console-container { min-height: 400px; border: 1px solid #ddd; padding: 20px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Schedule System Test</h1>
        
        <div class="test-section">
            <h2>Component Loading Test</h2>
            <button class="btn" onclick="testComponentLoading()">Test Component Loading</button>
            <div id="loading-results"></div>
        </div>
        
        <div class="test-section">
            <h2>Manual Initialization</h2>
            <button class="btn" onclick="manualInit()">Initialize Components</button>
            <div id="init-results"></div>
        </div>
        
        <div class="test-section">
            <h2>Schedule Console</h2>
            <div id="schedule-console-container">
                <p>Schedule console will appear here...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Console Output</h2>
            <div id="console-output" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- Load components -->
    <script src="schedule-manager.js"></script>
    <script src="schedule-console.js"></script>
    
    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://babgywcvqyclvxdkckkd.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2MzA3NTEsImV4cCI6MjA2NzIwNjc1MX0.XMSozwfTcSSCXX1yHXfb8LG99oiU8tE_twgYvNthZSs';
        const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzMDc1MSwiZXhwIjoyMDY3MjA2NzUxfQ.DUKouq8tLc1QwyWKw9pze3tHXIiN-L9SNpdvhEQw3cs';

        // Initialize Supabase clients
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        const adminSupabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
        
        let scheduleManager, scheduleConsole;
        
        // Override console.log to show in page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const output = document.getElementById('console-output');
            if (output) {
                const div = document.createElement('div');
                div.textContent = args.join(' ');
                output.appendChild(div);
                output.scrollTop = output.scrollHeight;
            }
        };
        
        function testComponentLoading() {
            const results = document.getElementById('loading-results');
            let html = '<h3>Component Loading Results:</h3>';
            
            const components = [
                'ScheduleManager',
                'ScheduleConsole'
            ];
            
            components.forEach(component => {
                const available = typeof window[component] !== 'undefined';
                html += `<p class="${available ? 'success' : 'error'}">${component}: ${available ? '✅ Available' : '❌ Missing'}</p>`;
            });
            
            // Test Supabase
            const supabaseOk = typeof supabase !== 'undefined';
            html += `<p class="${supabaseOk ? 'success' : 'error'}">Supabase: ${supabaseOk ? '✅ Available' : '❌ Missing'}</p>`;
            
            results.innerHTML = html;
        }
        
        async function manualInit() {
            const results = document.getElementById('init-results');
            results.innerHTML = '<p>Initializing...</p>';
            
            try {
                console.log('🚀 Starting manual initialization...');
                
                // Initialize Schedule Manager
                if (window.ScheduleManager) {
                    console.log('📅 Creating ScheduleManager...');
                    scheduleManager = new window.ScheduleManager(supabase, adminSupabase);
                    await scheduleManager.initialize();
                    console.log('✅ ScheduleManager initialized');
                } else {
                    throw new Error('ScheduleManager class not found');
                }
                
                // Initialize Schedule Console
                if (window.ScheduleConsole) {
                    console.log('🖥️ Creating ScheduleConsole...');
                    scheduleConsole = new window.ScheduleConsole('schedule-console-container');
                    scheduleConsole.setScheduleManager(scheduleManager);
                    console.log('✅ ScheduleConsole initialized');
                } else {
                    throw new Error('ScheduleConsole class not found');
                }
                
                // Test creating a schedule
                console.log('🧪 Testing schedule creation...');
                const testSchedule = await scheduleManager.createSchedule({
                    name: 'Test Schedule',
                    frequency: 'daily',
                    time: '14:00',
                    days: [1, 2, 3, 4, 5],
                    dataTypes: ['contacts'],
                    conditions: {
                        wifiOnly: true,
                        minBatteryLevel: 30,
                        maxStorageUsage: 80,
                        requireCharging: false,
                        requireIdle: false
                    }
                });
                
                console.log('✅ Test schedule created:', testSchedule.name);
                
                results.innerHTML = '<p class="success">✅ Initialization successful! Check console output and schedule console above.</p>';
                
            } catch (error) {
                console.error('❌ Initialization failed:', error);
                results.innerHTML = `<p class="error">❌ Initialization failed: ${error.message}</p>`;
            }
        }
        
        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testComponentLoading();
                console.log('🔍 Page loaded, components tested');
            }, 1000);
        });
    </script>
</body>
</html>