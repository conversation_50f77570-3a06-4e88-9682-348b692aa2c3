import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ProgressBar } from 'react-native-paper';

interface BackupProgressProps {
  progress: number;
  currentItem?: string;
  totalItems?: number;
  completedItems?: number;
}

const BackupProgress: React.FC<BackupProgressProps> = ({
  progress,
  currentItem,
  totalItems,
  completedItems,
}) => {
  return (
    <View style={styles.container}>
      <ProgressBar progress={progress} color="#6200EE" style={styles.progressBar} />
      <Text style={styles.progressText}>
        {Math.round(progress * 100)}% Complete
      </Text>
      {currentItem && (
        <Text style={styles.currentItem}>Processing: {currentItem}</Text>
      )}
      {totalItems && completedItems !== undefined && (
        <Text style={styles.itemCount}>
          {completedItems} of {totalItems} items
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  progressText: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 16,
    fontWeight: 'bold',
  },
  currentItem: {
    textAlign: 'center',
    marginTop: 4,
    fontSize: 14,
    color: '#666',
  },
  itemCount: {
    textAlign: 'center',
    marginTop: 4,
    fontSize: 12,
    color: '#999',
  },
});

export default BackupProgress;