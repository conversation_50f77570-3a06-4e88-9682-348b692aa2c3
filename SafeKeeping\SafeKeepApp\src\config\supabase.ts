import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@env';

// Validate Supabase configuration
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Supabase environment variables are missing');
  console.error('Please check your .env file and babel.config.js configuration');
  throw new Error('Supabase configuration is incomplete');
}

// Supabase configuration from environment variables
const supabaseUrl = SUPABASE_URL;
const supabaseAnonKey = SUPABASE_ANON_KEY;

// Initialize Supabase client
export const supabase: SupabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Test connection on initialization (will be handled by testSupabaseConnection utility)
console.log('🔧 Supabase client initialized');
console.log(`📡 Connected to: ${supabaseUrl}`);
console.log(`🔑 Using anon key: ${supabaseAnonKey.substring(0, 20)}...`);

// Supabase configuration validation
export const validateSupabaseConfig = (): boolean => {
  if (!supabaseUrl || !supabaseUrl.includes('supabase.co')) {
    console.error('Supabase URL not configured properly');
    return false;
  }

  if (!supabaseAnonKey || supabaseAnonKey.length < 100) {
    console.error('Supabase anon key not configured properly');
    return false;
  }

  console.log('✅ Supabase configuration validated successfully');
  return true;
};

// Storage bucket paths
export const STORAGE_PATHS = {
  PHOTOS: 'user-data/{userId}/photos/',
  CONTACTS: 'user-data/{userId}/contacts/',
  MESSAGES: 'user-data/{userId}/messages/',
  METADATA: 'user-data/{userId}/metadata/',
  THUMBNAILS: 'user-data/{userId}/thumbnails/'
} as const;

// Database tables
export const TABLES = {
  USERS: 'users',
  BACKUP_SESSIONS: 'backup_sessions',
  FILE_METADATA: 'file_metadata',
  STORAGE_USAGE: 'storage_usage',
  SYNC_STATUS: 'sync_status'
} as const;

// Storage buckets
export const BUCKETS = {
  USER_DATA: 'user-data',
  THUMBNAILS: 'thumbnails'
} as const;

// Database schema types (for TypeScript)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          display_name: string;
          created_at: string;
          last_login_at: string;
          storage_used: number;
          storage_quota: number;
          encryption_key_id: string | null;
          backup_settings: {
            auto_backup: boolean;
            wifi_only: boolean;
            frequency: 'daily' | 'weekly' | 'monthly';
          };
        };
        Insert: {
          id: string;
          email: string;
          display_name: string;
          created_at?: string;
          last_login_at?: string;
          storage_used?: number;
          storage_quota?: number;
          encryption_key_id?: string | null;
          backup_settings?: {
            auto_backup: boolean;
            wifi_only: boolean;
            frequency: 'daily' | 'weekly' | 'monthly';
          };
        };
        Update: {
          id?: string;
          email?: string;
          display_name?: string;
          created_at?: string;
          last_login_at?: string;
          storage_used?: number;
          storage_quota?: number;
          encryption_key_id?: string | null;
          backup_settings?: {
            auto_backup: boolean;
            wifi_only: boolean;
            frequency: 'daily' | 'weekly' | 'monthly';
          };
        };
      };
      file_metadata: {
        Row: {
          id: string;
          user_id: string;
          original_name: string;
          encrypted_name: string;
          mime_type: string;
          size: number;
          encrypted_size: number;
          uploaded_at: string;
          last_modified: string;
          category: 'photo' | 'contact' | 'message';
          hash: string;
          encryption_iv: string;
          encryption_salt: string;
          storage_path: string;
          is_backed_up: boolean;
        };
        Insert: {
          id?: string;
          user_id: string;
          original_name: string;
          encrypted_name: string;
          mime_type: string;
          size: number;
          encrypted_size: number;
          uploaded_at?: string;
          last_modified?: string;
          category: 'photo' | 'contact' | 'message';
          hash: string;
          encryption_iv: string;
          encryption_salt: string;
          storage_path: string;
          is_backed_up?: boolean;
        };
        Update: {
          id?: string;
          user_id?: string;
          original_name?: string;
          encrypted_name?: string;
          mime_type?: string;
          size?: number;
          encrypted_size?: number;
          uploaded_at?: string;
          last_modified?: string;
          category?: 'photo' | 'contact' | 'message';
          hash?: string;
          encryption_iv?: string;
          encryption_salt?: string;
          storage_path?: string;
          is_backed_up?: boolean;
        };
      };
      backup_sessions: {
        Row: {
          id: string;
          user_id: string;
          session_type: 'manual' | 'automatic';
          status: 'running' | 'completed' | 'failed' | 'cancelled';
          started_at: string;
          completed_at: string | null;
          total_files: number;
          processed_files: number;
          failed_files: number;
          total_bytes: number;
          processed_bytes: number;
          error_message: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          session_type: 'manual' | 'automatic';
          status?: 'running' | 'completed' | 'failed' | 'cancelled';
          started_at?: string;
          completed_at?: string | null;
          total_files: number;
          processed_files?: number;
          failed_files?: number;
          total_bytes: number;
          processed_bytes?: number;
          error_message?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          session_type?: 'manual' | 'automatic';
          status?: 'running' | 'completed' | 'failed' | 'cancelled';
          started_at?: string;
          completed_at?: string | null;
          total_files?: number;
          processed_files?: number;
          failed_files?: number;
          total_bytes?: number;
          processed_bytes?: number;
          error_message?: string | null;
        };
      };
    };
  };
}

export default supabase;
