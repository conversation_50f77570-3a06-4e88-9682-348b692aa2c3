-- SafeKeep Modular Pricing Verification Script
-- Run this script to verify that the modular pricing system is working correctly

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

SELECT '=== MODULAR PRICING SYSTEM VERIFICATION ===' as title;

-- 1. Verify all new tables exist
SELECT 'Table Verification:' as section;
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('service_types', 'subscription_plans', 'plan_services', 'user_service_selections') 
        THEN '✓ EXISTS' 
        ELSE '✗ MISSING' 
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('service_types', 'subscription_plans', 'plan_services', 'user_service_selections')
ORDER BY table_name;

-- 2. Verify service types are populated
SELECT 'Service Types:' as section;
SELECT id, name, base_price_cents, storage_limit_gb, is_active
FROM public.service_types
ORDER BY base_price_cents;

-- 3. Verify subscription plans are populated
SELECT 'Subscription Plans:' as section;
SELECT id, name, price_cents, total_storage_gb, is_combination, is_popular, is_active
FROM public.subscription_plans
ORDER BY price_cents;

-- 4. Verify plan-service relationships
SELECT 'Plan-Service Relationships:' as section;
SELECT 
    ps.plan_id,
    sp.name as plan_name,
    STRING_AGG(ps.service_type_id, ', ' ORDER BY ps.service_type_id) as included_services,
    sp.price_cents
FROM public.plan_services ps
JOIN public.subscription_plans sp ON ps.plan_id = sp.id
WHERE ps.is_included = TRUE
GROUP BY ps.plan_id, sp.name, sp.price_cents
ORDER BY sp.price_cents;

-- 5. Test pricing calculation functions
SELECT 'Pricing Calculation Tests:' as section;

-- Test individual service pricing
SELECT 'Individual Services Pricing:' as test_name;
SELECT * FROM public.calculate_optimal_price(ARRAY['contacts']);
SELECT * FROM public.calculate_optimal_price(ARRAY['messages']);
SELECT * FROM public.calculate_optimal_price(ARRAY['photos']);

-- Test combination pricing
SELECT 'Combination Services Pricing:' as test_name;
SELECT * FROM public.calculate_optimal_price(ARRAY['contacts', 'messages']);
SELECT * FROM public.calculate_optimal_price(ARRAY['contacts', 'photos']);
SELECT * FROM public.calculate_optimal_price(ARRAY['messages', 'photos']);
SELECT * FROM public.calculate_optimal_price(ARRAY['contacts', 'messages', 'photos']);

-- 6. Verify available service combinations
SELECT 'Available Service Combinations:' as section;
SELECT * FROM public.get_available_service_combinations();

-- 7. Check existing user subscriptions migration
SELECT 'Existing Subscriptions Migration:' as section;
SELECT 
    COUNT(*) as total_subscriptions,
    COUNT(CASE WHEN plan_id IS NOT NULL THEN 1 END) as migrated_subscriptions,
    COUNT(CASE WHEN legacy_tier_id IS NOT NULL THEN 1 END) as legacy_subscriptions
FROM public.user_subscriptions;

-- Show plan distribution
SELECT 'Plan Distribution:' as subsection;
SELECT 
    COALESCE(plan_id, 'NOT_MIGRATED') as plan_id,
    COUNT(*) as user_count,
    ROUND(AVG(total_price_cents), 2) as avg_price_cents
FROM public.user_subscriptions
GROUP BY plan_id
ORDER BY user_count DESC;

-- 8. Verify indexes exist
SELECT 'Index Verification:' as section;
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('service_types', 'subscription_plans', 'plan_services', 'user_service_selections')
ORDER BY tablename, indexname;

-- 9. Verify RLS policies
SELECT 'RLS Policies Verification:' as section;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('service_types', 'subscription_plans', 'plan_services', 'user_service_selections')
ORDER BY tablename, policyname;

-- 10. Test data integrity
SELECT 'Data Integrity Tests:' as section;

-- Check for orphaned plan_services
SELECT 'Orphaned Plan Services:' as test_name;
SELECT COUNT(*) as orphaned_count
FROM public.plan_services ps
LEFT JOIN public.subscription_plans sp ON ps.plan_id = sp.id
LEFT JOIN public.service_types st ON ps.service_type_id = st.id
WHERE sp.id IS NULL OR st.id IS NULL;

-- Check for users without service selections
SELECT 'Users Without Service Selections:' as test_name;
SELECT COUNT(*) as users_without_services
FROM public.users u
LEFT JOIN public.user_service_selections uss ON u.id = uss.user_id AND uss.is_active = TRUE
WHERE uss.user_id IS NULL;

-- 11. Sample pricing scenarios
SELECT 'Sample Pricing Scenarios:' as section;

-- Scenario 1: New user wants contacts only
SELECT 'Scenario 1 - Contacts Only:' as scenario;
SELECT 
    'contacts-only' as selected_plan,
    sp.name,
    sp.price_cents,
    sp.total_storage_gb,
    STRING_AGG(ps.service_type_id, ', ') as included_services
FROM public.subscription_plans sp
JOIN public.plan_services ps ON sp.id = ps.plan_id AND ps.is_included = TRUE
WHERE sp.id = 'contacts-only'
GROUP BY sp.id, sp.name, sp.price_cents, sp.total_storage_gb;

-- Scenario 2: User wants messages + photos (popular combination)
SELECT 'Scenario 2 - Messages + Photos (Popular):' as scenario;
SELECT 
    'messages-photos' as selected_plan,
    sp.name,
    sp.price_cents,
    sp.total_storage_gb,
    sp.is_popular,
    STRING_AGG(ps.service_type_id, ', ') as included_services
FROM public.subscription_plans sp
JOIN public.plan_services ps ON sp.id = ps.plan_id AND ps.is_included = TRUE
WHERE sp.id = 'messages-photos'
GROUP BY sp.id, sp.name, sp.price_cents, sp.total_storage_gb, sp.is_popular;

-- Scenario 3: User wants everything (complete backup)
SELECT 'Scenario 3 - Complete Backup:' as scenario;
SELECT 
    'complete-backup' as selected_plan,
    sp.name,
    sp.price_cents,
    sp.total_storage_gb,
    STRING_AGG(ps.service_type_id, ', ') as included_services,
    -- Calculate savings vs individual services
    sp.price_cents - (
        SELECT SUM(st.base_price_cents) 
        FROM public.service_types st 
        WHERE st.id IN ('contacts', 'messages', 'photos')
    ) as savings_cents
FROM public.subscription_plans sp
JOIN public.plan_services ps ON sp.id = ps.plan_id AND ps.is_included = TRUE
WHERE sp.id = 'complete-backup'
GROUP BY sp.id, sp.name, sp.price_cents, sp.total_storage_gb;

-- 12. Performance test
SELECT 'Performance Test:' as section;
EXPLAIN ANALYZE 
SELECT * FROM public.get_available_service_combinations();

SELECT '=== VERIFICATION COMPLETE ===' as title;
SELECT 'If all tests pass, the modular pricing system is ready for use!' as conclusion;