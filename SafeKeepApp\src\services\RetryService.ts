import { BackupError, RetryConfig } from '../types/backup';

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: BackupError;
  attempts: number;
}

class RetryService {
  private readonly defaultConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    backoffMultiplier: 2,
    jitter: true
  };

  /**
   * Execute a function with exponential backoff retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    errorContext?: Record<string, any>
  ): Promise<RetryResult<T>> {
    const finalConfig = { ...this.defaultConfig, ...config };
    let lastError: BackupError | undefined;
    let attempts = 0;

    for (attempts = 0; attempts <= finalConfig.maxRetries; attempts++) {
      try {
        console.log(`🔄 Retry attempt ${attempts + 1}/${finalConfig.maxRetries + 1}`);
        
        const result = await operation();
        
        if (attempts > 0) {
          console.log(`✅ Operation succeeded after ${attempts + 1} attempts`);
        }
        
        return {
          success: true,
          result,
          attempts: attempts + 1
        };
      } catch (error) {
        console.error(`❌ Attempt ${attempts + 1} failed:`, error);
        
        lastError = this.createBackupError(error, attempts, finalConfig.maxRetries, errorContext);
        
        // Don't retry if this is the last attempt or error is not retryable
        if (attempts === finalConfig.maxRetries || !this.isRetryableError(error)) {
          break;
        }
        
        // Calculate delay with exponential backoff and optional jitter
        const delay = this.calculateDelay(attempts, finalConfig);
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        
        await this.sleep(delay);
      }
    }

    return {
      success: false,
      error: lastError,
      attempts: attempts + 1
    };
  }

  /**
   * Retry specific backup errors with context-aware logic
   */
  async retryBackupError(
    error: BackupError,
    operation: () => Promise<any>,
    config?: Partial<RetryConfig>
  ): Promise<RetryResult<any>> {
    if (!error.retryable) {
      console.log(`❌ Error ${error.id} is not retryable`);
      return {
        success: false,
        error,
        attempts: 0
      };
    }

    // Adjust retry config based on error type
    const adjustedConfig = this.getConfigForErrorType(error.type, config);
    
    console.log(`🔄 Retrying error ${error.id} of type ${error.type}`);
    
    return this.executeWithRetry(operation, adjustedConfig, {
      originalErrorId: error.id,
      errorType: error.type,
      retryCount: (error.retryCount || 0) + 1
    });
  }

  /**
   * Batch retry multiple failed operations
   */
  async retryBatch<T>(
    operations: Array<{
      id: string;
      operation: () => Promise<T>;
      error?: BackupError;
    }>,
    config?: Partial<RetryConfig>
  ): Promise<Array<RetryResult<T>>> {
    console.log(`🔄 Retrying batch of ${operations.length} operations`);
    
    const results: Array<RetryResult<T>> = [];
    
    // Process operations sequentially to avoid overwhelming the system
    for (const op of operations) {
      try {
        let result: RetryResult<T>;
        
        if (op.error) {
          result = await this.retryBackupError(op.error, op.operation, config);
        } else {
          result = await this.executeWithRetry(op.operation, config, { operationId: op.id });
        }
        
        results.push(result);
        
        // Small delay between batch operations
        await this.sleep(100);
      } catch (error) {
        console.error(`❌ Batch operation ${op.id} failed:`, error);
        results.push({
          success: false,
          error: this.createBackupError(error, 0, 0, { operationId: op.id }),
          attempts: 1
        });
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    console.log(`✅ Batch retry completed: ${successCount}/${operations.length} succeeded`);
    
    return results;
  }

  /**
   * Check if an error should be retried
   */
  private isRetryableError(error: any): boolean {
    // Network errors are generally retryable
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('network')) {
      return true;
    }
    
    // Timeout errors are retryable
    if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {
      return true;
    }
    
    // Server errors (5xx) are retryable
    if (error.status >= 500 && error.status < 600) {
      return true;
    }
    
    // Rate limiting is retryable
    if (error.status === 429 || error.code === 'RATE_LIMITED') {
      return true;
    }
    
    // Temporary storage issues
    if (error.code === 'STORAGE_TEMPORARILY_UNAVAILABLE') {
      return true;
    }
    
    // Permission errors are generally not retryable without user action
    if (error.code === 'PERMISSION_DENIED' || error.status === 403) {
      return false;
    }
    
    // Authentication errors are not retryable
    if (error.status === 401 || error.code === 'UNAUTHORIZED') {
      return false;
    }
    
    // Client errors (4xx except 429) are generally not retryable
    if (error.status >= 400 && error.status < 500 && error.status !== 429) {
      return false;
    }
    
    // Default to retryable for unknown errors
    return true;
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(attempt: number, config: RetryConfig): number {
    const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
    let delay = Math.min(exponentialDelay, config.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (config.jitter) {
      const jitterAmount = delay * 0.1; // 10% jitter
      delay += (Math.random() - 0.5) * 2 * jitterAmount;
    }
    
    return Math.max(delay, 0);
  }

  /**
   * Get retry configuration based on error type
   */
  private getConfigForErrorType(errorType: BackupError['type'], baseConfig?: Partial<RetryConfig>): RetryConfig {
    const typeConfigs: Record<BackupError['type'], Partial<RetryConfig>> = {
      network: {
        maxRetries: 5,
        baseDelay: 2000,
        maxDelay: 60000,
        backoffMultiplier: 2,
        jitter: true
      },
      storage: {
        maxRetries: 3,
        baseDelay: 5000,
        maxDelay: 30000,
        backoffMultiplier: 1.5,
        jitter: true
      },
      encryption: {
        maxRetries: 2,
        baseDelay: 1000,
        maxDelay: 10000,
        backoffMultiplier: 2,
        jitter: false
      },
      platform: {
        maxRetries: 1,
        baseDelay: 1000,
        maxDelay: 5000,
        backoffMultiplier: 1,
        jitter: false
      },
      permission: {
        maxRetries: 0, // Permission errors require user action
        baseDelay: 0,
        maxDelay: 0,
        backoffMultiplier: 1,
        jitter: false
      }
    };

    const typeConfig = typeConfigs[errorType] || {};
    return { ...this.defaultConfig, ...typeConfig, ...baseConfig };
  }

  /**
   * Create a standardized BackupError from any error
   */
  private createBackupError(
    error: any, 
    retryCount: number, 
    maxRetries: number, 
    context?: Record<string, any>
  ): BackupError {
    const errorType = this.determineErrorType(error);
    
    return {
      id: `retry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: errorType,
      message: error.message || error.toString() || 'Unknown error',
      timestamp: new Date(),
      retryable: this.isRetryableError(error),
      retryCount,
      maxRetries,
      context: {
        ...context,
        originalError: {
          name: error.name,
          code: error.code,
          status: error.status,
          stack: error.stack
        }
      }
    };
  }

  /**
   * Determine error type from error object
   */
  private determineErrorType(error: any): BackupError['type'] {
    if (error.code === 'PERMISSION_DENIED' || error.status === 403) {
      return 'permission';
    }
    
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('network') || error.message?.includes('fetch')) {
      return 'network';
    }
    
    if (error.code === 'STORAGE_ERROR' || error.message?.includes('storage') || error.message?.includes('quota')) {
      return 'storage';
    }
    
    if (error.code === 'ENCRYPTION_ERROR' || error.message?.includes('encrypt') || error.message?.includes('decrypt')) {
      return 'encryption';
    }
    
    return 'platform';
  }

  /**
   * Sleep utility function
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get retry statistics for monitoring
   */
  getRetryStatistics(): {
    totalRetries: number;
    successfulRetries: number;
    failedRetries: number;
    averageAttempts: number;
  } {
    // This would be implemented with actual statistics tracking
    return {
      totalRetries: 0,
      successfulRetries: 0,
      failedRetries: 0,
      averageAttempts: 0
    };
  }
}

export default new RetryService();