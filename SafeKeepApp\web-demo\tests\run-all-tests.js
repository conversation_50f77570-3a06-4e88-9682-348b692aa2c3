#!/usr/bin/env node

/**
 * Master Test Runner
 * Runs all integration tests and generates comprehensive reports
 */

const IntegrationTestRunner = require('./integration-test-runner');
const E2ETestRunner = require('./e2e-test-runner');
const StripeIntegrationTests = require('./stripe-integration-tests');
const SubscriptionLifecycleTests = require('./subscription-lifecycle-tests');
const PerformanceBenchmarkTests = require('./performance-benchmark-tests');
const CrossBrowserTests = require('./cross-browser-tests');
const fs = require('fs');
const path = require('path');

class MasterTestRunner {
    constructor() {
        this.startTime = Date.now();
        this.testSuites = [
            { name: 'Integration Tests', runner: IntegrationTestRunner },
            { name: 'E2E Tests', runner: E2ETestRunner },
            { name: 'Stripe Integration Tests', runner: StripeIntegrationTests },
            { name: 'Subscription Lifecycle Tests', runner: SubscriptionLifecycleTests },
            { name: 'Performance Benchmark Tests', runner: PerformanceBenchmarkTests },
            { name: 'Cross-Browser Tests', runner: CrossBrowserTests }
        ];
        this.results = [];
    }

    async runAllTests() {
        console.log('🚀 SafeKeep Web Demo - Master Test Suite');
        console.log('=========================================');
        console.log(`Started at: ${new Date().toISOString()}`);
        console.log(`Test suites to run: ${this.testSuites.length}`);

        // Ensure reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        // Run each test suite
        for (const suite of this.testSuites) {
            await this.runTestSuite(suite);
        }

        // Generate master report
        this.generateMasterReport();
    }

    async runTestSuite(suite) {
        console.log(`\n📋 Running ${suite.name}...`);
        console.log('='.repeat(suite.name.length + 12));

        const startTime = Date.now();
        let result = {
            name: suite.name,
            status: 'ERROR',
            duration: 0,
            summary: {},
            error: null
        };

        try {
            const runner = new suite.runner();
            await runner.runAllTests();
            
            const duration = Date.now() - startTime;
            result = {
                name: suite.name,
                status: 'COMPLETED',
                duration,
                summary: this.extractSummaryFromReport(suite.name),
                error: null
            };

            console.log(`✅ ${suite.name} completed in ${duration}ms`);

        } catch (error) {
            const duration = Date.now() - startTime;
            result.duration = duration;
            result.error = error.message;
            
            console.error(`❌ ${suite.name} failed: ${error.message}`);
        }

        this.results.push(result);
    }

    extractSummaryFromReport(suiteName) {
        // Try to read the generated report file
        const reportFiles = {
            'Integration Tests': 'integration-test-report.json',
            'E2E Tests': 'e2e-test-report.json',
            'Stripe Integration Tests': 'stripe-integration-test-report.json',
            'Subscription Lifecycle Tests': 'subscription-lifecycle-test-report.json',
            'Performance Benchmark Tests': 'performance-benchmark-test-report.json',
            'Cross-Browser Tests': 'cross-browser-test-report.json'
        };

        const reportFile = reportFiles[suiteName];
        if (!reportFile) return {};

        const reportPath = path.join(__dirname, '../test-reports', reportFile);
        
        try {
            if (fs.existsSync(reportPath)) {
                const reportData = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
                return reportData.summary || {};
            }
        } catch (error) {
            console.warn(`⚠️ Could not read report for ${suiteName}: ${error.message}`);
        }

        return {};
    }

    generateMasterReport() {
        const totalDuration = Date.now() - this.startTime;
        const completedSuites = this.results.filter(r => r.status === 'COMPLETED').length;
        const failedSuites = this.results.filter(r => r.status === 'ERROR').length;
        const totalSuites = this.results.length;

        console.log('\n📊 Master Test Suite Results');
        console.log('=============================');
        console.log(`Total Duration: ${totalDuration}ms (${(totalDuration / 1000 / 60).toFixed(2)} minutes)`);
        console.log(`Completed Suites: ${completedSuites}/${totalSuites}`);
        console.log(`Failed Suites: ${failedSuites}`);
        console.log(`Overall Success Rate: ${((completedSuites / totalSuites) * 100).toFixed(1)}%`);

        // Detailed results
        console.log('\n📋 Detailed Results:');
        this.results.forEach(result => {
            console.log(`\n${result.name}:`);
            console.log(`  Status: ${result.status}`);
            console.log(`  Duration: ${result.duration}ms`);
            
            if (result.summary && Object.keys(result.summary).length > 0) {
                console.log(`  Summary:`);
                Object.entries(result.summary).forEach(([key, value]) => {
                    console.log(`    ${key}: ${value}`);
                });
            }
            
            if (result.error) {
                console.log(`  Error: ${result.error}`);
            }
        });

        // Calculate aggregate statistics
        const aggregateStats = this.calculateAggregateStats();
        
        console.log('\n🔢 Aggregate Statistics:');
        console.log(`Total Tests Run: ${aggregateStats.totalTests}`);
        console.log(`Total Tests Passed: ${aggregateStats.totalPassed} ✅`);
        console.log(`Total Tests Failed: ${aggregateStats.totalFailed} ❌`);
        console.log(`Total Errors: ${aggregateStats.totalErrors} 💥`);
        console.log(`Overall Test Success Rate: ${aggregateStats.overallSuccessRate}%`);

        // Generate master report file
        const masterReport = {
            timestamp: new Date().toISOString(),
            totalDuration,
            suiteResults: this.results,
            aggregateStats,
            summary: {
                totalSuites,
                completedSuites,
                failedSuites,
                suiteSuccessRate: ((completedSuites / totalSuites) * 100).toFixed(1)
            }
        };

        const reportPath = path.join(__dirname, '../test-reports/master-test-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(masterReport, null, 2));

        // Generate HTML report
        this.generateHTMLReport(masterReport);

        console.log('\n📄 Reports Generated:');
        console.log('  📄 master-test-report.json');
        console.log('  🌐 master-test-report.html');
        console.log(`\n🎯 Test Suite ${completedSuites === totalSuites ? 'PASSED' : 'FAILED'}`);
    }

    calculateAggregateStats() {
        let totalTests = 0;
        let totalPassed = 0;
        let totalFailed = 0;
        let totalErrors = 0;

        this.results.forEach(result => {
            if (result.summary) {
                totalTests += result.summary.total || 0;
                totalPassed += result.summary.passed || 0;
                totalFailed += result.summary.failed || 0;
                totalErrors += result.summary.errors || 0;
            }
        });

        const overallSuccessRate = totalTests > 0 ? 
            ((totalPassed / totalTests) * 100).toFixed(1) : '0.0';

        return {
            totalTests,
            totalPassed,
            totalFailed,
            totalErrors,
            overallSuccessRate
        };
    }

    generateHTMLReport(masterReport) {
        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep Web Demo - Test Results</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .error { color: #fd7e14; }
        .results {
            padding: 30px;
        }
        .suite-result {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        .suite-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .suite-name {
            font-weight: bold;
            font-size: 1.1em;
        }
        .suite-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .suite-details {
            padding: 20px;
        }
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .detail-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .detail-value {
            font-weight: bold;
            font-size: 1.2em;
        }
        .detail-label {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SafeKeep Web Demo</h1>
            <p>Integration Test Results - ${new Date(masterReport.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="stat-card">
                <div class="stat-number">${masterReport.summary.totalSuites}</div>
                <div class="stat-label">Total Suites</div>
            </div>
            <div class="stat-card">
                <div class="stat-number passed">${masterReport.summary.completedSuites}</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number failed">${masterReport.summary.failedSuites}</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${masterReport.summary.suiteSuccessRate}%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${masterReport.aggregateStats.totalTests}</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number passed">${masterReport.aggregateStats.totalPassed}</div>
                <div class="stat-label">Tests Passed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number failed">${masterReport.aggregateStats.totalFailed}</div>
                <div class="stat-label">Tests Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${masterReport.aggregateStats.overallSuccessRate}%</div>
                <div class="stat-label">Test Success Rate</div>
            </div>
        </div>
        
        <div class="results">
            <h2>Test Suite Results</h2>
            ${masterReport.suiteResults.map(result => `
                <div class="suite-result">
                    <div class="suite-header">
                        <div class="suite-name">${result.name}</div>
                        <div class="suite-status status-${result.status.toLowerCase()}">
                            ${result.status}
                        </div>
                    </div>
                    <div class="suite-details">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-value">${result.duration}ms</div>
                                <div class="detail-label">Duration</div>
                            </div>
                            ${result.summary.total ? `
                                <div class="detail-item">
                                    <div class="detail-value">${result.summary.total}</div>
                                    <div class="detail-label">Total Tests</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-value passed">${result.summary.passed || 0}</div>
                                    <div class="detail-label">Passed</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-value failed">${result.summary.failed || 0}</div>
                                    <div class="detail-label">Failed</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-value">${result.summary.successRate || '0.0'}%</div>
                                    <div class="detail-label">Success Rate</div>
                                </div>
                            ` : ''}
                        </div>
                        ${result.error ? `
                            <div class="error-message">
                                Error: ${result.error}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
        
        <div class="footer">
            <p>Generated by SafeKeep Web Demo Test Suite</p>
            <p>Total execution time: ${(masterReport.totalDuration / 1000 / 60).toFixed(2)} minutes</p>
        </div>
    </div>
</body>
</html>`;

        const htmlPath = path.join(__dirname, '../test-reports/master-test-report.html');
        fs.writeFileSync(htmlPath, html);
    }
}

// Run if called directly
if (require.main === module) {
    const runner = new MasterTestRunner();
    runner.runAllTests().catch(error => {
        console.error('❌ Master test runner failed:', error);
        process.exit(1);
    });
}

module.exports = MasterTestRunner;