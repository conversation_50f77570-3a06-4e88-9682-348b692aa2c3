# How to Get Your Supabase Credentials

Follow these steps to get your actual Supabase credentials:

## 1. Get Your Project URL and API Keys

1. **Go to your Supabase Dashboard**: https://babgywcvqyclvxdkckkd.supabase.co
2. **Navigate to Settings > API**
3. **Copy the following values:**

### Project URL
```
https://babgywcvqyclvxdkckkd.supabase.co
```

### Anon Key (Public)
This is safe to use in your React Native app:
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Service Role Key (Secret)
⚠️ **KEEP THIS SECRET** - Only use server-side:
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 2. Update Your .env File

Replace the placeholder values in `SafeKeepApp/.env`:

```env
REACT_APP_SUPABASE_URL=https://babgywcvqyclvxdkckkd.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your_actual_anon_key_here
REACT_APP_SUPABASE_SERVICE_KEY=your_actual_service_key_here
REACT_APP_VERSION=1.0.0
```

## 3. Security Notes

- ✅ **Anon Key**: Safe to use in mobile apps
- ❌ **Service Key**: Never expose in client-side code
- 🔒 **Add .env to .gitignore** to avoid committing secrets

## 4. Test Connection

After updating your credentials, you can test the connection by running the app and trying to:
- Sign up a new user
- Check if the user appears in your Supabase dashboard
- Verify the database triggers work correctly