import React from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Text, Card, Divider, Chip, IconButton } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { COLORS, SPACING } from '../../utils/constants';
import { useBackupHistory, useBackupStatistics } from '../../store/hooks/backupHooks';
import { BackupSession } from '../../types/backup';

type NavigationProp = StackNavigationProp<RootStackParamList>;

const BackupHistoryScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const backupHistory = useBackupHistory();
  const statistics = useBackupStatistics();

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return COLORS.success;
      case 'failed':
        return COLORS.error;
      case 'cancelled':
        return COLORS.warning;
      case 'in_progress':
        return COLORS.primary;
      default:
        return COLORS.textSecondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return 'check-circle';
      case 'failed':
        return 'alert-circle';
      case 'cancelled':
        return 'cancel';
      case 'in_progress':
        return 'sync';
      default:
        return 'help-circle';
    }
  };

  const renderBackupItem = ({ item }: { item: BackupSession }) => {
    const statusColor = getStatusColor(item.status);
    const statusIcon = getStatusIcon(item.status);
    const completionPercentage = item.total_items > 0 
      ? Math.round((item.completed_items / item.total_items) * 100) 
      : 0;

    return (
      <Card style={styles.historyCard}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Icon name={statusIcon} size={20} color={statusColor} />
            <Text style={[styles.statusText, { color: statusColor }]}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
            <Text style={styles.dateText}>
              {formatDate(item.start_time)}
            </Text>
          </View>

          <View style={styles.statsRow}>
            <Text style={styles.statsText}>
              {item.completed_items} of {item.total_items} items
            </Text>
            <Chip 
              style={[styles.percentageChip, { backgroundColor: statusColor + '20' }]}
              textStyle={{ color: statusColor }}
            >
              {completionPercentage}%
            </Chip>
          </View>

          <View style={styles.dataTypesContainer}>
            {item.configuration.includeContacts && (
              <Chip style={styles.dataTypeChip} icon="contacts">Contacts</Chip>
            )}
            {item.configuration.includeMessages && (
              <Chip style={styles.dataTypeChip} icon="message-text">Messages</Chip>
            )}
            {item.configuration.includePhotos && (
              <Chip style={styles.dataTypeChip} icon="camera">Photos</Chip>
            )}
          </View>

          {item.error_message && (
            <Text style={styles.errorText}>Error: {item.error_message}</Text>
          )}
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <Card style={styles.statisticsCard}>
        <Card.Title title="Backup Statistics" />
        <Card.Content>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{statistics.totalBackups}</Text>
              <Text style={styles.statLabel}>Total Backups</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{statistics.totalItems}</Text>
              <Text style={styles.statLabel}>Items Backed Up</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{Math.round(statistics.successRate)}%</Text>
              <Text style={styles.statLabel}>Success Rate</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <View style={styles.historyHeader}>
        <Text style={styles.historyTitle}>Backup History</Text>
        <Text style={styles.historySubtitle}>
          {backupHistory.length} {backupHistory.length === 1 ? 'session' : 'sessions'}
        </Text>
      </View>

      <FlatList
        data={backupHistory}
        renderItem={renderBackupItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="history" size={48} color={COLORS.textSecondary} />
            <Text style={styles.emptyText}>No backup history yet</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.md,
  },
  statisticsCard: {
    marginBottom: SPACING.md,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  historySubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  listContainer: {
    paddingBottom: SPACING.xl,
  },
  historyCard: {
    marginBottom: SPACING.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statusText: {
    marginLeft: SPACING.xs,
    fontWeight: '500',
    flex: 1,
  },
  dateText: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statsText: {
    fontSize: 14,
    color: COLORS.text,
  },
  percentageChip: {
    height: 24,
  },
  dataTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  dataTypeChip: {
    backgroundColor: COLORS.surface,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  errorText: {
    marginTop: SPACING.sm,
    color: COLORS.error,
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
  },
  emptyText: {
    marginTop: SPACING.md,
    color: COLORS.textSecondary,
    fontSize: 16,
  },
});

export default BackupHistoryScreen;