/**
 * Subscription Lifecycle Tests
 * Tests complete subscription workflows from signup to cancellation
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class SubscriptionLifecycleTests {
    constructor() {
        this.browser = null;
        this.testResults = [];
        this.baseUrl = 'http://localhost:3000';
        this.startTime = Date.now();
    }

    async initialize() {
        console.log('🔄 Initializing Subscription Lifecycle Tests...');
        
        this.browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        // Ensure test reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
    }

    async runAllTests() {
        await this.initialize();

        const testSuites = [
            this.testFreeTrialSignup,
            this.testSubscriptionUpgrade,
            this.testPaymentMethodUpdate,
            this.testSubscriptionDowngrade,
            this.testBillingCycleTransition,
            this.testFailedPaymentHandling,
            this.testSubscriptionPause,
            this.testSubscriptionCancellation,
            this.testReactivation,
            this.testProrationCalculation
        ];

        console.log('🔄 Running Subscription Lifecycle Tests...');
        console.log('==========================================');

        for (const testSuite of testSuites) {
            await this.runTestSuite(testSuite);
        }

        await this.cleanup();
        this.generateReport();
    }

    async runTestSuite(testFunction) {
        const testName = testFunction.name;
        console.log(`\n🧪 Running ${testName}...`);

        const startTime = Date.now();
        let page = null;

        try {
            page = await this.browser.newPage();
            await page.setViewport({ width: 1280, height: 720 });
            
            const result = await testFunction.call(this, page);
            const duration = Date.now() - startTime;

            this.testResults.push({
                test: testName,
                status: result.success ? 'PASSED' : 'FAILED',
                duration,
                details: result.details,
                errors: result.errors || [],
                data: result.data || {}
            });

            console.log(`${result.success ? '✅' : '❌'} ${testName} - ${duration}ms`);

        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`❌ ${testName} failed:`, error.message);
            
            this.testResults.push({
                test: testName,
                status: 'ERROR',
                duration,
                details: 'Test execution failed',
                errors: [error.message],
                data: {}
            });
        } finally {
            if (page) {
                await page.close();
            }
        }
    }

    async testFreeTrialSignup(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to subscription page
            await page.click('#subscription-tab');
            await page.waitForSelector('#subscription-tiers', { timeout: 3000 });
            steps.push('✅ Subscription page loaded');

            // Start free trial
            await page.click('#start-free-trial-btn');
            await page.waitForSelector('#trial-signup-form', { timeout: 3000 });
            steps.push('✅ Free trial signup form displayed');

            // Fill signup form
            await page.type('#trial-email', '<EMAIL>');
            await page.type('#trial-password', 'testpass123');
            await page.click('#trial-signup-submit');

            // Wait for trial activation
            await page.waitForSelector('#trial-dashboard', { timeout: 5000 });
            steps.push('✅ Free trial activated');

            // Verify trial status
            const trialStatus = await page.$eval('#trial-status', el => el.textContent);
            if (trialStatus.includes('Trial')) {
                steps.push('✅ Trial status displayed correctly');
            }

            // Check trial features access
            await page.click('#premium-feature-test');
            await page.waitForSelector('#feature-access-granted', { timeout: 3000 });
            steps.push('✅ Premium features accessible during trial');

            return {
                success: true,
                details: steps.join('\n'),
                data: { trialStatus }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testSubscriptionUpgrade(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate existing free user
            await page.evaluate(() => {
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-123',
                    email: '<EMAIL>',
                    subscription: { tier: 'free', status: 'active' }
                }));
            });
            await page.reload();

            // Navigate to upgrade
            await page.click('#upgrade-subscription-btn');
            await page.waitForSelector('#upgrade-options', { timeout: 3000 });
            steps.push('✅ Upgrade options displayed');

            // Select premium tier
            await page.click('#premium-tier-select');
            await page.waitForSelector('#payment-form', { timeout: 3000 });
            steps.push('✅ Premium tier selected, payment form shown');

            // Fill payment details (test mode)
            await page.type('#card-number', '****************');
            await page.type('#card-expiry', '12/25');
            await page.type('#card-cvc', '123');
            await page.type('#billing-name', 'Test User');
            steps.push('✅ Payment details entered');

            // Process upgrade
            await page.click('#process-upgrade-btn');
            await page.waitForSelector('#upgrade-success', { timeout: 10000 });
            steps.push('✅ Upgrade processed successfully');

            // Verify new subscription status
            const newStatus = await page.$eval('#subscription-status', el => el.textContent);
            if (newStatus.includes('Premium')) {
                steps.push('✅ Subscription status updated to Premium');
            }

            // Test premium feature access
            await page.click('#premium-analytics-tab');
            await page.waitForSelector('#premium-analytics-dashboard', { timeout: 3000 });
            steps.push('✅ Premium features now accessible');

            return {
                success: true,
                details: steps.join('\n'),
                data: { newStatus }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testPaymentMethodUpdate(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate existing premium user
            await page.evaluate(() => {
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-456',
                    email: '<EMAIL>',
                    subscription: { 
                        tier: 'premium', 
                        status: 'active',
                        paymentMethod: { last4: '4242', brand: 'visa' }
                    }
                }));
            });
            await page.reload();

            // Navigate to billing settings
            await page.click('#billing-settings-tab');
            await page.waitForSelector('#payment-methods', { timeout: 3000 });
            steps.push('✅ Billing settings loaded');

            // Current payment method displayed
            const currentCard = await page.$eval('#current-payment-method', el => el.textContent);
            if (currentCard.includes('4242')) {
                steps.push('✅ Current payment method displayed');
            }

            // Add new payment method
            await page.click('#add-payment-method-btn');
            await page.waitForSelector('#new-card-form', { timeout: 3000 });
            steps.push('✅ New payment method form displayed');

            // Enter new card details
            await page.type('#new-card-number', '****************');
            await page.type('#new-card-expiry', '06/26');
            await page.type('#new-card-cvc', '456');
            steps.push('✅ New card details entered');

            // Save new payment method
            await page.click('#save-payment-method-btn');
            await page.waitForSelector('#payment-method-saved', { timeout: 5000 });
            steps.push('✅ New payment method saved');

            // Set as default
            await page.click('#set-default-payment-btn');
            await page.waitForSelector('#default-payment-updated', { timeout: 3000 });
            steps.push('✅ Default payment method updated');

            // Verify update
            const updatedCard = await page.$eval('#current-payment-method', el => el.textContent);
            if (updatedCard.includes('5555')) {
                steps.push('✅ Payment method update verified');
            }

            return {
                success: true,
                details: steps.join('\n'),
                data: { updatedCard }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testSubscriptionDowngrade(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate premium user
            await page.evaluate(() => {
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-789',
                    email: '<EMAIL>',
                    subscription: { tier: 'premium', status: 'active' }
                }));
            });
            await page.reload();

            // Navigate to subscription management
            await page.click('#manage-subscription-btn');
            await page.waitForSelector('#subscription-management', { timeout: 3000 });
            steps.push('✅ Subscription management loaded');

            // Initiate downgrade
            await page.click('#downgrade-subscription-btn');
            await page.waitForSelector('#downgrade-options', { timeout: 3000 });
            steps.push('✅ Downgrade options displayed');

            // Select basic tier
            await page.click('#basic-tier-select');
            await page.waitForSelector('#downgrade-confirmation', { timeout: 3000 });
            steps.push('✅ Basic tier selected');

            // Confirm downgrade
            await page.click('#confirm-downgrade-btn');
            await page.waitForSelector('#downgrade-scheduled', { timeout: 5000 });
            steps.push('✅ Downgrade scheduled for end of billing period');

            // Verify downgrade scheduling
            const downgradeInfo = await page.$eval('#downgrade-info', el => el.textContent);
            if (downgradeInfo.includes('end of current period')) {
                steps.push('✅ Downgrade properly scheduled');
            }

            // Test immediate feature restriction
            await page.click('#premium-feature-test');
            await page.waitForSelector('#feature-restriction-notice', { timeout: 3000 });
            steps.push('✅ Premium features will be restricted after period end');

            return {
                success: true,
                details: steps.join('\n'),
                data: { downgradeInfo }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testBillingCycleTransition(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate user near billing cycle end
            await page.evaluate(() => {
                const nextBilling = new Date();
                nextBilling.setDate(nextBilling.getDate() + 2);
                
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-billing',
                    email: '<EMAIL>',
                    subscription: { 
                        tier: 'premium', 
                        status: 'active',
                        nextBillingDate: nextBilling.toISOString(),
                        amount: 9.99
                    }
                }));
            });
            await page.reload();

            // Check billing information
            await page.click('#billing-info-tab');
            await page.waitForSelector('#next-billing-date', { timeout: 3000 });
            steps.push('✅ Billing information displayed');

            // Verify upcoming billing notification
            const billingNotice = await page.$eval('#billing-notice', el => el.textContent);
            if (billingNotice.includes('upcoming')) {
                steps.push('✅ Upcoming billing notification shown');
            }

            // Simulate billing cycle completion
            await page.click('#simulate-billing-cycle-btn');
            await page.waitForSelector('#billing-processed', { timeout: 5000 });
            steps.push('✅ Billing cycle simulation completed');

            // Verify subscription renewal
            const renewalStatus = await page.$eval('#renewal-status', el => el.textContent);
            if (renewalStatus.includes('renewed')) {
                steps.push('✅ Subscription successfully renewed');
            }

            // Check new billing period
            const newBillingDate = await page.$eval('#next-billing-date', el => el.textContent);
            steps.push(`✅ New billing period set: ${newBillingDate}`);

            return {
                success: true,
                details: steps.join('\n'),
                data: { renewalStatus, newBillingDate }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testFailedPaymentHandling(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate user with failed payment
            await page.evaluate(() => {
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-failed',
                    email: '<EMAIL>',
                    subscription: { 
                        tier: 'premium', 
                        status: 'past_due',
                        paymentFailed: true,
                        retryDate: new Date(Date.now() + ********).toISOString()
                    }
                }));
            });
            await page.reload();

            // Check payment failure notification
            await page.waitForSelector('#payment-failure-notice', { timeout: 3000 });
            steps.push('✅ Payment failure notification displayed');

            // Verify account status
            const accountStatus = await page.$eval('#account-status', el => el.textContent);
            if (accountStatus.includes('Past Due')) {
                steps.push('✅ Account status shows past due');
            }

            // Test retry payment
            await page.click('#retry-payment-btn');
            await page.waitForSelector('#payment-retry-form', { timeout: 3000 });
            steps.push('✅ Payment retry form displayed');

            // Update payment method
            await page.type('#retry-card-number', '****************');
            await page.type('#retry-card-expiry', '12/25');
            await page.type('#retry-card-cvc', '123');
            steps.push('✅ Updated payment details entered');

            // Process retry
            await page.click('#process-retry-btn');
            await page.waitForSelector('#payment-retry-success', { timeout: 10000 });
            steps.push('✅ Payment retry successful');

            // Verify account reactivation
            const newStatus = await page.$eval('#account-status', el => el.textContent);
            if (newStatus.includes('Active')) {
                steps.push('✅ Account reactivated after successful payment');
            }

            return {
                success: true,
                details: steps.join('\n'),
                data: { accountStatus, newStatus }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testSubscriptionPause(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate active premium user
            await page.evaluate(() => {
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-pause',
                    email: '<EMAIL>',
                    subscription: { tier: 'premium', status: 'active' }
                }));
            });
            await page.reload();

            // Navigate to subscription management
            await page.click('#manage-subscription-btn');
            await page.waitForSelector('#subscription-management', { timeout: 3000 });
            steps.push('✅ Subscription management loaded');

            // Initiate pause
            await page.click('#pause-subscription-btn');
            await page.waitForSelector('#pause-options', { timeout: 3000 });
            steps.push('✅ Pause options displayed');

            // Select pause duration
            await page.select('#pause-duration', '30');
            await page.type('#pause-reason', 'Temporary break');
            steps.push('✅ Pause duration and reason selected');

            // Confirm pause
            await page.click('#confirm-pause-btn');
            await page.waitForSelector('#subscription-paused', { timeout: 5000 });
            steps.push('✅ Subscription paused successfully');

            // Verify pause status
            const pauseStatus = await page.$eval('#pause-status', el => el.textContent);
            if (pauseStatus.includes('Paused')) {
                steps.push('✅ Pause status displayed correctly');
            }

            // Check resume date
            const resumeDate = await page.$eval('#resume-date', el => el.textContent);
            steps.push(`✅ Resume date set: ${resumeDate}`);

            // Test early resume option
            await page.click('#resume-early-btn');
            await page.waitForSelector('#resume-confirmation', { timeout: 3000 });
            steps.push('✅ Early resume option available');

            return {
                success: true,
                details: steps.join('\n'),
                data: { pauseStatus, resumeDate }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testSubscriptionCancellation(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate active user
            await page.evaluate(() => {
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-cancel',
                    email: '<EMAIL>',
                    subscription: { tier: 'premium', status: 'active' }
                }));
            });
            await page.reload();

            // Navigate to cancellation
            await page.click('#cancel-subscription-btn');
            await page.waitForSelector('#cancellation-flow', { timeout: 3000 });
            steps.push('✅ Cancellation flow initiated');

            // Retention offer
            await page.waitForSelector('#retention-offer', { timeout: 3000 });
            steps.push('✅ Retention offer displayed');

            // Decline retention offer
            await page.click('#decline-retention-btn');
            await page.waitForSelector('#cancellation-survey', { timeout: 3000 });
            steps.push('✅ Cancellation survey displayed');

            // Fill survey
            await page.click('#reason-too-expensive');
            await page.type('#cancellation-feedback', 'Testing cancellation flow');
            steps.push('✅ Cancellation survey completed');

            // Confirm cancellation
            await page.click('#confirm-cancellation-btn');
            await page.waitForSelector('#cancellation-confirmed', { timeout: 5000 });
            steps.push('✅ Cancellation confirmed');

            // Verify cancellation details
            const cancellationInfo = await page.$eval('#cancellation-info', el => el.textContent);
            if (cancellationInfo.includes('end of current period')) {
                steps.push('✅ Cancellation scheduled for period end');
            }

            // Check access retention
            const accessInfo = await page.$eval('#access-retention-info', el => el.textContent);
            steps.push(`✅ Access retention info: ${accessInfo}`);

            return {
                success: true,
                details: steps.join('\n'),
                data: { cancellationInfo, accessInfo }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testReactivation(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate canceled user
            await page.evaluate(() => {
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-reactivate',
                    email: '<EMAIL>',
                    subscription: { 
                        tier: 'free', 
                        status: 'canceled',
                        previousTier: 'premium',
                        canceledAt: new Date(Date.now() - ********).toISOString()
                    }
                }));
            });
            await page.reload();

            // Check reactivation offer
            await page.waitForSelector('#reactivation-offer', { timeout: 3000 });
            steps.push('✅ Reactivation offer displayed');

            // View reactivation options
            await page.click('#view-reactivation-options-btn');
            await page.waitForSelector('#reactivation-tiers', { timeout: 3000 });
            steps.push('✅ Reactivation tier options shown');

            // Select previous tier
            await page.click('#reactivate-premium-btn');
            await page.waitForSelector('#reactivation-payment-form', { timeout: 3000 });
            steps.push('✅ Premium reactivation selected');

            // Enter payment details
            await page.type('#reactivation-card-number', '****************');
            await page.type('#reactivation-card-expiry', '12/25');
            await page.type('#reactivation-card-cvc', '123');
            steps.push('✅ Payment details entered for reactivation');

            // Process reactivation
            await page.click('#process-reactivation-btn');
            await page.waitForSelector('#reactivation-success', { timeout: 10000 });
            steps.push('✅ Subscription reactivated successfully');

            // Verify restored access
            const subscriptionStatus = await page.$eval('#subscription-status', el => el.textContent);
            if (subscriptionStatus.includes('Premium')) {
                steps.push('✅ Premium access restored');
            }

            // Test premium features
            await page.click('#premium-feature-test');
            await page.waitForSelector('#feature-access-granted', { timeout: 3000 });
            steps.push('✅ Premium features accessible after reactivation');

            return {
                success: true,
                details: steps.join('\n'),
                data: { subscriptionStatus }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async testProrationCalculation(page) {
        const steps = [];

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Simulate user mid-billing cycle
            await page.evaluate(() => {
                const billingStart = new Date();
                billingStart.setDate(billingStart.getDate() - 15);
                const billingEnd = new Date();
                billingEnd.setDate(billingEnd.getDate() + 15);
                
                localStorage.setItem('demo-user', JSON.stringify({
                    id: 'test-user-proration',
                    email: '<EMAIL>',
                    subscription: { 
                        tier: 'basic', 
                        status: 'active',
                        billingPeriodStart: billingStart.toISOString(),
                        billingPeriodEnd: billingEnd.toISOString(),
                        amount: 4.99
                    }
                }));
            });
            await page.reload();

            // Initiate upgrade mid-cycle
            await page.click('#upgrade-subscription-btn');
            await page.waitForSelector('#upgrade-options', { timeout: 3000 });
            steps.push('✅ Upgrade options displayed');

            // Select premium tier
            await page.click('#premium-tier-select');
            await page.waitForSelector('#proration-calculation', { timeout: 3000 });
            steps.push('✅ Proration calculation displayed');

            // Verify proration details
            const prorationAmount = await page.$eval('#proration-amount', el => el.textContent);
            const prorationDays = await page.$eval('#proration-days', el => el.textContent);
            
            steps.push(`✅ Proration amount: ${prorationAmount}`);
            steps.push(`✅ Proration days: ${prorationDays}`);

            // Confirm upgrade with proration
            await page.click('#confirm-prorated-upgrade-btn');
            await page.waitForSelector('#proration-processed', { timeout: 5000 });
            steps.push('✅ Prorated upgrade processed');

            // Verify new billing amount
            const newBillingAmount = await page.$eval('#new-billing-amount', el => el.textContent);
            steps.push(`✅ New billing amount: ${newBillingAmount}`);

            // Check next billing date adjustment
            const adjustedBillingDate = await page.$eval('#next-billing-date', el => el.textContent);
            steps.push(`✅ Billing date maintained: ${adjustedBillingDate}`);

            return {
                success: true,
                details: steps.join('\n'),
                data: { 
                    prorationAmount, 
                    prorationDays, 
                    newBillingAmount,
                    adjustedBillingDate 
                }
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                errors: [error.message]
            };
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    generateReport() {
        const totalDuration = Date.now() - this.startTime;
        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const failed = this.testResults.filter(r => r.status === 'FAILED').length;
        const errors = this.testResults.filter(r => r.status === 'ERROR').length;
        const total = this.testResults.length;

        console.log('\n🔄 Subscription Lifecycle Test Results');
        console.log('======================================');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed} ✅`);
        console.log(`Failed: ${failed} ❌`);
        console.log(`Errors: ${errors} 💥`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        // Generate detailed report
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total,
                passed,
                failed,
                errors,
                successRate: ((passed / total) * 100).toFixed(1),
                totalDuration
            },
            results: this.testResults
        };

        // Ensure reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        fs.writeFileSync(
            path.join(reportsDir, 'subscription-lifecycle-test-report.json'),
            JSON.stringify(report, null, 2)
        );

        console.log('\n📄 Report saved to test-reports/subscription-lifecycle-test-report.json');
    }
}

// Run tests if called directly
if (require.main === module) {
    const runner = new SubscriptionLifecycleTests();
    runner.runAllTests().catch(console.error);
}

module.exports = SubscriptionLifecycleTests;