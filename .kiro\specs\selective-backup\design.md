# Design Document: Selective Backup Feature

## Overview

The Selective Backup feature will provide users with the ability to selectively back up specific types of data from their device: contacts, SMS messages, and photos (explicitly excluding videos). This design document outlines the architecture, components, interfaces, data models, and implementation strategy for this feature.

## Architecture

The Selective Backup feature will follow a modular architecture with separate services for each data type (contacts, SMS messages, photos) while sharing common infrastructure for backup operations, encryption, and cloud storage. The architecture will consist of the following layers:

1. **UI Layer**: Provides user interface for configuring backup settings and monitoring backup progress
2. **Service Layer**: Contains specialized services for each data type and orchestration of the backup process
3. **Encryption Layer**: Handles secure encryption of all backed-up data
4. **Storage Layer**: Manages cloud storage operations for backup data

```mermaid
graph TD
    A[User Interface] --> B[Backup Manager]
    B --> C1[Contact Backup Service]
    B --> C2[Message Backup Service]
    B --> C3[Photo Backup Service]
    C1 --> D[Encryption Service]
    C2 --> D
    C3 --> D
    D --> E[Cloud Storage Service]
    F[Permission Manager] --> C1
    F --> C2
    F --> C3
    G[Backup Configuration] --> B
```

## Components and Interfaces

### 1. BackupConfigurationService

This service will manage user preferences for backup selection and scheduling.

```typescript
interface BackupConfiguration {
  enableContactBackup: boolean;
  enableMessageBackup: boolean;
  enablePhotoBackup: boolean;
  autoBackupEnabled: boolean;
  autoBackupFrequency: 'daily' | 'weekly' | 'monthly';
  autoBackupWifiOnly: boolean;
  lastBackupDate?: Date;
}

interface BackupConfigurationService {
  getBackupConfiguration(): Promise<BackupConfiguration>;
  updateBackupConfiguration(config: Partial<BackupConfiguration>): Promise<void>;
  isBackupInProgress(): boolean;
  getBackupStatus(): BackupStatus;
}
```

### 2. BackupManager

This service will orchestrate the backup process across all selected data types.

```typescript
interface BackupProgress {
  status: 'preparing' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  currentStep: string;
  overallProgress: number; // 0-100
  dataTypeProgress: {
    contacts?: { total: number; processed: number; };
    messages?: { total: number; processed: number; };
    photos?: { total: number; processed: number; };
  };
  error?: string;
}

interface BackupResult {
  success: boolean;
  timestamp: Date;
  contacts?: { total: number; backed_up: number; skipped: number; };
  messages?: { total: number; backed_up: number; skipped: number; };
  photos?: { total: number; backed_up: number; skipped: number; };
  errors: string[];
  duration: number;
}

interface BackupManager {
  startBackup(options?: { contacts?: boolean; messages?: boolean; photos?: boolean; }): Promise<void>;
  cancelBackup(): Promise<void>;
  getBackupProgress(): BackupProgress;
  getLastBackupResult(): BackupResult | null;
  registerProgressCallback(callback: (progress: BackupProgress) => void): void;
  unregisterProgressCallback(): void;
}
```

### 3. ContactBackupService

This service will handle the backup of contacts.

```typescript
interface ContactData {
  id: string;
  displayName: string;
  phoneNumbers: Array<{ label: string; number: string; }>;
  emails: Array<{ label: string; email: string; }>;
  addresses: Array<{ label: string; formattedAddress: string; }>;
  company?: string;
  jobTitle?: string;
  note?: string;
  avatar?: string; // Base64 encoded image
}

interface ContactBackupService {
  requestContactPermission(): Promise<boolean>;
  scanContacts(): Promise<ContactData[]>;
  backupContacts(onProgress?: (progress: { total: number; processed: number; }) => void): Promise<{
    success: boolean;
    totalContacts: number;
    backedUpContacts: number;
    skippedContacts: number;
    errors: string[];
  }>;
  restoreContacts(): Promise<{ success: boolean; contacts?: ContactData[]; error?: string; }>;
}
```

### 4. MessageBackupService

This service already exists and will be used with minimal modifications to integrate with the selective backup feature.

### 5. PhotoBackupService

This service will handle the backup of photos while explicitly excluding videos.

```typescript
interface MediaAsset {
  id: string;
  uri: string;
  filename: string;
  fileSize: number;
  width: number;
  height: number;
  creationTime: number;
  modificationTime: number;
  mediaType: 'photo' | 'video';
  mimeType: string;
  location?: { latitude: number; longitude: number; };
}

interface PhotoBackupService {
  requestPhotoLibraryPermission(): Promise<boolean>;
  scanPhotoLibrary(options: { photosOnly: boolean }): Promise<MediaAsset[]>;
  backupPhotos(onProgress?: (progress: { total: number; processed: number; }) => void): Promise<{
    success: boolean;
    totalPhotos: number;
    backedUpPhotos: number;
    skippedPhotos: number;
    errors: string[];
  }>;
  restorePhotos(): Promise<{ success: boolean; photos?: MediaAsset[]; error?: string; }>;
  isMediaTypePhoto(asset: MediaAsset): boolean;
}
```

### 6. PermissionManager

This service will centralize permission handling for all required data types.

```typescript
interface PermissionStatus {
  contacts: boolean;
  messages: boolean;
  photos: boolean;
}

interface PermissionManager {
  checkPermissions(): Promise<PermissionStatus>;
  requestPermissions(types: Array<'contacts' | 'messages' | 'photos'>): Promise<PermissionStatus>;
  handlePermissionDenied(type: 'contacts' | 'messages' | 'photos'): void;
}
```

## Data Models

### Backup Metadata

```typescript
interface BackupMetadata {
  version: string;
  userId: string;
  deviceInfo: {
    deviceId: string;
    model: string;
    osVersion: string;
  };
  timestamp: string;
  dataTypes: {
    contacts: boolean;
    messages: boolean;
    photos: boolean;
  };
  stats: {
    contacts?: { count: number; size: number; };
    messages?: { count: number; threads: number; size: number; };
    photos?: { count: number; size: number; };
    totalSize: number;
  };
}
```

### Backup File Structure

The backup data will be organized in the cloud storage as follows:

```
/users/{userId}/backups/
  ├── metadata.json
  ├── contacts/
  │   └── contacts_backup_{timestamp}.json
  ├── messages/
  │   └── messages_backup_{timestamp}.json
  └── photos/
      ├── photos_metadata_{timestamp}.json
      └── photos/
          ├── {encrypted_photo_id_1}.jpg
          ├── {encrypted_photo_id_2}.jpg
          └── ...
```

## Error Handling

The error handling strategy will include:

1. **Graceful Degradation**: If one data type fails to back up, the system will continue with other data types
2. **Retry Mechanism**: For transient errors (network issues, etc.), the system will implement automatic retries with exponential backoff
3. **Error Reporting**: Detailed error logs will be maintained and presented to the user in a digestible format
4. **Recovery Options**: For interrupted backups, the system will provide options to resume from the last successful point

Error codes will be standardized across all backup services:

- `PERMISSION_DENIED`: User denied required permissions
- `NETWORK_ERROR`: Network connectivity issues
- `STORAGE_FULL`: Cloud storage quota exceeded
- `ENCRYPTION_ERROR`: Issues with data encryption
- `AUTHENTICATION_ERROR`: User authentication issues
- `UNKNOWN_ERROR`: Unexpected errors

## Testing Strategy

The testing strategy will include:

1. **Unit Tests**:
   - Test each service in isolation with mock dependencies
   - Verify correct handling of edge cases (empty data sets, permission denied, etc.)
   - Test file type detection for photos vs. videos

2. **Integration Tests**:
   - Test interaction between services
   - Verify backup and restore workflows end-to-end
   - Test encryption and decryption of backup data

3. **Performance Tests**:
   - Test with large data sets (thousands of contacts, messages, photos)
   - Measure memory usage during backup operations
   - Verify backup speed and resource usage

4. **User Acceptance Testing**:
   - Verify UI feedback during backup operations
   - Test user flow for configuring backup settings
   - Validate error messages and recovery options

## Implementation Considerations

### Platform-Specific Implementations

#### Android
- Use ContentResolver to access contacts and SMS messages
- Use MediaStore API for accessing photos while filtering out videos
- Implement foreground service for long-running backup operations

#### iOS
- Use CNContact framework for contacts
- Use PhotoKit for accessing photos while filtering out videos
- Note: iOS does not provide direct SMS access; consider alternative approaches

### Performance Optimization

1. **Chunked Processing**: Process data in chunks to avoid memory issues with large data sets
2. **Incremental Backups**: After initial backup, only back up new or modified data
3. **Compression**: Compress data before encryption to reduce storage and transfer size
4. **Background Processing**: Perform intensive operations in background threads

### Security Considerations

1. **Data Encryption**: All backed-up data will be encrypted using the existing EncryptionService
2. **Secure Transfer**: Use secure protocols for data transfer to cloud storage
3. **Authentication**: Require user authentication before accessing backup settings or initiating restore
4. **Data Validation**: Validate all data during restore to prevent security issues

## User Experience

The user experience will focus on:

1. **Simplicity**: Clear toggles for selecting which data types to back up
2. **Transparency**: Real-time progress indicators during backup operations
3. **Control**: Options to pause, resume, or cancel backup operations
4. **Feedback**: Clear success/error messages and backup statistics
5. **Automation**: Options for scheduling automatic backups

## Future Enhancements

Potential future enhancements include:

1. **Selective Restore**: Allow users to restore specific items rather than entire data sets
2. **Cross-Platform Restore**: Enable restoring data across different device platforms
3. **Backup Versioning**: Maintain multiple versions of backups for point-in-time recovery
4. **Backup Analytics**: Provide insights into backup patterns and storage usage