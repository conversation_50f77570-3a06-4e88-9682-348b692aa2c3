/**
 * Performance Monitor - Client-side performance monitoring and analytics
 * Implements requirements 6.1, 6.2, 6.3, 6.4 for comprehensive backup analytics and monitoring
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            pageLoad: {},
            userInteractions: [],
            backupPerformance: [],
            errorLogs: [],
            resourceUsage: {},
            networkMetrics: {}
        };
        
        this.observers = {
            performance: null,
            intersection: null,
            mutation: null
        };
        
        this.heatmapData = [];
        this.abTestConfig = {};
        this.isMonitoring = false;
        
        this.init();
    }

    init() {
        this.setupPerformanceObserver();
        this.setupUserInteractionTracking();
        this.setupResourceMonitoring();
        this.setupNetworkMonitoring();
        this.setupHeatmapTracking();
        this.loadABTestConfig();
        
        console.log('Performance Monitor initialized');
    }

    // Performance Observer for Web Vitals and timing metrics
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // Core Web Vitals
            this.observers.performance = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordPerformanceEntry(entry);
                }
            });

            // Observe different entry types
            try {
                this.observers.performance.observe({ entryTypes: ['navigation', 'resource', 'measure', 'paint'] });
            } catch (e) {
                console.warn('Some performance entry types not supported:', e);
            }
        }

        // Manual Web Vitals collection
        this.collectWebVitals();
    }

    collectWebVitals() {
        // First Contentful Paint
        const paintEntries = performance.getEntriesByType('paint');
        paintEntries.forEach(entry => {
            if (entry.name === 'first-contentful-paint') {
                this.metrics.pageLoad.fcp = entry.startTime;
            }
        });

        // Largest Contentful Paint (if supported)
        if ('LargestContentfulPaint' in window) {
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.pageLoad.lcp = lastEntry.startTime;
            }).observe({ entryTypes: ['largest-contentful-paint'] });
        }

        // Cumulative Layout Shift
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            }
            this.metrics.pageLoad.cls = clsValue;
        }).observe({ entryTypes: ['layout-shift'] });
    }

    recordPerformanceEntry(entry) {
        const timestamp = Date.now();
        
        switch (entry.entryType) {
            case 'navigation':
                this.metrics.pageLoad = {
                    ...this.metrics.pageLoad,
                    domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                    loadComplete: entry.loadEventEnd - entry.loadEventStart,
                    domInteractive: entry.domInteractive - entry.fetchStart,
                    timestamp
                };
                break;
                
            case 'resource':
                if (entry.name.includes('backup') || entry.name.includes('restore')) {
                    this.recordBackupResourceMetric(entry);
                }
                break;
                
            case 'measure':
                this.recordCustomMeasure(entry);
                break;
        }
    }

    recordBackupResourceMetric(entry) {
        this.metrics.backupPerformance.push({
            name: entry.name,
            duration: entry.duration,
            transferSize: entry.transferSize,
            encodedBodySize: entry.encodedBodySize,
            decodedBodySize: entry.decodedBodySize,
            timestamp: Date.now()
        });
    }

    recordCustomMeasure(entry) {
        if (!this.metrics.customMeasures) {
            this.metrics.customMeasures = [];
        }
        
        this.metrics.customMeasures.push({
            name: entry.name,
            duration: entry.duration,
            startTime: entry.startTime,
            timestamp: Date.now()
        });
    }

    // User Interaction Tracking
    setupUserInteractionTracking() {
        const events = ['click', 'scroll', 'keydown', 'touchstart', 'mousemove'];
        
        events.forEach(eventType => {
            document.addEventListener(eventType, (event) => {
                this.recordUserInteraction(eventType, event);
            }, { passive: true });
        });
    }

    recordUserInteraction(type, event) {
        const interaction = {
            type,
            timestamp: Date.now(),
            target: event.target.tagName,
            targetId: event.target.id,
            targetClass: event.target.className,
            x: event.clientX || 0,
            y: event.clientY || 0,
            page: window.location.pathname
        };

        // Add to heatmap data for click events
        if (type === 'click' && event.clientX && event.clientY) {
            this.heatmapData.push({
                x: event.clientX,
                y: event.clientY,
                timestamp: Date.now(),
                page: window.location.pathname
            });
        }

        this.metrics.userInteractions.push(interaction);
        
        // Keep only last 1000 interactions to prevent memory issues
        if (this.metrics.userInteractions.length > 1000) {
            this.metrics.userInteractions = this.metrics.userInteractions.slice(-1000);
        }
    }

    // Resource Usage Monitoring
    setupResourceMonitoring() {
        // Memory usage (if supported)
        if ('memory' in performance) {
            setInterval(() => {
                this.metrics.resourceUsage = {
                    ...this.metrics.resourceUsage,
                    memory: {
                        used: performance.memory.usedJSHeapSize,
                        total: performance.memory.totalJSHeapSize,
                        limit: performance.memory.jsHeapSizeLimit,
                        timestamp: Date.now()
                    }
                };
            }, 5000);
        }

        // Connection information
        if ('connection' in navigator) {
            this.metrics.resourceUsage.connection = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData
            };
        }
    }

    // Network Performance Monitoring
    setupNetworkMonitoring() {
        // Monitor fetch requests
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0];
            
            try {
                const response = await originalFetch(...args);
                const endTime = performance.now();
                
                this.recordNetworkMetric({
                    url,
                    method: args[1]?.method || 'GET',
                    status: response.status,
                    duration: endTime - startTime,
                    success: response.ok,
                    timestamp: Date.now()
                });
                
                return response;
            } catch (error) {
                const endTime = performance.now();
                
                this.recordNetworkMetric({
                    url,
                    method: args[1]?.method || 'GET',
                    status: 0,
                    duration: endTime - startTime,
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                });
                
                throw error;
            }
        };
    }

    recordNetworkMetric(metric) {
        if (!this.metrics.networkMetrics.requests) {
            this.metrics.networkMetrics.requests = [];
        }
        
        this.metrics.networkMetrics.requests.push(metric);
        
        // Keep only last 500 requests
        if (this.metrics.networkMetrics.requests.length > 500) {
            this.metrics.networkMetrics.requests = this.metrics.networkMetrics.requests.slice(-500);
        }
    }

    // Heatmap Tracking
    setupHeatmapTracking() {
        // Track scroll depth
        let maxScrollDepth = 0;
        window.addEventListener('scroll', () => {
            const scrollDepth = (window.scrollY + window.innerHeight) / document.body.scrollHeight;
            if (scrollDepth > maxScrollDepth) {
                maxScrollDepth = scrollDepth;
                this.recordScrollDepth(scrollDepth);
            }
        }, { passive: true });
    }

    recordScrollDepth(depth) {
        if (!this.metrics.scrollDepth) {
            this.metrics.scrollDepth = [];
        }
        
        this.metrics.scrollDepth.push({
            depth,
            timestamp: Date.now(),
            page: window.location.pathname
        });
    }

    // A/B Testing Framework
    loadABTestConfig() {
        // Load A/B test configuration from localStorage or API
        const savedConfig = localStorage.getItem('abTestConfig');
        if (savedConfig) {
            this.abTestConfig = JSON.parse(savedConfig);
        } else {
            // Default A/B test configuration
            this.abTestConfig = {
                backupButtonColor: Math.random() < 0.5 ? 'blue' : 'green',
                progressBarStyle: Math.random() < 0.5 ? 'linear' : 'circular',
                dashboardLayout: Math.random() < 0.5 ? 'grid' : 'list',
                userId: this.generateUserId()
            };
            localStorage.setItem('abTestConfig', JSON.stringify(this.abTestConfig));
        }
    }

    generateUserId() {
        return 'user_' + Math.random().toString(36).substr(2, 9);
    }

    getABTestVariant(testName) {
        return this.abTestConfig[testName] || 'default';
    }

    recordABTestEvent(testName, variant, event, value = null) {
        if (!this.metrics.abTests) {
            this.metrics.abTests = [];
        }
        
        this.metrics.abTests.push({
            testName,
            variant,
            event,
            value,
            userId: this.abTestConfig.userId,
            timestamp: Date.now(),
            page: window.location.pathname
        });
    }

    // Backup Performance Tracking
    startBackupPerformanceTracking(backupId, type) {
        const startTime = performance.now();
        performance.mark(`backup-${backupId}-start`);
        
        return {
            backupId,
            type,
            startTime,
            startTimestamp: Date.now()
        };
    }

    endBackupPerformanceTracking(trackingData, success, filesProcessed, totalSize) {
        const endTime = performance.now();
        const duration = endTime - trackingData.startTime;
        
        performance.mark(`backup-${trackingData.backupId}-end`);
        performance.measure(
            `backup-${trackingData.backupId}`,
            `backup-${trackingData.backupId}-start`,
            `backup-${trackingData.backupId}-end`
        );
        
        const performanceData = {
            backupId: trackingData.backupId,
            type: trackingData.type,
            duration,
            success,
            filesProcessed,
            totalSize,
            throughput: totalSize / (duration / 1000), // bytes per second
            timestamp: Date.now(),
            startTimestamp: trackingData.startTimestamp
        };
        
        this.metrics.backupPerformance.push(performanceData);
        
        // Trigger performance analysis
        this.analyzeBackupPerformance();
        
        return performanceData;
    }

    analyzeBackupPerformance() {
        const recentBackups = this.metrics.backupPerformance.slice(-10);
        if (recentBackups.length < 3) return;
        
        const avgDuration = recentBackups.reduce((sum, b) => sum + b.duration, 0) / recentBackups.length;
        const successRate = recentBackups.filter(b => b.success).length / recentBackups.length;
        const avgThroughput = recentBackups.reduce((sum, b) => sum + (b.throughput || 0), 0) / recentBackups.length;
        
        // Generate performance recommendations
        const recommendations = this.generatePerformanceRecommendations({
            avgDuration,
            successRate,
            avgThroughput,
            recentBackups
        });
        
        this.metrics.performanceAnalysis = {
            avgDuration,
            successRate,
            avgThroughput,
            recommendations,
            timestamp: Date.now()
        };
    }

    generatePerformanceRecommendations(analysis) {
        const recommendations = [];
        
        if (analysis.successRate < 0.9) {
            recommendations.push({
                type: 'reliability',
                severity: 'high',
                message: 'Backup success rate is below 90%. Check network connectivity and error logs.',
                action: 'review_errors'
            });
        }
        
        if (analysis.avgDuration > 30000) { // 30 seconds
            recommendations.push({
                type: 'performance',
                severity: 'medium',
                message: 'Average backup duration is high. Consider optimizing file processing.',
                action: 'optimize_processing'
            });
        }
        
        if (analysis.avgThroughput < 1000000) { // 1MB/s
            recommendations.push({
                type: 'network',
                severity: 'medium',
                message: 'Network throughput is low. Check connection quality.',
                action: 'check_network'
            });
        }
        
        // Memory usage recommendations
        if (this.metrics.resourceUsage.memory) {
            const memoryUsage = this.metrics.resourceUsage.memory.used / this.metrics.resourceUsage.memory.total;
            if (memoryUsage > 0.8) {
                recommendations.push({
                    type: 'memory',
                    severity: 'high',
                    message: 'High memory usage detected. Consider reducing concurrent operations.',
                    action: 'reduce_concurrency'
                });
            }
        }
        
        return recommendations;
    }

    // Error Logging and Analysis
    recordError(error, context = {}) {
        const errorData = {
            message: error.message,
            stack: error.stack,
            name: error.name,
            context,
            timestamp: Date.now(),
            page: window.location.pathname,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        this.metrics.errorLogs.push(errorData);
        
        // Keep only last 100 errors
        if (this.metrics.errorLogs.length > 100) {
            this.metrics.errorLogs = this.metrics.errorLogs.slice(-100);
        }
        
        // Analyze error patterns
        this.analyzeErrorPatterns();
    }

    analyzeErrorPatterns() {
        const recentErrors = this.metrics.errorLogs.slice(-20);
        const errorCounts = {};
        
        recentErrors.forEach(error => {
            const key = error.name + ':' + error.message;
            errorCounts[key] = (errorCounts[key] || 0) + 1;
        });
        
        // Find frequent errors
        const frequentErrors = Object.entries(errorCounts)
            .filter(([, count]) => count >= 3)
            .map(([error, count]) => ({ error, count }));
        
        if (frequentErrors.length > 0) {
            this.metrics.errorAnalysis = {
                frequentErrors,
                totalErrors: recentErrors.length,
                timestamp: Date.now()
            };
        }
    }

    // Data Export and Reporting
    getMetricsSummary() {
        return {
            pageLoad: this.metrics.pageLoad,
            backupPerformance: {
                total: this.metrics.backupPerformance.length,
                recent: this.metrics.backupPerformance.slice(-10),
                analysis: this.metrics.performanceAnalysis
            },
            userInteractions: {
                total: this.metrics.userInteractions.length,
                recent: this.metrics.userInteractions.slice(-50)
            },
            errors: {
                total: this.metrics.errorLogs.length,
                recent: this.metrics.errorLogs.slice(-10),
                analysis: this.metrics.errorAnalysis
            },
            resourceUsage: this.metrics.resourceUsage,
            networkMetrics: this.metrics.networkMetrics,
            heatmapData: this.heatmapData.slice(-100),
            abTests: this.metrics.abTests || []
        };
    }

    exportMetrics() {
        const data = {
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics: this.getMetricsSummary()
        };
        
        return JSON.stringify(data, null, 2);
    }

    // Real-time monitoring control
    startMonitoring() {
        this.isMonitoring = true;
        console.log('Performance monitoring started');
    }

    stopMonitoring() {
        this.isMonitoring = false;
        
        // Disconnect observers
        if (this.observers.performance) {
            this.observers.performance.disconnect();
        }
        
        console.log('Performance monitoring stopped');
    }

    // Utility methods
    clearMetrics() {
        this.metrics = {
            pageLoad: {},
            userInteractions: [],
            backupPerformance: [],
            errorLogs: [],
            resourceUsage: {},
            networkMetrics: {}
        };
        this.heatmapData = [];
    }
}

// Global error handler
window.addEventListener('error', (event) => {
    if (window.performanceMonitor) {
        window.performanceMonitor.recordError(event.error, {
            type: 'javascript',
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
        });
    }
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
    if (window.performanceMonitor) {
        window.performanceMonitor.recordError(new Error(event.reason), {
            type: 'promise_rejection'
        });
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
}