/**
 * Verification Script for Subscription Tier Management System
 * Quick verification of all subscription system components
 */

async function verifySubscriptionSystem() {
    console.log('🔍 Verifying Subscription Tier Management System...');
    
    try {
        // Check if all required classes are available
        const requiredClasses = [
            'StripeManager',
            'SubscriptionManager', 
            'SubscriptionTierConfig',
            'UsageQuotaManager',
            'SubscriptionSystemTester'
        ];
        
        const missingClasses = requiredClasses.filter(className => !window[className]);
        
        if (missingClasses.length > 0) {
            throw new Error(`Missing required classes: ${missingClasses.join(', ')}`);
        }
        
        console.log('✅ All required classes loaded');
        
        // Initialize components
        console.log('🔧 Initializing components...');
        
        const stripeManager = new StripeManager();
        const tierConfig = new SubscriptionTierConfig();
        const subscriptionManager = new SubscriptionManager(stripeManager);
        const quotaManager = new UsageQuotaManager(subscriptionManager, tierConfig);
        
        // Initialize Stripe
        await stripeManager.initialize();
        console.log('✅ Stripe Manager initialized');
        
        // Test tier configuration
        const tiers = tierConfig.getAllTiers();
        if (tiers.length !== 3) {
            throw new Error(`Expected 3 tiers, got ${tiers.length}`);
        }
        console.log('✅ Tier configuration verified');
        
        // Initialize subscription manager
        await subscriptionManager.initialize('demo_user_verification');
        console.log('✅ Subscription Manager initialized');
        
        // Test current subscription
        const currentSub = subscriptionManager.getCurrentSubscription();
        if (!currentSub || currentSub.tierId !== 'free') {
            throw new Error('Default subscription should be free tier');
        }
        console.log('✅ Default subscription verified');
        
        // Test usage tracking
        const trackingResult = quotaManager.trackUsage('demo_user_verification', 'storage', 0.1);
        if (!trackingResult) {
            throw new Error('Usage tracking should allow initial usage');
        }
        console.log('✅ Usage tracking verified');
        
        // Test quota checking
        const limits = quotaManager.checkUsageLimits('demo_user_verification');
        if (!limits.storage || limits.storage.current !== 0.1) {
            throw new Error('Usage limits not calculated correctly');
        }
        console.log('✅ Quota enforcement verified');
        
        // Test feature access
        const hasAdvancedEncryption = subscriptionManager.hasFeatureAccess('advancedEncryption');
        if (hasAdvancedEncryption) {
            throw new Error('Free tier should not have advanced encryption');
        }
        console.log('✅ Feature access control verified');
        
        // Test subscription upgrade
        await subscriptionManager.upgradeSubscription('basic');
        const upgradedSub = subscriptionManager.getCurrentSubscription();
        if (upgradedSub.tierId !== 'basic') {
            throw new Error('Subscription upgrade failed');
        }
        console.log('✅ Subscription upgrade verified');
        
        // Test feature access after upgrade
        const hasAdvancedEncryptionAfterUpgrade = subscriptionManager.hasFeatureAccess('advancedEncryption');
        if (!hasAdvancedEncryptionAfterUpgrade) {
            throw new Error('Basic tier should have advanced encryption');
        }
        console.log('✅ Feature access after upgrade verified');
        
        // Test subscription downgrade
        await subscriptionManager.downgradeToFree();
        const downgradedSub = subscriptionManager.getCurrentSubscription();
        if (downgradedSub.tierId !== 'free') {
            throw new Error('Subscription downgrade failed');
        }
        console.log('✅ Subscription downgrade verified');
        
        // Test database schema validation
        if (typeof window.fetch !== 'undefined') {
            try {
                const schemaResponse = await fetch('subscription-schema.sql');
                if (schemaResponse.ok) {
                    console.log('✅ Database schema file accessible');
                }
            } catch (error) {
                console.log('⚠️ Database schema file check skipped (fetch not available)');
            }
        }
        
        // Cleanup
        quotaManager.cleanup();
        
        console.log('🎉 Subscription Tier Management System verification completed successfully!');
        
        return {
            success: true,
            message: 'All subscription system components verified successfully',
            components: {
                stripeManager: '✅ Working',
                tierConfig: '✅ Working', 
                subscriptionManager: '✅ Working',
                quotaManager: '✅ Working',
                database: '✅ Schema available'
            }
        };
        
    } catch (error) {
        console.error('❌ Subscription system verification failed:', error);
        
        return {
            success: false,
            message: error.message,
            error: error
        };
    }
}

/**
 * Display verification results in UI
 */
function displayVerificationResults(results) {
    const container = document.createElement('div');
    container.id = 'subscription-verification-results';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${results.success ? '#d4edda' : '#f8d7da'};
        border: 1px solid ${results.success ? '#c3e6cb' : '#f5c6cb'};
        color: ${results.success ? '#155724' : '#721c24'};
        padding: 15px;
        border-radius: 8px;
        max-width: 400px;
        z-index: 10000;
        font-family: monospace;
        font-size: 12px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    `;
    
    container.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 10px;">
            ${results.success ? '✅' : '❌'} Subscription System Verification
        </div>
        <div style="margin-bottom: 10px;">
            ${results.message}
        </div>
        ${results.components ? `
            <div style="font-size: 11px;">
                ${Object.entries(results.components).map(([key, status]) => 
                    `<div>${key}: ${status}</div>`
                ).join('')}
            </div>
        ` : ''}
        <button onclick="this.parentElement.remove()" style="
            margin-top: 10px;
            padding: 5px 10px;
            border: none;
            background: ${results.success ? '#28a745' : '#dc3545'};
            color: white;
            border-radius: 4px;
            cursor: pointer;
        ">Close</button>
    `;
    
    document.body.appendChild(container);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (container.parentElement) {
            container.remove();
        }
    }, 10000);
}

/**
 * Run verification when page loads
 */
if (typeof window !== 'undefined') {
    window.verifySubscriptionSystem = verifySubscriptionSystem;
    window.displayVerificationResults = displayVerificationResults;
    
    // Auto-run verification if requested
    window.addEventListener('load', async () => {
        if (window.location.search.includes('verify=subscription')) {
            const results = await verifySubscriptionSystem();
            displayVerificationResults(results);
        }
    });
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { verifySubscriptionSystem };
}