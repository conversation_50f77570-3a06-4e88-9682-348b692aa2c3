"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const PricingController_1 = require("../controllers/PricingController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const pricingController = new PricingController_1.PricingController();
router.get('/combinations', auth_1.authenticateToken, (req, res) => {
    pricingController.getServiceCombinations(req, res);
});
router.post('/calculate', auth_1.authenticateToken, (req, res) => {
    pricingController.calculateOptimalPrice(req, res);
});
router.get('/recommendations/:userId', auth_1.authenticateToken, (req, res) => {
    pricingController.getPlanRecommendations(req, res);
});
exports.default = router;
//# sourceMappingURL=pricing.js.map