import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role?: string;
            };
        }
    }
}
export declare const authenticateToken: (req: Request, res: Response, next: NextFunction) => void;
export declare const requireRole: (role: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const extractUserId: (req: Request) => string | null;
//# sourceMappingURL=auth.d.ts.map