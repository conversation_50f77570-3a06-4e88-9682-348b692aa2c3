/**
 * Advanced Security Demonstrations Styles
 * Comprehensive styling for security demonstration components
 */

/* Security Demo Container */
.security-demo-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 30px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;
}

.demo-header h3 {
    font-size: 2rem;
    margin: 0 0 10px 0;
    font-weight: 700;
}

.demo-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Demo Stages */
.demo-stages {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin: 30px 0;
}

.stage {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 25px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.stage.active {
    opacity: 1;
    border-color: #4facfe;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
}

.stage h4 {
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    color: #4facfe;
    display: flex;
    align-items: center;
    gap: 10px;
}

.stage h4::before {
    content: '';
    width: 30px;
    height: 30px;
    background: #4facfe;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: bold;
    color: white;
}

.stage:nth-child(1) h4::before { content: '1'; }
.stage:nth-child(2) h4::before { content: '2'; }
.stage:nth-child(3) h4::before { content: '3'; }
.stage:nth-child(4) h4::before { content: '4'; }

/* Key Generation */
.key-generation {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.progress-container {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 8px;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    border-radius: 6px;
    width: 0%;
    transition: width 0.3s ease;
}

.key-details {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 20px;
    font-family: 'Courier New', monospace;
}

.key-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.key-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.key-item label {
    font-weight: 600;
    color: #4facfe;
    font-size: 0.9rem;
}

.key-item code {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    word-break: break-all;
}

.security-badge {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: 10px;
}

/* Data Comparison */
.data-comparison {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    align-items: center;
}

.original-data,
.encrypted-data {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 20px;
}

.original-data h5,
.encrypted-data h5 {
    margin: 0 0 15px 0;
    color: #4facfe;
    font-size: 1rem;
}

.data-preview {
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
}

.data-display pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
}

.encrypted-blob {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    word-break: break-all;
    font-size: 0.75rem;
    line-height: 1.3;
}

.encryption-metadata {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.meta-item:last-child {
    border-bottom: none;
}

.meta-item label {
    font-weight: 600;
    color: #4facfe;
    font-size: 0.85rem;
}

.encryption-arrow {
    font-size: 2rem;
    color: #4facfe;
    text-align: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Network Visualization */
.network-path {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin: 20px 0;
}

.network-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.node-icon {
    font-size: 3rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.node-label {
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
}

.network-connection {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.connection-line {
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.connection-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: dataFlow 2s infinite;
}

@keyframes dataFlow {
    0% { left: -100%; }
    100% { left: 100%; }
}

.connection-label {
    font-size: 0.8rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    padding: 4px 8px;
    border-radius: 4px;
}

/* Security Indicators */
.security-checks {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.check-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.check-icon {
    font-size: 1.2rem;
}

/* Server Storage */
.server-storage {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 20px;
}

.server-storage h5 {
    margin: 0 0 15px 0;
    color: #4facfe;
}

.server-data {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.data-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.data-item label {
    font-weight: 600;
    color: #4facfe;
    font-size: 0.85rem;
}

.data-item code {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    word-break: break-all;
}

.server-limitations {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 15px;
}

.server-limitations h6 {
    margin: 0 0 10px 0;
    color: #ff6b6b;
    font-size: 1rem;
}

.server-limitations ul {
    margin: 0;
    padding-left: 20px;
}

.server-limitations li {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

/* Zero-Knowledge Proof */
.zk-proof-container {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 20px;
}

.zk-proof-container h5 {
    margin: 0 0 15px 0;
    color: #4facfe;
}

.proof-explanation p {
    margin: 0 0 15px 0;
    line-height: 1.6;
}

.proof-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.proof-step {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.step-number {
    background: #4facfe;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

/* Demo Controls */
.demo-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

.btn-demo {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-demo:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.btn-demo.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: none;
}

.btn-demo.secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Audit Trail Styles */
.audit-trail-container {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.audit-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f8f9fa;
}

.audit-header h3 {
    color: #333;
    font-size: 2rem;
    margin: 0 0 20px 0;
}

.audit-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Timeline */
.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.timeline-header h4 {
    color: #333;
    margin: 0;
}

.timeline-filters {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #4facfe;
    background: white;
    color: #4facfe;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: #4facfe;
    color: white;
}

.timeline-events {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 10px;
}

.timeline-event {
    display: grid;
    grid-template-columns: 120px 40px 1fr 100px;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
    transition: all 0.3s ease;
}

.timeline-event:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.timeline-event.critical {
    border-left-color: #dc3545;
}

.timeline-event.warning {
    border-left-color: #ffc107;
}

.timeline-event.info {
    border-left-color: #17a2b8;
}

.event-time {
    font-size: 0.85rem;
    color: #666;
    font-weight: 600;
}

.event-icon {
    font-size: 1.5rem;
    text-align: center;
}

.event-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.event-title {
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.event-description {
    color: #666;
    font-size: 0.85rem;
    line-height: 1.4;
}

.event-metadata {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.event-metadata span {
    font-size: 0.75rem;
    color: #999;
}

.event-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.event-status.success {
    background: #d4edda;
    color: #155724;
}

.event-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.event-status.blocked {
    background: #fff3cd;
    color: #856404;
}

/* Analysis Sections */
.audit-analysis {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 30px;
}

.analysis-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
}

.analysis-section h4 {
    color: #333;
    margin: 0 0 20px 0;
    font-size: 1.2rem;
}

.security-metrics,
.risk-indicators {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Penetration Test Styles */
.pentest-container {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.pentest-header {
    text-align: center;
    margin-bottom: 30px;
}

.pentest-header h3 {
    color: #333;
    font-size: 2rem;
    margin: 0 0 10px 0;
}

.pentest-dashboard {
    margin: 30px 0;
}

.test-progress {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
}

.test-progress h4 {
    color: #333;
    margin: 0 0 15px 0;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.progress-bar-container .progress-bar {
    flex: 1;
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.progress-bar-container .progress-bar .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-weight: 600;
    color: #333;
    min-width: 40px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.test-category {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.test-category:hover {
    border-color: #4facfe;
    background: #f0f8ff;
}

.test-category h5 {
    color: #333;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
}

.test-results {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.test-result {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border-left: 4px solid #dee2e6;
    font-size: 0.85rem;
}

.test-result.passed {
    border-left-color: #28a745;
}

.test-result.failed {
    border-left-color: #dc3545;
}

.test-name {
    flex: 1;
    color: #333;
}

.test-status {
    font-size: 1rem;
}

/* Compliance Report Styles */
.compliance-report-container {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.report-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f8f9fa;
}

.report-header h3 {
    color: #333;
    font-size: 2rem;
    margin: 0 0 15px 0;
}

.report-metadata {
    display: flex;
    justify-content: center;
    gap: 30px;
    font-size: 0.9rem;
    color: #666;
}

.compliance-sections {
    margin: 30px 0;
}

.compliance-summary {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    margin: 30px 0;
}

.compliance-summary h4 {
    color: #333;
    margin: 0 0 20px 0;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
}

.summary-label {
    font-weight: 600;
    color: #333;
}

.summary-value {
    font-weight: 700;
    color: #4facfe;
}

.summary-value.low {
    color: #28a745;
}

.summary-value.medium {
    color: #ffc107;
}

.summary-value.high {
    color: #dc3545;
}

.compliance-actions {
    margin: 30px 0;
}

.compliance-actions h4 {
    color: #333;
    margin: 0 0 20px 0;
}

.action-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-item {
    display: grid;
    grid-template-columns: 80px 1fr 100px;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
}

.action-item.critical {
    border-left-color: #dc3545;
}

.action-item.high {
    border-left-color: #fd7e14;
}

.action-item.medium {
    border-left-color: #ffc107;
}

.action-item.low {
    border-left-color: #28a745;
}

.action-priority {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    text-align: center;
}

.action-item.critical .action-priority {
    background: #f8d7da;
    color: #721c24;
}

.action-item.high .action-priority {
    background: #ffeaa7;
    color: #856404;
}

.action-item.medium .action-priority {
    background: #fff3cd;
    color: #856404;
}

.action-item.low .action-priority {
    background: #d4edda;
    color: #155724;
}

.action-description {
    color: #333;
    font-size: 0.9rem;
    line-height: 1.4;
}

.action-timeline {
    font-size: 0.85rem;
    color: #666;
    text-align: right;
}

/* Security Education Styles */
.security-education-container {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.education-header {
    text-align: center;
    margin-bottom: 30px;
}

.education-header h3 {
    color: #333;
    font-size: 2rem;
    margin: 0 0 10px 0;
}

.education-topics {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
    margin: 30px 0;
}

.topic-navigation {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.topic-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.topic-btn.active,
.topic-btn:hover {
    border-color: #4facfe;
    background: #f0f8ff;
    color: #4facfe;
}

.topic-content {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    min-height: 400px;
}

.education-interactive {
    margin-top: 30px;
}

.education-interactive h4 {
    color: #333;
    margin: 0 0 20px 0;
}

.lab-exercises {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    min-height: 200px;
}

/* Security Dashboard */
.security-dashboard {
    margin: 20px 0;
}

.security-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #4facfe;
}

.metric-card h4 {
    color: #333;
    margin: 0 0 10px 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #4facfe;
    margin-bottom: 5px;
}

.metric-status {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
}

.metric-status.excellent {
    background: #d4edda;
    color: #155724;
}

.metric-status.good {
    background: #d1ecf1;
    color: #0c5460;
}

.metric-status.fair {
    background: #fff3cd;
    color: #856404;
}

.metric-status.poor {
    background: #f8d7da;
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .security-demo-container,
    .audit-trail-container,
    .pentest-container,
    .compliance-report-container,
    .security-education-container {
        margin: 10px;
        padding: 20px;
    }

    .data-comparison {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .encryption-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }

    .network-path {
        flex-direction: column;
        gap: 20px;
    }

    .connection-line {
        width: 4px;
        height: 50px;
        transform: rotate(90deg);
    }

    .audit-analysis {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .category-grid {
        grid-template-columns: 1fr;
    }

    .timeline-event {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: left;
    }

    .education-topics {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .topic-navigation {
        flex-direction: row;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .topic-btn {
        flex-shrink: 0;
        min-width: 200px;
    }

    .security-metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .demo-controls {
        flex-direction: column;
        align-items: center;
    }

    .btn-demo {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .demo-header h3 {
        font-size: 1.5rem;
    }

    .audit-header h3,
    .pentest-header h3,
    .report-header h3,
    .education-header h3 {
        font-size: 1.5rem;
    }

    .audit-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .report-metadata {
        flex-direction: column;
        gap: 10px;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .action-item {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: left;
    }

    .timeline-filters {
        flex-wrap: wrap;
        gap: 5px;
    }

    .filter-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .audit-trail-container,
    .pentest-container,
    .compliance-report-container,
    .security-education-container {
        background: #2d3748;
        color: #e2e8f0;
    }

    .audit-header h3,
    .pentest-header h3,
    .report-header h3,
    .education-header h3,
    .timeline-header h4,
    .test-progress h4,
    .test-category h5,
    .compliance-summary h4,
    .compliance-actions h4,
    .education-interactive h4 {
        color: #e2e8f0;
    }

    .timeline-event,
    .test-category,
    .compliance-summary,
    .action-item,
    .topic-content,
    .lab-exercises {
        background: #4a5568;
        color: #e2e8f0;
    }

    .event-title,
    .test-name,
    .summary-label,
    .action-description {
        color: #e2e8f0;
    }

    .event-description,
    .event-metadata span,
    .action-timeline {
        color: #a0aec0;
    }

    .metric-card {
        background: #4a5568;
        color: #e2e8f0;
    }

    .topic-btn {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }

    .topic-btn.active,
    .topic-btn:hover {
        background: #2d3748;
        border-color: #4facfe;
        color: #4facfe;
    }
}

/* Print styles */
@media print {
    .security-demo-container,
    .audit-trail-container,
    .pentest-container,
    .compliance-report-container {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        border: 1px solid #ccc;
    }

    .demo-controls,
    .timeline-filters,
    .pentest-controls {
        display: none;
    }

    .timeline-events {
        max-height: none;
        overflow: visible;
    }

    .stage {
        page-break-inside: avoid;
    }
}