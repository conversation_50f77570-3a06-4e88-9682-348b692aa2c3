/**
 * Performance Optimizer - Generates performance optimization recommendations
 * Analyzes performance data and provides actionable optimization suggestions
 */

class PerformanceOptimizer {
    constructor(performanceMonitor) {
        this.performanceMonitor = performanceMonitor;
        this.recommendations = [];
        this.thresholds = {
            // Performance thresholds (in milliseconds)
            fcp: { good: 1800, poor: 3000 },
            lcp: { good: 2500, poor: 4000 },
            cls: { good: 0.1, poor: 0.25 },
            fid: { good: 100, poor: 300 },
            
            // Backup performance thresholds
            backupDuration: { good: 10000, poor: 30000 }, // 10s good, 30s poor
            backupThroughput: { good: 1000000, poor: 100000 }, // 1MB/s good, 100KB/s poor
            backupSuccessRate: { good: 0.95, poor: 0.8 }, // 95% good, 80% poor
            
            // Resource usage thresholds
            memoryUsage: { good: 0.6, poor: 0.8 }, // 60% good, 80% poor
            networkLatency: { good: 100, poor: 500 }, // 100ms good, 500ms poor
            
            // Error rate thresholds
            errorRate: { good: 0.01, poor: 0.05 } // 1% good, 5% poor
        };
        
        this.optimizationStrategies = new Map();
        this.init();
    }

    init() {
        this.setupOptimizationStrategies();
        console.log('Performance Optimizer initialized');
    }

    setupOptimizationStrategies() {
        // Web Vitals optimization strategies
        this.optimizationStrategies.set('fcp', {
            name: 'First Contentful Paint',
            strategies: [
                {
                    title: 'Optimize Critical Resources',
                    description: 'Minimize render-blocking resources and optimize critical CSS/JS',
                    impact: 'high',
                    effort: 'medium',
                    actions: [
                        'Inline critical CSS',
                        'Defer non-critical JavaScript',
                        'Optimize font loading',
                        'Use resource hints (preload, prefetch)'
                    ]
                },
                {
                    title: 'Improve Server Response Time',
                    description: 'Reduce Time to First Byte (TTFB)',
                    impact: 'high',
                    effort: 'high',
                    actions: [
                        'Optimize server-side processing',
                        'Use CDN for static assets',
                        'Enable server-side caching',
                        'Optimize database queries'
                    ]
                }
            ]
        });

        this.optimizationStrategies.set('lcp', {
            name: 'Largest Contentful Paint',
            strategies: [
                {
                    title: 'Optimize Largest Element',
                    description: 'Optimize the loading of the largest visible element',
                    impact: 'high',
                    effort: 'medium',
                    actions: [
                        'Preload hero images',
                        'Optimize image formats (WebP, AVIF)',
                        'Use responsive images',
                        'Lazy load below-the-fold content'
                    ]
                },
                {
                    title: 'Reduce Resource Load Times',
                    description: 'Minimize the time to load critical resources',
                    impact: 'medium',
                    effort: 'medium',
                    actions: [
                        'Compress images and assets',
                        'Minify CSS and JavaScript',
                        'Enable gzip/brotli compression',
                        'Optimize third-party scripts'
                    ]
                }
            ]
        });

        this.optimizationStrategies.set('cls', {
            name: 'Cumulative Layout Shift',
            strategies: [
                {
                    title: 'Reserve Space for Dynamic Content',
                    description: 'Prevent layout shifts by reserving space for dynamic elements',
                    impact: 'high',
                    effort: 'low',
                    actions: [
                        'Set dimensions for images and videos',
                        'Reserve space for ads and embeds',
                        'Use CSS aspect-ratio property',
                        'Avoid inserting content above existing content'
                    ]
                },
                {
                    title: 'Optimize Font Loading',
                    description: 'Prevent layout shifts caused by font swapping',
                    impact: 'medium',
                    effort: 'low',
                    actions: [
                        'Use font-display: swap',
                        'Preload critical fonts',
                        'Use system fonts as fallbacks',
                        'Optimize web font delivery'
                    ]
                }
            ]
        });

        // Backup performance optimization strategies
        this.optimizationStrategies.set('backup_performance', {
            name: 'Backup Performance',
            strategies: [
                {
                    title: 'Optimize Data Processing',
                    description: 'Improve backup speed through better data handling',
                    impact: 'high',
                    effort: 'medium',
                    actions: [
                        'Implement incremental backups',
                        'Use compression algorithms',
                        'Batch process small files',
                        'Optimize encryption performance'
                    ]
                },
                {
                    title: 'Improve Network Efficiency',
                    description: 'Optimize network usage during backups',
                    impact: 'medium',
                    effort: 'medium',
                    actions: [
                        'Implement connection pooling',
                        'Use parallel uploads',
                        'Add retry logic with exponential backoff',
                        'Optimize chunk sizes'
                    ]
                }
            ]
        });

        // Memory optimization strategies
        this.optimizationStrategies.set('memory', {
            name: 'Memory Usage',
            strategies: [
                {
                    title: 'Reduce Memory Footprint',
                    description: 'Optimize memory usage to prevent performance degradation',
                    impact: 'high',
                    effort: 'medium',
                    actions: [
                        'Implement object pooling',
                        'Clean up event listeners',
                        'Use WeakMap/WeakSet for caches',
                        'Optimize image and video memory usage'
                    ]
                },
                {
                    title: 'Prevent Memory Leaks',
                    description: 'Identify and fix memory leaks',
                    impact: 'high',
                    effort: 'high',
                    actions: [
                        'Profile memory usage regularly',
                        'Remove unused DOM references',
                        'Clear timers and intervals',
                        'Optimize closure usage'
                    ]
                }
            ]
        });

        // Network optimization strategies
        this.optimizationStrategies.set('network', {
            name: 'Network Performance',
            strategies: [
                {
                    title: 'Reduce Network Requests',
                    description: 'Minimize the number of network requests',
                    impact: 'medium',
                    effort: 'medium',
                    actions: [
                        'Bundle and minify assets',
                        'Use HTTP/2 server push',
                        'Implement request caching',
                        'Combine small API calls'
                    ]
                },
                {
                    title: 'Optimize Request Efficiency',
                    description: 'Make network requests more efficient',
                    impact: 'medium',
                    effort: 'low',
                    actions: [
                        'Use appropriate HTTP methods',
                        'Implement request deduplication',
                        'Add request timeouts',
                        'Use GraphQL for flexible queries'
                    ]
                }
            ]
        });
    }

    analyzePerformance() {
        const metrics = this.performanceMonitor.getMetricsSummary();
        this.recommendations = [];

        // Analyze Web Vitals
        this.analyzeWebVitals(metrics.pageLoad);
        
        // Analyze backup performance
        this.analyzeBackupPerformance(metrics.backupPerformance);
        
        // Analyze resource usage
        this.analyzeResourceUsage(metrics.resourceUsage);
        
        // Analyze network performance
        this.analyzeNetworkPerformance(metrics.networkMetrics);
        
        // Analyze error patterns
        this.analyzeErrorPatterns(metrics.errors);

        return this.recommendations;
    }

    analyzeWebVitals(pageLoad) {
        // First Contentful Paint
        if (pageLoad.fcp) {
            const fcpScore = this.getPerformanceScore(pageLoad.fcp, this.thresholds.fcp);
            if (fcpScore < 0.8) {
                this.addRecommendation({
                    category: 'web_vitals',
                    type: 'fcp',
                    severity: fcpScore < 0.5 ? 'high' : 'medium',
                    title: 'Improve First Contentful Paint',
                    description: `FCP is ${pageLoad.fcp.toFixed(0)}ms. Target: <${this.thresholds.fcp.good}ms`,
                    currentValue: pageLoad.fcp,
                    targetValue: this.thresholds.fcp.good,
                    strategies: this.optimizationStrategies.get('fcp').strategies
                });
            }
        }

        // Largest Contentful Paint
        if (pageLoad.lcp) {
            const lcpScore = this.getPerformanceScore(pageLoad.lcp, this.thresholds.lcp);
            if (lcpScore < 0.8) {
                this.addRecommendation({
                    category: 'web_vitals',
                    type: 'lcp',
                    severity: lcpScore < 0.5 ? 'high' : 'medium',
                    title: 'Improve Largest Contentful Paint',
                    description: `LCP is ${pageLoad.lcp.toFixed(0)}ms. Target: <${this.thresholds.lcp.good}ms`,
                    currentValue: pageLoad.lcp,
                    targetValue: this.thresholds.lcp.good,
                    strategies: this.optimizationStrategies.get('lcp').strategies
                });
            }
        }

        // Cumulative Layout Shift
        if (pageLoad.cls) {
            const clsScore = this.getPerformanceScore(pageLoad.cls, this.thresholds.cls, true);
            if (clsScore < 0.8) {
                this.addRecommendation({
                    category: 'web_vitals',
                    type: 'cls',
                    severity: clsScore < 0.5 ? 'high' : 'medium',
                    title: 'Reduce Cumulative Layout Shift',
                    description: `CLS is ${pageLoad.cls.toFixed(3)}. Target: <${this.thresholds.cls.good}`,
                    currentValue: pageLoad.cls,
                    targetValue: this.thresholds.cls.good,
                    strategies: this.optimizationStrategies.get('cls').strategies
                });
            }
        }
    }

    analyzeBackupPerformance(backupPerformance) {
        if (!backupPerformance.analysis) return;

        const analysis = backupPerformance.analysis;

        // Success rate analysis
        if (analysis.successRate < this.thresholds.backupSuccessRate.good) {
            this.addRecommendation({
                category: 'backup_reliability',
                type: 'success_rate',
                severity: analysis.successRate < this.thresholds.backupSuccessRate.poor ? 'high' : 'medium',
                title: 'Improve Backup Success Rate',
                description: `Success rate is ${(analysis.successRate * 100).toFixed(1)}%. Target: >${(this.thresholds.backupSuccessRate.good * 100).toFixed(0)}%`,
                currentValue: analysis.successRate,
                targetValue: this.thresholds.backupSuccessRate.good,
                strategies: [
                    {
                        title: 'Improve Error Handling',
                        description: 'Implement better error handling and retry logic',
                        impact: 'high',
                        effort: 'medium',
                        actions: [
                            'Add exponential backoff for retries',
                            'Implement circuit breaker pattern',
                            'Improve network error detection',
                            'Add backup validation checks'
                        ]
                    }
                ]
            });
        }

        // Duration analysis
        if (analysis.avgDuration > this.thresholds.backupDuration.good) {
            this.addRecommendation({
                category: 'backup_performance',
                type: 'duration',
                severity: analysis.avgDuration > this.thresholds.backupDuration.poor ? 'high' : 'medium',
                title: 'Reduce Backup Duration',
                description: `Average duration is ${(analysis.avgDuration / 1000).toFixed(1)}s. Target: <${(this.thresholds.backupDuration.good / 1000).toFixed(0)}s`,
                currentValue: analysis.avgDuration,
                targetValue: this.thresholds.backupDuration.good,
                strategies: this.optimizationStrategies.get('backup_performance').strategies
            });
        }

        // Throughput analysis
        if (analysis.avgThroughput < this.thresholds.backupThroughput.good) {
            this.addRecommendation({
                category: 'backup_performance',
                type: 'throughput',
                severity: analysis.avgThroughput < this.thresholds.backupThroughput.poor ? 'high' : 'medium',
                title: 'Improve Backup Throughput',
                description: `Average throughput is ${this.formatBytes(analysis.avgThroughput)}/s. Target: >${this.formatBytes(this.thresholds.backupThroughput.good)}/s`,
                currentValue: analysis.avgThroughput,
                targetValue: this.thresholds.backupThroughput.good,
                strategies: this.optimizationStrategies.get('backup_performance').strategies
            });
        }
    }

    analyzeResourceUsage(resourceUsage) {
        // Memory usage analysis
        if (resourceUsage.memory) {
            const memoryUsage = resourceUsage.memory.used / resourceUsage.memory.total;
            if (memoryUsage > this.thresholds.memoryUsage.good) {
                this.addRecommendation({
                    category: 'resource_usage',
                    type: 'memory',
                    severity: memoryUsage > this.thresholds.memoryUsage.poor ? 'high' : 'medium',
                    title: 'Optimize Memory Usage',
                    description: `Memory usage is ${(memoryUsage * 100).toFixed(1)}%. Target: <${(this.thresholds.memoryUsage.good * 100).toFixed(0)}%`,
                    currentValue: memoryUsage,
                    targetValue: this.thresholds.memoryUsage.good,
                    strategies: this.optimizationStrategies.get('memory').strategies
                });
            }
        }

        // Network connection analysis
        if (resourceUsage.connection) {
            const connection = resourceUsage.connection;
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                this.addRecommendation({
                    category: 'network_optimization',
                    type: 'slow_connection',
                    severity: 'medium',
                    title: 'Optimize for Slow Connections',
                    description: `Connection type: ${connection.effectiveType}. Optimize for low bandwidth.`,
                    strategies: [
                        {
                            title: 'Reduce Data Usage',
                            description: 'Minimize data transfer for slow connections',
                            impact: 'high',
                            effort: 'medium',
                            actions: [
                                'Implement adaptive quality',
                                'Use progressive loading',
                                'Compress all assets',
                                'Defer non-critical resources'
                            ]
                        }
                    ]
                });
            }
        }
    }

    analyzeNetworkPerformance(networkMetrics) {
        if (!networkMetrics.requests || networkMetrics.requests.length === 0) return;

        const requests = networkMetrics.requests;
        const failedRequests = requests.filter(r => !r.success);
        const slowRequests = requests.filter(r => r.duration > this.thresholds.networkLatency.poor);
        
        const errorRate = failedRequests.length / requests.length;
        const avgLatency = requests.reduce((sum, r) => sum + r.duration, 0) / requests.length;

        // Error rate analysis
        if (errorRate > this.thresholds.errorRate.good) {
            this.addRecommendation({
                category: 'network_reliability',
                type: 'error_rate',
                severity: errorRate > this.thresholds.errorRate.poor ? 'high' : 'medium',
                title: 'Reduce Network Error Rate',
                description: `Network error rate is ${(errorRate * 100).toFixed(1)}%. Target: <${(this.thresholds.errorRate.good * 100).toFixed(1)}%`,
                currentValue: errorRate,
                targetValue: this.thresholds.errorRate.good,
                strategies: [
                    {
                        title: 'Improve Network Resilience',
                        description: 'Make network requests more reliable',
                        impact: 'high',
                        effort: 'medium',
                        actions: [
                            'Implement retry logic',
                            'Add request timeouts',
                            'Use circuit breaker pattern',
                            'Improve error handling'
                        ]
                    }
                ]
            });
        }

        // Latency analysis
        if (avgLatency > this.thresholds.networkLatency.good) {
            this.addRecommendation({
                category: 'network_performance',
                type: 'latency',
                severity: avgLatency > this.thresholds.networkLatency.poor ? 'high' : 'medium',
                title: 'Reduce Network Latency',
                description: `Average latency is ${avgLatency.toFixed(0)}ms. Target: <${this.thresholds.networkLatency.good}ms`,
                currentValue: avgLatency,
                targetValue: this.thresholds.networkLatency.good,
                strategies: this.optimizationStrategies.get('network').strategies
            });
        }
    }

    analyzeErrorPatterns(errors) {
        if (errors.analysis && errors.analysis.frequentErrors.length > 0) {
            errors.analysis.frequentErrors.forEach(errorPattern => {
                this.addRecommendation({
                    category: 'error_handling',
                    type: 'frequent_error',
                    severity: errorPattern.count > 5 ? 'high' : 'medium',
                    title: 'Fix Frequent Error',
                    description: `Error "${errorPattern.error}" occurred ${errorPattern.count} times recently`,
                    strategies: [
                        {
                            title: 'Debug and Fix Error',
                            description: 'Investigate and resolve the recurring error',
                            impact: 'high',
                            effort: 'high',
                            actions: [
                                'Add detailed error logging',
                                'Implement error boundaries',
                                'Add input validation',
                                'Improve error recovery'
                            ]
                        }
                    ]
                });
            });
        }
    }

    addRecommendation(recommendation) {
        recommendation.id = this.generateRecommendationId();
        recommendation.timestamp = Date.now();
        recommendation.priority = this.calculatePriority(recommendation);
        
        this.recommendations.push(recommendation);
    }

    generateRecommendationId() {
        return 'rec_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    calculatePriority(recommendation) {
        let priority = 0;
        
        // Severity weight
        switch (recommendation.severity) {
            case 'high': priority += 100; break;
            case 'medium': priority += 50; break;
            case 'low': priority += 25; break;
        }
        
        // Category weight
        switch (recommendation.category) {
            case 'web_vitals': priority += 30; break;
            case 'backup_performance': priority += 25; break;
            case 'backup_reliability': priority += 35; break;
            case 'network_reliability': priority += 20; break;
            case 'resource_usage': priority += 15; break;
        }
        
        // Impact vs effort calculation
        if (recommendation.strategies) {
            const avgImpact = this.calculateAverageImpact(recommendation.strategies);
            const avgEffort = this.calculateAverageEffort(recommendation.strategies);
            priority += (avgImpact / avgEffort) * 10;
        }
        
        return Math.round(priority);
    }

    calculateAverageImpact(strategies) {
        const impactValues = { low: 1, medium: 2, high: 3 };
        const total = strategies.reduce((sum, s) => sum + (impactValues[s.impact] || 1), 0);
        return total / strategies.length;
    }

    calculateAverageEffort(strategies) {
        const effortValues = { low: 1, medium: 2, high: 3 };
        const total = strategies.reduce((sum, s) => sum + (effortValues[s.effort] || 2), 0);
        return total / strategies.length;
    }

    getPerformanceScore(value, threshold, lowerIsBetter = false) {
        const { good, poor } = threshold;
        
        if (lowerIsBetter) {
            if (value <= good) return 1.0;
            if (value >= poor) return 0.0;
            return 1.0 - ((value - good) / (poor - good));
        } else {
            if (value <= good) return 1.0;
            if (value >= poor) return 0.0;
            return 1.0 - ((value - good) / (poor - good));
        }
    }

    getRecommendationsByCategory(category) {
        return this.recommendations.filter(rec => rec.category === category);
    }

    getRecommendationsBySeverity(severity) {
        return this.recommendations.filter(rec => rec.severity === severity);
    }

    getTopRecommendations(limit = 5) {
        return this.recommendations
            .sort((a, b) => b.priority - a.priority)
            .slice(0, limit);
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    exportRecommendations() {
        return {
            recommendations: this.recommendations,
            thresholds: this.thresholds,
            generatedAt: Date.now(),
            totalRecommendations: this.recommendations.length,
            severityBreakdown: {
                high: this.recommendations.filter(r => r.severity === 'high').length,
                medium: this.recommendations.filter(r => r.severity === 'medium').length,
                low: this.recommendations.filter(r => r.severity === 'low').length
            }
        };
    }

    clearRecommendations() {
        this.recommendations = [];
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}