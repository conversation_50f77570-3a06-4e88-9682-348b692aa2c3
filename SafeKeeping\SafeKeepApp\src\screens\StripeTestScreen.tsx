import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {
  Button,
  Card,
  Text,
  ActivityIndicator,
} from 'react-native-paper';
import {
  useStripe,
  useConfirmPayment,
  CardField,
  CardFieldInput,
} from '@stripe/stripe-react-native';

import { COLORS, SPACING } from '../utils/constants';

const StripeTestScreen = () => {
  const { confirmPayment } = useConfirmPayment();
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardDetails, setCardDetails] = useState<CardFieldInput.Details | null>(null);
  const [lastResult, setLastResult] = useState<string>('');

  // Simple test payment - $5.00
  const testAmount = 500; // $5.00 in cents

  const handleTestPayment = async () => {
    if (!cardDetails?.complete) {
      Alert.alert('Incomplete Card', 'Please enter complete card details.');
      return;
    }

    setIsProcessing(true);
    setLastResult('Processing payment...');

    try {
      // For testing, we'll create a simple payment intent
      // In production, this should call your backend API
      const paymentIntentResponse = await createTestPaymentIntent();

      if (!paymentIntentResponse.client_secret) {
        throw new Error('Failed to create payment intent');
      }

      setLastResult('Payment intent created, confirming payment...');

      // Confirm payment with Stripe
      const { error, paymentIntent } = await confirmPayment(
        paymentIntentResponse.client_secret,
        {
          paymentMethodType: 'Card',
          paymentMethodData: {
            billingDetails: {
              email: '<EMAIL>',
              name: 'SafeKeep Test User',
            },
          },
        }
      );

      if (error) {
        console.error('Payment confirmation error:', error);
        setLastResult(`❌ Payment failed: ${error.message}`);
        Alert.alert('Payment Failed', error.message || 'Payment failed. Please try again.');
      } else if (paymentIntent) {
        console.log('Payment successful:', paymentIntent);
        setLastResult(`✅ Payment successful! ID: ${paymentIntent.id}`);
        Alert.alert(
          '🎉 Payment Successful!',
          `Test payment of $5.00 completed successfully!\n\nPayment ID: ${paymentIntent.id}`
        );
      }
    } catch (error) {
      console.error('Payment error:', error);
      const errorMessage = error.message || 'An unexpected error occurred.';
      setLastResult(`❌ Error: ${errorMessage}`);
      Alert.alert('Payment Error', errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // Create payment intent via backend API
  const createTestPaymentIntent = async () => {
    try {
      console.log('🔗 Calling backend API to create payment intent...');

      const response = await fetch('http://localhost:3000/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: testAmount,
          currency: 'usd',
          description: 'SafeKeep Test Payment - $5.00',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const paymentIntent = await response.json();
      console.log('✅ Payment intent received from backend:', paymentIntent);
      return paymentIntent;
    } catch (error) {
      console.error('❌ Failed to create payment intent:', error);
      throw error;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Header */}
        <Card style={styles.headerCard}>
          <Card.Content>
            <Text variant="headlineSmall" style={styles.title}>
              🧪 Stripe Integration Test
            </Text>
            <Text style={styles.subtitle}>
              Test your Stripe payment integration with a $5.00 test charge
            </Text>
          </Card.Content>
        </Card>

        {/* Test Card Information */}
        <Card style={styles.testCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.testTitle}>
              📋 Test Card Numbers
            </Text>
            <View style={styles.testCardRow}>
              <Text style={styles.testCardLabel}>✅ Success:</Text>
              <Text style={styles.testCardNumber}>4242 4242 4242 4242</Text>
            </View>
            <View style={styles.testCardRow}>
              <Text style={styles.testCardLabel}>❌ Declined:</Text>
              <Text style={styles.testCardNumber}>4000 0000 0000 0002</Text>
            </View>
            <Text style={styles.testNote}>
              Use any future expiry date and any 3-digit CVC
            </Text>
          </Card.Content>
        </Card>

        {/* Payment Form */}
        <Card style={styles.paymentCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.paymentTitle}>
              💳 Payment Details
            </Text>
            
            <Text style={styles.amountText}>
              Test Amount: $5.00
            </Text>

            {/* Stripe Card Input */}
            <View style={styles.cardFieldContainer}>
              <CardField
                postalCodeEnabled={true}
                placeholders={{
                  number: '4242 4242 4242 4242',
                  expiration: 'MM/YY',
                  cvc: 'CVC',
                  postalCode: 'ZIP',
                }}
                cardStyle={{
                  backgroundColor: '#FFFFFF',
                  textColor: '#000000',
                  fontSize: 16,
                  placeholderColor: '#999999',
                  borderWidth: 1,
                  borderColor: '#E0E0E0',
                  borderRadius: 8,
                }}
                style={styles.cardField}
                onCardChange={(cardDetails) => {
                  setCardDetails(cardDetails);
                  console.log('Card details:', cardDetails);
                }}
              />
            </View>

            {/* Payment Button */}
            <Button
              mode="contained"
              onPress={handleTestPayment}
              disabled={!cardDetails?.complete || isProcessing}
              style={styles.paymentButton}
              contentStyle={styles.paymentButtonContent}
            >
              {isProcessing ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                'Test Payment - $5.00'
              )}
            </Button>

            {/* Result Display */}
            {lastResult ? (
              <View style={styles.resultContainer}>
                <Text style={styles.resultTitle}>Last Result:</Text>
                <Text style={styles.resultText}>{lastResult}</Text>
              </View>
            ) : null}
          </Card.Content>
        </Card>

        {/* Instructions */}
        <Card style={styles.instructionsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.instructionsTitle}>
              📖 Test Instructions
            </Text>
            <Text style={styles.instructionText}>
              1. Enter the test card number: 4242 4242 4242 4242
            </Text>
            <Text style={styles.instructionText}>
              2. Use any future expiry date (e.g., 12/25)
            </Text>
            <Text style={styles.instructionText}>
              3. Use any 3-digit CVC (e.g., 123)
            </Text>
            <Text style={styles.instructionText}>
              4. Enter any ZIP code (e.g., 12345)
            </Text>
            <Text style={styles.instructionText}>
              5. Tap "Test Payment" to process
            </Text>
            <Text style={styles.warningText}>
              ⚠️ This is a test transaction - no real money will be charged
            </Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  headerCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  title: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  subtitle: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  testCard: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    marginBottom: SPACING.lg,
    elevation: 2,
  },
  testTitle: {
    color: COLORS.primary,
    marginBottom: SPACING.md,
  },
  testCardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  testCardLabel: {
    width: 80,
    fontSize: 14,
    fontWeight: 'bold',
  },
  testCardNumber: {
    fontFamily: 'monospace',
    fontSize: 14,
    color: COLORS.text,
  },
  testNote: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: SPACING.sm,
    fontStyle: 'italic',
  },
  paymentCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  paymentTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  amountText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  cardFieldContainer: {
    marginBottom: SPACING.lg,
  },
  cardField: {
    width: '100%',
    height: 50,
    marginVertical: SPACING.sm,
  },
  paymentButton: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.md,
  },
  paymentButtonContent: {
    height: 50,
  },
  resultContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: SPACING.md,
    borderRadius: 8,
    marginTop: SPACING.sm,
  },
  resultTitle: {
    fontWeight: 'bold',
    marginBottom: SPACING.xs,
    color: COLORS.text,
  },
  resultText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontFamily: 'monospace',
  },
  instructionsCard: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    elevation: 2,
  },
  instructionsTitle: {
    color: COLORS.success,
    marginBottom: SPACING.md,
  },
  instructionText: {
    fontSize: 14,
    color: COLORS.text,
    marginBottom: SPACING.xs,
    lineHeight: 20,
  },
  warningText: {
    fontSize: 12,
    color: COLORS.warning,
    marginTop: SPACING.sm,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default StripeTestScreen;
