import PhotoBackupService, { PhotoAsset, BackupProgress } from '../../src/services/PhotoBackupService';
import AuthService from '../../src/services/AuthService';
import EncryptionService from '../../src/services/EncryptionService';
import CloudStorageService from '../../src/services/CloudStorageService';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import { Platform, PermissionsAndroid } from 'react-native';
import { VIDEO_MIME_TYPES, VIDEO_FILE_EXTENSIONS } from '../../src/types/backup';

// Mock dependencies
jest.mock('../../src/services/AuthService');
jest.mock('../../src/services/EncryptionService');
jest.mock('../../src/services/CloudStorageService');
jest.mock('@react-native-camera-roll/camera-roll');
jest.mock('react-native-fs');
jest.mock('react-native', () => ({
  Platform: { OS: 'android' },
  PermissionsAndroid: {
    request: jest.fn(),
    PERMISSIONS: { READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE' },
    RESULTS: { GRANTED: 'granted' }
  }
}));

const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;
const mockEncryptionService = EncryptionService as jest.Mocked<typeof EncryptionService>;
const mockCloudStorageService = CloudStorageService as jest.Mocked<typeof CloudStorageService>;
const mockCameraRoll = CameraRoll as jest.Mocked<typeof CameraRoll>;
const mockRNFS = RNFS as jest.Mocked<typeof RNFS>;
const mockPermissionsAndroid = PermissionsAndroid as jest.Mocked<typeof PermissionsAndroid>;

describe('PhotoBackupService', () => {
  const mockPhotos: PhotoAsset[] = [
    {
      uri: 'file://photo1.jpg',
      filename: 'photo1.jpg',
      timestamp: Date.now() - 1000,
      type: 'image/jpeg',
      fileSize: 1024000,
      width: 1920,
      height: 1080
    },
    {
      uri: 'file://photo2.png',
      filename: 'photo2.png',
      timestamp: Date.now(),
      type: 'image/png',
      fileSize: 2048000,
      width: 1280,
      height: 720
    }
  ];

  const mockVideoFiles = [
    {
      uri: 'file://video1.mp4',
      filename: 'video1.mp4',
      timestamp: Date.now(),
      type: 'video/mp4',
      fileSize: 10240000,
      width: 1920,
      height: 1080
    }
  ];

  const mockCameraRollResponse = {
    edges: [
      {
        node: {
          image: {
            uri: 'file://photo1.jpg',
            filename: 'photo1.jpg',
            fileSize: 1024000,
            width: 1920,
            height: 1080
          },
          type: 'image/jpeg',
          timestamp: new Date(Date.now() - 1000).toISOString()
        }
      },
      {
        node: {
          image: {
            uri: 'file://video1.mp4',
            filename: 'video1.mp4',
            fileSize: 10240000,
            width: 1920,
            height: 1080
          },
          type: 'video/mp4',
          timestamp: new Date().toISOString()
        }
      }
    ]
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockAuthService.getCurrentUser.mockReturnValue({ id: 'user123', email: '<EMAIL>' });
    mockPermissionsAndroid.request.mockResolvedValue('granted');
    mockCameraRoll.getPhotos.mockResolvedValue(mockCameraRollResponse);
    mockRNFS.stat.mockResolvedValue({
      size: 1024000,
      mtime: new Date(),
      isFile: () => true,
      isDirectory: () => false,
      path: 'file://photo1.jpg'
    } as any);
    mockRNFS.read.mockResolvedValue('base64data');
    mockRNFS.readFile.mockResolvedValue('base64filedata');
  });

  describe('requestPhotoPermission', () => {
    it('should request Android photo permission successfully', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('granted');
      
      const result = await PhotoBackupService.requestPhotoPermission();
      
      expect(result).toBe(true);
      expect(mockPermissionsAndroid.request).toHaveBeenCalledWith(
        'android.permission.READ_EXTERNAL_STORAGE',
        expect.objectContaining({
          title: '📸 Photo Library Access Permission'
        })
      );
    });

    it('should handle permission denial on Android', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');
      
      const result = await PhotoBackupService.requestPhotoPermission();
      
      expect(result).toBe(false);
    });

    it('should return true for iOS platform', async () => {
      (Platform as any).OS = 'ios';
      
      const result = await PhotoBackupService.requestPhotoPermission();
      
      expect(result).toBe(true);
      expect(mockPermissionsAndroid.request).not.toHaveBeenCalled();
    });

    it('should handle permission request errors', async () => {
      mockPermissionsAndroid.request.mockRejectedValue(new Error('Permission error'));
      
      const result = await PhotoBackupService.requestPhotoPermission();
      
      expect(result).toBe(false);
    });
  });

  describe('video exclusion functionality', () => {
    it('should identify video files by MIME type', () => {
      const service = PhotoBackupService as any;
      
      VIDEO_MIME_TYPES.forEach(mimeType => {
        expect(service.isVideoFile('test.unknown', mimeType)).toBe(true);
      });
    });

    it('should identify video files by extension', () => {
      const service = PhotoBackupService as any;
      
      VIDEO_FILE_EXTENSIONS.forEach(extension => {
        expect(service.isVideoFile(`test${extension}`, 'unknown/type')).toBe(true);
      });
    });

    it('should not identify photo files as videos', () => {
      const service = PhotoBackupService as any;
      
      expect(service.isVideoFile('photo.jpg', 'image/jpeg')).toBe(false);
      expect(service.isVideoFile('photo.png', 'image/png')).toBe(false);
      expect(service.isVideoFile('photo.gif', 'image/gif')).toBe(false);
    });

    it('should handle case-insensitive file extensions', () => {
      const service = PhotoBackupService as any;
      
      expect(service.isVideoFile('VIDEO.MP4', 'unknown/type')).toBe(true);
      expect(service.isVideoFile('Video.MOV', 'unknown/type')).toBe(true);
    });
  });

  describe('scanPhotoLibrary', () => {
    it('should scan photo library and exclude videos', async () => {
      const result = await PhotoBackupService.scanPhotoLibrary(1000);
      
      expect(result.photos).toHaveLength(1);
      expect(result.excludedVideos).toHaveLength(1);
      expect(result.totalScanned).toBe(2);
      expect(result.photos[0].filename).toBe('photo1.jpg');
      expect(result.excludedVideos[0].filename).toBe('video1.mp4');
    });

    it('should handle permission denial', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');
      
      await expect(PhotoBackupService.scanPhotoLibrary())
        .rejects.toThrow('Photo library permission denied');
    });

    it('should handle camera roll errors', async () => {
      mockCameraRoll.getPhotos.mockRejectedValue(new Error('Camera roll error'));
      
      await expect(PhotoBackupService.scanPhotoLibrary())
        .rejects.toThrow('Failed to scan photo library');
    });

    it('should handle files without filenames', async () => {
      const responseWithoutFilename = {
        edges: [
          {
            node: {
              image: {
                uri: 'file://unnamed.jpg',
                filename: null,
                fileSize: 1024000,
                width: 1920,
                height: 1080
              },
              type: 'image/jpeg',
              timestamp: new Date().toISOString()
            }
          }
        ]
      };
      
      mockCameraRoll.getPhotos.mockResolvedValue(responseWithoutFilename);
      
      const result = await PhotoBackupService.scanPhotoLibrary();
      
      expect(result.photos).toHaveLength(1);
      expect(result.photos[0].filename).toMatch(/^media_\d+$/);
    });
  });

  describe('generatePhotoHash', () => {
    it('should generate hash from file stats and content', async () => {
      const hash = await PhotoBackupService.generatePhotoHash('file://photo1.jpg');
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBeGreaterThan(0);
      expect(mockRNFS.stat).toHaveBeenCalledWith('file://photo1.jpg');
      expect(mockRNFS.read).toHaveBeenCalledWith('file://photo1.jpg', 1024, 0, 'base64');
    });

    it('should use fallback hash when file operations fail', async () => {
      mockRNFS.stat.mockRejectedValue(new Error('File not found'));
      
      const hash = await PhotoBackupService.generatePhotoHash('file://photo1.jpg');
      
      expect(hash).toBeDefined();
      expect(typeof hash).toBe('string');
      expect(hash.length).toBeGreaterThan(0);
    });
  });

  describe('detectDuplicates', () => {
    it('should detect and remove duplicate photos', async () => {
      const duplicatePhotos = [
        ...mockPhotos,
        { ...mockPhotos[0], uri: 'file://photo1_copy.jpg' } // Same content, different URI
      ];

      // Mock same hash for duplicates
      mockRNFS.stat.mockResolvedValue({
        size: 1024000,
        mtime: new Date(),
        isFile: () => true,
        isDirectory: () => false,
        path: 'file://photo1.jpg'
      } as any);

      const result = await PhotoBackupService.detectDuplicates(duplicatePhotos);
      
      expect(result.length).toBeLessThan(duplicatePhotos.length);
    });

    it('should handle hash generation errors gracefully', async () => {
      mockRNFS.stat.mockRejectedValue(new Error('Hash error'));
      
      const result = await PhotoBackupService.detectDuplicates(mockPhotos);
      
      expect(result).toHaveLength(mockPhotos.length); // Should include all photos despite errors
    });

    it('should call progress callback during duplicate detection', async () => {
      const progressCallback = jest.fn();
      const service = PhotoBackupService as any;
      service.progressCallback = progressCallback;
      
      await PhotoBackupService.detectDuplicates(mockPhotos);
      
      expect(progressCallback).toHaveBeenCalled();
    });
  });

  describe('backupPhotos', () => {
    beforeEach(() => {
      mockEncryptionService.encrypt.mockResolvedValue({
        success: true,
        data: 'encrypted_data'
      });
      mockCloudStorageService.uploadFile.mockResolvedValue({
        success: true,
        fileId: 'file123'
      });
    });

    it('should successfully backup photos with video exclusion and encryption', async () => {
      const progressCallback = jest.fn();
      const result = await PhotoBackupService.backupPhotos(mockPhotos, progressCallback);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2);
      expect(result.errors).toHaveLength(0);
      expect(mockEncryptionService.encrypt).toHaveBeenCalled();
      expect(mockCloudStorageService.uploadFile).toHaveBeenCalled();
      expect(progressCallback).toHaveBeenCalledTimes(6); // Multiple progress updates
    });

    it('should handle authentication failure', async () => {
      mockAuthService.getCurrentUser.mockReturnValue(null);

      const result = await PhotoBackupService.backupPhotos(mockPhotos);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('authentication required');
    });

    it('should handle permission denial', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');

      const result = await PhotoBackupService.backupPhotos(mockPhotos);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('permission denied');
    });

    it('should validate photo data and exclude videos', async () => {
      const mixedMedia = [
        ...mockPhotos,
        {
          uri: 'file://video1.mp4',
          filename: 'video1.mp4',
          timestamp: Date.now(),
          type: 'video/mp4',
          fileSize: 10240000,
          width: 1920,
          height: 1080
        }
      ];

      const result = await PhotoBackupService.backupPhotos(mixedMedia);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2); // Only photos, video excluded
    });

    it('should handle no valid photos scenario', async () => {
      const invalidPhotos = [
        {
          uri: '',
          filename: '',
          timestamp: 0,
          type: 'video/mp4',
          fileSize: 0,
          width: 0,
          height: 0
        }
      ];

      const result = await PhotoBackupService.backupPhotos(invalidPhotos);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
      expect(result.errors[0].message).toContain('No valid photos found');
    });

    it('should handle encryption failure', async () => {
      mockEncryptionService.encrypt.mockResolvedValue({
        success: false,
        error: 'Encryption failed'
      });

      const result = await PhotoBackupService.backupPhotos(mockPhotos);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(2); // One error per photo
      expect(result.errors[0].type).toBe('network');
      expect(result.errors[0].message).toContain('Failed to encrypt photo');
    });

    it('should handle upload failure with retry logic', async () => {
      mockCloudStorageService.uploadFile.mockResolvedValue({
        success: false,
        error: 'Upload failed'
      });

      const result = await PhotoBackupService.backupPhotos(mockPhotos, undefined, 2);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(2); // One error per photo after retries
      expect(result.errors[0].type).toBe('network');
      expect(result.errors[0].retryable).toBe(true);
    });

    it('should handle general errors gracefully', async () => {
      mockRNFS.readFile.mockRejectedValue(new Error('File read error'));

      const result = await PhotoBackupService.backupPhotos(mockPhotos);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
    });

    it('should prevent concurrent backup operations', async () => {
      // Start first backup
      const firstBackup = PhotoBackupService.backupPhotos(mockPhotos);
      
      // Try to start second backup while first is running
      await expect(PhotoBackupService.backupPhotos(mockPhotos))
        .rejects.toThrow('Photo backup is already running');
      
      // Wait for first backup to complete
      await firstBackup;
    });

    it('should track progress correctly during backup', async () => {
      const progressCallback = jest.fn();
      
      await PhotoBackupService.backupPhotos(mockPhotos, progressCallback);

      // Verify progress updates
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentPhoto: 'Validating photo data...',
          percentage: 10,
          status: 'processing'
        })
      );

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentPhoto: 'Detecting duplicates...',
          percentage: 20,
          status: 'processing'
        })
      );

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentPhoto: 'Backup completed!',
          percentage: 100,
          status: 'completed'
        })
      );
    });

    it('should handle backup pause functionality', async () => {
      // Start backup
      const backupPromise = PhotoBackupService.backupPhotos(mockPhotos);
      
      // Pause backup
      PhotoBackupService.pauseBackup();
      
      const result = await backupPromise;
      
      // Should complete but may have fewer items processed due to pause
      expect(result).toBeDefined();
    });
  });

  describe('validatePhotoData', () => {
    it('should filter out photos with missing required fields', () => {
      const mixedPhotos = [
        ...mockPhotos,
        {
          uri: '',
          filename: 'invalid.jpg',
          timestamp: Date.now(),
          type: 'image/jpeg',
          fileSize: 1024000,
          width: 1920,
          height: 1080
        },
        {
          uri: 'file://valid.jpg',
          filename: '',
          timestamp: Date.now(),
          type: 'image/jpeg',
          fileSize: 1024000,
          width: 1920,
          height: 1080
        }
      ];

      const service = PhotoBackupService as any;
      const result = service.validatePhotoData(mixedPhotos);

      expect(result).toHaveLength(2); // Only original valid photos
      expect(result.every((p: PhotoAsset) => 
        p.uri.trim().length > 0 && 
        p.filename.trim().length > 0 && 
        p.timestamp > 0 &&
        p.fileSize > 0
      )).toBe(true);
    });

    it('should exclude video files during validation', () => {
      const mixedMedia = [
        ...mockPhotos,
        {
          uri: 'file://video.mp4',
          filename: 'video.mp4',
          timestamp: Date.now(),
          type: 'video/mp4',
          fileSize: 10240000,
          width: 1920,
          height: 1080
        }
      ];

      const service = PhotoBackupService as any;
      const result = service.validatePhotoData(mixedMedia);

      expect(result).toHaveLength(2); // Only photos, video excluded
      expect(result.every((p: PhotoAsset) => !p.type.startsWith('video/'))).toBe(true);
    });
  });

  describe('control functions', () => {
    it('should track running state correctly', () => {
      expect(PhotoBackupService.isRunning()).toBe(false);
    });

    it('should handle pause and resume', () => {
      PhotoBackupService.pauseBackup();
      PhotoBackupService.resumeBackup();
      // These are void functions, just ensure they don't throw
      expect(true).toBe(true);
    });
  });

  describe('restorePhotos', () => {
    it('should restore photos successfully', async () => {
      const mockFiles = [
        {
          id: 'file1',
          original_name: 'photo1.jpg',
          size: 1024000,
          uploaded_at: '2023-01-01T00:00:00Z',
          mime_type: 'image/jpeg'
        }
      ];

      mockCloudStorageService.getUserFiles.mockResolvedValue(mockFiles);

      const result = await PhotoBackupService.restorePhotos();

      expect(result.success).toBe(true);
      expect(result.photos).toBeDefined();
      expect(result.photos!).toHaveLength(1);
    });

    it('should handle no backup files found', async () => {
      mockCloudStorageService.getUserFiles.mockResolvedValue([]);

      const result = await PhotoBackupService.restorePhotos();

      expect(result.success).toBe(false);
      expect(result.error).toContain('No photo backup found');
    });
  });

  describe('getBackupStatsFromCloud', () => {
    it('should return backup statistics', async () => {
      const mockFiles = [
        {
          id: 'file1',
          uploaded_at: '2023-01-01T00:00:00Z'
        }
      ];

      mockCloudStorageService.getUserFiles.mockResolvedValue(mockFiles);

      const result = await PhotoBackupService.getBackupStatsFromCloud();

      expect(result.totalBackups).toBe(1);
      expect(result.totalPhotos).toBe(1);
      expect(result.lastBackupDate).toBeDefined();
    });

    it('should handle no backup files', async () => {
      mockCloudStorageService.getUserFiles.mockResolvedValue([]);

      const result = await PhotoBackupService.getBackupStatsFromCloud();

      expect(result.totalBackups).toBe(0);
      expect(result.totalPhotos).toBe(0);
    });
  });

  describe('convertToPhotoAssets', () => {
    it('should convert MediaFile to PhotoAsset format', () => {
      const mediaFiles = [
        {
          uri: 'file://photo1.jpg',
          filename: 'photo1.jpg',
          type: 'image/jpeg',
          fileSize: 1024000,
          timestamp: Date.now()
        }
      ];

      const service = PhotoBackupService as any;
      const result = service.convertToPhotoAssets(mediaFiles);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        uri: 'file://photo1.jpg',
        filename: 'photo1.jpg',
        type: 'image/jpeg',
        fileSize: 1024000,
        width: 0,
        height: 0
      });
    });
  });
});