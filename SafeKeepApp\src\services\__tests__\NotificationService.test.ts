import { Alert } from 'react-native';
import NotificationService from '../NotificationService';
import PermissionService from '../PermissionService';
import { BackupError } from '../../types/backup';

// Mock React Native Alert
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn()
  },
  Platform: {
    OS: 'ios'
  }
}));

// Mock PermissionService
jest.mock('../PermissionService', () => ({
  requestPermission: jest.fn()
}));

const mockAlert = Alert.alert as jest.MockedFunction<typeof Alert.alert>;
const mockRequestPermission = PermissionService.requestPermission as jest.MockedFunction<typeof PermissionService.requestPermission>;

describe('NotificationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    NotificationService.clearNotifications();
  });

  describe('showErrorNotification', () => {
    it('should show permission error with grant action', async () => {
      const error: BackupError = {
        id: 'permission-error',
        type: 'permission',
        message: 'Camera permission denied',
        timestamp: new Date(),
        retryable: false
      };

      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('🔐 Permission Required');
        expect(message).toContain('We need permission to backup your data');
        expect(buttons).toHaveLength(1);
        expect(buttons![0].text).toBe('Grant Permission');
        
        // Simulate user pressing the button
        buttons![0].onPress!();
      });

      mockRequestPermission.mockResolvedValue({
        granted: true,
        denied: false,
        blocked: false,
        unavailable: false
      });

      await NotificationService.showErrorNotification(error, {
        dataType: 'photos'
      });

      expect(mockAlert).toHaveBeenCalled();
      expect(mockRequestPermission).toHaveBeenCalledWith('photos');
    });

    it('should show network error with retry action', async () => {
      const error: BackupError = {
        id: 'network-error',
        type: 'network',
        message: 'Connection timeout',
        timestamp: new Date(),
        retryable: true
      };

      const mockRetry = jest.fn();

      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('📶 Connection Issue');
        expect(message).toContain('There\'s a problem with your internet connection');
        expect(buttons).toHaveLength(1);
        expect(buttons![0].text).toBe('Retry');
        
        // Simulate user pressing retry
        buttons![0].onPress!();
      });

      await NotificationService.showErrorNotification(error, {
        onRetry: mockRetry
      });

      expect(mockAlert).toHaveBeenCalled();
      expect(mockRetry).toHaveBeenCalled();
    });

    it('should show storage error with manage storage action', async () => {
      const error: BackupError = {
        id: 'storage-error',
        type: 'storage',
        message: 'Insufficient storage space',
        timestamp: new Date(),
        retryable: false
      };

      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('💾 Storage Issue');
        expect(message).toContain('There\'s not enough storage space');
        expect(buttons).toHaveLength(1);
        expect(buttons![0].text).toBe('Manage Storage');
      });

      await NotificationService.showErrorNotification(error);

      expect(mockAlert).toHaveBeenCalled();
    });
  });

  describe('showPermissionNotification', () => {
    it('should show contacts permission notification', async () => {
      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('📞 Permission Needed');
        expect(message).toContain('SafeKeep needs access to your contacts');
        expect(message).toContain('Never lose important phone numbers');
        expect(buttons).toHaveLength(2);
        expect(buttons![0].text).toBe('Grant Permission');
        expect(buttons![1].text).toBe('Skip for Now');
      });

      const mockOnGrant = jest.fn();
      const mockOnSkip = jest.fn();

      await NotificationService.showPermissionNotification('contacts', mockOnGrant, mockOnSkip);

      expect(mockAlert).toHaveBeenCalled();
    });

    it('should show photos permission notification', async () => {
      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('📸 Permission Needed');
        expect(message).toContain('SafeKeep needs access to your photos');
        expect(message).toContain('Never lose precious memories');
      });

      await NotificationService.showPermissionNotification('photos');

      expect(mockAlert).toHaveBeenCalled();
    });
  });

  describe('showNetworkWarning', () => {
    it('should show WiFi required warning', async () => {
      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('📶 WiFi Required');
        expect(message).toContain('Your backup settings require WiFi');
        expect(buttons).toHaveLength(1);
        expect(buttons![0].text).toBe('Wait for WiFi');
      });

      await NotificationService.showNetworkWarning(false, true);

      expect(mockAlert).toHaveBeenCalled();
    });

    it('should show cellular data warning', async () => {
      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('📱 Cellular Data Warning');
        expect(message).toContain('You\'re about to backup using cellular data');
        expect(buttons).toHaveLength(2);
        expect(buttons![0].text).toBe('Continue Anyway');
        expect(buttons![1].text).toBe('Wait for WiFi');
      });

      const mockContinue = jest.fn();
      const mockWait = jest.fn();

      await NotificationService.showNetworkWarning(false, false, mockContinue, mockWait);

      expect(mockAlert).toHaveBeenCalled();
    });
  });

  describe('showBatteryWarning', () => {
    it('should show low battery warning', async () => {
      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('🔋 Low Battery');
        expect(message).toContain('Your battery is at 15%');
        expect(buttons).toHaveLength(2);
        expect(buttons![0].text).toBe('Pause Backup');
        expect(buttons![1].text).toBe('Continue Anyway');
      });

      const mockContinue = jest.fn();
      const mockPause = jest.fn();

      await NotificationService.showBatteryWarning(15, mockContinue, mockPause);

      expect(mockAlert).toHaveBeenCalled();
    });
  });

  describe('showStorageWarning', () => {
    it('should show storage almost full warning', async () => {
      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('💾 Storage Almost Full');
        expect(message).toContain('Your backup storage is 90% full');
        expect(buttons).toHaveLength(2);
        expect(buttons![0].text).toBe('Upgrade Storage');
        expect(buttons![1].text).toBe('Continue Anyway');
      });

      const mockUpgrade = jest.fn();
      const mockContinue = jest.fn();

      await NotificationService.showStorageWarning(
        900 * 1024 * 1024, // 900MB used
        1000 * 1024 * 1024, // 1GB total
        mockUpgrade,
        mockContinue
      );

      expect(mockAlert).toHaveBeenCalled();
    });
  });

  describe('showResumeNotification', () => {
    it('should show backup resume notification', async () => {
      mockAlert.mockImplementation((title, message, buttons) => {
        expect(title).toBe('⏸️ Resume Backup?');
        expect(message).toContain('Your previous backup was interrupted at 50% complete');
        expect(message).toContain('(50/100 items)');
        expect(buttons).toHaveLength(2);
        expect(buttons![0].text).toBe('Resume Backup');
        expect(buttons![1].text).toBe('Start Over');
      });

      const mockResume = jest.fn();
      const mockRestart = jest.fn();

      await NotificationService.showResumeNotification(
        'session-123',
        'photos',
        50,
        100,
        mockResume,
        mockRestart
      );

      expect(mockAlert).toHaveBeenCalled();
    });
  });

  describe('notification management', () => {
    it('should add notifications to the list', async () => {
      await NotificationService.showInfoNotification('Test Title', 'Test message');

      const notifications = NotificationService.getNotifications();
      expect(notifications).toHaveLength(1);
      expect(notifications[0].title).toBe('Test Title');
      expect(notifications[0].message).toBe('Test message');
      expect(notifications[0].type).toBe('info');
    });

    it('should clear all notifications', async () => {
      await NotificationService.showInfoNotification('Test 1', 'Message 1');
      await NotificationService.showInfoNotification('Test 2', 'Message 2');

      expect(NotificationService.getNotifications()).toHaveLength(2);

      NotificationService.clearNotifications();

      expect(NotificationService.getNotifications()).toHaveLength(0);
    });

    it('should clear specific notification', async () => {
      await NotificationService.showInfoNotification('Test 1', 'Message 1');
      await NotificationService.showInfoNotification('Test 2', 'Message 2');

      const notifications = NotificationService.getNotifications();
      expect(notifications).toHaveLength(2);

      NotificationService.clearNotification(notifications[0].id);

      const remainingNotifications = NotificationService.getNotifications();
      expect(remainingNotifications).toHaveLength(1);
      expect(remainingNotifications[0].title).toBe('Test 2');
    });

    it('should limit number of notifications', async () => {
      // Add more than the max limit (10)
      for (let i = 0; i < 15; i++) {
        await NotificationService.showInfoNotification(`Test ${i}`, `Message ${i}`);
      }

      const notifications = NotificationService.getNotifications();
      expect(notifications.length).toBeLessThanOrEqual(10);
      
      // Should keep the most recent ones
      expect(notifications[0].title).toBe('Test 14');
    });
  });

  describe('notification statistics', () => {
    it('should provide accurate statistics', async () => {
      const error: BackupError = {
        id: 'error-1',
        type: 'network',
        message: 'Network error',
        timestamp: new Date(),
        retryable: true
      };

      await NotificationService.showErrorNotification(error);
      await NotificationService.showWarningNotification('Warning', 'Warning message');
      await NotificationService.showInfoNotification('Info', 'Info message', true); // persistent

      const stats = NotificationService.getStatistics();
      
      expect(stats.totalNotifications).toBe(3);
      expect(stats.errorNotifications).toBe(1);
      expect(stats.warningNotifications).toBe(1);
      expect(stats.persistentNotifications).toBe(1);
    });
  });
});