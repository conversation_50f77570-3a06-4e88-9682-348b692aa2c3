/**
 * Theme Manager for SafeKeep Web Demo
 * Handles dark/light theme switching, system preference detection, and persistence
 */

class ThemeManager {
    constructor() {
        this.themes = {
            light: 'light',
            dark: 'dark',
            auto: 'auto',
            highContrast: 'high-contrast'
        };
        
        this.currentTheme = this.getStoredTheme() || this.themes.auto;
        this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        this.contrastQuery = window.matchMedia('(prefers-contrast: high)');
        
        this.init();
    }
    
    init() {
        // Apply initial theme
        this.applyTheme(this.currentTheme);
        
        // Listen for system theme changes
        this.mediaQuery.addEventListener('change', () => {
            if (this.currentTheme === this.themes.auto) {
                this.applyTheme(this.themes.auto);
            }
        });
        
        // Listen for contrast preference changes
        this.contrastQuery.addEventListener('change', () => {
            this.handleContrastChange();
        });
        
        // Create theme toggle UI
        this.createThemeToggle();
        
        // Announce theme changes for screen readers
        this.createAriaLiveRegion();
    }
    
    getStoredTheme() {
        try {
            return localStorage.getItem('safekeep-theme');
        } catch (e) {
            console.warn('Unable to access localStorage for theme preference');
            return null;
        }
    }
    
    storeTheme(theme) {
        try {
            localStorage.setItem('safekeep-theme', theme);
        } catch (e) {
            console.warn('Unable to store theme preference in localStorage');
        }
    }
    
    getSystemTheme() {
        if (this.contrastQuery.matches) {
            return this.themes.highContrast;
        }
        return this.mediaQuery.matches ? this.themes.dark : this.themes.light;
    }
    
    applyTheme(theme) {
        const root = document.documentElement;
        
        // Remove existing theme attributes
        root.removeAttribute('data-theme');
        
        let effectiveTheme = theme;
        
        if (theme === this.themes.auto) {
            effectiveTheme = this.getSystemTheme();
        }
        
        // Apply theme attribute
        if (effectiveTheme !== this.themes.light) {
            root.setAttribute('data-theme', effectiveTheme);
        }
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(effectiveTheme);
        
        // Announce theme change
        this.announceThemeChange(effectiveTheme);
        
        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('themechange', {
            detail: { theme: effectiveTheme, userSelected: theme }
        }));
    }
    
    updateMetaThemeColor(theme) {
        let themeColor = '#4facfe'; // Default light theme color
        
        if (theme === this.themes.dark) {
            themeColor = '#2d3748';
        } else if (theme === this.themes.highContrast) {
            themeColor = '#000000';
        }
        
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = themeColor;
    }
    
    setTheme(theme) {
        if (!Object.values(this.themes).includes(theme)) {
            console.warn(`Invalid theme: ${theme}`);
            return;
        }
        
        this.currentTheme = theme;
        this.storeTheme(theme);
        this.applyTheme(theme);
        this.updateThemeToggle();
    }
    
    toggleTheme() {
        const themes = [this.themes.light, this.themes.dark, this.themes.auto];
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        this.setTheme(themes[nextIndex]);
    }
    
    createThemeToggle() {
        const toggle = document.createElement('button');
        toggle.id = 'theme-toggle';
        toggle.className = 'btn btn-outline btn-sm';
        toggle.setAttribute('aria-label', 'Toggle theme');
        toggle.setAttribute('title', 'Toggle between light, dark, and auto themes');
        toggle.innerHTML = this.getThemeIcon(this.currentTheme);
        
        toggle.addEventListener('click', () => this.toggleTheme());
        
        // Add to header or create a floating button
        const header = document.querySelector('.header') || document.querySelector('header');
        if (header) {
            const themeContainer = document.createElement('div');
            themeContainer.className = 'theme-toggle-container';
            themeContainer.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 1000;
            `;
            themeContainer.appendChild(toggle);
            header.style.position = 'relative';
            header.appendChild(themeContainer);
        } else {
            // Create floating button
            toggle.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                border-radius: 50%;
                width: 48px;
                height: 48px;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: var(--shadow-lg);
            `;
            document.body.appendChild(toggle);
        }
        
        this.themeToggle = toggle;
    }
    
    updateThemeToggle() {
        if (this.themeToggle) {
            this.themeToggle.innerHTML = this.getThemeIcon(this.currentTheme);
            this.themeToggle.setAttribute('aria-label', `Current theme: ${this.getThemeLabel(this.currentTheme)}. Click to change.`);
        }
    }
    
    getThemeIcon(theme) {
        const icons = {
            light: '☀️',
            dark: '🌙',
            auto: '🔄',
            'high-contrast': '⚫'
        };
        return `<span style="font-size: 18px;">${icons[theme] || icons.light}</span>`;
    }
    
    getThemeLabel(theme) {
        const labels = {
            light: 'Light theme',
            dark: 'Dark theme',
            auto: 'Auto theme (follows system)',
            'high-contrast': 'High contrast theme'
        };
        return labels[theme] || labels.light;
    }
    
    createAriaLiveRegion() {
        const liveRegion = document.createElement('div');
        liveRegion.id = 'theme-announcements';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
        this.ariaLiveRegion = liveRegion;
    }
    
    announceThemeChange(theme) {
        if (this.ariaLiveRegion) {
            const message = `Theme changed to ${this.getThemeLabel(theme)}`;
            this.ariaLiveRegion.textContent = message;
            
            // Clear the message after a delay
            setTimeout(() => {
                this.ariaLiveRegion.textContent = '';
            }, 1000);
        }
    }
    
    handleContrastChange() {
        if (this.contrastQuery.matches && this.currentTheme === this.themes.auto) {
            this.applyTheme(this.themes.auto);
        }
    }
    
    // Utility method to get current effective theme
    getCurrentEffectiveTheme() {
        if (this.currentTheme === this.themes.auto) {
            return this.getSystemTheme();
        }
        return this.currentTheme;
    }
    
    // Method to check if dark theme is active
    isDarkTheme() {
        const effective = this.getCurrentEffectiveTheme();
        return effective === this.themes.dark || effective === this.themes.highContrast;
    }
    
    // Method for components to register theme change listeners
    onThemeChange(callback) {
        window.addEventListener('themechange', callback);
        return () => window.removeEventListener('themechange', callback);
    }
}

// Initialize theme manager when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.themeManager = new ThemeManager();
    });
} else {
    window.themeManager = new ThemeManager();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}