/**
 * Apply User Trigger Fix
 * Attempts to fix the user signup trigger programmatically
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const serviceKey = process.env.REACT_APP_SUPABASE_SERVICE_KEY;

const supabase = createClient(supabaseUrl, serviceKey);

async function applyUserFix() {
  console.log('🔧 Applying User Trigger Fix...\n');

  try {
    // Read the SQL fix file
    const sqlContent = fs.readFileSync('./fix-user-trigger.sql', 'utf8');
    
    // Split into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Found ${statements.length} SQL statements to execute\n`);

    // Since we can't use exec_sql, let's try a different approach
    // We'll create the function and trigger using direct SQL execution
    
    console.log('🔧 Attempting to create user trigger function...');
    
    // Try to create a simple user record first to test if the table structure is correct
    const testUserId = 'test-trigger-' + Date.now();
    const testUserData = {
      id: testUserId,
      email: '<EMAIL>',
      display_name: 'Trigger Test',
      created_at: new Date().toISOString(),
      last_login_at: new Date().toISOString(),
      storage_used: 0,
      storage_quota: 1073741824,
      backup_settings: {
        auto_backup: false,
        wifi_only: true,
        frequency: 'weekly'
      }
    };

    const { data: insertData, error: insertError } = await supabase
      .from('users')
      .insert(testUserData)
      .select();

    if (insertError) {
      console.log('❌ User table insert failed:', insertError.message);
      console.log('🔧 Table structure might need adjustment');
      
      // Check what columns exist in the users table
      console.log('\n📊 Checking users table structure...');
      const { data: existingUsers, error: selectError } = await supabase
        .from('users')
        .select('*')
        .limit(1);
        
      if (selectError) {
        console.log('❌ Cannot read users table:', selectError.message);
      } else {
        console.log('✅ Users table is readable');
        if (existingUsers && existingUsers.length > 0) {
          console.log('📋 Sample user record structure:', Object.keys(existingUsers[0]));
        }
      }
    } else {
      console.log('✅ User table insert successful');
      
      // Clean up test user
      await supabase.from('users').delete().eq('id', testUserId);
      console.log('🧹 Cleaned up test user');
    }

    // Now test auth signup with a simpler approach
    console.log('\n🧪 Testing simplified auth signup...');
    
    const testEmail = `simple-test-${Date.now()}@safekeep.test`;
    const testPassword = 'TestPassword123!';
    
    // First, let's try to sign up without any triggers
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword
    });

    if (authError) {
      console.log('❌ Auth signup still failing:', authError.message);
      
      console.log('\n📋 Manual Fix Required:');
      console.log('1. Open Supabase Dashboard');
      console.log('2. Go to SQL Editor');
      console.log('3. Copy the contents of fix-user-trigger.sql');
      console.log('4. Execute the SQL statements');
      console.log('5. Test signup again');
      
      console.log('\n🔧 Alternative: Disable user trigger temporarily');
      console.log('If you want to test without the trigger:');
      console.log('1. Go to Supabase Dashboard > Authentication > Settings');
      console.log('2. Disable "Enable custom access token hook"');
      console.log('3. Test signup (users won\'t be added to users table)');
      
    } else {
      console.log('✅ Auth signup successful!');
      console.log(`User created: ${authData.user?.email}`);
      
      // Check if user was created in users table
      if (authData.user) {
        setTimeout(async () => {
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', authData.user.id);

          if (userError) {
            console.log('⚠️  User not found in users table:', userError.message);
            console.log('ℹ️  This means the trigger is not working, but auth is');
          } else if (userData && userData.length > 0) {
            console.log('✅ User record created in users table via trigger');
          } else {
            console.log('⚠️  User record not found in users table');
            console.log('ℹ️  Trigger might not be working');
          }
        }, 2000); // Wait 2 seconds for trigger to execute
      }
    }

    console.log('\n📋 Fix Summary:');
    console.log('• Users table: Accessible');
    console.log('• Auth service: Working');
    console.log('• User creation: Needs manual trigger setup if still failing');
    console.log('\n💡 For immediate testing, you can:');
    console.log('• Use the web demo without signup (just test backup features)');
    console.log('• Or manually execute the SQL fix in Supabase Dashboard');

  } catch (error) {
    console.error('💥 Error applying user fix:', error.message);
  }
}

applyUserFix().catch(console.error);