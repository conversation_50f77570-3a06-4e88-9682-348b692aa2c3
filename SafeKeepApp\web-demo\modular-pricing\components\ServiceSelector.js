/**
 * ServiceSelector Component
 * Container component that manages multiple service checkboxes
 */

class ServiceSelector {
    /**
     * @param {Object} props - Component properties
     * @param {ServiceOption[]} props.services - Array of available services
     * @param {string[]} props.selectedServices - Array of selected service IDs
     * @param {Function} props.onServiceToggle - Callback when service is toggled
     * @param {Function} props.onServiceDetailsToggle - Callback when service details are toggled
     * @param {string|null} props.showDetails - ID of service with expanded details
     * @param {boolean} props.disabled - Whether all services are disabled
     */
    constructor(props) {
        this.props = props;
        this.element = null;
        this.serviceCheckboxes = new Map();
        
        this.render();
    }

    /**
     * Render the service selector component
     */
    render() {
        const { services, disabled } = this.props;
        
        this.element = window.ModularPricingUtils.createElement('div', {
            className: `service-selector ${disabled ? 'disabled' : ''}`
        });

        // Create header
        const header = window.ModularPricingUtils.createElement('div', {
            className: 'service-selector-header'
        });

        const title = window.ModularPricingUtils.createElement('h3', {
            className: 'service-selector-title'
        }, 'Choose Your Backup Services');

        const subtitle = window.ModularPricingUtils.createElement('p', {
            className: 'service-selector-subtitle'
        }, 'Select the services you want to include in your backup plan');

        header.appendChild(title);
        header.appendChild(subtitle);

        // Create services container
        const servicesContainer = window.ModularPricingUtils.createElement('div', {
            className: 'services-container'
        });

        // Create service checkboxes
        services.forEach(service => {
            const isSelected = this.props.selectedServices.includes(service.id);
            
            const serviceCheckbox = new window.ServiceCheckbox({
                service: service,
                checked: isSelected,
                disabled: disabled,
                onChange: this.handleServiceToggle.bind(this)
            });

            this.serviceCheckboxes.set(service.id, serviceCheckbox);
            servicesContainer.appendChild(serviceCheckbox.getElement());
        });

        this.element.appendChild(header);
        this.element.appendChild(servicesContainer);

        // Add selection summary if services are selected
        if (this.props.selectedServices.length > 0) {
            this.renderSelectionSummary();
        }
    }

    /**
     * Render selection summary
     */
    renderSelectionSummary() {
        const existingSummary = this.element.querySelector('.selection-summary');
        if (existingSummary) {
            existingSummary.remove();
        }

        const { selectedServices, services } = this.props;
        
        if (selectedServices.length === 0) return;

        const summary = window.ModularPricingUtils.createElement('div', {
            className: 'selection-summary'
        });

        const summaryTitle = window.ModularPricingUtils.createElement('h4', {
            className: 'summary-title'
        }, `Selected Services (${selectedServices.length})`);

        const summaryList = window.ModularPricingUtils.createElement('div', {
            className: 'summary-list'
        });

        selectedServices.forEach(serviceId => {
            const service = services.find(s => s.id === serviceId);
            if (service) {
                const summaryItem = window.ModularPricingUtils.createElement('div', {
                    className: 'summary-item'
                });

                const itemIcon = window.ModularPricingUtils.createElement('span', {
                    className: 'summary-item-icon'
                }, service.icon);

                const itemName = window.ModularPricingUtils.createElement('span', {
                    className: 'summary-item-name'
                }, service.name);

                const itemPrice = window.ModularPricingUtils.createElement('span', {
                    className: 'summary-item-price'
                }, window.ModularPricingUtils.formatPrice(service.individualPrice));

                summaryItem.appendChild(itemIcon);
                summaryItem.appendChild(itemName);
                summaryItem.appendChild(itemPrice);
                summaryList.appendChild(summaryItem);
            }
        });

        summary.appendChild(summaryTitle);
        summary.appendChild(summaryList);
        this.element.appendChild(summary);
    }

    /**
     * Handle service toggle
     * @param {string} serviceId - ID of the toggled service
     * @param {boolean} checked - New checked state
     */
    handleServiceToggle(serviceId, checked) {
        if (this.props.onServiceToggle) {
            this.props.onServiceToggle(serviceId, checked);
        }
    }

    /**
     * Handle service details toggle
     * @param {string} serviceId - ID of the service to show details for
     */
    handleServiceDetailsToggle(serviceId) {
        if (this.props.onServiceDetailsToggle) {
            this.props.onServiceDetailsToggle(serviceId);
        }
    }

    /**
     * Update component props
     * @param {Object} newProps - New properties
     */
    updateProps(newProps) {
        const oldProps = this.props;
        this.props = { ...this.props, ...newProps };

        // Update service checkboxes if selection changed
        if (JSON.stringify(oldProps.selectedServices) !== JSON.stringify(this.props.selectedServices)) {
            this.props.services.forEach(service => {
                const checkbox = this.serviceCheckboxes.get(service.id);
                if (checkbox) {
                    const isSelected = this.props.selectedServices.includes(service.id);
                    checkbox.updateProps({ checked: isSelected });
                }
            });

            // Update selection summary
            this.renderSelectionSummary();
        }

        // Update disabled state if changed
        if (oldProps.disabled !== this.props.disabled) {
            if (this.props.disabled) {
                this.element.classList.add('disabled');
            } else {
                this.element.classList.remove('disabled');
            }

            // Update all checkboxes
            this.serviceCheckboxes.forEach(checkbox => {
                checkbox.updateProps({ disabled: this.props.disabled });
            });
        }
    }

    /**
     * Get selected services data
     * @returns {ServiceOption[]} Array of selected service objects
     */
    getSelectedServices() {
        return this.props.services.filter(service => 
            this.props.selectedServices.includes(service.id)
        );
    }

    /**
     * Select all services
     */
    selectAll() {
        const allServiceIds = this.props.services.map(service => service.id);
        allServiceIds.forEach(serviceId => {
            if (!this.props.selectedServices.includes(serviceId)) {
                this.handleServiceToggle(serviceId, true);
            }
        });
    }

    /**
     * Clear all selections
     */
    clearAll() {
        this.props.selectedServices.forEach(serviceId => {
            this.handleServiceToggle(serviceId, false);
        });
    }

    /**
     * Get the DOM element
     * @returns {HTMLElement} The component's DOM element
     */
    getElement() {
        return this.element;
    }

    /**
     * Destroy the component and clean up
     */
    destroy() {
        // Destroy all service checkboxes
        this.serviceCheckboxes.forEach(checkbox => {
            checkbox.destroy();
        });
        this.serviceCheckboxes.clear();

        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        this.element = null;
    }
}

// Export component
if (typeof window !== 'undefined') {
    window.ServiceSelector = ServiceSelector;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ServiceSelector;
}