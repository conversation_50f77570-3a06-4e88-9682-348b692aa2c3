/**
 * Backup History and Analytics Manager
 * Manages backup session history, analytics, and performance tracking
 */

class BackupHistoryManager {
    constructor(supabase, adminSupabase) {
        this.supabase = supabase;
        this.adminSupabase = adminSupabase;
        this.sessions = [];
        this.analytics = {
            totalSessions: 0,
            successfulSessions: 0,
            failedSessions: 0,
            totalDataBackedUp: 0,
            averageBackupTime: 0,
            storageUsage: {
                used: 0,
                quota: 5 * 1024 * 1024 * 1024, // 5GB default
                breakdown: {
                    contacts: 0,
                    messages: 0,
                    photos: 0
                }
            }
        };
        this.performanceMetrics = [];
        this.listeners = [];
        
        // Initialize with demo data
        this.initializeDemoData();
    }

    /**
     * Initialize with demo backup history data
     */
    initializeDemoData() {
        const now = new Date();
        const demoSessions = [
            {
                id: 'session-001',
                userId: 'demo-user',
                type: 'manual',
                status: 'completed',
                startTime: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
                endTime: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000), // 5 minutes later
                progress: {
                    contacts: { total: 150, completed: 150, failed: 0 },
                    messages: { total: 2500, completed: 2500, failed: 0 },
                    photos: { total: 45, completed: 45, failed: 0 },
                    overall: { total: 2695, completed: 2695, failed: 0 }
                },
                encryption: {
                    algorithm: 'AES-256-GCM',
                    keyId: 'key-001',
                    originalSize: 125 * 1024 * 1024, // 125MB
                    encryptedSize: 127 * 1024 * 1024 // 127MB (with overhead)
                },
                performance: {
                    transferRate: 2.5 * 1024 * 1024, // 2.5 MB/s
                    averageFileSize: 46 * 1024, // 46KB
                    errorRate: 0
                }
            },
            {
                id: 'session-002',
                userId: 'demo-user',
                type: 'scheduled',
                status: 'completed',
                startTime: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
                endTime: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000 + 7 * 60 * 1000), // 7 minutes later
                progress: {
                    contacts: { total: 155, completed: 155, failed: 0 },
                    messages: { total: 2750, completed: 2745, failed: 5 },
                    photos: { total: 52, completed: 50, failed: 2 },
                    overall: { total: 2957, completed: 2950, failed: 7 }
                },
                encryption: {
                    algorithm: 'AES-256-GCM',
                    keyId: 'key-002',
                    originalSize: 142 * 1024 * 1024, // 142MB
                    encryptedSize: 144 * 1024 * 1024 // 144MB
                },
                performance: {
                    transferRate: 2.1 * 1024 * 1024, // 2.1 MB/s
                    averageFileSize: 48 * 1024, // 48KB
                    errorRate: 0.24 // 0.24%
                }
            },
            {
                id: 'session-003',
                userId: 'demo-user',
                type: 'manual',
                status: 'failed',
                startTime: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
                endTime: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000 + 2 * 60 * 1000), // 2 minutes later
                progress: {
                    contacts: { total: 160, completed: 160, failed: 0 },
                    messages: { total: 2800, completed: 1200, failed: 1600 },
                    photos: { total: 58, completed: 0, failed: 58 },
                    overall: { total: 3018, completed: 1360, failed: 1658 }
                },
                encryption: {
                    algorithm: 'AES-256-GCM',
                    keyId: 'key-003',
                    originalSize: 65 * 1024 * 1024, // 65MB (partial)
                    encryptedSize: 66 * 1024 * 1024 // 66MB
                },
                performance: {
                    transferRate: 0.8 * 1024 * 1024, // 0.8 MB/s (slow due to errors)
                    averageFileSize: 48 * 1024, // 48KB
                    errorRate: 54.9 // 54.9%
                },
                errors: [
                    { time: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000 + 60 * 1000), phase: 'messages', message: 'Network timeout', itemName: 'message_1201.json' },
                    { time: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000 + 90 * 1000), phase: 'photos', message: 'Storage quota exceeded', itemName: 'IMG_045.jpg' }
                ]
            },
            {
                id: 'session-004',
                userId: 'demo-user',
                type: 'scheduled',
                status: 'completed',
                startTime: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
                endTime: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000 + 6 * 60 * 1000), // 6 minutes later
                progress: {
                    contacts: { total: 162, completed: 162, failed: 0 },
                    messages: { total: 2900, completed: 2898, failed: 2 },
                    photos: { total: 61, completed: 61, failed: 0 },
                    overall: { total: 3123, completed: 3121, failed: 2 }
                },
                encryption: {
                    algorithm: 'AES-256-GCM',
                    keyId: 'key-004',
                    originalSize: 158 * 1024 * 1024, // 158MB
                    encryptedSize: 160 * 1024 * 1024 // 160MB
                },
                performance: {
                    transferRate: 2.8 * 1024 * 1024, // 2.8 MB/s
                    averageFileSize: 51 * 1024, // 51KB
                    errorRate: 0.06 // 0.06%
                }
            }
        ];

        this.sessions = demoSessions;
        this.updateAnalytics();
    }

    /**
     * Update analytics based on current sessions
     */
    updateAnalytics() {
        const completedSessions = this.sessions.filter(s => s.status === 'completed');
        const failedSessions = this.sessions.filter(s => s.status === 'failed');
        
        this.analytics.totalSessions = this.sessions.length;
        this.analytics.successfulSessions = completedSessions.length;
        this.analytics.failedSessions = failedSessions.length;
        
        // Calculate total data backed up (only from successful sessions)
        this.analytics.totalDataBackedUp = completedSessions.reduce((total, session) => {
            return total + (session.encryption?.originalSize || 0);
        }, 0);
        
        // Calculate average backup time
        const sessionsWithDuration = this.sessions.filter(s => s.endTime && s.startTime);
        if (sessionsWithDuration.length > 0) {
            const totalDuration = sessionsWithDuration.reduce((total, session) => {
                return total + (session.endTime.getTime() - session.startTime.getTime());
            }, 0);
            this.analytics.averageBackupTime = totalDuration / sessionsWithDuration.length;
        }
        
        // Update storage usage
        this.analytics.storageUsage.used = this.analytics.totalDataBackedUp;
        
        // Calculate breakdown by data type
        const breakdown = { contacts: 0, messages: 0, photos: 0 };
        completedSessions.forEach(session => {
            const contactsSize = (session.progress.contacts.completed / session.progress.overall.completed) * session.encryption.originalSize;
            const messagesSize = (session.progress.messages.completed / session.progress.overall.completed) * session.encryption.originalSize;
            const photosSize = (session.progress.photos.completed / session.progress.overall.completed) * session.encryption.originalSize;
            
            breakdown.contacts += contactsSize;
            breakdown.messages += messagesSize;
            breakdown.photos += photosSize;
        });
        
        this.analytics.storageUsage.breakdown = breakdown;
        
        // Generate performance metrics for charts
        this.generatePerformanceMetrics();
        
        // Notify listeners
        this.notifyListeners('analytics_updated', this.analytics);
    }

    /**
     * Generate performance metrics for trend visualization
     */
    generatePerformanceMetrics() {
        this.performanceMetrics = this.sessions.map(session => ({
            date: session.startTime,
            sessionId: session.id,
            successRate: session.progress.overall.total > 0 ? 
                (session.progress.overall.completed / session.progress.overall.total) * 100 : 0,
            transferRate: session.performance.transferRate,
            duration: session.endTime ? 
                (session.endTime.getTime() - session.startTime.getTime()) / 1000 : 0,
            dataSize: session.encryption?.originalSize || 0,
            errorRate: session.performance.errorRate
        })).sort((a, b) => a.date.getTime() - b.date.getTime());
    }

    /**
     * Get backup sessions with optional filtering
     */
    getSessions(filter = {}) {
        let filteredSessions = [...this.sessions];
        
        if (filter.status) {
            filteredSessions = filteredSessions.filter(s => s.status === filter.status);
        }
        
        if (filter.type) {
            filteredSessions = filteredSessions.filter(s => s.type === filter.type);
        }
        
        if (filter.dateRange) {
            const { start, end } = filter.dateRange;
            filteredSessions = filteredSessions.filter(s => 
                s.startTime >= start && s.startTime <= end
            );
        }
        
        // Sort by start time (newest first)
        return filteredSessions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
    }

    /**
     * Get session by ID
     */
    getSession(sessionId) {
        return this.sessions.find(s => s.id === sessionId);
    }

    /**
     * Get analytics data
     */
    getAnalytics() {
        return { ...this.analytics };
    }

    /**
     * Get performance metrics for charts
     */
    getPerformanceMetrics() {
        return [...this.performanceMetrics];
    }

    /**
     * Add a new backup session (for live demo)
     */
    addSession(sessionData) {
        const session = {
            id: sessionData.id || `session-${Date.now()}`,
            userId: sessionData.userId || 'demo-user',
            type: sessionData.type || 'manual',
            status: sessionData.status || 'pending',
            startTime: sessionData.startTime || new Date(),
            endTime: sessionData.endTime,
            progress: sessionData.progress || {
                contacts: { total: 0, completed: 0, failed: 0 },
                messages: { total: 0, completed: 0, failed: 0 },
                photos: { total: 0, completed: 0, failed: 0 },
                overall: { total: 0, completed: 0, failed: 0 }
            },
            encryption: sessionData.encryption || {},
            performance: sessionData.performance || {
                transferRate: 0,
                averageFileSize: 0,
                errorRate: 0
            },
            errors: sessionData.errors || []
        };
        
        this.sessions.unshift(session); // Add to beginning (newest first)
        this.updateAnalytics();
        this.notifyListeners('session_added', session);
        
        return session;
    }

    /**
     * Update an existing session
     */
    updateSession(sessionId, updates) {
        const sessionIndex = this.sessions.findIndex(s => s.id === sessionId);
        if (sessionIndex === -1) return null;
        
        this.sessions[sessionIndex] = { ...this.sessions[sessionIndex], ...updates };
        this.updateAnalytics();
        this.notifyListeners('session_updated', this.sessions[sessionIndex]);
        
        return this.sessions[sessionIndex];
    }

    /**
     * Simulate a restore session
     */
    async simulateRestore(sessionId, dataTypes = ['contacts', 'messages', 'photos']) {
        const session = this.getSession(sessionId);
        if (!session || session.status !== 'completed') {
            throw new Error('Cannot restore from incomplete or failed backup session');
        }

        const restoreSession = {
            id: `restore-${Date.now()}`,
            backupSessionId: sessionId,
            dataTypes,
            status: 'pending',
            progress: {
                download: 0,
                decryption: 0,
                verification: 0
            },
            restoredData: {},
            startTime: new Date()
        };

        // Simulate restore process
        this.notifyListeners('restore_started', restoreSession);

        // Download phase
        restoreSession.status = 'downloading';
        for (let i = 0; i <= 100; i += 10) {
            restoreSession.progress.download = i;
            this.notifyListeners('restore_progress', restoreSession);
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        // Decryption phase
        restoreSession.status = 'decrypting';
        for (let i = 0; i <= 100; i += 15) {
            restoreSession.progress.decryption = i;
            this.notifyListeners('restore_progress', restoreSession);
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        // Verification phase
        restoreSession.status = 'verifying';
        for (let i = 0; i <= 100; i += 20) {
            restoreSession.progress.verification = i;
            this.notifyListeners('restore_progress', restoreSession);
            await new Promise(resolve => setTimeout(resolve, 150));
        }

        // Generate restored data
        restoreSession.restoredData = this.generateRestoredData(session, dataTypes);
        restoreSession.status = 'completed';
        restoreSession.endTime = new Date();

        this.notifyListeners('restore_completed', restoreSession);
        return restoreSession;
    }

    /**
     * Generate sample restored data
     */
    generateRestoredData(session, dataTypes) {
        const data = {};

        if (dataTypes.includes('contacts')) {
            data.contacts = [
                { name: 'John Doe', phone: '+1234567890', email: '<EMAIL>' },
                { name: 'Jane Smith', phone: '+0987654321', email: '<EMAIL>' },
                { name: 'Bob Johnson', phone: '+1122334455', email: '<EMAIL>' }
            ].slice(0, Math.min(3, session.progress.contacts.completed));
        }

        if (dataTypes.includes('messages')) {
            data.messages = [
                { from: '+1234567890', message: 'Hey, how are you?', timestamp: new Date().toISOString() },
                { from: '+0987654321', message: 'Meeting at 3pm today', timestamp: new Date().toISOString() },
                { from: '+1122334455', message: 'Thanks for the help!', timestamp: new Date().toISOString() }
            ].slice(0, Math.min(3, Math.floor(session.progress.messages.completed / 100)));
        }

        if (dataTypes.includes('photos')) {
            data.photos = [
                { filename: 'IMG_001.jpg', size: 2048000, thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNiAxNkwyNCAyNEgxNlYxNloiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+' },
                { filename: 'IMG_002.png', size: 1536000, thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNiAxNkwyNCAyNEgxNlYxNloiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+' }
            ].slice(0, Math.min(2, session.progress.photos.completed));
        }

        return data;
    }

    /**
     * Add event listener
     */
    addListener(callback) {
        this.listeners.push(callback);
    }

    /**
     * Remove event listener
     */
    removeListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * Notify all listeners
     */
    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in backup history listener:', error);
            }
        });
    }

    /**
     * Format file size for display
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Format duration for display
     */
    static formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }
}

// Make it available globally
window.BackupHistoryManager = BackupHistoryManager;