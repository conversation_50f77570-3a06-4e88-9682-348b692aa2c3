// File Deduplication Utility for SafeKeep
// Provides functions for detecting and managing duplicate files

import { supabase } from '../config/supabase';
import { getCurrentUserId } from './supabaseHelpers';
import { FileMetadata, getFileMetadata } from './fileMetadataManager';

export interface DuplicateGroup {
  hash: string;
  files: FileMetadata[];
  totalSize: number;
  potentialSavings: number;
}

/**
 * Generate a hash for a file to use for deduplication
 */
export const generateFileHash = async (fileData: string | ArrayBuffer): Promise<string> => {
  try {
    let data: ArrayBuffer;
    
    if (typeof fileData === 'string') {
      // Convert string to ArrayBuffer
      const encoder = new TextEncoder();
      data = encoder.encode(fileData).buffer;
    } else {
      data = fileData;
    }
    
    // Use crypto API to generate SHA-256 hash
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    
    // Convert to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    console.error('Error generating file hash:', error);
    
    // Fallback to simpler hash if crypto API fails
    if (typeof fileData === 'string') {
      let hash = 0;
      for (let i = 0; i < fileData.length; i++) {
        const char = fileData.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return hash.toString(16);
    }
    
    throw new Error('Failed to generate file hash');
  }
};

/**
 * Check if a file with the same hash already exists
 * @param hash The file hash to check
 * @param excludeFileId Optional file ID to exclude from the check
 */
export const checkDuplicateByHash = async (
  hash: string,
  excludeFileId?: string
): Promise<{ 
  isDuplicate: boolean; 
  duplicates: FileMetadata[];
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { isDuplicate: false, duplicates: [], error: 'User not authenticated' };
    }

    let query = supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .eq('hash', hash);
      
    if (excludeFileId) {
      query = query.neq('id', excludeFileId);
    }

    const { data, error } = await query;

    if (error) {
      return { isDuplicate: false, duplicates: [], error: error.message };
    }

    return {
      isDuplicate: data.length > 0,
      duplicates: data as FileMetadata[],
      error: null
    };
  } catch (error) {
    console.error('Error checking for duplicate file:', error);
    return { isDuplicate: false, duplicates: [], error: (error as Error).message };
  }
};

/**
 * Find all duplicate files for the current user
 */
export const findAllDuplicates = async (): Promise<{ 
  duplicateGroups: DuplicateGroup[]; 
  totalPotentialSavings: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        duplicateGroups: [], 
        totalPotentialSavings: 0,
        error: 'User not authenticated' 
      };
    }

    // Try to use the database function first
    try {
      const { data, error } = await supabase.rpc('find_duplicate_files', {
        user_id_param: userId
      });

      if (!error && data) {
        // Process the results
        const duplicateGroups: DuplicateGroup[] = data.map((group: any) => {
          const files = group.files as FileMetadata[];
          const totalSize = files.reduce((sum, file) => sum + file.size, 0);
          // Potential savings = total size - size of one file
          const potentialSavings = totalSize - (files[0]?.size || 0);
          
          return {
            hash: group.hash,
            files,
            totalSize,
            potentialSavings
          };
        });

        const totalPotentialSavings = duplicateGroups.reduce(
          (sum, group) => sum + group.potentialSavings, 
          0
        );

        return { 
          duplicateGroups, 
          totalPotentialSavings,
          error: null 
        };
      }
    } catch (rpcError) {
      console.warn('RPC function not available, using client-side implementation:', rpcError);
    }

    // Fallback to client-side implementation
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      return { 
        duplicateGroups: [], 
        totalPotentialSavings: 0,
        error: error.message 
      };
    }

    // Group files by hash
    const filesByHash: Record<string, FileMetadata[]> = {};
    data.forEach((file: any) => {
      if (!filesByHash[file.hash]) {
        filesByHash[file.hash] = [];
      }
      filesByHash[file.hash].push(file as FileMetadata);
    });

    // Filter to only include hashes with multiple files
    const duplicateGroups: DuplicateGroup[] = Object.entries(filesByHash)
      .filter(([_, files]) => files.length > 1)
      .map(([hash, files]) => {
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        // Potential savings = total size - size of one file
        const potentialSavings = totalSize - files[0].size;
        
        return {
          hash,
          files,
          totalSize,
          potentialSavings
        };
      });

    const totalPotentialSavings = duplicateGroups.reduce(
      (sum, group) => sum + group.potentialSavings, 
      0
    );

    return { 
      duplicateGroups, 
      totalPotentialSavings,
      error: null 
    };
  } catch (error) {
    console.error('Error finding duplicate files:', error);
    return { 
      duplicateGroups: [], 
      totalPotentialSavings: 0,
      error: (error as Error).message 
    };
  }
};

/**
 * Find duplicates by category
 */
export const findDuplicatesByCategory = async (
  category: 'photo' | 'contact' | 'message'
): Promise<{ 
  duplicateGroups: DuplicateGroup[]; 
  totalPotentialSavings: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        duplicateGroups: [], 
        totalPotentialSavings: 0,
        error: 'User not authenticated' 
      };
    }

    // Get all files in the specified category
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .eq('category', category);

    if (error) {
      return { 
        duplicateGroups: [], 
        totalPotentialSavings: 0,
        error: error.message 
      };
    }

    // Group files by hash
    const filesByHash: Record<string, FileMetadata[]> = {};
    data.forEach((file: any) => {
      if (!filesByHash[file.hash]) {
        filesByHash[file.hash] = [];
      }
      filesByHash[file.hash].push(file as FileMetadata);
    });

    // Filter to only include hashes with multiple files
    const duplicateGroups: DuplicateGroup[] = Object.entries(filesByHash)
      .filter(([_, files]) => files.length > 1)
      .map(([hash, files]) => {
        const totalSize = files.reduce((sum, file) => sum + file.size, 0);
        // Potential savings = total size - size of one file
        const potentialSavings = totalSize - files[0].size;
        
        return {
          hash,
          files,
          totalSize,
          potentialSavings
        };
      });

    const totalPotentialSavings = duplicateGroups.reduce(
      (sum, group) => sum + group.potentialSavings, 
      0
    );

    return { 
      duplicateGroups, 
      totalPotentialSavings,
      error: null 
    };
  } catch (error) {
    console.error(`Error finding duplicate ${category} files:`, error);
    return { 
      duplicateGroups: [], 
      totalPotentialSavings: 0,
      error: (error as Error).message 
    };
  }
};

/**
 * Resolve duplicates by keeping one file and deleting the others
 */
export const resolveDuplicates = async (
  hash: string,
  fileIdToKeep: string
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Verify the file to keep exists and belongs to the user
    const { data: fileToKeep, error: fetchError } = await getFileMetadata(fileIdToKeep);
    if (fetchError || !fileToKeep) {
      return { success: false, error: fetchError || 'File not found' };
    }

    if (fileToKeep.user_id !== userId || fileToKeep.hash !== hash) {
      return { success: false, error: 'Invalid file selection' };
    }

    // Find all duplicates with the same hash
    const { data: duplicates, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .eq('hash', hash)
      .neq('id', fileIdToKeep);

    if (error) {
      return { success: false, error: error.message };
    }

    // Delete all duplicates except the one to keep
    for (const file of duplicates) {
      // Delete from storage first
      const { error: storageError } = await supabase.storage
        .from('user-data')
        .remove([file.storage_path]);

      if (storageError) {
        console.error(`Failed to delete file from storage: ${file.id}`, storageError);
        // Continue with metadata deletion even if storage deletion fails
      }

      // Delete metadata
      const { error: deleteError } = await supabase
        .from('file_metadata')
        .delete()
        .eq('id', file.id);

      if (deleteError) {
        console.error(`Failed to delete file metadata: ${file.id}`, deleteError);
        // Continue with other files
      }
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error resolving duplicates:', error);
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Compare two files to check if they are identical
 */
export const compareFiles = async (
  fileId1: string,
  fileId2: string
): Promise<{ 
  areIdentical: boolean; 
  hashesMatch: boolean;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { areIdentical: false, hashesMatch: false, error: 'User not authenticated' };
    }

    // Get metadata for both files
    const { data: file1, error: error1 } = await getFileMetadata(fileId1);
    const { data: file2, error: error2 } = await getFileMetadata(fileId2);

    if (error1 || !file1) {
      return { areIdentical: false, hashesMatch: false, error: error1 || 'File 1 not found' };
    }

    if (error2 || !file2) {
      return { areIdentical: false, hashesMatch: false, error: error2 || 'File 2 not found' };
    }

    // Verify user owns both files
    if (file1.user_id !== userId || file2.user_id !== userId) {
      return { areIdentical: false, hashesMatch: false, error: 'Access denied' };
    }

    // Compare hashes
    const hashesMatch = file1.hash === file2.hash;

    // If hashes match, files are considered identical
    // For more thorough comparison, we would need to download and compare content
    
    return { 
      areIdentical: hashesMatch, 
      hashesMatch,
      error: null 
    };
  } catch (error) {
    console.error('Error comparing files:', error);
    return { areIdentical: false, hashesMatch: false, error: (error as Error).message };
  }
};