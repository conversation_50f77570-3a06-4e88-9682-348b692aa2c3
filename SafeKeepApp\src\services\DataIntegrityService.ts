import CryptoJS from 'crypto-js';
import EncryptionService from './EncryptionService';
import { BackupError } from '../types/backup';

export interface IntegrityCheckResult {
  isValid: boolean;
  originalChecksum: string;
  currentChecksum: string;
  dataSize: number;
  timestamp: Date;
  error?: string;
}

export interface BackupIntegrityReport {
  sessionId: string;
  overallIntegrity: boolean;
  itemsChecked: number;
  itemsValid: number;
  itemsCorrupted: number;
  corruptedItems: CorruptedItemInfo[];
  checksumAlgorithm: string;
  validationTimestamp: Date;
}

export interface CorruptedItemInfo {
  itemId: string;
  itemType: 'contact' | 'message' | 'photo';
  originalChecksum: string;
  currentChecksum: string;
  corruptionType: 'checksum_mismatch' | 'decryption_failed' | 'data_missing';
  error: string;
}

export interface ChecksumValidationOptions {
  algorithm: 'SHA-256' | 'SHA-512' | 'MD5';
  includeMetadata: boolean;
  verifyEncryption: boolean;
}

export interface CorruptionPatternsResult {
  patterns: Array<{
    type: string;
    count: number;
    percentage: number;
    description: string;
  }>;
  recommendations: string[];
}

class DataIntegrityService {
  private readonly DEFAULT_ALGORITHM = 'SHA-256';
  private readonly CHUNK_SIZE = 64 * 1024; // 64KB chunks for large files

  /**
   * Generate checksum for backup data
   */
  generateChecksum(
    data: string | ArrayBuffer,
    algorithm: 'SHA-256' | 'SHA-512' | 'MD5' = this.DEFAULT_ALGORITHM
  ): string {
    try {
      let wordArray: CryptoJS.lib.WordArray;

      if (typeof data === 'string') {
        wordArray = CryptoJS.enc.Utf8.parse(data);
      } else {
        // Convert ArrayBuffer to WordArray
        const uint8Array = new Uint8Array(data);
        const words: number[] = [];
        for (let i = 0; i < uint8Array.length; i += 4) {
          const word = (uint8Array[i] << 24) | 
                      (uint8Array[i + 1] << 16) | 
                      (uint8Array[i + 2] << 8) | 
                      uint8Array[i + 3];
          words.push(word);
        }
        wordArray = CryptoJS.lib.WordArray.create(words, uint8Array.length);
      }

      switch (algorithm) {
        case 'SHA-256':
          return CryptoJS.SHA256(wordArray).toString(CryptoJS.enc.Hex);
        case 'SHA-512':
          return CryptoJS.SHA512(wordArray).toString(CryptoJS.enc.Hex);
        case 'MD5':
          return CryptoJS.MD5(wordArray).toString(CryptoJS.enc.Hex);
        default:
          throw new Error(`Unsupported algorithm: ${algorithm}`);
      }
    } catch (error) {
      console.error('Checksum generation failed:', error);
      throw new Error(`Failed to generate ${algorithm} checksum`);
    }
  }

  /**
   * Validate data integrity using checksums
   */
  async validateDataIntegrity(
    originalData: string,
    storedChecksum: string,
    options: Partial<ChecksumValidationOptions> = {}
  ): Promise<IntegrityCheckResult> {
    const opts: ChecksumValidationOptions = {
      algorithm: 'SHA-256',
      includeMetadata: false,
      verifyEncryption: false,
      ...options
    };

    try {
      const timestamp = new Date();
      const currentChecksum = this.generateChecksum(originalData, opts.algorithm);
      const isValid = currentChecksum === storedChecksum;

      return {
        isValid,
        originalChecksum: storedChecksum,
        currentChecksum,
        dataSize: originalData.length,
        timestamp,
        error: isValid ? undefined : 'Checksum mismatch detected'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        isValid: false,
        originalChecksum: storedChecksum,
        currentChecksum: '',
        dataSize: 0,
        timestamp: new Date(),
        error: `Integrity validation failed: ${errorMessage}`
      };
    }
  }

  /**
   * Validate encrypted backup data integrity
   */
  async validateEncryptedDataIntegrity(
    encryptedData: string,
    iv: string,
    salt: string,
    originalChecksum: string,
    algorithm: 'SHA-256' | 'SHA-512' | 'MD5' = this.DEFAULT_ALGORITHM
  ): Promise<IntegrityCheckResult> {
    try {
      // First, verify the encrypted data hasn't been tampered with
      const encryptedChecksum = this.generateChecksum(encryptedData, algorithm);
      
      // Decrypt the data
      const decryptionResult = await EncryptionService.decryptData(encryptedData, iv, salt);
      
      if (!decryptionResult.success || !decryptionResult.data) {
        return {
          isValid: false,
          originalChecksum,
          currentChecksum: encryptedChecksum,
          dataSize: encryptedData.length,
          timestamp: new Date(),
          error: 'Failed to decrypt data for integrity check'
        };
      }

      // Generate checksum of decrypted data
      const decryptedChecksum = this.generateChecksum(decryptionResult.data, algorithm);
      const isValid = decryptedChecksum === originalChecksum;

      return {
        isValid,
        originalChecksum,
        currentChecksum: decryptedChecksum,
        dataSize: decryptionResult.data.length,
        timestamp: new Date(),
        error: isValid ? undefined : 'Decrypted data checksum mismatch'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        isValid: false,
        originalChecksum,
        currentChecksum: '',
        dataSize: 0,
        timestamp: new Date(),
        error: `Encrypted data integrity check failed: ${errorMessage}`
      };
    }
  }

  /**
   * Validate backup session integrity
   */
  async validateBackupSessionIntegrity(
    sessionId: string,
    backupItems: Array<{
      id: string;
      type: 'contact' | 'message' | 'photo';
      encryptedData: string;
      iv: string;
      salt: string;
      checksum: string;
    }>
  ): Promise<BackupIntegrityReport> {
    const corruptedItems: CorruptedItemInfo[] = [];
    let itemsValid = 0;
    let itemsCorrupted = 0;

    console.log(`🔍 Validating integrity for backup session: ${sessionId}`);

    for (const item of backupItems) {
      try {
        // Validate encrypted data integrity
        const integrityResult = await this.validateEncryptedDataIntegrity(
          item.encryptedData,
          item.iv,
          item.salt,
          item.checksum
        );

        if (integrityResult.isValid) {
          itemsValid++;
        } else {
          itemsCorrupted++;
          corruptedItems.push({
            itemId: item.id,
            itemType: item.type,
            originalChecksum: item.checksum,
            currentChecksum: integrityResult.currentChecksum,
            corruptionType: 'checksum_mismatch',
            error: integrityResult.error || 'Unknown integrity error'
          });
        }

      } catch (error) {
        itemsCorrupted++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        corruptedItems.push({
          itemId: item.id,
          itemType: item.type,
          originalChecksum: item.checksum,
          currentChecksum: '',
          corruptionType: 'decryption_failed',
          error: errorMessage
        });
      }
    }

    const overallIntegrity = itemsCorrupted === 0;

    console.log(`✅ Integrity validation completed: ${itemsValid}/${backupItems.length} items valid`);

    return {
      sessionId,
      overallIntegrity,
      itemsChecked: backupItems.length,
      itemsValid,
      itemsCorrupted,
      corruptedItems,
      checksumAlgorithm: this.DEFAULT_ALGORITHM,
      validationTimestamp: new Date()
    };
  }

  /**
   * Generate integrity manifest for backup session
   */
  async generateIntegrityManifest(
    sessionId: string,
    backupItems: Array<{
      id: string;
      type: 'contact' | 'message' | 'photo';
      originalData: string;
      encryptedData: string;
      iv: string;
      salt: string;
    }>
  ): Promise<{
    sessionId: string;
    manifestVersion: string;
    createdAt: Date;
    totalItems: number;
    checksumAlgorithm: string;
    items: Array<{
      itemId: string;
      itemType: string;
      originalChecksum: string;
      encryptedChecksum: string;
      dataSize: number;
      encryptedSize: number;
    }>;
    sessionChecksum: string;
  }> {
    try {
      console.log(`📋 Generating integrity manifest for session: ${sessionId}`);

      const manifestItems = [];
      let manifestData = '';

      for (const item of backupItems) {
        const originalChecksum = this.generateChecksum(item.originalData);
        const encryptedChecksum = this.generateChecksum(item.encryptedData);

        const manifestItem = {
          itemId: item.id,
          itemType: item.type,
          originalChecksum,
          encryptedChecksum,
          dataSize: item.originalData.length,
          encryptedSize: item.encryptedData.length
        };

        manifestItems.push(manifestItem);
        manifestData += JSON.stringify(manifestItem);
      }

      // Generate overall session checksum
      const sessionChecksum = this.generateChecksum(manifestData);

      return {
        sessionId,
        manifestVersion: '1.0',
        createdAt: new Date(),
        totalItems: backupItems.length,
        checksumAlgorithm: this.DEFAULT_ALGORITHM,
        items: manifestItems,
        sessionChecksum
      };

    } catch (error) {
      console.error('Failed to generate integrity manifest:', error);
      throw new Error('Integrity manifest generation failed');
    }
  }

  /**
   * Verify backup completeness
   */
  async verifyBackupCompleteness(
    expectedItems: string[],
    actualItems: string[]
  ): Promise<{
    isComplete: boolean;
    missingItems: string[];
    extraItems: string[];
    completenessPercentage: number;
  }> {
    const expectedSet = new Set(expectedItems);
    const actualSet = new Set(actualItems);

    const missingItems = expectedItems.filter(item => !actualSet.has(item));
    const extraItems = actualItems.filter(item => !expectedSet.has(item));

    const completenessPercentage = expectedItems.length > 0 
      ? Math.round(((expectedItems.length - missingItems.length) / expectedItems.length) * 100)
      : 100;

    return {
      isComplete: missingItems.length === 0,
      missingItems,
      extraItems,
      completenessPercentage
    };
  }

  /**
   * Detect data corruption patterns
   */
  detectCorruptionPatterns(corruptedItems: CorruptedItemInfo[]): CorruptionPatternsResult {
    const patterns = new Map<string, number>();
    const recommendations: string[] = [];

    // Count corruption types
    corruptedItems.forEach(item => {
      patterns.set(item.corruptionType, (patterns.get(item.corruptionType) || 0) + 1);
    });

    const totalCorrupted = corruptedItems.length;
    const patternResults = Array.from(patterns.entries()).map(([type, count]) => ({
      type,
      count,
      percentage: Math.round((count / totalCorrupted) * 100),
      description: this.getCorruptionDescription(type)
    }));

    // Generate recommendations
    if (patterns.get('decryption_failed') && patterns.get('decryption_failed')! > totalCorrupted * 0.5) {
      recommendations.push('High decryption failure rate suggests key management issues');
    }

    if (patterns.get('checksum_mismatch') && patterns.get('checksum_mismatch')! > totalCorrupted * 0.3) {
      recommendations.push('Checksum mismatches indicate data transmission or storage corruption');
    }

    return {
      patterns: patternResults,
      recommendations
    };
  }

  /**
   * Get human-readable corruption description
   */
  private getCorruptionDescription(corruptionType: string): string {
    switch (corruptionType) {
      case 'checksum_mismatch':
        return 'Data has been modified or corrupted during storage/transmission';
      case 'decryption_failed':
        return 'Unable to decrypt data, possibly due to key issues or corruption';
      case 'data_missing':
        return 'Expected data is missing from backup';
      default:
        return 'Unknown corruption type';
    }
  }

  /**
   * Generate data integrity report
   */
  async generateIntegrityReport(
    sessionId: string,
    backupItems: Array<{
      id: string;
      type: 'contact' | 'message' | 'photo';
      encryptedData: string;
      iv: string;
      salt: string;
      checksum: string;
    }>
  ): Promise<{
    sessionId: string;
    integrityReport: BackupIntegrityReport;
    corruptionPatterns: CorruptionPatternsResult;
    recommendations: string[];
    overallScore: number;
    timestamp: Date;
  }> {
    const integrityReport = await this.validateBackupSessionIntegrity(sessionId, backupItems);
    const corruptionPatterns = this.detectCorruptionPatterns(integrityReport.corruptedItems);
    
    const recommendations = [
      ...corruptionPatterns.recommendations
    ];

    if (!integrityReport.overallIntegrity) {
      recommendations.push('Consider re-running backup for corrupted items');
    }

    // Calculate overall integrity score (0-100)
    const overallScore = integrityReport.itemsChecked > 0 
      ? Math.round((integrityReport.itemsValid / integrityReport.itemsChecked) * 100)
      : 0;

    return {
      sessionId,
      integrityReport,
      corruptionPatterns,
      recommendations,
      overallScore,
      timestamp: new Date()
    };
  }
}

export default new DataIntegrityService();