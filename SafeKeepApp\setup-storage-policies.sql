-- Storage Policies for SafeKeep App
-- Execute this in Supabase SQL Editor to set up storage access policies

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can upload their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Public can view thumbnails" ON storage.objects;

-- User Data Bucket Policies
-- Users can upload files to their own folder in user-data bucket
CREATE POLICY "Users can upload their own files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'user-data' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Users can view their own files in user-data bucket
CREATE POLICY "Users can view their own files" ON storage.objects
FOR SELECT USING (
  bucket_id = 'user-data' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Users can update their own files in user-data bucket
CREATE POLICY "Users can update their own files" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'user-data' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Users can delete their own files in user-data bucket
CREATE POLICY "Users can delete their own files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'user-data' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Thumbnails Bucket Policies
-- Anyone can view thumbnails (public bucket)
CREATE POLICY "Public can view thumbnails" ON storage.objects
FOR SELECT USING (bucket_id = 'thumbnails');

-- Users can upload thumbnails to their own folder
CREATE POLICY "Users can upload thumbnails" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'thumbnails' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Users can update their own thumbnails
CREATE POLICY "Users can update thumbnails" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'thumbnails' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Users can delete their own thumbnails
CREATE POLICY "Users can delete thumbnails" ON storage.objects
FOR DELETE USING (
  bucket_id = 'thumbnails' AND
  auth.uid()::text = (string_to_array(name, '/'))[1]
);

-- Allow service role to bypass RLS (for admin operations)
CREATE POLICY "Service role can manage all files" ON storage.objects
FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Grant necessary permissions
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.objects TO service_role;

-- Verify policies were created
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;