/**
 * Utility functions for Modular Pricing UI
 */

/**
 * Format price in cents to currency string
 * @param {number} cents - Price in cents
 * @param {string} currency - Currency code
 * @returns {string} Formatted price string
 */
function formatPrice(cents, currency = 'USD') {
    const dollars = cents / 100;
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(dollars);
}

/**
 * Calculate savings percentage
 * @param {number} originalPrice - Original price in cents
 * @param {number} discountedPrice - Discounted price in cents
 * @returns {number} Savings percentage (0-100)
 */
function calculateSavingsPercentage(originalPrice, discountedPrice) {
    if (originalPrice <= 0) return 0;
    return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
}

/**
 * Debounce function to limit API calls
 * @param {Function} func - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

/**
 * Check if device is mobile based on screen width
 * @returns {boolean} True if mobile device
 */
function isMobile() {
    return window.innerWidth < 768;
}

/**
 * Generate unique ID for components
 * @returns {string} Unique ID
 */
function generateId() {
    return 'modular-pricing-' + Math.random().toString(36).substr(2, 9);
}

/**
 * Animate number changes with smooth transitions
 * @param {HTMLElement} element - Element containing the number
 * @param {number} from - Starting number
 * @param {number} to - Ending number
 * @param {number} duration - Animation duration in ms
 */
function animateNumber(element, from, to, duration = 300) {
    const startTime = performance.now();
    const difference = to - from;
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.round(from + (difference * easeOutQuart));
        
        element.textContent = formatPrice(currentValue);
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * Add CSS class with animation support
 * @param {HTMLElement} element - Target element
 * @param {string} className - CSS class to add
 * @param {number} duration - Duration to keep class (optional)
 */
function addAnimatedClass(element, className, duration = null) {
    element.classList.add(className);
    
    if (duration) {
        setTimeout(() => {
            element.classList.remove(className);
        }, duration);
    }
}

/**
 * Create DOM element with attributes and content
 * @param {string} tag - HTML tag name
 * @param {Object} attributes - Element attributes
 * @param {string|HTMLElement[]} content - Element content
 * @returns {HTMLElement} Created element
 */
function createElement(tag, attributes = {}, content = '') {
    const element = document.createElement(tag);
    
    // Set attributes
    Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'className') {
            element.className = value;
        } else if (key === 'dataset') {
            Object.entries(value).forEach(([dataKey, dataValue]) => {
                element.dataset[dataKey] = dataValue;
            });
        } else {
            element.setAttribute(key, value);
        }
    });
    
    // Set content
    if (typeof content === 'string') {
        element.innerHTML = content;
    } else if (Array.isArray(content)) {
        content.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });
    }
    
    return element;
}

/**
 * Validate service combination
 * @param {string[]} serviceIds - Array of service IDs
 * @returns {Object} Validation result
 */
function validateServiceCombination(serviceIds) {
    const errors = [];
    const warnings = [];
    
    if (!Array.isArray(serviceIds)) {
        errors.push('Service IDs must be an array');
        return { isValid: false, errors, warnings };
    }
    
    if (serviceIds.length === 0) {
        warnings.push('No services selected');
    }
    
    const validServices = ['contacts', 'messages', 'photos'];
    const invalidServices = serviceIds.filter(id => !validServices.includes(id));
    
    if (invalidServices.length > 0) {
        errors.push(`Invalid service IDs: ${invalidServices.join(', ')}`);
    }
    
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}

// Export utility functions
if (typeof window !== 'undefined') {
    window.ModularPricingUtils = {
        formatPrice,
        calculateSavingsPercentage,
        debounce,
        isMobile,
        generateId,
        animateNumber,
        addAnimatedClass,
        createElement,
        validateServiceCombination
    };
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatPrice,
        calculateSavingsPercentage,
        debounce,
        isMobile,
        generateId,
        animateNumber,
        addAnimatedClass,
        createElement,
        validateServiceCombination
    };
}