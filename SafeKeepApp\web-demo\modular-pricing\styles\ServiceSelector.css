/* ServiceSelector Component Styles */

.service-selector {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
}

.service-selector.disabled {
    opacity: 0.7;
    pointer-events: none;
}

/* Header styles */
.service-selector-header {
    text-align: center;
    margin-bottom: 32px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.service-selector-title {
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.service-selector-subtitle {
    color: #666;
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

/* Services container */
.services-container {
    display: grid;
    gap: 16px;
    margin-bottom: 24px;
}

/* Selection summary */
.selection-summary {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #e8f5e8;
    margin-top: 24px;
}

.summary-title {
    color: #155724;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-title::before {
    content: '✓';
    background: #28a745;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.summary-list {
    display: grid;
    gap: 12px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8fff9;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.summary-item-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.summary-item-name {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.summary-item-price {
    font-weight: 600;
    color: #28a745;
    font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 1024px) {
    .services-container {
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .service-selector {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .service-selector-header {
        margin-bottom: 24px;
        padding-bottom: 16px;
    }
    
    .service-selector-title {
        font-size: 1.3rem;
    }
    
    .service-selector-subtitle {
        font-size: 0.9rem;
    }
    
    .selection-summary {
        padding: 16px;
        margin-top: 20px;
    }
    
    .summary-item {
        padding: 10px;
        gap: 10px;
    }
    
    .summary-item-icon {
        font-size: 16px;
    }
    
    .summary-item-name {
        font-size: 0.9rem;
    }
    
    .summary-item-price {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .service-selector {
        padding: 16px;
        border-radius: 12px;
    }
    
    .service-selector-header {
        margin-bottom: 20px;
    }
    
    .service-selector-title {
        font-size: 1.2rem;
    }
    
    .services-container {
        gap: 10px;
    }
    
    .summary-list {
        gap: 8px;
    }
    
    .summary-item {
        padding: 8px;
        flex-wrap: wrap;
    }
    
    .summary-item-price {
        margin-left: auto;
    }
}

/* Animation for summary appearance */
.selection-summary {
    animation: summarySlideIn 0.3s ease-out;
}

@keyframes summarySlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .service-selector {
        border: 2px solid #333;
    }
    
    .selection-summary {
        border-width: 3px;
        border-color: #000;
    }
    
    .summary-item {
        border-left-width: 6px;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .selection-summary {
        animation: none;
    }
}

/* Focus management for accessibility */
.service-selector:focus-within {
    outline: 2px solid #4facfe;
    outline-offset: 4px;
}

/* Loading state */
.service-selector.loading {
    position: relative;
    overflow: hidden;
}

.service-selector.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}