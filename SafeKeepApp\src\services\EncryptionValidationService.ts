import EncryptionService from './EncryptionService';
import CryptoJ<PERSON> from 'crypto-js';
import { BackupError } from '../types/backup';

export interface EncryptionValidationResult {
  success: boolean;
  validationResults: {
    contacts: DataTypeValidationResult;
    messages: DataTypeValidationResult;
    photos: DataTypeValidationResult;
  };
  errors: string[];
  timestamp: Date;
}

export interface DataTypeValidationResult {
  encrypted: boolean;
  decrypted: boolean;
  integrityValid: boolean;
  checksumMatch: boolean;
  error?: string;
}

export interface DataIntegrityResult {
  valid: boolean;
  originalChecksum: string;
  decryptedChecksum?: string;
  error?: string;
}

export interface TransmissionSecurityResult {
  isSecure: boolean;
  protocol: string;
  tlsVersion?: string;
  certificateValid?: boolean;
  securityIssues: string[];
}

export interface EncryptionAtRestResult {
  encrypted: boolean;
  storageSecure: boolean;
  checksumValid: boolean;
  encryptionAlgorithm?: string;
  keyRotationStatus?: string;
}

export interface ValidationTestData {
  contacts: any[];
  messages: any[];
  photos: any[];
}

class EncryptionValidationService {
  private readonly CHECKSUM_ALGORITHM = 'SHA-256';
  private readonly REQUIRED_TLS_VERSION = '1.2';

  /**
   * Validate encryption for all data types (contacts, messages, photos)
   */
  async validateEncryptionForAllDataTypes(testData: ValidationTestData): Promise<EncryptionValidationResult> {
    const errors: string[] = [];
    const validationResults = {
      contacts: await this.validateDataTypeEncryption('contacts', testData.contacts),
      messages: await this.validateDataTypeEncryption('messages', testData.messages),
      photos: await this.validateDataTypeEncryption('photos', testData.photos)
    };

    // Collect errors from all validations
    Object.entries(validationResults).forEach(([dataType, result]) => {
      if (result.error) {
        errors.push(`${dataType.charAt(0).toUpperCase() + dataType.slice(1)} encryption validation failed: ${result.error}`);
      }
    });

    return {
      success: errors.length === 0,
      validationResults,
      errors,
      timestamp: new Date()
    };
  }

  /**
   * Validate encryption for a specific data type
   */
  private async validateDataTypeEncryption(dataType: string, data: any[]): Promise<DataTypeValidationResult> {
    try {
      if (data.length === 0) {
        return {
          encrypted: true,
          decrypted: true,
          integrityValid: true,
          checksumMatch: true
        };
      }

      // Test with first item from the data array
      const testItem = data[0];
      const serializedData = JSON.stringify(testItem);

      // Step 1: Encrypt the data
      const encryptionResult = await EncryptionService.encryptData(serializedData);
      
      // Step 2: Verify encryption succeeded
      if (!encryptionResult.encryptedData || !encryptionResult.iv || !encryptionResult.salt) {
        throw new Error('Encryption result incomplete');
      }

      // Step 3: Decrypt the data
      const decryptionResult = await EncryptionService.decryptData(
        encryptionResult.encryptedData,
        encryptionResult.iv,
        encryptionResult.salt
      );

      if (!decryptionResult.success || !decryptionResult.data) {
        throw new Error('Decryption failed');
      }

      // Step 4: Verify data integrity
      const integrityResult = await this.validateDataIntegrity(
        serializedData,
        encryptionResult.encryptedData,
        encryptionResult.iv,
        encryptionResult.salt
      );

      return {
        encrypted: true,
        decrypted: true,
        integrityValid: integrityResult.valid,
        checksumMatch: integrityResult.valid,
        error: integrityResult.error
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        encrypted: false,
        decrypted: false,
        integrityValid: false,
        checksumMatch: false,
        error: errorMessage
      };
    }
  }

  /**
   * Validate data integrity using checksums
   */
  async validateDataIntegrity(
    originalData: string,
    encryptedData: string,
    iv: string,
    salt: string
  ): Promise<DataIntegrityResult> {
    try {
      // Generate checksum of original data
      const originalChecksum = CryptoJS.SHA256(originalData).toString(CryptoJS.enc.Hex);

      // Decrypt the data
      const decryptionResult = await EncryptionService.decryptData(encryptedData, iv, salt);
      
      if (!decryptionResult.success || !decryptionResult.data) {
        return {
          valid: false,
          originalChecksum,
          error: 'Failed to decrypt data for integrity check'
        };
      }

      // Generate checksum of decrypted data
      const decryptedChecksum = CryptoJS.SHA256(decryptionResult.data).toString(CryptoJS.enc.Hex);

      // Compare checksums
      const valid = originalChecksum === decryptedChecksum;

      return {
        valid,
        originalChecksum,
        decryptedChecksum,
        error: valid ? undefined : 'Data integrity check failed - checksums do not match'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        valid: false,
        originalChecksum: '',
        error: `Integrity validation error: ${errorMessage}`
      };
    }
  }

  /**
   * Validate data transmission security
   */
  async validateTransmissionSecurity(endpoint: string): Promise<TransmissionSecurityResult> {
    const securityIssues: string[] = [];
    
    try {
      const url = new URL(endpoint);
      const protocol = url.protocol.replace(':', '');

      // Check protocol security
      if (protocol !== 'https') {
        securityIssues.push('Insecure HTTP protocol detected');
      }

      // Simulate TLS validation (in real app, would use native modules)
      const tlsVersion = await this.validateTLSVersion(endpoint);
      if (tlsVersion && parseFloat(tlsVersion) < parseFloat(this.REQUIRED_TLS_VERSION)) {
        securityIssues.push(`TLS version ${tlsVersion} is below required ${this.REQUIRED_TLS_VERSION}`);
      }

      // Check certificate validity (simulated)
      const certificateValid = await this.validateCertificate(endpoint);
      if (!certificateValid) {
        securityIssues.push('Invalid or expired SSL certificate');
      }

      return {
        isSecure: securityIssues.length === 0,
        protocol,
        tlsVersion,
        certificateValid,
        securityIssues
      };

    } catch (error) {
      securityIssues.push(`URL validation error: ${error instanceof Error ? error.message : String(error)}`);
      
      return {
        isSecure: false,
        protocol: 'unknown',
        securityIssues
      };
    }
  }

  /**
   * Validate TLS version (simulated - would use native implementation)
   */
  private async validateTLSVersion(endpoint: string): Promise<string> {
    // In a real implementation, this would use native modules to check TLS
    // For testing purposes, we'll simulate a secure TLS version
    return '1.3';
  }

  /**
   * Validate SSL certificate (simulated - would use native implementation)
   */
  private async validateCertificate(endpoint: string): Promise<boolean> {
    // In a real implementation, this would validate the actual certificate
    // For testing purposes, we'll assume HTTPS endpoints have valid certificates
    return endpoint.startsWith('https://');
  }

  /**
   * Validate encryption at rest in Supabase storage
   */
  async validateEncryptionAtRest(storageData: {
    encryptedData: string;
    iv: string;
    salt: string;
    checksum: string;
  }): Promise<EncryptionAtRestResult> {
    try {
      // Verify data is encrypted (not plaintext)
      const isEncrypted = this.verifyDataIsEncrypted(storageData.encryptedData);
      
      // Verify storage security (Supabase uses AES-256 encryption at rest)
      const storageSecure = await this.verifySupabaseStorageSecurity();
      
      // Validate checksum
      const checksumValid = await this.validateStoredChecksum(storageData);

      return {
        encrypted: isEncrypted,
        storageSecure,
        checksumValid,
        encryptionAlgorithm: 'AES-256-CBC',
        keyRotationStatus: 'active'
      };

    } catch (error) {
      console.error('Encryption at rest validation failed:', error);
      return {
        encrypted: false,
        storageSecure: false,
        checksumValid: false
      };
    }
  }

  /**
   * Verify data appears to be encrypted (not plaintext)
   */
  private verifyDataIsEncrypted(data: string): boolean {
    try {
      // Check if data looks like base64 encrypted content
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(data)) {
        return false;
      }

      // Check entropy - encrypted data should have high entropy
      const entropy = this.calculateEntropy(data);
      return entropy > 4.0; // Threshold for encrypted-like data

    } catch (error) {
      return false;
    }
  }

  /**
   * Calculate Shannon entropy of a string
   */
  private calculateEntropy(str: string): number {
    const len = str.length;
    const frequencies: { [key: string]: number } = {};
    
    // Count character frequencies
    for (let i = 0; i < len; i++) {
      const char = str[i];
      frequencies[char] = (frequencies[char] || 0) + 1;
    }
    
    // Calculate entropy
    let entropy = 0;
    for (const char in frequencies) {
      const p = frequencies[char] / len;
      entropy -= p * Math.log2(p);
    }
    
    return entropy;
  }

  /**
   * Verify Supabase storage security configuration
   */
  private async verifySupabaseStorageSecurity(): Promise<boolean> {
    // In a real implementation, this would check Supabase security settings
    // For now, we assume Supabase provides secure storage
    return true;
  }

  /**
   * Validate stored checksum matches data
   */
  private async validateStoredChecksum(storageData: {
    encryptedData: string;
    checksum: string;
  }): Promise<boolean> {
    try {
      const calculatedChecksum = CryptoJS.SHA256(storageData.encryptedData).toString(CryptoJS.enc.Hex);
      return calculatedChecksum === storageData.checksum;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate backup data against Supabase storage security requirements
   */
  async validateSupabaseStorageSecurity(backupData: {
    sessionId: string;
    encryptedItems: Array<{
      id: string;
      type: 'contact' | 'message' | 'photo';
      encryptedData: string;
      checksum: string;
    }>;
  }): Promise<{
    isSecure: boolean;
    encryptionCompliant: boolean;
    checksumValid: boolean;
    storageCompliant: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];
    let encryptionCompliant = true;
    let checksumValid = true;

    // Validate each encrypted item
    for (const item of backupData.encryptedItems) {
      // Check if data appears encrypted
      if (!this.verifyDataIsEncrypted(item.encryptedData)) {
        encryptionCompliant = false;
        issues.push(`Item ${item.id} does not appear to be properly encrypted`);
      }

      // Validate checksum format
      if (!item.checksum || item.checksum.length !== 64) {
        checksumValid = false;
        issues.push(`Item ${item.id} has invalid checksum format`);
      }
    }

    // Validate session ID format
    if (!backupData.sessionId || backupData.sessionId.length < 10) {
      issues.push('Invalid session ID format');
    }

    const storageCompliant = await this.verifySupabaseStorageSecurity();

    return {
      isSecure: issues.length === 0,
      encryptionCompliant,
      checksumValid,
      storageCompliant,
      issues
    };
  }

  /**
   * Perform comprehensive security audit of backup system
   */
  async performSecurityAudit(): Promise<{
    auditId: string;
    timestamp: Date;
    overallScore: number; // 0-100
    categories: {
      encryption: { score: number; issues: string[] };
      keyManagement: { score: number; issues: string[] };
      dataIntegrity: { score: number; issues: string[] };
      transmission: { score: number; issues: string[] };
      storage: { score: number; issues: string[] };
    };
    recommendations: Array<{
      priority: 'high' | 'medium' | 'low';
      category: string;
      issue: string;
      recommendation: string;
    }>;
  }> {
    const auditId = `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const recommendations: Array<{
      priority: 'high' | 'medium' | 'low';
      category: string;
      issue: string;
      recommendation: string;
    }> = [];

    // Test encryption capabilities
    const encryptionScore = await this.auditEncryptionSecurity();
    
    // Test key management
    const keyManagementScore = await this.auditKeyManagementSecurity();
    
    // Test data integrity
    const dataIntegrityScore = await this.auditDataIntegritySecurity();
    
    // Test transmission security
    const transmissionScore = await this.auditTransmissionSecurity();
    
    // Test storage security
    const storageScore = await this.auditStorageSecurity();

    const categories = {
      encryption: encryptionScore,
      keyManagement: keyManagementScore,
      dataIntegrity: dataIntegrityScore,
      transmission: transmissionScore,
      storage: storageScore
    };

    // Calculate overall score
    const overallScore = Math.round(
      (encryptionScore.score + keyManagementScore.score + dataIntegrityScore.score + 
       transmissionScore.score + storageScore.score) / 5
    );

    // Generate recommendations based on issues
    Object.entries(categories).forEach(([category, result]) => {
      result.issues.forEach(issue => {
        const priority = result.score < 50 ? 'high' : result.score < 80 ? 'medium' : 'low';
        recommendations.push({
          priority,
          category,
          issue,
          recommendation: this.generateRecommendation(category, issue)
        });
      });
    });

    return {
      auditId,
      timestamp: new Date(),
      overallScore,
      categories,
      recommendations
    };
  }

  /**
   * Audit encryption security
   */
  private async auditEncryptionSecurity(): Promise<{ score: number; issues: string[] }> {
    const issues: string[] = [];
    let score = 100;

    try {
      // Test basic encryption functionality
      const testData = 'encryption audit test data';
      const encrypted = await EncryptionService.encryptData(testData);
      
      if (!encrypted.encryptedData || !encrypted.iv || !encrypted.salt) {
        issues.push('Encryption service not functioning properly');
        score -= 30;
      }

      // Test decryption
      const decrypted = await EncryptionService.decryptData(
        encrypted.encryptedData,
        encrypted.iv,
        encrypted.salt
      );

      if (!decrypted.success || decrypted.data !== testData) {
        issues.push('Decryption functionality compromised');
        score -= 30;
      }

      // Test encryption strength
      if (!this.verifyDataIsEncrypted(encrypted.encryptedData)) {
        issues.push('Encryption appears weak or ineffective');
        score -= 20;
      }

    } catch (error) {
      issues.push('Encryption service unavailable or failing');
      score -= 50;
    }

    return { score: Math.max(0, score), issues };
  }

  /**
   * Audit key management security
   */
  private async auditKeyManagementSecurity(): Promise<{ score: number; issues: string[] }> {
    const issues: string[] = [];
    let score = 100;

    try {
      // Import SecureKeyManagementService dynamically to avoid circular dependency
      const SecureKeyManagementService = (await import('./SecureKeyManagementService')).default;
      
      const keyStatus = await SecureKeyManagementService.getKeyManagementStatus();
      
      if (!keyStatus.hasKey) {
        issues.push('No encryption key found');
        score -= 40;
      }

      if (keyStatus.needsRotation) {
        issues.push('Encryption key needs rotation');
        score -= 20;
      }

      if (keyStatus.securityLevel === 'low') {
        issues.push('Key security level is low');
        score -= 30;
      } else if (keyStatus.securityLevel === 'medium') {
        issues.push('Key security level is medium');
        score -= 15;
      }

      if (keyStatus.backupCount === 0) {
        issues.push('No key backups found');
        score -= 10;
      }

    } catch (error) {
      issues.push('Key management service unavailable');
      score -= 50;
    }

    return { score: Math.max(0, score), issues };
  }

  /**
   * Audit data integrity security
   */
  private async auditDataIntegritySecurity(): Promise<{ score: number; issues: string[] }> {
    const issues: string[] = [];
    let score = 100;

    try {
      // Import DataIntegrityService dynamically
      const DataIntegrityService = (await import('./DataIntegrityService')).default;
      
      // Test checksum generation
      const testData = 'integrity audit test data';
      const checksum = DataIntegrityService.generateChecksum(testData);
      
      if (!checksum || checksum.length !== 64) {
        issues.push('Checksum generation not working properly');
        score -= 30;
      }

      // Test integrity validation
      const integrityResult = await DataIntegrityService.validateDataIntegrity(
        testData,
        checksum
      );

      if (!integrityResult.isValid) {
        issues.push('Data integrity validation failing');
        score -= 30;
      }

    } catch (error) {
      issues.push('Data integrity service unavailable');
      score -= 50;
    }

    return { score: Math.max(0, score), issues };
  }

  /**
   * Audit transmission security
   */
  private async auditTransmissionSecurity(): Promise<{ score: number; issues: string[] }> {
    const issues: string[] = [];
    let score = 100;

    try {
      const transmissionResult = await this.validateTransmissionSecurity('https://api.supabase.co');
      
      if (!transmissionResult.isSecure) {
        issues.push('Transmission security validation failed');
        score -= 40;
      }

      if (transmissionResult.protocol !== 'https') {
        issues.push('Insecure transmission protocol detected');
        score -= 30;
      }

      if (transmissionResult.tlsVersion && parseFloat(transmissionResult.tlsVersion) < 1.2) {
        issues.push('TLS version below security requirements');
        score -= 20;
      }

    } catch (error) {
      issues.push('Transmission security validation unavailable');
      score -= 30;
    }

    return { score: Math.max(0, score), issues };
  }

  /**
   * Audit storage security
   */
  private async auditStorageSecurity(): Promise<{ score: number; issues: string[] }> {
    const issues: string[] = [];
    let score = 100;

    try {
      const testStorageData = {
        encryptedData: 'test_encrypted_storage_data',
        iv: 'test_iv',
        salt: 'test_salt',
        checksum: 'test_checksum'
      };

      const storageResult = await this.validateEncryptionAtRest(testStorageData);
      
      if (!storageResult.encrypted) {
        issues.push('Storage encryption validation failed');
        score -= 30;
      }

      if (!storageResult.storageSecure) {
        issues.push('Storage security configuration issues');
        score -= 25;
      }

      if (!storageResult.checksumValid) {
        issues.push('Storage checksum validation failed');
        score -= 20;
      }

    } catch (error) {
      issues.push('Storage security validation unavailable');
      score -= 30;
    }

    return { score: Math.max(0, score), issues };
  }

  /**
   * Generate recommendation for security issue
   */
  private generateRecommendation(category: string, issue: string): string {
    const recommendations: { [key: string]: { [key: string]: string } } = {
      encryption: {
        'Encryption service not functioning properly': 'Verify encryption service configuration and dependencies',
        'Decryption functionality compromised': 'Check encryption keys and algorithm implementation',
        'Encryption appears weak or ineffective': 'Review encryption algorithm and key strength settings'
      },
      keyManagement: {
        'No encryption key found': 'Initialize encryption key management system',
        'Encryption key needs rotation': 'Perform key rotation to maintain security',
        'Key security level is low': 'Upgrade key generation parameters and storage security',
        'No key backups found': 'Create secure backups of encryption keys'
      },
      dataIntegrity: {
        'Checksum generation not working properly': 'Verify checksum algorithm implementation',
        'Data integrity validation failing': 'Review data integrity validation logic and dependencies'
      },
      transmission: {
        'Transmission security validation failed': 'Ensure all data transmission uses HTTPS with proper TLS',
        'Insecure transmission protocol detected': 'Upgrade to HTTPS for all data transmission',
        'TLS version below security requirements': 'Upgrade TLS to version 1.2 or higher'
      },
      storage: {
        'Storage encryption validation failed': 'Verify storage encryption configuration',
        'Storage security configuration issues': 'Review and update storage security settings',
        'Storage checksum validation failed': 'Fix checksum validation for stored data'
      }
    };

    return recommendations[category]?.[issue] || 'Review and address the identified security issue';
  }

  /**
   * Generate comprehensive security report
   */
  async generateSecurityReport(testData: ValidationTestData): Promise<{
    overallSecurity: 'secure' | 'warning' | 'insecure';
    encryptionValidation: EncryptionValidationResult;
    transmissionSecurity: TransmissionSecurityResult;
    storageEncryption: EncryptionAtRestResult;
    recommendations: string[];
    timestamp: Date;
  }> {
    const recommendations: string[] = [];
    
    // Run all validations
    const encryptionValidation = await this.validateEncryptionForAllDataTypes(testData);
    const transmissionSecurity = await this.validateTransmissionSecurity('https://api.supabase.co');
    const storageEncryption = await this.validateEncryptionAtRest({
      encryptedData: 'sample_encrypted_data',
      iv: 'sample_iv',
      salt: 'sample_salt',
      checksum: 'sample_checksum'
    });

    // Generate recommendations
    if (!encryptionValidation.success) {
      recommendations.push('Fix encryption validation issues for data types');
    }
    
    if (!transmissionSecurity.isSecure) {
      recommendations.push('Ensure all data transmission uses HTTPS with TLS 1.2+');
    }
    
    if (!storageEncryption.encrypted || !storageEncryption.storageSecure) {
      recommendations.push('Verify encryption at rest configuration');
    }

    // Determine overall security level
    let overallSecurity: 'secure' | 'warning' | 'insecure' = 'secure';
    
    if (!encryptionValidation.success || !transmissionSecurity.isSecure || !storageEncryption.encrypted) {
      overallSecurity = 'insecure';
    } else if (recommendations.length > 0) {
      overallSecurity = 'warning';
    }

    return {
      overallSecurity,
      encryptionValidation,
      transmissionSecurity,
      storageEncryption,
      recommendations,
      timestamp: new Date()
    };
  }
}

export default new EncryptionValidationService();