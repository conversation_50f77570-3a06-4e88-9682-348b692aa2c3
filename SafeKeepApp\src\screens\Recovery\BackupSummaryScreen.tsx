import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { 
  Button, 
  Card, 
  Text, 
  ActivityIndicator,
  Chip,
  Divider
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

import RecoveryService, { BackupSummary, BackupCategory } from '../../services/RecoveryService';
import { COLORS, SPACING } from '../../utils/constants';

const BackupSummaryScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [backupSummary, setBackupSummary] = useState<BackupSummary | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadBackupSummary();
  }, []);

  const loadBackupSummary = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('📊 Loading backup summary...');
      const summary = await RecoveryService.fetchBackupSummary();
      
      if (summary) {
        setBackupSummary(summary);
        console.log('✅ Backup summary loaded successfully');
      } else {
        setError('No backup data found for your account.');
      }
    } catch (error) {
      console.error('❌ Failed to load backup summary:', error);
      setError('Failed to load backup data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestoreAll = () => {
    if (!backupSummary) return;

    const totalItems = backupSummary.totalPhotos + backupSummary.totalContacts + 
                     backupSummary.totalMessages + backupSummary.totalDocuments;

    Alert.alert(
      '🔄 Restore All Data?',
      `This will restore all your backed up data:\n\n` +
      `• ${backupSummary.totalPhotos} photos\n` +
      `• ${backupSummary.totalContacts} contacts\n` +
      `• ${backupSummary.totalMessages} messages\n` +
      `• ${backupSummary.totalDocuments} documents\n\n` +
      `Total: ${totalItems} items (${backupSummary.storageUsed})\n\n` +
      `This may take several minutes depending on your internet connection.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Restore All', 
          onPress: () => startRestoreAll()
        }
      ]
    );
  };

  const startRestoreAll = () => {
    // Navigate to restore selection with all categories selected
    navigation.navigate('RestoreSelection' as never, {
      preselectedOptions: {
        selectAll: true,
        categories: {
          photos: { selected: true, downloadWifi: true, downloadCellular: false },
          contacts: { selected: true, mergeStrategy: 'smart' },
          messages: { selected: true, format: 'native' },
          documents: { selected: true }
        }
      }
    } as never);
  };

  const handleSelectiveRestore = () => {
    // Navigate to restore selection screen
    navigation.navigate('RestoreSelection' as never);
  };

  const getCategoryIcon = (type: string): string => {
    switch (type) {
      case 'photo': return 'camera';
      case 'contact': return 'contacts';
      case 'message': return 'message-text';
      case 'document': return 'file-document';
      default: return 'file';
    }
  };

  const getCategoryColor = (type: string): string => {
    switch (type) {
      case 'photo': return '#4CAF50';
      case 'contact': return '#2196F3';
      case 'message': return '#FF9800';
      case 'document': return '#9C27B0';
      default: return COLORS.primary;
    }
  };

  const formatCategoryName = (type: string): string => {
    switch (type) {
      case 'photo': return 'Photos';
      case 'contact': return 'Contacts';
      case 'message': return 'Messages';
      case 'document': return 'Documents';
      default: return type;
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          centerComponent={{
            text: 'Your Backup Data',
            style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
          }}
          backgroundColor={COLORS.primary}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading your backup data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !backupSummary) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          centerComponent={{
            text: 'Your Backup Data',
            style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
          }}
          backgroundColor={COLORS.primary}
        />
        <View style={styles.errorContainer}>
          <Icon name="alert-circle" size={64} color={COLORS.error} />
          <Text style={styles.errorTitle}>No Backup Data Found</Text>
          <Text style={styles.errorText}>
            {error || 'We couldn\'t find any backup data for your account. Please make sure you\'re signed in with the correct email address.'}
          </Text>
          <Button
            mode="contained"
            onPress={loadBackupSummary}
            style={styles.retryButton}
            icon="refresh"
          >
            Try Again
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  const totalItems = backupSummary.totalPhotos + backupSummary.totalContacts + 
                    backupSummary.totalMessages + backupSummary.totalDocuments;

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Your Backup Data',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        {/* Welcome Message */}
        <Card style={styles.welcomeCard}>
          <Card.Content>
            <View style={styles.welcomeHeader}>
              <Icon name="backup-restore" size={32} color={COLORS.success} />
              <Text variant="headlineSmall" style={styles.welcomeTitle}>
                Welcome Back!
              </Text>
            </View>
            
            <Text style={styles.welcomeText}>
              We found your backup data! You can restore all your important information to this device.
            </Text>

            <View style={styles.summaryStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{totalItems}</Text>
                <Text style={styles.statLabel}>Total Items</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{backupSummary.storageUsed}</Text>
                <Text style={styles.statLabel}>Data Size</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{backupSummary.lastBackup}</Text>
                <Text style={styles.statLabel}>Last Backup</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Data Categories */}
        <Text variant="titleLarge" style={styles.sectionTitle}>
          Available Data
        </Text>

        {backupSummary.categories.map((category, index) => (
          <Card key={category.type} style={styles.categoryCard}>
            <Card.Content>
              <View style={styles.categoryHeader}>
                <View style={styles.categoryInfo}>
                  <Icon 
                    name={getCategoryIcon(category.type)} 
                    size={24} 
                    color={getCategoryColor(category.type)} 
                  />
                  <View style={styles.categoryDetails}>
                    <Text variant="titleMedium" style={styles.categoryName}>
                      {formatCategoryName(category.type)}
                    </Text>
                    <Text style={styles.categorySubtext}>
                      Last updated: {new Date(category.lastModified).toLocaleDateString()}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.categoryStats}>
                  <Chip style={[styles.countChip, { backgroundColor: getCategoryColor(category.type) }]}>
                    <Text style={styles.countText}>{category.count}</Text>
                  </Chip>
                  <Text style={styles.sizeText}>{category.size}</Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        ))}

        {/* Action Buttons */}
        <Card style={styles.actionCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.actionTitle}>
              Ready to Restore Your Data?
            </Text>
            
            <Text style={styles.actionDescription}>
              Choose how you want to restore your backed up information. 
              You can restore everything at once or select specific categories.
            </Text>

            <View style={styles.actionButtons}>
              <Button
                mode="contained"
                onPress={handleRestoreAll}
                style={styles.primaryButton}
                icon="restore"
                contentStyle={styles.buttonContent}
              >
                Restore All Data
              </Button>

              <Button
                mode="outlined"
                onPress={handleSelectiveRestore}
                style={styles.secondaryButton}
                icon="playlist-check"
                contentStyle={styles.buttonContent}
              >
                Choose What to Restore
              </Button>
            </View>

            <View style={styles.infoBox}>
              <Icon name="information" size={16} color={COLORS.primary} />
              <Text style={styles.infoText}>
                Your data is encrypted and will be decrypted securely on this device. 
                The restore process may take several minutes.
              </Text>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  loadingText: {
    marginTop: SPACING.md,
    color: COLORS.textSecondary,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  errorText: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
  },
  welcomeCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  welcomeTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  welcomeText: {
    color: COLORS.textSecondary,
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: 4,
  },
  sectionTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
    marginTop: SPACING.sm,
  },
  categoryCard: {
    marginBottom: SPACING.md,
    elevation: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryDetails: {
    marginLeft: SPACING.sm,
    flex: 1,
  },
  categoryName: {
    color: COLORS.text,
  },
  categorySubtext: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  categoryStats: {
    alignItems: 'flex-end',
  },
  countChip: {
    marginBottom: 4,
  },
  countText: {
    color: 'white',
    fontWeight: 'bold',
  },
  sizeText: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  actionCard: {
    marginTop: SPACING.md,
    elevation: 4,
  },
  actionTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  actionDescription: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  actionButtons: {
    gap: SPACING.sm,
    marginBottom: SPACING.md,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    borderColor: COLORS.primary,
  },
  buttonContent: {
    paddingVertical: SPACING.xs,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: COLORS.background,
    padding: SPACING.sm,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.primary,
  },
  infoText: {
    marginLeft: SPACING.xs,
    color: COLORS.textSecondary,
    fontSize: 12,
    lineHeight: 16,
    flex: 1,
  },
});

export default BackupSummaryScreen;
