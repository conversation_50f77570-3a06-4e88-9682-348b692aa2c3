/* PlanRecommendations Component Styles */

.plan-recommendations {
    margin: 24px 0;
}

/* Header */
.recommendations-header {
    text-align: center;
    margin-bottom: 32px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.recommendations-title {
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.recommendations-title::before {
    content: '⭐';
    font-size: 1.2rem;
}

.recommendations-subtitle {
    color: #666;
    font-size: 1rem;
    margin: 0;
    line-height: 1.5;
}

/* Plans container */
.plans-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Plan card */
.plan-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 24px;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.plan-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #4facfe;
}

.plan-card.popular {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff9e6 0%, #fffbf0 100%);
    transform: scale(1.05);
}

.plan-card.popular:hover {
    transform: scale(1.05) translateY(-4px);
}

.plan-card.recommended {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.plan-card.current {
    border-color: #4facfe;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

/* Badges */
.popular-badge,
.recommended-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    z-index: 1;
}

.popular-badge {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4);
}

.recommended-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4);
}

/* Plan header */
.plan-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f8f9fa;
}

.plan-name {
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.plan-price {
    margin-bottom: 8px;
}

.price-amount {
    color: #4facfe;
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.price-frequency {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin-left: 4px;
}

.plan-savings {
    background: #28a745;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-block;
    margin-top: 8px;
}

/* Services section */
.plan-services {
    flex: 1;
    margin-bottom: 20px;
}

.services-title {
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.services-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    gap: 8px;
}

.service-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(79, 172, 254, 0.05);
    border-radius: 8px;
    border-left: 3px solid #4facfe;
    transition: all 0.2s ease;
}

.service-item:hover {
    background: rgba(79, 172, 254, 0.1);
    transform: translateX(4px);
}

.check-icon {
    color: #28a745;
    font-weight: bold;
    font-size: 14px;
    width: 16px;
    height: 16px;
    background: #e8f5e8;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.service-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.service-name {
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
    flex: 1;
}

/* Action button */
.plan-action-btn {
    width: 100%;
    padding: 14px 20px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: auto;
}

.plan-action-btn.select {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.plan-action-btn.select:hover {
    background: linear-gradient(135deg, #2196f3 0%, #00bcd4 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.plan-action-btn.current {
    background: #28a745;
    color: white;
    cursor: default;
}

.plan-action-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Highlight animation */
.plan-card.plan-highlight {
    animation: planHighlight 1s ease-out;
}

@keyframes planHighlight {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.4);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 15px rgba(79, 172, 254, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
    }
}

/* Mobile responsive design */
@media (max-width: 768px) {
    .plans-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .plan-card {
        padding: 20px;
    }
    
    .plan-card.popular {
        transform: none;
    }
    
    .plan-card.popular:hover {
        transform: translateY(-4px);
    }
    
    .recommendations-header {
        margin-bottom: 24px;
        padding-bottom: 16px;
    }
    
    .recommendations-title {
        font-size: 1.3rem;
    }
    
    .recommendations-subtitle {
        font-size: 0.9rem;
    }
    
    .plan-name {
        font-size: 1.2rem;
    }
    
    .price-amount {
        font-size: 1.8rem;
    }
    
    .service-item {
        padding: 6px 10px;
    }
    
    .service-name {
        font-size: 0.85rem;
    }
    
    .plan-action-btn {
        padding: 12px 16px;
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .plan-card {
        padding: 16px;
        border-radius: 12px;
    }
    
    .popular-badge,
    .recommended-badge {
        font-size: 0.75rem;
        padding: 4px 12px;
    }
    
    .plan-header {
        margin-bottom: 16px;
        padding-bottom: 12px;
    }
    
    .plan-name {
        font-size: 1.1rem;
    }
    
    .price-amount {
        font-size: 1.6rem;
    }
    
    .price-frequency {
        font-size: 0.9rem;
    }
    
    .plan-savings {
        font-size: 0.8rem;
        padding: 3px 10px;
    }
    
    .services-title {
        font-size: 0.95rem;
    }
    
    .service-item {
        padding: 6px 8px;
        gap: 8px;
    }
    
    .service-icon {
        font-size: 16px;
    }
    
    .service-name {
        font-size: 0.8rem;
    }
    
    .check-icon {
        width: 14px;
        height: 14px;
        font-size: 12px;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .plan-card {
        border-width: 3px;
    }
    
    .plan-card.popular {
        border-color: #000;
        background: #fff;
    }
    
    .plan-card.recommended {
        border-color: #000;
        background: #fff;
    }
    
    .plan-card.current {
        border-color: #000;
        background: #fff;
    }
    
    .popular-badge,
    .recommended-badge {
        background: #000;
        color: #fff;
    }
    
    .service-item {
        border-left-width: 4px;
        border-left-color: #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .plan-card,
    .service-item,
    .plan-action-btn {
        transition: none;
    }
    
    .plan-card:hover,
    .plan-card.popular:hover {
        transform: none;
    }
    
    .plan-card.plan-highlight {
        animation: none;
    }
    
    .service-item:hover {
        transform: none;
    }
    
    .plan-action-btn.select:hover {
        transform: none;
    }
}

/* Focus management */
.plan-recommendations:focus-within {
    outline: 2px solid #4facfe;
    outline-offset: 4px;
    border-radius: 16px;
}

.plan-action-btn:focus {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .plan-card {
        break-inside: avoid;
        box-shadow: none;
        border: 2px solid #000;
    }
    
    .popular-badge,
    .recommended-badge {
        background: #000;
        color: #fff;
    }
    
    .plan-action-btn {
        display: none;
    }
}