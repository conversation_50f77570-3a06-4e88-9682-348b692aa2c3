/**
 * ModularPricingUI - Main Component
 * Orchestrates the entire modular pricing interface
 */

class ModularPricingUI {
    constructor(props) {
        this.props = {
            userId: null,
            onSubscriptionSelect: null,
            onPriceUpdate: null,
            initialServices: [],
            theme: 'light',
            showRecommendations: true,
            apiBaseUrl: null,
            ...props
        };
        
        this.state = {
            selectedServices: [...(this.props.initialServices || [])],
            availableServices: [],
            currentPricing: null,
            isLoading: false,
            showServiceDetails: null,
            animationState: 'idle'
        };
        
        this.element = null;
        this.components = {};
        
        this.initialize();
    }

    async initialize() {
        this.loadAvailableServices();
        this.render();
        this.attachEventListeners();
        
        // Initial pricing calculation
        if (this.state.selectedServices.length > 0) {
            await this.updatePricing();
        }
    }

    loadAvailableServices() {
        const { SERVICE_CONFIG } = window.ModularPricingConfig;
        
        this.state.availableServices = Object.values(SERVICE_CONFIG);
    }

    render() {
        this.element = window.ModularPricingUtils.createElement('div', {
            className: `modular-pricing-ui theme-${this.props.theme}`,
            dataset: { userId: this.props.userId || 'anonymous' }
        });

        // Create main layout
        const container = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-container'
        });

        // Header section
        const header = this.createHeader();
        
        // Main content area
        const mainContent = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-main-content'
        });

        // Left column - Service selection
        const leftColumn = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-left-column'
        });

        // Right column - Pricing and recommendations
        const rightColumn = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-right-column'
        });

        // Create components
        this.createServiceSelector(leftColumn);
        this.createPricingSection(rightColumn);
        
        if (this.props.showRecommendations) {
            this.createRecommendationsSection(rightColumn);
        }

        // Action section
        const actionSection = this.createActionSection();

        mainContent.appendChild(leftColumn);
        mainContent.appendChild(rightColumn);

        container.appendChild(header);
        container.appendChild(mainContent);
        container.appendChild(actionSection);

        this.element.appendChild(container);
    }

    createHeader() {
        const header = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-header'
        });

        const title = window.ModularPricingUtils.createElement('h1', {
            className: 'pricing-title'
        }, 'Choose Your Backup Plan');

        const subtitle = window.ModularPricingUtils.createElement('p', {
            className: 'pricing-subtitle'
        }, 'Select individual services or save with our bundle options');

        header.appendChild(title);
        header.appendChild(subtitle);

        return header;
    }

    createServiceSelector(container) {
        this.components.serviceSelector = new window.ServiceSelector({
            services: this.state.availableServices,
            selectedServices: this.state.selectedServices,
            onServiceToggle: this.handleServiceToggle.bind(this),
            onServiceDetailsToggle: this.handleServiceDetailsToggle.bind(this),
            showDetails: this.state.showServiceDetails,
            disabled: this.state.isLoading
        });

        container.appendChild(this.components.serviceSelector.getElement());
    }

    createPricingSection(container) {
        const pricingSection = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-section'
        });

        // Pricing calculator
        this.components.pricingCalculator = new window.PricingCalculator({
            selectedServices: this.state.selectedServices,
            onPricingUpdate: this.handlePricingUpdate.bind(this),
            animateChanges: true,
            apiBaseUrl: this.props.apiBaseUrl
        });

        pricingSection.appendChild(this.components.pricingCalculator.getElement());

        // Savings display
        this.components.savingsDisplay = new window.SavingsDisplay({
            savings: 0,
            individualTotal: 0,
            bundlePrice: 0,
            currency: 'USD',
            showComparison: true,
            animate: true
        });

        pricingSection.appendChild(this.components.savingsDisplay.getElement());

        container.appendChild(pricingSection);
    }

    createRecommendationsSection(container) {
        this.components.planRecommendations = new window.PlanRecommendations({
            plans: [],
            selectedServices: this.state.selectedServices,
            onPlanSelect: this.handlePlanSelect.bind(this),
            highlightPopular: true
        });

        container.appendChild(this.components.planRecommendations.getElement());
    }

    createActionSection() {
        const actionSection = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-actions'
        });

        const continueButton = window.ModularPricingUtils.createElement('button', {
            className: 'continue-btn primary',
            type: 'button',
            disabled: this.state.selectedServices.length === 0
        }, 'Continue to Checkout');

        const summaryText = window.ModularPricingUtils.createElement('p', {
            className: 'action-summary'
        }, this.getActionSummaryText());

        continueButton.addEventListener('click', this.handleContinue.bind(this));

        actionSection.appendChild(summaryText);
        actionSection.appendChild(continueButton);

        return actionSection;
    }

    getActionSummaryText() {
        const { selectedServices, currentPricing } = this.state;
        
        if (selectedServices.length === 0) {
            return 'Select services to continue';
        }
        
        const serviceCount = selectedServices.length;
        const totalPrice = currentPricing ? window.ModularPricingUtils.formatPrice(currentPricing.totalPrice) : '$0.00';
        
        return `${serviceCount} service${serviceCount > 1 ? 's' : ''} selected • ${totalPrice}/month`;
    }

    attachEventListeners() {
        // Window resize handler for responsive updates
        window.addEventListener('resize', window.ModularPricingUtils.debounce(() => {
            this.handleResize();
        }, 250));

        // Keyboard navigation
        this.element.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    handleServiceToggle(serviceId, checked) {
        const newSelectedServices = checked 
            ? [...this.state.selectedServices, serviceId]
            : this.state.selectedServices.filter(id => id !== serviceId);

        this.updateSelectedServices(newSelectedServices);
    }

    handleServiceDetailsToggle(serviceId) {
        this.setState({
            showServiceDetails: this.state.showServiceDetails === serviceId ? null : serviceId
        });
    }

    async updateSelectedServices(newSelectedServices) {
        this.setState({
            selectedServices: newSelectedServices,
            animationState: 'updating'
        });

        // Update all components
        this.updateComponents();
        
        // Update pricing
        await this.updatePricing();
        
        // Update action section
        this.updateActionSection();

        this.setState({ animationState: 'complete' });
    }

    updateComponents() {
        // Update service selector
        if (this.components.serviceSelector) {
            this.components.serviceSelector.updateProps({
                selectedServices: this.state.selectedServices
            });
        }

        // Update pricing calculator
        if (this.components.pricingCalculator) {
            this.components.pricingCalculator.updateProps({
                selectedServices: this.state.selectedServices
            });
        }

        // Update plan recommendations
        if (this.components.planRecommendations) {
            this.components.planRecommendations.updateProps({
                selectedServices: this.state.selectedServices
            });
        }
    }

    async updatePricing() {
        this.setState({ isLoading: true });

        try {
            // Pricing is calculated by the PricingCalculator component
            // We just need to wait for the update
            await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
            console.error('Pricing update failed:', error);
        } finally {
            this.setState({ isLoading: false });
        }
    }

    handlePricingUpdate(pricingResult) {
        this.setState({ currentPricing: pricingResult });

        // Update savings display
        if (this.components.savingsDisplay) {
            this.components.savingsDisplay.updateProps({
                savings: pricingResult.savings || 0,
                individualTotal: pricingResult.individualTotal || 0,
                bundlePrice: pricingResult.totalPrice || 0,
                currency: pricingResult.currency || 'USD'
            });
        }

        // Notify parent component
        if (this.props.onPriceUpdate) {
            this.props.onPriceUpdate(pricingResult);
        }

        // Update action section
        this.updateActionSection();
    }

    handlePlanSelect(planId, serviceIds) {
        this.updateSelectedServices(serviceIds);
        
        // Highlight the selected plan
        if (this.components.planRecommendations) {
            this.components.planRecommendations.highlightPlan(planId);
        }
    }

    handleContinue() {
        const { selectedServices, currentPricing } = this.state;
        
        if (selectedServices.length === 0) {
            return;
        }

        if (this.props.onSubscriptionSelect) {
            this.props.onSubscriptionSelect(
                selectedServices,
                currentPricing?.recommendedPlanId || null,
                currentPricing
            );
        }
    }

    updateActionSection() {
        const actionSection = this.element.querySelector('.pricing-actions');
        const continueButton = actionSection.querySelector('.continue-btn');
        const summaryText = actionSection.querySelector('.action-summary');

        // Update button state
        continueButton.disabled = this.state.selectedServices.length === 0 || this.state.isLoading;
        
        // Update summary text
        summaryText.textContent = this.getActionSummaryText();

        // Add visual feedback
        if (this.state.selectedServices.length > 0) {
            continueButton.classList.add('ready');
        } else {
            continueButton.classList.remove('ready');
        }
    }

    handleResize() {
        // Handle responsive layout changes
        const isMobile = window.ModularPricingUtils.isMobile();
        
        if (isMobile) {
            this.element.classList.add('mobile-layout');
        } else {
            this.element.classList.remove('mobile-layout');
        }
    }

    handleKeyDown(event) {
        // Keyboard navigation support
        if (event.key === 'Escape') {
            this.setState({ showServiceDetails: null });
        }
    }

    setState(newState) {
        this.state = { ...this.state, ...newState };
    }

    // Public API methods
    getSelectedServices() {
        return [...this.state.selectedServices];
    }

    getCurrentPricing() {
        return this.state.currentPricing;
    }

    selectServices(serviceIds) {
        this.updateSelectedServices(serviceIds);
    }

    clearSelection() {
        this.updateSelectedServices([]);
    }

    setTheme(theme) {
        this.props.theme = theme;
        this.element.className = `modular-pricing-ui theme-${theme}`;
    }

    getElement() {
        return this.element;
    }

    destroy() {
        // Clean up event listeners
        window.removeEventListener('resize', this.handleResize);

        // Destroy all components
        Object.values(this.components).forEach(component => {
            if (component && typeof component.destroy === 'function') {
                component.destroy();
            }
        });

        this.components = {};

        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        
        this.element = null;
    }
}

// Export component
if (typeof window !== 'undefined') {
    window.ModularPricingUI = ModularPricingUI;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModularPricingUI;
}