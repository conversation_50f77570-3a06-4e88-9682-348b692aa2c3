/**
 * Data Export and Import Manager
 * Handles backup data export in multiple formats, import simulation, 
 * format conversion, verification, and cross-platform compatibility
 */

class DataExportImportManager {
    constructor(supabase, adminSupabase) {
        this.supabase = supabase;
        this.adminSupabase = adminSupabase;
        this.listeners = [];
        
        // Supported export formats
        this.exportFormats = {
            'json': {
                name: 'JSON',
                extension: '.json',
                mimeType: 'application/json',
                description: 'JavaScript Object Notation - Universal format',
                compatibility: ['Web', 'Mobile', 'Desktop', 'API'],
                compression: false,
                encryption: true
            },
            'csv': {
                name: 'CSV',
                extension: '.csv',
                mimeType: 'text/csv',
                description: 'Comma Separated Values - Spreadsheet compatible',
                compatibility: ['Excel', 'Google Sheets', 'Database'],
                compression: false,
                encryption: false
            },
            'xml': {
                name: 'XML',
                extension: '.xml',
                mimeType: 'application/xml',
                description: 'Extensible Markup Language - Enterprise systems',
                compatibility: ['Enterprise', 'Legacy Systems', 'SOAP APIs'],
                compression: false,
                encryption: false
            },
            'zip': {
                name: 'ZIP Archive',
                extension: '.zip',
                mimeType: 'application/zip',
                description: 'Compressed archive with multiple files',
                compatibility: ['All Platforms'],
                compression: true,
                encryption: true
            },
            'backup': {
                name: 'SafeKeep Backup',
                extension: '.skb',
                mimeType: 'application/octet-stream',
                description: 'Native SafeKeep backup format',
                compatibility: ['SafeKeep Apps'],
                compression: true,
                encryption: true
            }
        };
        
        // Import validation rules
        this.validationRules = {
            contacts: {
                required: ['name'],
                optional: ['phone', 'email', 'address'],
                maxSize: 1000000 // 1MB per contact
            },
            messages: {
                required: ['content', 'timestamp'],
                optional: ['sender', 'recipient', 'type'],
                maxSize: 10000000 // 10MB per message thread
            },
            photos: {
                required: ['filename', 'data'],
                optional: ['metadata', 'location', 'timestamp'],
                maxSize: 50000000 // 50MB per photo
            }
        };
        
        this.init();
    }
    
    init() {
        console.log('Data Export/Import Manager initialized');
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Listen for export requests
        document.addEventListener('exportData', (event) => {
            this.handleExportRequest(event.detail);
        });
        
        // Listen for import requests
        document.addEventListener('importData', (event) => {
            this.handleImportRequest(event.detail);
        });
    }
    
    /**
     * Export backup data in specified format
     */
    async exportBackupData(backupSessionId, format = 'json', options = {}) {
        try {
            this.notifyListeners('export_started', { backupSessionId, format });
            
            // Get backup session data
            const backupData = await this.getBackupSessionData(backupSessionId);
            if (!backupData) {
                throw new Error('Backup session not found');
            }
            
            // Convert to requested format
            const exportedData = await this.convertToFormat(backupData, format, options);
            
            // Create download
            const filename = this.generateFilename(backupData, format);
            await this.downloadFile(exportedData, filename, format);
            
            this.notifyListeners('export_completed', { 
                backupSessionId, 
                format, 
                filename,
                size: exportedData.length 
            });
            
            return {
                success: true,
                filename,
                format,
                size: exportedData.length
            };
            
        } catch (error) {
            console.error('Export failed:', error);
            this.notifyListeners('export_failed', { backupSessionId, format, error: error.message });
            throw error;
        }
    }
    
    /**
     * Get backup session data from database
     */
    async getBackupSessionData(backupSessionId) {
        try {
            // Get backup session info
            const { data: session, error: sessionError } = await this.supabase
                .from('enhanced_backup_sessions')
                .select('*')
                .eq('id', backupSessionId)
                .single();
                
            if (sessionError) throw sessionError;
            
            // Get backup items
            const { data: items, error: itemsError } = await this.supabase
                .from('backup_items')
                .select('*')
                .eq('backup_session_id', backupSessionId);
                
            if (itemsError) throw itemsError;
            
            // Organize data by type
            const organizedData = {
                session: session,
                contacts: items.filter(item => item.data_type === 'contacts'),
                messages: items.filter(item => item.data_type === 'messages'),
                photos: items.filter(item => item.data_type === 'photos'),
                metadata: {
                    exportedAt: new Date().toISOString(),
                    version: '1.0',
                    totalItems: items.length,
                    dataTypes: [...new Set(items.map(item => item.data_type))]
                }
            };
            
            return organizedData;
            
        } catch (error) {
            console.error('Failed to get backup data:', error);
            return null;
        }
    }
    
    /**
     * Convert data to specified format
     */
    async convertToFormat(data, format, options = {}) {
        switch (format) {
            case 'json':
                return this.convertToJSON(data, options);
            case 'csv':
                return this.convertToCSV(data, options);
            case 'xml':
                return this.convertToXML(data, options);
            case 'zip':
                return await this.convertToZIP(data, options);
            case 'backup':
                return await this.convertToBackup(data, options);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }
    
    /**
     * Convert to JSON format
     */
    convertToJSON(data, options = {}) {
        const jsonData = {
            ...data,
            exportOptions: options,
            format: 'json'
        };
        
        return JSON.stringify(jsonData, null, options.pretty ? 2 : 0);
    }
    
    /**
     * Convert to CSV format
     */
    convertToCSV(data, options = {}) {
        const csvFiles = {};
        
        // Convert each data type to CSV
        ['contacts', 'messages', 'photos'].forEach(dataType => {
            if (data[dataType] && data[dataType].length > 0) {
                csvFiles[dataType] = this.arrayToCSV(data[dataType], dataType);
            }
        });
        
        // If multiple data types, create a ZIP with CSV files
        if (Object.keys(csvFiles).length > 1) {
            return this.createCSVZip(csvFiles, data.metadata);
        } else {
            // Single data type, return CSV directly
            const dataType = Object.keys(csvFiles)[0];
            return csvFiles[dataType];
        }
    }
    
    /**
     * Convert array to CSV
     */
    arrayToCSV(array, dataType) {
        if (!array || array.length === 0) return '';
        
        // Get headers from first item
        const headers = Object.keys(array[0].data || array[0]);
        
        // Create CSV content
        const csvContent = [
            headers.join(','),
            ...array.map(item => {
                const data = item.data || item;
                return headers.map(header => {
                    const value = data[header];
                    // Escape commas and quotes
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value || '';
                }).join(',');
            })
        ].join('\n');
        
        return csvContent;
    }
    
    /**
     * Convert to XML format
     */
    convertToXML(data, options = {}) {
        let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
        xml += '<SafeKeepBackup>\n';
        xml += `  <metadata>\n`;
        xml += `    <exportedAt>${data.metadata.exportedAt}</exportedAt>\n`;
        xml += `    <version>${data.metadata.version}</version>\n`;
        xml += `    <totalItems>${data.metadata.totalItems}</totalItems>\n`;
        xml += `  </metadata>\n`;
        
        // Convert each data type
        ['contacts', 'messages', 'photos'].forEach(dataType => {
            if (data[dataType] && data[dataType].length > 0) {
                xml += `  <${dataType}>\n`;
                data[dataType].forEach(item => {
                    xml += `    <item>\n`;
                    xml += this.objectToXML(item.data || item, '      ');
                    xml += `    </item>\n`;
                });
                xml += `  </${dataType}>\n`;
            }
        });
        
        xml += '</SafeKeepBackup>';
        return xml;
    }
    
    /**
     * Convert object to XML elements
     */
    objectToXML(obj, indent = '') {
        let xml = '';
        for (const [key, value] of Object.entries(obj)) {
            if (typeof value === 'object' && value !== null) {
                xml += `${indent}<${key}>\n`;
                xml += this.objectToXML(value, indent + '  ');
                xml += `${indent}</${key}>\n`;
            } else {
                const escapedValue = String(value).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                xml += `${indent}<${key}>${escapedValue}</${key}>\n`;
            }
        }
        return xml;
    }
    
    /**
     * Convert to ZIP format
     */
    async convertToZIP(data, options = {}) {
        // This would require a ZIP library like JSZip
        // For demo purposes, we'll simulate the ZIP creation
        const zipContent = {
            'backup.json': JSON.stringify(data, null, 2),
            'contacts.csv': data.contacts ? this.arrayToCSV(data.contacts, 'contacts') : '',
            'messages.csv': data.messages ? this.arrayToCSV(data.messages, 'messages') : '',
            'photos.csv': data.photos ? this.arrayToCSV(data.photos, 'photos') : '',
            'metadata.txt': `SafeKeep Backup Export\nExported: ${data.metadata.exportedAt}\nTotal Items: ${data.metadata.totalItems}`
        };
        
        // Simulate ZIP compression
        const zipData = JSON.stringify(zipContent);
        return `ZIP_ARCHIVE:${btoa(zipData)}`;
    }
    
    /**
     * Convert to native SafeKeep backup format
     */
    async convertToBackup(data, options = {}) {
        const backupFormat = {
            header: {
                format: 'SafeKeep Backup v1.0',
                created: data.metadata.exportedAt,
                encrypted: options.encrypt || false,
                compressed: true
            },
            data: data,
            checksum: this.calculateChecksum(JSON.stringify(data))
        };
        
        let backupData = JSON.stringify(backupFormat);
        
        // Simulate encryption if requested
        if (options.encrypt) {
            backupData = `ENCRYPTED:${btoa(backupData)}`;
        }
        
        return backupData;
    }
    
    /**
     * Calculate simple checksum for data integrity
     */
    calculateChecksum(data) {
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(16);
    }
    
    /**
     * Generate filename for export
     */
    generateFilename(data, format) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const sessionId = data.session.id.substring(0, 8);
        const extension = this.exportFormats[format].extension;
        
        return `safekeep-backup-${sessionId}-${timestamp}${extension}`;
    }
    
    /**
     * Download file to user's device
     */
    async downloadFile(content, filename, format) {
        const formatInfo = this.exportFormats[format];
        const blob = new Blob([content], { type: formatInfo.mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        setTimeout(() => URL.revokeObjectURL(url), 1000);
    }
    
    /**
     * Import data from file
     */
    async importBackupData(file, options = {}) {
        try {
            this.notifyListeners('import_started', { filename: file.name, size: file.size });
            
            // Read file content
            const content = await this.readFile(file);
            
            // Detect format
            const format = this.detectFormat(file, content);
            
            // Parse content
            const parsedData = await this.parseImportData(content, format);
            
            // Validate data
            const validationResult = await this.validateImportData(parsedData);
            
            if (!validationResult.valid) {
                throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
            }
            
            // Convert to standard format
            const standardData = await this.convertToStandardFormat(parsedData, format);
            
            // Simulate import process
            await this.simulateImportProcess(standardData, options);
            
            this.notifyListeners('import_completed', { 
                filename: file.name,
                format,
                itemCount: this.countItems(standardData)
            });
            
            return {
                success: true,
                format,
                itemCount: this.countItems(standardData),
                data: standardData
            };
            
        } catch (error) {
            console.error('Import failed:', error);
            this.notifyListeners('import_failed', { filename: file.name, error: error.message });
            throw error;
        }
    }
    
    /**
     * Read file content
     */
    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }
    
    /**
     * Detect file format
     */
    detectFormat(file, content) {
        const extension = file.name.split('.').pop().toLowerCase();
        
        // Check by extension first
        for (const [format, info] of Object.entries(this.exportFormats)) {
            if (info.extension === `.${extension}`) {
                return format;
            }
        }
        
        // Check by content
        if (content.startsWith('<?xml')) return 'xml';
        if (content.startsWith('ZIP_ARCHIVE:')) return 'zip';
        if (content.includes('SafeKeep Backup v1.0')) return 'backup';
        
        try {
            JSON.parse(content);
            return 'json';
        } catch (e) {
            // Not JSON
        }
        
        // Default to CSV if comma-separated
        if (content.includes(',')) return 'csv';
        
        throw new Error('Unable to detect file format');
    }
    
    /**
     * Parse import data based on format
     */
    async parseImportData(content, format) {
        switch (format) {
            case 'json':
                return JSON.parse(content);
            case 'csv':
                return this.parseCSV(content);
            case 'xml':
                return this.parseXML(content);
            case 'zip':
                return this.parseZIP(content);
            case 'backup':
                return this.parseBackup(content);
            default:
                throw new Error(`Unsupported import format: ${format}`);
        }
    }
    
    /**
     * Parse CSV content
     */
    parseCSV(content) {
        const lines = content.split('\n').filter(line => line.trim());
        if (lines.length < 2) throw new Error('Invalid CSV format');
        
        const headers = lines[0].split(',').map(h => h.trim());
        const data = [];
        
        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',').map(v => v.trim().replace(/^"|"$/g, ''));
            const item = {};
            headers.forEach((header, index) => {
                item[header] = values[index] || '';
            });
            data.push(item);
        }
        
        return { contacts: data }; // Assume CSV is contacts for demo
    }
    
    /**
     * Parse XML content (simplified)
     */
    parseXML(content) {
        // This would require a proper XML parser
        // For demo purposes, we'll simulate parsing
        return {
            contacts: [],
            messages: [],
            photos: [],
            metadata: {
                importedAt: new Date().toISOString(),
                format: 'xml'
            }
        };
    }
    
    /**
     * Parse ZIP content
     */
    parseZIP(content) {
        if (!content.startsWith('ZIP_ARCHIVE:')) {
            throw new Error('Invalid ZIP format');
        }
        
        const zipData = content.substring(12); // Remove 'ZIP_ARCHIVE:' prefix
        const decodedData = atob(zipData);
        const zipContent = JSON.parse(decodedData);
        
        // Parse individual files from ZIP
        const result = {};
        if (zipContent['backup.json']) {
            const backupData = JSON.parse(zipContent['backup.json']);
            Object.assign(result, backupData);
        }
        
        return result;
    }
    
    /**
     * Parse SafeKeep backup format
     */
    parseBackup(content) {
        let backupData = content;
        
        // Handle encryption
        if (content.startsWith('ENCRYPTED:')) {
            backupData = atob(content.substring(10));
        }
        
        const parsed = JSON.parse(backupData);
        
        // Verify checksum
        const dataString = JSON.stringify(parsed.data);
        const calculatedChecksum = this.calculateChecksum(dataString);
        
        if (parsed.checksum !== calculatedChecksum) {
            console.warn('Checksum mismatch - data may be corrupted');
        }
        
        return parsed.data;
    }
    
    /**
     * Validate imported data
     */
    async validateImportData(data) {
        const errors = [];
        const warnings = [];
        
        // Check required structure
        if (!data || typeof data !== 'object') {
            errors.push('Invalid data structure');
            return { valid: false, errors, warnings };
        }
        
        // Validate each data type
        for (const [dataType, items] of Object.entries(data)) {
            if (!Array.isArray(items) && dataType !== 'metadata' && dataType !== 'session') {
                continue;
            }
            
            if (Array.isArray(items) && this.validationRules[dataType]) {
                const rules = this.validationRules[dataType];
                
                items.forEach((item, index) => {
                    const itemData = item.data || item;
                    
                    // Check required fields
                    rules.required.forEach(field => {
                        if (!itemData[field]) {
                            errors.push(`${dataType}[${index}]: Missing required field '${field}'`);
                        }
                    });
                    
                    // Check size limits
                    const itemSize = JSON.stringify(itemData).length;
                    if (itemSize > rules.maxSize) {
                        warnings.push(`${dataType}[${index}]: Item size (${itemSize}) exceeds recommended limit (${rules.maxSize})`);
                    }
                });
            }
        }
        
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    /**
     * Convert imported data to standard format
     */
    async convertToStandardFormat(data, sourceFormat) {
        // Ensure consistent structure
        const standardData = {
            contacts: data.contacts || [],
            messages: data.messages || [],
            photos: data.photos || [],
            metadata: {
                ...data.metadata,
                importedAt: new Date().toISOString(),
                sourceFormat,
                convertedAt: new Date().toISOString()
            }
        };
        
        // Normalize data structure
        ['contacts', 'messages', 'photos'].forEach(dataType => {
            standardData[dataType] = standardData[dataType].map(item => {
                if (item.data) {
                    return item; // Already in correct format
                } else {
                    return {
                        id: this.generateId(),
                        data_type: dataType,
                        data: item,
                        created_at: new Date().toISOString()
                    };
                }
            });
        });
        
        return standardData;
    }
    
    /**
     * Simulate import process with progress updates
     */
    async simulateImportProcess(data, options = {}) {
        const totalItems = this.countItems(data);
        let processedItems = 0;
        
        this.notifyListeners('import_progress', { 
            progress: 0, 
            stage: 'preparing',
            message: 'Preparing import...' 
        });
        
        await this.delay(500);
        
        // Process each data type
        for (const [dataType, items] of Object.entries(data)) {
            if (!Array.isArray(items)) continue;
            
            this.notifyListeners('import_progress', { 
                progress: (processedItems / totalItems) * 100,
                stage: 'processing',
                message: `Processing ${dataType}...`,
                currentType: dataType
            });
            
            for (const item of items) {
                // Simulate processing time
                await this.delay(50);
                
                processedItems++;
                
                this.notifyListeners('import_progress', { 
                    progress: (processedItems / totalItems) * 100,
                    stage: 'processing',
                    message: `Processing ${dataType}... (${processedItems}/${totalItems})`,
                    currentType: dataType,
                    currentItem: item.data?.name || item.data?.content?.substring(0, 50) || 'Unknown'
                });
            }
        }
        
        // Verification stage
        this.notifyListeners('import_progress', { 
            progress: 95,
            stage: 'verifying',
            message: 'Verifying data integrity...' 
        });
        
        await this.delay(1000);
        
        // Complete
        this.notifyListeners('import_progress', { 
            progress: 100,
            stage: 'completed',
            message: 'Import completed successfully!' 
        });
    }
    
    /**
     * Count total items in data
     */
    countItems(data) {
        let count = 0;
        ['contacts', 'messages', 'photos'].forEach(dataType => {
            if (data[dataType] && Array.isArray(data[dataType])) {
                count += data[dataType].length;
            }
        });
        return count;
    }
    
    /**
     * Generate unique ID
     */
    generateId() {
        return 'import_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Get supported export formats
     */
    getSupportedFormats() {
        return this.exportFormats;
    }
    
    /**
     * Get format compatibility info
     */
    getFormatCompatibility(format) {
        return this.exportFormats[format]?.compatibility || [];
    }
    
    /**
     * Verify data integrity
     */
    async verifyDataIntegrity(data, originalChecksum = null) {
        const currentChecksum = this.calculateChecksum(JSON.stringify(data));
        
        const result = {
            valid: true,
            checksum: currentChecksum,
            originalChecksum,
            issues: []
        };
        
        if (originalChecksum && originalChecksum !== currentChecksum) {
            result.valid = false;
            result.issues.push('Checksum mismatch - data may be corrupted');
        }
        
        // Additional integrity checks
        ['contacts', 'messages', 'photos'].forEach(dataType => {
            if (data[dataType]) {
                const duplicates = this.findDuplicates(data[dataType]);
                if (duplicates.length > 0) {
                    result.issues.push(`Found ${duplicates.length} duplicate ${dataType}`);
                }
            }
        });
        
        return result;
    }
    
    /**
     * Find duplicate items
     */
    findDuplicates(items) {
        const seen = new Set();
        const duplicates = [];
        
        items.forEach((item, index) => {
            const key = JSON.stringify(item.data || item);
            if (seen.has(key)) {
                duplicates.push(index);
            } else {
                seen.add(key);
            }
        });
        
        return duplicates;
    }
    
    /**
     * Test cross-platform compatibility
     */
    async testCrossPlatformCompatibility(data, targetPlatforms = ['Web', 'Mobile', 'Desktop']) {
        const results = {};
        
        for (const platform of targetPlatforms) {
            results[platform] = {
                compatible: true,
                issues: [],
                recommendations: []
            };
            
            // Platform-specific checks
            switch (platform) {
                case 'Mobile':
                    // Check for mobile-specific limitations
                    if (this.countItems(data) > 10000) {
                        results[platform].issues.push('Large dataset may cause performance issues on mobile');
                        results[platform].recommendations.push('Consider splitting into smaller batches');
                    }
                    break;
                    
                case 'Desktop':
                    // Desktop usually has fewer limitations
                    results[platform].recommendations.push('Full feature compatibility expected');
                    break;
                    
                case 'Web':
                    // Check for web-specific limitations
                    const dataSize = JSON.stringify(data).length;
                    if (dataSize > 50000000) { // 50MB
                        results[platform].issues.push('Large dataset may exceed browser memory limits');
                        results[platform].recommendations.push('Use streaming or chunked processing');
                    }
                    break;
            }
        }
        
        return results;
    }
    
    /**
     * Add event listener
     */
    addEventListener(callback) {
        this.listeners.push(callback);
    }
    
    /**
     * Remove event listener
     */
    removeEventListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }
    
    /**
     * Notify all listeners
     */
    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback({ event, data, timestamp: new Date().toISOString() });
            } catch (error) {
                console.error('Error in event listener:', error);
            }
        });
    }
    
    /**
     * Handle export request from UI
     */
    async handleExportRequest(request) {
        const { backupSessionId, format, options } = request;
        
        try {
            const result = await this.exportBackupData(backupSessionId, format, options);
            
            // Dispatch success event
            document.dispatchEvent(new CustomEvent('exportCompleted', {
                detail: result
            }));
            
        } catch (error) {
            // Dispatch error event
            document.dispatchEvent(new CustomEvent('exportFailed', {
                detail: { error: error.message }
            }));
        }
    }
    
    /**
     * Handle import request from UI
     */
    async handleImportRequest(request) {
        const { file, options } = request;
        
        try {
            const result = await this.importBackupData(file, options);
            
            // Dispatch success event
            document.dispatchEvent(new CustomEvent('importCompleted', {
                detail: result
            }));
            
        } catch (error) {
            // Dispatch error event
            document.dispatchEvent(new CustomEvent('importFailed', {
                detail: { error: error.message }
            }));
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataExportImportManager;
} else if (typeof window !== 'undefined') {
    window.DataExportImportManager = DataExportImportManager;
}

// Proper module export
module.exports = DataExportImportManager;