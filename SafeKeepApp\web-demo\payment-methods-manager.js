/**
 * Payment Methods Manager for SafeKeep Web Demo
 * Handles payment method storage, management, and selection
 */

class PaymentMethodsManager {
    constructor(stripeManager, paymentProcessor) {
        this.stripeManager = stripeManager;
        this.paymentProcessor = paymentProcessor;
        this.currentCustomerId = null;
        this.paymentMethods = new Map();
        this.defaultPaymentMethod = null;
        
        this.setupEventListeners();
    }

    /**
     * Initialize payment methods manager
     */
    async initialize(customerId) {
        try {
            this.currentCustomerId = customerId;
            await this.loadPaymentMethods();
            this.setupUI();
            
            console.log('✅ Payment Methods Manager initialized');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Payment Methods Manager:', error);
            throw error;
        }
    }

    /**
     * Load payment methods for customer
     */
    async loadPaymentMethods() {
        try {
            if (!this.currentCustomerId) return;

            // In demo mode, load from localStorage
            const saved = localStorage.getItem(`payment_methods_${this.currentCustomerId}`);
            if (saved) {
                const data = JSON.parse(saved);
                this.paymentMethods = new Map(Object.entries(data.methods || {}));
                this.defaultPaymentMethod = data.default || null;
            }

            // In production, this would fetch from Stripe
            // const methods = await this.stripeManager.getCustomerPaymentMethods(this.currentCustomerId);
            
        } catch (error) {
            console.error('❌ Failed to load payment methods:', error);
            throw error;
        }
    }

    /**
     * Save payment methods to storage
     */
    savePaymentMethods() {
        if (!this.currentCustomerId) return;

        const data = {
            methods: Object.fromEntries(this.paymentMethods),
            default: this.defaultPaymentMethod,
            updated: new Date().toISOString()
        };

        localStorage.setItem(`payment_methods_${this.currentCustomerId}`, JSON.stringify(data));
    }

    /**
     * Setup payment methods UI
     */
    setupUI() {
        this.createPaymentMethodsInterface();
        this.updatePaymentMethodsDisplay();
    }

    /**
     * Create payment methods interface
     */
    createPaymentMethodsInterface() {
        let container = document.getElementById('payment-methods-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'payment-methods-container';
            container.className = 'payment-methods-section';
            
            // Insert into subscription dashboard or create standalone
            const subscriptionDashboard = document.querySelector('.subscription-dashboard');
            if (subscriptionDashboard) {
                subscriptionDashboard.appendChild(container);
            } else {
                document.querySelector('.main-content').appendChild(container);
            }
        }

        container.innerHTML = `
            <div class="payment-methods-manager">
                <div class="payment-methods-header">
                    <h3>💳 Payment Methods</h3>
                    <button class="btn primary" id="add-payment-method-btn">
                        <span class="btn-icon">+</span>
                        Add Payment Method
                    </button>
                </div>
                
                <div class="payment-methods-content">
                    <div class="payment-methods-list" id="payment-methods-list">
                        <!-- Payment methods will be populated here -->
                    </div>
                    
                    <div class="payment-methods-empty" id="payment-methods-empty" style="display: none;">
                        <div class="empty-state">
                            <div class="empty-icon">💳</div>
                            <h4>No Payment Methods</h4>
                            <p>Add a payment method to manage your subscriptions and make payments.</p>
                            <button class="btn primary" onclick="paymentMethodsManager.showAddPaymentMethodModal()">
                                Add Your First Payment Method
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Payment Method Modal -->
            <div class="payment-method-modal" id="payment-method-modal" style="display: none;">
                <div class="modal-overlay" onclick="paymentMethodsManager.hideAddPaymentMethodModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h4>Add Payment Method</h4>
                        <button class="close-btn" onclick="paymentMethodsManager.hideAddPaymentMethodModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="payment-method-form">
                            <div class="form-section">
                                <h5>Payment Information</h5>
                                <div id="payment-method-element">
                                    <!-- Stripe Elements will be mounted here -->
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <h5>Billing Address</h5>
                                <div id="billing-address-form">
                                    <!-- Billing address form will be created here -->
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="set-as-default" checked>
                                    <span class="checkmark"></span>
                                    Set as default payment method
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn secondary" onclick="paymentMethodsManager.hideAddPaymentMethodModal()">
                            Cancel
                        </button>
                        <button class="btn primary" id="save-payment-method-btn">
                            <span id="save-payment-method-text">Save Payment Method</span>
                            <span id="save-payment-method-loading" style="display: none;">Saving...</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Edit Payment Method Modal -->
            <div class="edit-payment-method-modal" id="edit-payment-method-modal" style="display: none;">
                <div class="modal-overlay" onclick="paymentMethodsManager.hideEditPaymentMethodModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h4>Edit Payment Method</h4>
                        <button class="close-btn" onclick="paymentMethodsManager.hideEditPaymentMethodModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="edit-payment-method-form" id="edit-payment-method-form">
                            <!-- Edit form will be populated dynamically -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn secondary" onclick="paymentMethodsManager.hideEditPaymentMethodModal()">
                            Cancel
                        </button>
                        <button class="btn primary" id="update-payment-method-btn">
                            Update Payment Method
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.setupEventListeners();
    }

    /**
     * Update payment methods display
     */
    updatePaymentMethodsDisplay() {
        const listContainer = document.getElementById('payment-methods-list');
        const emptyContainer = document.getElementById('payment-methods-empty');
        
        if (!listContainer || !emptyContainer) return;

        if (this.paymentMethods.size === 0) {
            listContainer.style.display = 'none';
            emptyContainer.style.display = 'block';
            return;
        }

        listContainer.style.display = 'block';
        emptyContainer.style.display = 'none';
        
        listContainer.innerHTML = '';

        for (const [methodId, method] of this.paymentMethods) {
            const methodCard = this.createPaymentMethodCard(methodId, method);
            listContainer.appendChild(methodCard);
        }
    }

    /**
     * Create payment method card
     */
    createPaymentMethodCard(methodId, method) {
        const card = document.createElement('div');
        card.className = `payment-method-card ${method.id === this.defaultPaymentMethod ? 'default' : ''}`;
        card.dataset.methodId = methodId;

        const cardBrandIcon = this.getCardBrandIcon(method.card?.brand);
        const expiryDate = method.card ? `${method.card.exp_month}/${method.card.exp_year}` : '';

        card.innerHTML = `
            <div class="payment-method-info">
                <div class="payment-method-icon">
                    ${cardBrandIcon}
                </div>
                <div class="payment-method-details">
                    <div class="payment-method-primary">
                        <span class="card-brand">${this.formatCardBrand(method.card?.brand)}</span>
                        <span class="card-last4">•••• ${method.card?.last4}</span>
                        ${method.id === this.defaultPaymentMethod ? '<span class="default-badge">Default</span>' : ''}
                    </div>
                    <div class="payment-method-secondary">
                        <span class="expiry">Expires ${expiryDate}</span>
                        <span class="added-date">Added ${this.formatDate(method.created)}</span>
                    </div>
                </div>
            </div>
            <div class="payment-method-actions">
                <button class="btn-icon" onclick="paymentMethodsManager.editPaymentMethod('${methodId}')" title="Edit">
                    ✏️
                </button>
                ${method.id !== this.defaultPaymentMethod ? `
                    <button class="btn-icon" onclick="paymentMethodsManager.setDefaultPaymentMethod('${methodId}')" title="Set as Default">
                        ⭐
                    </button>
                ` : ''}
                <button class="btn-icon danger" onclick="paymentMethodsManager.removePaymentMethod('${methodId}')" title="Remove">
                    🗑️
                </button>
            </div>
        `;

        return card;
    }

    /**
     * Get card brand icon
     */
    getCardBrandIcon(brand) {
        const icons = {
            'visa': '💳',
            'mastercard': '💳',
            'amex': '💳',
            'discover': '💳',
            'diners': '💳',
            'jcb': '💳',
            'unionpay': '💳'
        };
        return icons[brand] || '💳';
    }

    /**
     * Format card brand name
     */
    formatCardBrand(brand) {
        if (!brand) return 'Card';
        return brand.charAt(0).toUpperCase() + brand.slice(1);
    }

    /**
     * Format date for display
     */
    formatDate(timestamp) {
        if (!timestamp) return '';
        const date = new Date(timestamp * 1000);
        return date.toLocaleDateString();
    }

    /**
     * Show add payment method modal
     */
    async showAddPaymentMethodModal() {
        const modal = document.getElementById('payment-method-modal');
        if (!modal) return;

        // Initialize payment processor elements
        await this.paymentProcessor.initialize();
        
        // Mount payment elements
        this.paymentProcessor.mountPaymentElements('payment-method-element', true);
        
        // Create billing address form
        this.paymentProcessor.createBillingAddressForm('billing-address-form');
        
        modal.style.display = 'flex';
    }

    /**
     * Hide add payment method modal
     */
    hideAddPaymentMethodModal() {
        const modal = document.getElementById('payment-method-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * Save payment method
     */
    async savePaymentMethod() {
        const saveBtn = document.getElementById('save-payment-method-btn');
        const saveText = document.getElementById('save-payment-method-text');
        const saveLoading = document.getElementById('save-payment-method-loading');
        const setAsDefault = document.getElementById('set-as-default')?.checked;

        try {
            // Show loading state
            saveBtn.disabled = true;
            saveText.style.display = 'none';
            saveLoading.style.display = 'inline';

            // Validate billing address
            if (!this.paymentProcessor.validateBillingAddress()) {
                throw new Error('Please complete the billing address');
            }

            // Create setup intent for saving payment method
            const setupIntent = await this.createSetupIntent();
            
            // Confirm setup intent
            const result = await this.confirmSetupIntent(setupIntent);
            
            if (result.error) {
                throw new Error(result.error.message);
            }

            // Save payment method
            const paymentMethod = result.setupIntent.payment_method;
            await this.addPaymentMethod(paymentMethod, setAsDefault);

            this.hideAddPaymentMethodModal();
            this.showSuccessMessage('Payment method added successfully!');

        } catch (error) {
            console.error('❌ Failed to save payment method:', error);
            this.showErrorMessage(`Failed to save payment method: ${error.message}`);
        } finally {
            // Reset button state
            saveBtn.disabled = false;
            saveText.style.display = 'inline';
            saveLoading.style.display = 'none';
        }
    }

    /**
     * Create setup intent for saving payment method
     */
    async createSetupIntent() {
        // In demo mode, simulate setup intent
        return {
            id: `seti_demo_${Date.now()}`,
            client_secret: `seti_demo_${Date.now()}_secret_${Math.random().toString(36).substr(2, 16)}`,
            status: 'requires_payment_method'
        };
    }

    /**
     * Confirm setup intent
     */
    async confirmSetupIntent(setupIntent) {
        try {
            // In demo mode, simulate confirmation
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            return {
                setupIntent: {
                    id: setupIntent.id,
                    status: 'succeeded',
                    payment_method: {
                        id: `pm_demo_${Date.now()}`,
                        type: 'card',
                        card: {
                            brand: 'visa',
                            last4: '4242',
                            exp_month: 12,
                            exp_year: 2025
                        },
                        billing_details: this.paymentProcessor.billingAddress
                    }
                }
            };
        } catch (error) {
            return { error: { message: error.message } };
        }
    }

    /**
     * Add payment method to customer
     */
    async addPaymentMethod(paymentMethod, setAsDefault = false) {
        const methodId = paymentMethod.id;
        
        // Store payment method
        this.paymentMethods.set(methodId, {
            id: methodId,
            type: paymentMethod.type,
            card: paymentMethod.card,
            billing_details: paymentMethod.billing_details,
            created: Math.floor(Date.now() / 1000)
        });

        // Set as default if requested or if it's the first method
        if (setAsDefault || this.paymentMethods.size === 1) {
            this.defaultPaymentMethod = methodId;
        }

        this.savePaymentMethods();
        this.updatePaymentMethodsDisplay();

        return paymentMethod;
    }

    /**
     * Edit payment method
     */
    editPaymentMethod(methodId) {
        const method = this.paymentMethods.get(methodId);
        if (!method) return;

        const modal = document.getElementById('edit-payment-method-modal');
        const form = document.getElementById('edit-payment-method-form');
        
        if (!modal || !form) return;

        form.innerHTML = `
            <div class="current-payment-method">
                <h5>Current Payment Method</h5>
                <div class="payment-method-summary">
                    <span class="card-info">
                        ${this.formatCardBrand(method.card?.brand)} •••• ${method.card?.last4}
                    </span>
                    <span class="expiry">Expires ${method.card?.exp_month}/${method.card?.exp_year}</span>
                </div>
            </div>
            
            <div class="edit-options">
                <h5>Update Options</h5>
                <label class="checkbox-label">
                    <input type="checkbox" id="edit-set-as-default" ${method.id === this.defaultPaymentMethod ? 'checked' : ''}>
                    <span class="checkmark"></span>
                    Set as default payment method
                </label>
            </div>
            
            <div class="billing-address-update">
                <h5>Billing Address</h5>
                <div class="current-address">
                    ${this.formatBillingAddress(method.billing_details)}
                </div>
                <button type="button" class="btn secondary" onclick="paymentMethodsManager.updateBillingAddress('${methodId}')">
                    Update Billing Address
                </button>
            </div>
        `;

        modal.style.display = 'flex';
        modal.dataset.editingMethodId = methodId;
    }

    /**
     * Format billing address for display
     */
    formatBillingAddress(billingDetails) {
        if (!billingDetails) return 'No billing address on file';

        const { name, address } = billingDetails;
        if (!address) return name || 'No billing address on file';

        return `
            <div class="address-line">${name || ''}</div>
            <div class="address-line">${address.line1 || ''}</div>
            ${address.line2 ? `<div class="address-line">${address.line2}</div>` : ''}
            <div class="address-line">${address.city || ''}, ${address.state || ''} ${address.postal_code || ''}</div>
            <div class="address-line">${address.country || ''}</div>
        `;
    }

    /**
     * Update payment method
     */
    async updatePaymentMethod() {
        const modal = document.getElementById('edit-payment-method-modal');
        const methodId = modal?.dataset.editingMethodId;
        
        if (!methodId) return;

        const method = this.paymentMethods.get(methodId);
        if (!method) return;

        const setAsDefault = document.getElementById('edit-set-as-default')?.checked;

        try {
            // Update default status
            if (setAsDefault && this.defaultPaymentMethod !== methodId) {
                this.defaultPaymentMethod = methodId;
            } else if (!setAsDefault && this.defaultPaymentMethod === methodId) {
                // Find another method to set as default
                const otherMethods = Array.from(this.paymentMethods.keys()).filter(id => id !== methodId);
                this.defaultPaymentMethod = otherMethods[0] || null;
            }

            this.savePaymentMethods();
            this.updatePaymentMethodsDisplay();
            this.hideEditPaymentMethodModal();
            this.showSuccessMessage('Payment method updated successfully!');

        } catch (error) {
            console.error('❌ Failed to update payment method:', error);
            this.showErrorMessage(`Failed to update payment method: ${error.message}`);
        }
    }

    /**
     * Set default payment method
     */
    async setDefaultPaymentMethod(methodId) {
        try {
            if (!this.paymentMethods.has(methodId)) {
                throw new Error('Payment method not found');
            }

            this.defaultPaymentMethod = methodId;
            this.savePaymentMethods();
            this.updatePaymentMethodsDisplay();
            this.showSuccessMessage('Default payment method updated!');

        } catch (error) {
            console.error('❌ Failed to set default payment method:', error);
            this.showErrorMessage(`Failed to update default payment method: ${error.message}`);
        }
    }

    /**
     * Remove payment method
     */
    async removePaymentMethod(methodId) {
        try {
            const method = this.paymentMethods.get(methodId);
            if (!method) return;

            const cardInfo = `${this.formatCardBrand(method.card?.brand)} •••• ${method.card?.last4}`;
            
            if (!confirm(`Are you sure you want to remove ${cardInfo}?`)) {
                return;
            }

            this.paymentMethods.delete(methodId);

            // Update default if this was the default method
            if (this.defaultPaymentMethod === methodId) {
                const remainingMethods = Array.from(this.paymentMethods.keys());
                this.defaultPaymentMethod = remainingMethods[0] || null;
            }

            this.savePaymentMethods();
            this.updatePaymentMethodsDisplay();
            this.showSuccessMessage('Payment method removed successfully!');

        } catch (error) {
            console.error('❌ Failed to remove payment method:', error);
            this.showErrorMessage(`Failed to remove payment method: ${error.message}`);
        }
    }

    /**
     * Hide edit payment method modal
     */
    hideEditPaymentMethodModal() {
        const modal = document.getElementById('edit-payment-method-modal');
        if (modal) {
            modal.style.display = 'none';
            delete modal.dataset.editingMethodId;
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Add payment method button
        document.addEventListener('click', (event) => {
            if (event.target.id === 'add-payment-method-btn') {
                this.showAddPaymentMethodModal();
            }
            
            if (event.target.id === 'save-payment-method-btn') {
                this.savePaymentMethod();
            }
            
            if (event.target.id === 'update-payment-method-btn') {
                this.updatePaymentMethod();
            }
        });
    }

    /**
     * Show success message
     */
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    /**
     * Show error message
     */
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    /**
     * Show message
     */
    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `payment-message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }

    /**
     * Get default payment method
     */
    getDefaultPaymentMethod() {
        if (!this.defaultPaymentMethod) return null;
        return this.paymentMethods.get(this.defaultPaymentMethod);
    }

    /**
     * Get all payment methods
     */
    getAllPaymentMethods() {
        return Array.from(this.paymentMethods.values());
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        // Clean up any resources
        console.log('🧹 Payment Methods Manager cleaned up');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.PaymentMethodsManager = PaymentMethodsManager;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PaymentMethodsManager;
}