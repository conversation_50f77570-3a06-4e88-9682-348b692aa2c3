# Implementation Plan

- [x] 1. Set up modular pricing backend API structure

  - Create directory structure for controllers, services, and middleware
  - Set up TypeScript interfaces and types for modular pricing
  - Configure Express.js routing for new pricing endpoints
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1_

- [x] 2. Implement Pricing Engine service



  - [x] 2.1 Create PricingEngine class with optimal pricing calculation


    - Implement calculateOptimalPrice method using database functions
    - Add getAvailableServiceCombinations method
    - Write unit tests for pricing calculations
    - _Requirements: 2.1, 2.2, 2.3, 2.4_

  - [x] 2.2 Add pricing recommendation functionality


    - Implement getPlanRecommendations method
    - Add logic to suggest upgrades/downgrades based on usage
    - Write unit tests for recommendation logic
    - _Requirements: 2.1, 2.2_

- [x] 3. Implement Service Validator






  - [x] 3.1 Create ServiceValidator class with combination validation


    - Implement validateServiceCombination method
    - Add service existence and duplication checks
    - Write unit tests for validation logic
    - _Requirements: 4.1, 4.2, 4.3, 4.4_

  - [x] 3.2 Add service access control functionality


    - Implement checkServiceAccess method using database functions
    - Add getUserServices method for retrieving user's active services
    - Write unit tests for access control logic
    - _Requirements: 5.1, 5.2, 5.3, 5.4_
- [x] 4. Implement Subscription Manager





- [ ] 4. Implement Subscription Manager

  - [x] 4.1 Create SubscriptionManager class for subscription lifecycle


    - Implement createSubscription method with service combination support
    - Add updateSubscription method for service changes
    - Write unit tests for subscription management
    - _Requirements: 1.1, 1.2, 1.3, 3.1, 3.2, 3.3_

  - [x] 4.2 Add subscription details and status management


    - Implement getSubscriptionDetails method
    - Add subscription cancellation functionality
    - Write unit tests for subscription status management
    - _Requirements: 3.1, 3.4, 5.4_

- [x] 5. Implement Billing Integration service






  - [x] 5.1 Create BillingIntegration class for Stripe operations


    - Implement createPaymentIntent method with modular pricing metadata
    - Add processSubscriptionPayment method for service combinations
    - Write unit tests for billing integration
    - _Requirements: 1.1, 1.2, 3.1, 3.2_

  - [x] 5.2 Add Stripe webhook handling for modular subscriptions


    - Implement handleWebhook method for subscription events
    - Add webhook handlers for service-specific subscription changes
    - Write unit tests for webhook processing
    - _Requirements: 3.3, 3.4_
-

- [x] 6. Create API controllers and routes





  - [x] 6.1 Implement Pricing Controller with endpoints


    - Create GET /api/pricing/combinations endpoint
    - Create POST /api/pricing/calculate endpoint
    - Create GET /api/pricing/recommendations/:userId endpoint
    - Add input validation and error handling
    - _Requirements: 2.1, 2.2, 2.3, 2.4_

  - [x] 6.2 Implement Subscription Controller with endpoints


    - Create POST /api/subscriptions endpoint for service combinations
    - Create PUT /api/subscriptions/:subscriptionId endpoint
    - Create GET /api/subscriptions/:userId endpoint
    - Create DELETE /api/subscriptions/:subscriptionId endpoint
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 3.1, 3.2, 3.3, 3.4_

  - [x] 6.3 Implement Service Controller with endpoints


    - Create GET /api/services/user/:userId endpoint
    - Create POST /api/services/validate endpoint
    - Create GET /api/services/access/:userId/:serviceType endpoint
    - Add authentication and authorization middleware
    - _Requirements: 4.1, 4.2, 4.3, 4.4, 5.1, 5.2, 5.3, 5.4_

  - [x] 6.4 Implement Billing Controller with endpoints



    - Create POST /api/billing/payment-intent endpoint
    - Create POST /api/billing/webhook endpoint
    - Create GET /api/billing/status/:userId endpoint
    - Add Stripe webhook signature verification
    - _Requirements: 1.1, 1.2, 3.1, 3.2, 3.3, 3.4_

- [x] 7. Add middleware and error handling




  - [x] 7.1 Create authentication and authorization middleware


    - Implement JWT token validation middleware
    - Add user ID extraction from tokens
    - Add role-based access control for admin endpoints
    - Write unit tests for middleware
    - _Requirements: 5.1, 5.2, 5.3, 5.4_

  - [x] 7.2 Implement comprehensive error handling


    - Create error response formatting middleware
    - Add validation error handling for service combinations
    - Add Stripe API error handling and retry logic
    - Write unit tests for error scenarios
    - _Requirements: 1.4, 4.4_

- [x] 8. Update existing backend API integration






  - [x] 8.1 Update backend-api-example.js with modular pricing support


    - Replace old subscription plans with modular pricing structure
    - Update create-payment-intent endpoint to handle service combinations
    - Add new endpoints for pricing calculations and service validation
    - Update webhook handlers for modular subscription events
    - _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 3.1, 3.2_

  - [x] 8.2 Update Stripe service integration


    - Modify StripeService.ts to support service combination metadata
    - Update payment intent creation with service-specific information
    - Add methods for subscription management with service selections
    - Write integration tests for updated Stripe service
    - _Requirements: 1.1, 1.2, 3.1, 3.2, 3.3_

- [-] 9. Write integration tests





  - [x] 9.1 Create database integration tests


    - Test pricing calculation functions with various service combinations
    - Test subscription creation and updates with database
    - Test service access validation with database
    - Verify data consistency across subscription changes
    - _Requirements: 1.1, 1.2, 2.1, 2.2, 4.1, 5.1_

  - [x] 9.2 Create Stripe integration tests





    - Test payment intent creation with service combination metadata
    - Test webhook processing for modular subscription events
    - Test subscription management through Stripe API
    - Verify billing accuracy for different service combinations
    - _Requirements: 1.1, 1.2, 3.1, 3.2, 3.3, 3.4_

- [ ] 10. Create end-to-end API tests
  - [x] 10.1 Test complete subscription flow


    - Test user selects services and gets pricing calculation
    - Test subscription creation with payment processing
    - Test service access validation after subscription activation
    - Test subscription updates and service changes
    - _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 3.1, 3.2, 5.1, 5.2_


  - [ ] 10.2 Test error scenarios and edge cases
    - Test invalid service combinations and validation errors
    - Test payment failures and subscription rollback
    - Test service access denial for inactive subscriptions
    - Test webhook failure handling and retry logic
    - _Requirements: 1.4, 4.4, 5.3, 5.4_