# Backup Tracking Schema Migration

This migration implements the enhanced backup tracking database schema as specified in the secure media backup feature design document.

## Overview

The migration creates three main tables for comprehensive backup tracking:

1. **backup_sessions** - Tracks individual backup sessions with session management fields
2. **backup_items** - Tracks individual items being backed up within each session
3. **backup_progress** - Provides real-time progress updates for each data type

## Tables Created

### backup_sessions
- Manages backup session lifecycle and overall progress
- Includes configuration settings and error tracking
- Supports different backup statuses (pending, in_progress, completed, failed, cancelled)

### backup_items
- Tracks individual items (contacts, messages, photos) within backup sessions
- Stores encrypted data and metadata for each item
- Includes checksums and file paths for integrity verification

### backup_progress
- Provides real-time progress tracking for each data type within a session
- Tracks total, completed, and failed counts per item type
- Optimized for frequent updates during backup operations

## Security Features

### Row Level Security (RLS)
- All tables have RLS enabled with user isolation policies
- Users can only access their own backup data
- Policies enforce user_id matching through session relationships

### Data Protection
- Encrypted data stored as BYTEA in backup_items table
- Metadata stored as JSONB for flexible schema evolution
- Checksums for data integrity verification

## Performance Optimizations

### Indexes Created
- User-based queries: `idx_backup_sessions_user_id`, `idx_backup_sessions_user_status`
- Status filtering: `idx_backup_sessions_status`, `idx_backup_items_status`
- Time-based queries: `idx_backup_sessions_start_time`
- Item lookups: `idx_backup_items_original_id`, `idx_backup_items_checksum`
- Progress tracking: `idx_backup_progress_last_updated`

### Helper Functions
- `initialize_backup_progress()` - Sets up progress tracking for new sessions
- `update_backup_progress()` - Efficiently updates progress counters
- `get_backup_session_summary()` - Retrieves comprehensive session information
- `cleanup_old_backup_sessions()` - Maintains database size by removing old sessions

## How to Apply Migration

### Option 1: Supabase SQL Editor
1. Open your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `backup_tracking_schema.sql`
4. Execute the migration

### Option 2: Command Line (if using Supabase CLI)
```bash
supabase db reset
# Then apply the full schema including this migration
```

### Option 3: Programmatic Application
```typescript
import { supabase } from '../lib/supabase';
import { readFileSync } from 'fs';

const migrationSQL = readFileSync('./migrations/backup_tracking_schema.sql', 'utf8');
await supabase.rpc('exec', { sql: migrationSQL });
```

## Verification

After applying the migration, verify the tables were created correctly:

```sql
-- Check tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('backup_sessions', 'backup_items', 'backup_progress');

-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('backup_sessions', 'backup_items', 'backup_progress');

-- Check indexes were created
SELECT indexname, tablename FROM pg_indexes 
WHERE tablename IN ('backup_sessions', 'backup_items', 'backup_progress');

-- Test helper functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'initialize_backup_progress',
    'update_backup_progress', 
    'get_backup_session_summary',
    'cleanup_old_backup_sessions'
);
```

## Usage Examples

### Creating a New Backup Session
```sql
INSERT INTO backup_sessions (user_id, status, total_items, configuration)
VALUES (
    auth.uid(),
    'pending',
    1500,
    '{"autoBackup": false, "wifiOnly": true, "includeContacts": true, "includeMessages": true, "includePhotos": true}'::jsonb
);
```

### Initializing Progress Tracking
```sql
SELECT initialize_backup_progress(
    'session-uuid-here',
    100, -- contacts
    500, -- messages  
    900  -- photos
);
```

### Updating Progress
```sql
SELECT update_backup_progress(
    'session-uuid-here',
    'photo',
    1, -- completed increment
    0  -- failed increment
);
```

### Getting Session Summary
```sql
SELECT * FROM get_backup_session_summary('session-uuid-here');
```

## Requirements Satisfied

This migration satisfies the following requirements from the task:

- ✅ Create backup_sessions table with session management fields
- ✅ Create backup_items table for individual item tracking  
- ✅ Create backup_progress table for real-time progress updates
- ✅ Add Row Level Security (RLS) policies for user data isolation
- ✅ Create database indexes for efficient backup queries

## Related Files

- `../src/types/backup.ts` - TypeScript interfaces for the backup schema
- `../src/services/BackupDatabaseService.ts` - Service layer for database operations (to be implemented)
- `backup_tracking_schema.sql` - The actual migration SQL file