# Design Document: Android Java Version Upgrade

## Overview

This design document outlines the approach for upgrading the Java version used for Android builds in the SafeKeepApp from Java 8 to Java 11 or higher. The current build is failing because the Gradle distribution (8.14.1) requires JVM runtime version 11, but the build is using Java 8.

## Architecture

The Android build system for the SafeKeepApp is based on Gradle, which is configured through several key files:

1. **android/build.gradle**: The root project build file that defines global project settings
2. **android/gradle/wrapper/gradle-wrapper.properties**: Defines the Gradle version used by the project
3. **android/gradle.properties**: Contains JVM and other Gradle-specific settings

From the analysis of these files, we can see that:

- The project is already configured to use Java 11 compatibility in the build.gradle file:
  ```gradle
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_11
      targetCompatibility JavaVersion.VERSION_11
  }
  ```
- The Gradle version is set to 8.14.1, which requires Java 11 or higher
- The JVM arguments in gradle.properties are set for memory allocation but don't specify a Java version

The issue is that while the project is configured to use Java 11 compatibility, the actual JVM running the build is Java 8, which is insufficient for Gradle 8.14.1.

## Components and Interfaces

### Components Affected

1. **Development Environment Configuration**:
   - Local Java Development Kit (JDK) installation
   - IDE Java configuration (if applicable)
   - Environment variables (JAVA_HOME)

2. **Build Configuration**:
   - Gradle wrapper configuration
   - JVM arguments in gradle.properties

3. **CI/CD Configuration** (if applicable):
   - Build environment Java version settings

### Interfaces

No application interfaces will be affected by this change. This is purely a build environment configuration change.

## Data Models

No data models will be affected by this change.

## Error Handling

The current error handling is working as expected - the build system correctly identifies that Java 8 is insufficient and provides an error message. Our solution will focus on resolving this error rather than changing error handling behavior.

## Testing Strategy

1. **Verification Testing**:
   - Verify that the Android build completes successfully after the Java version upgrade
   - Verify that all existing Android functionality works as expected

2. **Compatibility Testing**:
   - Ensure the application builds and runs on various Android devices and emulators
   - Verify that the upgrade doesn't introduce any regressions

## Implementation Approach

### Option 1: Local JDK Upgrade Only

This approach focuses on upgrading the local development environment without changing project files:

1. Install JDK 11 or higher on the development machine
2. Configure the environment to use the new JDK (JAVA_HOME)
3. No changes to project files required

**Pros**: Minimal changes to the codebase, low risk
**Cons**: Doesn't enforce the Java version for other developers

### Option 2: Project Configuration Update

This approach updates project files to explicitly require Java 11:

1. Install JDK 11 or higher on the development machine
2. Update gradle.properties to specify Java 11 as the minimum version
3. Add documentation about Java version requirements

**Pros**: Enforces the correct Java version for all developers
**Cons**: Requires changes to project files

### Option 3: Downgrade Gradle Version

This approach would downgrade the Gradle version to one compatible with Java 8:

1. Modify gradle-wrapper.properties to use an older Gradle version compatible with Java 8

**Pros**: Could avoid the need to upgrade Java
**Cons**: May introduce compatibility issues with plugins and dependencies, goes against modern development practices

## Decision

Based on the requirements and analysis, **Option 2: Project Configuration Update** is recommended because:

1. It ensures consistency across all development environments
2. It provides clear documentation for future developers
3. It follows modern development practices by using up-to-date versions
4. It makes minimal changes to the project configuration

Option 3 is not recommended as it would be a step backward in terms of using current tools and could introduce other compatibility issues.

## Implementation Details

1. **Update gradle.properties**:
   - Add a comment indicating the minimum Java version required
   - Configure Gradle to fail with a clear message if Java version is insufficient

2. **Documentation Updates**:
   - Update README.md to include Java version requirements
   - Add instructions for installing and configuring JDK 11+

3. **Developer Environment Setup**:
   - Provide instructions for installing JDK 11+ on different operating systems
   - Provide instructions for configuring JAVA_HOME environment variable