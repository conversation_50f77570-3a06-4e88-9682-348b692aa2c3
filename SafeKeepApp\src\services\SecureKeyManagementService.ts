import AsyncStorage from '@react-native-async-storage/async-storage';
import CryptoJS from 'crypto-js';
import { Platform } from 'react-native';

export interface KeyRotationResult {
  success: boolean;
  newKeyId: string;
  rotationTimestamp: Date;
  error?: string;
}

export interface KeyValidationResult {
  isValid: boolean;
  keyAge: number; // in days
  needsRotation: boolean;
  securityLevel: 'high' | 'medium' | 'low';
  issues: string[];
}

export interface SecureKeyMetadata {
  keyId: string;
  createdAt: Date;
  lastUsed: Date;
  rotationCount: number;
  algorithm: string;
  keySize: number;
  derivationIterations: number;
}

export interface KeyBackupData {
  encryptedMasterKey: string;
  keyMetadata: SecureKeyMetadata;
  recoveryHint: string;
  backupTimestamp: Date;
}

class SecureKeyManagementService {
  private readonly STORAGE_PREFIX = 'safekeep_secure_';
  private readonly MASTER_KEY_STORAGE = `${this.STORAGE_PREFIX}master_key`;
  private readonly KEY_METADATA_STORAGE = `${this.STORAGE_PREFIX}key_metadata`;
  private readonly KEY_BACKUP_STORAGE = `${this.STORAGE_PREFIX}key_backup`;
  
  // Security constants
  private readonly KEY_SIZE = 256; // bits
  private readonly MIN_ITERATIONS = 100000;
  private readonly MAX_KEY_AGE_DAYS = 90; // Rotate keys every 90 days
  private readonly SALT_SIZE = 32; // bytes
  
  /**
   * Generate a cryptographically secure master key
   */
  async generateSecureMasterKey(): Promise<{
    masterKey: string;
    keyId: string;
    metadata: SecureKeyMetadata;
  }> {
    try {
      // Generate secure random key
      const keyBytes = CryptoJS.lib.WordArray.random(this.KEY_SIZE / 8);
      const masterKey = keyBytes.toString(CryptoJS.enc.Base64);
      
      // Generate unique key ID
      const keyId = this.generateKeyId();
      
      // Create metadata
      const metadata: SecureKeyMetadata = {
        keyId,
        createdAt: new Date(),
        lastUsed: new Date(),
        rotationCount: 0,
        algorithm: 'AES-256-CBC',
        keySize: this.KEY_SIZE,
        derivationIterations: this.MIN_ITERATIONS
      };

      console.log(`🔐 Generated secure master key: ${keyId}`);
      
      return { masterKey, keyId, metadata };
      
    } catch (error) {
      console.error('Failed to generate secure master key:', error);
      throw new Error('Secure key generation failed');
    }
  }

  /**
   * Store master key with enhanced security
   */
  async storeSecureMasterKey(
    masterKey: string,
    metadata: SecureKeyMetadata,
    userPassphrase?: string
  ): Promise<void> {
    try {
      let keyToStore = masterKey;
      
      // If user provides passphrase, encrypt the master key with it
      if (userPassphrase) {
        const salt = CryptoJS.lib.WordArray.random(this.SALT_SIZE);
        const derivedKey = CryptoJS.PBKDF2(userPassphrase, salt, {
          keySize: this.KEY_SIZE / 32,
          iterations: this.MIN_ITERATIONS,
          hasher: CryptoJS.algo.SHA256
        });
        
        const encrypted = CryptoJS.AES.encrypt(masterKey, derivedKey.toString());
        keyToStore = JSON.stringify({
          encrypted: encrypted.toString(),
          salt: salt.toString(CryptoJS.enc.Base64),
          iterations: this.MIN_ITERATIONS
        });
      }

      // Store encrypted key
      await AsyncStorage.setItem(this.MASTER_KEY_STORAGE, keyToStore);
      
      // Store metadata separately
      await AsyncStorage.setItem(this.KEY_METADATA_STORAGE, JSON.stringify(metadata));
      
      console.log(`🔐 Stored secure master key: ${metadata.keyId}`);
      
    } catch (error) {
      console.error('Failed to store secure master key:', error);
      throw new Error('Secure key storage failed');
    }
  }

  /**
   * Retrieve master key with security validation
   */
  async retrieveSecureMasterKey(userPassphrase?: string): Promise<{
    masterKey: string;
    metadata: SecureKeyMetadata;
  } | null> {
    try {
      const storedKey = await AsyncStorage.getItem(this.MASTER_KEY_STORAGE);
      const storedMetadata = await AsyncStorage.getItem(this.KEY_METADATA_STORAGE);
      
      if (!storedKey || !storedMetadata) {
        return null;
      }

      const metadata: SecureKeyMetadata = JSON.parse(storedMetadata);
      let masterKey = storedKey;

      // If key is encrypted with passphrase, decrypt it
      if (userPassphrase && this.isEncryptedWithPassphrase(storedKey)) {
        const keyData = JSON.parse(storedKey);
        const salt = CryptoJS.enc.Base64.parse(keyData.salt);
        const derivedKey = CryptoJS.PBKDF2(userPassphrase, salt, {
          keySize: this.KEY_SIZE / 32,
          iterations: keyData.iterations,
          hasher: CryptoJS.algo.SHA256
        });
        
        const decrypted = CryptoJS.AES.decrypt(keyData.encrypted, derivedKey.toString());
        masterKey = decrypted.toString(CryptoJS.enc.Utf8);
        
        if (!masterKey) {
          throw new Error('Invalid passphrase');
        }
      }

      // Update last used timestamp
      metadata.lastUsed = new Date();
      await AsyncStorage.setItem(this.KEY_METADATA_STORAGE, JSON.stringify(metadata));

      return { masterKey, metadata };
      
    } catch (error) {
      console.error('Failed to retrieve secure master key:', error);
      return null;
    }
  }

  /**
   * Validate key security and determine if rotation is needed
   */
  async validateKeySecurity(): Promise<KeyValidationResult> {
    try {
      const keyData = await this.retrieveSecureMasterKey();
      
      if (!keyData) {
        return {
          isValid: false,
          keyAge: 0,
          needsRotation: true,
          securityLevel: 'low',
          issues: ['No master key found']
        };
      }

      const { metadata } = keyData;
      const issues: string[] = [];
      
      // Calculate key age
      const keyAge = Math.floor(
        (Date.now() - metadata.createdAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      // Check key age
      const needsRotation = keyAge > this.MAX_KEY_AGE_DAYS;
      if (needsRotation) {
        issues.push(`Key is ${keyAge} days old, rotation recommended`);
      }
      
      // Check algorithm strength
      if (metadata.keySize < 256) {
        issues.push('Key size below recommended 256 bits');
      }
      
      // Check derivation iterations
      if (metadata.derivationIterations < this.MIN_ITERATIONS) {
        issues.push('PBKDF2 iterations below security threshold');
      }
      
      // Determine security level
      let securityLevel: 'high' | 'medium' | 'low' = 'high';
      if (issues.length > 2) {
        securityLevel = 'low';
      } else if (issues.length > 0) {
        securityLevel = 'medium';
      }

      return {
        isValid: true,
        keyAge,
        needsRotation,
        securityLevel,
        issues
      };
      
    } catch (error) {
      console.error('Key validation failed:', error);
      return {
        isValid: false,
        keyAge: 0,
        needsRotation: true,
        securityLevel: 'low',
        issues: ['Key validation error']
      };
    }
  }

  /**
   * Rotate master key for enhanced security
   */
  async rotateKey(userPassphrase?: string): Promise<KeyRotationResult> {
    try {
      console.log('🔄 Starting key rotation...');
      
      // Get current key data
      const currentKeyData = await this.retrieveSecureMasterKey(userPassphrase);
      
      // Generate new key
      const { masterKey: newMasterKey, keyId: newKeyId, metadata: newMetadata } = 
        await this.generateSecureMasterKey();
      
      // Update rotation count if we have previous metadata
      if (currentKeyData) {
        newMetadata.rotationCount = currentKeyData.metadata.rotationCount + 1;
      }
      
      // Store new key
      await this.storeSecureMasterKey(newMasterKey, newMetadata, userPassphrase);
      
      // Create backup of old key if it exists
      if (currentKeyData) {
        await this.createKeyBackup(currentKeyData.masterKey, currentKeyData.metadata);
      }
      
      console.log(`✅ Key rotation completed: ${newKeyId}`);
      
      return {
        success: true,
        newKeyId,
        rotationTimestamp: new Date()
      };
      
    } catch (error) {
      console.error('Key rotation failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        success: false,
        newKeyId: '',
        rotationTimestamp: new Date(),
        error: errorMessage
      };
    }
  }

  /**
   * Create secure backup of encryption key
   */
  async createKeyBackup(
    masterKey: string,
    metadata: SecureKeyMetadata,
    recoveryHint?: string
  ): Promise<void> {
    try {
      // Encrypt master key for backup
      const backupSalt = CryptoJS.lib.WordArray.random(this.SALT_SIZE);
      const backupKey = CryptoJS.PBKDF2(masterKey, backupSalt, {
        keySize: this.KEY_SIZE / 32,
        iterations: this.MIN_ITERATIONS * 2, // Double iterations for backup
        hasher: CryptoJS.algo.SHA256
      });
      
      const encryptedMasterKey = CryptoJS.AES.encrypt(masterKey, backupKey.toString()).toString();
      
      const backupData: KeyBackupData = {
        encryptedMasterKey,
        keyMetadata: metadata,
        recoveryHint: recoveryHint || 'Key backup created during rotation',
        backupTimestamp: new Date()
      };
      
      // Store backup with timestamp in key name
      const backupKey_name = `${this.KEY_BACKUP_STORAGE}_${metadata.keyId}_${Date.now()}`;
      await AsyncStorage.setItem(backupKey_name, JSON.stringify(backupData));
      
      console.log(`💾 Key backup created: ${metadata.keyId}`);
      
    } catch (error) {
      console.error('Key backup creation failed:', error);
      throw new Error('Key backup failed');
    }
  }

  /**
   * Verify key integrity and detect tampering
   */
  async verifyKeyIntegrity(): Promise<{
    isIntact: boolean;
    tamperingDetected: boolean;
    issues: string[];
  }> {
    try {
      const keyData = await this.retrieveSecureMasterKey();
      const issues: string[] = [];
      
      if (!keyData) {
        return {
          isIntact: false,
          tamperingDetected: true,
          issues: ['Master key not found']
        };
      }
      
      // Verify key format and structure
      if (!this.isValidKeyFormat(keyData.masterKey)) {
        issues.push('Invalid key format detected');
      }
      
      // Test key functionality
      const testData = 'integrity_test_data';
      try {
        const testHash = CryptoJS.SHA256(testData + keyData.masterKey).toString();
        if (!testHash || testHash.length !== 64) {
          issues.push('Key functionality test failed');
        }
      } catch (error) {
        issues.push('Key cryptographic operations failed');
      }
      
      // Check metadata consistency
      if (!keyData.metadata.keyId || !keyData.metadata.createdAt) {
        issues.push('Key metadata corruption detected');
      }
      
      return {
        isIntact: issues.length === 0,
        tamperingDetected: issues.length > 0,
        issues
      };
      
    } catch (error) {
      console.error('Key integrity verification failed:', error);
      return {
        isIntact: false,
        tamperingDetected: true,
        issues: ['Integrity verification error']
      };
    }
  }

  /**
   * Securely wipe all encryption keys
   */
  async secureKeyWipe(): Promise<void> {
    try {
      console.log('🗑️ Starting secure key wipe...');
      
      // Remove all key-related storage
      await AsyncStorage.removeItem(this.MASTER_KEY_STORAGE);
      await AsyncStorage.removeItem(this.KEY_METADATA_STORAGE);
      
      // Remove all backup keys
      const allKeys = await AsyncStorage.getAllKeys();
      const backupKeys = allKeys.filter(key => key.startsWith(this.KEY_BACKUP_STORAGE));
      
      for (const backupKey of backupKeys) {
        await AsyncStorage.removeItem(backupKey);
      }
      
      console.log('✅ Secure key wipe completed');
      
    } catch (error) {
      console.error('Secure key wipe failed:', error);
      throw new Error('Key wipe failed');
    }
  }

  /**
   * Generate unique key identifier
   */
  private generateKeyId(): string {
    const timestamp = Date.now().toString(36);
    const random = CryptoJS.lib.WordArray.random(8).toString(CryptoJS.enc.Base64);
    return `key_${timestamp}_${random.replace(/[+/=]/g, '')}`;
  }

  /**
   * Check if stored key is encrypted with passphrase
   */
  private isEncryptedWithPassphrase(storedKey: string): boolean {
    try {
      const parsed = JSON.parse(storedKey);
      return parsed.encrypted && parsed.salt && parsed.iterations;
    } catch {
      return false;
    }
  }

  /**
   * Validate key format
   */
  private isValidKeyFormat(key: string): boolean {
    try {
      // Check if it's valid base64
      const decoded = CryptoJS.enc.Base64.parse(key);
      return decoded.sigBytes === this.KEY_SIZE / 8;
    } catch {
      return false;
    }
  }

  /**
   * Get key management status
   */
  async getKeyManagementStatus(): Promise<{
    hasKey: boolean;
    keyAge: number;
    securityLevel: string;
    needsRotation: boolean;
    backupCount: number;
  }> {
    try {
      const validation = await this.validateKeySecurity();
      
      // Count backup keys
      const allKeys = await AsyncStorage.getAllKeys();
      const backupCount = allKeys.filter(key => key.startsWith(this.KEY_BACKUP_STORAGE)).length;
      
      return {
        hasKey: validation.isValid,
        keyAge: validation.keyAge,
        securityLevel: validation.securityLevel,
        needsRotation: validation.needsRotation,
        backupCount
      };
      
    } catch (error) {
      console.error('Failed to get key management status:', error);
      return {
        hasKey: false,
        keyAge: 0,
        securityLevel: 'unknown',
        needsRotation: true,
        backupCount: 0
      };
    }
  }
}

export default new SecureKeyManagementService();