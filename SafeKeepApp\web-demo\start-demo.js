/**
 * Startup script for SafeKeep Web Demo
 * Starts both HTTP server and WebSocket server
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting SafeKeep Web Demo with Real-time Progress...\n');

// Start HTTP server
const httpServer = spawn('node', ['server.js'], {
    cwd: __dirname,
    stdio: 'inherit'
});

// Start WebSocket server
const wsServer = spawn('node', ['websocket-server.js'], {
    cwd: __dirname,
    stdio: 'inherit'
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down SafeKeep Web Demo...');
    
    httpServer.kill('SIGINT');
    wsServer.kill('SIGINT');
    
    setTimeout(() => {
        process.exit(0);
    }, 1000);
});

// Handle server errors
httpServer.on('error', (error) => {
    console.error('❌ HTTP Server error:', error.message);
});

wsServer.on('error', (error) => {
    console.error('❌ WebSocket Server error:', error.message);
});

// Handle server exits
httpServer.on('exit', (code) => {
    if (code !== 0) {
        console.error(`❌ HTTP Server exited with code ${code}`);
    }
});

wsServer.on('exit', (code) => {
    if (code !== 0) {
        console.error(`❌ WebSocket Server exited with code ${code}`);
    }
});

console.log('✅ Both servers started successfully!');
console.log('📡 HTTP Server: http://localhost:3000');
console.log('🔌 WebSocket Server: ws://localhost:3001/progress');
console.log('\n💡 Press Ctrl+C to stop both servers\n');