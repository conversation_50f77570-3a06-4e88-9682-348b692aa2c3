import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BackupSettingsScreen from '../BackupSettingsScreen';
import settingsReducer from '../../../store/slices/settingsSlice';
import { BackupConfigurationService } from '../../../services/BackupConfigurationService';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

// Mock BackupConfigurationService
jest.mock('../../../services/BackupConfigurationService');

const mockStore = configureStore({
  reducer: {
    settings: settingsReducer,
  },
});

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      {component}
    </Provider>
  );
};

describe('BackupSettingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (BackupConfigurationService.loadConfiguration as jest.Mock).mockResolvedValue({
      autoBackup: false,
      wifiOnly: true,
      includeContacts: true,
      includeMessages: true,
      includePhotos: true,
      compressionLevel: 'medium',
    });
  });

  it('renders loading state initially', () => {
    const { getByText } = renderWithProvider(<BackupSettingsScreen />);
    expect(getByText('Loading settings...')).toBeTruthy();
  });

  it('loads and displays backup configuration', async () => {
    const { getByText } = renderWithProvider(<BackupSettingsScreen />);
    
    await waitFor(() => {
      expect(getByText('Data Types to Backup')).toBeTruthy();
      expect(getByText('Network Preferences')).toBeTruthy();
      expect(getByText('Automatic Backup')).toBeTruthy();
    });
  });

  it('displays data type toggles correctly', async () => {
    const { getByText } = renderWithProvider(<BackupSettingsScreen />);
    
    await waitFor(() => {
      expect(getByText('Contacts')).toBeTruthy();
      expect(getByText('Messages')).toBeTruthy();
      expect(getByText('Photos')).toBeTruthy();
      expect(getByText('Names, phone numbers, email addresses')).toBeTruthy();
      expect(getByText('SMS text messages and conversations')).toBeTruthy();
      expect(getByText('Camera roll images (videos excluded)')).toBeTruthy();
    });
  });

  it('shows data warning when WiFi-only is disabled', async () => {
    (BackupConfigurationService.loadConfiguration as jest.Mock).mockResolvedValue({
      autoBackup: false,
      wifiOnly: false,
      includeContacts: true,
      includeMessages: true,
      includePhotos: true,
      compressionLevel: 'medium',
    });

    const { getByText } = renderWithProvider(<BackupSettingsScreen />);
    
    await waitFor(() => {
      expect(getByText('Cellular backups may use significant data. Monitor your usage.')).toBeTruthy();
    });
  });

  it('displays automatic backup scheduling when enabled', async () => {
    (BackupConfigurationService.loadConfiguration as jest.Mock).mockResolvedValue({
      autoBackup: true,
      wifiOnly: true,
      includeContacts: true,
      includeMessages: true,
      includePhotos: true,
      compressionLevel: 'medium',
    });

    const { getByText } = renderWithProvider(<BackupSettingsScreen />);
    
    await waitFor(() => {
      expect(getByText('Backup Frequency')).toBeTruthy();
      expect(getByText('Daily')).toBeTruthy();
      expect(getByText('Weekly')).toBeTruthy();
      expect(getByText('Monthly')).toBeTruthy();
      expect(getByText('Update Schedule')).toBeTruthy();
    });
  });

  it('shows compression settings', async () => {
    const { getByText } = renderWithProvider(<BackupSettingsScreen />);
    
    await waitFor(() => {
      expect(getByText('Compression Settings')).toBeTruthy();
      expect(getByText('Photo Compression Level')).toBeTruthy();
      expect(getByText('Higher compression reduces storage usage but may affect image quality.')).toBeTruthy();
    });
  });

  it('displays settings summary', async () => {
    const { getByText } = renderWithProvider(<BackupSettingsScreen />);
    
    await waitFor(() => {
      expect(getByText('Settings Summary')).toBeTruthy();
      expect(getByText(/Data types: 3 of 3 selected/)).toBeTruthy();
      expect(getByText(/Network: WiFi only/)).toBeTruthy();
      expect(getByText(/Auto backup: Disabled/)).toBeTruthy();
      expect(getByText(/Compression: medium/)).toBeTruthy();
    });
  });

  it('saves configuration when settings are changed', async () => {
    (BackupConfigurationService.saveConfiguration as jest.Mock).mockResolvedValue(undefined);
    
    const { getByTestId } = renderWithProvider(<BackupSettingsScreen />);
    
    await waitFor(() => {
      // This would require adding testID props to switches in the actual component
      // For now, we'll test that the service method gets called
      expect(BackupConfigurationService.loadConfiguration).toHaveBeenCalled();
    });
  });
});