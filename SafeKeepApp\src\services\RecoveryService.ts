import { supabase, TABLES, BUCKETS } from '../config/supabase';
import AuthService from './AuthService';
import EncryptionService from './EncryptionService';
import CloudStorageService, { FileMetadata } from './CloudStorageService';

export interface BackupSummary {
  totalPhotos: number;
  totalContacts: number;
  totalMessages: number;
  totalDocuments: number;
  storageUsed: string;
  lastBackup: string;
  categories: BackupCategory[];
}

export interface BackupCategory {
  type: 'photo' | 'contact' | 'message' | 'document';
  count: number;
  size: string;
  icon: string;
  lastModified: Date;
}

export interface RestoreOptions {
  selectAll: boolean;
  categories: {
    photos: { selected: boolean; downloadWifi: boolean; downloadCellular: boolean };
    contacts: { selected: boolean; mergeStrategy: 'replace' | 'merge' | 'smart' };
    messages: { selected: boolean; format: 'native' | 'export' };
    documents: { selected: boolean };
  };
}

export interface RestoreProgress {
  currentCategory: string;
  progress: {
    [key: string]: {
      current: number;
      total: number;
      percentage: number;
      status: 'queued' | 'downloading' | 'decrypting' | 'integrating' | 'complete' | 'error';
    };
  };
  estimatedTime: string;
  networkStatus: 'wifi' | 'cellular' | 'offline';
  pauseResumeEnabled: boolean;
  backgroundDownload: boolean;
}

export interface RestoreResult {
  success: boolean;
  restored: {
    photos: number;
    contacts: number;
    messages: number;
    documents: number;
  };
  failed: {
    photos: number;
    contacts: number;
    messages: number;
    documents: number;
  };
  errors: string[];
  duration: number;
}

class RecoveryService {
  private isRestoring = false;
  private shouldPauseRestore = false;
  private progressCallback?: (progress: RestoreProgress) => void;
  private currentRestoreSession?: string;

  // Authenticate user for recovery
  async authenticateForRecovery(email: string, password: string): Promise<{
    success: boolean;
    user?: any;
    requiresTwoFactor?: boolean;
    error?: string;
  }> {
    try {
      console.log('🔐 Authenticating user for data recovery...');
      
      const result = await AuthService.signInUser(email, password);
      
      if (result.success) {
        // Check if user has backup data
        const hasBackups = await this.checkUserHasBackups();
        
        if (!hasBackups) {
          return {
            success: false,
            error: 'No backup data found for this account. Please ensure you are using the correct email address.'
          };
        }
        
        console.log('✅ Authentication successful, backup data found');
        return {
          success: true,
          user: result.user,
          requiresTwoFactor: false // TODO: Implement 2FA check
        };
      } else {
        return {
          success: false,
          error: result.error
        };
      }
    } catch (error) {
      console.error('❌ Recovery authentication failed:', error);
      return {
        success: false,
        error: 'Authentication failed. Please try again.'
      };
    }
  }

  // Check if user has any backup data
  async checkUserHasBackups(): Promise<boolean> {
    try {
      const user = AuthService.getCurrentUser();
      if (!user) return false;

      const { data, error } = await supabase
        .from(TABLES.FILE_METADATA)
        .select('id')
        .eq('user_id', user.id)
        .limit(1);

      return !error && data && data.length > 0;
    } catch (error) {
      console.error('Error checking for backups:', error);
      return false;
    }
  }

  // Fetch backup summary and metadata
  async fetchBackupSummary(): Promise<BackupSummary | null> {
    try {
      console.log('📊 Fetching backup summary...');
      
      const user = AuthService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get all file metadata for the user
      const { data: files, error } = await supabase
        .from(TABLES.FILE_METADATA)
        .select('*')
        .eq('user_id', user.id)
        .order('uploaded_at', { ascending: false });

      if (error) {
        throw error;
      }

      if (!files || files.length === 0) {
        return null;
      }

      // Categorize and count files
      const categories: { [key: string]: { count: number; size: number; lastModified: Date } } = {
        photo: { count: 0, size: 0, lastModified: new Date(0) },
        contact: { count: 0, size: 0, lastModified: new Date(0) },
        message: { count: 0, size: 0, lastModified: new Date(0) },
        document: { count: 0, size: 0, lastModified: new Date(0) }
      };

      let totalSize = 0;
      let lastBackup = new Date(0);

      files.forEach(file => {
        const category = file.category || 'document';
        if (categories[category]) {
          categories[category].count++;
          categories[category].size += file.encrypted_size || 0;
          
          const fileDate = new Date(file.uploaded_at);
          if (fileDate > categories[category].lastModified) {
            categories[category].lastModified = fileDate;
          }
          
          if (fileDate > lastBackup) {
            lastBackup = fileDate;
          }
        }
        totalSize += file.encrypted_size || 0;
      });

      // Format the summary
      const summary: BackupSummary = {
        totalPhotos: categories.photo.count,
        totalContacts: categories.contact.count,
        totalMessages: categories.message.count,
        totalDocuments: categories.document.count,
        storageUsed: this.formatBytes(totalSize),
        lastBackup: this.formatRelativeTime(lastBackup),
        categories: [
          {
            type: 'photo',
            count: categories.photo.count,
            size: this.formatBytes(categories.photo.size),
            icon: '📸',
            lastModified: categories.photo.lastModified
          },
          {
            type: 'contact',
            count: categories.contact.count,
            size: this.formatBytes(categories.contact.size),
            icon: '📱',
            lastModified: categories.contact.lastModified
          },
          {
            type: 'message',
            count: categories.message.count,
            size: this.formatBytes(categories.message.size),
            icon: '💬',
            lastModified: categories.message.lastModified
          },
          {
            type: 'document',
            count: categories.document.count,
            size: this.formatBytes(categories.document.size),
            icon: '📄',
            lastModified: categories.document.lastModified
          }
        ].filter(cat => cat.count > 0) // Only show categories with data
      };

      console.log('✅ Backup summary retrieved:', summary);
      return summary;
    } catch (error) {
      console.error('❌ Failed to fetch backup summary:', error);
      return null;
    }
  }

  // Start the restoration process
  async startRestore(
    options: RestoreOptions,
    onProgress?: (progress: RestoreProgress) => void
  ): Promise<RestoreResult> {
    if (this.isRestoring) {
      throw new Error('Restore operation already in progress');
    }

    const user = AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User authentication required for restore');
    }

    this.isRestoring = true;
    this.shouldPauseRestore = false;
    this.progressCallback = onProgress;
    this.currentRestoreSession = `restore_${user.id}_${Date.now()}`;

    const startTime = Date.now();
    const result: RestoreResult = {
      success: false,
      restored: { photos: 0, contacts: 0, messages: 0, documents: 0 },
      failed: { photos: 0, contacts: 0, messages: 0, documents: 0 },
      errors: [],
      duration: 0
    };

    try {
      console.log('🚀 Starting data restoration...');

      // Get files to restore based on selected categories
      const filesToRestore = await this.getFilesToRestore(options);
      
      if (filesToRestore.length === 0) {
        throw new Error('No files selected for restoration');
      }

      // Initialize progress tracking
      const progress: RestoreProgress = {
        currentCategory: '',
        progress: {},
        estimatedTime: 'Calculating...',
        networkStatus: 'wifi', // TODO: Detect actual network status
        pauseResumeEnabled: true,
        backgroundDownload: false
      };

      // Initialize progress for each category
      Object.keys(options.categories).forEach(category => {
        if (options.categories[category as keyof typeof options.categories].selected) {
          const categoryFiles = filesToRestore.filter(f => f.category === category);
          progress.progress[category] = {
            current: 0,
            total: categoryFiles.length,
            percentage: 0,
            status: 'queued'
          };
        }
      });

      // Process each category
      for (const [category, categoryOptions] of Object.entries(options.categories)) {
        if (!categoryOptions.selected) continue;

        progress.currentCategory = category;
        progress.progress[category].status = 'downloading';
        this.updateProgress(progress);

        const categoryFiles = filesToRestore.filter(f => f.category === category);
        
        for (let i = 0; i < categoryFiles.length; i++) {
          if (this.shouldPauseRestore) {
            await this.waitForResume();
          }

          const file = categoryFiles[i];
          
          try {
            // Download and decrypt file
            progress.progress[category].status = 'downloading';
            this.updateProgress(progress);
            
            const downloadResult = await CloudStorageService.downloadFile(file.id);
            
            if (!downloadResult.success) {
              throw new Error(downloadResult.error || 'Download failed');
            }

            // Update progress
            progress.progress[category].current = i + 1;
            progress.progress[category].percentage = Math.round(((i + 1) / categoryFiles.length) * 100);
            progress.progress[category].status = 'integrating';
            this.updateProgress(progress);

            // Integrate with device (placeholder - actual implementation would vary by platform)
            await this.integrateWithDevice(category as any, downloadResult.data!, downloadResult.fileName!);
            
            result.restored[category as keyof typeof result.restored]++;
          } catch (error) {
            console.error(`Failed to restore ${file.original_name}:`, error);
            result.failed[category as keyof typeof result.failed]++;
            result.errors.push(`${file.original_name}: ${error.message}`);
          }
        }

        progress.progress[category].status = 'complete';
        this.updateProgress(progress);
      }

      result.success = result.errors.length === 0;
      result.duration = Date.now() - startTime;

      console.log('✅ Data restoration completed:', result);
      return result;

    } catch (error) {
      console.error('❌ Data restoration failed:', error);
      result.errors.push(error.message);
      result.duration = Date.now() - startTime;
      return result;
    } finally {
      this.isRestoring = false;
      this.progressCallback = undefined;
      this.currentRestoreSession = undefined;
    }
  }

  // Get files to restore based on options
  private async getFilesToRestore(options: RestoreOptions): Promise<FileMetadata[]> {
    const user = AuthService.getCurrentUser();
    if (!user) return [];

    const selectedCategories = Object.entries(options.categories)
      .filter(([_, opts]) => opts.selected)
      .map(([category, _]) => category);

    if (selectedCategories.length === 0) return [];

    const { data: files, error } = await supabase
      .from(TABLES.FILE_METADATA)
      .select('*')
      .eq('user_id', user.id)
      .in('category', selectedCategories)
      .order('uploaded_at', { ascending: false });

    if (error) {
      throw error;
    }

    return files || [];
  }

  // Integrate restored data with device
  private async integrateWithDevice(
    dataType: 'photo' | 'contact' | 'message' | 'document',
    data: string,
    fileName: string
  ): Promise<void> {
    // This is a placeholder implementation
    // In a real app, this would integrate with platform-specific APIs
    
    switch (dataType) {
      case 'photo':
        // Save to camera roll using react-native-cameraroll
        console.log(`📸 Integrating photo: ${fileName}`);
        break;
      case 'contact':
        // Add to contacts using react-native-contacts
        console.log(`📱 Integrating contact: ${fileName}`);
        break;
      case 'message':
        // Store in app database
        console.log(`💬 Integrating message: ${fileName}`);
        break;
      case 'document':
        // Save to documents folder
        console.log(`📄 Integrating document: ${fileName}`);
        break;
    }

    // Simulate integration time
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Update progress callback
  private updateProgress(progress: RestoreProgress): void {
    if (this.progressCallback) {
      this.progressCallback(progress);
    }
  }

  // Wait for resume when paused
  private async waitForResume(): Promise<void> {
    while (this.shouldPauseRestore) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Control functions
  pauseRestore(): void {
    this.shouldPauseRestore = true;
    console.log('⏸️ Data restoration paused');
  }

  resumeRestore(): void {
    this.shouldPauseRestore = false;
    console.log('▶️ Data restoration resumed');
  }

  cancelRestore(): void {
    this.isRestoring = false;
    this.shouldPauseRestore = false;
    console.log('❌ Data restoration cancelled');
  }

  isRunning(): boolean {
    return this.isRestoring;
  }

  // Utility functions
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  private formatRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  }
}

export default new RecoveryService();
