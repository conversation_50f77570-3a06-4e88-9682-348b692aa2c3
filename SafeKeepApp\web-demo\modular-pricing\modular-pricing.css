/**
 * Modular Pricing UI - Complete Stylesheet
 * Imports all component styles for the modular pricing interface
 */

/* Import all component styles */
@import url('./styles/ModularPricingUI.css');
@import url('./styles/ServiceCheckbox.css');
@import url('./styles/ServiceSelector.css');
@import url('./styles/ServiceDetails.css');
@import url('./styles/PricingCalculator.css');
@import url('./styles/SavingsDisplay.css');
@import url('./styles/PlanRecommendations.css');

/* Global styles and overrides */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f0f2f5;
    line-height: 1.6;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.bounce-in {
    animation: bounceIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Focus ring utilities */
.focus-ring:focus {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
    * {
        border-color: #000 !important;
    }
    
    .modular-pricing-ui {
        background: #fff !important;
        color: #000 !important;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Print optimizations */
@media print {
    .modular-pricing-ui {
        background: white !important;
        color: black !important;
    }
    
    * {
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .pricing-actions {
        display: none !important;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .modular-pricing-ui:not(.theme-light) {
        background: #1a1a1a;
        color: #ffffff;
    }
    
    .modular-pricing-ui:not(.theme-light) .pricing-container {
        background: #2d2d2d;
        border-color: #444;
    }
    
    .modular-pricing-ui:not(.theme-light) .service-checkbox-label {
        background: #333;
        border-color: #555;
        color: #fff;
    }
    
    .modular-pricing-ui:not(.theme-light) .service-checkbox.checked .service-checkbox-label {
        background: #2a2a3a;
    }
}