import { Alert, Platform } from 'react-native';
import { BackupError, UserNotification, ErrorRecoveryAction } from '../types/backup';
import PermissionService from './PermissionService';

export interface NotificationConfig {
  showSystemAlerts: boolean;
  persistentNotifications: boolean;
  autoRetryEnabled: boolean;
  maxNotificationsPerSession: number;
}

class NotificationService {
  private notifications: UserNotification[] = [];
  private notificationCount = 0;
  private readonly maxNotifications = 10;
  
  private config: NotificationConfig = {
    showSystemAlerts: true,
    persistentNotifications: true,
    autoRetryEnabled: true,
    maxNotificationsPerSession: 5
  };

  /**
   * Show error notification with actionable recovery options
   */
  async showErrorNotification(
    error: BackupError,
    context?: {
      dataType?: 'contacts' | 'messages' | 'photos';
      sessionId?: string;
      onRetry?: () => Promise<void>;
      onSkip?: () => Promise<void>;
      onCancel?: () => Promise<void>;
    }
  ): Promise<void> {
    console.log(`🚨 Showing error notification for: ${error.type} - ${error.message}`);

    // Create recovery actions based on error type
    const actions = this.createRecoveryActions(error, context);
    
    // Create notification
    const notification: UserNotification = {
      id: `error_${error.id}`,
      type: 'error',
      title: this.getErrorTitle(error),
      message: this.getErrorMessage(error, context?.dataType),
      timestamp: new Date(),
      persistent: error.type === 'permission' || !error.retryable,
      actions,
      context: {
        errorId: error.id,
        errorType: error.type,
        dataType: context?.dataType,
        sessionId: context?.sessionId
      }
    };

    // Add to notification list
    this.addNotification(notification);

    // Show system alert if enabled
    if (this.config.showSystemAlerts && this.notificationCount < this.config.maxNotificationsPerSession) {
      await this.showSystemAlert(notification);
      this.notificationCount++;
    }
  }

  /**
   * Show warning notification for non-critical issues
   */
  async showWarningNotification(
    title: string,
    message: string,
    actions?: ErrorRecoveryAction[]
  ): Promise<void> {
    const notification: UserNotification = {
      id: `warning_${Date.now()}`,
      type: 'warning',
      title,
      message,
      timestamp: new Date(),
      persistent: false,
      actions
    };

    this.addNotification(notification);

    if (this.config.showSystemAlerts) {
      await this.showSystemAlert(notification);
    }
  }

  /**
   * Show info notification for user guidance
   */
  async showInfoNotification(
    title: string,
    message: string,
    persistent = false
  ): Promise<void> {
    const notification: UserNotification = {
      id: `info_${Date.now()}`,
      type: 'info',
      title,
      message,
      timestamp: new Date(),
      persistent
    };

    this.addNotification(notification);
  }

  /**
   * Show success notification
   */
  async showSuccessNotification(
    title: string,
    message: string
  ): Promise<void> {
    const notification: UserNotification = {
      id: `success_${Date.now()}`,
      type: 'success',
      title,
      message,
      timestamp: new Date(),
      persistent: false
    };

    this.addNotification(notification);
  }

  /**
   * Show permission request notification with guidance
   */
  async showPermissionNotification(
    permissionType: 'contacts' | 'messages' | 'photos',
    onGrant?: () => Promise<void>,
    onSkip?: () => Promise<void>
  ): Promise<void> {
    const permissionInfo = this.getPermissionInfo(permissionType);
    
    const actions: ErrorRecoveryAction[] = [
      {
        id: 'grant_permission',
        label: 'Grant Permission',
        action: async () => {
          const result = await PermissionService.requestPermission(permissionType);
          if (result.granted && onGrant) {
            await onGrant();
          }
        },
        primary: true
      }
    ];

    if (onSkip) {
      actions.push({
        id: 'skip_permission',
        label: 'Skip for Now',
        action: onSkip
      });
    }

    const notification: UserNotification = {
      id: `permission_${permissionType}`,
      type: 'warning',
      title: `${permissionInfo.icon} Permission Needed`,
      message: `${permissionInfo.message}\n\nThis will help you:\n${permissionInfo.benefits.map(b => `• ${b}`).join('\n')}`,
      timestamp: new Date(),
      persistent: true,
      actions
    };

    this.addNotification(notification);
    
    if (this.config.showSystemAlerts) {
      await this.showSystemAlert(notification);
    }
  }

  /**
   * Show network condition warning
   */
  async showNetworkWarning(
    isWiFi: boolean,
    wifiOnlyEnabled: boolean,
    onContinue?: () => Promise<void>,
    onWaitForWiFi?: () => Promise<void>
  ): Promise<void> {
    let title: string;
    let message: string;
    const actions: ErrorRecoveryAction[] = [];

    if (wifiOnlyEnabled && !isWiFi) {
      title = '📶 WiFi Required';
      message = 'Your backup settings require WiFi, but you\'re currently on cellular data. Please connect to WiFi or change your settings.';
      
      actions.push({
        id: 'wait_wifi',
        label: 'Wait for WiFi',
        action: onWaitForWiFi || (() => Promise.resolve()),
        primary: true
      });
    } else if (!isWiFi) {
      title = '📱 Cellular Data Warning';
      message = 'You\'re about to backup using cellular data. This may use significant data from your plan. Consider connecting to WiFi first.';
      
      actions.push(
        {
          id: 'continue_cellular',
          label: 'Continue Anyway',
          action: onContinue || (() => Promise.resolve())
        },
        {
          id: 'wait_wifi',
          label: 'Wait for WiFi',
          action: onWaitForWiFi || (() => Promise.resolve()),
          primary: true
        }
      );
    }

    if (actions.length > 0) {
      const notification: UserNotification = {
        id: 'network_warning',
        type: 'warning',
        title,
        message,
        timestamp: new Date(),
        persistent: false,
        actions
      };

      this.addNotification(notification);
      
      if (this.config.showSystemAlerts) {
        await this.showSystemAlert(notification);
      }
    }
  }

  /**
   * Show battery warning
   */
  async showBatteryWarning(
    batteryLevel: number,
    onContinue?: () => Promise<void>,
    onPause?: () => Promise<void>
  ): Promise<void> {
    const actions: ErrorRecoveryAction[] = [
      {
        id: 'pause_backup',
        label: 'Pause Backup',
        action: onPause || (() => Promise.resolve()),
        primary: true
      }
    ];

    if (onContinue) {
      actions.push({
        id: 'continue_backup',
        label: 'Continue Anyway',
        action: onContinue
      });
    }

    const notification: UserNotification = {
      id: 'battery_warning',
      type: 'warning',
      title: '🔋 Low Battery',
      message: `Your battery is at ${batteryLevel}%. Backup may drain your battery further. Consider charging your device or pausing the backup.`,
      timestamp: new Date(),
      persistent: false,
      actions
    };

    this.addNotification(notification);
    
    if (this.config.showSystemAlerts) {
      await this.showSystemAlert(notification);
    }
  }

  /**
   * Show storage quota warning
   */
  async showStorageWarning(
    usedSpace: number,
    totalSpace: number,
    onUpgrade?: () => Promise<void>,
    onContinue?: () => Promise<void>
  ): Promise<void> {
    const usagePercent = Math.round((usedSpace / totalSpace) * 100);
    
    const actions: ErrorRecoveryAction[] = [];
    
    if (onUpgrade) {
      actions.push({
        id: 'upgrade_storage',
        label: 'Upgrade Storage',
        action: onUpgrade,
        primary: true
      });
    }

    if (onContinue) {
      actions.push({
        id: 'continue_backup',
        label: 'Continue Anyway',
        action: onContinue
      });
    }

    const notification: UserNotification = {
      id: 'storage_warning',
      type: 'warning',
      title: '💾 Storage Almost Full',
      message: `Your backup storage is ${usagePercent}% full. You may not be able to complete this backup. Consider upgrading your storage plan.`,
      timestamp: new Date(),
      persistent: true,
      actions
    };

    this.addNotification(notification);
    
    if (this.config.showSystemAlerts) {
      await this.showSystemAlert(notification);
    }
  }

  /**
   * Show backup resume notification
   */
  async showResumeNotification(
    sessionId: string,
    lastDataType: string,
    itemsCompleted: number,
    totalItems: number,
    onResume?: () => Promise<void>,
    onRestart?: () => Promise<void>
  ): Promise<void> {
    const progress = Math.round((itemsCompleted / totalItems) * 100);
    
    const actions: ErrorRecoveryAction[] = [];
    
    if (onResume) {
      actions.push({
        id: 'resume_backup',
        label: 'Resume Backup',
        action: onResume,
        primary: true
      });
    }

    if (onRestart) {
      actions.push({
        id: 'restart_backup',
        label: 'Start Over',
        action: onRestart
      });
    }

    const notification: UserNotification = {
      id: `resume_${sessionId}`,
      type: 'info',
      title: '⏸️ Resume Backup?',
      message: `Your previous backup was interrupted at ${progress}% complete (${itemsCompleted}/${totalItems} items). Would you like to resume where you left off?`,
      timestamp: new Date(),
      persistent: true,
      actions
    };

    this.addNotification(notification);
    
    if (this.config.showSystemAlerts) {
      await this.showSystemAlert(notification);
    }
  }

  /**
   * Create recovery actions based on error type
   */
  private createRecoveryActions(
    error: BackupError,
    context?: {
      dataType?: 'contacts' | 'messages' | 'photos';
      onRetry?: () => Promise<void>;
      onSkip?: () => Promise<void>;
      onCancel?: () => Promise<void>;
    }
  ): ErrorRecoveryAction[] {
    const actions: ErrorRecoveryAction[] = [];

    switch (error.type) {
      case 'permission':
        actions.push({
          id: 'grant_permission',
          label: 'Grant Permission',
          action: async () => {
            if (context?.dataType) {
              await PermissionService.requestPermission(context.dataType);
            }
          },
          primary: true
        });
        break;

      case 'network':
        if (error.retryable && context?.onRetry) {
          actions.push({
            id: 'retry_operation',
            label: 'Retry',
            action: context.onRetry,
            primary: true
          });
        }
        break;

      case 'storage':
        actions.push({
          id: 'manage_storage',
          label: 'Manage Storage',
          action: async () => {
            // Navigate to storage management
            console.log('Navigate to storage management');
          },
          primary: true
        });
        break;

      case 'platform':
        if (error.retryable && context?.onRetry) {
          actions.push({
            id: 'retry_operation',
            label: 'Try Again',
            action: context.onRetry
          });
        }
        break;
    }

    // Common actions
    if (context?.onSkip) {
      actions.push({
        id: 'skip_item',
        label: 'Skip This Item',
        action: context.onSkip
      });
    }

    if (context?.onCancel) {
      actions.push({
        id: 'cancel_backup',
        label: 'Cancel Backup',
        action: context.onCancel
      });
    }

    return actions;
  }

  /**
   * Show system alert dialog
   */
  private async showSystemAlert(notification: UserNotification): Promise<void> {
    return new Promise((resolve) => {
      const buttons = notification.actions?.map(action => ({
        text: action.label,
        style: action.primary ? 'default' : 'cancel' as any,
        onPress: async () => {
          try {
            await action.action();
          } catch (error) {
            console.error(`Error executing action ${action.id}:`, error);
          }
          resolve();
        }
      })) || [{ text: 'OK', onPress: () => resolve() }];

      Alert.alert(
        notification.title,
        notification.message,
        buttons,
        { cancelable: false }
      );
    });
  }

  /**
   * Get error title based on error type
   */
  private getErrorTitle(error: BackupError): string {
    const titles: Record<BackupError['type'], string> = {
      permission: '🔐 Permission Required',
      network: '📶 Connection Issue',
      storage: '💾 Storage Issue',
      encryption: '🔒 Security Issue',
      platform: '⚠️ System Issue'
    };

    return titles[error.type] || '❌ Backup Error';
  }

  /**
   * Get user-friendly error message
   */
  private getErrorMessage(error: BackupError, dataType?: string): string {
    const dataTypeLabel = dataType ? ` your ${dataType}` : ' your data';
    
    const messages: Record<BackupError['type'], string> = {
      permission: `We need permission to backup${dataTypeLabel}. Please grant access to continue.`,
      network: `There's a problem with your internet connection. We'll keep trying to backup${dataTypeLabel}.`,
      storage: `There's not enough storage space to backup${dataTypeLabel}. Please free up space or upgrade your plan.`,
      encryption: `There was a security issue while preparing${dataTypeLabel} for backup. This helps keep your data safe.`,
      platform: `There was a system issue while backing up${dataTypeLabel}. This sometimes happens and we'll try again.`
    };

    return messages[error.type] || `There was an issue backing up${dataTypeLabel}: ${error.message}`;
  }

  /**
   * Get permission information for notifications
   */
  private getPermissionInfo(permissionType: 'contacts' | 'messages' | 'photos') {
    const permissionInfos = {
      contacts: {
        icon: '📞',
        message: 'SafeKeep needs access to your contacts to back them up safely.',
        benefits: [
          'Never lose important phone numbers',
          'Restore contacts on new devices',
          'Keep family and friends\' info safe'
        ]
      },
      messages: {
        icon: '💬',
        message: 'SafeKeep needs access to your messages to back them up.',
        benefits: [
          'Save important conversations',
          'Keep precious text memories',
          'Restore messages on new devices'
        ]
      },
      photos: {
        icon: '📸',
        message: 'SafeKeep needs access to your photos to back them up.',
        benefits: [
          'Never lose precious memories',
          'Access photos from anywhere',
          'Share memories with family easily'
        ]
      }
    };

    return permissionInfos[permissionType];
  }

  /**
   * Add notification to the list
   */
  private addNotification(notification: UserNotification): void {
    this.notifications.unshift(notification);
    
    // Keep only the most recent notifications
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.maxNotifications);
    }
  }

  /**
   * Get all notifications
   */
  getNotifications(): UserNotification[] {
    return [...this.notifications];
  }

  /**
   * Clear all notifications
   */
  clearNotifications(): void {
    this.notifications = [];
    this.notificationCount = 0;
  }

  /**
   * Clear specific notification
   */
  clearNotification(id: string): void {
    this.notifications = this.notifications.filter(n => n.id !== id);
  }

  /**
   * Update notification configuration
   */
  updateConfig(config: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get notification statistics
   */
  getStatistics(): {
    totalNotifications: number;
    errorNotifications: number;
    warningNotifications: number;
    persistentNotifications: number;
  } {
    return {
      totalNotifications: this.notifications.length,
      errorNotifications: this.notifications.filter(n => n.type === 'error').length,
      warningNotifications: this.notifications.filter(n => n.type === 'warning').length,
      persistentNotifications: this.notifications.filter(n => n.persistent).length
    };
  }
}

export default new NotificationService();