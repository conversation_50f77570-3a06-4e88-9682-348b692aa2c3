import BackupManager from '../../src/services/BackupManager';
import ContactBackupService from '../../src/services/ContactBackupService';
import MessageBackupService from '../../src/services/MessageBackupService';
import PhotoBackupService from '../../src/services/PhotoBackupService';
import AuthService from '../../src/services/AuthService';
import PermissionService from '../../src/services/PermissionService';
import EncryptionService from '../../src/services/EncryptionService';
import CloudStorageService from '../../src/services/CloudStorageService';
import NotificationService from '../../src/services/NotificationService';
import RetryService from '../../src/services/RetryService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupConfiguration, BackupError } from '../../src/types/backup';
import { Platform, PermissionsAndroid } from 'react-native';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage');
jest.mock('../../src/utils/helpers', () => ({
  isWiFiConnected: jest.fn().mockResolvedValue(true),
  hasSufficientBattery: jest.fn().mockResolvedValue(true),
  generateId: jest.fn().mockReturnValue('test-session-id'),
  delay: jest.fn().mockResolvedValue(undefined)
}));
jest.mock('react-native', () => ({
  Platform: { OS: 'android' },
  PermissionsAndroid: {
    request: jest.fn(),
    PERMISSIONS: {
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      READ_SMS: 'android.permission.READ_SMS',
      READ_CONTACTS: 'android.permission.READ_CONTACTS'
    },
    RESULTS: { GRANTED: 'granted', DENIED: 'denied' }
  }
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockPermissionsAndroid = PermissionsAndroid as jest.Mocked<typeof PermissionsAndroid>;

describe('Error Recovery Integration Tests', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    aud: 'authenticated',
    role: 'authenticated',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    app_metadata: {},
    user_metadata: {}
  };

  const defaultConfiguration: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium'
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Setup basic mocks
    jest.spyOn(AuthService, 'getCurrentUser').mockReturnValue(mockUser);
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    mockPermissionsAndroid.request.mockResolvedValue('granted');
    
    // Setup notification service
    jest.spyOn(NotificationService, 'showErrorNotification').mockResolvedValue();
    jest.spyOn(NotificationService, 'showWarningNotification').mockResolvedValue();
    jest.spyOn(NotificationService, 'showRetryNotification').mockResolvedValue();
    
    // Setup retry service
    jest.spyOn(RetryService, 'executeWithRetry').mockImplementation(async (operation, config) => {
      return await operation();
    });
    
    await BackupManager.initialize();
  });

  describe('Authentication and Authorization Errors', () => {
    it('should handle user not authenticated error', async () => {
      jest.spyOn(AuthService, 'getCurrentUser').mockReturnValue(null);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('authentication required');
      expect(result.errors[0].retryable).toBe(true);
    });

    it('should handle expired authentication during backup', async () => {
      // Start with valid auth
      jest.spyOn(AuthService, 'getCurrentUser').mockReturnValue(mockUser);
      
      // Mock auth expiring during backup
      jest.spyOn(CloudStorageService, 'uploadFile').mockRejectedValue(new Error('Authentication expired'));
      
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ] as any);
      
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('data', 'contact.json', 'application/json', 'contact');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'auth_expired_error',
              type: 'permission',
              message: 'Authentication expired',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('Authentication expired'))).toBe(true);
      expect(NotificationService.showErrorNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'permission',
          retryable: true
        })
      );
    });

    it('should handle insufficient permissions error', async () => {
      jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
        contacts: { granted: false, denied: true, blocked: false, unavailable: false },
        sms: { granted: false, denied: true, blocked: false, unavailable: false },
        photos: { granted: false, denied: true, blocked: false, unavailable: false }
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('Missing permissions');
    });

    it('should handle permission revocation during backup', async () => {
      // Start with permissions granted
      jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
        contacts: { granted: true, denied: false, blocked: false, unavailable: false },
        sms: { granted: true, denied: false, blocked: false, unavailable: false },
        photos: { granted: true, denied: false, blocked: false, unavailable: false }
      });

      // Mock permission being revoked during photo backup
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockRejectedValue(new Error('Photo permission denied'));
      jest.spyOn(PhotoBackupService, 'backupPhotos').mockResolvedValue({
        success: false,
        itemsProcessed: 0,
        errors: [{
          id: 'permission_revoked',
          type: 'permission',
          message: 'Photo permission was revoked during backup',
          timestamp: new Date(),
          retryable: true
        }]
      });

      // Other services succeed
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([]);
      jest.spyOn(ContactBackupService, 'backupContacts').mockResolvedValue({
        success: true,
        itemsProcessed: 0,
        errors: []
      });
      jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue([]);
      jest.spyOn(MessageBackupService, 'backupMessages').mockResolvedValue({
        success: true,
        itemsProcessed: 0,
        errors: []
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('permission was revoked'))).toBe(true);
    });
  });

  describe('Network and Connectivity Errors', () => {
    it('should handle network timeout with retry logic', async () => {
      let attemptCount = 0;
      
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async () => {
        attemptCount++;
        if (attemptCount <= 2) {
          throw new Error('Network timeout');
        }
        return { success: true, fileId: 'success_after_retry' };
      });

      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ] as any);

      // Mock retry logic in contact backup
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async (contacts) => {
        const errors: BackupError[] = [];
        let successfulBackups = 0;

        for (const contact of contacts) {
          let success = false;
          let retryCount = 0;
          const maxRetries = 3;

          while (!success && retryCount < maxRetries) {
            try {
              await CloudStorageService.uploadFile('contact_data', `${contact.recordID}.json`, 'application/json', 'contact');
              success = true;
              successfulBackups++;
            } catch (error) {
              retryCount++;
              if (retryCount >= maxRetries) {
                errors.push({
                  id: `contact_retry_error_${contact.recordID}`,
                  type: 'network',
                  message: `Failed to backup contact after ${maxRetries} retries: ${error instanceof Error ? error.message : 'Unknown error'}`,
                  timestamp: new Date(),
                  retryable: true,
                  itemId: contact.recordID
                });
              } else {
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            }
          }
        }

        return {
          success: errors.length === 0,
          itemsProcessed: successfulBackups,
          errors
        };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(1);
      expect(attemptCount).toBe(3); // Should have retried twice
    });

    it('should handle server errors with exponential backoff', async () => {
      const retryDelays: number[] = [];
      let attemptCount = 0;

      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async () => {
        attemptCount++;
        if (attemptCount <= 3) {
          throw new Error('Server error 500');
        }
        return { success: true, fileId: 'success_after_backoff' };
      });

      // Mock exponential backoff in retry service
      jest.spyOn(RetryService, 'executeWithRetry').mockImplementation(async (operation, config) => {
        let lastError: Error | null = null;
        
        for (let attempt = 0; attempt < (config?.maxRetries || 3); attempt++) {
          try {
            return await operation();
          } catch (error) {
            lastError = error as Error;
            
            if (attempt < (config?.maxRetries || 3) - 1) {
              const delay = Math.min(
                (config?.baseDelay || 1000) * Math.pow(2, attempt),
                config?.maxDelay || 10000
              );
              retryDelays.push(delay);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }
        
        throw lastError;
      });

      jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue([
        { id: '1', body: 'Test message', address: '+1234567890', date: Date.now() }
      ] as any);

      jest.spyOn(MessageBackupService, 'backupMessages').mockImplementation(async () => {
        try {
          await RetryService.executeWithRetry(
            () => CloudStorageService.uploadFile('message_data', 'message.json', 'application/json', 'message'),
            { maxRetries: 4, baseDelay: 1000, maxDelay: 8000 }
          );
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'server_error_retry',
              type: 'network',
              message: error instanceof Error ? error.message : 'Server error',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includePhotos: false
      });

      expect(result.success).toBe(true);
      expect(retryDelays.length).toBe(3); // Should have 3 retry delays
      expect(retryDelays[0]).toBe(1000); // First retry: 1s
      expect(retryDelays[1]).toBe(2000); // Second retry: 2s
      expect(retryDelays[2]).toBe(4000); // Third retry: 4s
    });

    it('should handle DNS resolution failures', async () => {
      jest.spyOn(CloudStorageService, 'uploadFile').mockRejectedValue(new Error('DNS resolution failed'));

      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: [
          { uri: 'file://photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now() }
        ],
        excludedVideos: [],
        totalScanned: 1
      });

      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('photo_data', 'photo1.jpg', 'image/jpeg', 'photo');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'dns_error',
              type: 'network',
              message: 'DNS resolution failed - check internet connection',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('DNS resolution failed'))).toBe(true);
    });
  });

  describe('Storage and Quota Errors', () => {
    it('should handle storage quota exceeded error', async () => {
      jest.spyOn(CloudStorageService, 'uploadFile').mockRejectedValue(new Error('Storage quota exceeded'));

      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ] as any);

      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('contact_data', 'contact.json', 'application/json', 'contact');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'quota_exceeded',
              type: 'storage',
              message: 'Storage quota exceeded. Please upgrade your plan or free up space.',
              timestamp: new Date(),
              retryable: false // Not retryable until user takes action
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors[0].type).toBe('storage');
      expect(result.errors[0].retryable).toBe(false);
      expect(NotificationService.showErrorNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'storage',
          message: expect.stringContaining('quota exceeded')
        })
      );
    });

    it('should handle insufficient local storage error', async () => {
      // Mock file system error
      const RNFS = require('react-native-fs');
      jest.spyOn(RNFS, 'readFile').mockRejectedValue(new Error('Insufficient storage space'));

      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: [
          { uri: 'file://photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now() }
        ],
        excludedVideos: [],
        totalScanned: 1
      });

      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async () => {
        try {
          await RNFS.readFile('file://photo1.jpg', 'base64');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'local_storage_error',
              type: 'platform',
              message: 'Insufficient local storage space to process files',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('Insufficient local storage'))).toBe(true);
    });

    it('should handle cloud storage service unavailable', async () => {
      jest.spyOn(CloudStorageService, 'uploadFile').mockRejectedValue(new Error('Service temporarily unavailable'));

      jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue([
        { id: '1', body: 'Test message', address: '+1234567890', date: Date.now() }
      ] as any);

      jest.spyOn(MessageBackupService, 'backupMessages').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('message_data', 'message.json', 'application/json', 'message');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'service_unavailable',
              type: 'network',
              message: 'Cloud storage service temporarily unavailable. Please try again later.',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('temporarily unavailable'))).toBe(true);
    });
  });

  describe('Encryption and Security Errors', () => {
    it('should handle encryption key generation failure', async () => {
      jest.spyOn(EncryptionService, 'encrypt').mockResolvedValue({
        success: false,
        error: 'Failed to generate encryption key'
      });

      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ] as any);

      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async () => {
        const encryptResult = await EncryptionService.encrypt('contact_data');
        if (!encryptResult.success) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'encryption_key_error',
              type: 'encryption',
              message: 'Failed to generate encryption key. Please check device security settings.',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
        return { success: true, itemsProcessed: 1, errors: [] };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors[0].type).toBe('encryption');
      expect(result.errors[0].message).toContain('encryption key');
    });

    it('should handle data corruption during encryption', async () => {
      jest.spyOn(EncryptionService, 'encrypt').mockImplementation(async (data) => {
        if (data.includes('corrupted')) {
          return {
            success: false,
            error: 'Data corruption detected during encryption'
          };
        }
        return {
          success: true,
          data: 'encrypted_data'
        };
      });

      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: [
          { uri: 'file://corrupted_photo.jpg', filename: 'corrupted_photo.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now() }
        ],
        excludedVideos: [],
        totalScanned: 1
      });

      // Mock file read returning corrupted data
      const RNFS = require('react-native-fs');
      jest.spyOn(RNFS, 'readFile').mockResolvedValue('corrupted_data_content');

      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async (photos) => {
        const errors: BackupError[] = [];
        let processedPhotos = 0;

        for (const photo of photos) {
          try {
            const fileData = await RNFS.readFile(photo.uri, 'base64');
            const encryptResult = await EncryptionService.encrypt(fileData);
            
            if (!encryptResult.success) {
              errors.push({
                id: `encryption_error_${photo.filename}`,
                type: 'encryption',
                message: `Failed to encrypt ${photo.filename}: ${encryptResult.error}`,
                timestamp: new Date(),
                retryable: true,
                itemId: photo.filename
              });
            } else {
              processedPhotos++;
            }
          } catch (error) {
            errors.push({
              id: `photo_error_${photo.filename}`,
              type: 'platform',
              message: error instanceof Error ? error.message : 'Unknown error',
              timestamp: new Date(),
              retryable: true,
              itemId: photo.filename
            });
          }
        }

        return {
          success: errors.length === 0,
          itemsProcessed: processedPhotos,
          errors
        };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('corruption detected'))).toBe(true);
    });

    it('should handle encryption service unavailable', async () => {
      jest.spyOn(EncryptionService, 'encrypt').mockRejectedValue(new Error('Encryption service unavailable'));

      jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue([
        { id: '1', body: 'Test message', address: '+1234567890', date: Date.now() }
      ] as any);

      jest.spyOn(MessageBackupService, 'backupMessages').mockImplementation(async () => {
        try {
          await EncryptionService.encrypt('message_data');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'encryption_service_error',
              type: 'encryption',
              message: 'Encryption service unavailable. Please restart the app and try again.',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('Encryption service unavailable'))).toBe(true);
    });
  });

  describe('Platform-Specific Errors', () => {
    it('should handle iOS message backup restriction', async () => {
      (Platform as any).OS = 'ios';

      const result = await BackupManager.startBackup(defaultConfiguration);

      // Should continue with other data types but show iOS message limitation
      expect(result.errors.some(e => 
        e.message.includes('iOS') && 
        e.message.includes('Message') &&
        e.type === 'platform'
      )).toBe(true);
    });

    it('should handle Android permission model changes', async () => {
      (Platform as any).OS = 'android';
      
      // Mock Android 11+ scoped storage restrictions
      mockPermissionsAndroid.request.mockImplementation(async (permission) => {
        if (permission === 'android.permission.READ_EXTERNAL_STORAGE') {
          return 'denied'; // Scoped storage restriction
        }
        return 'granted';
      });

      jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
        contacts: { granted: true, denied: false, blocked: false, unavailable: false },
        sms: { granted: true, denied: false, blocked: false, unavailable: false },
        photos: { granted: false, denied: true, blocked: false, unavailable: false }
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('Missing permissions'))).toBe(true);
    });

    it('should handle device-specific API limitations', async () => {
      // Mock device-specific contact API limitation
      jest.spyOn(ContactBackupService, 'scanContacts').mockRejectedValue(
        new Error('Contact API not available on this device')
      );

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('not available on this device'))).toBe(true);
    });
  });

  describe('Recovery Mechanisms', () => {
    it('should provide retry functionality for failed items', async () => {
      // Mock initial failure then success
      let retryAttempt = 0;
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async () => {
        retryAttempt++;
        if (retryAttempt === 1) {
          throw new Error('Temporary network error');
        }
        return { success: true, fileId: 'retry_success' };
      });

      // Initial backup with failure
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ] as any);

      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('contact_data', 'contact.json', 'application/json', 'contact');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'retry_test_error',
              type: 'network',
              message: 'Temporary network error',
              timestamp: new Date(),
              retryable: true,
              itemId: '1'
            }]
          };
        }
      });

      const initialResult = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(initialResult.success).toBe(false);
      expect(initialResult.errors.some(e => e.retryable)).toBe(true);

      // Reset retry attempt counter for retry operation
      retryAttempt = 0;

      // Test retry functionality
      const retryResult = await BackupManager.retryFailedItems();
      expect(retryResult.success).toBe(true);
    });

    it('should show actionable error notifications with recovery options', async () => {
      jest.spyOn(CloudStorageService, 'uploadFile').mockRejectedValue(new Error('Storage quota exceeded'));

      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: [
          { uri: 'file://photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now() }
        ],
        excludedVideos: [],
        totalScanned: 1
      });

      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('photo_data', 'photo1.jpg', 'image/jpeg', 'photo');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'quota_error_with_actions',
              type: 'storage',
              message: 'Storage quota exceeded',
              timestamp: new Date(),
              retryable: false
            }]
          };
        }
      });

      await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(NotificationService.showErrorNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'storage',
          message: expect.stringContaining('quota exceeded')
        })
      );
    });

    it('should handle partial recovery scenarios', async () => {
      const mockContacts = [
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] },
        { recordID: '2', displayName: 'Jane Smith', phoneNumbers: [], emailAddresses: [] },
        { recordID: '3', displayName: 'Bob Wilson', phoneNumbers: [], emailAddresses: [] }
      ];

      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);

      // Mock partial failure - first contact fails, others succeed
      let uploadAttempt = 0;
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async (data, filename) => {
        uploadAttempt++;
        if (filename.includes('1.json')) {
          throw new Error('Network error for contact 1');
        }
        return { success: true, fileId: `success_${uploadAttempt}` };
      });

      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async (contacts) => {
        const errors: BackupError[] = [];
        let successfulBackups = 0;

        for (const contact of contacts) {
          try {
            await CloudStorageService.uploadFile('contact_data', `${contact.recordID}.json`, 'application/json', 'contact');
            successfulBackups++;
          } catch (error) {
            errors.push({
              id: `contact_error_${contact.recordID}`,
              type: 'network',
              message: error instanceof Error ? error.message : 'Unknown error',
              timestamp: new Date(),
              retryable: true,
              itemId: contact.recordID
            });
          }
        }

        return {
          success: errors.length === 0,
          itemsProcessed: successfulBackups,
          errors
        };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.itemsProcessed).toBe(2); // 2 out of 3 contacts succeeded
      expect(result.errors).toHaveLength(1); // 1 contact failed
      expect(result.errors[0].retryable).toBe(true);
    });

    it('should provide detailed error context for troubleshooting', async () => {
      jest.spyOn(EncryptionService, 'encrypt').mockResolvedValue({
        success: false,
        error: 'Key derivation failed: insufficient entropy'
      });

      jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue([
        { id: '1', body: 'Test message', address: '+1234567890', date: Date.now() }
      ] as any);

      jest.spyOn(MessageBackupService, 'backupMessages').mockImplementation(async () => {
        const encryptResult = await EncryptionService.encrypt('message_data');
        if (!encryptResult.success) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'detailed_encryption_error',
              type: 'encryption',
              message: `Encryption failed: ${encryptResult.error}`,
              timestamp: new Date(),
              retryable: true,
              context: {
                errorCode: 'ENCRYPTION_KEY_DERIVATION_FAILED',
                deviceInfo: {
                  platform: Platform.OS,
                  hasSecureHardware: true
                },
                troubleshooting: [
                  'Check device lock screen security',
                  'Restart the app',
                  'Reboot the device'
                ]
              }
            }]
          };
        }
        return { success: true, itemsProcessed: 1, errors: [] };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors[0].context).toBeDefined();
      expect(result.errors[0].context?.troubleshooting).toContain('Check device lock screen security');
    });
  });

  describe('Error Aggregation and Reporting', () => {
    it('should aggregate errors across multiple data types', async () => {
      // Mock failures in all data types
      jest.spyOn(ContactBackupService, 'scanContacts').mockRejectedValue(new Error('Contact service error'));
      jest.spyOn(MessageBackupService, 'scanMessages').mockRejectedValue(new Error('Message service error'));
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockRejectedValue(new Error('Photo service error'));

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBe(3); // One error per data type
      expect(result.errors.some(e => e.message.includes('Contact service'))).toBe(true);
      expect(result.errors.some(e => e.message.includes('Message service'))).toBe(true);
      expect(result.errors.some(e => e.message.includes('Photo service'))).toBe(true);
    });

    it('should provide error summary for user reporting', async () => {
      // Mock various error types
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ] as any);

      jest.spyOn(ContactBackupService, 'backupContacts').mockResolvedValue({
        success: false,
        itemsProcessed: 0,
        errors: [
          {
            id: 'network_error_1',
            type: 'network',
            message: 'Network timeout',
            timestamp: new Date(),
            retryable: true
          },
          {
            id: 'permission_error_1',
            type: 'permission',
            message: 'Permission denied',
            timestamp: new Date(),
            retryable: true
          }
        ]
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      
      // Should have error summary
      const errorTypes = result.errors.map(e => e.type);
      expect(errorTypes).toContain('network');
      expect(errorTypes).toContain('permission');
      
      const retryableErrors = result.errors.filter(e => e.retryable);
      expect(retryableErrors.length).toBe(2);
    });
  });
});