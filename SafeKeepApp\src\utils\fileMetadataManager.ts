// File Metadata Manager for SafeKeep
// Handles tracking and organizing file metadata in Supabase

import { supabase } from '../config/supabase';
import { getCurrentUserId, handleSupabaseError } from './supabaseHelpers';
import { PostgrestError } from '@supabase/supabase-js';
import { generateFileHash } from './fileDeduplication';

export interface FileMetadata {
  id?: string;
  user_id: string;
  original_name: string;
  encrypted_name: string;
  mime_type: string;
  size: number;
  encrypted_size: number;
  category: 'photo' | 'contact' | 'message';
  hash: string;
  encryption_iv: string;
  encryption_salt: string;
  storage_path: string;
  is_backed_up?: boolean;
  device_id?: string;
  uploaded_at?: string;
  last_modified?: string;
  sync_status?: 'synced' | 'pending' | 'failed';
  tags?: string[];
  description?: string;
  favorite?: boolean;
}

export interface FileSearchParams {
  category?: 'photo' | 'contact' | 'message';
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
  sortBy?: 'uploaded_at' | 'original_name' | 'size';
  sortDirection?: 'asc' | 'desc';
}

export interface FileOrganization {
  folders: string[];
  tags: string[];
}

/**
 * Create a new file metadata entry with deduplication check
 * If a file with the same hash already exists, returns the existing metadata
 */
export const createFileMetadata = async (metadata: Omit<FileMetadata, 'id'>): Promise<{ 
  id: string | null; 
  error: string | null;
  isDuplicate: boolean;
  existingMetadata?: FileMetadata;
}> => {
  try {
    // Check if file with same hash already exists (deduplication)
    const { exists, metadata: existingFile } = await checkFileExists(metadata.hash);
    
    if (exists && existingFile) {
      console.log(`File with hash ${metadata.hash} already exists, skipping upload`);
      return { 
        id: existingFile.id || null, 
        error: null, 
        isDuplicate: true,
        existingMetadata: existingFile
      };
    }
    
    // No duplicate found, create new metadata entry
    const { data, error } = await supabase
      .from('file_metadata')
      .insert(metadata)
      .select('id')
      .single();

    if (error) {
      console.error('Error creating file metadata:', error);
      return { id: null, error: error.message, isDuplicate: false };
    }

    return { id: data.id, error: null, isDuplicate: false };
  } catch (error) {
    console.error('Exception creating file metadata:', error);
    return { id: null, error: handleSupabaseError(error as Error, 'createFileMetadata'), isDuplicate: false };
  }
};

/**
 * Get file metadata by ID
 */
export const getFileMetadata = async (fileId: string): Promise<{ data: FileMetadata | null; error: string | null }> => {
  try {
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('id', fileId)
      .single();

    if (error) {
      return { data: null, error: error.message };
    }

    return { data: data as FileMetadata, error: null };
  } catch (error) {
    return { data: null, error: handleSupabaseError(error as Error, 'getFileMetadata') };
  }
};

/**
 * Search for files with advanced filtering and sorting options
 */
export const searchFiles = async (params: FileSearchParams): Promise<{ 
  data: FileMetadata[]; 
  totalCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], totalCount: 0, error: 'User not authenticated' };
    }

    // First get total count for pagination info
    const countQuery = supabase
      .from('file_metadata')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
      
    // Apply the same filters to count query
    if (params.category) {
      countQuery.eq('category', params.category);
    }
    
    if (params.startDate) {
      countQuery.gte('uploaded_at', params.startDate.toISOString());
    }
    
    if (params.endDate) {
      countQuery.lte('uploaded_at', params.endDate.toISOString());
    }
    
    if (params.searchTerm) {
      countQuery.or(`original_name.ilike.%${params.searchTerm}%,storage_path.ilike.%${params.searchTerm}%`);
    }
    
    if (params.tags && params.tags.length > 0) {
      // Search for files that contain any of the specified tags
      countQuery.contains('tags', params.tags);
    }
    
    const { count: totalCount, error: countError } = await countQuery;
    
    if (countError) {
      return { data: [], totalCount: 0, error: countError.message };
    }
    
    // Now build the main query
    let query = supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId);

    // Apply filters
    if (params.category) {
      query = query.eq('category', params.category);
    }

    if (params.startDate) {
      query = query.gte('uploaded_at', params.startDate.toISOString());
    }

    if (params.endDate) {
      query = query.lte('uploaded_at', params.endDate.toISOString());
    }

    if (params.searchTerm) {
      // Search in both original name and storage path
      query = query.or(`original_name.ilike.%${params.searchTerm}%,storage_path.ilike.%${params.searchTerm}%`);
    }
    
    if (params.tags && params.tags.length > 0) {
      // Search for files that contain any of the specified tags
      query = query.contains('tags', params.tags);
    }

    // Apply sorting
    const sortField = params.sortBy || 'uploaded_at';
    const sortDirection = params.sortDirection === 'asc' ? true : false;
    query = query.order(sortField, { ascending: sortDirection });

    // Apply pagination
    if (params.limit) {
      query = query.limit(params.limit);
    }

    if (params.offset) {
      query = query.range(params.offset, params.offset + (params.limit || 20) - 1);
    }

    const { data, error } = await query;

    if (error) {
      return { data: [], totalCount: 0, error: error.message };
    }

    return { 
      data: data as FileMetadata[], 
      totalCount: totalCount || 0,
      error: null 
    };
  } catch (error) {
    console.error('Error searching files:', error);
    return { data: [], totalCount: 0, error: handleSupabaseError(error as Error, 'searchFiles') };
  }
};

/**
 * Check if a file with the same hash already exists
 */
export const checkFileExists = async (hash: string): Promise<{ exists: boolean; metadata?: FileMetadata }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { exists: false };
    }

    const { data } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .eq('hash', hash)
      .single();

    return {
      exists: !!data,
      metadata: data as FileMetadata
    };
  } catch (error) {
    return { exists: false };
  }
};

/**
 * Delete file metadata
 */
export const deleteFileMetadata = async (fileId: string): Promise<{ success: boolean; error: string | null }> => {
  try {
    const { error } = await supabase
      .from('file_metadata')
      .delete()
      .eq('id', fileId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: handleSupabaseError(error as Error, 'deleteFileMetadata') };
  }
};

/**
 * Get file count by category
 */
export const getFileCounts = async (): Promise<{ 
  photos: number; 
  contacts: number; 
  messages: number; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { photos: 0, contacts: 0, messages: 0, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('file_metadata')
      .select('category, count')
      .eq('user_id', userId)
      .group('category');

    if (error) {
      return { photos: 0, contacts: 0, messages: 0, error: error.message };
    }

    const counts = {
      photos: 0,
      contacts: 0,
      messages: 0,
      error: null
    };

    data.forEach((item: { category: string; count: number }) => {
      if (item.category === 'photo') counts.photos = item.count;
      if (item.category === 'contact') counts.contacts = item.count;
      if (item.category === 'message') counts.messages = item.count;
    });

    return counts;
  } catch (error) {
    return { photos: 0, contacts: 0, messages: 0, error: (error as Error).message };
  }
};

/**
 * Update file metadata
 */
export const updateFileMetadata = async (
  fileId: string, 
  updates: Partial<FileMetadata>
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const { error } = await supabase
      .from('file_metadata')
      .update(updates)
      .eq('id', fileId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Add tags to a file
 */
export const addTagsToFile = async (
  fileId: string,
  tags: string[]
): Promise<{ success: boolean; error: string | null }> => {
  try {
    // First get current tags
    const { data: fileData, error: fetchError } = await supabase
      .from('file_metadata')
      .select('tags')
      .eq('id', fileId)
      .single();

    if (fetchError) {
      return { success: false, error: fetchError.message };
    }

    // Merge existing tags with new ones, removing duplicates
    const currentTags = fileData.tags || [];
    const updatedTags = [...new Set([...currentTags, ...tags])];

    // Update the file with new tags
    const { error } = await supabase
      .from('file_metadata')
      .update({ tags: updatedTags })
      .eq('id', fileId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error adding tags:', error);
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Remove tags from a file
 */
export const removeTagsFromFile = async (
  fileId: string,
  tagsToRemove: string[]
): Promise<{ success: boolean; error: string | null }> => {
  try {
    // First get current tags
    const { data: fileData, error: fetchError } = await supabase
      .from('file_metadata')
      .select('tags')
      .eq('id', fileId)
      .single();

    if (fetchError) {
      return { success: false, error: fetchError.message };
    }

    // Filter out tags to remove
    const currentTags = fileData.tags || [];
    const updatedTags = currentTags.filter(tag => !tagsToRemove.includes(tag));

    // Update the file with new tags
    const { error } = await supabase
      .from('file_metadata')
      .update({ tags: updatedTags })
      .eq('id', fileId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error removing tags:', error);
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Get all unique tags used by the current user
 */
export const getUserTags = async (): Promise<{ tags: string[]; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { tags: [], error: 'User not authenticated' };
    }

    // Query all file metadata to extract unique tags
    const { data, error } = await supabase
      .from('file_metadata')
      .select('tags')
      .eq('user_id', userId)
      .not('tags', 'is', null);

    if (error) {
      return { tags: [], error: error.message };
    }

    // Extract and flatten all tags, then remove duplicates
    const allTags = data.flatMap((item: { tags: string[] | null }) => item.tags || []);
    const uniqueTags = [...new Set(allTags)];

    return { tags: uniqueTags, error: null };
  } catch (error) {
    console.error('Error getting user tags:', error);
    return { tags: [], error: (error as Error).message };
  }
};

/**
 * Get file organization structure (virtual folders and tags)
 */
export const getFileOrganization = async (): Promise<{ 
  organization: FileOrganization; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        organization: { folders: [], tags: [] }, 
        error: 'User not authenticated' 
      };
    }

    // Get all unique tags
    const { tags, error: tagsError } = await getUserTags();
    if (tagsError) {
      return { 
        organization: { folders: [], tags: [] }, 
        error: tagsError 
      };
    }

    // Try to use the database function first
    try {
      const { data: folderData, error: rpcError } = await supabase.rpc('get_virtual_folders', {
        user_id_param: userId
      });

      if (!rpcError && folderData) {
        // Extract just the folder paths from the RPC result
        const folders = folderData.map((folder: { path: string }) => folder.path);
        return { 
          organization: { 
            folders, 
            tags 
          }, 
          error: null 
        };
      }
    } catch (rpcError) {
      console.warn('RPC function not available, using client-side implementation:', rpcError);
    }

    // Extract virtual folders from storage paths
    const { data: files, error: filesError } = await supabase
      .from('file_metadata')
      .select('storage_path')
      .eq('user_id', userId);

    if (filesError) {
      return { 
        organization: { folders: [], tags }, 
        error: filesError.message 
      };
    }

    // Extract folder paths from storage paths
    const folderPaths = files.map((file: { storage_path: string }) => {
      const pathParts = file.storage_path.split('/');
      // Remove the filename (last part) and user ID (first part)
      return pathParts.slice(1, -1).join('/');
    });

    // Remove duplicates
    const uniqueFolders = [...new Set(folderPaths)].filter(Boolean);

    return { 
      organization: { 
        folders: uniqueFolders, 
        tags 
      }, 
      error: null 
    };
  } catch (error) {
    console.error('Error getting file organization:', error);
    return { 
      organization: { folders: [], tags: [] }, 
      error: (error as Error).message 
    };
  }
};

/**
 * Batch update multiple files with the same tags
 */
export const batchUpdateFileTags = async (
  fileIds: string[],
  tags: string[],
  operation: 'add' | 'remove' | 'set'
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    if (operation === 'set') {
      // Simply set the tags for all files
      const { error } = await supabase
        .from('file_metadata')
        .update({ tags })
        .in('id', fileIds)
        .eq('user_id', userId); // Security check

      if (error) {
        return { success: false, error: error.message };
      }
    } else {
      // For add or remove, we need to process each file individually
      // to preserve existing tags
      for (const fileId of fileIds) {
        const { data: fileData, error: fetchError } = await supabase
          .from('file_metadata')
          .select('tags')
          .eq('id', fileId)
          .eq('user_id', userId) // Security check
          .single();

        if (fetchError) {
          console.error(`Error fetching tags for file ${fileId}:`, fetchError);
          continue;
        }

        const currentTags = fileData.tags || [];
        let updatedTags: string[];

        if (operation === 'add') {
          // Add new tags, avoiding duplicates
          updatedTags = [...new Set([...currentTags, ...tags])];
        } else { // remove
          // Remove specified tags
          updatedTags = currentTags.filter(tag => !tags.includes(tag));
        }

        // Update the file
        const { error: updateError } = await supabase
          .from('file_metadata')
          .update({ tags: updatedTags })
          .eq('id', fileId)
          .eq('user_id', userId); // Security check

        if (updateError) {
          console.error(`Error updating tags for file ${fileId}:`, updateError);
        }
      }
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in batch update file tags:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Find duplicate files based on hash comparison
 */
export const findDuplicateFiles = async (): Promise<{ 
  duplicates: { hash: string; files: FileMetadata[] }[]; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { duplicates: [], error: 'User not authenticated' };
    }

    // Find files with duplicate hashes
    const { data, error } = await supabase.rpc('find_duplicate_files', {
      user_id_param: userId
    });

    if (error) {
      // If the RPC function doesn't exist, fall back to client-side implementation
      console.warn('RPC function not available, using client-side implementation:', error);
      
      // Get all files
      const { data: files, error: filesError } = await supabase
        .from('file_metadata')
        .select('*')
        .eq('user_id', userId);
        
      if (filesError) {
        return { duplicates: [], error: filesError.message };
      }
      
      // Group files by hash
      const filesByHash: Record<string, FileMetadata[]> = {};
      files.forEach((file: any) => {
        if (!filesByHash[file.hash]) {
          filesByHash[file.hash] = [];
        }
        filesByHash[file.hash].push(file as FileMetadata);
      });
      
      // Filter to only include hashes with multiple files
      const duplicates = Object.entries(filesByHash)
        .filter(([_, files]) => files.length > 1)
        .map(([hash, files]) => ({ hash, files }));
        
      return { duplicates, error: null };
    }

    return { duplicates: data as { hash: string; files: FileMetadata[] }[], error: null };
  } catch (error) {
    console.error('Error finding duplicate files:', error);
    return { duplicates: [], error: (error as Error).message };
  }
};

/**
 * Get file statistics by category and date range
 */
export const getFileStatistics = async (
  startDate?: Date,
  endDate?: Date
): Promise<{ 
  totalFiles: number;
  totalSize: number;
  byCategory: Record<string, { count: number; size: number }>;
  byDate: Record<string, { count: number; size: number }>;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        totalFiles: 0, 
        totalSize: 0, 
        byCategory: {}, 
        byDate: {}, 
        error: 'User not authenticated' 
      };
    }

    let query = supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId);

    if (startDate) {
      query = query.gte('uploaded_at', startDate.toISOString());
    }

    if (endDate) {
      query = query.lte('uploaded_at', endDate.toISOString());
    }

    const { data, error } = await query;

    if (error) {
      return { 
        totalFiles: 0, 
        totalSize: 0, 
        byCategory: {}, 
        byDate: {}, 
        error: error.message 
      };
    }

    // Calculate statistics
    const totalFiles = data.length;
    const totalSize = data.reduce((sum: number, file: any) => sum + file.size, 0);

    // Group by category
    const byCategory: Record<string, { count: number; size: number }> = {};
    data.forEach((file: any) => {
      if (!byCategory[file.category]) {
        byCategory[file.category] = { count: 0, size: 0 };
      }
      byCategory[file.category].count++;
      byCategory[file.category].size += file.size;
    });

    // Group by date (YYYY-MM-DD)
    const byDate: Record<string, { count: number; size: number }> = {};
    data.forEach((file: any) => {
      const date = new Date(file.uploaded_at).toISOString().split('T')[0];
      if (!byDate[date]) {
        byDate[date] = { count: 0, size: 0 };
      }
      byDate[date].count++;
      byDate[date].size += file.size;
    });

    return {
      totalFiles,
      totalSize,
      byCategory,
      byDate,
      error: null
    };
  } catch (error) {
    console.error('Error getting file statistics:', error);
    return { 
      totalFiles: 0, 
      totalSize: 0, 
      byCategory: {}, 
      byDate: {}, 
      error: error.message 
    };
  }
};/**
 * C
reate file metadata from a file object
 * This is a helper function that generates metadata from a file and its encrypted version
 */
export const createFileMetadataFromFile = async (
  file: File | Blob,
  encryptedFile: Blob,
  category: 'photo' | 'contact' | 'message',
  encryptionDetails: {
    iv: string;
    salt: string;
  },
  storagePath: string
): Promise<{ 
  metadata: Omit<FileMetadata, 'id'> | null; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { metadata: null, error: 'User not authenticated' };
    }

    // Generate file hash for deduplication
    const fileBuffer = await file.arrayBuffer();
    const hash = await generateFileHash(fileBuffer);

    // Create metadata object
    const metadata: Omit<FileMetadata, 'id'> = {
      user_id: userId,
      original_name: 'file' in file ? file.name : 'unknown',
      encrypted_name: `${hash.substring(0, 8)}_${Date.now()}`,
      mime_type: file.type || 'application/octet-stream',
      size: file.size,
      encrypted_size: encryptedFile.size,
      category,
      hash,
      encryption_iv: encryptionDetails.iv,
      encryption_salt: encryptionDetails.salt,
      storage_path: storagePath,
      is_backed_up: true,
      device_id: `device_${Date.now()}`, // Replace with actual device ID in production
      uploaded_at: new Date().toISOString(),
      last_modified: 'lastModified' in file ? new Date(file.lastModified).toISOString() : new Date().toISOString(),
      sync_status: 'synced',
      tags: []
    };

    return { metadata, error: null };
  } catch (error) {
    console.error('Error creating file metadata from file:', error);
    return { metadata: null, error: (error as Error).message };
  }
};

/**
 * Bulk create file metadata for multiple files
 */
export const bulkCreateFileMetadata = async (
  metadataList: Omit<FileMetadata, 'id'>[]
): Promise<{ 
  results: { id: string | null; isDuplicate: boolean }[]; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        results: metadataList.map(() => ({ id: null, isDuplicate: false })), 
        error: 'User not authenticated' 
      };
    }

    const results: { id: string | null; isDuplicate: boolean }[] = [];

    // Process files in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < metadataList.length; i += batchSize) {
      const batch = metadataList.slice(i, i + batchSize);
      
      // Process each file in the batch
      const batchPromises = batch.map(async (metadata) => {
        // Verify user ID for security
        if (metadata.user_id !== userId) {
          return { id: null, isDuplicate: false };
        }
        
        // Check for duplicates
        const { exists, metadata: existingFile } = await checkFileExists(metadata.hash);
        
        if (exists && existingFile) {
          return { id: existingFile.id || null, isDuplicate: true };
        }
        
        // Create new metadata
        const { data, error } = await supabase
          .from('file_metadata')
          .insert(metadata)
          .select('id')
          .single();
          
        if (error) {
          console.error('Error creating file metadata:', error);
          return { id: null, isDuplicate: false };
        }
        
        return { id: data.id, isDuplicate: false };
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return { results, error: null };
  } catch (error) {
    console.error('Error in bulk create file metadata:', error);
    return { 
      results: metadataList.map(() => ({ id: null, isDuplicate: false })), 
      error: (error as Error).message 
    };
  }
};

/**
 * Get files by category with pagination
 */
export const getFilesByCategory = async (
  category: 'photo' | 'contact' | 'message',
  limit: number = 20,
  offset: number = 0,
  sortBy: 'uploaded_at' | 'original_name' | 'size' = 'uploaded_at',
  sortDirection: 'asc' | 'desc' = 'desc'
): Promise<{ 
  data: FileMetadata[]; 
  totalCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], totalCount: 0, error: 'User not authenticated' };
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('file_metadata')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('category', category);
      
    if (countError) {
      return { data: [], totalCount: 0, error: countError.message };
    }
    
    // Get files with pagination
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .eq('category', category)
      .order(sortBy, { ascending: sortDirection === 'asc' })
      .range(offset, offset + limit - 1);
      
    if (error) {
      return { data: [], totalCount: 0, error: error.message };
    }
    
    return { 
      data: data as FileMetadata[], 
      totalCount: count || 0, 
      error: null 
    };
  } catch (error) {
    console.error('Error getting files by category:', error);
    return { data: [], totalCount: 0, error: (error as Error).message };
  }
};

/**
 * Get favorite files
 */
export const getFavoriteFiles = async (
  limit: number = 20,
  offset: number = 0
): Promise<{ 
  data: FileMetadata[]; 
  totalCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], totalCount: 0, error: 'User not authenticated' };
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('file_metadata')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('favorite', true);
      
    if (countError) {
      return { data: [], totalCount: 0, error: countError.message };
    }
    
    // Get favorite files with pagination
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .eq('favorite', true)
      .order('uploaded_at', { ascending: false })
      .range(offset, offset + limit - 1);
      
    if (error) {
      return { data: [], totalCount: 0, error: error.message };
    }
    
    return { 
      data: data as FileMetadata[], 
      totalCount: count || 0, 
      error: null 
    };
  } catch (error) {
    console.error('Error getting favorite files:', error);
    return { data: [], totalCount: 0, error: (error as Error).message };
  }
};

/**
 * Toggle favorite status for a file
 */
export const toggleFavorite = async (
  fileId: string,
  isFavorite: boolean
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    const { error } = await supabase
      .from('file_metadata')
      .update({ favorite: isFavorite })
      .eq('id', fileId)
      .eq('user_id', userId); // Security check
      
    if (error) {
      return { success: false, error: error.message };
    }
    
    return { success: true, error: null };
  } catch (error) {
    console.error('Error toggling favorite status:', error);
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Get recently added files
 */
export const getRecentFiles = async (
  limit: number = 20
): Promise<{ 
  data: FileMetadata[]; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], error: 'User not authenticated' };
    }
    
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .order('uploaded_at', { ascending: false })
      .limit(limit);
      
    if (error) {
      return { data: [], error: error.message };
    }
    
    return { data: data as FileMetadata[], error: null };
  } catch (error) {
    console.error('Error getting recent files:', error);
    return { data: [], error: (error as Error).message };
  }
};

/**
 * Update file description
 */
export const updateFileDescription = async (
  fileId: string,
  description: string
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    const { error } = await supabase
      .from('file_metadata')
      .update({ description })
      .eq('id', fileId)
      .eq('user_id', userId); // Security check
      
    if (error) {
      return { success: false, error: error.message };
    }
    
    return { success: true, error: null };
  } catch (error) {
    console.error('Error updating file description:', error);
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Batch delete multiple files
 */
export const batchDeleteFiles = async (
  fileIds: string[]
): Promise<{ 
  success: boolean; 
  deletedCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, deletedCount: 0, error: 'User not authenticated' };
    }

    // Delete files in batches to avoid overwhelming the database
    let deletedCount = 0;
    const batchSize = 10;
    
    for (let i = 0; i < fileIds.length; i += batchSize) {
      const batch = fileIds.slice(i, i + batchSize);
      
      // Delete the batch
      const { data, error } = await supabase
        .from('file_metadata')
        .delete()
        .in('id', batch)
        .eq('user_id', userId) // Security check
        .select('id');
        
      if (error) {
        console.error('Error deleting files batch:', error);
        continue;
      }
      
      deletedCount += data?.length || 0;
    }
    
    return { 
      success: deletedCount > 0, 
      deletedCount, 
      error: deletedCount === fileIds.length ? null : `Only deleted ${deletedCount} of ${fileIds.length} files` 
    };
  } catch (error) {
    console.error('Error in batch delete files:', error);
    return { success: false, deletedCount: 0, error: (error as Error).message };
  }
};

/**
 * Get files with sync issues
 */
export const getFilesWithSyncIssues = async (): Promise<{ 
  data: FileMetadata[]; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], error: 'User not authenticated' };
    }
    
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .eq('sync_status', 'failed');
      
    if (error) {
      return { data: [], error: error.message };
    }
    
    return { data: data as FileMetadata[], error: null };
  } catch (error) {
    console.error('Error getting files with sync issues:', error);
    return { data: [], error: (error as Error).message };
  }
};

/**
 * Retry syncing files with sync issues
 */
export const retrySyncFailedFiles = async (): Promise<{ 
  success: boolean; 
  retriedCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, retriedCount: 0, error: 'User not authenticated' };
    }
    
    // Get files with sync issues
    const { data: failedFiles, error: fetchError } = await supabase
      .from('file_metadata')
      .select('id')
      .eq('user_id', userId)
      .eq('sync_status', 'failed');
      
    if (fetchError) {
      return { success: false, retriedCount: 0, error: fetchError.message };
    }
    
    if (!failedFiles || failedFiles.length === 0) {
      return { success: true, retriedCount: 0, error: null };
    }
    
    // Update sync status to pending for retry
    const { data, error } = await supabase
      .from('file_metadata')
      .update({ sync_status: 'pending' })
      .in('id', failedFiles.map(file => file.id))
      .eq('user_id', userId); // Security check
      
    if (error) {
      return { success: false, retriedCount: 0, error: error.message };
    }
    
    return { 
      success: true, 
      retriedCount: failedFiles.length, 
      error: null 
    };
  } catch (error) {
    console.error('Error retrying failed syncs:', error);
    return { success: false, retriedCount: 0, error: (error as Error).message };
  }
};