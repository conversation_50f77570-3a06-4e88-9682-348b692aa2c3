/**
 * Performance Analytics Integration - Main integration file for performance monitoring system
 * Integrates all performance monitoring components and provides unified interface
 */

class PerformanceAnalyticsIntegration {
    constructor() {
        this.performanceMonitor = null;
        this.analyticsDashboard = null;
        this.heatmapTracker = null;
        this.abTestingFramework = null;
        this.performanceOptimizer = null;
        
        this.isInitialized = false;
        this.config = {
            enableRealTimeUpdates: true,
            enableHeatmapTracking: true,
            enableABTesting: true,
            enableOptimizationRecommendations: true,
            updateInterval: 5000, // 5 seconds
            autoAnalyzeInterval: 30000 // 30 seconds
        };
        
        this.intervals = {
            realTimeUpdate: null,
            autoAnalyze: null
        };
    }

    async init() {
        try {
            console.log('Initializing Performance Analytics Integration...');
            
            // Initialize core performance monitor
            this.performanceMonitor = new PerformanceMonitor();
            
            // Initialize heatmap tracker
            if (this.config.enableHeatmapTracking) {
                this.heatmapTracker = new HeatmapTracker(this.performanceMonitor);
            }
            
            // Initialize A/B testing framework
            if (this.config.enableABTesting) {
                this.abTestingFramework = new ABTestingFramework(this.performanceMonitor);
            }
            
            // Initialize performance optimizer
            if (this.config.enableOptimizationRecommendations) {
                this.performanceOptimizer = new PerformanceOptimizer(this.performanceMonitor);
            }
            
            // Initialize analytics dashboard
            this.analyticsDashboard = new AnalyticsDashboard(this.performanceMonitor);
            
            // Setup integration points
            this.setupIntegrationPoints();
            
            // Start monitoring
            this.startMonitoring();
            
            // Setup UI controls
            this.setupUIControls();
            
            this.isInitialized = true;
            console.log('Performance Analytics Integration initialized successfully');
            
            // Initial analysis
            this.runPerformanceAnalysis();
            
        } catch (error) {
            console.error('Failed to initialize Performance Analytics Integration:', error);
            throw error;
        }
    }

    setupIntegrationPoints() {
        // Integrate with existing backup system
        this.integrateWithBackupSystem();
        
        // Integrate with existing UI components
        this.integrateWithUIComponents();
        
        // Setup cross-component communication
        this.setupCrossComponentCommunication();
    }

    integrateWithBackupSystem() {
        // Hook into existing backup manager if available
        if (window.backupManager) {
            const originalStartBackup = window.backupManager.startBackup;
            window.backupManager.startBackup = (...args) => {
                const backupId = 'backup_' + Date.now();
                const trackingData = this.performanceMonitor.startBackupPerformanceTracking(
                    backupId, 
                    args[0] || 'manual'
                );
                
                // Track A/B test events
                if (this.abTestingFramework) {
                    this.abTestingFramework.trackEvent('backup_button_color', null, 'backup_started');
                }
                
                const result = originalStartBackup.apply(window.backupManager, args);
                
                // If backup returns a promise, track completion
                if (result && typeof result.then === 'function') {
                    result.then(
                        (success) => {
                            this.performanceMonitor.endBackupPerformanceTracking(
                                trackingData, 
                                true, 
                                success.filesProcessed || 0, 
                                success.totalSize || 0
                            );
                        },
                        (error) => {
                            this.performanceMonitor.endBackupPerformanceTracking(
                                trackingData, 
                                false, 
                                0, 
                                0
                            );
                            this.performanceMonitor.recordError(error, { context: 'backup_failure' });
                        }
                    );
                }
                
                return result;
            };
        }
    }

    integrateWithUIComponents() {
        // Apply A/B test variants to UI elements
        if (this.abTestingFramework) {
            this.applyABTestVariants();
        }
        
        // Setup performance tracking for UI interactions
        this.setupUIPerformanceTracking();
    }

    applyABTestVariants() {
        // Apply backup button color variant
        const backupButtons = document.querySelectorAll('.backup-btn, #start-backup, [data-action="backup"]');
        backupButtons.forEach(button => {
            const variant = this.abTestingFramework.getVariant('backup_button_color');
            const config = this.abTestingFramework.getVariantConfig('backup_button_color');
            
            if (config && config.color) {
                button.style.backgroundColor = config.color;
                button.setAttribute('data-ab-variant', variant);
                
                // Track clicks on this variant
                button.addEventListener('click', () => {
                    this.abTestingFramework.trackClick('backup_button_color', button);
                });
            }
        });
        
        // Apply progress bar style variant
        const progressBars = document.querySelectorAll('.progress-bar, .backup-progress');
        progressBars.forEach(progressBar => {
            const variant = this.abTestingFramework.getVariant('progress_bar_style');
            const config = this.abTestingFramework.getVariantConfig('progress_bar_style');
            
            if (config) {
                progressBar.classList.add(`progress-${config.style}`);
                progressBar.setAttribute('data-ab-variant', variant);
            }
        });
        
        // Apply dashboard layout variant
        const dashboard = document.querySelector('.dashboard, .main-dashboard');
        if (dashboard) {
            const variant = this.abTestingFramework.getVariant('dashboard_layout');
            const config = this.abTestingFramework.getVariantConfig('dashboard_layout');
            
            if (config) {
                dashboard.classList.add(`layout-${config.layout}`);
                dashboard.setAttribute('data-ab-variant', variant);
            }
        }
    }

    setupUIPerformanceTracking() {
        // Track form interactions
        document.addEventListener('submit', (event) => {
            const formId = event.target.id || 'unknown_form';
            performance.mark(`form_submit_${formId}_start`);
            
            // Track A/B test conversion if applicable
            if (this.abTestingFramework) {
                this.abTestingFramework.trackConversion('backup_button_color', 'form_submission');
            }
        });
        
        // Track navigation performance
        document.addEventListener('click', (event) => {
            if (event.target.matches('a, button, [role="button"]')) {
                const elementText = event.target.textContent?.trim().substring(0, 20) || 'unknown';
                performance.mark(`interaction_${elementText}_${Date.now()}`);
            }
        });
    }

    setupCrossComponentCommunication() {
        // Setup event system for cross-component communication
        this.eventBus = new EventTarget();
        
        // Performance threshold alerts
        if (this.performanceOptimizer) {
            setInterval(() => {
                const recommendations = this.performanceOptimizer.analyzePerformance();
                const highPriorityRecs = recommendations.filter(r => r.severity === 'high');
                
                if (highPriorityRecs.length > 0) {
                    this.eventBus.dispatchEvent(new CustomEvent('highPriorityRecommendations', {
                        detail: { recommendations: highPriorityRecs }
                    }));
                }
            }, this.config.autoAnalyzeInterval);
        }
        
        // A/B test result notifications
        if (this.abTestingFramework) {
            setInterval(() => {
                const results = this.abTestingFramework.getAllTestResults();
                Object.entries(results).forEach(([testName, result]) => {
                    const winner = this.abTestingFramework.getWinningVariant(testName);
                    if (winner && winner.confidence > 80) {
                        this.eventBus.dispatchEvent(new CustomEvent('abTestWinner', {
                            detail: { testName, winner, result }
                        }));
                    }
                });
            }, 60000); // Check every minute
        }
    }

    setupUIControls() {
        // Create performance analytics toggle button
        const toggleButton = document.createElement('button');
        toggleButton.id = 'performance-analytics-toggle';
        toggleButton.className = 'performance-toggle-btn';
        toggleButton.innerHTML = '📊 Analytics';
        toggleButton.title = 'Toggle Performance Analytics Dashboard';
        
        toggleButton.addEventListener('click', () => {
            this.toggleAnalyticsDashboard();
        });
        
        // Add to page (try multiple locations)
        const targetContainers = [
            document.querySelector('.header-controls'),
            document.querySelector('.toolbar'),
            document.querySelector('header'),
            document.body
        ];
        
        for (const container of targetContainers) {
            if (container) {
                container.appendChild(toggleButton);
                break;
            }
        }
        
        // Add keyboard shortcut (Ctrl+Shift+A)
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.shiftKey && event.key === 'A') {
                event.preventDefault();
                this.toggleAnalyticsDashboard();
            }
        });
        
        // Create performance status indicator
        this.createPerformanceStatusIndicator();
    }

    createPerformanceStatusIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'performance-status-indicator';
        indicator.className = 'performance-status-indicator';
        indicator.innerHTML = `
            <div class="status-dot" id="performance-status-dot"></div>
            <span class="status-text" id="performance-status-text">Monitoring</span>
        `;
        
        // Position in corner
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
            z-index: 9999;
            cursor: pointer;
        `;
        
        const statusDot = indicator.querySelector('#performance-status-dot');
        statusDot.style.cssText = `
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        `;
        
        indicator.addEventListener('click', () => {
            this.showQuickStats();
        });
        
        document.body.appendChild(indicator);
        
        // Update status periodically
        setInterval(() => {
            this.updatePerformanceStatus();
        }, 5000);
    }

    updatePerformanceStatus() {
        const statusDot = document.getElementById('performance-status-dot');
        const statusText = document.getElementById('performance-status-text');
        
        if (!statusDot || !statusText) return;
        
        const metrics = this.performanceMonitor.getMetricsSummary();
        
        // Determine status based on recent errors and performance
        let status = 'good';
        let statusMessage = 'Good';
        
        if (metrics.errors.recent.length > 5) {
            status = 'warning';
            statusMessage = 'Issues';
        }
        
        if (metrics.errors.recent.length > 10) {
            status = 'error';
            statusMessage = 'Problems';
        }
        
        // Update UI
        const colors = {
            good: '#28a745',
            warning: '#ffc107',
            error: '#dc3545'
        };
        
        statusDot.style.backgroundColor = colors[status];
        statusText.textContent = statusMessage;
    }

    showQuickStats() {
        const metrics = this.performanceMonitor.getMetricsSummary();
        
        const quickStats = `
Performance Quick Stats:
• Total Interactions: ${metrics.userInteractions.total}
• Backup Sessions: ${metrics.backupPerformance.total}
• Recent Errors: ${metrics.errors.recent.length}
• Memory Usage: ${metrics.resourceUsage.memory ? 
    this.formatBytes(metrics.resourceUsage.memory.used) : 'N/A'}

Click Analytics button for detailed view.
        `;
        
        alert(quickStats);
    }

    startMonitoring() {
        if (this.performanceMonitor) {
            this.performanceMonitor.startMonitoring();
        }
        
        if (this.config.enableRealTimeUpdates) {
            this.startRealTimeUpdates();
        }
        
        if (this.config.enableOptimizationRecommendations) {
            this.startAutoAnalysis();
        }
    }

    stopMonitoring() {
        if (this.performanceMonitor) {
            this.performanceMonitor.stopMonitoring();
        }
        
        this.stopRealTimeUpdates();
        this.stopAutoAnalysis();
    }

    startRealTimeUpdates() {
        if (this.intervals.realTimeUpdate) {
            clearInterval(this.intervals.realTimeUpdate);
        }
        
        this.intervals.realTimeUpdate = setInterval(() => {
            if (this.analyticsDashboard && this.analyticsDashboard.isVisible) {
                this.analyticsDashboard.updateDashboard();
            }
        }, this.config.updateInterval);
    }

    stopRealTimeUpdates() {
        if (this.intervals.realTimeUpdate) {
            clearInterval(this.intervals.realTimeUpdate);
            this.intervals.realTimeUpdate = null;
        }
    }

    startAutoAnalysis() {
        if (this.intervals.autoAnalyze) {
            clearInterval(this.intervals.autoAnalyze);
        }
        
        this.intervals.autoAnalyze = setInterval(() => {
            this.runPerformanceAnalysis();
        }, this.config.autoAnalyzeInterval);
    }

    stopAutoAnalysis() {
        if (this.intervals.autoAnalyze) {
            clearInterval(this.intervals.autoAnalyze);
            this.intervals.autoAnalyze = null;
        }
    }

    runPerformanceAnalysis() {
        if (this.performanceOptimizer) {
            const recommendations = this.performanceOptimizer.analyzePerformance();
            console.log(`Generated ${recommendations.length} performance recommendations`);
            
            // Show high-priority recommendations as notifications
            const highPriorityRecs = recommendations.filter(r => r.severity === 'high');
            if (highPriorityRecs.length > 0) {
                this.showPerformanceAlert(highPriorityRecs);
            }
        }
    }

    showPerformanceAlert(recommendations) {
        // Create non-intrusive notification
        const notification = document.createElement('div');
        notification.className = 'performance-alert';
        notification.innerHTML = `
            <div class="alert-header">
                <span>⚠️ Performance Alert</span>
                <button class="alert-close">×</button>
            </div>
            <div class="alert-body">
                ${recommendations.length} high-priority performance issue${recommendations.length > 1 ? 's' : ''} detected.
                <button class="alert-action">View Details</button>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 60px;
            right: 10px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 12px;
            max-width: 300px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 10000);
        
        // Event handlers
        notification.querySelector('.alert-close').addEventListener('click', () => {
            notification.parentNode.removeChild(notification);
        });
        
        notification.querySelector('.alert-action').addEventListener('click', () => {
            this.showAnalyticsDashboard();
            notification.parentNode.removeChild(notification);
        });
        
        document.body.appendChild(notification);
    }

    toggleAnalyticsDashboard() {
        if (this.analyticsDashboard) {
            this.analyticsDashboard.toggle();
        }
    }

    showAnalyticsDashboard() {
        if (this.analyticsDashboard) {
            this.analyticsDashboard.show();
        }
    }

    hideAnalyticsDashboard() {
        if (this.analyticsDashboard) {
            this.analyticsDashboard.hide();
        }
    }

    // Public API methods
    getPerformanceMetrics() {
        return this.performanceMonitor ? this.performanceMonitor.getMetricsSummary() : null;
    }

    getHeatmapData(timeRange = null) {
        return this.heatmapTracker ? this.heatmapTracker.exportHeatmapData() : null;
    }

    getABTestResults() {
        return this.abTestingFramework ? this.abTestingFramework.getAllTestResults() : null;
    }

    getPerformanceRecommendations() {
        return this.performanceOptimizer ? this.performanceOptimizer.analyzePerformance() : [];
    }

    exportAllData() {
        return {
            performance: this.getPerformanceMetrics(),
            heatmap: this.getHeatmapData(),
            abTests: this.getABTestResults(),
            recommendations: this.getPerformanceRecommendations(),
            exportTime: Date.now()
        };
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Cleanup method
    destroy() {
        this.stopMonitoring();
        
        // Remove UI elements
        const toggleButton = document.getElementById('performance-analytics-toggle');
        if (toggleButton) {
            toggleButton.parentNode.removeChild(toggleButton);
        }
        
        const statusIndicator = document.getElementById('performance-status-indicator');
        if (statusIndicator) {
            statusIndicator.parentNode.removeChild(statusIndicator);
        }
        
        // Cleanup components
        if (this.performanceMonitor) {
            this.performanceMonitor.stopMonitoring();
        }
        
        if (this.heatmapTracker) {
            this.heatmapTracker.stopTracking();
        }
        
        console.log('Performance Analytics Integration destroyed');
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if not already initialized
    if (!window.performanceAnalytics) {
        window.performanceAnalytics = new PerformanceAnalyticsIntegration();
        window.performanceAnalytics.init().catch(error => {
            console.error('Failed to initialize performance analytics:', error);
        });
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceAnalyticsIntegration;
}