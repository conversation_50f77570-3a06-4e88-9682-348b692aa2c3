/* ServiceCheckbox Component Styles */

.service-checkbox {
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.service-checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    position: relative;
    overflow: hidden;
}

.service-checkbox-label:hover {
    border-color: #4facfe;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.15);
}

.service-checkbox.checked .service-checkbox-label {
    border-color: #4facfe;
    background: #f8f9ff;
}

.service-checkbox.disabled .service-checkbox-label {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.service-checkbox.disabled .service-checkbox-label:hover {
    border-color: #e9ecef;
    transform: none;
    box-shadow: none;
}

/* Hide default checkbox */
.service-checkbox-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

/* Custom checkbox visual */
.service-checkbox-visual {
    width: 24px;
    height: 24px;
    border: 2px solid #ddd;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    background: white;
    flex-shrink: 0;
    margin-top: 2px;
}

.service-checkbox-visual .checkmark {
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.2s ease;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.service-checkbox.checked .service-checkbox-visual {
    background: #4facfe;
    border-color: #4facfe;
}

.service-checkbox.checked .service-checkbox-visual .checkmark {
    opacity: 1;
    transform: scale(1);
}

/* Service information */
.service-info {
    flex: 1;
    min-width: 0;
}

.service-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.service-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.service-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
    flex: 1;
}

.service-price {
    font-weight: 600;
    color: #4facfe;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.service-description {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Animation states */
.service-checkbox.selecting {
    animation: selectPulse 0.2s ease-out;
}

.service-checkbox.deselecting {
    animation: deselectPulse 0.2s ease-out;
}

@keyframes selectPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

@keyframes deselectPulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
}

/* Focus states for accessibility */
.service-checkbox.focused .service-checkbox-label {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
}

/* Hover effects */
.service-checkbox.hover:not(.disabled) .service-checkbox-visual {
    border-color: #4facfe;
    transform: scale(1.05);
}

.service-checkbox.hover:not(.disabled) .service-name {
    color: #4facfe;
}

/* Mobile responsive design */
@media (max-width: 768px) {
    .service-checkbox-label {
        padding: 16px;
        gap: 12px;
    }
    
    .service-header {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .service-name {
        font-size: 1rem;
    }
    
    .service-price {
        font-size: 1rem;
    }
    
    .service-description {
        font-size: 0.85rem;
    }
    
    .service-icon {
        font-size: 20px;
    }
    
    .service-checkbox-visual {
        width: 20px;
        height: 20px;
    }
    
    .service-checkbox-visual .checkmark {
        font-size: 12px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .service-checkbox-label {
        border-width: 3px;
    }
    
    .service-checkbox-visual {
        border-width: 3px;
    }
    
    .service-checkbox.checked .service-checkbox-visual {
        background: #000;
        border-color: #000;
    }
    
    .service-checkbox.checked .service-checkbox-visual .checkmark {
        color: #fff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .service-checkbox-label,
    .service-checkbox-visual,
    .service-checkbox-visual .checkmark {
        transition: none;
    }
    
    .service-checkbox.selecting,
    .service-checkbox.deselecting {
        animation: none;
    }
    
    .service-checkbox.hover:not(.disabled) .service-checkbox-visual {
        transform: none;
    }
}