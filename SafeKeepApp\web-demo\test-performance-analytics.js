/**
 * Test Performance Analytics - Test suite for performance monitoring system
 * Tests all components of the performance analytics implementation
 */

class PerformanceAnalyticsTest {
    constructor() {
        this.testResults = [];
        this.performanceAnalytics = null;
    }

    async runAllTests() {
        console.log('Starting Performance Analytics Tests...');
        
        try {
            // Initialize the system
            await this.testInitialization();
            
            // Test individual components
            await this.testPerformanceMonitor();
            await this.testHeatmapTracker();
            await this.testABTestingFramework();
            await this.testPerformanceOptimizer();
            await this.testAnalyticsDashboard();
            
            // Test integration
            await this.testIntegration();
            
            // Generate test report
            this.generateTestReport();
            
        } catch (error) {
            console.error('Test suite failed:', error);
            this.addTestResult('Test Suite', false, error.message);
        }
    }

    async testInitialization() {
        console.log('Testing initialization...');
        
        try {
            // Test if classes are available
            this.assert(typeof PerformanceMonitor !== 'undefined', 'PerformanceMonitor class available');
            this.assert(typeof AnalyticsDashboard !== 'undefined', 'AnalyticsDashboard class available');
            this.assert(typeof HeatmapTracker !== 'undefined', 'HeatmapTracker class available');
            this.assert(typeof ABTestingFramework !== 'undefined', 'ABTestingFramework class available');
            this.assert(typeof PerformanceOptimizer !== 'undefined', 'PerformanceOptimizer class available');
            
            // Test initialization
            this.performanceAnalytics = new PerformanceAnalyticsIntegration();
            await this.performanceAnalytics.init();
            
            this.assert(this.performanceAnalytics.isInitialized, 'Performance analytics initialized');
            this.addTestResult('Initialization', true, 'All components initialized successfully');
            
        } catch (error) {
            this.addTestResult('Initialization', false, error.message);
            throw error;
        }
    }

    async testPerformanceMonitor() {
        console.log('Testing Performance Monitor...');
        
        try {
            const monitor = this.performanceAnalytics.performanceMonitor;
            
            // Test basic functionality
            this.assert(monitor !== null, 'Performance monitor exists');
            this.assert(typeof monitor.recordError === 'function', 'recordError method exists');
            this.assert(typeof monitor.getMetricsSummary === 'function', 'getMetricsSummary method exists');
            
            // Test error recording
            const testError = new Error('Test error');
            monitor.recordError(testError, { test: true });
            
            const metrics = monitor.getMetricsSummary();
            this.assert(metrics.errors.total > 0, 'Error recorded successfully');
            
            // Test backup performance tracking
            const trackingData = monitor.startBackupPerformanceTracking('test_backup', 'manual');
            this.assert(trackingData.backupId === 'test_backup', 'Backup tracking started');
            
            // Simulate backup completion
            setTimeout(() => {
                const result = monitor.endBackupPerformanceTracking(trackingData, true, 100, 1024000);
                this.assert(result.success === true, 'Backup tracking completed');
            }, 100);
            
            this.addTestResult('Performance Monitor', true, 'All performance monitor tests passed');
            
        } catch (error) {
            this.addTestResult('Performance Monitor', false, error.message);
        }
    }

    async testHeatmapTracker() {
        console.log('Testing Heatmap Tracker...');
        
        try {
            const heatmapTracker = this.performanceAnalytics.heatmapTracker;
            
            this.assert(heatmapTracker !== null, 'Heatmap tracker exists');
            this.assert(typeof heatmapTracker.generateClickHeatmap === 'function', 'generateClickHeatmap method exists');
            
            // Simulate click events
            const mockClickEvent = {
                clientX: 100,
                clientY: 200,
                target: {
                    tagName: 'BUTTON',
                    id: 'test-button',
                    className: 'test-class',
                    textContent: 'Test Button'
                }
            };
            
            heatmapTracker.trackClick(mockClickEvent);
            
            const stats = heatmapTracker.getInteractionStats();
            this.assert(stats.clicks > 0, 'Click tracking works');
            
            const heatmapData = heatmapTracker.generateClickHeatmap();
            this.assert(Array.isArray(heatmapData), 'Heatmap data generated');
            
            this.addTestResult('Heatmap Tracker', true, 'All heatmap tracker tests passed');
            
        } catch (error) {
            this.addTestResult('Heatmap Tracker', false, error.message);
        }
    }

    async testABTestingFramework() {
        console.log('Testing A/B Testing Framework...');
        
        try {
            const abTesting = this.performanceAnalytics.abTestingFramework;
            
            this.assert(abTesting !== null, 'A/B testing framework exists');
            this.assert(typeof abTesting.getVariant === 'function', 'getVariant method exists');
            
            // Test variant assignment
            const variant = abTesting.getVariant('backup_button_color');
            this.assert(variant !== null, 'Variant assigned');
            this.assert(['blue', 'green'].includes(variant), 'Valid variant assigned');
            
            // Test event tracking
            abTesting.trackEvent('backup_button_color', variant, 'test_event');
            
            const results = abTesting.getTestResults('backup_button_color');
            this.assert(results !== null, 'Test results available');
            this.assert(results.variants[variant].events.length > 0, 'Event tracked');
            
            this.addTestResult('A/B Testing Framework', true, 'All A/B testing tests passed');
            
        } catch (error) {
            this.addTestResult('A/B Testing Framework', false, error.message);
        }
    }

    async testPerformanceOptimizer() {
        console.log('Testing Performance Optimizer...');
        
        try {
            const optimizer = this.performanceAnalytics.performanceOptimizer;
            
            this.assert(optimizer !== null, 'Performance optimizer exists');
            this.assert(typeof optimizer.analyzePerformance === 'function', 'analyzePerformance method exists');
            
            // Run analysis
            const recommendations = optimizer.analyzePerformance();
            this.assert(Array.isArray(recommendations), 'Recommendations generated');
            
            // Test recommendation structure
            if (recommendations.length > 0) {
                const rec = recommendations[0];
                this.assert(rec.hasOwnProperty('title'), 'Recommendation has title');
                this.assert(rec.hasOwnProperty('severity'), 'Recommendation has severity');
                this.assert(rec.hasOwnProperty('category'), 'Recommendation has category');
            }
            
            this.addTestResult('Performance Optimizer', true, 'All performance optimizer tests passed');
            
        } catch (error) {
            this.addTestResult('Performance Optimizer', false, error.message);
        }
    }

    async testAnalyticsDashboard() {
        console.log('Testing Analytics Dashboard...');
        
        try {
            const dashboard = this.performanceAnalytics.analyticsDashboard;
            
            this.assert(dashboard !== null, 'Analytics dashboard exists');
            this.assert(typeof dashboard.show === 'function', 'show method exists');
            this.assert(typeof dashboard.hide === 'function', 'hide method exists');
            
            // Test dashboard container creation
            const container = document.getElementById('analytics-dashboard');
            this.assert(container !== null, 'Dashboard container created');
            
            // Test show/hide functionality
            dashboard.show();
            this.assert(!container.classList.contains('hidden'), 'Dashboard shows correctly');
            
            dashboard.hide();
            this.assert(container.classList.contains('hidden'), 'Dashboard hides correctly');
            
            this.addTestResult('Analytics Dashboard', true, 'All analytics dashboard tests passed');
            
        } catch (error) {
            this.addTestResult('Analytics Dashboard', false, error.message);
        }
    }

    async testIntegration() {
        console.log('Testing Integration...');
        
        try {
            // Test public API methods
            const metrics = this.performanceAnalytics.getPerformanceMetrics();
            this.assert(metrics !== null, 'Performance metrics accessible');
            
            const heatmapData = this.performanceAnalytics.getHeatmapData();
            this.assert(heatmapData !== null, 'Heatmap data accessible');
            
            const abTestResults = this.performanceAnalytics.getABTestResults();
            this.assert(abTestResults !== null, 'A/B test results accessible');
            
            const recommendations = this.performanceAnalytics.getPerformanceRecommendations();
            this.assert(Array.isArray(recommendations), 'Performance recommendations accessible');
            
            // Test data export
            const exportData = this.performanceAnalytics.exportAllData();
            this.assert(exportData.hasOwnProperty('performance'), 'Export includes performance data');
            this.assert(exportData.hasOwnProperty('heatmap'), 'Export includes heatmap data');
            this.assert(exportData.hasOwnProperty('abTests'), 'Export includes A/B test data');
            this.assert(exportData.hasOwnProperty('recommendations'), 'Export includes recommendations');
            
            // Test UI controls
            const toggleButton = document.getElementById('performance-analytics-toggle');
            this.assert(toggleButton !== null, 'Toggle button created');
            
            const statusIndicator = document.getElementById('performance-status-indicator');
            this.assert(statusIndicator !== null, 'Status indicator created');
            
            this.addTestResult('Integration', true, 'All integration tests passed');
            
        } catch (error) {
            this.addTestResult('Integration', false, error.message);
        }
    }

    // Test utility methods
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
        console.log(`✓ ${message}`);
    }

    addTestResult(testName, passed, message) {
        this.testResults.push({
            testName,
            passed,
            message,
            timestamp: Date.now()
        });
    }

    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n=== Performance Analytics Test Report ===');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        console.log('\nDetailed Results:');
        this.testResults.forEach(result => {
            const status = result.passed ? '✓ PASS' : '✗ FAIL';
            console.log(`${status} ${result.testName}: ${result.message}`);
        });
        
        // Create visual test report
        this.createVisualTestReport();
        
        return {
            totalTests,
            passedTests,
            failedTests,
            successRate: (passedTests / totalTests) * 100,
            results: this.testResults
        };
    }

    createVisualTestReport() {
        const reportContainer = document.createElement('div');
        reportContainer.id = 'performance-test-report';
        reportContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 10001;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const successRate = ((passedTests / totalTests) * 100).toFixed(1);
        
        reportContainer.innerHTML = `
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #007bff; margin: 0;">Performance Analytics Test Report</h2>
                <div style="margin: 10px 0;">
                    <span style="font-size: 24px; font-weight: bold; color: ${successRate >= 90 ? '#28a745' : successRate >= 70 ? '#ffc107' : '#dc3545'};">
                        ${successRate}%
                    </span>
                    <div style="font-size: 14px; color: #666;">
                        ${passedTests}/${totalTests} tests passed
                    </div>
                </div>
            </div>
            
            <div style="margin-bottom: 20px;">
                ${this.testResults.map(result => `
                    <div style="display: flex; align-items: center; padding: 8px; margin: 4px 0; background: ${result.passed ? '#d4edda' : '#f8d7da'}; border-radius: 4px;">
                        <span style="margin-right: 10px; font-size: 16px;">
                            ${result.passed ? '✓' : '✗'}
                        </span>
                        <div style="flex: 1;">
                            <strong>${result.testName}</strong>
                            <div style="font-size: 12px; color: #666;">${result.message}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
            
            <div style="text-align: center;">
                <button id="close-test-report" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                    Close Report
                </button>
            </div>
        `;
        
        document.body.appendChild(reportContainer);
        
        // Close button handler
        document.getElementById('close-test-report').addEventListener('click', () => {
            document.body.removeChild(reportContainer);
        });
        
        // Auto-close after 30 seconds
        setTimeout(() => {
            if (reportContainer.parentNode) {
                document.body.removeChild(reportContainer);
            }
        }, 30000);
    }

    // Simulate user interactions for testing
    simulateUserInteractions() {
        console.log('Simulating user interactions...');
        
        // Simulate clicks
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                const event = new MouseEvent('click', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight,
                    bubbles: true
                });
                document.body.dispatchEvent(event);
            }, i * 100);
        }
        
        // Simulate scrolling
        setTimeout(() => {
            window.scrollTo(0, 100);
            setTimeout(() => window.scrollTo(0, 0), 500);
        }, 1000);
        
        // Simulate form interaction
        const testInput = document.createElement('input');
        testInput.type = 'text';
        testInput.style.position = 'absolute';
        testInput.style.left = '-9999px';
        document.body.appendChild(testInput);
        
        setTimeout(() => {
            testInput.focus();
            setTimeout(() => {
                testInput.blur();
                document.body.removeChild(testInput);
            }, 200);
        }, 1500);
    }
}

// Auto-run tests when this script is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for the main system to initialize
    setTimeout(() => {
        const tester = new PerformanceAnalyticsTest();
        
        // Add test button to page
        const testButton = document.createElement('button');
        testButton.textContent = 'Run Performance Tests';
        testButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 9999;
        `;
        
        testButton.addEventListener('click', async () => {
            testButton.disabled = true;
            testButton.textContent = 'Running Tests...';
            
            try {
                await tester.runAllTests();
                testButton.textContent = 'Tests Complete';
                testButton.style.background = '#007bff';
            } catch (error) {
                testButton.textContent = 'Tests Failed';
                testButton.style.background = '#dc3545';
            }
            
            setTimeout(() => {
                testButton.disabled = false;
                testButton.textContent = 'Run Performance Tests';
                testButton.style.background = '#28a745';
            }, 5000);
        });
        
        document.body.appendChild(testButton);
        
        // Simulate some interactions for testing
        setTimeout(() => {
            tester.simulateUserInteractions();
        }, 2000);
        
    }, 3000);
});

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceAnalyticsTest;
}