/**
 * TypeScript-style interfaces and types for Modular Pricing UI
 * Using JSDoc for type definitions in JavaScript
 */

/**
 * @typedef {Object} ServiceOption
 * @property {string} id - Unique service identifier
 * @property {string} name - Display name of the service
 * @property {string} description - Brief description of the service
 * @property {string[]} features - List of features included in the service
 * @property {string} icon - Icon representation (emoji or class name)
 * @property {number} individualPrice - Price in cents for individual service
 */

/**
 * @typedef {Object} PricingResult
 * @property {string} recommendedPlanId - ID of the recommended plan
 * @property {string} recommendedPlanName - Name of the recommended plan
 * @property {number} totalPrice - Total price in cents
 * @property {number} individualTotal - Sum of individual service prices
 * @property {number} savings - Amount saved compared to individual pricing
 * @property {string} currency - Currency code (e.g., 'USD')
 */

/**
 * @typedef {Object} PlanOption
 * @property {string} id - Unique plan identifier
 * @property {string} name - Display name of the plan
 * @property {number} price - Price in cents
 * @property {string[]} services - Array of included service IDs
 * @property {boolean} isPopular - Whether this plan should be highlighted
 * @property {string[]} features - List of plan features
 * @property {string} storageLimit - Storage limit description
 */

/**
 * @typedef {Object} PricingUIState
 * @property {string[]} selectedServices - Array of selected service IDs
 * @property {ServiceOption[]} availableServices - Array of available services
 * @property {PricingResult|null} currentPricing - Current pricing calculation
 * @property {boolean} isLoading - Whether pricing calculation is in progress
 * @property {string|null} showServiceDetails - ID of service with expanded details
 * @property {'idle'|'updating'|'complete'} animationState - Current animation state
 */

/**
 * @typedef {Object} PricingError
 * @property {'network'|'validation'|'calculation'} type - Type of error
 * @property {string} message - Error message to display
 * @property {boolean} retryable - Whether the error can be retried
 */

// Export types for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        // Types are exported via JSDoc comments above
    };
}