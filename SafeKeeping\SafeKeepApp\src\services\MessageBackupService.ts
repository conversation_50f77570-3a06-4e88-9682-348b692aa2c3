import { Platform, PermissionsAndroid } from 'react-native';
import CloudStorageService from './CloudStorageService';
import AuthService from './AuthService';

export interface MessageData {
  id: string;
  threadId: string;
  address: string; // Phone number or email
  person: string; // Contact name if available
  date: number; // Timestamp
  dateReceived: number;
  protocol: number; // SMS protocol
  read: boolean;
  status: number; // Message status
  type: number; // 1 = received, 2 = sent
  body: string; // Message content
  serviceCenter?: string;
  subject?: string;
  replyPathPresent?: boolean;
  locked?: boolean;
}

export interface MessageThread {
  threadId: string;
  contactName: string;
  address: string;
  messageCount: number;
  lastMessage: string;
  lastMessageDate: number;
  messages: MessageData[];
}

export interface MessageBackupProgress {
  totalMessages: number;
  processedMessages: number;
  currentThread: string;
  percentage: number;
  status: 'scanning' | 'processing' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface MessageBackupResult {
  success: boolean;
  totalMessages: number;
  backedUpMessages: number;
  skippedMessages: number;
  totalThreads: number;
  errors: string[];
  duration: number;
}

class MessageBackupService {
  private isBackupRunning = false;
  private shouldPauseBackup = false;
  private progressCallback?: (progress: MessageBackupProgress) => void;

  // Request SMS permission
  async requestSMSPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_SMS,
          {
            title: '💬 SMS Access Permission',
            message: 'SafeKeep needs access to your text messages to back them up safely.\n\nThis helps you:\n• Never lose important conversations\n• Restore messages if you get a new phone\n• Keep family and business messages safe',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'Allow',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        // iOS doesn't allow SMS access for third-party apps
        // We would need to use alternative methods like iMessage backup
        console.warn('iOS does not allow SMS access for third-party apps');
        return false;
      }
    } catch (error) {
      console.error('Error requesting SMS permission:', error);
      return false;
    }
  }

  // Scan device messages (Android only - iOS would need different approach)
  async scanMessages(): Promise<MessageData[]> {
    try {
      console.log('💬 Starting message scan...');

      // Check permission first
      const hasPermission = await this.requestSMSPermission();
      if (!hasPermission) {
        throw new Error('SMS permission denied');
      }

      // For demo purposes, we'll simulate message scanning
      // In a real implementation, this would use native modules to access SMS database
      const simulatedMessages: MessageData[] = this.generateSimulatedMessages();

      console.log(`💬 Found ${simulatedMessages.length} messages`);
      return simulatedMessages;
    } catch (error) {
      console.error('Error scanning messages:', error);
      throw new Error('Failed to scan messages. Please check permissions.');
    }
  }

  // Generate simulated messages for demo
  private generateSimulatedMessages(): MessageData[] {
    const contacts = [
      { name: 'Mom', number: '+**********' },
      { name: 'Dad', number: '+**********' },
      { name: 'Sarah', number: '+**********' },
      { name: 'John', number: '+**********' },
      { name: 'Work', number: '+**********' },
      { name: 'Bank', number: '+**********' },
      { name: 'Doctor', number: '+**********' }
    ];

    const messageTemplates = [
      'Hi! How are you doing?',
      'Can you call me when you get this?',
      'Thanks for your help today',
      'See you tomorrow at 3pm',
      'Don\'t forget about dinner tonight',
      'Happy birthday! 🎉',
      'Your appointment is confirmed',
      'Package delivered successfully',
      'Meeting moved to 2pm',
      'Have a great weekend!'
    ];

    const messages: MessageData[] = [];
    let messageId = 1;

    contacts.forEach((contact, contactIndex) => {
      const messageCount = Math.floor(Math.random() * 20) + 5; // 5-25 messages per contact
      
      for (let i = 0; i < messageCount; i++) {
        const date = Date.now() - (Math.random() * 30 * 24 * 60 * 60 * 1000); // Last 30 days
        const isReceived = Math.random() > 0.5;
        
        messages.push({
          id: messageId.toString(),
          threadId: (contactIndex + 1).toString(),
          address: contact.number,
          person: contact.name,
          date: date,
          dateReceived: date,
          protocol: 0,
          read: Math.random() > 0.1, // 90% read
          status: 0,
          type: isReceived ? 1 : 2, // 1 = received, 2 = sent
          body: messageTemplates[Math.floor(Math.random() * messageTemplates.length)],
          locked: false
        });
        
        messageId++;
      }
    });

    return messages.sort((a, b) => b.date - a.date); // Sort by date, newest first
  }

  // Group messages by thread/conversation
  groupMessagesByThread(messages: MessageData[]): MessageThread[] {
    const threads = new Map<string, MessageThread>();

    messages.forEach(message => {
      if (!threads.has(message.threadId)) {
        threads.set(message.threadId, {
          threadId: message.threadId,
          contactName: message.person || message.address,
          address: message.address,
          messageCount: 0,
          lastMessage: '',
          lastMessageDate: 0,
          messages: []
        });
      }

      const thread = threads.get(message.threadId)!;
      thread.messages.push(message);
      thread.messageCount++;

      if (message.date > thread.lastMessageDate) {
        thread.lastMessageDate = message.date;
        thread.lastMessage = message.body;
      }
    });

    return Array.from(threads.values()).sort((a, b) => b.lastMessageDate - a.lastMessageDate);
  }

  // Backup messages to cloud storage
  async backupMessages(
    messages: MessageData[],
    onProgress?: (progress: MessageBackupProgress) => void
  ): Promise<MessageBackupResult> {
    if (this.isBackupRunning) {
      throw new Error('Message backup is already running');
    }

    // Check authentication
    const user = AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User authentication required for backup');
    }

    this.isBackupRunning = true;
    this.shouldPauseBackup = false;
    this.progressCallback = onProgress;

    const startTime = Date.now();
    let backedUpMessages = 0;
    let skippedMessages = 0;
    const errors: string[] = [];

    try {
      console.log(`🚀 Starting backup of ${messages.length} messages...`);

      // Step 1: Group messages by thread
      if (onProgress) {
        onProgress({
          totalMessages: messages.length,
          processedMessages: 0,
          currentThread: 'Organizing conversations...',
          percentage: 0,
          status: 'processing'
        });
      }

      const threads = this.groupMessagesByThread(messages);
      console.log(`📱 Organized into ${threads.length} conversation threads`);

      // Step 2: Create backup data structure
      const messageBackupData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        totalMessages: messages.length,
        totalThreads: threads.length,
        threads: threads
      };

      const backupFileName = `messages_backup_${new Date().toISOString().split('T')[0]}.json`;
      const backupDataString = JSON.stringify(messageBackupData, null, 2);

      if (onProgress) {
        onProgress({
          totalMessages: messages.length,
          processedMessages: Math.floor(messages.length * 0.5),
          currentThread: 'Uploading message backup...',
          percentage: 50,
          status: 'uploading'
        });
      }

      // Step 3: Upload the messages backup file
      const uploadResult = await CloudStorageService.uploadFile(
        backupDataString,
        backupFileName,
        'application/json',
        'message',
        (progress) => {
          if (onProgress) {
            onProgress({
              totalMessages: messages.length,
              processedMessages: Math.floor((progress.percentage / 100) * messages.length),
              currentThread: 'Uploading message backup...',
              percentage: 50 + (progress.percentage / 2), // 50-100%
              status: 'uploading'
            });
          }
        }
      );

      if (uploadResult.success) {
        backedUpMessages = messages.length;
        console.log(`✅ Successfully backed up ${backedUpMessages} messages in ${threads.length} threads`);
      } else {
        errors.push(`Failed to upload messages backup: ${uploadResult.error}`);
        skippedMessages = messages.length;
      }

      // Final progress update
      if (onProgress) {
        onProgress({
          totalMessages: messages.length,
          processedMessages: messages.length,
          currentThread: 'Backup completed!',
          percentage: 100,
          status: 'completed'
        });
      }

      const duration = Date.now() - startTime;
      const result: MessageBackupResult = {
        success: errors.length === 0,
        totalMessages: messages.length,
        backedUpMessages,
        skippedMessages,
        totalThreads: threads.length,
        errors,
        duration
      };

      console.log('🎉 Message backup completed:', result);
      return result;

    } catch (error: unknown) {
      console.error('❌ Message backup failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (onProgress) {
        onProgress({
          totalMessages: messages.length,
          processedMessages: 0,
          currentThread: '',
          percentage: 0,
          status: 'error',
          error: errorMessage
        });
      }

      return {
        success: false,
        totalMessages: messages.length,
        backedUpMessages,
        skippedMessages,
        totalThreads: 0,
        errors: [errorMessage],
        duration: Date.now() - startTime
      };
    } finally {
      this.isBackupRunning = false;
      this.progressCallback = undefined;
    }
  }

  // Restore messages from backup
  async restoreMessages(): Promise<{ success: boolean; threads?: MessageThread[]; error?: string }> {
    try {
      console.log('💬 Starting message restore...');

      // Get user's message backup files
      const files = await CloudStorageService.getUserFiles('message');
      
      if (files.length === 0) {
        return { success: false, error: 'No message backup found' };
      }

      // Get the most recent backup
      const latestBackup = files.sort((a, b) => 
        new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
      )[0];

      // Download and decrypt the backup
      const downloadResult = await CloudStorageService.downloadFile(latestBackup.id);
      
      if (!downloadResult.success) {
        return { success: false, error: downloadResult.error };
      }

      // Parse the backup data
      const backupData = JSON.parse(downloadResult.data!);
      const threads = backupData.threads as MessageThread[];

      console.log(`✅ Successfully restored ${threads.length} conversation threads`);
      return { success: true, threads };

    } catch (error: unknown) {
      console.error('❌ Message restore failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  // Get backup statistics
  async getBackupStats(): Promise<{
    totalBackups: number;
    lastBackupDate?: Date;
    totalMessages: number;
    totalThreads: number;
  }> {
    try {
      const files = await CloudStorageService.getUserFiles('message');
      
      if (files.length === 0) {
        return { totalBackups: 0, totalMessages: 0, totalThreads: 0 };
      }

      const latestBackup = files.sort((a, b) => 
        new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
      )[0];

      // Try to get message count from latest backup
      let totalMessages = 0;
      let totalThreads = 0;
      try {
        const downloadResult = await CloudStorageService.downloadFile(latestBackup.id);
        if (downloadResult.success) {
          const backupData = JSON.parse(downloadResult.data!);
          totalMessages = backupData.totalMessages || 0;
          totalThreads = backupData.totalThreads || 0;
        }
      } catch (error: unknown) {
        console.warn('Could not read backup file for stats:', error);
      }

      return {
        totalBackups: files.length,
        lastBackupDate: new Date(latestBackup.uploaded_at),
        totalMessages,
        totalThreads
      };
    } catch (error: unknown) {
      console.error('Failed to get backup stats:', error);
      return { totalBackups: 0, totalMessages: 0, totalThreads: 0 };
    }
  }

  // Control functions
  pauseBackup(): void {
    this.shouldPauseBackup = true;
    console.log('⏸️ Message backup paused by user');
  }

  resumeBackup(): void {
    this.shouldPauseBackup = false;
    console.log('▶️ Message backup resumed by user');
  }

  isRunning(): boolean {
    return this.isBackupRunning;
  }
}

export default new MessageBackupService();
