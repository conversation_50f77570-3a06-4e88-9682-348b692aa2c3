// Simple test to check if the class can be imported
try {
    const DataExportImportManager = require('./data-export-import-manager.js');
    console.log('Type:', typeof DataExportImportManager);
    console.log('Constructor:', DataExportImportManager.constructor);
    console.log('Name:', DataExportImportManager.name);
    
    // Try to create an instance
    const mockSupabase = { from: () => ({}) };
    const instance = new DataExportImportManager(mockSupabase, mockSupabase);
    console.log('Instance created successfully:', !!instance);
    
} catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
}