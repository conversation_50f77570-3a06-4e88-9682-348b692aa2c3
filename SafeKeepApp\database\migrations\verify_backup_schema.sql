-- Verification script for backup tracking schema
-- Run this after applying the backup_tracking_schema.sql migration

-- Check that all required tables exist
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('backup_sessions', 'backup_items', 'backup_progress')
ORDER BY table_name;

-- Check that <PERSON><PERSON> is enabled on all backup tables
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename IN ('backup_sessions', 'backup_items', 'backup_progress')
ORDER BY tablename;

-- Check that all required indexes exist
SELECT 
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('backup_sessions', 'backup_items', 'backup_progress')
ORDER BY tablename, indexname;

-- Check that all helper functions exist
SELECT 
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'initialize_backup_progress',
    'update_backup_progress', 
    'get_backup_session_summary',
    'cleanup_old_backup_sessions'
)
ORDER BY routine_name;

-- Check table structures
\d backup_sessions;
\d backup_items;
\d backup_progress;

-- Test basic functionality (requires authenticated user)
-- These queries will only work if run by an authenticated user

-- Test creating a backup session
INSERT INTO backup_sessions (user_id, total_items, configuration)
VALUES (
    auth.uid(),
    100,
    '{"autoBackup": true, "wifiOnly": true, "includeContacts": true, "includeMessages": true, "includePhotos": true, "compressionLevel": "medium"}'::jsonb
)
RETURNING id, status, start_time;

-- Test initializing progress (replace session-id with actual ID from above)
-- SELECT initialize_backup_progress('session-id-here', 50, 30, 20);

-- Test getting session summary (replace session-id with actual ID)
-- SELECT * FROM get_backup_session_summary('session-id-here');

-- Clean up test data
-- DELETE FROM backup_sessions WHERE user_id = auth.uid() AND total_items = 100;