import SecureKeyManagementService from '../SecureKeyManagementService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock AsyncStorage
const mockAsyncStorage = {
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  getAllKeys: jest.fn(),
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

describe('SecureKeyManagementService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateSecureMasterKey', () => {
    it('should generate a secure master key with metadata', async () => {
      const result = await SecureKeyManagementService.generateSecureMasterKey();

      expect(result.masterKey).toBeDefined();
      expect(result.keyId).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.metadata.keySize).toBe(256);
      expect(result.metadata.algorithm).toBe('AES-256-CBC');
      expect(result.metadata.derivationIterations).toBe(100000);
    });

    it('should generate unique key IDs', async () => {
      const result1 = await SecureKeyManagementService.generateSecureMasterKey();
      const result2 = await SecureKeyManagementService.generateSecureMasterKey();

      expect(result1.keyId).not.toBe(result2.keyId);
    });
  });

  describe('storeSecureMasterKey', () => {
    it('should store master key and metadata', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();

      await SecureKeyManagementService.storeSecureMasterKey(masterKey, metadata);

      expect(mockAsyncStorage.setItem).toHaveBeenCalledTimes(2);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        expect.stringContaining('master_key'),
        masterKey
      );
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        expect.stringContaining('key_metadata'),
        JSON.stringify(metadata)
      );
    });

    it('should encrypt master key with user passphrase', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
      const passphrase = 'user_passphrase_123';

      await SecureKeyManagementService.storeSecureMasterKey(masterKey, metadata, passphrase);

      expect(mockAsyncStorage.setItem).toHaveBeenCalledTimes(2);
      
      // The stored key should be encrypted (different from original)
      const storedKeyCall = mockAsyncStorage.setItem.mock.calls.find(
        call => call[0].includes('master_key')
      );
      expect(storedKeyCall[1]).not.toBe(masterKey);
    });
  });

  describe('retrieveSecureMasterKey', () => {
    it('should retrieve stored master key and metadata', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
      
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(masterKey) // master key
        .mockResolvedValueOnce(JSON.stringify(metadata)); // metadata

      const result = await SecureKeyManagementService.retrieveSecureMasterKey();

      expect(result).toBeDefined();
      expect(result?.masterKey).toBe(masterKey);
      expect(result?.metadata.keyId).toBe(metadata.keyId);
    });

    it('should return null when no key is stored', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const result = await SecureKeyManagementService.retrieveSecureMasterKey();

      expect(result).toBeNull();
    });
  });

  describe('validateKeySecurity', () => {
    it('should validate key security for new key', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
      
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(masterKey)
        .mockResolvedValueOnce(JSON.stringify(metadata));

      const result = await SecureKeyManagementService.validateKeySecurity();

      expect(result.isValid).toBe(true);
      expect(result.keyAge).toBe(0);
      expect(result.needsRotation).toBe(false);
      expect(result.securityLevel).toBe('high');
    });

    it('should detect old keys needing rotation', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
      
      // Set key creation date to 100 days ago
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 100);
      metadata.createdAt = oldDate;
      
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(masterKey)
        .mockResolvedValueOnce(JSON.stringify(metadata));

      const result = await SecureKeyManagementService.validateKeySecurity();

      expect(result.isValid).toBe(true);
      expect(result.keyAge).toBeGreaterThan(90);
      expect(result.needsRotation).toBe(true);
      expect(result.securityLevel).toBe('medium');
    });
  });

  describe('rotateKey', () => {
    it('should successfully rotate encryption key', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
      
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(masterKey)
        .mockResolvedValueOnce(JSON.stringify(metadata));

      const result = await SecureKeyManagementService.rotateKey();

      expect(result.success).toBe(true);
      expect(result.newKeyId).toBeDefined();
      expect(result.rotationTimestamp).toBeInstanceOf(Date);
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should handle rotation errors gracefully', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const result = await SecureKeyManagementService.rotateKey();

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('verifyKeyIntegrity', () => {
    it('should verify intact key integrity', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
      
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(masterKey)
        .mockResolvedValueOnce(JSON.stringify(metadata));

      const result = await SecureKeyManagementService.verifyKeyIntegrity();

      expect(result.isIntact).toBe(true);
      expect(result.tamperingDetected).toBe(false);
      expect(result.issues).toHaveLength(0);
    });

    it('should detect missing key', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const result = await SecureKeyManagementService.verifyKeyIntegrity();

      expect(result.isIntact).toBe(false);
      expect(result.tamperingDetected).toBe(true);
      expect(result.issues).toContain('Master key not found');
    });
  });

  describe('secureKeyWipe', () => {
    it('should wipe all encryption keys', async () => {
      mockAsyncStorage.getAllKeys.mockResolvedValue([
        'safekeep_secure_key_backup_key1_123',
        'safekeep_secure_key_backup_key2_456',
        'other_key'
      ]);

      await SecureKeyManagementService.secureKeyWipe();

      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith(
        expect.stringContaining('master_key')
      );
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith(
        expect.stringContaining('key_metadata')
      );
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith(
        'safekeep_secure_key_backup_key1_123'
      );
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith(
        'safekeep_secure_key_backup_key2_456'
      );
    });
  });

  describe('getKeyManagementStatus', () => {
    it('should return comprehensive key status', async () => {
      const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
      
      mockAsyncStorage.getItem
        .mockResolvedValueOnce(masterKey)
        .mockResolvedValueOnce(JSON.stringify(metadata));
      
      mockAsyncStorage.getAllKeys.mockResolvedValue([
        'safekeep_secure_key_backup_key1_123',
        'safekeep_secure_key_backup_key2_456'
      ]);

      const status = await SecureKeyManagementService.getKeyManagementStatus();

      expect(status.hasKey).toBe(true);
      expect(status.keyAge).toBe(0);
      expect(status.securityLevel).toBe('high');
      expect(status.needsRotation).toBe(false);
      expect(status.backupCount).toBe(2);
    });
  });

  describe('Enhanced Security Validation Tests', () => {
    describe('Key Security Strength Tests', () => {
      it('should validate key entropy and randomness', async () => {
        const keys = [];
        
        // Generate multiple keys to test randomness
        for (let i = 0; i < 10; i++) {
          const { masterKey } = await SecureKeyManagementService.generateSecureMasterKey();
          keys.push(masterKey);
        }
        
        // Ensure all keys are unique
        const uniqueKeys = new Set(keys);
        expect(uniqueKeys.size).toBe(keys.length);
        
        // Ensure keys have proper length and format
        keys.forEach(key => {
          expect(key.length).toBeGreaterThan(40); // Base64 encoded 256-bit key
          expect(/^[A-Za-z0-9+/]+=*$/.test(key)).toBe(true); // Valid base64
        });
      });

      it('should validate key derivation security parameters', async () => {
        const { metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        expect(metadata.keySize).toBe(256);
        expect(metadata.derivationIterations).toBeGreaterThanOrEqual(100000);
        expect(metadata.algorithm).toBe('AES-256-CBC');
        expect(metadata.createdAt).toBeInstanceOf(Date);
        expect(metadata.keyId).toMatch(/^key_[a-z0-9]+_[A-Za-z0-9]+$/);
      });

      it('should detect weak security configurations', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        // Simulate weak configuration
        const weakMetadata = {
          ...metadata,
          keySize: 128, // Weak key size
          derivationIterations: 1000 // Too few iterations
        };
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(masterKey)
          .mockResolvedValueOnce(JSON.stringify(weakMetadata));

        const validation = await SecureKeyManagementService.validateKeySecurity();
        
        expect(validation.securityLevel).toBe('low');
        expect(validation.issues).toContain('Key size below recommended 256 bits');
        expect(validation.issues).toContain('PBKDF2 iterations below security threshold');
      });
    });

    describe('Key Backup and Recovery Tests', () => {
      it('should create secure key backups', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        await SecureKeyManagementService.createKeyBackup(
          masterKey,
          metadata,
          'Test backup for key rotation'
        );
        
        expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
          expect.stringContaining('key_backup'),
          expect.any(String)
        );
        
        // Verify backup data structure
        const backupCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('key_backup')
        );
        
        const backupData = JSON.parse(backupCall[1]);
        expect(backupData.encryptedMasterKey).toBeDefined();
        expect(backupData.keyMetadata).toEqual(metadata);
        expect(backupData.recoveryHint).toBe('Test backup for key rotation');
        expect(backupData.backupTimestamp).toBeDefined();
      });

      it('should handle multiple key backups', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        // Create multiple backups
        await SecureKeyManagementService.createKeyBackup(masterKey, metadata, 'Backup 1');
        await SecureKeyManagementService.createKeyBackup(masterKey, metadata, 'Backup 2');
        
        // Verify multiple backup calls with different timestamps
        const backupCalls = mockAsyncStorage.setItem.mock.calls.filter(
          call => call[0].includes('key_backup')
        );
        
        expect(backupCalls.length).toBe(2);
        expect(backupCalls[0][0]).not.toBe(backupCalls[1][0]); // Different backup keys
      });

      it('should validate backup integrity', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        await SecureKeyManagementService.createKeyBackup(masterKey, metadata);
        
        const backupCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('key_backup')
        );
        
        const backupData = JSON.parse(backupCall[1]);
        
        // Verify backup contains encrypted key (not plaintext)
        expect(backupData.encryptedMasterKey).not.toBe(masterKey);
        expect(backupData.encryptedMasterKey.length).toBeGreaterThan(0);
        
        // Verify metadata integrity
        expect(backupData.keyMetadata.keyId).toBe(metadata.keyId);
        expect(backupData.keyMetadata.keySize).toBe(metadata.keySize);
      });
    });

    describe('Key Rotation Security Tests', () => {
      it('should maintain security during key rotation', async () => {
        const { masterKey: oldKey, metadata: oldMetadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(oldKey)
          .mockResolvedValueOnce(JSON.stringify(oldMetadata));

        const rotationResult = await SecureKeyManagementService.rotateKey();
        
        expect(rotationResult.success).toBe(true);
        expect(rotationResult.newKeyId).not.toBe(oldMetadata.keyId);
        
        // Verify new key was stored
        const newKeyCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('master_key')
        );
        expect(newKeyCall).toBeDefined();
        
        // Verify backup was created for old key
        const backupCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('key_backup')
        );
        expect(backupCall).toBeDefined();
      });

      it('should increment rotation count', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        metadata.rotationCount = 2; // Simulate previous rotations
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(masterKey)
          .mockResolvedValueOnce(JSON.stringify(metadata));

        await SecureKeyManagementService.rotateKey();
        
        // Check that new metadata has incremented rotation count
        const newMetadataCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('key_metadata')
        );
        
        const newMetadata = JSON.parse(newMetadataCall[1]);
        expect(newMetadata.rotationCount).toBe(3);
      });

      it('should handle rotation failures gracefully', async () => {
        mockAsyncStorage.setItem.mockRejectedValueOnce(new Error('Storage write failed'));
        
        const result = await SecureKeyManagementService.rotateKey();
        
        expect(result.success).toBe(false);
        expect(result.error).toContain('Storage write failed');
        expect(result.newKeyId).toBe('');
      });
    });

    describe('Key Tampering Detection Tests', () => {
      it('should detect key format tampering', async () => {
        const invalidKey = 'invalid_key_format';
        const { metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(invalidKey)
          .mockResolvedValueOnce(JSON.stringify(metadata));

        const integrityResult = await SecureKeyManagementService.verifyKeyIntegrity();
        
        expect(integrityResult.isIntact).toBe(false);
        expect(integrityResult.tamperingDetected).toBe(true);
        expect(integrityResult.issues).toContain('Invalid key format detected');
      });

      it('should detect metadata corruption', async () => {
        const { masterKey } = await SecureKeyManagementService.generateSecureMasterKey();
        const corruptedMetadata = {
          keyId: null, // Corrupted field
          createdAt: 'invalid_date',
          keySize: 256
        };
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(masterKey)
          .mockResolvedValueOnce(JSON.stringify(corruptedMetadata));

        const integrityResult = await SecureKeyManagementService.verifyKeyIntegrity();
        
        expect(integrityResult.isIntact).toBe(false);
        expect(integrityResult.tamperingDetected).toBe(true);
        expect(integrityResult.issues).toContain('Key metadata corruption detected');
      });

      it('should validate key cryptographic functionality', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(masterKey)
          .mockResolvedValueOnce(JSON.stringify(metadata));

        const integrityResult = await SecureKeyManagementService.verifyKeyIntegrity();
        
        expect(integrityResult.isIntact).toBe(true);
        expect(integrityResult.tamperingDetected).toBe(false);
        expect(integrityResult.issues).toHaveLength(0);
      });
    });

    describe('Passphrase Protection Tests', () => {
      it('should encrypt keys with user passphrase', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        const passphrase = 'user_secure_passphrase_123!';
        
        await SecureKeyManagementService.storeSecureMasterKey(masterKey, metadata, passphrase);
        
        const storedKeyCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('master_key')
        );
        
        const storedKey = storedKeyCall[1];
        
        // Verify key is encrypted (different from original and contains encryption metadata)
        expect(storedKey).not.toBe(masterKey);
        
        const parsedKey = JSON.parse(storedKey);
        expect(parsedKey.encrypted).toBeDefined();
        expect(parsedKey.salt).toBeDefined();
        expect(parsedKey.iterations).toBe(100000);
      });

      it('should retrieve passphrase-protected keys', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        const passphrase = 'test_passphrase_456';
        
        // First store with passphrase
        await SecureKeyManagementService.storeSecureMasterKey(masterKey, metadata, passphrase);
        
        // Get the encrypted key that was stored
        const storedKeyCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('master_key')
        );
        const encryptedKey = storedKeyCall[1];
        
        // Mock retrieval
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(encryptedKey)
          .mockResolvedValueOnce(JSON.stringify(metadata));

        const retrieved = await SecureKeyManagementService.retrieveSecureMasterKey(passphrase);
        
        expect(retrieved).toBeDefined();
        expect(retrieved?.masterKey).toBe(masterKey);
        expect(retrieved?.metadata.keyId).toBe(metadata.keyId);
      });

      it('should reject invalid passphrases', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        const correctPassphrase = 'correct_passphrase';
        const wrongPassphrase = 'wrong_passphrase';
        
        await SecureKeyManagementService.storeSecureMasterKey(masterKey, metadata, correctPassphrase);
        
        const storedKeyCall = mockAsyncStorage.setItem.mock.calls.find(
          call => call[0].includes('master_key')
        );
        const encryptedKey = storedKeyCall[1];
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(encryptedKey)
          .mockResolvedValueOnce(JSON.stringify(metadata));

        const retrieved = await SecureKeyManagementService.retrieveSecureMasterKey(wrongPassphrase);
        
        expect(retrieved).toBeNull();
      });
    });

    describe('Secure Wipe Tests', () => {
      it('should completely wipe all key data', async () => {
        mockAsyncStorage.getAllKeys.mockResolvedValue([
          'safekeep_secure_master_key',
          'safekeep_secure_key_metadata',
          'safekeep_secure_key_backup_key1_123',
          'safekeep_secure_key_backup_key2_456',
          'other_unrelated_key'
        ]);

        await SecureKeyManagementService.secureKeyWipe();
        
        // Verify all key-related items are removed
        expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('safekeep_secure_master_key');
        expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('safekeep_secure_key_metadata');
        expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('safekeep_secure_key_backup_key1_123');
        expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('safekeep_secure_key_backup_key2_456');
        
        // Verify unrelated keys are not removed
        expect(mockAsyncStorage.removeItem).not.toHaveBeenCalledWith('other_unrelated_key');
      });

      it('should handle wipe errors gracefully', async () => {
        mockAsyncStorage.getAllKeys.mockRejectedValue(new Error('Storage access failed'));
        
        await expect(SecureKeyManagementService.secureKeyWipe()).rejects.toThrow('Key wipe failed');
      });
    });

    describe('Key Age and Rotation Policy Tests', () => {
      it('should recommend rotation for old keys', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        // Set key to be 100 days old
        const oldDate = new Date();
        oldDate.setDate(oldDate.getDate() - 100);
        metadata.createdAt = oldDate;
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(masterKey)
          .mockResolvedValueOnce(JSON.stringify(metadata));

        const validation = await SecureKeyManagementService.validateKeySecurity();
        
        expect(validation.needsRotation).toBe(true);
        expect(validation.keyAge).toBeGreaterThan(90);
        expect(validation.securityLevel).toBe('medium');
        expect(validation.issues).toContain(expect.stringContaining('rotation recommended'));
      });

      it('should not recommend rotation for new keys', async () => {
        const { masterKey, metadata } = await SecureKeyManagementService.generateSecureMasterKey();
        
        mockAsyncStorage.getItem
          .mockResolvedValueOnce(masterKey)
          .mockResolvedValueOnce(JSON.stringify(metadata));

        const validation = await SecureKeyManagementService.validateKeySecurity();
        
        expect(validation.needsRotation).toBe(false);
        expect(validation.keyAge).toBe(0);
        expect(validation.securityLevel).toBe('high');
      });
    });
  });
});