import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    ActivityIndicator,
    Alert
} from 'react-native';
import { runRlsPolicyFix } from '../utils/testRlsPolicies';
import { testSupabaseConnection } from '../utils/testSupabaseConnection';
import { executeRlsFix } from '../utils/executeRlsFix';
import AuthService from '../services/AuthService';

const DatabaseSetupScreen: React.FC = () => {
    const [loading, setLoading] = useState<boolean>(false);
    const [status, setStatus] = useState<string>('');
    const [results, setResults] = useState<any>(null);

    const handleTestConnection = async () => {
        setLoading(true);
        setStatus('Testing Supabase connection...');

        try {
            const result = await testSupabaseConnection();
            setResults(result);

            if (result.success) {
                setStatus('Connection successful! ✅');
            } else {
                setStatus(`Connection failed: ${result.message} ❌`);
            }
        } catch (error) {
            setStatus(`Error: ${error instanceof Error ? error.message : String(error)} ❌`);
            setResults(null);
        } finally {
            setLoading(false);
        }
    };

    const handleFixRlsPolicies = async () => {
        setLoading(true);
        setStatus('Fixing RLS policies...');

        try {
            const result = await executeRlsFix();
            setResults(result);

            if (result.success) {
                setStatus('RLS policies fixed successfully! ✅');
                Alert.alert('Success', 'RLS policies have been fixed successfully.');
            } else {
                setStatus(`Failed to fix RLS policies: ${result.message} ❌`);
                Alert.alert('Error', `Failed to fix RLS policies: ${result.message}`);
            }
        } catch (error) {
            setStatus(`Error: ${error instanceof Error ? error.message : String(error)} ❌`);
            setResults(null);
            Alert.alert('Error', `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
        } finally {
            setLoading(false);
        }
    };

    const handleFixWithAuthService = async () => {
        setLoading(true);
        setStatus('Fixing RLS policies using AuthService...');

        try {
            const result = await AuthService.fixRlsPolicies();
            setResults(result);

            if (result.success) {
                setStatus('RLS policies fixed successfully! ✅');
                Alert.alert('Success', 'RLS policies have been fixed successfully.');
            } else {
                setStatus(`Failed to fix RLS policies: ${result.message} ❌`);
                Alert.alert('Error', `Failed to fix RLS policies: ${result.message}`);
            }
        } catch (error) {
            setStatus(`Error: ${error instanceof Error ? error.message : String(error)} ❌`);
            setResults(null);
            Alert.alert('Error', `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
        } finally {
            setLoading(false);
        }
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>Database Setup</Text>
                <Text style={styles.subtitle}>
                    Configure and test your Supabase database connection
                </Text>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Connection Status</Text>
                <TouchableOpacity
                    style={styles.button}
                    onPress={handleTestConnection}
                    disabled={loading}
                >
                    <Text style={styles.buttonText}>Test Connection</Text>
                </TouchableOpacity>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>RLS Policy Fix</Text>
                <Text style={styles.description}>
                    If you're experiencing issues with Row Level Security policies, use the buttons below to fix them.
                </Text>

                <TouchableOpacity
                    style={styles.button}
                    onPress={handleFixRlsPolicies}
                    disabled={loading}
                >
                    <Text style={styles.buttonText}>Fix RLS Policies (Direct)</Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[styles.button, { marginTop: 10 }]}
                    onPress={handleFixWithAuthService}
                    disabled={loading}
                >
                    <Text style={styles.buttonText}>Fix RLS Policies (Auth Service)</Text>
                </TouchableOpacity>
            </View>

            {loading && (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#0066cc" />
                    <Text style={styles.loadingText}>{status}</Text>
                </View>
            )}

            {!loading && status && (
                <View style={styles.statusContainer}>
                    <Text style={styles.statusText}>{status}</Text>
                </View>
            )}

            {results && (
                <View style={styles.resultsContainer}>
                    <Text style={styles.resultsTitle}>Results:</Text>
                    <Text style={styles.resultsText}>
                        {JSON.stringify(results, null, 2)}
                    </Text>
                </View>
            )}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        padding: 16,
    },
    header: {
        marginBottom: 24,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 16,
        color: '#666',
    },
    section: {
        backgroundColor: '#fff',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
        color: '#333',
    },
    description: {
        fontSize: 14,
        color: '#666',
        marginBottom: 16,
    },
    button: {
        backgroundColor: '#0066cc',
        borderRadius: 8,
        padding: 12,
        alignItems: 'center',
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        padding: 16,
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 8,
        fontSize: 14,
        color: '#666',
    },
    statusContainer: {
        padding: 16,
        backgroundColor: '#fff',
        borderRadius: 8,
        marginBottom: 16,
    },
    statusText: {
        fontSize: 16,
        color: '#333',
    },
    resultsContainer: {
        padding: 16,
        backgroundColor: '#fff',
        borderRadius: 8,
        marginBottom: 16,
    },
    resultsTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 8,
        color: '#333',
    },
    resultsText: {
        fontSize: 14,
        color: '#333',
        fontFamily: 'monospace',
    },
});

export default DatabaseSetupScreen;