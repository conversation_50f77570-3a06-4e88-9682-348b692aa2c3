import { Request, Response } from 'express';
import { SubscriptionController } from '../SubscriptionController';
import { SubscriptionManager } from '../../services/SubscriptionManager';

// Mock the SubscriptionManager
jest.mock('../../services/SubscriptionManager');

describe('SubscriptionController', () => {
  let subscriptionController: SubscriptionController;
  let mockSubscriptionManager: jest.Mocked<SubscriptionManager>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock response
    mockResponse = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    };

    // Create controller instance
    subscriptionController = new SubscriptionController();
    
    // Get the mocked SubscriptionManager instance
    mockSubscriptionManager = (subscriptionController as any).subscriptionManager;
  });

  describe('createSubscription', () => {
    it('should create subscription successfully', async () => {
      mockRequest = {
        body: {
          userId: 'user-123',
          serviceIds: ['contacts', 'messages'],
          paymentMethodId: 'pm_123'
        }
      };

      const mockSubscription = {
        id: 'sub-123',
        userId: 'user-123',
        planId: 'standard',
        serviceIds: ['contacts', 'messages'],
        totalPriceCents: 1999,
        status: 'active' as const,
        stripeSubscriptionId: 'stripe-sub-123',
        stripeCustomerId: 'cus-123',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockSubscriptionManager.createSubscription.mockResolvedValue(mockSubscription);

      await subscriptionController.createSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockSubscriptionManager.createSubscription).toHaveBeenCalledWith({
        userId: 'user-123',
        serviceIds: ['contacts', 'messages'],
        paymentMethodId: 'pm_123'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockSubscription
      });
    });

    it('should return 400 error when userId is missing', async () => {
      mockRequest = {
        body: {
          serviceIds: ['contacts', 'messages']
        }
      };

      await subscriptionController.createSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds is empty', async () => {
      mockRequest = {
        body: {
          userId: 'user-123',
          serviceIds: []
        }
      };

      await subscriptionController.createSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error when serviceIds contains invalid values', async () => {
      mockRequest = {
        body: {
          userId: 'user-123',
          serviceIds: ['contacts', '', 'messages']
        }
      };

      await subscriptionController.createSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'All service IDs must be non-empty strings',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 400 error for invalid service combination', async () => {
      mockRequest = {
        body: {
          userId: 'user-123',
          serviceIds: ['invalid-service']
        }
      };

      const error = new Error('Invalid service combination: Service does not exist');
      mockSubscriptionManager.createSubscription.mockRejectedValue(error);

      await subscriptionController.createSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_SERVICE_COMBINATION',
          message: 'Invalid service combination: Service does not exist',
          details: { message: 'Invalid service combination: Service does not exist' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 409 error when user already has active subscription', async () => {
      mockRequest = {
        body: {
          userId: 'user-123',
          serviceIds: ['contacts']
        }
      };

      const error = new Error('User already has an active subscription. Use updateSubscription to modify services.');
      mockSubscriptionManager.createSubscription.mockRejectedValue(error);

      await subscriptionController.createSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SUBSCRIPTION_EXISTS',
          message: 'User already has an active subscription. Use updateSubscription to modify services.',
          details: { message: 'User already has an active subscription. Use updateSubscription to modify services.' },
          timestamp: expect.any(String)
        }
      });
    });
  });

  describe('updateSubscription', () => {
    it('should update subscription successfully', async () => {
      mockRequest = {
        params: { subscriptionId: 'sub-123' },
        body: { serviceIds: ['contacts', 'messages', 'photos'] }
      };

      const mockUpdatedSubscription = {
        id: 'sub-123',
        userId: 'user-123',
        planId: 'premium',
        serviceIds: ['contacts', 'messages', 'photos'],
        totalPriceCents: 2999,
        status: 'active' as const,
        stripeSubscriptionId: 'stripe-sub-123',
        stripeCustomerId: 'cus-123',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockSubscriptionManager.updateSubscription.mockResolvedValue(mockUpdatedSubscription);

      await subscriptionController.updateSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockSubscriptionManager.updateSubscription).toHaveBeenCalledWith('sub-123', ['contacts', 'messages', 'photos']);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockUpdatedSubscription
      });
    });

    it('should return 400 error when subscriptionId is missing', async () => {
      mockRequest = {
        params: {},
        body: { serviceIds: ['contacts'] }
      };

      await subscriptionController.updateSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid Subscription ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 404 error when subscription not found', async () => {
      mockRequest = {
        params: { subscriptionId: 'non-existent' },
        body: { serviceIds: ['contacts'] }
      };

      const error = new Error('Subscription not found: No subscription found');
      mockSubscriptionManager.updateSubscription.mockRejectedValue(error);

      await subscriptionController.updateSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SUBSCRIPTION_NOT_FOUND',
          message: 'Subscription not found: No subscription found',
          details: { message: 'Subscription not found: No subscription found' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 409 error when trying to update inactive subscription', async () => {
      mockRequest = {
        params: { subscriptionId: 'sub-123' },
        body: { serviceIds: ['contacts'] }
      };

      const error = new Error('Cannot update inactive subscription');
      mockSubscriptionManager.updateSubscription.mockRejectedValue(error);

      await subscriptionController.updateSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SUBSCRIPTION_INACTIVE',
          message: 'Cannot update inactive subscription',
          details: { message: 'Cannot update inactive subscription' },
          timestamp: expect.any(String)
        }
      });
    });
  });

  describe('getSubscriptionDetails', () => {
    it('should get subscription details successfully', async () => {
      mockRequest = {
        params: { userId: 'user-123' }
      };

      const mockSubscriptionDetails = {
        subscriptionId: 'sub-123',
        planId: 'standard',
        planName: 'Standard Plan',
        currentPriceCents: 1999,
        status: 'active' as const,
        services: ['contacts', 'messages'],
        storageQuotaGb: 10,
        storageUsedGb: 2.5,
        nextBillingDate: new Date()
      };

      mockSubscriptionManager.getSubscriptionDetails.mockResolvedValue(mockSubscriptionDetails);

      await subscriptionController.getSubscriptionDetails(mockRequest as Request, mockResponse as Response);

      expect(mockSubscriptionManager.getSubscriptionDetails).toHaveBeenCalledWith('user-123');
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockSubscriptionDetails
      });
    });

    it('should return 400 error when userId is missing', async () => {
      mockRequest = { params: {} };

      await subscriptionController.getSubscriptionDetails(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 404 error when no subscription found', async () => {
      mockRequest = {
        params: { userId: 'user-123' }
      };

      mockSubscriptionManager.getSubscriptionDetails.mockResolvedValue(null);

      await subscriptionController.getSubscriptionDetails(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SUBSCRIPTION_NOT_FOUND',
          message: 'No active subscription found for this user',
          timestamp: expect.any(String)
        }
      });
    });

    it('should trim whitespace from userId', async () => {
      mockRequest = {
        params: { userId: '  user-123  ' }
      };

      const mockSubscriptionDetails = {
        subscriptionId: 'sub-123',
        planId: 'standard',
        planName: 'Standard Plan',
        currentPriceCents: 1999,
        status: 'active' as const,
        services: ['contacts'],
        storageQuotaGb: 5,
        storageUsedGb: 1,
        nextBillingDate: new Date()
      };

      mockSubscriptionManager.getSubscriptionDetails.mockResolvedValue(mockSubscriptionDetails);

      await subscriptionController.getSubscriptionDetails(mockRequest as Request, mockResponse as Response);

      expect(mockSubscriptionManager.getSubscriptionDetails).toHaveBeenCalledWith('user-123');
    });
  });

  describe('cancelSubscription', () => {
    it('should cancel subscription successfully', async () => {
      mockRequest = {
        params: { subscriptionId: 'sub-123' }
      };

      mockSubscriptionManager.cancelSubscription.mockResolvedValue(true);

      await subscriptionController.cancelSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockSubscriptionManager.cancelSubscription).toHaveBeenCalledWith('sub-123');
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: { cancelled: true }
      });
    });

    it('should return 400 error when subscriptionId is missing', async () => {
      mockRequest = { params: {} };

      await subscriptionController.cancelSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid Subscription ID is required',
          timestamp: expect.any(String)
        }
      });
    });

    it('should return 404 error when subscription not found', async () => {
      mockRequest = {
        params: { subscriptionId: 'non-existent' }
      };

      const error = new Error('Subscription not found: No subscription found');
      mockSubscriptionManager.cancelSubscription.mockRejectedValue(error);

      await subscriptionController.cancelSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SUBSCRIPTION_NOT_FOUND',
          message: 'Subscription not found: No subscription found',
          details: { message: 'Subscription not found: No subscription found' },
          timestamp: expect.any(String)
        }
      });
    });

    it('should trim whitespace from subscriptionId', async () => {
      mockRequest = {
        params: { subscriptionId: '  sub-123  ' }
      };

      mockSubscriptionManager.cancelSubscription.mockResolvedValue(true);

      await subscriptionController.cancelSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockSubscriptionManager.cancelSubscription).toHaveBeenCalledWith('sub-123');
    });

    it('should handle general errors', async () => {
      mockRequest = {
        params: { subscriptionId: 'sub-123' }
      };

      const error = new Error('Database connection failed');
      mockSubscriptionManager.cancelSubscription.mockRejectedValue(error);

      await subscriptionController.cancelSubscription(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SUBSCRIPTION_CANCELLATION_ERROR',
          message: 'Failed to cancel subscription',
          details: { message: 'Database connection failed' },
          timestamp: expect.any(String)
        }
      });
    });
  });
});