"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionManager = void 0;
class SubscriptionManager {
    async createSubscription(subscriptionRequest) {
        return {
            id: '',
            userId: subscriptionRequest.userId,
            planId: '',
            serviceIds: subscriptionRequest.serviceIds,
            totalPriceCents: 0,
            status: 'active',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }
    async updateSubscription(subscriptionId, serviceIds) {
        return {
            id: subscriptionId,
            userId: '',
            planId: '',
            serviceIds: serviceIds,
            totalPriceCents: 0,
            status: 'active',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }
    async getSubscriptionDetails(userId) {
        return null;
    }
    async cancelSubscription(subscriptionId) {
        return false;
    }
}
exports.SubscriptionManager = SubscriptionManager;
//# sourceMappingURL=SubscriptionManager.js.map