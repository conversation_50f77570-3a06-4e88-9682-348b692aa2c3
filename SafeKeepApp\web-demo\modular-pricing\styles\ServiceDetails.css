/* ServiceDetails Component Styles */

.service-details {
    margin-top: 12px;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.service-details.expanded {
    background: #f8f9ff;
    border: 1px solid #e3f2fd;
}

.service-details.collapsed {
    background: transparent;
}

/* Toggle button */
.service-details-toggle {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    color: #4facfe;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 6px;
}

.service-details-toggle:hover {
    background: rgba(79, 172, 254, 0.1);
    color: #2196f3;
}

.service-details-toggle:focus {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
}

.toggle-icon {
    font-size: 12px;
    transition: transform 0.3s ease;
    width: 12px;
    text-align: center;
}

.service-details.expanded .toggle-icon {
    transform: rotate(0deg);
}

.service-details.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.toggle-text {
    flex: 1;
    text-align: left;
}

/* Content container */
.service-details-content {
    height: 0;
    overflow: hidden;
    transition: height 0.3s ease;
    padding: 0 16px;
}

.service-details.expanded .service-details-content {
    padding: 0 16px 16px 16px;
}

/* Features section */
.features-section {
    margin-bottom: 20px;
}

.features-title {
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.features-title::before {
    content: '📋';
    font-size: 16px;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    gap: 8px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border-left: 3px solid #28a745;
    transition: all 0.2s ease;
}

.feature-item:hover {
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-check {
    color: #28a745;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    background: #e8f5e8;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1px;
}

.feature-text {
    color: #333;
    font-size: 0.9rem;
    line-height: 1.4;
    flex: 1;
}

/* Info section */
.info-section {
    margin-bottom: 8px;
}

.info-title {
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-title::before {
    content: 'ℹ️';
    font-size: 16px;
}

.info-list {
    display: grid;
    gap: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.info-label {
    color: #666;
    font-size: 0.85rem;
    font-weight: 500;
}

.info-value {
    color: #333;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: right;
}

/* Mobile responsive design */
@media (max-width: 768px) {
    .service-details-toggle {
        padding: 10px 12px;
        font-size: 0.85rem;
    }
    
    .service-details-content {
        padding: 0 12px;
    }
    
    .service-details.expanded .service-details-content {
        padding: 0 12px 12px 12px;
    }
    
    .features-title,
    .info-title {
        font-size: 0.9rem;
    }
    
    .feature-item {
        padding: 6px 10px;
    }
    
    .feature-text {
        font-size: 0.85rem;
    }
    
    .info-item {
        padding: 8px 10px;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .info-value {
        text-align: left;
        font-size: 0.8rem;
    }
    
    .info-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .service-details-toggle {
        padding: 8px 10px;
        gap: 6px;
    }
    
    .toggle-icon {
        font-size: 10px;
        width: 10px;
    }
    
    .features-section,
    .info-section {
        margin-bottom: 16px;
    }
    
    .feature-item {
        padding: 6px 8px;
        gap: 8px;
    }
    
    .feature-check {
        width: 14px;
        height: 14px;
        font-size: 12px;
    }
    
    .info-item {
        padding: 6px 8px;
    }
}

/* Animation states */
.service-details.expanding .service-details-content {
    animation: expandContent 0.3s ease-out;
}

.service-details.collapsing .service-details-content {
    animation: collapseContent 0.3s ease-in;
}

@keyframes expandContent {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes collapseContent {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .service-details.expanded {
        border-width: 2px;
        border-color: #000;
    }
    
    .service-details-toggle {
        border: 1px solid #000;
    }
    
    .feature-item {
        border-left-width: 4px;
        border-left-color: #000;
    }
    
    .info-item {
        border-width: 2px;
        border-color: #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .service-details,
    .service-details-content,
    .toggle-icon,
    .feature-item {
        transition: none;
    }
    
    .service-details.expanding .service-details-content,
    .service-details.collapsing .service-details-content {
        animation: none;
    }
    
    .feature-item:hover {
        transform: none;
    }
}

/* Focus management */
.service-details:focus-within {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
    border-radius: 8px;
}

/* Loading state */
.service-details.loading .service-details-content {
    position: relative;
    overflow: hidden;
}

.service-details.loading .service-details-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.6),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}