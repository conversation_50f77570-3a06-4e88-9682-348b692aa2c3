# Java Setup Guide for SafeKeepApp

This guide provides instructions for installing and configuring Java Development Kit (JDK) 11 or newer, which is required for building the Android version of SafeKeepApp.

## Why Java 11+?

SafeKeepApp uses Gradle 8.14.1 for Android builds, which requires JDK 11 or newer. Using an older Java version will result in build failures.

## Installing JDK 11+

### Windows

1. Download JDK 11 or newer from one of these sources:
   - [Oracle JDK](https://www.oracle.com/java/technologies/javase-jdk11-downloads.html) (requires Oracle account)
   - [AdoptOpenJDK](https://adoptopenjdk.net/) (free, open source)
   - [Amazon Corretto](https://aws.amazon.com/corretto/) (free, open source)

2. Run the installer and follow the installation wizard.

3. Set the JAVA_HOME environment variable:
   - Right-click on "This PC" or "My Computer" and select "Properties"
   - Click on "Advanced system settings"
   - Click on "Environment Variables"
   - Under "System variables", click "New"
   - Variable name: `JAVA_HOME`
   - Variable value: Path to your JDK installation (e.g., `C:\Program Files\Java\jdk-11.0.12`)
   - Click "OK"

4. Add Java to your PATH:
   - In the same "Environment Variables" dialog, find the "Path" variable under "System variables"
   - Click "Edit"
   - Click "New"
   - Add `%JAVA_HOME%\bin`
   - Click "OK" on all dialogs

5. Verify installation by opening Command Prompt and typing:
   ```
   java -version
   ```
   You should see output indicating Java 11 or newer.

### macOS

1. Install using Homebrew (recommended):
   ```
   brew install openjdk@11
   ```

2. Set JAVA_HOME by adding these lines to your `~/.zshrc` or `~/.bash_profile`:
   ```
   export JAVA_HOME=$(/usr/libexec/java_home -v 11)
   export PATH=$JAVA_HOME/bin:$PATH
   ```

3. Apply the changes:
   ```
   source ~/.zshrc
   ```
   or
   ```
   source ~/.bash_profile
   ```

4. Verify installation:
   ```
   java -version
   ```

### Linux

#### Ubuntu/Debian:

1. Install OpenJDK 11:
   ```
   sudo apt update
   sudo apt install openjdk-11-jdk
   ```

2. Set JAVA_HOME by adding these lines to your `~/.bashrc`:
   ```
   export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
   export PATH=$JAVA_HOME/bin:$PATH
   ```

3. Apply the changes:
   ```
   source ~/.bashrc
   ```

4. Verify installation:
   ```
   java -version
   ```

#### Fedora/CentOS/RHEL:

1. Install OpenJDK 11:
   ```
   sudo dnf install java-11-openjdk-devel
   ```

2. Set JAVA_HOME by adding these lines to your `~/.bashrc`:
   ```
   export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
   export PATH=$JAVA_HOME/bin:$PATH
   ```

3. Apply the changes:
   ```
   source ~/.bashrc
   ```

4. Verify installation:
   ```
   java -version
   ```

## Configuring Android Studio to Use JDK 11+

1. Open Android Studio
2. Go to File > Settings (on Windows/Linux) or Android Studio > Preferences (on macOS)
3. Navigate to Build, Execution, Deployment > Build Tools > Gradle
4. In the "Gradle JDK" dropdown, select your installed JDK 11 or newer
5. Click "Apply" and "OK"

## Troubleshooting

If you encounter build errors related to Java version:

1. Verify your Java version:
   ```
   java -version
   ```

2. Make sure JAVA_HOME is correctly set:
   - Windows: `echo %JAVA_HOME%`
   - macOS/Linux: `echo $JAVA_HOME`

3. Ensure Android Studio is using the correct JDK version in the Gradle settings.

4. If you have multiple Java versions installed, make sure your PATH is set to prioritize JDK 11+.

## Additional Resources

- [JDK 11 Documentation](https://docs.oracle.com/en/java/javase/11/)
- [Gradle Compatibility Matrix](https://docs.gradle.org/current/userguide/compatibility.html)
- [Android Studio Configuration Guide](https://developer.android.com/studio/intro/studio-config)