/**
 * PricingCalculator Component
 * Handles pricing calculations and displays dynamic pricing
 */

class PricingCalculator {
    /**
     * @param {Object} props - Component properties
     * @param {string[]} props.selectedServices - Array of selected service IDs
     * @param {Function} props.onPricingUpdate - Callback when pricing is updated
     * @param {boolean} props.animateChanges - Whether to animate price changes
     * @param {string} props.apiBaseUrl - Base URL for API calls
     */
    constructor(props) {
        this.props = props;
        this.element = null;
        this.currentPricing = null;
        this.isLoading = false;
        this.cache = new Map();
        
        // Debounced calculation function
        this.debouncedCalculate = window.ModularPricingUtils.debounce(
            this.calculatePricing.bind(this), 
            500
        );
        
        this.render();
        this.calculatePricing();
    }

    /**
     * Render the pricing calculator component
     */
    render() {
        this.element = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-calculator'
        });

        // Create header
        const header = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-header'
        });

        const title = window.ModularPricingUtils.createElement('h3', {
            className: 'pricing-title'
        }, 'Your Plan Pricing');

        header.appendChild(title);

        // Create pricing display
        const pricingDisplay = this.createPricingDisplay();

        // Create loading indicator
        const loadingIndicator = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-loading hidden'
        });

        const spinner = window.ModularPricingUtils.createElement('div', {
            className: 'loading-spinner'
        });

        const loadingText = window.ModularPricingUtils.createElement('span', {
            className: 'loading-text'
        }, 'Calculating pricing...');

        loadingIndicator.appendChild(spinner);
        loadingIndicator.appendChild(loadingText);

        // Create error display
        const errorDisplay = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-error hidden'
        });

        this.element.appendChild(header);
        this.element.appendChild(pricingDisplay);
        this.element.appendChild(loadingIndicator);
        this.element.appendChild(errorDisplay);
    }

    /**
     * Create pricing display section
     * @returns {HTMLElement} Pricing display element
     */
    createPricingDisplay() {
        const display = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-display'
        });

        // Main price
        const mainPrice = window.ModularPricingUtils.createElement('div', {
            className: 'main-price'
        });

        const priceLabel = window.ModularPricingUtils.createElement('span', {
            className: 'price-label'
        }, 'Total Monthly Cost');

        const priceValue = window.ModularPricingUtils.createElement('span', {
            className: 'price-value',
            id: 'main-price-value'
        }, '$0.00');

        const priceFrequency = window.ModularPricingUtils.createElement('span', {
            className: 'price-frequency'
        }, '/month');

        mainPrice.appendChild(priceLabel);
        mainPrice.appendChild(priceValue);
        mainPrice.appendChild(priceFrequency);

        // Pricing breakdown
        const breakdown = window.ModularPricingUtils.createElement('div', {
            className: 'pricing-breakdown'
        });

        const breakdownTitle = window.ModularPricingUtils.createElement('h4', {
            className: 'breakdown-title'
        }, 'Pricing Breakdown');

        const breakdownList = window.ModularPricingUtils.createElement('div', {
            className: 'breakdown-list',
            id: 'pricing-breakdown-list'
        });

        breakdown.appendChild(breakdownTitle);
        breakdown.appendChild(breakdownList);

        // Savings indicator
        const savingsIndicator = window.ModularPricingUtils.createElement('div', {
            className: 'savings-indicator hidden',
            id: 'savings-indicator'
        });

        display.appendChild(mainPrice);
        display.appendChild(breakdown);
        display.appendChild(savingsIndicator);

        return display;
    }

    /**
     * Calculate pricing for selected services
     */
    async calculatePricing() {
        const { selectedServices } = this.props;
        
        // Check cache first
        const cacheKey = selectedServices.sort().join(',');
        if (this.cache.has(cacheKey)) {
            this.updatePricingDisplay(this.cache.get(cacheKey));
            return;
        }

        // Show loading state
        this.setLoadingState(true);

        try {
            let pricingResult;

            if (selectedServices.length === 0) {
                // No services selected
                pricingResult = {
                    recommendedPlanId: null,
                    recommendedPlanName: 'No Services',
                    totalPrice: 0,
                    individualTotal: 0,
                    savings: 0,
                    currency: 'USD',
                    breakdown: []
                };
            } else {
                // Calculate pricing based on configuration
                pricingResult = this.calculateLocalPricing(selectedServices);
                
                // Try to get pricing from API if available
                try {
                    const apiResult = await this.fetchPricingFromAPI(selectedServices);
                    if (apiResult) {
                        pricingResult = apiResult;
                    }
                } catch (apiError) {
                    console.warn('API pricing failed, using local calculation:', apiError);
                }
            }

            // Cache the result
            this.cache.set(cacheKey, pricingResult);

            // Update display
            this.updatePricingDisplay(pricingResult);

            // Notify parent component
            if (this.props.onPricingUpdate) {
                this.props.onPricingUpdate(pricingResult);
            }

        } catch (error) {
            this.showError('Failed to calculate pricing. Please try again.');
            console.error('Pricing calculation error:', error);
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * Calculate pricing locally using configuration
     * @param {string[]} serviceIds - Selected service IDs
     * @returns {Object} Pricing result
     */
    calculateLocalPricing(serviceIds) {
        const { SERVICE_CONFIG, PRICING_PLANS } = window.ModularPricingConfig;
        
        // Calculate individual total
        const individualTotal = serviceIds.reduce((total, serviceId) => {
            const service = SERVICE_CONFIG[serviceId];
            return total + (service ? service.individualPrice : 0);
        }, 0);

        // Find best plan
        let bestPlan = null;
        let bestPrice = individualTotal;

        // Check combination plans
        Object.entries(PRICING_PLANS.combinations).forEach(([planKey, plan]) => {
            const planServices = plan.services || [];
            const hasAllServices = serviceIds.every(id => planServices.includes(id));
            const hasExtraServices = planServices.some(id => !serviceIds.includes(id));
            
            if (hasAllServices && !hasExtraServices && plan.price < bestPrice) {
                bestPlan = {
                    id: planKey,
                    name: plan.name,
                    price: plan.price,
                    isPopular: plan.isPopular || false
                };
                bestPrice = plan.price;
            }
        });

        // Create breakdown
        const breakdown = serviceIds.map(serviceId => {
            const service = SERVICE_CONFIG[serviceId];
            return {
                name: service.name,
                price: service.individualPrice,
                included: true
            };
        });

        return {
            recommendedPlanId: bestPlan ? bestPlan.id : null,
            recommendedPlanName: bestPlan ? bestPlan.name : 'Individual Services',
            totalPrice: bestPrice,
            individualTotal: individualTotal,
            savings: individualTotal - bestPrice,
            currency: 'USD',
            breakdown: breakdown,
            isPopular: bestPlan ? bestPlan.isPopular : false
        };
    }

    /**
     * Fetch pricing from API
     * @param {string[]} serviceIds - Selected service IDs
     * @returns {Promise<Object>} Pricing result from API
     */
    async fetchPricingFromAPI(serviceIds) {
        const { API_CONFIG } = window.ModularPricingConfig;
        const baseUrl = this.props.apiBaseUrl || API_CONFIG.baseUrl;
        
        const response = await fetch(`${baseUrl}${API_CONFIG.endpoints.pricing}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ serviceIds })
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Update pricing display with new data
     * @param {Object} pricingResult - Pricing calculation result
     */
    updatePricingDisplay(pricingResult) {
        this.currentPricing = pricingResult;

        // Update main price
        const priceElement = document.getElementById('main-price-value');
        if (priceElement) {
            const newPrice = window.ModularPricingUtils.formatPrice(pricingResult.totalPrice);
            
            if (this.props.animateChanges && priceElement.textContent !== newPrice) {
                const oldPrice = this.parsePrice(priceElement.textContent);
                window.ModularPricingUtils.animateNumber(
                    priceElement, 
                    oldPrice, 
                    pricingResult.totalPrice
                );
            } else {
                priceElement.textContent = newPrice;
            }
        }

        // Update breakdown
        this.updateBreakdown(pricingResult.breakdown || []);

        // Update savings indicator
        this.updateSavingsIndicator(pricingResult.savings);

        // Add visual feedback
        if (this.props.animateChanges) {
            window.ModularPricingUtils.addAnimatedClass(this.element, 'pricing-updated', 500);
        }
    }

    /**
     * Update pricing breakdown
     * @param {Array} breakdown - Breakdown items
     */
    updateBreakdown(breakdown) {
        const breakdownList = document.getElementById('pricing-breakdown-list');
        if (!breakdownList) return;

        // Clear existing breakdown
        breakdownList.innerHTML = '';

        if (breakdown.length === 0) {
            const emptyMessage = window.ModularPricingUtils.createElement('p', {
                className: 'breakdown-empty'
            }, 'Select services to see pricing breakdown');
            
            breakdownList.appendChild(emptyMessage);
            return;
        }

        // Add breakdown items
        breakdown.forEach(item => {
            const breakdownItem = window.ModularPricingUtils.createElement('div', {
                className: 'breakdown-item'
            });

            const itemName = window.ModularPricingUtils.createElement('span', {
                className: 'breakdown-item-name'
            }, item.name);

            const itemPrice = window.ModularPricingUtils.createElement('span', {
                className: 'breakdown-item-price'
            }, window.ModularPricingUtils.formatPrice(item.price));

            breakdownItem.appendChild(itemName);
            breakdownItem.appendChild(itemPrice);
            breakdownList.appendChild(breakdownItem);
        });
    }

    /**
     * Update savings indicator
     * @param {number} savings - Savings amount in cents
     */
    updateSavingsIndicator(savings) {
        const savingsElement = document.getElementById('savings-indicator');
        if (!savingsElement) return;

        if (savings > 0) {
            const savingsAmount = window.ModularPricingUtils.formatPrice(savings);
            const savingsPercentage = this.currentPricing ? 
                window.ModularPricingUtils.calculateSavingsPercentage(
                    this.currentPricing.individualTotal, 
                    this.currentPricing.totalPrice
                ) : 0;

            savingsElement.innerHTML = `
                <div class="savings-content">
                    <span class="savings-label">You Save</span>
                    <span class="savings-amount">${savingsAmount}</span>
                    <span class="savings-percentage">(${savingsPercentage}% off)</span>
                </div>
            `;
            
            savingsElement.classList.remove('hidden');
            
            if (this.props.animateChanges) {
                window.ModularPricingUtils.addAnimatedClass(savingsElement, 'savings-highlight', 1000);
            }
        } else {
            savingsElement.classList.add('hidden');
        }
    }

    /**
     * Set loading state
     * @param {boolean} loading - Whether component is loading
     */
    setLoadingState(loading) {
        this.isLoading = loading;
        
        const loadingElement = this.element.querySelector('.pricing-loading');
        const displayElement = this.element.querySelector('.pricing-display');
        
        if (loading) {
            loadingElement.classList.remove('hidden');
            displayElement.classList.add('loading');
        } else {
            loadingElement.classList.add('hidden');
            displayElement.classList.remove('loading');
        }
    }

    /**
     * Show error message
     * @param {string} message - Error message to display
     */
    showError(message) {
        const errorElement = this.element.querySelector('.pricing-error');
        if (errorElement) {
            errorElement.innerHTML = `
                <div class="error-content">
                    <span class="error-icon">⚠️</span>
                    <span class="error-message">${message}</span>
                    <button class="error-retry" onclick="this.calculatePricing()">Retry</button>
                </div>
            `;
            errorElement.classList.remove('hidden');
        }
    }

    /**
     * Parse price string to cents
     * @param {string} priceString - Price string like "$9.99"
     * @returns {number} Price in cents
     */
    parsePrice(priceString) {
        const match = priceString.match(/[\d.]+/);
        return match ? Math.round(parseFloat(match[0]) * 100) : 0;
    }

    /**
     * Update component props
     * @param {Object} newProps - New properties
     */
    updateProps(newProps) {
        const oldProps = this.props;
        this.props = { ...this.props, ...newProps };

        // Recalculate if services changed
        if (JSON.stringify(oldProps.selectedServices) !== JSON.stringify(this.props.selectedServices)) {
            this.debouncedCalculate();
        }
    }

    /**
     * Get current pricing result
     * @returns {Object|null} Current pricing result
     */
    getCurrentPricing() {
        return this.currentPricing;
    }

    /**
     * Clear pricing cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get the DOM element
     * @returns {HTMLElement} The component's DOM element
     */
    getElement() {
        return this.element;
    }

    /**
     * Destroy the component and clean up
     */
    destroy() {
        this.cache.clear();
        
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        this.element = null;
        this.currentPricing = null;
    }
}

// Export component
if (typeof window !== 'undefined') {
    window.PricingCalculator = PricingCalculator;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = PricingCalculator;
}