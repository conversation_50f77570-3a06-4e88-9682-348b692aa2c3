/**
 * Demo State Manager
 * Handles demo state persistence, recovery, and reset functionality
 */

class DemoStateManager {
    constructor() {
        this.currentState = {};
        this.defaultState = {};
        this.stateHistory = [];
        this.maxHistorySize = 10;
        this.autoSaveInterval = 5000; // 5 seconds
        this.autoSaveTimer = null;
        
        this.initializeDefaultState();
        this.loadState();
        this.startAutoSave();
    }

    initializeDefaultState() {
        this.defaultState = {
            user: {
                id: null,
                email: null,
                isAuthenticated: false,
                isGuest: false,
                profile: {
                    name: '',
                    preferences: {
                        theme: 'light',
                        notifications: true,
                        autoBackup: true
                    }
                }
            },
            demo: {
                currentStep: 0,
                completedSteps: [],
                tutorialActive: false,
                featuresExplored: [],
                sessionStartTime: null,
                totalTimeSpent: 0
            },
            backup: {
                sessions: [],
                currentSession: null,
                totalBackups: 0,
                totalDataBackedUp: 0,
                lastBackupTime: null
            },
            restore: {
                sessions: [],
                currentSession: null,
                totalRestores: 0,
                lastRestoreTime: null
            },
            encryption: {
                demonstrations: [],
                algorithmsUsed: [],
                totalEncryptions: 0,
                performanceMetrics: []
            },
            subscription: {
                currentTier: 'free',
                status: 'active',
                features: [],
                usageThisPeriod: {
                    storage: 0,
                    backups: 0,
                    restores: 0
                },
                billingHistory: []
            },
            ui: {
                theme: 'light',
                sidebarCollapsed: false,
                activeTab: 'dashboard',
                notifications: [],
                errors: []
            },
            performance: {
                sessionCount: 0,
                averageSessionDuration: 0,
                featuresUsed: {},
                errorCount: 0,
                lastErrorTime: null
            }
        };
    }

    loadState() {
        try {
            // Load from localStorage
            const savedState = localStorage.getItem('safekeep_demo_state');
            if (savedState) {
                const parsedState = JSON.parse(savedState);
                this.currentState = this.mergeStates(this.defaultState, parsedState);
            } else {
                this.currentState = { ...this.defaultState };
            }

            // Load state history
            const savedHistory = localStorage.getItem('safekeep_demo_history');
            if (savedHistory) {
                this.stateHistory = JSON.parse(savedHistory);
            }

            // Update session info
            this.currentState.demo.sessionStartTime = Date.now();
            this.currentState.performance.sessionCount++;

        } catch (error) {
            console.warn('Failed to load demo state, using defaults:', error);
            this.currentState = { ...this.defaultState };
            
            if (window.errorHandler) {
                window.errorHandler.handleError(error, 'DEMO_STATE', {
                    operation: 'load_state',
                    fallback: 'default_state'
                });
            }
        }
    }

    saveState() {
        try {
            // Update total time spent
            if (this.currentState.demo.sessionStartTime) {
                const sessionDuration = Date.now() - this.currentState.demo.sessionStartTime;
                this.currentState.demo.totalTimeSpent += sessionDuration;
                this.currentState.demo.sessionStartTime = Date.now();
            }

            // Save current state
            localStorage.setItem('safekeep_demo_state', JSON.stringify(this.currentState));

            // Add to history
            this.addToHistory();

            // Save history
            localStorage.setItem('safekeep_demo_history', JSON.stringify(this.stateHistory));

        } catch (error) {
            console.warn('Failed to save demo state:', error);
            
            if (window.errorHandler) {
                window.errorHandler.handleError(error, 'DEMO_STATE', {
                    operation: 'save_state',
                    stateSize: JSON.stringify(this.currentState).length
                });
            }
        }
    }

    addToHistory() {
        const historyEntry = {
            timestamp: Date.now(),
            state: JSON.parse(JSON.stringify(this.currentState)), // Deep copy
            version: this.getStateVersion()
        };

        this.stateHistory.push(historyEntry);

        // Limit history size
        if (this.stateHistory.length > this.maxHistorySize) {
            this.stateHistory = this.stateHistory.slice(-this.maxHistorySize);
        }
    }

    getStateVersion() {
        // Simple version based on significant changes
        const significantFields = [
            'user.isAuthenticated',
            'demo.currentStep',
            'backup.totalBackups',
            'subscription.currentTier'
        ];

        let version = '';
        significantFields.forEach(field => {
            const value = this.getNestedValue(this.currentState, field);
            version += String(value).slice(0, 2);
        });

        return version;
    }

    startAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }

        this.autoSaveTimer = setInterval(() => {
            this.saveState();
        }, this.autoSaveInterval);
    }

    stopAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
    }

    // State manipulation methods
    updateState(path, value) {
        this.setNestedValue(this.currentState, path, value);
        this.saveState();
        this.notifyStateChange(path, value);
    }

    getState(path = null) {
        if (path) {
            return this.getNestedValue(this.currentState, path);
        }
        return { ...this.currentState };
    }

    mergeState(updates) {
        this.currentState = this.mergeStates(this.currentState, updates);
        this.saveState();
        this.notifyStateChange('*', updates);
    }

    // Demo-specific state methods
    setUser(user) {
        this.updateState('user', user);
    }

    setDemoStep(step) {
        this.updateState('demo.currentStep', step);
        
        // Add to completed steps if not already there
        const completedSteps = this.getState('demo.completedSteps') || [];
        if (!completedSteps.includes(step)) {
            completedSteps.push(step);
            this.updateState('demo.completedSteps', completedSteps);
        }
    }

    addFeatureExplored(feature) {
        const explored = this.getState('demo.featuresExplored') || [];
        if (!explored.includes(feature)) {
            explored.push(feature);
            this.updateState('demo.featuresExplored', explored);
        }
    }

    addBackupSession(session) {
        const sessions = this.getState('backup.sessions') || [];
        sessions.push(session);
        this.updateState('backup.sessions', sessions);
        this.updateState('backup.totalBackups', sessions.length);
        this.updateState('backup.lastBackupTime', Date.now());
    }

    addRestoreSession(session) {
        const sessions = this.getState('restore.sessions') || [];
        sessions.push(session);
        this.updateState('restore.sessions', sessions);
        this.updateState('restore.totalRestores', sessions.length);
        this.updateState('restore.lastRestoreTime', Date.now());
    }

    addEncryptionDemo(demo) {
        const demos = this.getState('encryption.demonstrations') || [];
        demos.push(demo);
        this.updateState('encryption.demonstrations', demos);
        this.updateState('encryption.totalEncryptions', demos.length);
    }

    setSubscription(tier, status = 'active') {
        this.updateState('subscription.currentTier', tier);
        this.updateState('subscription.status', status);
    }

    addNotification(notification) {
        const notifications = this.getState('ui.notifications') || [];
        notifications.push({
            ...notification,
            id: this.generateId(),
            timestamp: Date.now()
        });
        
        // Keep only last 50 notifications
        if (notifications.length > 50) {
            notifications.splice(0, notifications.length - 50);
        }
        
        this.updateState('ui.notifications', notifications);
    }

    addError(error) {
        const errors = this.getState('ui.errors') || [];
        errors.push({
            ...error,
            id: this.generateId(),
            timestamp: Date.now()
        });
        
        // Keep only last 20 errors
        if (errors.length > 20) {
            errors.splice(0, errors.length - 20);
        }
        
        this.updateState('ui.errors', errors);
        this.updateState('performance.errorCount', errors.length);
        this.updateState('performance.lastErrorTime', Date.now());
    }

    // State recovery methods
    restoreFromHistory(index) {
        if (index >= 0 && index < this.stateHistory.length) {
            const historyEntry = this.stateHistory[index];
            this.currentState = { ...historyEntry.state };
            this.saveState();
            
            if (window.errorHandler) {
                window.errorHandler.showUserNotification({
                    type: 'success',
                    title: 'State Restored',
                    message: `Demo state restored from ${new Date(historyEntry.timestamp).toLocaleString()}`,
                    autoHide: true
                });
            }
            
            return true;
        }
        return false;
    }

    restoreToDefault() {
        this.currentState = { ...this.defaultState };
        this.currentState.demo.sessionStartTime = Date.now();
        this.currentState.performance.sessionCount = 1;
        this.saveState();
        
        if (window.errorHandler) {
            window.errorHandler.showUserNotification({
                type: 'info',
                title: 'Demo Reset',
                message: 'Demo state has been reset to defaults.',
                autoHide: true
            });
        }
    }

    // Reset methods
    async reset() {
        try {
            // Stop auto-save
            this.stopAutoSave();
            
            // Clear storage
            localStorage.removeItem('safekeep_demo_state');
            localStorage.removeItem('safekeep_demo_history');
            sessionStorage.clear();
            
            // Reset managers
            await this.resetManagers();
            
            // Restore default state
            this.restoreToDefault();
            
            // Restart auto-save
            this.startAutoSave();
            
            return true;
            
        } catch (error) {
            console.error('Error during demo reset:', error);
            
            if (window.errorHandler) {
                window.errorHandler.handleError(error, 'DEMO_STATE', {
                    operation: 'reset',
                    fallback: 'force_reload'
                });
            }
            
            return false;
        }
    }

    async resetManagers() {
        const managers = [
            'authManager',
            'backupManager',
            'restoreManager',
            'realtimeManager',
            'encryptionManager',
            'subscriptionManager',
            'paymentProcessor'
        ];

        for (const managerName of managers) {
            try {
                if (window[managerName] && typeof window[managerName].reset === 'function') {
                    await window[managerName].reset();
                }
            } catch (error) {
                console.warn(`Failed to reset ${managerName}:`, error);
            }
        }
    }

    // Utility methods
    mergeStates(target, source) {
        const result = { ...target };
        
        Object.keys(source).forEach(key => {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.mergeStates(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        });
        
        return result;
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    }

    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
    }

    generateId() {
        return `state_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Event system for state changes
    notifyStateChange(path, value) {
        const event = new CustomEvent('demoStateChange', {
            detail: { path, value, state: this.currentState }
        });
        window.dispatchEvent(event);
    }

    // Public API methods
    getHistory() {
        return this.stateHistory.map(entry => ({
            timestamp: entry.timestamp,
            version: entry.version,
            date: new Date(entry.timestamp).toLocaleString()
        }));
    }

    getStats() {
        return {
            sessionCount: this.getState('performance.sessionCount'),
            totalTimeSpent: this.getState('demo.totalTimeSpent'),
            featuresExplored: this.getState('demo.featuresExplored')?.length || 0,
            backupsCompleted: this.getState('backup.totalBackups'),
            restoresCompleted: this.getState('restore.totalRestores'),
            encryptionDemos: this.getState('encryption.totalEncryptions'),
            errorCount: this.getState('performance.errorCount')
        };
    }

    exportState() {
        return {
            state: this.currentState,
            history: this.stateHistory,
            exportTime: Date.now(),
            version: '1.0'
        };
    }

    importState(exportedData) {
        try {
            if (exportedData.state) {
                this.currentState = exportedData.state;
            }
            if (exportedData.history) {
                this.stateHistory = exportedData.history;
            }
            
            this.saveState();
            
            if (window.errorHandler) {
                window.errorHandler.showUserNotification({
                    type: 'success',
                    title: 'State Imported',
                    message: 'Demo state has been imported successfully.',
                    autoHide: true
                });
            }
            
            return true;
        } catch (error) {
            if (window.errorHandler) {
                window.errorHandler.handleError(error, 'DEMO_STATE', {
                    operation: 'import_state'
                });
            }
            return false;
        }
    }

    // Cleanup
    destroy() {
        this.stopAutoSave();
        this.saveState();
    }
}

// Initialize global demo state manager
window.DemoStateManager = DemoStateManager;
window.demoManager = new DemoStateManager();

// Save state before page unload
window.addEventListener('beforeunload', () => {
    if (window.demoManager) {
        window.demoManager.saveState();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DemoStateManager;
}