-- SafeKeep Database Verification Script
-- Run this in Supabase SQL Editor to verify your setup

-- 1. Check if all tables exist
SELECT 'Tables Status:' as check_type, 
       COUNT(*) as found_tables,
       '5 expected' as expected
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status');

-- 2. List all created tables
SELECT 'Created Tables:' as check_type, table_name
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status')
ORDER BY table_name;

-- 3. Check RLS status
SELECT 'RLS Status:' as check_type,
       schemaname,
       tablename,
       rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status')
ORDER BY tablename;

-- 4. Check policies
SELECT 'Policies:' as check_type,
       schemaname,
       tablename,
       policyname,
       cmd as operation
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 5. Check triggers
SELECT 'Triggers:' as check_type,
       trigger_name,
       event_object_table as table_name,
       action_timing,
       event_manipulation
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND event_object_table IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status')
ORDER BY event_object_table, trigger_name;

-- 6. Check functions
SELECT 'Functions:' as check_type,
       routine_name as function_name,
       routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
AND routine_name IN ('handle_new_user', 'update_storage_usage', 'update_updated_at_column')
ORDER BY routine_name;

-- 7. Test user creation trigger (this will show if the trigger works)
SELECT 'Auth Integration:' as check_type,
       'Ready to test user creation' as status;

-- 8. Check indexes
SELECT 'Indexes:' as check_type,
       indexname,
       tablename
FROM pg_indexes 
WHERE schemaname = 'public'
AND tablename IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status')
ORDER BY tablename, indexname;