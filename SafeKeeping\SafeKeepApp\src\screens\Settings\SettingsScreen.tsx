import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootState } from '../../store';
import { updateSettings } from '../../store/slices/settingsSlice';
import { COLORS, FONTS, SPACING } from '../../utils/constants';
import { RootStackParamList } from '../../navigation/AppNavigator';

type SettingsScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const SettingsScreen = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const { settings } = useSelector((state: RootState) => state.settings);

  const handleToggle = (key: keyof typeof settings, value: boolean) => {
    dispatch(updateSettings({ [key]: value }));
  };

  const handleFrequencyChange = () => {
    const frequencies = ['daily', 'weekly', 'manual'] as const;
    const currentIndex = frequencies.indexOf(settings.backupFrequency);
    const nextIndex = (currentIndex + 1) % frequencies.length;
    dispatch(updateSettings({ backupFrequency: frequencies[nextIndex] }));
  };

  const handleSubscription = () => {
    navigation.navigate('Subscription');
  };

  const handleStripeTest = () => {
    Alert.alert(
      '🧪 Stripe Test',
      'This will open the Stripe payment test screen where you can test payment processing with test card numbers.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Test', onPress: openStripeTest }
      ]
    );
  };

  const openStripeTest = () => {
    // Test the backend connection first
    testBackendConnection();
  };

  const testBackendConnection = async () => {
    try {
      console.log('🔗 Testing backend connection...');

      const response = await fetch('http://localhost:3000/health');
      const data = await response.json();

      if (data.status === 'OK') {
        Alert.alert(
          '✅ Backend Connected!',
          `Server is running and Stripe is ${data.stripe_configured ? 'configured' : 'not configured'}.\n\nReady to test payments!\n\nTest Card: 4242 4242 4242 4242\nExpiry: Any future date\nCVC: Any 3 digits`,
          [{ text: 'Great!' }]
        );
      } else {
        throw new Error('Server not responding correctly');
      }
    } catch (error) {
      console.error('❌ Backend connection failed:', error);
      Alert.alert(
        '❌ Backend Connection Failed',
        'Could not connect to the test server. Please make sure:\n\n1. The test server is running (node test-server.js)\n2. Server is on http://localhost:3000\n3. No firewall blocking the connection',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>Settings</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Backup Settings</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Auto Backup</Text>
              <Text style={styles.settingDescription}>
                Automatically backup new photos and contacts
              </Text>
            </View>
            <Switch
              value={settings.autoBackup}
              onValueChange={(value) => handleToggle('autoBackup', value)}
              trackColor={{ false: COLORS.border, true: COLORS.primary }}
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>WiFi Only</Text>
              <Text style={styles.settingDescription}>
                Only backup when connected to WiFi
              </Text>
            </View>
            <Switch
              value={settings.wifiOnly}
              onValueChange={(value) => handleToggle('wifiOnly', value)}
              trackColor={{ false: COLORS.border, true: COLORS.primary }}
            />
          </View>

          <TouchableOpacity style={styles.settingRow} onPress={handleFrequencyChange}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Backup Frequency</Text>
              <Text style={styles.settingDescription}>
                How often to run automatic backups
              </Text>
            </View>
            <Text style={styles.settingValue}>
              {settings.backupFrequency.charAt(0).toUpperCase() + 
               settings.backupFrequency.slice(1)}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Push Notifications</Text>
              <Text style={styles.settingDescription}>
                Get notified about backup status
              </Text>
            </View>
            <Switch
              value={settings.notifications}
              onValueChange={(value) => handleToggle('notifications', value)}
              trackColor={{ false: COLORS.border, true: COLORS.primary }}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Security</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Biometric Authentication</Text>
              <Text style={styles.settingDescription}>
                Use fingerprint or face ID to unlock app
              </Text>
            </View>
            <Switch
              value={settings.biometricAuth}
              onValueChange={(value) => handleToggle('biometricAuth', value)}
              trackColor={{ false: COLORS.border, true: COLORS.primary }}
            />
          </View>
        </View>

        {/* Subscription Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💳 Subscription</Text>

          <TouchableOpacity style={styles.settingRow} onPress={handleSubscription}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Manage Subscription</Text>
              <Text style={styles.settingDescription}>
                View plans, upgrade, or manage your SafeKeep subscription
              </Text>
            </View>
            <Text style={styles.settingValue}>Plans →</Text>
          </TouchableOpacity>
        </View>

        {/* Development/Test Section */}
        {__DEV__ && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🧪 Development & Testing</Text>

            <TouchableOpacity style={styles.settingRow} onPress={handleStripeTest}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingLabel}>Test Stripe Integration</Text>
                <Text style={styles.settingDescription}>
                  Test payment processing with Stripe test cards
                </Text>
              </View>
              <Text style={styles.settingValue}>Test →</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
    padding: SPACING.lg,
  },
  title: {
    fontSize: FONTS.sizes.xxlarge,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.large,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  settingRow: {
    backgroundColor: COLORS.surface,
    padding: SPACING.lg,
    borderRadius: 12,
    marginBottom: SPACING.sm,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  settingInfo: {
    flex: 1,
    marginRight: SPACING.md,
  },
  settingLabel: {
    fontSize: FONTS.sizes.medium,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  settingDescription: {
    fontSize: FONTS.sizes.small,
    color: COLORS.textSecondary,
  },
  settingValue: {
    fontSize: FONTS.sizes.medium,
    color: COLORS.primary,
    fontWeight: '600',
  },
});

export default SettingsScreen;
