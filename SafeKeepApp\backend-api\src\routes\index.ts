import { Router } from 'express';
import pricingRoutes from './pricing';
import subscriptionRoutes from './subscriptions';
import serviceRoutes from './services';
import billingRoutes from './billing';

const router = Router();

/**
 * Main API routes configuration for modular pricing system
 * All routes are prefixed with /api
 */

// Mount route modules
router.use('/pricing', pricingRoutes);
router.use('/subscriptions', subscriptionRoutes);
router.use('/services', serviceRoutes);
router.use('/billing', billingRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'SafeKeep Modular Pricing API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      pricing: '/api/pricing',
      subscriptions: '/api/subscriptions',
      services: '/api/services',
      billing: '/api/billing'
    }
  });
});

export default router;