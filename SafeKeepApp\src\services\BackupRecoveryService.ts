import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupSession, BackupError, BackupRecoveryState, BackupConfiguration } from '../types/backup';
import BackupDatabaseService from './BackupDatabaseService';
import NotificationService from './NotificationService';
import RetryService from './RetryService';

export interface RecoveryOptions {
  resumeFromCheckpoint: boolean;
  retryFailedItems: boolean;
  skipFailedItems: boolean;
  restartSession: boolean;
}

export interface RecoveryResult {
  success: boolean;
  resumedSession?: BackupSession;
  recoveredItems: number;
  skippedItems: number;
  errors: BackupError[];
}

class BackupRecoveryService {
  private readonly RECOVERY_STORAGE_KEY = 'backup_recovery_state';
  private readonly SESSION_STORAGE_KEY = 'backup_session_checkpoints';
  private readonly MAX_RECOVERY_ATTEMPTS = 3;

  /**
   * Save recovery checkpoint during backup
   */
  async saveCheckpoint(
    sessionId: string,
    dataType: 'contacts' | 'messages' | 'photos',
    itemIndex: number,
    failedItems: BackupError[] = []
  ): Promise<void> {
    try {
      const recoveryState: BackupRecoveryState = {
        sessionId,
        resumable: true,
        lastCheckpoint: {
          dataType,
          itemIndex,
          timestamp: new Date()
        },
        failedItems,
        retryableErrors: failedItems.filter(error => error.retryable)
      };

      await AsyncStorage.setItem(
        `${this.RECOVERY_STORAGE_KEY}_${sessionId}`,
        JSON.stringify(recoveryState)
      );

      console.log(`💾 Saved recovery checkpoint: ${dataType} at index ${itemIndex}`);
    } catch (error) {
      console.error('Failed to save recovery checkpoint:', error);
    }
  }

  /**
   * Check if there's a resumable backup session
   */
  async checkForResumableSession(): Promise<BackupRecoveryState | null> {
    try {
      // Get all recovery states
      const keys = await AsyncStorage.getAllKeys();
      const recoveryKeys = keys.filter(key => key.startsWith(this.RECOVERY_STORAGE_KEY));

      if (recoveryKeys.length === 0) {
        return null;
      }

      // Get the most recent recovery state
      const recoveryStates = await Promise.all(
        recoveryKeys.map(async (key) => {
          const stateJson = await AsyncStorage.getItem(key);
          return stateJson ? JSON.parse(stateJson) as BackupRecoveryState : null;
        })
      );

      const validStates = recoveryStates
        .filter((state): state is BackupRecoveryState => state !== null && state.resumable)
        .sort((a, b) => new Date(b.lastCheckpoint.timestamp).getTime() - new Date(a.lastCheckpoint.timestamp).getTime());

      return validStates[0] || null;
    } catch (error) {
      console.error('Failed to check for resumable session:', error);
      return null;
    }
  }

  /**
   * Resume backup from last checkpoint
   */
  async resumeBackup(
    recoveryState: BackupRecoveryState,
    configuration: BackupConfiguration,
    onProgress?: (progress: any) => void
  ): Promise<RecoveryResult> {
    console.log(`🔄 Resuming backup session: ${recoveryState.sessionId}`);

    try {
      // Validate that the session can be resumed
      const canResume = await this.validateResumableSession(recoveryState);
      if (!canResume) {
        return {
          success: false,
          recoveredItems: 0,
          skippedItems: 0,
          errors: [{
            id: `resume_validation_${Date.now()}`,
            type: 'platform',
            message: 'Backup session cannot be resumed - too much time has passed or data has changed',
            timestamp: new Date(),
            retryable: false
          }]
        };
      }

      // Get the original session from database
      const originalSession = await BackupDatabaseService.getBackupSession(recoveryState.sessionId);
      if (!originalSession) {
        return {
          success: false,
          recoveredItems: 0,
          skippedItems: 0,
          errors: [{
            id: `session_not_found_${Date.now()}`,
            type: 'platform',
            message: 'Original backup session not found',
            timestamp: new Date(),
            retryable: false
          }]
        };
      }

      // Create resumed session
      const resumedSession: BackupSession = {
        ...originalSession,
        status: 'in_progress',
        // Keep original start time but don't set end time
        endTime: undefined
      };

      // Show resume notification
      await NotificationService.showResumeNotification(
        recoveryState.sessionId,
        recoveryState.lastCheckpoint.dataType,
        resumedSession.completedItems,
        resumedSession.totalItems
      );

      // Resume from checkpoint
      const result = await this.continueFromCheckpoint(
        resumedSession,
        recoveryState,
        configuration,
        onProgress
      );

      // Clean up recovery state if successful
      if (result.success) {
        await this.clearRecoveryState(recoveryState.sessionId);
      }

      return result;

    } catch (error) {
      console.error('Failed to resume backup:', error);
      
      return {
        success: false,
        recoveredItems: 0,
        skippedItems: 0,
        errors: [{
          id: `resume_error_${Date.now()}`,
          type: 'platform',
          message: error instanceof Error ? error.message : 'Failed to resume backup',
          timestamp: new Date(),
          retryable: true
        }]
      };
    }
  }

  /**
   * Retry failed items from a backup session
   */
  async retryFailedItems(
    sessionId: string,
    configuration: BackupConfiguration,
    onProgress?: (progress: any) => void
  ): Promise<RecoveryResult> {
    console.log(`🔄 Retrying failed items for session: ${sessionId}`);

    try {
      // Get recovery state
      const recoveryState = await this.getRecoveryState(sessionId);
      if (!recoveryState) {
        return {
          success: false,
          recoveredItems: 0,
          skippedItems: 0,
          errors: [{
            id: `no_recovery_state_${Date.now()}`,
            type: 'platform',
            message: 'No recovery information found for this session',
            timestamp: new Date(),
            retryable: false
          }]
        };
      }

      const retryableErrors = recoveryState.retryableErrors;
      if (retryableErrors.length === 0) {
        return {
          success: true,
          recoveredItems: 0,
          skippedItems: 0,
          errors: []
        };
      }

      console.log(`🔄 Found ${retryableErrors.length} retryable errors`);

      // Group errors by data type for efficient processing
      const errorsByType = this.groupErrorsByDataType(retryableErrors);
      
      let totalRecovered = 0;
      let totalSkipped = 0;
      const allErrors: BackupError[] = [];

      // Process each data type
      for (const [dataType, errors] of Object.entries(errorsByType)) {
        const result = await this.retryDataTypeErrors(
          dataType as 'contacts' | 'messages' | 'photos',
          errors,
          configuration,
          onProgress
        );

        totalRecovered += result.recoveredItems;
        totalSkipped += result.skippedItems;
        allErrors.push(...result.errors);
      }

      // Update recovery state
      const remainingErrors = allErrors.filter(error => error.retryable);
      await this.updateRecoveryState(sessionId, {
        ...recoveryState,
        retryableErrors: remainingErrors,
        failedItems: [...recoveryState.failedItems, ...allErrors.filter(error => !error.retryable)]
      });

      return {
        success: allErrors.length === 0,
        recoveredItems: totalRecovered,
        skippedItems: totalSkipped,
        errors: allErrors
      };

    } catch (error) {
      console.error('Failed to retry failed items:', error);
      
      return {
        success: false,
        recoveredItems: 0,
        skippedItems: 0,
        errors: [{
          id: `retry_error_${Date.now()}`,
          type: 'platform',
          message: error instanceof Error ? error.message : 'Failed to retry failed items',
          timestamp: new Date(),
          retryable: true
        }]
      };
    }
  }

  /**
   * Handle partial backup continuation when individual data types fail
   */
  async continuePartialBackup(
    sessionId: string,
    failedDataType: 'contacts' | 'messages' | 'photos',
    configuration: BackupConfiguration,
    onProgress?: (progress: any) => void
  ): Promise<RecoveryResult> {
    console.log(`🔄 Continuing partial backup, skipping ${failedDataType}`);

    try {
      const recoveryState = await this.getRecoveryState(sessionId);
      if (!recoveryState) {
        return {
          success: false,
          recoveredItems: 0,
          skippedItems: 0,
          errors: [{
            id: `no_recovery_state_${Date.now()}`,
            type: 'platform',
            message: 'No recovery information found for this session',
            timestamp: new Date(),
            retryable: false
          }]
        };
      }

      // Determine next data types to process
      const dataTypeOrder: Array<'contacts' | 'messages' | 'photos'> = ['contacts', 'messages', 'photos'];
      const currentIndex = dataTypeOrder.indexOf(failedDataType);
      const remainingDataTypes = dataTypeOrder.slice(currentIndex + 1);

      // Filter remaining data types based on configuration
      const enabledRemainingTypes = remainingDataTypes.filter(type => {
        switch (type) {
          case 'contacts': return configuration.includeContacts;
          case 'messages': return configuration.includeMessages;
          case 'photos': return configuration.includePhotos;
          default: return false;
        }
      });

      if (enabledRemainingTypes.length === 0) {
        console.log('No remaining data types to process');
        return {
          success: true,
          recoveredItems: 0,
          skippedItems: 0,
          errors: []
        };
      }

      // Show notification about partial backup continuation
      await NotificationService.showInfoNotification(
        'Continuing Backup',
        `Skipping ${failedDataType} backup and continuing with remaining data types: ${enabledRemainingTypes.join(', ')}`
      );

      // Continue with remaining data types
      const result = await this.processRemainingDataTypes(
        sessionId,
        enabledRemainingTypes,
        configuration,
        onProgress
      );

      return result;

    } catch (error) {
      console.error('Failed to continue partial backup:', error);
      
      return {
        success: false,
        recoveredItems: 0,
        skippedItems: 0,
        errors: [{
          id: `partial_backup_error_${Date.now()}`,
          type: 'platform',
          message: error instanceof Error ? error.message : 'Failed to continue partial backup',
          timestamp: new Date(),
          retryable: true
        }]
      };
    }
  }

  /**
   * Clean up recovery state after successful completion
   */
  async clearRecoveryState(sessionId: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${this.RECOVERY_STORAGE_KEY}_${sessionId}`);
      console.log(`🧹 Cleared recovery state for session: ${sessionId}`);
    } catch (error) {
      console.error('Failed to clear recovery state:', error);
    }
  }

  /**
   * Get all recovery states for debugging/monitoring
   */
  async getAllRecoveryStates(): Promise<BackupRecoveryState[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const recoveryKeys = keys.filter(key => key.startsWith(this.RECOVERY_STORAGE_KEY));

      const states = await Promise.all(
        recoveryKeys.map(async (key) => {
          const stateJson = await AsyncStorage.getItem(key);
          return stateJson ? JSON.parse(stateJson) as BackupRecoveryState : null;
        })
      );

      return states.filter((state): state is BackupRecoveryState => state !== null);
    } catch (error) {
      console.error('Failed to get all recovery states:', error);
      return [];
    }
  }

  /**
   * Validate that a session can be resumed
   */
  private async validateResumableSession(recoveryState: BackupRecoveryState): Promise<boolean> {
    try {
      // Check if too much time has passed (e.g., more than 24 hours)
      const checkpointTime = new Date(recoveryState.lastCheckpoint.timestamp);
      const now = new Date();
      const hoursSinceCheckpoint = (now.getTime() - checkpointTime.getTime()) / (1000 * 60 * 60);

      if (hoursSinceCheckpoint > 24) {
        console.log(`❌ Session too old: ${hoursSinceCheckpoint} hours since checkpoint`);
        return false;
      }

      // Check if the session exists in the database
      const session = await BackupDatabaseService.getBackupSession(recoveryState.sessionId);
      if (!session) {
        console.log('❌ Session not found in database');
        return false;
      }

      // Check if session is in a resumable state
      if (session.status === 'completed' || session.status === 'cancelled') {
        console.log(`❌ Session not resumable: status is ${session.status}`);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating resumable session:', error);
      return false;
    }
  }

  /**
   * Continue backup from checkpoint
   */
  private async continueFromCheckpoint(
    session: BackupSession,
    recoveryState: BackupRecoveryState,
    configuration: BackupConfiguration,
    onProgress?: (progress: any) => void
  ): Promise<RecoveryResult> {
    // This would integrate with the actual backup services
    // For now, return a placeholder result
    console.log(`▶️ Continuing from checkpoint: ${recoveryState.lastCheckpoint.dataType} at index ${recoveryState.lastCheckpoint.itemIndex}`);

    // Simulate recovery process
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      resumedSession: session,
      recoveredItems: 10, // Placeholder
      skippedItems: 0,
      errors: []
    };
  }

  /**
   * Get recovery state for a session
   */
  private async getRecoveryState(sessionId: string): Promise<BackupRecoveryState | null> {
    try {
      const stateJson = await AsyncStorage.getItem(`${this.RECOVERY_STORAGE_KEY}_${sessionId}`);
      return stateJson ? JSON.parse(stateJson) as BackupRecoveryState : null;
    } catch (error) {
      console.error('Failed to get recovery state:', error);
      return null;
    }
  }

  /**
   * Update recovery state
   */
  private async updateRecoveryState(sessionId: string, state: BackupRecoveryState): Promise<void> {
    try {
      await AsyncStorage.setItem(
        `${this.RECOVERY_STORAGE_KEY}_${sessionId}`,
        JSON.stringify(state)
      );
    } catch (error) {
      console.error('Failed to update recovery state:', error);
    }
  }

  /**
   * Group errors by data type
   */
  private groupErrorsByDataType(errors: BackupError[]): Record<string, BackupError[]> {
    return errors.reduce((groups, error) => {
      const dataType = error.item_type || 'unknown';
      if (!groups[dataType]) {
        groups[dataType] = [];
      }
      groups[dataType].push(error);
      return groups;
    }, {} as Record<string, BackupError[]>);
  }

  /**
   * Retry errors for a specific data type
   */
  private async retryDataTypeErrors(
    dataType: 'contacts' | 'messages' | 'photos',
    errors: BackupError[],
    configuration: BackupConfiguration,
    onProgress?: (progress: any) => void
  ): Promise<RecoveryResult> {
    console.log(`🔄 Retrying ${errors.length} errors for ${dataType}`);

    // Create retry operations
    const retryOperations = errors.map(error => ({
      id: error.id,
      operation: async () => {
        // This would call the appropriate service method
        console.log(`Retrying ${dataType} item: ${error.item_id}`);
        return { success: true };
      },
      error
    }));

    // Execute retries
    const results = await RetryService.retryBatch(retryOperations);
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    return {
      success: failed.length === 0,
      recoveredItems: successful.length,
      skippedItems: 0,
      errors: failed.map(r => r.error!).filter(Boolean)
    };
  }

  /**
   * Process remaining data types after a failure
   */
  private async processRemainingDataTypes(
    sessionId: string,
    dataTypes: Array<'contacts' | 'messages' | 'photos'>,
    configuration: BackupConfiguration,
    onProgress?: (progress: any) => void
  ): Promise<RecoveryResult> {
    console.log(`🔄 Processing remaining data types: ${dataTypes.join(', ')}`);

    // This would integrate with the actual backup services
    // For now, return a placeholder result
    await new Promise(resolve => setTimeout(resolve, 2000));

    return {
      success: true,
      recoveredItems: dataTypes.length * 5, // Placeholder
      skippedItems: 0,
      errors: []
    };
  }
}

export default new BackupRecoveryService();