/**
 * ServiceCheckbox Component
 * Individual service checkbox with visual feedback and styling
 */

class ServiceCheckbox {
    /**
     * @param {Object} props - Component properties
     * @param {ServiceOption} props.service - Service configuration
     * @param {boolean} props.checked - Whether checkbox is checked
     * @param {Function} props.onChange - Callback when checkbox state changes
     * @param {boolean} props.disabled - Whether checkbox is disabled
     */
    constructor(props) {
        this.props = props;
        this.element = null;
        this.checkboxElement = null;
        this.id = window.ModularPricingUtils.generateId();
        
        this.render();
        this.attachEventListeners();
    }

    /**
     * Render the checkbox component
     */
    render() {
        const { service, checked, disabled } = this.props;
        
        this.element = window.ModularPricingUtils.createElement('div', {
            className: `service-checkbox ${checked ? 'checked' : ''} ${disabled ? 'disabled' : ''}`,
            dataset: { serviceId: service.id }
        });

        // Create checkbox input
        this.checkboxElement = window.ModularPricingUtils.createElement('input', {
            type: 'checkbox',
            id: this.id,
            checked: checked || false,
            disabled: disabled || false,
            className: 'service-checkbox-input'
        });

        // Create custom checkbox visual
        const checkboxVisual = window.ModularPricingUtils.createElement('div', {
            className: 'service-checkbox-visual'
        }, '<span class="checkmark">✓</span>');

        // Create service info
        const serviceInfo = window.ModularPricingUtils.createElement('div', {
            className: 'service-info'
        });

        const serviceHeader = window.ModularPricingUtils.createElement('div', {
            className: 'service-header'
        });

        const serviceIcon = window.ModularPricingUtils.createElement('span', {
            className: 'service-icon'
        }, service.icon);

        const serviceName = window.ModularPricingUtils.createElement('span', {
            className: 'service-name'
        }, service.name);

        const servicePrice = window.ModularPricingUtils.createElement('span', {
            className: 'service-price'
        }, window.ModularPricingUtils.formatPrice(service.individualPrice));

        serviceHeader.appendChild(serviceIcon);
        serviceHeader.appendChild(serviceName);
        serviceHeader.appendChild(servicePrice);

        const serviceDescription = window.ModularPricingUtils.createElement('p', {
            className: 'service-description'
        }, service.description);

        serviceInfo.appendChild(serviceHeader);
        serviceInfo.appendChild(serviceDescription);

        // Create label wrapper
        const label = window.ModularPricingUtils.createElement('label', {
            htmlFor: this.id,
            className: 'service-checkbox-label'
        });

        label.appendChild(this.checkboxElement);
        label.appendChild(checkboxVisual);
        label.appendChild(serviceInfo);

        this.element.appendChild(label);

        // Add service details component
        this.serviceDetails = new window.ServiceDetails({
            service: this.props.service,
            isExpanded: false,
            onToggle: this.handleDetailsToggle.bind(this)
        });

        this.element.appendChild(this.serviceDetails.getElement());
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        if (this.checkboxElement && this.props.onChange) {
            this.checkboxElement.addEventListener('change', (e) => {
                this.handleChange(e.target.checked);
            });
        }

        // Add visual feedback on interaction
        this.element.addEventListener('mouseenter', () => {
            if (!this.props.disabled) {
                this.element.classList.add('hover');
            }
        });

        this.element.addEventListener('mouseleave', () => {
            this.element.classList.remove('hover');
        });

        this.element.addEventListener('focus', () => {
            this.element.classList.add('focused');
        }, true);

        this.element.addEventListener('blur', () => {
            this.element.classList.remove('focused');
        }, true);
    }

    /**
     * Handle checkbox state change
     * @param {boolean} checked - New checked state
     */
    handleChange(checked) {
        this.updateVisualState(checked);
        
        if (this.props.onChange) {
            this.props.onChange(this.props.service.id, checked);
        }
    }

    /**
     * Handle service details toggle
     * @param {string} serviceId - Service ID
     * @param {boolean} expanded - Whether details should be expanded
     */
    handleDetailsToggle(serviceId, expanded) {
        if (this.props.onDetailsToggle) {
            this.props.onDetailsToggle(serviceId, expanded);
        }
    }

    /**
     * Update visual state of checkbox
     * @param {boolean} checked - Whether checkbox is checked
     */
    updateVisualState(checked) {
        if (checked) {
            this.element.classList.add('checked');
            window.ModularPricingUtils.addAnimatedClass(this.element, 'selecting', 200);
        } else {
            this.element.classList.remove('checked');
            window.ModularPricingUtils.addAnimatedClass(this.element, 'deselecting', 200);
        }
    }

    /**
     * Update component props
     * @param {Object} newProps - New properties
     */
    updateProps(newProps) {
        const oldProps = this.props;
        this.props = { ...this.props, ...newProps };

        // Update checked state if changed
        if (oldProps.checked !== this.props.checked) {
            this.checkboxElement.checked = this.props.checked;
            this.updateVisualState(this.props.checked);
        }

        // Update disabled state if changed
        if (oldProps.disabled !== this.props.disabled) {
            this.checkboxElement.disabled = this.props.disabled;
            if (this.props.disabled) {
                this.element.classList.add('disabled');
            } else {
                this.element.classList.remove('disabled');
            }
        }
    }

    /**
     * Get the DOM element
     * @returns {HTMLElement} The component's DOM element
     */
    getElement() {
        return this.element;
    }

    /**
     * Destroy the component and clean up event listeners
     */
    destroy() {
        if (this.serviceDetails) {
            this.serviceDetails.destroy();
            this.serviceDetails = null;
        }
        
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
        this.element = null;
        this.checkboxElement = null;
    }
}

// Export component
if (typeof window !== 'undefined') {
    window.ServiceCheckbox = ServiceCheckbox;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ServiceCheckbox;
}