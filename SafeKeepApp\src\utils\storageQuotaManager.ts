// Storage Quota Manager for SafeKeep
// Handles storage usage tracking and quota enforcement

import { supabase } from '../config/supabase';
import { getCurrentUserId } from './supabaseHelpers';

export interface StorageQuota {
  used: number;
  total: number;
  percentage: number;
  remaining: number;
  tier: 'basic' | 'premium' | 'family';
}

export interface StorageUsageByCategory {
  photos: number;
  contacts: number;
  messages: number;
  total: number;
}

/**
 * Get user's current storage quota and usage
 */
export const getStorageQuota = async (): Promise<{ data: StorageQuota | null; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('users')
      .select('storage_used, storage_quota, subscription_tier')
      .eq('id', userId)
      .single();

    if (error) {
      return { data: null, error: error.message };
    }

    const used = data.storage_used || 0;
    const total = data.storage_quota || 5368709120; // 5GB default
    const percentage = total > 0 ? Math.round((used / total) * 100) : 0;
    const remaining = Math.max(0, total - used);

    return {
      data: {
        used,
        total,
        percentage,
        remaining,
        tier: data.subscription_tier || 'basic'
      },
      error: null
    };
  } catch (error) {
    return { data: null, error: error.message };
  }
};

/**
 * Check if user has enough storage for a new file
 */
export const checkStorageAvailable = async (fileSize: number): Promise<{ 
  available: boolean; 
  quota: StorageQuota | null; 
  error: string | null 
}> => {
  try {
    const { data: quota, error } = await getStorageQuota();
    
    if (error || !quota) {
      return { available: false, quota: null, error: error || 'Failed to get quota' };
    }

    const available = quota.remaining >= fileSize;
    
    return { available, quota, error: null };
  } catch (error) {
    return { available: false, quota: null, error: error.message };
  }
};

/**
 * Update user's storage usage (called automatically by database triggers)
 */
export const refreshStorageUsage = async (): Promise<{ success: boolean; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Call the database function to recalculate storage
    const { error } = await supabase.rpc('update_storage_usage', { user_id: userId });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Get storage usage breakdown by category
 */
export const getStorageUsageByCategory = async (): Promise<{ 
  data: StorageUsageByCategory | null; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('file_metadata')
      .select('category, size')
      .eq('user_id', userId)
      .eq('is_backed_up', true);

    if (error) {
      return { data: null, error: error.message };
    }

    const usage = {
      photos: 0,
      contacts: 0,
      messages: 0,
      total: 0
    };

    data.forEach(file => {
      const size = file.size || 0;
      usage.total += size;
      
      if (file.category === 'photo') usage.photos += size;
      else if (file.category === 'contact') usage.contacts += size;
      else if (file.category === 'message') usage.messages += size;
    });

    return { data: usage, error: null };
  } catch (error) {
    return { data: null, error: error.message };
  }
};

/**
 * Format bytes to human readable format
 */
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get subscription tier limits
 */
export const getSubscriptionLimits = (tier: 'basic' | 'premium' | 'family') => {
  const limits = {
    basic: {
      storage: 5 * 1024 * 1024 * 1024, // 5GB
      devices: 1,
      features: ['Basic backup', 'Email support']
    },
    premium: {
      storage: 50 * 1024 * 1024 * 1024, // 50GB
      devices: 3,
      features: ['Advanced backup', 'Priority support', 'Backup scheduling']
    },
    family: {
      storage: 200 * 1024 * 1024 * 1024, // 200GB
      devices: 6,
      features: ['Family sharing', 'Premium support', 'Advanced features']
    }
  };

  return limits[tier];
};