/**
 * WebSocket Server for Real-time Backup Progress
 * Provides real-time communication for backup progress updates
 */

const WebSocket = require('ws');
const http = require('http');

class ProgressWebSocketServer {
    constructor(port = 3001) {
        this.port = port;
        this.clients = new Map();
        this.sessions = new Map();
        this.server = null;
        this.wss = null;
    }

    start() {
        // Create HTTP server for WebSocket upgrade
        this.server = http.createServer();
        
        // Create WebSocket server
        this.wss = new WebSocket.Server({ 
            server: this.server,
            path: '/progress'
        });

        this.wss.on('connection', (ws, req) => {
            const clientId = this.generateClientId();
            this.clients.set(clientId, {
                ws,
                connectedAt: Date.now(),
                lastHeartbeat: Date.now(),
                sessions: new Set()
            });

            console.log(`📡 WebSocket client connected: ${clientId} (${this.clients.size} total)`);

            // Handle incoming messages
            ws.on('message', (data) => {
                this.handleClientMessage(clientId, data);
            });

            // Handle client disconnect
            ws.on('close', () => {
                console.log(`📡 WebSocket client disconnected: ${clientId}`);
                this.clients.delete(clientId);
            });

            // Handle errors
            ws.on('error', (error) => {
                console.error(`❌ WebSocket error for client ${clientId}:`, error.message);
                this.clients.delete(clientId);
            });

            // Send welcome message
            this.sendToClient(clientId, {
                type: 'welcome',
                clientId,
                serverTime: Date.now(),
                capabilities: ['progress_updates', 'session_management', 'heartbeat']
            });
        });

        // Start HTTP server
        this.server.listen(this.port, () => {
            console.log('🚀 WebSocket Server Started!');
            console.log('=' .repeat(50));
            console.log(`📡 WebSocket server running on: ws://localhost:${this.port}/progress`);
            console.log(`🔗 Clients can connect for real-time progress updates`);
            console.log('=' .repeat(50));
            console.log('\n🎯 Supported Features:');
            console.log('• Real-time backup progress updates');
            console.log('• Session management and queuing');
            console.log('• Heartbeat monitoring');
            console.log('• Multi-client broadcasting');
            console.log('\n📝 Activity will be logged here...\n');
        });

        // Start heartbeat monitoring
        this.startHeartbeatMonitoring();

        return this;
    }

    generateClientId() {
        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    handleClientMessage(clientId, data) {
        try {
            const message = JSON.parse(data);
            const client = this.clients.get(clientId);
            
            if (!client) return;

            console.log(`📥 Message from ${clientId}: ${message.type}`);

            switch (message.type) {
                case 'heartbeat':
                    this.handleHeartbeat(clientId, message);
                    break;
                
                case 'session_register':
                    this.handleSessionRegister(clientId, message);
                    break;
                
                case 'progress_update':
                case 'detailed_progress_update':
                    this.handleProgressUpdate(clientId, message);
                    break;
                
                case 'session_started':
                case 'session_completed':
                case 'session_paused':
                case 'session_resumed':
                case 'session_cancelled':
                    this.handleSessionEvent(clientId, message);
                    break;
                
                case 'session_queued':
                case 'session_dequeued':
                case 'queue_position_update':
                    this.handleQueueEvent(clientId, message);
                    break;
                
                default:
                    console.log(`⚠️ Unknown message type: ${message.type}`);
            }
        } catch (error) {
            console.error(`❌ Error handling message from ${clientId}:`, error.message);
        }
    }

    handleHeartbeat(clientId, message) {
        const client = this.clients.get(clientId);
        if (client) {
            client.lastHeartbeat = Date.now();
            
            // Send heartbeat response
            this.sendToClient(clientId, {
                type: 'heartbeat_ack',
                serverTime: Date.now(),
                clientTime: message.timestamp,
                activeSessions: message.activeSessions || 0
            });
        }
    }

    handleSessionRegister(clientId, message) {
        const client = this.clients.get(clientId);
        if (client) {
            client.sessions.add(message.sessionId);
            
            // Store session info
            this.sessions.set(message.sessionId, {
                clientId,
                status: message.status,
                registeredAt: Date.now()
            });
            
            console.log(`📋 Session registered: ${message.sessionId} for client ${clientId}`);
        }
    }

    handleProgressUpdate(clientId, message) {
        // Broadcast progress update to all clients
        this.broadcastToAllClients({
            type: 'progress_broadcast',
            originalType: message.type,
            sessionId: message.sessionId,
            data: message,
            timestamp: Date.now()
        });
        
        // Update session info
        const session = this.sessions.get(message.sessionId);
        if (session) {
            session.lastUpdate = Date.now();
            session.progress = message.progress;
        }
    }

    handleSessionEvent(clientId, message) {
        console.log(`🎯 Session event: ${message.type} for ${message.sessionId}`);
        
        // Update session status
        const session = this.sessions.get(message.sessionId);
        if (session) {
            session.status = message.type.replace('session_', '');
            session.lastEvent = Date.now();
        }
        
        // Broadcast to all clients
        this.broadcastToAllClients({
            type: 'session_event_broadcast',
            originalType: message.type,
            sessionId: message.sessionId,
            data: message,
            timestamp: Date.now()
        });
    }

    handleQueueEvent(clientId, message) {
        console.log(`📋 Queue event: ${message.type} for ${message.sessionId}`);
        
        // Broadcast queue updates to all clients
        this.broadcastToAllClients({
            type: 'queue_event_broadcast',
            originalType: message.type,
            sessionId: message.sessionId,
            data: message,
            timestamp: Date.now()
        });
    }

    sendToClient(clientId, message) {
        const client = this.clients.get(clientId);
        if (client && client.ws.readyState === WebSocket.OPEN) {
            try {
                client.ws.send(JSON.stringify(message));
            } catch (error) {
                console.error(`❌ Error sending to client ${clientId}:`, error.message);
                this.clients.delete(clientId);
            }
        }
    }

    broadcastToAllClients(message) {
        let sentCount = 0;
        
        this.clients.forEach((client, clientId) => {
            if (client.ws.readyState === WebSocket.OPEN) {
                try {
                    client.ws.send(JSON.stringify(message));
                    sentCount++;
                } catch (error) {
                    console.error(`❌ Error broadcasting to client ${clientId}:`, error.message);
                    this.clients.delete(clientId);
                }
            }
        });
        
        if (sentCount > 0) {
            console.log(`📡 Broadcasted ${message.type} to ${sentCount} clients`);
        }
    }

    startHeartbeatMonitoring() {
        setInterval(() => {
            const now = Date.now();
            const timeout = 60000; // 60 seconds timeout
            
            this.clients.forEach((client, clientId) => {
                if (now - client.lastHeartbeat > timeout) {
                    console.log(`💔 Client ${clientId} heartbeat timeout, disconnecting`);
                    client.ws.terminate();
                    this.clients.delete(clientId);
                }
            });
            
            // Send server stats to all clients
            this.broadcastToAllClients({
                type: 'server_stats',
                data: {
                    connectedClients: this.clients.size,
                    activeSessions: this.sessions.size,
                    uptime: now - this.startTime,
                    timestamp: now
                }
            });
        }, 30000); // Check every 30 seconds
    }

    getStats() {
        return {
            connectedClients: this.clients.size,
            activeSessions: this.sessions.size,
            uptime: Date.now() - (this.startTime || Date.now())
        };
    }

    stop() {
        if (this.wss) {
            this.wss.close();
        }
        if (this.server) {
            this.server.close();
        }
        console.log('🛑 WebSocket server stopped');
    }
}

// Start server if run directly
if (require.main === module) {
    // Handle command line arguments for custom port
    const args = process.argv.slice(2);
    let port = 3001; // default port
    
    const portIndex = args.indexOf('--port');
    if (portIndex !== -1 && args[portIndex + 1]) {
        const customPort = parseInt(args[portIndex + 1]);
        if (!isNaN(customPort)) {
            port = customPort;
        }
    }
    
    const server = new ProgressWebSocketServer(port);
    server.startTime = Date.now();
    server.start();
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down WebSocket server...');
        server.stop();
        process.exit(0);
    });
}

module.exports = ProgressWebSocketServer;