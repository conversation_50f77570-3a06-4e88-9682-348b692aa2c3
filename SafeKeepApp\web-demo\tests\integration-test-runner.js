/**
 * Integration Test Runner
 * Orchestrates all integration tests for the SafeKeep Web Demo
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class IntegrationTestRunner {
    constructor() {
        this.testResults = [];
        this.startTime = Date.now();
        this.testSuites = [
            'user-journey-tests.js',
            'realtime-features-tests.js',
            'encryption-demo-tests.js',
            'backup-restore-tests.js',
            'scheduling-tests.js',
            'analytics-tests.js',
            'ui-component-tests.js',
            'error-handling-tests.js'
        ];
    }

    async runAllTests() {
        console.log('🚀 Starting Integration Test Suite');
        console.log('=====================================');

        for (const testSuite of this.testSuites) {
            await this.runTestSuite(testSuite);
        }

        this.generateReport();
    }

    async runTestSuite(testSuite) {
        console.log(`\n📋 Running ${testSuite}...`);
        
        try {
            const testPath = path.join(__dirname, testSuite);
            if (!fs.existsSync(testPath)) {
                console.log(`⚠️  Test suite ${testSuite} not found, skipping...`);
                return;
            }

            const result = await this.executeTest(testPath);
            this.testResults.push({
                suite: testSuite,
                status: result.success ? 'PASSED' : 'FAILED',
                duration: result.duration,
                details: result.details,
                errors: result.errors || []
            });

            console.log(`${result.success ? '✅' : '❌'} ${testSuite} - ${result.status}`);
            
        } catch (error) {
            console.error(`❌ Error running ${testSuite}:`, error.message);
            this.testResults.push({
                suite: testSuite,
                status: 'ERROR',
                duration: 0,
                details: 'Test execution failed',
                errors: [error.message]
            });
        }
    }

    async executeTest(testPath) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            const child = spawn('node', [testPath], {
                stdio: 'pipe',
                cwd: path.dirname(testPath)
            });

            let stdout = '';
            let stderr = '';

            child.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            child.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            child.on('close', (code) => {
                const duration = Date.now() - startTime;
                resolve({
                    success: code === 0,
                    duration,
                    status: code === 0 ? 'PASSED' : 'FAILED',
                    details: stdout,
                    errors: stderr ? [stderr] : []
                });
            });

            // Timeout after 30 seconds
            setTimeout(() => {
                child.kill();
                resolve({
                    success: false,
                    duration: 30000,
                    status: 'TIMEOUT',
                    details: 'Test timed out after 30 seconds',
                    errors: ['Test execution timeout']
                });
            }, 30000);
        });
    }

    generateReport() {
        const totalDuration = Date.now() - this.startTime;
        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const failed = this.testResults.filter(r => r.status === 'FAILED').length;
        const errors = this.testResults.filter(r => r.status === 'ERROR').length;
        const total = this.testResults.length;

        console.log('\n📊 Integration Test Results');
        console.log('============================');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed} ✅`);
        console.log(`Failed: ${failed} ❌`);
        console.log(`Errors: ${errors} 💥`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        // Detailed results
        console.log('\n📋 Detailed Results:');
        this.testResults.forEach(result => {
            console.log(`\n${result.suite}:`);
            console.log(`  Status: ${result.status}`);
            console.log(`  Duration: ${result.duration}ms`);
            if (result.errors.length > 0) {
                console.log(`  Errors: ${result.errors.join(', ')}`);
            }
        });

        // Generate JSON report
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total,
                passed,
                failed,
                errors,
                successRate: ((passed / total) * 100).toFixed(1),
                totalDuration
            },
            results: this.testResults
        };

        fs.writeFileSync(
            path.join(__dirname, '../test-reports/integration-test-report.json'),
            JSON.stringify(report, null, 2)
        );

        console.log('\n📄 Report saved to test-reports/integration-test-report.json');
    }
}

// Run tests if called directly
if (require.main === module) {
    const runner = new IntegrationTestRunner();
    runner.runAllTests().catch(console.error);
}

module.exports = IntegrationTestRunner;