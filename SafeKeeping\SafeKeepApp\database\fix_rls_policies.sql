-- Fix RLS policies for file_metadata table

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own file metadata" ON file_metadata;
DROP POLICY IF EXISTS "Users can insert own file metadata" ON file_metadata;
DROP POLICY IF EXISTS "Users can update own file metadata" ON file_metadata;
DROP POLICY IF EXISTS "Users can delete own file metadata" ON file_metadata;

-- Enable RLS on file_metadata table
ALTER TABLE file_metadata ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own file metadata" 
ON file_metadata FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own file metadata" 
ON file_metadata FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own file metadata" 
ON file_metadata FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own file metadata" 
ON file_metadata FOR DELETE 
USING (auth.uid() = user_id);

-- Create index on user_id for better performance
CREATE INDEX IF NOT EXISTS idx_file_metadata_user_id ON file_metadata(user_id);

-- Add comment for documentation
COMMENT ON TABLE file_metadata IS 'Stores metadata for user files with RLS policies';