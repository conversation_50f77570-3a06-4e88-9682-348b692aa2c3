-- Enhanced Backup Tracking Schema Migration
-- This migration creates the enhanced backup tracking tables as specified in the design document

-- Drop existing backup_sessions table if it exists (will be recreated with enhanced schema)
DROP TABLE IF EXISTS public.backup_sessions CASCADE;

-- Create enhanced backup_sessions table with session management fields
CREATE TABLE public.backup_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    start_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    end_time TIMESTAMPTZ,
    status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    configuration JSONB DEFAULT '{}'::jsonb,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create backup_items table for individual item tracking
CREATE TABLE public.backup_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES public.backup_sessions(id) ON DELETE CASCADE NOT NULL,
    item_type TEXT NOT NULL CHECK (item_type IN ('contact', 'message', 'photo')),
    original_id TEXT NOT NULL,
    encrypted_data BYTEA NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    file_path TEXT,
    checksum TEXT,
    size_bytes BIGINT,
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create backup_progress table for real-time progress updates
CREATE TABLE public.backup_progress (
    session_id UUID REFERENCES public.backup_sessions(id) ON DELETE CASCADE,
    item_type TEXT NOT NULL CHECK (item_type IN ('contact', 'message', 'photo')),
    total_count INTEGER DEFAULT 0,
    completed_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (session_id, item_type)
);

-- Create database indexes for efficient backup queries
CREATE INDEX idx_backup_sessions_user_id ON public.backup_sessions(user_id);
CREATE INDEX idx_backup_sessions_status ON public.backup_sessions(status);
CREATE INDEX idx_backup_sessions_start_time ON public.backup_sessions(start_time DESC);
CREATE INDEX idx_backup_sessions_user_status ON public.backup_sessions(user_id, status);

CREATE INDEX idx_backup_items_session_id ON public.backup_items(session_id);
CREATE INDEX idx_backup_items_type ON public.backup_items(item_type);
CREATE INDEX idx_backup_items_status ON public.backup_items(status);
CREATE INDEX idx_backup_items_session_type ON public.backup_items(session_id, item_type);
CREATE INDEX idx_backup_items_original_id ON public.backup_items(original_id);
CREATE INDEX idx_backup_items_checksum ON public.backup_items(checksum);

CREATE INDEX idx_backup_progress_session_id ON public.backup_progress(session_id);
CREATE INDEX idx_backup_progress_type ON public.backup_progress(item_type);
CREATE INDEX idx_backup_progress_last_updated ON public.backup_progress(last_updated DESC);

-- Add updated_at triggers for the new tables
CREATE TRIGGER update_backup_sessions_updated_at 
    BEFORE UPDATE ON public.backup_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_backup_items_updated_at 
    BEFORE UPDATE ON public.backup_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) on all backup tracking tables
ALTER TABLE public.backup_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies for backup_sessions table
CREATE POLICY "Users can view own backup sessions" ON public.backup_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own backup sessions" ON public.backup_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own backup sessions" ON public.backup_sessions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own backup sessions" ON public.backup_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for backup_items table
CREATE POLICY "Users can view own backup items" ON public.backup_items
    FOR SELECT USING (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_items.session_id
        )
    );

CREATE POLICY "Users can insert own backup items" ON public.backup_items
    FOR INSERT WITH CHECK (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_items.session_id
        )
    );

CREATE POLICY "Users can update own backup items" ON public.backup_items
    FOR UPDATE USING (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_items.session_id
        )
    );

CREATE POLICY "Users can delete own backup items" ON public.backup_items
    FOR DELETE USING (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_items.session_id
        )
    );

-- RLS Policies for backup_progress table
CREATE POLICY "Users can view own backup progress" ON public.backup_progress
    FOR SELECT USING (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_progress.session_id
        )
    );

CREATE POLICY "Users can insert own backup progress" ON public.backup_progress
    FOR INSERT WITH CHECK (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_progress.session_id
        )
    );

CREATE POLICY "Users can update own backup progress" ON public.backup_progress
    FOR UPDATE USING (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_progress.session_id
        )
    );

CREATE POLICY "Users can delete own backup progress" ON public.backup_progress
    FOR DELETE USING (
        auth.uid() = (
            SELECT user_id FROM public.backup_sessions 
            WHERE id = backup_progress.session_id
        )
    );

-- Grant necessary permissions for the new tables
GRANT ALL ON public.backup_sessions TO authenticated;
GRANT ALL ON public.backup_items TO authenticated;
GRANT ALL ON public.backup_progress TO authenticated;

-- Create helper functions for backup management

-- Function to initialize backup progress for a session
CREATE OR REPLACE FUNCTION public.initialize_backup_progress(
    p_session_id UUID,
    p_contacts_total INTEGER DEFAULT 0,
    p_messages_total INTEGER DEFAULT 0,
    p_photos_total INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
    -- Insert progress records for each data type
    INSERT INTO public.backup_progress (session_id, item_type, total_count, completed_count, failed_count)
    VALUES 
        (p_session_id, 'contact', p_contacts_total, 0, 0),
        (p_session_id, 'message', p_messages_total, 0, 0),
        (p_session_id, 'photo', p_photos_total, 0, 0)
    ON CONFLICT (session_id, item_type) 
    DO UPDATE SET 
        total_count = EXCLUDED.total_count,
        last_updated = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update backup progress
CREATE OR REPLACE FUNCTION public.update_backup_progress(
    p_session_id UUID,
    p_item_type TEXT,
    p_completed_increment INTEGER DEFAULT 1,
    p_failed_increment INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.backup_progress 
    SET 
        completed_count = completed_count + p_completed_increment,
        failed_count = failed_count + p_failed_increment,
        last_updated = NOW()
    WHERE session_id = p_session_id AND item_type = p_item_type;
    
    -- Update the backup session's overall progress
    UPDATE public.backup_sessions 
    SET 
        completed_items = (
            SELECT COALESCE(SUM(completed_count), 0) 
            FROM public.backup_progress 
            WHERE session_id = p_session_id
        ),
        updated_at = NOW()
    WHERE id = p_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get backup session summary
CREATE OR REPLACE FUNCTION public.get_backup_session_summary(p_session_id UUID)
RETURNS TABLE (
    session_id UUID,
    status TEXT,
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    total_items INTEGER,
    completed_items INTEGER,
    contacts_total INTEGER,
    contacts_completed INTEGER,
    contacts_failed INTEGER,
    messages_total INTEGER,
    messages_completed INTEGER,
    messages_failed INTEGER,
    photos_total INTEGER,
    photos_completed INTEGER,
    photos_failed INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bs.id,
        bs.status,
        bs.start_time,
        bs.end_time,
        bs.total_items,
        bs.completed_items,
        COALESCE(bp_contact.total_count, 0) as contacts_total,
        COALESCE(bp_contact.completed_count, 0) as contacts_completed,
        COALESCE(bp_contact.failed_count, 0) as contacts_failed,
        COALESCE(bp_message.total_count, 0) as messages_total,
        COALESCE(bp_message.completed_count, 0) as messages_completed,
        COALESCE(bp_message.failed_count, 0) as messages_failed,
        COALESCE(bp_photo.total_count, 0) as photos_total,
        COALESCE(bp_photo.completed_count, 0) as photos_completed,
        COALESCE(bp_photo.failed_count, 0) as photos_failed
    FROM public.backup_sessions bs
    LEFT JOIN public.backup_progress bp_contact ON bs.id = bp_contact.session_id AND bp_contact.item_type = 'contact'
    LEFT JOIN public.backup_progress bp_message ON bs.id = bp_message.session_id AND bp_message.item_type = 'message'
    LEFT JOIN public.backup_progress bp_photo ON bs.id = bp_photo.session_id AND bp_photo.item_type = 'photo'
    WHERE bs.id = p_session_id
    AND bs.user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old backup sessions (keep last 30 days)
CREATE OR REPLACE FUNCTION public.cleanup_old_backup_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    WITH deleted_sessions AS (
        DELETE FROM public.backup_sessions 
        WHERE start_time < NOW() - INTERVAL '30 days'
        AND status IN ('completed', 'failed', 'cancelled')
        RETURNING id
    )
    SELECT COUNT(*) INTO deleted_count FROM deleted_sessions;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on helper functions
GRANT EXECUTE ON FUNCTION public.initialize_backup_progress TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_backup_progress TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_backup_session_summary TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_old_backup_sessions TO authenticated;