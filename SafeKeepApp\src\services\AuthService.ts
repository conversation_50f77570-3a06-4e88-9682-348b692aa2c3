import { type User, type Session } from '@supabase/supabase-js/src/index';
import { supabase } from '../utils/supabaseClient';
import EncryptionService from './EncryptionService';
import { executeRlsFix } from '../utils/executeRlsFix';
import { verifyRlsPolicies } from '../utils/verifyRlsPolicies';

// Table names
const TABLES = {
  USERS: 'users',
  FILE_METADATA: 'file_metadata',
  BACKUP_SESSIONS: 'backup_sessions',
  STORAGE_USAGE: 'storage_usage'
};

export interface UserProfile {
  id: string;
  email: string;
  display_name: string;
  created_at: Date;
  last_login_at: Date;
  storage_used: number;
  storage_quota: number;
  encryption_key_id?: string;
  backup_settings: {
    auto_backup: boolean;
    wifi_only: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
  };
}

export interface AuthResult {
  success: boolean;
  user?: User;
  session?: Session;
  error?: string;
}

class AuthService {
  private currentUser: User | null = null;
  private currentSession: Session | null = null;
  private authStateListeners: ((user: User | null) => void)[] = [];

  constructor() {
    // Listen for auth state changes
    supabase.auth.onAuthStateChange((event: string, session: Session | null) => {
      this.currentUser = session?.user || null;
      this.currentSession = session;
      this.notifyAuthStateListeners(this.currentUser);

      if (event === 'SIGNED_IN' && session?.user) {
        this.updateLastLoginTime(session.user.id);

        // Check and fix RLS policies after sign in
        this.checkAndFixRlsPolicies().catch(err =>
          console.error('Failed to check/fix RLS policies:', err)
        );
      }
    });

    // Initialize current session
    this.initializeSession();
  }

  private async initializeSession() {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      this.currentUser = session?.user || null;
      this.currentSession = session;
    } catch (error) {
      console.error('Failed to initialize session:', error);
    }
  }

  // Register new user with email and password
  async registerUser(
    email: string,
    password: string,
    displayName: string
  ): Promise<AuthResult> {
    try {
      console.log('🔐 Registering new user...');

      // Create user account
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            display_name: displayName
          }
        }
      });

      if (error) {
        throw error;
      }

      if (!data.user) {
        throw new Error('User creation failed');
      }

      // Initialize encryption for new user
      const masterKey = await EncryptionService.initializeEncryption();
      const encryptionStatus = await EncryptionService.getEncryptionStatus();

      // Create user profile in database
      const userProfile = {
        id: data.user.id,
        email: data.user.email!,
        display_name: displayName,
        storage_used: 0,
        storage_quota: 5 * 1024 * 1024 * 1024, // 5GB default quota
        encryption_key_id: encryptionStatus.keyId,
        backup_settings: {
          auto_backup: true,
          wifi_only: true,
          frequency: 'daily' as const
        }
      };

      const { error: profileError } = await supabase
        .from(TABLES.USERS)
        .insert(userProfile);

      if (profileError) {
        console.error('Failed to create user profile:', profileError);
        // Continue anyway, profile can be created later
      }

      console.log('✅ User registered successfully');
      return { success: true, user: data.user, session: data.session || undefined };
    } catch (error: any) {
      console.error('❌ Registration failed:', error);
      return {
        success: false,
        error: this.getSupabaseErrorMessage(error.message)
      };
    }
  }

  // Sign in existing user
  async signInUser(email: string, password: string): Promise<AuthResult> {
    try {
      console.log('🔐 Signing in user...');

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        throw error;
      }

      if (!data.user) {
        throw new Error('Sign in failed');
      }

      // Verify encryption is working
      const encryptionStatus = await EncryptionService.getEncryptionStatus();
      if (!encryptionStatus.isWorking) {
        console.warn('⚠️ Encryption verification failed');
        // Could prompt user to re-enter master password or reset encryption
      }

      console.log('✅ User signed in successfully');
      return { success: true, user: data.user, session: data.session || undefined };
    } catch (error: any) {
      console.error('❌ Sign in failed:', error);
      return {
        success: false,
        error: this.getSupabaseErrorMessage(error.message)
      };
    }
  }

  // Sign out current user
  async signOutUser(): Promise<AuthResult> {
    try {
      console.log('🔐 Signing out user...');

      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      // Clear encryption keys for security
      await EncryptionService.clearEncryptionKeys();

      console.log('✅ User signed out successfully');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Sign out failed:', error);
      return {
        success: false,
        error: 'Failed to sign out'
      };
    }
  }

  // Send password reset email
  async resetPassword(email: string): Promise<AuthResult> {
    try {
      console.log('📧 Sending password reset email...');

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'safekeep://reset-password'
      });

      if (error) {
        throw error;
      }

      console.log('✅ Password reset email sent');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Password reset failed:', error);
      return {
        success: false,
        error: this.getSupabaseErrorMessage(error.message)
      };
    }
  }

  // Update user password
  async updateUserPassword(newPassword: string): Promise<AuthResult> {
    try {
      if (!this.currentUser) {
        return { success: false, error: 'No user signed in' };
      }

      console.log('🔐 Updating user password...');

      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        throw error;
      }

      console.log('✅ Password updated successfully');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Password update failed:', error);
      return {
        success: false,
        error: this.getSupabaseErrorMessage(error.message)
      };
    }
  }

  // Get current user profile
  async getUserProfile(): Promise<UserProfile | null> {
    try {
      if (!this.currentUser) {
        return null;
      }

      const { data, error } = await supabase
        .from(TABLES.USERS)
        .select('*')
        .eq('id', this.currentUser.id)
        .single();

      if (error) {
        console.warn('User profile not found:', error);
        return null;
      }

      return {
        ...data,
        created_at: new Date(data.created_at),
        last_login_at: new Date(data.last_login_at)
      } as UserProfile;
    } catch (error) {
      console.error('Failed to get user profile:', error);
      return null;
    }
  }

  // Update user profile
  async updateUserProfile(updates: Partial<UserProfile>): Promise<boolean> {
    try {
      if (!this.currentUser) {
        return false;
      }

      console.log('📝 Updating user profile...');

      const { error } = await supabase
        .from(TABLES.USERS)
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.currentUser.id);

      if (error) {
        throw error;
      }

      console.log('✅ User profile updated');
      return true;
    } catch (error) {
      console.error('❌ Failed to update user profile:', error);
      return false;
    }
  }

  // Update storage usage
  async updateStorageUsage(bytesUsed: number): Promise<boolean> {
    try {
      if (!this.currentUser) {
        return false;
      }

      const { error } = await supabase
        .from(TABLES.USERS)
        .update({
          storage_used: bytesUsed,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.currentUser.id);

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to update storage usage:', error);
      return false;
    }
  }

  // Delete user account
  async deleteUserAccount(): Promise<AuthResult> {
    try {
      if (!this.currentUser) {
        return { success: false, error: 'No user signed in' };
      }

      console.log('🗑️ Deleting user account...');

      const userId = this.currentUser.id;

      // Mark user as deleted in database
      await supabase
        .from(TABLES.USERS)
        .update({
          status: 'deleted',
          deleted_at: new Date().toISOString()
        })
        .eq('id', userId);

      // Clear encryption keys
      await EncryptionService.clearEncryptionKeys();

      // Note: Supabase doesn't allow deleting auth users from client
      // This would need to be done via admin API or Edge Function
      console.log('✅ User account marked for deletion');
      return { success: true };
    } catch (error: any) {
      console.error('❌ Account deletion failed:', error);
      return {
        success: false,
        error: this.getSupabaseErrorMessage(error.message)
      };
    }
  }

  // Add auth state listener
  addAuthStateListener(listener: (user: User | null) => void): void {
    this.authStateListeners.push(listener);
  }

  // Remove auth state listener
  removeAuthStateListener(listener: (user: User | null) => void): void {
    const index = this.authStateListeners.indexOf(listener);
    if (index > -1) {
      this.authStateListeners.splice(index, 1);
    }
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  // Check if user is signed in
  isSignedIn(): boolean {
    return !!this.currentUser;
  }

  // Fix RLS policy issues
  async fixRlsPolicies(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔒 Fixing RLS policies...');

      // Execute the RLS policy fix
      const result = await executeRlsFix();

      if (result.success) {
        console.log('✅ RLS policies fixed successfully');
      } else {
        console.error('❌ Failed to fix RLS policies:', result.message);
      }

      return result;
    } catch (error) {
      console.error('❌ Unexpected error fixing RLS policies:', error);
      return {
        success: false,
        message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  // Check and fix RLS policies if needed
  async checkAndFixRlsPolicies(): Promise<void> {
    try {
      // First verify if policies are working correctly
      const verifyResult = await verifyRlsPolicies();

      // If verification fails, apply the fix
      if (!verifyResult.success || !verifyResult.policies || verifyResult.policies.length === 0) {
        console.log('⚠️ RLS policies verification failed, applying fix...');
        await this.fixRlsPolicies();
      } else {
        console.log('✅ RLS policies are correctly configured');
      }
    } catch (error) {
      console.error('Error checking/fixing RLS policies:', error);
    }
  }

  // Private methods
  private notifyAuthStateListeners(user: User | null): void {
    this.authStateListeners.forEach(listener => listener(user));
  }

  private async updateLastLoginTime(userId: string): Promise<void> {
    try {
      await supabase
        .from(TABLES.USERS)
        .update({
          last_login_at: new Date().toISOString()
        })
        .eq('id', userId);
    } catch (error) {
      console.error('Failed to update last login time:', error);
    }
  }

  private getSupabaseErrorMessage(errorMessage: string): string {
    // Convert Supabase error messages to user-friendly messages
    if (errorMessage.includes('User already registered')) {
      return 'This email address is already registered. Please sign in instead.';
    }
    if (errorMessage.includes('Invalid login credentials')) {
      return 'Incorrect email or password. Please try again.';
    }
    if (errorMessage.includes('Email not confirmed')) {
      return 'Please check your email and click the confirmation link before signing in.';
    }
    if (errorMessage.includes('Password should be at least')) {
      return 'Password should be at least 6 characters long.';
    }
    if (errorMessage.includes('Invalid email')) {
      return 'Please enter a valid email address.';
    }
    if (errorMessage.includes('Too many requests')) {
      return 'Too many failed attempts. Please try again later.';
    }
    if (errorMessage.includes('Network')) {
      return 'Network error. Please check your connection and try again.';
    }

    // Return the original message if no specific mapping found
    return errorMessage || 'An error occurred. Please try again.';
  }
}

export default new AuthService();
