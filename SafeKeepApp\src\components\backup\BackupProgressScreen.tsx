import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import {
  Card,
  Text,
  Button,
  ProgressBar,
  Chip,
  IconButton,
  Surface,
  Divider,
  Dialog,
  Portal,
  List,
} from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  useBackupState,
  useBackupActions,
  useOverallBackupProgress,
  useBackupSessionDuration,
  useEstimatedTimeRemaining,
  useIsBackupInProgress,
  useBackupErrors,
} from '../../store/hooks/backupHooks';
import { COLORS, SPACING, FONTS } from '../../utils/constants';
import BackupManager from '../../services/BackupManager';
import { BackupError } from '../../types/backup';

const { width } = Dimensions.get('window');

interface BackupProgressScreenProps {
  onNavigateBack?: () => void;
}

const BackupProgressScreen: React.FC<BackupProgressScreenProps> = ({
  onNavigateBack,
}) => {
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [selectedError, setSelectedError] = useState<BackupError | null>(null);
  const [retryingErrors, setRetryingErrors] = useState<Set<string>>(new Set());

  // Redux state
  const backupState = useBackupState();
  const backupActions = useBackupActions();
  const overallProgress = useOverallBackupProgress();
  const sessionDuration = useBackupSessionDuration();
  const estimatedTimeRemaining = useEstimatedTimeRemaining();
  const isBackupInProgress = useIsBackupInProgress();
  const backupErrors = useBackupErrors();

  // Auto-refresh progress every second
  useEffect(() => {
    if (!isBackupInProgress) return;

    const interval = setInterval(() => {
      // Progress updates are handled by the BackupManager callback
      // This just ensures the UI stays responsive
    }, 1000);

    return () => clearInterval(interval);
  }, [isBackupInProgress]);

  // Handle cancel backup
  const handleCancelBackup = async () => {
    try {
      await BackupManager.cancelBackup();
      backupActions.cancelSession();
      setShowCancelDialog(false);
      
      Alert.alert(
        'Backup Cancelled',
        'The backup process has been cancelled.',
        [{ text: 'OK', onPress: onNavigateBack }]
      );
    } catch (error) {
      console.error('Failed to cancel backup:', error);
      Alert.alert(
        'Error',
        'Failed to cancel backup. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Handle retry failed item
  const handleRetryError = async (error: BackupError) => {
    if (!error.retryable || retryingErrors.has(error.id)) return;

    setRetryingErrors(prev => new Set(prev).add(error.id));

    try {
      // Attempt to retry the failed item
      // This would typically involve calling the specific backup service
      // For now, we'll simulate a retry and remove the error
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      backupActions.removeErrorById(error.id);
      
      Alert.alert(
        'Retry Successful',
        'The failed item has been successfully processed.',
        [{ text: 'OK' }]
      );
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      Alert.alert(
        'Retry Failed',
        'Failed to retry the item. Please try again later.',
        [{ text: 'OK' }]
      );
    } finally {
      setRetryingErrors(prev => {
        const newSet = new Set(prev);
        newSet.delete(error.id);
        return newSet;
      });
    }
  };

  // Get status color based on backup state
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return COLORS.success;
      case 'failed':
        return COLORS.error;
      case 'in_progress':
        return COLORS.primary;
      case 'cancelled':
        return COLORS.warning;
      default:
        return COLORS.textSecondary;
    }
  };

  // Format duration for display
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  // Get current processing item info
  const getCurrentProcessingInfo = () => {
    const { realTimeProgress } = backupState;
    
    for (const [dataType, progress] of Object.entries(realTimeProgress)) {
      if (progress.status === 'in_progress' && progress.completed < progress.total) {
        return {
          dataType: dataType as 'contacts' | 'messages' | 'photos',
          currentItem: progress.completed + 1,
          totalItems: progress.total,
        };
      }
    }
    
    return null;
  };

  const currentProcessing = getCurrentProcessingInfo();

  return (
    <ScrollView style={styles.container}>
      {/* Header Section */}
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.headerContent}>
            <View style={styles.headerInfo}>
              <Text variant="headlineSmall" style={styles.title}>
                Backup Progress
              </Text>
              <Text variant="bodyMedium" style={styles.subtitle}>
                {isBackupInProgress ? 'Backup in progress...' : 'Backup completed'}
              </Text>
            </View>
            
            {onNavigateBack && (
              <IconButton
                icon="arrow-left"
                size={24}
                onPress={onNavigateBack}
              />
            )}
          </View>

          {/* Overall Progress */}
          <View style={styles.overallProgress}>
            <View style={styles.progressHeader}>
              <Text variant="titleMedium">
                Overall Progress: {overallProgress.percentage}%
              </Text>
              <Text variant="bodySmall" style={styles.progressSubtext}>
                {overallProgress.completedItems} of {overallProgress.totalItems} items
              </Text>
            </View>
            
            <ProgressBar
              progress={overallProgress.percentage / 100}
              color={COLORS.primary}
              style={styles.mainProgressBar}
            />

            {/* Time Information */}
            <View style={styles.timeInfo}>
              <View style={styles.timeItem}>
                <Text variant="bodySmall" style={styles.timeLabel}>Elapsed</Text>
                <Text variant="bodyMedium" style={styles.timeValue}>
                  {formatDuration(sessionDuration)}
                </Text>
              </View>
              
              {estimatedTimeRemaining && (
                <View style={styles.timeItem}>
                  <Text variant="bodySmall" style={styles.timeLabel}>Remaining</Text>
                  <Text variant="bodyMedium" style={styles.timeValue}>
                    {formatDuration(estimatedTimeRemaining)}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* Cancel Button */}
          {isBackupInProgress && (
            <Button
              mode="outlined"
              onPress={() => setShowCancelDialog(true)}
              textColor={COLORS.error}
              style={styles.cancelButton}
            >
              Cancel Backup
            </Button>
          )}
        </Card.Content>
      </Card>

      {/* Current Processing Item */}
      {currentProcessing && (
        <Card style={styles.currentItemCard}>
          <Card.Content>
            <View style={styles.currentItemHeader}>
              <MaterialCommunityIcons
                name={
                  currentProcessing.dataType === 'contacts' ? 'contacts' :
                  currentProcessing.dataType === 'messages' ? 'message-text' : 'camera'
                }
                size={24}
                color={COLORS.primary}
              />
              <View style={styles.currentItemInfo}>
                <Text variant="titleSmall">
                  Processing {currentProcessing.dataType}
                </Text>
                <Text variant="bodySmall" style={styles.currentItemSubtext}>
                  Item {currentProcessing.currentItem} of {currentProcessing.totalItems}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Individual Data Type Progress */}
      <Card style={styles.progressCard}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Detailed Progress
          </Text>

          <View style={styles.dataTypeList}>
            {Object.entries(backupState.realTimeProgress).map(([dataType, progress]) => (
              <View key={dataType} style={styles.dataTypeItem}>
                <View style={styles.dataTypeHeader}>
                  <View style={styles.dataTypeInfo}>
                    <MaterialCommunityIcons
                      name={
                        dataType === 'contacts' ? 'contacts' :
                        dataType === 'messages' ? 'message-text' : 'camera'
                      }
                      size={20}
                      color={getStatusColor(progress.status)}
                    />
                    <Text variant="bodyMedium" style={styles.dataTypeLabel}>
                      {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
                    </Text>
                  </View>
                  
                  <View style={styles.dataTypeStats}>
                    <Chip
                      mode="outlined"
                      compact
                      textStyle={styles.chipText}
                      style={[
                        styles.statusChip,
                        { borderColor: getStatusColor(progress.status) }
                      ]}
                    >
                      {progress.status}
                    </Chip>
                    <Text variant="bodySmall" style={styles.dataTypeCount}>
                      {progress.completed} / {progress.total}
                    </Text>
                  </View>
                </View>

                <ProgressBar
                  progress={progress.total > 0 ? progress.completed / progress.total : 0}
                  color={getStatusColor(progress.status)}
                  style={styles.dataTypeProgressBar}
                />

                {progress.failed > 0 && (
                  <Text variant="bodySmall" style={styles.failedText}>
                    {progress.failed} failed items
                  </Text>
                )}
              </View>
            ))}
          </View>
        </Card.Content>
      </Card>

      {/* Error Section */}
      {backupErrors.length > 0 && (
        <Card style={styles.errorCard}>
          <Card.Content>
            <View style={styles.errorHeader}>
              <Text variant="titleMedium" style={styles.errorTitle}>
                Errors ({backupErrors.length})
              </Text>
              <Button
                mode="text"
                compact
                onPress={backupActions.clearAllErrors}
              >
                Clear All
              </Button>
            </View>

            <View style={styles.errorList}>
              {backupErrors.slice(0, 5).map((error) => (
                <Surface key={error.id} style={styles.errorItem}>
                  <View style={styles.errorContent}>
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={16}
                      color={COLORS.error}
                    />
                    <View style={styles.errorInfo}>
                      <Text variant="bodySmall" style={styles.errorMessage}>
                        {error.message}
                      </Text>
                      <Text variant="bodySmall" style={styles.errorTimestamp}>
                        {new Date(error.timestamp).toLocaleTimeString()}
                      </Text>
                    </View>
                    
                    <View style={styles.errorActions}>
                      {error.retryable && (
                        <IconButton
                          icon="refresh"
                          size={16}
                          onPress={() => handleRetryError(error)}
                          disabled={retryingErrors.has(error.id)}
                        />
                      )}
                      <IconButton
                        icon="information"
                        size={16}
                        onPress={() => {
                          setSelectedError(error);
                          setShowErrorDialog(true);
                        }}
                      />
                    </View>
                  </View>
                </Surface>
              ))}
              
              {backupErrors.length > 5 && (
                <Button
                  mode="text"
                  compact
                  onPress={() => {
                    // Show all errors dialog
                    Alert.alert(
                      'All Errors',
                      `Total errors: ${backupErrors.length}\n\n${backupErrors.map(e => e.message).join('\n')}`,
                      [{ text: 'OK' }]
                    );
                  }}
                >
                  View All {backupErrors.length} Errors
                </Button>
              )}
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Cancel Confirmation Dialog */}
      <Portal>
        <Dialog visible={showCancelDialog} onDismiss={() => setShowCancelDialog(false)}>
          <Dialog.Title>Cancel Backup</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium">
              Are you sure you want to cancel the current backup? 
              Progress will be lost and you'll need to start over.
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowCancelDialog(false)}>
              Keep Going
            </Button>
            <Button
              onPress={handleCancelBackup}
              textColor={COLORS.error}
            >
              Cancel Backup
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Error Details Dialog */}
      <Portal>
        <Dialog visible={showErrorDialog} onDismiss={() => setShowErrorDialog(false)}>
          <Dialog.Title>Error Details</Dialog.Title>
          <Dialog.Content>
            {selectedError && (
              <View style={styles.errorDetails}>
                <Text variant="bodyMedium" style={styles.errorDetailTitle}>
                  Error Type: {selectedError.type}
                </Text>
                <Text variant="bodyMedium" style={styles.errorDetailMessage}>
                  {selectedError.message}
                </Text>
                <Text variant="bodySmall" style={styles.errorDetailTime}>
                  Occurred at: {new Date(selectedError.timestamp).toLocaleString()}
                </Text>
                {selectedError.itemId && (
                  <Text variant="bodySmall" style={styles.errorDetailItem}>
                    Item ID: {selectedError.itemId}
                  </Text>
                )}
                <Text variant="bodySmall" style={styles.errorDetailRetry}>
                  Retryable: {selectedError.retryable ? 'Yes' : 'No'}
                </Text>
              </View>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            {selectedError?.retryable && (
              <Button
                onPress={() => {
                  setShowErrorDialog(false);
                  if (selectedError) {
                    handleRetryError(selectedError);
                  }
                }}
                disabled={selectedError ? retryingErrors.has(selectedError.id) : false}
              >
                Retry
              </Button>
            )}
            <Button onPress={() => setShowErrorDialog(false)}>
              Close
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  headerCard: {
    margin: SPACING.md,
    marginBottom: SPACING.sm,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  headerInfo: {
    flex: 1,
  },
  title: {
    fontWeight: 'bold',
    color: COLORS.text,
  },
  subtitle: {
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  overallProgress: {
    gap: SPACING.sm,
  },
  progressHeader: {
    alignItems: 'center',
  },
  progressSubtext: {
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  mainProgressBar: {
    height: 12,
    borderRadius: 6,
  },
  timeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: SPACING.sm,
  },
  timeItem: {
    alignItems: 'center',
  },
  timeLabel: {
    color: COLORS.textSecondary,
  },
  timeValue: {
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.xs,
  },
  cancelButton: {
    marginTop: SPACING.md,
    borderColor: COLORS.error,
  },
  currentItemCard: {
    margin: SPACING.md,
    marginVertical: SPACING.sm,
    backgroundColor: COLORS.primaryLight,
  },
  currentItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  currentItemInfo: {
    flex: 1,
  },
  currentItemSubtext: {
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  progressCard: {
    margin: SPACING.md,
    marginVertical: SPACING.sm,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  dataTypeList: {
    gap: SPACING.md,
  },
  dataTypeItem: {
    gap: SPACING.sm,
  },
  dataTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dataTypeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  dataTypeLabel: {
    color: COLORS.text,
    fontWeight: '500',
  },
  dataTypeStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  dataTypeCount: {
    color: COLORS.textSecondary,
    minWidth: 60,
    textAlign: 'right',
  },
  dataTypeProgressBar: {
    height: 6,
    borderRadius: 3,
  },
  failedText: {
    color: COLORS.error,
    fontSize: 12,
  },
  statusChip: {
    height: 24,
  },
  chipText: {
    fontSize: 10,
  },
  errorCard: {
    margin: SPACING.md,
    marginVertical: SPACING.sm,
    backgroundColor: COLORS.errorLight,
  },
  errorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  errorTitle: {
    fontWeight: 'bold',
    color: COLORS.error,
  },
  errorList: {
    gap: SPACING.sm,
  },
  errorItem: {
    padding: SPACING.sm,
    borderRadius: 8,
    elevation: 1,
  },
  errorContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: SPACING.sm,
  },
  errorInfo: {
    flex: 1,
  },
  errorMessage: {
    color: COLORS.text,
  },
  errorTimestamp: {
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  errorActions: {
    flexDirection: 'row',
  },
  errorDetails: {
    gap: SPACING.sm,
  },
  errorDetailTitle: {
    fontWeight: 'bold',
    color: COLORS.text,
  },
  errorDetailMessage: {
    color: COLORS.text,
  },
  errorDetailTime: {
    color: COLORS.textSecondary,
  },
  errorDetailItem: {
    color: COLORS.textSecondary,
  },
  errorDetailRetry: {
    color: COLORS.textSecondary,
  },
});

export default BackupProgressScreen;