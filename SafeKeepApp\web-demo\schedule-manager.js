/**
 * Advanced Backup Scheduling Manager
 * Handles backup scheduling, condition evaluation, and automatic execution
 */

class ScheduleManager {
    constructor(supabase, adminSupabase) {
        this.supabase = supabase;
        this.adminSupabase = adminSupabase;
        this.schedules = new Map();
        this.activeTimers = new Map();
        this.simulatedConditions = {
            networkType: 'wifi', // 'wifi', 'cellular', 'offline'
            batteryLevel: 85, // 0-100
            storageUsage: 45, // 0-100 percentage
            isCharging: false,
            deviceIdle: true
        };
        this.listeners = [];
        this.isInitialized = false;
        
        // Bind methods
        this.evaluateSchedule = this.evaluateSchedule.bind(this);
        this.executeScheduledBackup = this.executeScheduledBackup.bind(this);
        this.updateSimulatedConditions = this.updateSimulatedConditions.bind(this);
    }

    /**
     * Initialize the schedule manager
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            await this.loadSchedules();
            this.startScheduleEvaluation();
            this.isInitialized = true;
            this.notifyListeners('initialized', { schedules: Array.from(this.schedules.values()) });
        } catch (error) {
            console.error('Failed to initialize ScheduleManager:', error);
            throw error;
        }
    }

    /**
     * Add event listener
     */
    addListener(callback) {
        this.listeners.push(callback);
    }

    /**
     * Remove event listener
     */
    removeListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * Notify all listeners of events
     */
    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in schedule manager listener:', error);
            }
        });
    }

    /**
     * Create a new backup schedule
     */
    async createSchedule(scheduleData) {
        try {
            const schedule = {
                id: this.generateId(),
                userId: this.getCurrentUserId(),
                name: scheduleData.name || 'Untitled Schedule',
                enabled: scheduleData.enabled !== false,
                frequency: scheduleData.frequency || 'daily',
                time: scheduleData.time || '02:00',
                days: scheduleData.days || [0, 1, 2, 3, 4, 5, 6], // All days by default
                conditions: {
                    wifiOnly: scheduleData.conditions?.wifiOnly !== false,
                    minBatteryLevel: scheduleData.conditions?.minBatteryLevel || 20,
                    maxStorageUsage: scheduleData.conditions?.maxStorageUsage || 80,
                    requireCharging: scheduleData.conditions?.requireCharging || false,
                    requireIdle: scheduleData.conditions?.requireIdle || false
                },
                dataTypes: scheduleData.dataTypes || ['contacts', 'messages', 'photos'],
                lastRun: null,
                nextRun: null,
                runCount: 0,
                successCount: 0,
                createdAt: new Date(),
                updatedAt: new Date()
            };

            // Calculate next run time
            schedule.nextRun = this.calculateNextRun(schedule);

            // Store in memory and database
            this.schedules.set(schedule.id, schedule);
            await this.saveSchedule(schedule);

            // Set up timer if enabled
            if (schedule.enabled) {
                this.setupScheduleTimer(schedule);
            }

            this.notifyListeners('schedule_created', schedule);
            return schedule;
        } catch (error) {
            console.error('Failed to create schedule:', error);
            throw error;
        }
    }

    /**
     * Update an existing schedule
     */
    async updateSchedule(scheduleId, updates) {
        try {
            const schedule = this.schedules.get(scheduleId);
            if (!schedule) {
                throw new Error('Schedule not found');
            }

            // Clear existing timer
            this.clearScheduleTimer(scheduleId);

            // Apply updates
            Object.assign(schedule, updates, { updatedAt: new Date() });

            // Recalculate next run if timing changed
            if (updates.frequency || updates.time || updates.days || updates.enabled !== undefined) {
                schedule.nextRun = schedule.enabled ? this.calculateNextRun(schedule) : null;
            }

            // Save to database
            await this.saveSchedule(schedule);

            // Set up new timer if enabled
            if (schedule.enabled) {
                this.setupScheduleTimer(schedule);
            }

            this.notifyListeners('schedule_updated', schedule);
            return schedule;
        } catch (error) {
            console.error('Failed to update schedule:', error);
            throw error;
        }
    }

    /**
     * Delete a schedule
     */
    async deleteSchedule(scheduleId) {
        try {
            const schedule = this.schedules.get(scheduleId);
            if (!schedule) {
                throw new Error('Schedule not found');
            }

            // Clear timer
            this.clearScheduleTimer(scheduleId);

            // Remove from memory and database
            this.schedules.delete(scheduleId);
            await this.removeScheduleFromDatabase(scheduleId);

            this.notifyListeners('schedule_deleted', { id: scheduleId });
            return true;
        } catch (error) {
            console.error('Failed to delete schedule:', error);
            throw error;
        }
    }

    /**
     * Get all schedules
     */
    getSchedules() {
        return Array.from(this.schedules.values());
    }

    /**
     * Get schedule by ID
     */
    getSchedule(scheduleId) {
        return this.schedules.get(scheduleId);
    }

    /**
     * Toggle schedule enabled/disabled
     */
    async toggleSchedule(scheduleId) {
        const schedule = this.schedules.get(scheduleId);
        if (!schedule) {
            throw new Error('Schedule not found');
        }

        return await this.updateSchedule(scheduleId, { enabled: !schedule.enabled });
    }

    /**
     * Calculate next run time for a schedule
     */
    calculateNextRun(schedule) {
        if (!schedule.enabled) return null;

        const now = new Date();
        const [hours, minutes] = schedule.time.split(':').map(Number);
        
        let nextRun = new Date();
        nextRun.setHours(hours, minutes, 0, 0);

        // If time has passed today, start from tomorrow
        if (nextRun <= now) {
            nextRun.setDate(nextRun.getDate() + 1);
        }

        // Find next valid day based on frequency and days
        switch (schedule.frequency) {
            case 'daily':
                // For daily, check if today is in allowed days
                while (!schedule.days.includes(nextRun.getDay())) {
                    nextRun.setDate(nextRun.getDate() + 1);
                }
                break;
            
            case 'weekly':
                // For weekly, find next occurrence of the first allowed day
                const targetDay = schedule.days[0];
                const daysUntilTarget = (targetDay - nextRun.getDay() + 7) % 7;
                if (daysUntilTarget > 0) {
                    nextRun.setDate(nextRun.getDate() + daysUntilTarget);
                }
                break;
            
            case 'monthly':
                // For monthly, use the same day of next month
                nextRun.setMonth(nextRun.getMonth() + 1);
                break;
            
            case 'custom':
                // For custom, use the days array as specific days of week
                while (!schedule.days.includes(nextRun.getDay())) {
                    nextRun.setDate(nextRun.getDate() + 1);
                }
                break;
        }

        return nextRun;
    }

    /**
     * Set up timer for a schedule
     */
    setupScheduleTimer(schedule) {
        if (!schedule.enabled || !schedule.nextRun) return;

        const now = new Date();
        const timeUntilRun = schedule.nextRun.getTime() - now.getTime();

        if (timeUntilRun <= 0) {
            // Should run now, recalculate
            schedule.nextRun = this.calculateNextRun(schedule);
            return this.setupScheduleTimer(schedule);
        }

        // Clear existing timer
        this.clearScheduleTimer(schedule.id);

        // Set new timer
        const timerId = setTimeout(() => {
            this.evaluateSchedule(schedule.id);
        }, Math.min(timeUntilRun, 2147483647)); // Max setTimeout value

        this.activeTimers.set(schedule.id, timerId);
    }

    /**
     * Clear timer for a schedule
     */
    clearScheduleTimer(scheduleId) {
        const timerId = this.activeTimers.get(scheduleId);
        if (timerId) {
            clearTimeout(timerId);
            this.activeTimers.delete(scheduleId);
        }
    }

    /**
     * Evaluate if a schedule should run based on conditions
     */
    async evaluateSchedule(scheduleId) {
        try {
            const schedule = this.schedules.get(scheduleId);
            if (!schedule || !schedule.enabled) return;

            this.notifyListeners('schedule_evaluating', { schedule, conditions: this.simulatedConditions });

            // Check all conditions
            const conditionResults = this.checkConditions(schedule.conditions);
            
            if (conditionResults.canRun) {
                // All conditions met, execute backup
                await this.executeScheduledBackup(schedule);
            } else {
                // Conditions not met, reschedule for later
                this.notifyListeners('schedule_conditions_not_met', { 
                    schedule, 
                    conditions: this.simulatedConditions,
                    failedConditions: conditionResults.failedConditions
                });
                
                // Retry in 30 minutes
                setTimeout(() => this.evaluateSchedule(scheduleId), 30 * 60 * 1000);
            }
        } catch (error) {
            console.error('Error evaluating schedule:', error);
            this.notifyListeners('schedule_error', { scheduleId, error: error.message });
        }
    }

    /**
     * Check if conditions are met for backup execution
     */
    checkConditions(conditions) {
        const results = {
            canRun: true,
            failedConditions: []
        };

        // Check WiFi requirement
        if (conditions.wifiOnly && this.simulatedConditions.networkType !== 'wifi') {
            results.canRun = false;
            results.failedConditions.push({
                condition: 'wifiOnly',
                required: 'WiFi connection',
                current: this.simulatedConditions.networkType
            });
        }

        // Check battery level
        if (this.simulatedConditions.batteryLevel < conditions.minBatteryLevel) {
            results.canRun = false;
            results.failedConditions.push({
                condition: 'minBatteryLevel',
                required: `${conditions.minBatteryLevel}%`,
                current: `${this.simulatedConditions.batteryLevel}%`
            });
        }

        // Check storage usage
        if (this.simulatedConditions.storageUsage > conditions.maxStorageUsage) {
            results.canRun = false;
            results.failedConditions.push({
                condition: 'maxStorageUsage',
                required: `Under ${conditions.maxStorageUsage}%`,
                current: `${this.simulatedConditions.storageUsage}%`
            });
        }

        // Check charging requirement
        if (conditions.requireCharging && !this.simulatedConditions.isCharging) {
            results.canRun = false;
            results.failedConditions.push({
                condition: 'requireCharging',
                required: 'Device charging',
                current: 'Not charging'
            });
        }

        // Check idle requirement
        if (conditions.requireIdle && !this.simulatedConditions.deviceIdle) {
            results.canRun = false;
            results.failedConditions.push({
                condition: 'requireIdle',
                required: 'Device idle',
                current: 'Device in use'
            });
        }

        return results;
    }

    /**
     * Execute a scheduled backup
     */
    async executeScheduledBackup(schedule) {
        try {
            this.notifyListeners('scheduled_backup_starting', schedule);

            // Update schedule run tracking
            schedule.lastRun = new Date();
            schedule.runCount++;
            schedule.nextRun = this.calculateNextRun(schedule);

            // Simulate backup execution
            const backupResult = await this.simulateBackupExecution(schedule);

            if (backupResult.success) {
                schedule.successCount++;
                this.notifyListeners('scheduled_backup_completed', { schedule, result: backupResult });
            } else {
                this.notifyListeners('scheduled_backup_failed', { schedule, error: backupResult.error });
            }

            // Save updated schedule
            await this.saveSchedule(schedule);

            // Set up next timer
            this.setupScheduleTimer(schedule);

        } catch (error) {
            console.error('Error executing scheduled backup:', error);
            this.notifyListeners('scheduled_backup_error', { schedule, error: error.message });
        }
    }

    /**
     * Simulate backup execution
     */
    async simulateBackupExecution(schedule) {
        return new Promise((resolve) => {
            // Simulate backup process
            const duration = 5000 + Math.random() * 10000; // 5-15 seconds
            const success = Math.random() > 0.1; // 90% success rate

            setTimeout(() => {
                if (success) {
                    resolve({
                        success: true,
                        duration,
                        filesBackedUp: Math.floor(Math.random() * 100) + 50,
                        dataSize: Math.floor(Math.random() * 50) + 10, // MB
                        dataTypes: schedule.dataTypes
                    });
                } else {
                    resolve({
                        success: false,
                        error: 'Network timeout during backup',
                        duration
                    });
                }
            }, duration);
        });
    }

    /**
     * Update simulated device conditions
     */
    updateSimulatedConditions(conditions) {
        Object.assign(this.simulatedConditions, conditions);
        this.notifyListeners('conditions_updated', this.simulatedConditions);
    }

    /**
     * Get current simulated conditions
     */
    getSimulatedConditions() {
        return { ...this.simulatedConditions };
    }

    /**
     * Start periodic schedule evaluation
     */
    startScheduleEvaluation() {
        // Check schedules every minute
        setInterval(() => {
            this.schedules.forEach(schedule => {
                if (schedule.enabled && schedule.nextRun) {
                    const now = new Date();
                    const timeUntilRun = schedule.nextRun.getTime() - now.getTime();
                    
                    // If it's time to run (within 1 minute)
                    if (timeUntilRun <= 60000 && timeUntilRun > 0) {
                        this.evaluateSchedule(schedule.id);
                    }
                }
            });
        }, 60000); // Every minute
    }

    /**
     * Test a schedule without executing
     */
    testSchedule(schedule) {
        const conditionResults = this.checkConditions(schedule.conditions);
        const nextRun = this.calculateNextRun(schedule);
        
        return {
            canRunNow: conditionResults.canRun,
            failedConditions: conditionResults.failedConditions,
            nextRun,
            timeUntilNextRun: nextRun ? nextRun.getTime() - new Date().getTime() : null,
            currentConditions: this.simulatedConditions
        };
    }

    /**
     * Get schedule statistics
     */
    getScheduleStats() {
        const schedules = Array.from(this.schedules.values());
        
        return {
            totalSchedules: schedules.length,
            enabledSchedules: schedules.filter(s => s.enabled).length,
            totalRuns: schedules.reduce((sum, s) => sum + s.runCount, 0),
            successfulRuns: schedules.reduce((sum, s) => sum + s.successCount, 0),
            successRate: schedules.length > 0 ? 
                (schedules.reduce((sum, s) => sum + s.successCount, 0) / 
                 Math.max(1, schedules.reduce((sum, s) => sum + s.runCount, 0))) * 100 : 0,
            nextScheduledRun: this.getNextScheduledRun()
        };
    }

    /**
     * Get the next scheduled run across all schedules
     */
    getNextScheduledRun() {
        const enabledSchedules = Array.from(this.schedules.values())
            .filter(s => s.enabled && s.nextRun);
        
        if (enabledSchedules.length === 0) return null;
        
        return enabledSchedules.reduce((earliest, schedule) => {
            return !earliest || schedule.nextRun < earliest.nextRun ? schedule : earliest;
        });
    }

    // Database operations (simulated for demo)
    async loadSchedules() {
        // In a real app, this would load from Supabase
        // For demo, we'll use localStorage
        try {
            const stored = localStorage.getItem('safekeep_schedules');
            if (stored) {
                const schedules = JSON.parse(stored);
                schedules.forEach(schedule => {
                    // Convert date strings back to Date objects
                    schedule.createdAt = new Date(schedule.createdAt);
                    schedule.updatedAt = new Date(schedule.updatedAt);
                    schedule.lastRun = schedule.lastRun ? new Date(schedule.lastRun) : null;
                    schedule.nextRun = schedule.nextRun ? new Date(schedule.nextRun) : null;
                    
                    this.schedules.set(schedule.id, schedule);
                    
                    // Set up timer if enabled
                    if (schedule.enabled) {
                        this.setupScheduleTimer(schedule);
                    }
                });
            }
        } catch (error) {
            console.error('Failed to load schedules:', error);
        }
    }

    async saveSchedule(schedule) {
        // In a real app, this would save to Supabase
        // For demo, we'll use localStorage
        try {
            const schedules = Array.from(this.schedules.values());
            localStorage.setItem('safekeep_schedules', JSON.stringify(schedules));
        } catch (error) {
            console.error('Failed to save schedule:', error);
        }
    }

    async removeScheduleFromDatabase(scheduleId) {
        // Update localStorage
        try {
            const schedules = Array.from(this.schedules.values());
            localStorage.setItem('safekeep_schedules', JSON.stringify(schedules));
        } catch (error) {
            console.error('Failed to remove schedule from database:', error);
        }
    }

    generateId() {
        return 'schedule_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getCurrentUserId() {
        // In a real app, get from auth
        return 'demo_user';
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.ScheduleManager = ScheduleManager;
}