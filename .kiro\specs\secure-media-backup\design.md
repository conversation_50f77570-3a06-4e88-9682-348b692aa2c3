# Design Document - Secure Media Backup

## Overview

The Secure Media Backup feature provides comprehensive backup functionality for contacts, SMS messages, and photos (excluding videos) with end-to-end encryption. The system leverages the existing React Native architecture with Supabase backend, Redux state management, and modular service architecture.

The design builds upon existing services (ContactBackupService, MessageBackupService, PhotoBackupService) and integrates with the current encryption and cloud storage infrastructure to provide a seamless, secure backup experience.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[Backup UI Components] --> BM[Backup Manager]
    BM --> CBS[Contact Backup Service]
    BM --> MBS[Message Backup Service] 
    BM --> PBS[Photo Backup Service]
    
    CBS --> PS[Permission Service]
    MBS --> PS
    PBS --> PS
    
    CBS --> ES[Encryption Service]
    MBS --> ES
    PBS --> ES
    
    ES --> CSS[Cloud Storage Service]
    CSS --> SB[Supabase Backend]
    
    BM --> BSM[Backup Session Manager]
    BSM --> RS[Redux Store]
```

### Service Layer Architecture

The backup system follows a modular service architecture where each data type (contacts, messages, photos) has its dedicated service that handles:

1. **Data Access**: Platform-specific APIs for accessing device data
2. **Filtering**: Excluding videos from photo backup, handling platform limitations for SMS
3. **Encryption**: End-to-end encryption before transmission
4. **Upload**: Secure transmission to cloud storage
5. **Progress Tracking**: Real-time progress updates and error handling

## Components and Interfaces

### Core Interfaces

```typescript
interface BackupItem {
  id: string;
  type: 'contact' | 'message' | 'photo';
  data: any;
  metadata: {
    size: number;
    timestamp: Date;
    checksum: string;
  };
}

interface BackupSession {
  id: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  progress: {
    contacts: BackupProgress;
    messages: BackupProgress;
    photos: BackupProgress;
  };
  totalItems: number;
  completedItems: number;
}

interface BackupProgress {
  total: number;
  completed: number;
  failed: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  errors: BackupError[];
}

interface BackupConfiguration {
  autoBackup: boolean;
  wifiOnly: boolean;
  includeContacts: boolean;
  includeMessages: boolean;
  includePhotos: boolean;
  compressionLevel: 'none' | 'low' | 'medium' | 'high';
}
```

### Service Components

#### 1. Enhanced Backup Manager
- **Purpose**: Orchestrates the entire backup process across all data types
- **Responsibilities**:
  - Coordinate backup sessions
  - Manage progress tracking
  - Handle error recovery
  - Enforce backup policies (WiFi-only, battery level)
  - Provide unified API for UI components

#### 2. Enhanced Contact Backup Service
- **Purpose**: Handle contact data extraction and backup
- **Key Features**:
  - Access device contact database
  - Extract contact fields (name, phone, email, etc.)
  - Handle contact groups and custom fields
  - Manage contact permissions

#### 3. Enhanced Message Backup Service
- **Purpose**: Handle SMS message extraction and backup
- **Key Features**:
  - Access SMS database (platform-dependent)
  - Extract message threads and metadata
  - Handle MMS attachments (excluding videos)
  - Manage SMS permissions and platform limitations

#### 4. Enhanced Photo Backup Service
- **Purpose**: Handle photo extraction and backup with video exclusion
- **Key Features**:
  - Scan camera roll and photo library
  - Filter out video files by extension and MIME type
  - Extract photo metadata (EXIF, location, timestamp)
  - Handle large photo files with chunked upload
  - Manage photo library permissions

### UI Components

#### 1. Backup Dashboard
- Real-time progress display for all data types
- Backup history and statistics
- Quick action buttons for manual backup
- Settings access

#### 2. Backup Progress Screen
- Individual progress bars for contacts, messages, photos
- Current item being processed
- Estimated time remaining
- Cancel backup option

#### 3. Backup Settings Screen
- Toggle for each data type
- Network preferences (WiFi-only)
- Automatic backup scheduling
- Compression settings

## Data Models

### Database Schema (Supabase)

```sql
-- Backup Sessions Table
CREATE TABLE backup_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ,
  status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'cancelled')),
  total_items INTEGER DEFAULT 0,
  completed_items INTEGER DEFAULT 0,
  configuration JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Backup Items Table
CREATE TABLE backup_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES backup_sessions(id),
  item_type TEXT NOT NULL CHECK (item_type IN ('contact', 'message', 'photo')),
  original_id TEXT NOT NULL,
  encrypted_data BYTEA NOT NULL,
  metadata JSONB,
  file_path TEXT,
  checksum TEXT,
  size_bytes BIGINT,
  status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')),
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Backup Progress Table
CREATE TABLE backup_progress (
  session_id UUID REFERENCES backup_sessions(id),
  item_type TEXT NOT NULL CHECK (item_type IN ('contact', 'message', 'photo')),
  total_count INTEGER DEFAULT 0,
  completed_count INTEGER DEFAULT 0,
  failed_count INTEGER DEFAULT 0,
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (session_id, item_type)
);
```

### Local Storage Schema (AsyncStorage)

```typescript
interface LocalBackupState {
  lastBackupTime: string;
  backupConfiguration: BackupConfiguration;
  pendingSessions: string[];
  failedItems: BackupItem[];
  backupStatistics: {
    totalBackups: number;
    totalItems: number;
    lastSuccessfulBackup: string;
  };
}
```

## Error Handling

### Error Categories

1. **Permission Errors**: User denies access to contacts, messages, or photos
2. **Network Errors**: Connection failures, timeout, server errors
3. **Storage Errors**: Insufficient cloud storage, quota exceeded
4. **Encryption Errors**: Key generation failures, encryption/decryption errors
5. **Platform Errors**: OS-specific limitations, API unavailability

### Error Recovery Strategies

1. **Retry Logic**: Exponential backoff for transient network errors
2. **Partial Backup**: Continue with available data types if one fails
3. **Resume Capability**: Resume interrupted backups from last checkpoint
4. **User Notification**: Clear error messages with actionable steps
5. **Fallback Options**: Alternative backup methods when primary fails

### Error Handling Flow

```mermaid
graph TD
    A[Backup Operation] --> B{Error Occurred?}
    B -->|No| C[Continue Backup]
    B -->|Yes| D{Error Type}
    
    D -->|Permission| E[Request Permission]
    D -->|Network| F[Retry with Backoff]
    D -->|Storage| G[Notify User - Upgrade Storage]
    D -->|Encryption| H[Regenerate Keys]
    D -->|Platform| I[Skip Item - Log Error]
    
    E --> J{Permission Granted?}
    J -->|Yes| C
    J -->|No| K[Skip Data Type]
    
    F --> L{Retry Successful?}
    L -->|Yes| C
    L -->|No| M[Mark as Failed]
    
    G --> N[Pause Backup]
    H --> O{Key Generation OK?}
    O -->|Yes| C
    O -->|No| M
    
    I --> C
    K --> C
    M --> P[Update Progress]
    N --> P
    P --> Q[Continue with Next Item]
```

## Testing Strategy

### Unit Testing

1. **Service Layer Tests**
   - Mock device APIs for contacts, messages, photos
   - Test encryption/decryption functionality
   - Validate data filtering (video exclusion)
   - Test error handling scenarios

2. **Component Tests**
   - UI component rendering with different states
   - Progress indicator accuracy
   - User interaction handling
   - Navigation flow testing

### Integration Testing

1. **End-to-End Backup Flow**
   - Complete backup process from UI to storage
   - Cross-platform compatibility (iOS/Android)
   - Network condition testing (WiFi, cellular, offline)
   - Large dataset handling

2. **Error Scenario Testing**
   - Permission denial handling
   - Network interruption recovery
   - Storage quota exceeded scenarios
   - Encryption key corruption

### Performance Testing

1. **Large Dataset Handling**
   - Backup performance with 10,000+ photos
   - Memory usage optimization
   - Battery consumption monitoring
   - Network bandwidth utilization

2. **Concurrent Operations**
   - Multiple backup sessions
   - Background processing efficiency
   - UI responsiveness during backup

### Security Testing

1. **Encryption Validation**
   - End-to-end encryption verification
   - Key management security
   - Data transmission security
   - Storage encryption at rest

2. **Permission Testing**
   - Minimal permission requirements
   - Permission revocation handling
   - Data access auditing

### Platform-Specific Testing

1. **iOS Testing**
   - Photo library access with limited permissions
   - Background app refresh limitations
   - iOS-specific contact fields

2. **Android Testing**
   - SMS access restrictions (Android 10+)
   - Scoped storage compliance
   - Battery optimization interference