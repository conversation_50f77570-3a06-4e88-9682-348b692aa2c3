import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Card, Text, Button, Chip } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

import PermissionService, { PermissionStatus } from '../../services/PermissionService';
import { COLORS, SPACING } from '../../utils/constants';

interface PermissionStatusProps {
  onPermissionChange?: (permissions: Record<string, PermissionStatus>) => void;
}

const PermissionStatusComponent: React.FC<PermissionStatusProps> = ({ onPermissionChange }) => {
  const navigation = useNavigation();
  const [permissions, setPermissions] = useState<Record<string, PermissionStatus>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    try {
      const currentPermissions = await PermissionService.checkAllPermissions();
      setPermissions(currentPermissions);
      onPermissionChange?.(currentPermissions);
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const handleFixPermissions = () => {
    Alert.alert(
      '🔧 Fix Permissions',
      'Would you like to review and update your permissions? This will help SafeKeep work better for you.',
      [
        {
          text: 'Not Now',
          style: 'cancel'
        },
        {
          text: 'Fix Permissions',
          onPress: () => navigation.navigate('PermissionSetup' as never)
        }
      ]
    );
  };

  const requestMissingPermissions = async () => {
    setIsLoading(true);
    try {
      const results = await PermissionService.requestAllPermissions();
      setPermissions(results);
      onPermissionChange?.(results);
      
      const summary = PermissionService.getPermissionSummary(results);
      
      if (summary.allGranted) {
        Alert.alert(
          '✅ Perfect!',
          'All permissions are now set up. SafeKeep can now backup all your important data!',
          [{ text: 'Great!' }]
        );
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPermissionSummary = () => {
    return PermissionService.getPermissionSummary(permissions);
  };

  const getStatusIcon = () => {
    const summary = getPermissionSummary();
    
    if (summary.allGranted) {
      return { name: 'shield-check', color: COLORS.success };
    } else if (summary.grantedCount > 0) {
      return { name: 'shield-alert', color: COLORS.warning };
    } else {
      return { name: 'shield-off', color: COLORS.error };
    }
  };

  const getStatusMessage = () => {
    const summary = getPermissionSummary();
    
    if (summary.allGranted) {
      return {
        title: 'All Permissions Ready',
        message: 'SafeKeep has all the permissions needed to keep your data safe!'
      };
    } else if (summary.grantedCount > 0) {
      return {
        title: 'Some Permissions Missing',
        message: `${summary.grantedCount} of ${summary.totalCount} permissions granted. Some features may be limited.`
      };
    } else {
      return {
        title: 'Permissions Needed',
        message: 'SafeKeep needs permissions to backup your important data.'
      };
    }
  };

  const summary = getPermissionSummary();
  const statusIcon = getStatusIcon();
  const statusMessage = getStatusMessage();

  if (summary.totalCount === 0) {
    return null; // Don't show if no permissions to check
  }

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <Icon name={statusIcon.name} size={24} color={statusIcon.color} />
          <Text variant="titleMedium" style={styles.title}>
            {statusMessage.title}
          </Text>
        </View>

        <Text variant="bodyMedium" style={styles.message}>
          {statusMessage.message}
        </Text>

        <View style={styles.permissionList}>
          <View style={styles.permissionRow}>
            <Icon name="camera" size={20} color={COLORS.primary} />
            <Text style={styles.permissionText}>Photos</Text>
            <Chip 
              style={[
                styles.statusChip,
                { backgroundColor: permissions.photos?.granted ? COLORS.success : COLORS.error }
              ]}
              textStyle={{ color: '#fff', fontSize: 12 }}
            >
              {permissions.photos?.granted ? 'OK' : 'Missing'}
            </Chip>
          </View>

          <View style={styles.permissionRow}>
            <Icon name="contacts" size={20} color={COLORS.primary} />
            <Text style={styles.permissionText}>Contacts</Text>
            <Chip 
              style={[
                styles.statusChip,
                { backgroundColor: permissions.contacts?.granted ? COLORS.success : COLORS.error }
              ]}
              textStyle={{ color: '#fff', fontSize: 12 }}
            >
              {permissions.contacts?.granted ? 'OK' : 'Missing'}
            </Chip>
          </View>

          {!permissions.sms?.unavailable && (
            <View style={styles.permissionRow}>
              <Icon name="message-text" size={20} color={COLORS.primary} />
              <Text style={styles.permissionText}>Messages</Text>
              <Chip 
                style={[
                  styles.statusChip,
                  { backgroundColor: permissions.sms?.granted ? COLORS.success : COLORS.warning }
                ]}
                textStyle={{ color: '#fff', fontSize: 12 }}
              >
                {permissions.sms?.granted ? 'OK' : 'Optional'}
              </Chip>
            </View>
          )}
        </View>

        {!summary.allGranted && (
          <View style={styles.actions}>
            <Button
              mode="contained"
              onPress={requestMissingPermissions}
              disabled={isLoading}
              loading={isLoading}
              style={styles.primaryButton}
              icon="shield-check"
            >
              Grant Permissions
            </Button>
            
            <Button
              mode="outlined"
              onPress={handleFixPermissions}
              style={styles.secondaryButton}
            >
              Review Settings
            </Button>
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  title: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  message: {
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
    lineHeight: 20,
  },
  permissionList: {
    marginBottom: SPACING.md,
  },
  permissionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
    justifyContent: 'space-between',
  },
  permissionText: {
    flex: 1,
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  statusChip: {
    height: 24,
  },
  actions: {
    gap: SPACING.sm,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    borderColor: COLORS.primary,
  },
});

export default PermissionStatusComponent;
