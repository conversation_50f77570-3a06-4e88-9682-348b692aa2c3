import { useSelector, useDispatch } from 'react-redux';
import { useCallback, useMemo } from 'react';
import { RootState, AppDispatch } from '../index';
import {
  startBackupSession,
  updateBackupSession,
  completeBackupSession,
  cancelBackupSession,
  updateDataTypeProgress,
  addProgressError,
  updateConfiguration,
  calculateStatistics,
  clearErrors,
  removeError,
  setLoading,
  resetBackupState,
  loadBackupHistory,
  saveBackupHistory,
  BackupStatistics,
} from '../slices/backupSlice';
import { BackupSession, BackupProgress, BackupError, BackupConfiguration } from '../../types/backup';

// Selectors
export const useBackupState = () => {
  return useSelector((state: RootState) => state.backup);
};

export const useCurrentBackupSession = () => {
  return useSelector((state: RootState) => state.backup.currentSession);
};

export const useBackupProgress = () => {
  return useSelector((state: RootState) => state.backup.realTimeProgress);
};

export const useBackupHistory = () => {
  return useSelector((state: RootState) => state.backup.backupHistory);
};

export const useBackupStatistics = () => {
  return useSelector((state: RootState) => state.backup.statistics);
};

export const useBackupConfiguration = () => {
  return useSelector((state: RootState) => state.backup.configuration);
};

export const useBackupErrors = () => {
  return useSelector((state: RootState) => state.backup.errors);
};

export const useIsBackupInProgress = () => {
  return useSelector((state: RootState) => state.backup.isBackupInProgress);
};

export const useBackupLoading = () => {
  return useSelector((state: RootState) => state.backup.loading);
};

// Computed selectors
export const useOverallBackupProgress = () => {
  return useSelector((state: RootState) => {
    const { realTimeProgress } = state.backup;
    const totalItems = Object.values(realTimeProgress).reduce((sum, p) => sum + p.total, 0);
    const completedItems = Object.values(realTimeProgress).reduce((sum, p) => sum + p.completed, 0);
    const failedItems = Object.values(realTimeProgress).reduce((sum, p) => sum + p.failed, 0);
    
    return {
      totalItems,
      completedItems,
      failedItems,
      percentage: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0,
      isComplete: totalItems > 0 && completedItems === totalItems,
      hasErrors: failedItems > 0,
    };
  });
};

export const useRecentBackupSessions = (limit: number = 10) => {
  return useSelector((state: RootState) => 
    state.backup.backupHistory.slice(0, limit)
  );
};

export const useBackupSuccessRate = () => {
  return useSelector((state: RootState) => {
    const { backupHistory } = state.backup;
    if (backupHistory.length === 0) return 0;
    
    const successfulBackups = backupHistory.filter(session => session.status === 'completed').length;
    return Math.round((successfulBackups / backupHistory.length) * 100);
  });
};

export const useLastSuccessfulBackup = () => {
  return useSelector((state: RootState) => {
    const successfulBackup = state.backup.backupHistory.find(session => session.status === 'completed');
    return successfulBackup?.endTime;
  });
};

// Action hooks
export const useBackupActions = () => {
  const dispatch = useDispatch<AppDispatch>();

  return useMemo(() => ({
    startSession: useCallback((session: BackupSession) => {
      dispatch(startBackupSession(session));
    }, [dispatch]),

    updateSession: useCallback((updates: Partial<BackupSession>) => {
      dispatch(updateBackupSession(updates));
    }, [dispatch]),

    completeSession: useCallback((endTime: Date, status: BackupSession['status']) => {
      dispatch(completeBackupSession({ endTime, status }));
    }, [dispatch]),

    cancelSession: useCallback(() => {
      dispatch(cancelBackupSession());
    }, [dispatch]),

    updateProgress: useCallback((dataType: 'contacts' | 'messages' | 'photos', progress: Partial<BackupProgress>) => {
      dispatch(updateDataTypeProgress({ dataType, progress }));
    }, [dispatch]),

    addError: useCallback((dataType: 'contacts' | 'messages' | 'photos', error: BackupError) => {
      dispatch(addProgressError({ dataType, error }));
    }, [dispatch]),

    updateConfig: useCallback((config: Partial<BackupConfiguration>) => {
      dispatch(updateConfiguration(config));
    }, [dispatch]),

    calculateStats: useCallback(() => {
      dispatch(calculateStatistics());
    }, [dispatch]),

    clearAllErrors: useCallback(() => {
      dispatch(clearErrors());
    }, [dispatch]),

    removeErrorById: useCallback((errorId: string) => {
      dispatch(removeError(errorId));
    }, [dispatch]),

    setLoadingState: useCallback((loading: boolean) => {
      dispatch(setLoading(loading));
    }, [dispatch]),

    resetState: useCallback(() => {
      dispatch(resetBackupState());
    }, [dispatch]),

    loadHistory: useCallback(() => {
      return dispatch(loadBackupHistory());
    }, [dispatch]),

    saveHistory: useCallback((data: { history: BackupSession[]; statistics: BackupStatistics; configuration: BackupConfiguration }) => {
      return dispatch(saveBackupHistory(data));
    }, [dispatch]),
  }), [dispatch]);
};

// Utility hooks
export const useBackupProgressByType = (dataType: 'contacts' | 'messages' | 'photos') => {
  return useSelector((state: RootState) => state.backup.realTimeProgress[dataType]);
};

export const useBackupErrorsByType = (dataType: 'contacts' | 'messages' | 'photos') => {
  return useSelector((state: RootState) => 
    state.backup.realTimeProgress[dataType].errors
  );
};

export const useBackupSessionDuration = () => {
  return useSelector((state: RootState) => {
    const { currentSession } = state.backup;
    if (!currentSession || !currentSession.startTime) return 0;
    
    const endTime = currentSession.endTime || new Date();
    const startTime = new Date(currentSession.startTime);
    
    return Math.floor((endTime.getTime() - startTime.getTime()) / 1000); // Duration in seconds
  });
};

export const useEstimatedTimeRemaining = () => {
  return useSelector((state: RootState) => {
    const { currentSession, realTimeProgress } = state.backup;
    if (!currentSession || !currentSession.startTime) return null;
    
    const totalItems = Object.values(realTimeProgress).reduce((sum, p) => sum + p.total, 0);
    const completedItems = Object.values(realTimeProgress).reduce((sum, p) => sum + p.completed, 0);
    
    if (completedItems === 0 || totalItems === 0) return null;
    
    const elapsedTime = Date.now() - new Date(currentSession.startTime).getTime();
    const averageTimePerItem = elapsedTime / completedItems;
    const remainingItems = totalItems - completedItems;
    
    return Math.floor((remainingItems * averageTimePerItem) / 1000); // Estimated seconds remaining
  });
};

// Data formatting hooks
export const useFormattedBackupStatistics = () => {
  const statistics = useBackupStatistics();
  
  return useMemo(() => ({
    ...statistics,
    formattedDataSize: formatBytes(statistics.totalDataSize),
    formattedAverageTime: formatDuration(statistics.averageBackupTime),
    formattedSuccessRate: `${statistics.successRate.toFixed(1)}%`,
    dataTypeBreakdown: {
      contacts: {
        ...statistics.dataTypeBreakdown.contacts,
        formattedSize: formatBytes(statistics.dataTypeBreakdown.contacts.size),
      },
      messages: {
        ...statistics.dataTypeBreakdown.messages,
        formattedSize: formatBytes(statistics.dataTypeBreakdown.messages.size),
      },
      photos: {
        ...statistics.dataTypeBreakdown.photos,
        formattedSize: formatBytes(statistics.dataTypeBreakdown.photos.size),
      },
    },
  }), [statistics]);
};

// Utility functions
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};