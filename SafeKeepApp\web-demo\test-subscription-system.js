/**
 * Test Suite for Subscription Tier Management System
 * Tests subscription tiers, usage tracking, and quota enforcement
 */

class SubscriptionSystemTester {
    constructor() {
        this.testResults = [];
        this.setupTestEnvironment();
    }

    /**
     * Setup test environment
     */
    setupTestEnvironment() {
        // Initialize components
        this.stripeManager = new StripeManager();
        this.tierConfig = new SubscriptionTierConfig();
        this.subscriptionManager = new SubscriptionManager(this.stripeManager);
        this.quotaManager = new UsageQuotaManager(this.subscriptionManager, this.tierConfig);
        
        console.log('🧪 Test environment initialized');
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting Subscription System Tests...');
        
        try {
            await this.testTierConfiguration();
            await this.testSubscriptionManager();
            await this.testUsageTracking();
            await this.testQuotaEnforcement();
            await this.testUpgradeDowngrade();
            await this.testFeatureAccess();
            
            this.displayTestResults();
        } catch (error) {
            console.error('❌ Test suite failed:', error);
        }
    }

    /**
     * Test tier configuration
     */
    async testTierConfiguration() {
        console.log('📋 Testing Tier Configuration...');
        
        // Test getting all tiers
        const tiers = this.tierConfig.getAllTiers();
        this.assert(tiers.length === 3, 'Should have 3 tiers');
        this.assert(tiers.some(t => t.id === 'free'), 'Should have free tier');
        this.assert(tiers.some(t => t.id === 'basic'), 'Should have basic tier');
        this.assert(tiers.some(t => t.id === 'premium'), 'Should have premium tier');
        
        // Test tier features
        const freeTier = this.tierConfig.getTier('free');
        this.assert(freeTier.features.backup_encryption === true, 'Free tier should have basic encryption');
        this.assert(freeTier.features.advanced_encryption === false, 'Free tier should not have advanced encryption');
        
        const premiumTier = this.tierConfig.getTier('premium');
        this.assert(premiumTier.features.priority_support === true, 'Premium tier should have priority support');
        this.assert(premiumTier.limits.maxBackupsPerMonth === -1, 'Premium tier should have unlimited backups');
        
        // Test tier comparison
        const comparison = this.tierConfig.calculateTierComparison();
        this.assert(comparison.tiers.length === 3, 'Comparison should include all tiers');
        this.assert(comparison.features.length > 0, 'Comparison should include features');
        
        console.log('✅ Tier Configuration tests passed');
    }

    /**
     * Test subscription manager
     */
    async testSubscriptionManager() {
        console.log('💳 Testing Subscription Manager...');
        
        // Initialize with test user
        await this.stripeManager.initialize();
        await this.subscriptionManager.initialize('test_user_123');
        
        // Test default subscription
        const subscription = this.subscriptionManager.getCurrentSubscription();
        this.assert(subscription.tierId === 'free', 'Should default to free tier');
        this.assert(subscription.status === 'active', 'Should be active by default');
        
        // Test feature access
        this.assert(this.subscriptionManager.hasFeatureAccess('advancedEncryption') === false, 'Free tier should not have advanced encryption');
        this.assert(this.subscriptionManager.checkUsageLimit('storage') === true, 'Should allow storage usage initially');
        
        console.log('✅ Subscription Manager tests passed');
    }

    /**
     * Test usage tracking
     */
    async testUsageTracking() {
        console.log('📊 Testing Usage Tracking...');
        
        const userId = 'test_user_123';
        
        // Test initial usage
        const initialUsage = this.quotaManager.getUserUsage(userId);
        this.assert(initialUsage.storageUsed === 0, 'Initial storage usage should be 0');
        this.assert(initialUsage.backupCount === 0, 'Initial backup count should be 0');
        
        // Test tracking storage usage
        this.quotaManager.trackUsage(userId, 'storage', 0.5);
        const afterStorage = this.quotaManager.getUserUsage(userId);
        this.assert(afterStorage.storageUsed === 0.5, 'Storage usage should be tracked');
        
        // Test tracking backup usage
        this.quotaManager.trackUsage(userId, 'backup', 1);
        const afterBackup = this.quotaManager.getUserUsage(userId);
        this.assert(afterBackup.backupCount === 1, 'Backup count should be tracked');
        
        // Test usage statistics
        const stats = this.quotaManager.getUsageStatistics(userId);
        this.assert(stats.current.storageUsed === 0.5, 'Stats should reflect current usage');
        this.assert(stats.limits.storage.percentage === 50, 'Storage percentage should be calculated correctly');
        
        console.log('✅ Usage Tracking tests passed');
    }

    /**
     * Test quota enforcement
     */
    async testQuotaEnforcement() {
        console.log('🚫 Testing Quota Enforcement...');
        
        const userId = 'test_user_quota';
        
        // Initialize user with free tier (1GB storage limit)
        await this.subscriptionManager.initialize(userId);
        
        // Test storage limit enforcement
        const storageAllowed1 = this.quotaManager.trackUsage(userId, 'storage', 0.5);
        this.assert(storageAllowed1 === true, 'Should allow storage within limit');
        
        const storageAllowed2 = this.quotaManager.trackUsage(userId, 'storage', 0.6);
        this.assert(storageAllowed2 === false, 'Should deny storage over limit');
        
        // Test backup limit enforcement (free tier: 5 backups/month)
        for (let i = 0; i < 5; i++) {
            const allowed = this.quotaManager.trackUsage(userId, 'backup', 1);
            this.assert(allowed === true, `Backup ${i + 1} should be allowed`);
        }
        
        const backupDenied = this.quotaManager.trackUsage(userId, 'backup', 1);
        this.assert(backupDenied === false, 'Should deny backup over monthly limit');
        
        // Test quota warnings
        const warnings = this.quotaManager.getQuotaWarnings(userId);
        this.assert(warnings.length > 0, 'Should have quota warnings');
        
        console.log('✅ Quota Enforcement tests passed');
    }

    /**
     * Test upgrade/downgrade functionality
     */
    async testUpgradeDowngrade() {
        console.log('⬆️ Testing Upgrade/Downgrade...');
        
        const userId = 'test_user_upgrade';
        await this.subscriptionManager.initialize(userId);
        
        // Test upgrade to basic
        await this.subscriptionManager.upgradeSubscription('basic');
        const afterUpgrade = this.subscriptionManager.getCurrentSubscription();
        this.assert(afterUpgrade.tierId === 'basic', 'Should upgrade to basic tier');
        
        // Test feature access after upgrade
        this.assert(this.subscriptionManager.hasFeatureAccess('advancedEncryption') === true, 'Basic tier should have advanced encryption');
        
        // Test downgrade to free
        await this.subscriptionManager.downgradeToFree();
        const afterDowngrade = this.subscriptionManager.getCurrentSubscription();
        this.assert(afterDowngrade.tierId === 'free', 'Should downgrade to free tier');
        
        console.log('✅ Upgrade/Downgrade tests passed');
    }

    /**
     * Test feature access control
     */
    async testFeatureAccess() {
        console.log('🔐 Testing Feature Access Control...');
        
        const userId = 'test_user_features';
        
        // Test free tier features
        await this.subscriptionManager.initialize(userId);
        this.assert(this.subscriptionManager.hasFeatureAccess('advancedEncryption') === false, 'Free tier should not have advanced encryption');
        this.assert(this.subscriptionManager.hasFeatureAccess('prioritySupport') === false, 'Free tier should not have priority support');
        
        // Test basic tier features
        await this.subscriptionManager.upgradeSubscription('basic');
        this.assert(this.subscriptionManager.hasFeatureAccess('advancedEncryption') === true, 'Basic tier should have advanced encryption');
        this.assert(this.subscriptionManager.hasFeatureAccess('prioritySupport') === false, 'Basic tier should not have priority support');
        
        // Test premium tier features
        await this.subscriptionManager.upgradeSubscription('premium');
        this.assert(this.subscriptionManager.hasFeatureAccess('advancedEncryption') === true, 'Premium tier should have advanced encryption');
        this.assert(this.subscriptionManager.hasFeatureAccess('prioritySupport') === true, 'Premium tier should have priority support');
        this.assert(this.subscriptionManager.hasFeatureAccess('multiDeviceSync') === true, 'Premium tier should have multi-device sync');
        
        console.log('✅ Feature Access Control tests passed');
    }

    /**
     * Assert helper
     */
    assert(condition, message) {
        const result = {
            passed: !!condition,
            message: message,
            timestamp: new Date()
        };
        
        this.testResults.push(result);
        
        if (!condition) {
            console.error(`❌ ASSERTION FAILED: ${message}`);
            throw new Error(`Assertion failed: ${message}`);
        } else {
            console.log(`✅ ${message}`);
        }
    }

    /**
     * Display test results
     */
    displayTestResults() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log('\n📊 TEST RESULTS SUMMARY');
        console.log('========================');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${total - passed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        
        if (passed === total) {
            console.log('🎉 All tests passed!');
        } else {
            console.log('❌ Some tests failed');
            this.testResults.filter(r => !r.passed).forEach(result => {
                console.log(`  - ${result.message}`);
            });
        }
    }

    /**
     * Run performance tests
     */
    async runPerformanceTests() {
        console.log('⚡ Running Performance Tests...');
        
        const iterations = 1000;
        const userId = 'perf_test_user';
        
        // Test usage tracking performance
        const startTime = performance.now();
        
        for (let i = 0; i < iterations; i++) {
            this.quotaManager.trackUsage(userId, 'backup', 1);
        }
        
        const endTime = performance.now();
        const avgTime = (endTime - startTime) / iterations;
        
        console.log(`📈 Usage tracking: ${avgTime.toFixed(3)}ms per operation`);
        console.log(`📈 Throughput: ${(1000 / avgTime).toFixed(0)} operations/second`);
        
        this.assert(avgTime < 10, 'Usage tracking should be under 10ms per operation');
    }

    /**
     * Cleanup test environment
     */
    cleanup() {
        this.quotaManager.cleanup();
        console.log('🧹 Test environment cleaned up');
    }
}

// Auto-run tests when loaded
if (typeof window !== 'undefined') {
    window.SubscriptionSystemTester = SubscriptionSystemTester;
    
    // Auto-run tests in demo environment
    window.addEventListener('load', async () => {
        if (window.location.search.includes('test=subscription')) {
            const tester = new SubscriptionSystemTester();
            await tester.runAllTests();
            await tester.runPerformanceTests();
            tester.cleanup();
        }
    });
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionSystemTester;
}