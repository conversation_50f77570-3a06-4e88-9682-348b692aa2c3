<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep Stripe Integration Test</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .button {
            background-color: #4A90E2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background-color 0.3s;
        }
        .button:hover:not(:disabled) {
            background-color: #357abd;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .test-output {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        .test-results {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            background: white;
            border: 1px solid #e0e0e0;
        }
        .result-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .result-card {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .result-card.passed {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .result-card.failed {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .result-card.total {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .result-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        .result-label {
            font-size: 0.9rem;
            margin-top: 5px;
        }
        .test-list {
            display: grid;
            gap: 10px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-radius: 6px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        .test-item.passed {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-item.failed {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .test-status.passed {
            background: #28a745;
            color: white;
        }
        .test-status.failed {
            background: #dc3545;
            color: white;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4A90E2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .info-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .info-section h4 {
            margin-top: 0;
            color: #1976d2;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.connected {
            background: #28a745;
        }
        .status-indicator.disconnected {
            background: #dc3545;
        }
        .status-indicator.unknown {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 SafeKeep Stripe Integration Test</h1>
            <p>Comprehensive testing suite for Stripe payment infrastructure</p>
        </div>

        <!-- Server Status -->
        <div class="info-section">
            <h4>🌐 Server Status</h4>
            <div id="server-status">
                <span class="status-indicator unknown"></span>
                <span>Checking server connection...</span>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <div class="test-controls">
                <button class="button" id="run-all-tests">
                    <span id="run-all-text">Run All Tests</span>
                    <span id="run-all-loading" class="loading" style="display: none;"></span>
                </button>
                <button class="button secondary" id="run-config-tests">Configuration Tests</button>
                <button class="button secondary" id="run-customer-tests">Customer Tests</button>
                <button class="button secondary" id="run-payment-tests">Payment Tests</button>
                <button class="button secondary" id="run-subscription-tests">Subscription Tests</button>
                <button class="button secondary" id="run-webhook-tests">Webhook Tests</button>
                <button class="button danger" id="reset-test-data">Reset Test Data</button>
            </div>
        </div>

        <!-- Test Output -->
        <div class="test-section">
            <h3>📋 Test Output</h3>
            <div id="test-output" class="test-output">
                Ready to run tests. Click "Run All Tests" to begin...
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-results" id="test-results" style="display: none;">
            <h3>📊 Test Results</h3>
            <div class="result-summary" id="result-summary">
                <!-- Results will be populated here -->
            </div>
            <div class="test-list" id="test-list">
                <!-- Test list will be populated here -->
            </div>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="stripe-config.js"></script>
    <script src="stripe-manager.js"></script>
    <script src="subscription-manager.js"></script>
    <script src="test-stripe-integration.js"></script>

    <script>
        let testSuite = null;
        let isTestRunning = false;

        // Initialize on page load
        window.addEventListener('load', async () => {
            await checkServerStatus();
            setupEventListeners();
        });

        /**
         * Check server status
         */
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            
            try {
                // Try to connect to Stripe server
                const response = await fetch('http://localhost:3001/api/stripe/status');
                const status = await response.json();
                
                if (status.status === 'OK') {
                    statusElement.innerHTML = `
                        <span class="status-indicator connected"></span>
                        <span>Stripe server connected (${status.customers_count} customers, ${status.subscriptions_count} subscriptions)</span>
                    `;
                } else {
                    throw new Error('Server not ready');
                }
            } catch (error) {
                statusElement.innerHTML = `
                    <span class="status-indicator disconnected"></span>
                    <span>Stripe server not available - using demo mode only</span>
                `;
            }
        }

        /**
         * Setup event listeners
         */
        function setupEventListeners() {
            document.getElementById('run-all-tests').addEventListener('click', runAllTests);
            document.getElementById('run-config-tests').addEventListener('click', () => runSpecificTests(['config']));
            document.getElementById('run-customer-tests').addEventListener('click', () => runSpecificTests(['customer']));
            document.getElementById('run-payment-tests').addEventListener('click', () => runSpecificTests(['payment']));
            document.getElementById('run-subscription-tests').addEventListener('click', () => runSpecificTests(['subscription']));
            document.getElementById('run-webhook-tests').addEventListener('click', () => runSpecificTests(['webhook']));
            document.getElementById('reset-test-data').addEventListener('click', resetTestData);
        }

        /**
         * Run all tests
         */
        async function runAllTests() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            setRunningState(true);
            
            try {
                // Initialize test suite
                testSuite = new StripeIntegrationTest();
                
                // Redirect console.log to our output
                const originalLog = console.log;
                const originalError = console.error;
                const output = document.getElementById('test-output');
                
                console.log = (...args) => {
                    output.textContent += args.join(' ') + '\n';
                    output.scrollTop = output.scrollHeight;
                    originalLog.apply(console, args);
                };
                
                console.error = (...args) => {
                    output.textContent += 'ERROR: ' + args.join(' ') + '\n';
                    output.scrollTop = output.scrollHeight;
                    originalError.apply(console, args);
                };
                
                // Clear output
                output.textContent = '';
                
                // Initialize and run tests
                await testSuite.initialize();
                const results = await testSuite.runAllTests();
                
                // Restore console
                console.log = originalLog;
                console.error = originalError;
                
                // Display results
                displayTestResults(results);
                
            } catch (error) {
                console.error('Test suite failed:', error);
                document.getElementById('test-output').textContent += `\nFATAL ERROR: ${error.message}\n`;
            } finally {
                isTestRunning = false;
                setRunningState(false);
            }
        }

        /**
         * Run specific test categories
         */
        async function runSpecificTests(categories) {
            // For now, just run all tests
            // In a full implementation, you would filter tests by category
            await runAllTests();
        }

        /**
         * Reset test data
         */
        function resetTestData() {
            if (testSuite) {
                testSuite.resetTestData();
            }
            
            document.getElementById('test-output').textContent = 'Test data reset. Ready to run tests again...';
            document.getElementById('test-results').style.display = 'none';
        }

        /**
         * Set running state UI
         */
        function setRunningState(running) {
            const button = document.getElementById('run-all-tests');
            const text = document.getElementById('run-all-text');
            const loading = document.getElementById('run-all-loading');
            
            if (running) {
                button.disabled = true;
                text.style.display = 'none';
                loading.style.display = 'inline-block';
            } else {
                button.disabled = false;
                text.style.display = 'inline';
                loading.style.display = 'none';
            }
        }

        /**
         * Display test results
         */
        function displayTestResults(results) {
            const resultsSection = document.getElementById('test-results');
            const summarySection = document.getElementById('result-summary');
            const listSection = document.getElementById('test-list');
            
            // Show results section
            resultsSection.style.display = 'block';
            
            // Create summary cards
            summarySection.innerHTML = `
                <div class="result-card total">
                    <span class="result-number">${results.total}</span>
                    <div class="result-label">Total Tests</div>
                </div>
                <div class="result-card passed">
                    <span class="result-number">${results.passed}</span>
                    <div class="result-label">Passed</div>
                </div>
                <div class="result-card failed">
                    <span class="result-number">${results.failed}</span>
                    <div class="result-label">Failed</div>
                </div>
                <div class="result-card total">
                    <span class="result-number">${results.successRate.toFixed(1)}%</span>
                    <div class="result-label">Success Rate</div>
                </div>
            `;
            
            // Create test list
            listSection.innerHTML = '';
            results.results.forEach(test => {
                const testItem = document.createElement('div');
                testItem.className = `test-item ${test.passed ? 'passed' : 'failed'}`;
                testItem.innerHTML = `
                    <span>${test.name}</span>
                    <span class="test-status ${test.passed ? 'passed' : 'failed'}">
                        ${test.passed ? '✅ PASSED' : '❌ FAILED'}
                    </span>
                `;
                
                if (!test.passed && test.error) {
                    testItem.title = test.error;
                }
                
                listSection.appendChild(testItem);
            });
        }

        /**
         * Auto-check server status periodically
         */
        setInterval(checkServerStatus, 30000); // Check every 30 seconds
    </script>
</body>
</html>