/**
 * Verify Advanced Backup Scheduling System Integration
 * Check that all components are properly integrated
 */

function verifyScheduleSystem() {
    console.log('🔍 Verifying Advanced Backup Scheduling System Integration...');
    
    const results = {
        filesExist: true,
        classesLoaded: true,
        integration: true,
        errors: []
    };
    
    try {
        // Check if required files exist (by checking if classes are available)
        console.log('📁 Checking file availability...');
        
        if (typeof window.ScheduleManager === 'undefined') {
            results.classesLoaded = false;
            results.errors.push('ScheduleManager class not loaded');
        } else {
            console.log('✅ ScheduleManager class available');
        }
        
        if (typeof window.ScheduleConsole === 'undefined') {
            results.classesLoaded = false;
            results.errors.push('ScheduleConsole class not loaded');
        } else {
            console.log('✅ ScheduleConsole class available');
        }
        
        // Check if container exists in HTML
        const container = document.getElementById('schedule-console-container');
        if (!container) {
            results.integration = false;
            results.errors.push('Schedule console container not found in HTML');
        } else {
            console.log('✅ Schedule console container found in DOM');
        }
        
        // Check if global variables are available
        if (typeof supabase === 'undefined') {
            results.integration = false;
            results.errors.push('Supabase client not available');
        } else {
            console.log('✅ Supabase client available');
        }
        
        if (typeof adminSupabase === 'undefined') {
            results.integration = false;
            results.errors.push('Admin Supabase client not available');
        } else {
            console.log('✅ Admin Supabase client available');
        }
        
        // Test basic instantiation
        if (results.classesLoaded && results.integration) {
            console.log('🧪 Testing basic instantiation...');
            
            try {
                const testManager = new window.ScheduleManager(supabase, adminSupabase);
                console.log('✅ ScheduleManager instantiation successful');
                
                const testConsole = new window.ScheduleConsole('schedule-console-container');
                console.log('✅ ScheduleConsole instantiation successful');
                
                // Test method availability
                const managerMethods = [
                    'initialize', 'createSchedule', 'updateSchedule', 'deleteSchedule',
                    'getSchedules', 'testSchedule', 'updateSimulatedConditions'
                ];
                
                managerMethods.forEach(method => {
                    if (typeof testManager[method] !== 'function') {
                        results.errors.push(`ScheduleManager.${method} method not available`);
                    }
                });
                
                const consoleMethods = [
                    'setScheduleManager', 'showCreateScheduleModal', 'showConditionsModal',
                    'updateSchedulesList', 'updateConditionsDisplay'
                ];
                
                consoleMethods.forEach(method => {
                    if (typeof testConsole[method] !== 'function') {
                        results.errors.push(`ScheduleConsole.${method} method not available`);
                    }
                });
                
                if (results.errors.length === 0) {
                    console.log('✅ All required methods available');
                }
                
            } catch (error) {
                results.integration = false;
                results.errors.push(`Instantiation failed: ${error.message}`);
            }
        }
        
        // Summary
        console.log('\n📊 Verification Summary:');
        console.log('   Files exist:', results.filesExist ? '✅' : '❌');
        console.log('   Classes loaded:', results.classesLoaded ? '✅' : '❌');
        console.log('   Integration ready:', results.integration ? '✅' : '❌');
        
        if (results.errors.length > 0) {
            console.log('\n❌ Issues found:');
            results.errors.forEach(error => console.log('   -', error));
        } else {
            console.log('\n🎉 All verification checks passed!');
            console.log('📋 Advanced Backup Scheduling System is ready to use');
            console.log('\n💡 To test the system:');
            console.log('   1. Open the web demo in a browser');
            console.log('   2. Scroll to the "Advanced Backup Scheduling" section');
            console.log('   3. Click "New Schedule" to create a backup schedule');
            console.log('   4. Use "Conditions" to simulate device states');
            console.log('   5. Monitor the activity log for schedule events');
        }
        
        return results;
        
    } catch (error) {
        console.error('❌ Verification failed:', error);
        results.errors.push(`Verification error: ${error.message}`);
        return results;
    }
}

// Auto-run verification if loaded in browser
if (typeof window !== 'undefined') {
    window.verifyScheduleSystem = verifyScheduleSystem;
    
    // Run verification after page load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(verifyScheduleSystem, 1000);
        });
    } else {
        setTimeout(verifyScheduleSystem, 1000);
    }
}

// Export for Node.js testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { verifyScheduleSystem };
}