# Advanced Backup Scheduling System

## Overview

The Advanced Backup Scheduling System provides comprehensive backup scheduling functionality with condition-based triggers, automatic execution, and intelligent conflict resolution. This system demonstrates how SafeKeep handles automated backups based on user preferences and device conditions.

## Features

### 🕒 Schedule Management
- **Multiple Frequency Options**: Daily, weekly, monthly, and custom scheduling
- **Flexible Time Configuration**: Set specific times for backup execution
- **Day Selection**: Choose specific days of the week for backups
- **Data Type Selection**: Configure which data types to backup (contacts, messages, photos)

### 📱 Condition-Based Triggers
- **Network Requirements**: WiFi-only or allow cellular backups
- **Battery Level Monitoring**: Set minimum battery level requirements
- **Storage Usage Limits**: Prevent backups when storage is nearly full
- **Charging Status**: Require device to be charging for backups
- **Device Idle Detection**: Only backup when device is not in active use

### 🔄 Automatic Execution
- **Real-time Condition Evaluation**: Continuously monitor device conditions
- **Intelligent Retry Logic**: Automatically retry when conditions improve
- **Conflict Resolution**: Handle multiple schedules with overlapping times
- **Performance Tracking**: Monitor backup success rates and performance

### 🧪 Testing and Simulation
- **Schedule Testing**: Test schedules without executing actual backups
- **Condition Simulation**: Simulate different device conditions for testing
- **Next Run Prediction**: Calculate when schedules will next execute
- **Performance Analytics**: Track backup statistics and success rates

## Components

### ScheduleManager
Core scheduling engine that handles:
- Schedule creation, modification, and deletion
- Condition evaluation and backup execution
- Timer management and automatic scheduling
- Statistics tracking and performance monitoring

### ScheduleConsole
User interface component providing:
- Schedule configuration interface
- Condition simulation controls
- Real-time activity monitoring
- Statistics and analytics display

## Usage

### Basic Setup

```javascript
// Initialize the schedule manager
const scheduleManager = new ScheduleManager(supabase, adminSupabase);
await scheduleManager.initialize();

// Initialize the console UI
const scheduleConsole = new ScheduleConsole('schedule-container');
scheduleConsole.setScheduleManager(scheduleManager);
```

### Creating Schedules

```javascript
// Create a daily backup schedule
const dailySchedule = await scheduleManager.createSchedule({
    name: 'Daily Evening Backup',
    frequency: 'daily',
    time: '22:00',
    days: [1, 2, 3, 4, 5], // Monday through Friday
    dataTypes: ['contacts', 'messages'],
    conditions: {
        wifiOnly: true,
        minBatteryLevel: 30,
        maxStorageUsage: 80,
        requireCharging: false,
        requireIdle: true
    }
});
```

### Condition Simulation

```javascript
// Simulate device conditions
scheduleManager.updateSimulatedConditions({
    networkType: 'wifi',
    batteryLevel: 85,
    storageUsage: 45,
    isCharging: true,
    deviceIdle: true
});

// Test if schedule can run with current conditions
const testResult = scheduleManager.testSchedule(schedule);
console.log('Can run now:', testResult.canRunNow);
console.log('Failed conditions:', testResult.failedConditions);
```

### Event Handling

```javascript
// Listen for schedule events
scheduleManager.addListener((event, data) => {
    switch (event) {
        case 'scheduled_backup_starting':
            console.log('Backup starting:', data.name);
            break;
        case 'scheduled_backup_completed':
            console.log('Backup completed:', data.result);
            break;
        case 'schedule_conditions_not_met':
            console.log('Conditions not met:', data.failedConditions);
            break;
    }
});
```

## Schedule Configuration Options

### Frequency Types
- **Daily**: Run every day at specified time, optionally limited to certain days
- **Weekly**: Run once per week on specified day
- **Monthly**: Run once per month on the same date
- **Custom**: Run on specific days of the week

### Condition Parameters
- **wifiOnly**: Only backup when connected to WiFi (default: true)
- **minBatteryLevel**: Minimum battery percentage required (default: 20%)
- **maxStorageUsage**: Maximum storage usage percentage allowed (default: 80%)
- **requireCharging**: Only backup when device is charging (default: false)
- **requireIdle**: Only backup when device is not in use (default: false)

### Data Types
- **contacts**: Backup contact information
- **messages**: Backup text messages and conversations
- **photos**: Backup photos and media files

## Testing

### Quick Test
```bash
# Run quick functionality test
node quick-test-schedule.js
```

### Comprehensive Test
```bash
# Run full test suite
node test-schedule-system.js
```

### Manual Testing
1. Open the web demo
2. Navigate to the Schedule Console
3. Create test schedules with different configurations
4. Simulate various device conditions
5. Monitor automatic backup execution

## Implementation Details

### Timer Management
The system uses JavaScript `setTimeout` for scheduling with automatic cleanup and recreation when schedules are modified. Timers are limited to the maximum `setTimeout` value and are refreshed as needed.

### Condition Evaluation
Device conditions are evaluated in real-time before each scheduled backup. If conditions are not met, the system automatically retries after a configurable delay (default: 30 minutes).

### Data Persistence
Schedules are stored in localStorage for the demo, but the system is designed to work with Supabase database storage in production.

### Performance Optimization
- Efficient timer management with automatic cleanup
- Minimal memory footprint with Map-based storage
- Lazy loading of schedule data
- Debounced condition updates

## Error Handling

The system includes comprehensive error handling for:
- Network connectivity issues
- Database operation failures
- Invalid schedule configurations
- Timer management errors
- Backup execution failures

## Browser Compatibility

- Modern browsers with ES6+ support
- localStorage support required
- setTimeout/setInterval support required
- No external dependencies beyond Supabase client

## Security Considerations

- Schedule data is stored locally in demo mode
- No sensitive information in schedule configurations
- Backup execution is simulated for demo purposes
- Real implementation would include proper authentication and authorization

## Future Enhancements

- Cloud-based schedule synchronization
- Advanced conflict resolution algorithms
- Machine learning-based optimal scheduling
- Integration with device power management APIs
- Support for backup priority levels
- Advanced analytics and reporting

## Troubleshooting

### Common Issues

1. **Schedules not executing**: Check that conditions are met and schedule is enabled
2. **Timer not working**: Verify browser supports setTimeout with large values
3. **Conditions not updating**: Check that updateSimulatedConditions is called properly
4. **UI not refreshing**: Ensure event listeners are properly attached

### Debug Mode
Enable debug logging by setting `localStorage.setItem('schedule_debug', 'true')` in the browser console.

## API Reference

### ScheduleManager Methods

- `initialize()`: Initialize the schedule manager
- `createSchedule(data)`: Create a new schedule
- `updateSchedule(id, updates)`: Update existing schedule
- `deleteSchedule(id)`: Delete a schedule
- `toggleSchedule(id)`: Enable/disable a schedule
- `testSchedule(schedule)`: Test schedule without executing
- `getSchedules()`: Get all schedules
- `getScheduleStats()`: Get system statistics
- `updateSimulatedConditions(conditions)`: Update device conditions

### ScheduleConsole Methods

- `setScheduleManager(manager)`: Connect to schedule manager
- `showCreateScheduleModal()`: Open schedule creation dialog
- `showConditionsModal()`: Open conditions simulation dialog
- `refreshData()`: Refresh all displayed data

### Events

- `initialized`: System initialization complete
- `schedule_created`: New schedule created
- `schedule_updated`: Schedule modified
- `schedule_deleted`: Schedule removed
- `scheduled_backup_starting`: Backup execution beginning
- `scheduled_backup_completed`: Backup finished successfully
- `scheduled_backup_failed`: Backup execution failed
- `conditions_updated`: Device conditions changed
- `schedule_conditions_not_met`: Backup postponed due to conditions