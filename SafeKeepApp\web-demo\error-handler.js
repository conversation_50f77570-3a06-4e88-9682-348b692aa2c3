/**
 * Advanced Error Handling and Recovery System
 * Provides comprehensive error handling for all SafeKeep web demo features
 */

class SafeKeepErrorHandler {
    constructor() {
        this.errorLog = [];
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        this.baseDelay = 1000; // 1 second
        this.maxDelay = 30000; // 30 seconds
        this.errorCallbacks = new Map();
        this.recoveryStrategies = new Map();
        this.userNotificationQueue = [];
        
        this.initializeErrorCategories();
        this.setupGlobalErrorHandlers();
        this.initializeRecoveryStrategies();
    }

    initializeErrorCategories() {
        this.errorCategories = {
            AUTHENTICATION: {
                code: 'AUTH',
                severity: 'high',
                userFriendly: true,
                autoRetry: true
            },
            NETWORK: {
                code: 'NET',
                severity: 'medium',
                userFriendly: true,
                autoRetry: true
            },
            ENCRYPTION: {
                code: 'ENC',
                severity: 'high',
                userFriendly: true,
                autoRetry: false
            },
            REALTIME: {
                code: 'RT',
                severity: 'medium',
                userFriendly: true,
                autoRetry: true
            },
            BACKUP: {
                code: 'BKP',
                severity: 'medium',
                userFriendly: true,
                autoRetry: true
            },
            RESTORE: {
                code: 'RST',
                severity: 'medium',
                userFriendly: true,
                autoRetry: true
            },
            PAYMENT: {
                code: 'PAY',
                severity: 'high',
                userFriendly: true,
                autoRetry: false
            },
            SUBSCRIPTION: {
                code: 'SUB',
                severity: 'medium',
                userFriendly: true,
                autoRetry: true
            },
            DEMO_STATE: {
                code: 'DEMO',
                severity: 'low',
                userFriendly: true,
                autoRetry: true
            },
            SYSTEM: {
                code: 'SYS',
                severity: 'high',
                userFriendly: false,
                autoRetry: false
            }
        };
    }

    setupGlobalErrorHandlers() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'SYSTEM', {
                context: 'unhandled_promise_rejection',
                promise: event.promise
            });
        });

        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleError(event.error, 'SYSTEM', {
                context: 'javascript_error',
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        // Handle network errors
        window.addEventListener('offline', () => {
            this.handleNetworkStatusChange(false);
        });

        window.addEventListener('online', () => {
            this.handleNetworkStatusChange(true);
        });
    }

    initializeRecoveryStrategies() {
        this.recoveryStrategies.set('AUTHENTICATION', {
            immediate: () => this.attemptAuthRecovery(),
            graceful: () => this.enableGuestMode(),
            reset: () => this.resetAuthState()
        });

        this.recoveryStrategies.set('NETWORK', {
            immediate: () => this.enableOfflineMode(),
            graceful: () => this.queueOperationsForRetry(),
            reset: () => this.clearNetworkQueue()
        });

        this.recoveryStrategies.set('REALTIME', {
            immediate: () => this.fallbackToPolling(),
            graceful: () => this.reduceUpdateFrequency(),
            reset: () => this.resetRealtimeConnections()
        });

        this.recoveryStrategies.set('BACKUP', {
            immediate: () => this.pauseBackupOperations(),
            graceful: () => this.enablePartialBackup(),
            reset: () => this.resetBackupState()
        });

        this.recoveryStrategies.set('RESTORE', {
            immediate: () => this.pauseRestoreOperations(),
            graceful: () => this.enablePartialRestore(),
            reset: () => this.resetRestoreState()
        });

        this.recoveryStrategies.set('PAYMENT', {
            immediate: () => this.showPaymentErrorDialog(),
            graceful: () => this.enableFreeMode(),
            reset: () => this.resetPaymentState()
        });

        this.recoveryStrategies.set('DEMO_STATE', {
            immediate: () => this.recoverDemoState(),
            graceful: () => this.useDefaultDemoState(),
            reset: () => this.resetDemoState()
        });
    }

    /**
     * Main error handling method
     */
    async handleError(error, category = 'SYSTEM', context = {}) {
        const errorId = this.generateErrorId();
        const timestamp = new Date().toISOString();
        
        const errorInfo = {
            id: errorId,
            timestamp,
            category,
            error: this.serializeError(error),
            context,
            severity: this.errorCategories[category]?.severity || 'medium',
            userFriendly: this.errorCategories[category]?.userFriendly || false
        };

        // Log the error
        this.logError(errorInfo);

        // Determine if we should retry
        const shouldRetry = this.shouldRetryError(errorInfo);
        
        if (shouldRetry) {
            const retryResult = await this.attemptRetry(errorInfo);
            if (retryResult.success) {
                return retryResult;
            }
        }

        // Apply recovery strategy
        await this.applyRecoveryStrategy(errorInfo);

        // Notify user if appropriate
        if (errorInfo.userFriendly) {
            this.notifyUser(errorInfo);
        }

        // Trigger error callbacks
        this.triggerErrorCallbacks(errorInfo);

        return {
            success: false,
            errorId,
            recoveryApplied: true
        };
    }

    serializeError(error) {
        if (error instanceof Error) {
            return {
                name: error.name,
                message: error.message,
                stack: error.stack,
                cause: error.cause
            };
        }
        return { message: String(error) };
    }

    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    logError(errorInfo) {
        this.errorLog.push(errorInfo);
        
        // Keep only last 100 errors
        if (this.errorLog.length > 100) {
            this.errorLog = this.errorLog.slice(-100);
        }

        // Console logging for development
        if (process.env.NODE_ENV === 'development') {
            console.error(`[SafeKeep Error ${errorInfo.id}]`, errorInfo);
        }
    }

    shouldRetryError(errorInfo) {
        const category = this.errorCategories[errorInfo.category];
        if (!category?.autoRetry) return false;

        const retryKey = `${errorInfo.category}_${errorInfo.context.operation || 'default'}`;
        const attempts = this.retryAttempts.get(retryKey) || 0;
        
        return attempts < this.maxRetries;
    }

    async attemptRetry(errorInfo) {
        const retryKey = `${errorInfo.category}_${errorInfo.context.operation || 'default'}`;
        const attempts = this.retryAttempts.get(retryKey) || 0;
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
            this.baseDelay * Math.pow(2, attempts),
            this.maxDelay
        );

        // Add jitter to prevent thundering herd
        const jitteredDelay = delay + (Math.random() * 1000);

        this.retryAttempts.set(retryKey, attempts + 1);

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, jitteredDelay));

        try {
            // Attempt to retry the operation
            const retryResult = await this.executeRetry(errorInfo);
            
            // Clear retry count on success
            this.retryAttempts.delete(retryKey);
            
            return { success: true, result: retryResult };
        } catch (retryError) {
            // Log retry failure
            this.logError({
                ...errorInfo,
                id: this.generateErrorId(),
                timestamp: new Date().toISOString(),
                context: {
                    ...errorInfo.context,
                    retryAttempt: attempts + 1,
                    originalErrorId: errorInfo.id
                },
                error: this.serializeError(retryError)
            });

            return { success: false, error: retryError };
        }
    }

    async executeRetry(errorInfo) {
        const { category, context } = errorInfo;
        
        switch (category) {
            case 'AUTHENTICATION':
                return await this.retryAuthentication(context);
            case 'NETWORK':
                return await this.retryNetworkOperation(context);
            case 'REALTIME':
                return await this.retryRealtimeConnection(context);
            case 'BACKUP':
                return await this.retryBackupOperation(context);
            case 'RESTORE':
                return await this.retryRestoreOperation(context);
            case 'SUBSCRIPTION':
                return await this.retrySubscriptionOperation(context);
            default:
                throw new Error(`No retry strategy for category: ${category}`);
        }
    }

    async applyRecoveryStrategy(errorInfo) {
        const strategies = this.recoveryStrategies.get(errorInfo.category);
        if (!strategies) return;

        try {
            // Try immediate recovery first
            await strategies.immediate();
        } catch (immediateError) {
            try {
                // Fall back to graceful degradation
                await strategies.graceful();
            } catch (gracefulError) {
                // Last resort: reset
                await strategies.reset();
            }
        }
    }

    // Recovery Strategy Implementations
    async attemptAuthRecovery() {
        try {
            // Try to refresh the session
            if (window.supabase) {
                const { data, error } = await window.supabase.auth.refreshSession();
                if (!error && data.session) {
                    return true;
                }
            }
        } catch (error) {
            console.warn('Auth recovery failed:', error);
        }
        return false;
    }

    enableGuestMode() {
        // Enable limited functionality without authentication
        const guestUser = {
            id: 'guest',
            email: '<EMAIL>',
            isGuest: true
        };
        
        if (window.authManager) {
            window.authManager.setGuestMode(guestUser);
        }
        
        this.showUserNotification({
            type: 'info',
            title: 'Guest Mode Enabled',
            message: 'Some features are limited. Sign in for full access.',
            actions: [
                { label: 'Sign In', action: () => this.showSignInDialog() }
            ]
        });
    }

    resetAuthState() {
        if (window.authManager) {
            window.authManager.reset();
        }
        localStorage.removeItem('supabase.auth.token');
        sessionStorage.clear();
    }

    enableOfflineMode() {
        // Enable offline functionality
        if (window.offlineManager) {
            window.offlineManager.enable();
        }
        
        this.showUserNotification({
            type: 'warning',
            title: 'Offline Mode',
            message: 'Working offline. Changes will sync when connection is restored.',
            persistent: true
        });
    }

    queueOperationsForRetry() {
        // Queue failed operations for retry when network is restored
        if (window.networkQueue) {
            window.networkQueue.enableQueueing();
        }
    }

    clearNetworkQueue() {
        if (window.networkQueue) {
            window.networkQueue.clear();
        }
    }

    fallbackToPolling() {
        // Switch from WebSocket to polling for real-time updates
        if (window.realtimeManager) {
            window.realtimeManager.fallbackToPolling();
        }
    }

    reduceUpdateFrequency() {
        // Reduce update frequency to conserve resources
        if (window.realtimeManager) {
            window.realtimeManager.setUpdateInterval(5000); // 5 seconds
        }
    }

    resetRealtimeConnections() {
        if (window.realtimeManager) {
            window.realtimeManager.reset();
        }
    }

    pauseBackupOperations() {
        if (window.backupManager) {
            window.backupManager.pauseAll();
        }
        
        this.showUserNotification({
            type: 'warning',
            title: 'Backup Paused',
            message: 'Backup operations paused due to errors. Click to retry.',
            actions: [
                { label: 'Retry', action: () => this.resumeBackupOperations() }
            ]
        });
    }

    enablePartialBackup() {
        if (window.backupManager) {
            window.backupManager.enablePartialMode();
        }
    }

    resetBackupState() {
        if (window.backupManager) {
            window.backupManager.reset();
        }
    }

    pauseRestoreOperations() {
        if (window.restoreManager) {
            window.restoreManager.pauseAll();
        }
    }

    enablePartialRestore() {
        if (window.restoreManager) {
            window.restoreManager.enablePartialMode();
        }
    }

    resetRestoreState() {
        if (window.restoreManager) {
            window.restoreManager.reset();
        }
    }

    showPaymentErrorDialog() {
        this.showUserNotification({
            type: 'error',
            title: 'Payment Error',
            message: 'There was an issue processing your payment. Please try again or contact support.',
            actions: [
                { label: 'Retry Payment', action: () => this.retryPayment() },
                { label: 'Contact Support', action: () => this.showSupportDialog() }
            ]
        });
    }

    enableFreeMode() {
        // Revert to free tier functionality
        if (window.subscriptionManager) {
            window.subscriptionManager.setFreeTier();
        }
    }

    resetPaymentState() {
        if (window.paymentProcessor) {
            window.paymentProcessor.reset();
        }
    }

    recoverDemoState() {
        try {
            const savedState = localStorage.getItem('safekeep_demo_state');
            if (savedState) {
                const state = JSON.parse(savedState);
                if (window.demoManager) {
                    window.demoManager.restoreState(state);
                }
                return true;
            }
        } catch (error) {
            console.warn('Failed to recover demo state:', error);
        }
        return false;
    }

    useDefaultDemoState() {
        if (window.demoManager) {
            window.demoManager.useDefaultState();
        }
    }

    resetDemoState() {
        localStorage.removeItem('safekeep_demo_state');
        sessionStorage.clear();
        
        if (window.demoManager) {
            window.demoManager.reset();
        }
        
        this.showUserNotification({
            type: 'info',
            title: 'Demo Reset',
            message: 'Demo state has been reset. You can start fresh.',
            actions: [
                { label: 'Start Tutorial', action: () => this.startTutorial() }
            ]
        });
    }

    // Network status handling
    handleNetworkStatusChange(isOnline) {
        if (isOnline) {
            this.handleNetworkRecovery();
        } else {
            this.handleNetworkLoss();
        }
    }

    handleNetworkRecovery() {
        // Retry queued operations
        if (window.networkQueue) {
            window.networkQueue.processQueue();
        }
        
        // Re-establish real-time connections
        if (window.realtimeManager) {
            window.realtimeManager.reconnect();
        }
        
        this.showUserNotification({
            type: 'success',
            title: 'Connection Restored',
            message: 'Network connection restored. Syncing data...',
            autoHide: true
        });
    }

    handleNetworkLoss() {
        this.enableOfflineMode();
    }

    // User notification system
    showUserNotification(notification) {
        this.userNotificationQueue.push({
            ...notification,
            id: this.generateErrorId(),
            timestamp: new Date().toISOString()
        });
        
        this.displayNotification(notification);
    }

    displayNotification(notification) {
        // Create notification element
        const notificationEl = document.createElement('div');
        notificationEl.className = `safekeep-notification safekeep-notification--${notification.type}`;
        notificationEl.innerHTML = `
            <div class="safekeep-notification__content">
                <div class="safekeep-notification__title">${notification.title}</div>
                <div class="safekeep-notification__message">${notification.message}</div>
                ${notification.actions ? this.renderNotificationActions(notification.actions) : ''}
            </div>
            <button class="safekeep-notification__close" onclick="this.parentElement.remove()">×</button>
        `;
        
        // Add to page
        let container = document.querySelector('.safekeep-notifications');
        if (!container) {
            container = document.createElement('div');
            container.className = 'safekeep-notifications';
            document.body.appendChild(container);
        }
        
        container.appendChild(notificationEl);
        
        // Auto-hide if specified
        if (notification.autoHide !== false) {
            setTimeout(() => {
                if (notificationEl.parentElement) {
                    notificationEl.remove();
                }
            }, notification.duration || 5000);
        }
    }

    renderNotificationActions(actions) {
        return `
            <div class="safekeep-notification__actions">
                ${actions.map(action => `
                    <button class="safekeep-notification__action" onclick="${action.action.toString()}">
                        ${action.label}
                    </button>
                `).join('')}
            </div>
        `;
    }

    // Retry operation implementations
    async retryAuthentication(context) {
        if (context.operation === 'signup') {
            return await window.authManager?.signup(context.email, context.password);
        } else if (context.operation === 'signin') {
            return await window.authManager?.signin(context.email, context.password);
        }
        throw new Error('Unknown auth operation');
    }

    async retryNetworkOperation(context) {
        if (context.url && context.options) {
            return await fetch(context.url, context.options);
        }
        throw new Error('Insufficient context for network retry');
    }

    async retryRealtimeConnection(context) {
        if (window.realtimeManager) {
            return await window.realtimeManager.reconnect();
        }
        throw new Error('Realtime manager not available');
    }

    async retryBackupOperation(context) {
        if (window.backupManager && context.sessionId) {
            return await window.backupManager.resumeSession(context.sessionId);
        }
        throw new Error('Backup context insufficient for retry');
    }

    async retryRestoreOperation(context) {
        if (window.restoreManager && context.sessionId) {
            return await window.restoreManager.resumeSession(context.sessionId);
        }
        throw new Error('Restore context insufficient for retry');
    }

    async retrySubscriptionOperation(context) {
        if (window.subscriptionManager && context.operation) {
            return await window.subscriptionManager[context.operation](context.params);
        }
        throw new Error('Subscription context insufficient for retry');
    }

    // Public API methods
    onError(category, callback) {
        if (!this.errorCallbacks.has(category)) {
            this.errorCallbacks.set(category, []);
        }
        this.errorCallbacks.get(category).push(callback);
    }

    triggerErrorCallbacks(errorInfo) {
        const callbacks = this.errorCallbacks.get(errorInfo.category) || [];
        callbacks.forEach(callback => {
            try {
                callback(errorInfo);
            } catch (error) {
                console.error('Error in error callback:', error);
            }
        });
    }

    getErrorLog() {
        return [...this.errorLog];
    }

    clearErrorLog() {
        this.errorLog = [];
        this.retryAttempts.clear();
    }

    // Demo-specific methods
    async resetDemo() {
        try {
            // Reset all managers
            const managers = [
                'authManager',
                'backupManager', 
                'restoreManager',
                'realtimeManager',
                'encryptionManager',
                'subscriptionManager',
                'paymentProcessor',
                'demoManager'
            ];

            for (const managerName of managers) {
                if (window[managerName] && typeof window[managerName].reset === 'function') {
                    await window[managerName].reset();
                }
            }

            // Clear storage
            localStorage.clear();
            sessionStorage.clear();

            // Clear error state
            this.clearErrorLog();

            // Reload page to ensure clean state
            window.location.reload();

        } catch (error) {
            console.error('Error during demo reset:', error);
            // Force reload as last resort
            window.location.reload();
        }
    }

    // Helper methods
    showSignInDialog() {
        if (window.authManager) {
            window.authManager.showSignInDialog();
        }
    }

    resumeBackupOperations() {
        if (window.backupManager) {
            window.backupManager.resumeAll();
        }
    }

    retryPayment() {
        if (window.paymentProcessor) {
            window.paymentProcessor.retryLastPayment();
        }
    }

    showSupportDialog() {
        this.showUserNotification({
            type: 'info',
            title: 'Support Contact',
            message: 'For demo purposes, support would be <NAME_EMAIL>',
            autoHide: true
        });
    }

    startTutorial() {
        if (window.tutorialManager) {
            window.tutorialManager.start();
        }
    }
}

// Initialize global error handler
window.SafeKeepErrorHandler = SafeKeepErrorHandler;
window.errorHandler = new SafeKeepErrorHandler();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SafeKeepErrorHandler;
}