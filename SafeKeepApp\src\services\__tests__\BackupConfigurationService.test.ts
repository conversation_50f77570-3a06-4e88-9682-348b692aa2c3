import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupConfigurationService } from '../BackupConfigurationService';
import { BackupConfiguration } from '../../types/backup';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('BackupConfigurationService', () => {
  const defaultConfig: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('loadConfiguration', () => {
    it('returns default configuration when no stored data exists', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);
      
      const config = await BackupConfigurationService.loadConfiguration();
      
      expect(config).toEqual(defaultConfig);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@SafeKeep:backupConfiguration');
    });

    it('returns stored configuration when data exists', async () => {
      const storedConfig: BackupConfiguration = {
        ...defaultConfig,
        autoBackup: true,
        wifiOnly: false,
      };
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(storedConfig));
      
      const config = await BackupConfigurationService.loadConfiguration();
      
      expect(config).toEqual(storedConfig);
    });

    it('merges stored config with defaults for missing properties', async () => {
      const partialConfig = {
        autoBackup: true,
        wifiOnly: false,
      };
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(partialConfig));
      
      const config = await BackupConfigurationService.loadConfiguration();
      
      expect(config).toEqual({
        ...defaultConfig,
        autoBackup: true,
        wifiOnly: false,
      });
    });

    it('returns default configuration on error', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));
      
      const config = await BackupConfigurationService.loadConfiguration();
      
      expect(config).toEqual(defaultConfig);
    });
  });

  describe('saveConfiguration', () => {
    it('saves configuration to AsyncStorage', async () => {
      const config: BackupConfiguration = {
        ...defaultConfig,
        autoBackup: true,
      };
      
      mockAsyncStorage.setItem.mockResolvedValue();
      
      await BackupConfigurationService.saveConfiguration(config);
      
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        '@SafeKeep:backupConfiguration',
        JSON.stringify(config)
      );
    });

    it('throws error when save fails', async () => {
      const config: BackupConfiguration = defaultConfig;
      
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage error'));
      
      await expect(BackupConfigurationService.saveConfiguration(config))
        .rejects.toThrow('Failed to save backup settings');
    });
  });

  describe('updateSetting', () => {
    it('updates specific setting and saves configuration', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(defaultConfig));
      mockAsyncStorage.setItem.mockResolvedValue();
      
      const updatedConfig = await BackupConfigurationService.updateSetting('autoBackup', true);
      
      expect(updatedConfig.autoBackup).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        '@SafeKeep:backupConfiguration',
        JSON.stringify({ ...defaultConfig, autoBackup: true })
      );
    });
  });

  describe('resetConfiguration', () => {
    it('resets configuration to defaults', async () => {
      mockAsyncStorage.setItem.mockResolvedValue();
      
      const config = await BackupConfigurationService.resetConfiguration();
      
      expect(config).toEqual(defaultConfig);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        '@SafeKeep:backupConfiguration',
        JSON.stringify(defaultConfig)
      );
    });
  });

  describe('getDataUsageEstimate', () => {
    it('returns correct estimates for all data types enabled', () => {
      const estimates = BackupConfigurationService.getDataUsageEstimate(defaultConfig);
      
      expect(estimates.contacts).toBe('~1-5 MB');
      expect(estimates.messages).toBe('~5-50 MB');
      expect(estimates.photos).toBe('~100-500 MB'); // medium compression
      expect(estimates.total).toBe('Varies by data amount');
    });

    it('returns 0 MB for disabled data types', () => {
      const config: BackupConfiguration = {
        ...defaultConfig,
        includeContacts: false,
        includeMessages: false,
        includePhotos: false,
      };
      
      const estimates = BackupConfigurationService.getDataUsageEstimate(config);
      
      expect(estimates.contacts).toBe('0 MB');
      expect(estimates.messages).toBe('0 MB');
      expect(estimates.photos).toBe('0 MB');
    });

    it('adjusts photo estimates based on compression level', () => {
      const highCompressionConfig: BackupConfiguration = {
        ...defaultConfig,
        compressionLevel: 'high',
      };
      
      const noneCompressionConfig: BackupConfiguration = {
        ...defaultConfig,
        compressionLevel: 'none',
      };
      
      const highEstimates = BackupConfigurationService.getDataUsageEstimate(highCompressionConfig);
      const noneEstimates = BackupConfigurationService.getDataUsageEstimate(noneCompressionConfig);
      
      expect(highEstimates.photos).toBe('~50-200 MB');
      expect(noneEstimates.photos).toBe('~200-1000 MB');
    });
  });

  describe('validateConfiguration', () => {
    it('validates configuration with all data types enabled', () => {
      const result = BackupConfigurationService.validateConfiguration(defaultConfig);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(0);
    });

    it('warns when no data types are selected', () => {
      const config: BackupConfiguration = {
        ...defaultConfig,
        includeContacts: false,
        includeMessages: false,
        includePhotos: false,
      };
      
      const result = BackupConfigurationService.validateConfiguration(config);
      
      expect(result.isValid).toBe(false);
      expect(result.warnings).toContain('At least one data type must be selected for backup');
    });

    it('warns about cellular data usage', () => {
      const config: BackupConfiguration = {
        ...defaultConfig,
        wifiOnly: false,
      };
      
      const result = BackupConfigurationService.validateConfiguration(config);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Cellular backup may consume significant data allowance');
    });

    it('warns about automatic backup over cellular', () => {
      const config: BackupConfiguration = {
        ...defaultConfig,
        autoBackup: true,
        wifiOnly: false,
      };
      
      const result = BackupConfigurationService.validateConfiguration(config);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Automatic backup over cellular may result in unexpected data charges');
    });
  });
});