import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import {
  authenticateToken,
  requireRole,
  requireAdmin,
  requireOwnership,
  extractUserId,
  extractUserRole,
  isAdmin
} from '../auth';

// Mock jwt
jest.mock('jsonwebtoken');
const mockedJwt = jwt as jest.Mocked<typeof jwt>;

describe('Authentication Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      params: {},
      body: {}
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();
    
    // Set up environment variable
    process.env.REACT_APP_SUPABASE_SERVICE_KEY = 'test-secret';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('authenticateToken', () => {
    it('should return 401 when no authorization header is provided', () => {
      authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Access token is required',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when authorization header is malformed', () => {
      mockRequest.headers = { authorization: 'InvalidFormat' };

      authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: 'Access token is required',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should authenticate valid token and set user in request', () => {
      const mockPayload = {
        sub: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      };

      mockRequest.headers = { authorization: 'Bearer valid-token' };
      mockedJwt.verify.mockReturnValue(mockPayload as any);

      authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockedJwt.verify).toHaveBeenCalledWith('valid-token', 'test-secret');
      expect(mockRequest.user).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should set default role to user when role is not provided', () => {
      const mockPayload = {
        sub: 'user-123',
        email: '<EMAIL>'
      };

      mockRequest.headers = { authorization: 'Bearer valid-token' };
      mockedJwt.verify.mockReturnValue(mockPayload as any);

      authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockRequest.user).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should return 403 for expired token', () => {
      mockRequest.headers = { authorization: 'Bearer expired-token' };
      mockedJwt.verify.mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired', new Date());
      });

      authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: 'Access token has expired',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 403 for malformed token', () => {
      mockRequest.headers = { authorization: 'Bearer malformed-token' };
      mockedJwt.verify.mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Malformed token');
      });

      authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'MALFORMED_TOKEN',
          message: 'Malformed access token',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 403 for invalid token', () => {
      mockRequest.headers = { authorization: 'Bearer invalid-token' };
      mockedJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid access token',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireRole', () => {
    it('should return 401 when user is not authenticated', () => {
      const middleware = requireRole('admin');
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should allow access when user has required role', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'admin'
      };

      const middleware = requireRole('admin');
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should allow admin access to any role-protected endpoint', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'admin'
      };

      const middleware = requireRole('moderator');
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny access when user does not have required role', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      };

      const middleware = requireRole('admin');
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'admin role required',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle user with no role (default to user)', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>'
      };

      const middleware = requireRole('admin');
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireAdmin', () => {
    it('should allow access for admin user', () => {
      mockRequest.user = {
        id: 'admin-123',
        email: '<EMAIL>',
        role: 'admin'
      };

      requireAdmin(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny access for non-admin user', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      };

      requireAdmin(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireOwnership', () => {
    it('should allow user to access their own resources', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      };
      mockRequest.params = { userId: 'user-123' };

      const middleware = requireOwnership();
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should allow admin to access any user resources', () => {
      mockRequest.user = {
        id: 'admin-123',
        email: '<EMAIL>',
        role: 'admin'
      };
      mockRequest.params = { userId: 'user-456' };

      const middleware = requireOwnership();
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny user access to other user resources', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      };
      mockRequest.params = { userId: 'user-456' };

      const middleware = requireOwnership();
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: 'You can only access your own resources',
          timestamp: expect.any(String)
        }
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should work with custom parameter name', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      };
      mockRequest.params = { customUserId: 'user-123' };

      const middleware = requireOwnership('customUserId');
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should check userId in request body if not in params', () => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'user'
      };
      mockRequest.body = { userId: 'user-123' };

      const middleware = requireOwnership();
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should return 401 when user is not authenticated', () => {
      const middleware = requireOwnership();
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Utility functions', () => {
    describe('extractUserId', () => {
      it('should return user ID when user is authenticated', () => {
        mockRequest.user = {
          id: 'user-123',
          email: '<EMAIL>',
          role: 'user'
        };

        const userId = extractUserId(mockRequest as Request);
        expect(userId).toBe('user-123');
      });

      it('should return null when user is not authenticated', () => {
        const userId = extractUserId(mockRequest as Request);
        expect(userId).toBeNull();
      });
    });

    describe('extractUserRole', () => {
      it('should return user role when user is authenticated', () => {
        mockRequest.user = {
          id: 'user-123',
          email: '<EMAIL>',
          role: 'admin'
        };

        const role = extractUserRole(mockRequest as Request);
        expect(role).toBe('admin');
      });

      it('should return default role "user" when user has no role', () => {
        mockRequest.user = {
          id: 'user-123',
          email: '<EMAIL>'
        };

        const role = extractUserRole(mockRequest as Request);
        expect(role).toBe('user');
      });

      it('should return default role "user" when user is not authenticated', () => {
        const role = extractUserRole(mockRequest as Request);
        expect(role).toBe('user');
      });
    });

    describe('isAdmin', () => {
      it('should return true for admin user', () => {
        mockRequest.user = {
          id: 'admin-123',
          email: '<EMAIL>',
          role: 'admin'
        };

        const result = isAdmin(mockRequest as Request);
        expect(result).toBe(true);
      });

      it('should return false for non-admin user', () => {
        mockRequest.user = {
          id: 'user-123',
          email: '<EMAIL>',
          role: 'user'
        };

        const result = isAdmin(mockRequest as Request);
        expect(result).toBe(false);
      });

      it('should return false when user is not authenticated', () => {
        const result = isAdmin(mockRequest as Request);
        expect(result).toBe(false);
      });
    });
  });
});