/**
 * Security Education Module
 * Interactive security education and best practices content
 */

class SecurityEducation {
    constructor() {
        this.educationTopics = this.initializeTopics();
        this.interactiveLabs = this.initializeLabs();
        this.userProgress = new Map();
        this.currentTopic = null;
    }

    initializeTopics() {
        return [
            {
                id: 'zero-knowledge',
                title: 'Zero-Knowledge Encryption',
                icon: '🔐',
                difficulty: 'intermediate',
                duration: '15 minutes',
                description: 'Learn how zero-knowledge encryption protects your data',
                content: this.getZeroKnowledgeContent(),
                quiz: this.getZeroKnowledgeQuiz(),
                practicalExercise: 'zero-knowledge-lab'
            },
            {
                id: 'threat-modeling',
                title: 'Threat Modeling',
                icon: '🎯',
                difficulty: 'advanced',
                duration: '20 minutes',
                description: 'Understand how to identify and assess security threats',
                content: this.getThreatModelingContent(),
                quiz: this.getThreatModelingQuiz(),
                practicalExercise: 'threat-modeling-lab'
            },
            {
                id: 'secure-coding',
                title: 'Secure Coding Practices',
                icon: '💻',
                difficulty: 'intermediate',
                duration: '25 minutes',
                description: 'Best practices for writing secure code',
                content: this.getSecureCodingContent(),
                quiz: this.getSecureCodingQuiz(),
                practicalExercise: 'secure-coding-lab'
            },
            {
                id: 'incident-response',
                title: 'Incident Response',
                icon: '🚨',
                difficulty: 'advanced',
                duration: '18 minutes',
                description: 'How to respond to security incidents effectively',
                content: this.getIncidentResponseContent(),
                quiz: this.getIncidentResponseQuiz(),
                practicalExercise: 'incident-response-lab'
            },
            {
                id: 'compliance-basics',
                title: 'Compliance Fundamentals',
                icon: '📋',
                difficulty: 'beginner',
                duration: '12 minutes',
                description: 'Understanding security compliance requirements',
                content: this.getComplianceContent(),
                quiz: this.getComplianceQuiz(),
                practicalExercise: 'compliance-lab'
            },
            {
                id: 'risk-assessment',
                title: 'Risk Assessment',
                icon: '⚖️',
                difficulty: 'intermediate',
                duration: '16 minutes',
                description: 'How to assess and manage security risks',
                content: this.getRiskAssessmentContent(),
                quiz: this.getRiskAssessmentQuiz(),
                practicalExercise: 'risk-assessment-lab'
            }
        ];
    }

    initializeLabs() {
        return {
            'zero-knowledge-lab': {
                title: 'Zero-Knowledge Encryption Lab',
                description: 'Practice implementing zero-knowledge encryption',
                exercises: this.getZeroKnowledgeLabExercises()
            },
            'threat-modeling-lab': {
                title: 'Threat Modeling Workshop',
                description: 'Create threat models for sample applications',
                exercises: this.getThreatModelingLabExercises()
            },
            'secure-coding-lab': {
                title: 'Secure Code Review Lab',
                description: 'Identify and fix security vulnerabilities in code',
                exercises: this.getSecureCodingLabExercises()
            },
            'incident-response-lab': {
                title: 'Incident Response Simulation',
                description: 'Practice responding to security incidents',
                exercises: this.getIncidentResponseLabExercises()
            },
            'compliance-lab': {
                title: 'Compliance Assessment Lab',
                description: 'Assess compliance with security frameworks',
                exercises: this.getComplianceLabExercises()
            },
            'risk-assessment-lab': {
                title: 'Risk Assessment Workshop',
                description: 'Conduct security risk assessments',
                exercises: this.getRiskAssessmentLabExercises()
            }
        };
    }

    async getEducationalContent() {
        return {
            topics: this.educationTopics,
            labs: this.interactiveLabs,
            progress: this.getUserProgress(),
            recommendations: this.getPersonalizedRecommendations()
        };
    }

    getUserProgress() {
        // In a real implementation, this would load from user data
        return {
            completedTopics: Array.from(this.userProgress.keys()),
            totalTopics: this.educationTopics.length,
            averageScore: this.calculateAverageScore(),
            timeSpent: this.calculateTimeSpent(),
            certificates: this.getUserCertificates()
        };
    }

    getPersonalizedRecommendations() {
        const completedTopics = Array.from(this.userProgress.keys());
        const remainingTopics = this.educationTopics.filter(topic => 
            !completedTopics.includes(topic.id)
        );

        // Sort by difficulty and relevance
        return remainingTopics
            .sort((a, b) => {
                const difficultyOrder = { 'beginner': 1, 'intermediate': 2, 'advanced': 3 };
                return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
            })
            .slice(0, 3)
            .map(topic => ({
                ...topic,
                reason: this.getRecommendationReason(topic, completedTopics)
            }));
    }

    getRecommendationReason(topic, completedTopics) {
        if (completedTopics.length === 0) {
            return topic.difficulty === 'beginner' ? 
                'Great starting point for security education' : 
                'Build foundational knowledge first';
        }
        
        if (topic.difficulty === 'beginner') {
            return 'Strengthen your security fundamentals';
        } else if (topic.difficulty === 'intermediate') {
            return 'Ready to advance your security knowledge';
        } else {
            return 'Challenge yourself with advanced concepts';
        }
    }

    // Content for each topic
    getZeroKnowledgeContent() {
        return {
            introduction: {
                title: 'What is Zero-Knowledge Encryption?',
                content: `
                    Zero-knowledge encryption is a security model where the service provider 
                    has no knowledge of the data being stored or processed. This means that 
                    even if the provider's systems are compromised, your data remains secure 
                    because it's encrypted with keys that only you control.
                `,
                keyPoints: [
                    'Client-side encryption ensures data is encrypted before leaving your device',
                    'Service providers never have access to your encryption keys',
                    'Even with full server access, attackers cannot decrypt your data',
                    'You maintain complete control over your data privacy'
                ]
            },
            principles: {
                title: 'Core Principles',
                content: `
                    Zero-knowledge systems are built on several fundamental principles that 
                    ensure maximum security and privacy protection.
                `,
                principles: [
                    {
                        name: 'Client-Side Encryption',
                        description: 'All encryption happens on your device before data transmission',
                        importance: 'Prevents data exposure during the encryption process'
                    },
                    {
                        name: 'Key Derivation',
                        description: 'Encryption keys are derived from your master password',
                        importance: 'Ensures only you can generate the keys needed to decrypt your data'
                    },
                    {
                        name: 'Server Blindness',
                        description: 'Servers only see encrypted data, never plaintext',
                        importance: 'Protects against insider threats and server compromises'
                    },
                    {
                        name: 'Forward Secrecy',
                        description: 'Past communications remain secure even if current keys are compromised',
                        importance: 'Limits the impact of potential security breaches'
                    }
                ]
            },
            implementation: {
                title: 'How SafeKeep Implements Zero-Knowledge',
                content: `
                    SafeKeep's zero-knowledge implementation follows industry best practices 
                    to ensure your data remains private and secure.
                `,
                steps: [
                    {
                        step: 1,
                        title: 'Master Key Generation',
                        description: 'A master key is derived from your password using PBKDF2 with 100,000+ iterations',
                        technical: 'Uses cryptographically secure random number generation'
                    },
                    {
                        step: 2,
                        title: 'Data Encryption',
                        description: 'Your data is encrypted locally using AES-256-GCM encryption',
                        technical: 'Each file gets a unique initialization vector (IV)'
                    },
                    {
                        step: 3,
                        title: 'Secure Transmission',
                        description: 'Encrypted data is transmitted over TLS 1.3 connections',
                        technical: 'Certificate pinning prevents man-in-the-middle attacks'
                    },
                    {
                        step: 4,
                        title: 'Server Storage',
                        description: 'Only encrypted data is stored on servers, never plaintext',
                        technical: 'Servers cannot decrypt data without your master key'
                    }
                ]
            },
            benefits: {
                title: 'Benefits of Zero-Knowledge Encryption',
                benefits: [
                    {
                        category: 'Privacy Protection',
                        description: 'Your data remains private even from the service provider',
                        examples: ['Personal photos', 'Private messages', 'Financial documents']
                    },
                    {
                        category: 'Regulatory Compliance',
                        description: 'Helps meet strict data protection requirements',
                        examples: ['GDPR compliance', 'HIPAA requirements', 'Financial regulations']
                    },
                    {
                        category: 'Breach Protection',
                        description: 'Data breaches cannot expose your actual information',
                        examples: ['Server compromises', 'Insider threats', 'Government requests']
                    },
                    {
                        category: 'Trust Minimization',
                        description: 'Reduces the need to trust service providers',
                        examples: ['No vendor lock-in', 'Reduced attack surface', 'User control']
                    }
                ]
            }
        };
    }

    getThreatModelingContent() {
        return {
            introduction: {
                title: 'Understanding Threat Modeling',
                content: `
                    Threat modeling is a structured approach to identifying, quantifying, 
                    and addressing security threats. It helps organizations understand 
                    what they need to protect, who they need to protect it from, and 
                    how well they're doing at providing that protection.
                `,
                keyPoints: [
                    'Systematic approach to identifying security threats',
                    'Helps prioritize security investments',
                    'Enables proactive rather than reactive security',
                    'Essential for building secure systems'
                ]
            },
            methodology: {
                title: 'STRIDE Threat Modeling',
                content: `
                    STRIDE is a widely-used threat modeling methodology that categorizes 
                    threats into six main types.
                `,
                categories: [
                    {
                        name: 'Spoofing',
                        description: 'Impersonating someone or something else',
                        examples: ['User impersonation', 'IP address spoofing', 'Email spoofing'],
                        mitigations: ['Strong authentication', 'Digital signatures', 'Certificate validation']
                    },
                    {
                        name: 'Tampering',
                        description: 'Modifying data or code',
                        examples: ['Data modification', 'Code injection', 'Configuration changes'],
                        mitigations: ['Digital signatures', 'Integrity checks', 'Access controls']
                    },
                    {
                        name: 'Repudiation',
                        description: 'Claiming to have not performed an action',
                        examples: ['Denying transactions', 'Claiming innocence', 'False alibis'],
                        mitigations: ['Audit logging', 'Digital signatures', 'Non-repudiation protocols']
                    },
                    {
                        name: 'Information Disclosure',
                        description: 'Exposing information to unauthorized individuals',
                        examples: ['Data breaches', 'Information leakage', 'Eavesdropping'],
                        mitigations: ['Encryption', 'Access controls', 'Data classification']
                    },
                    {
                        name: 'Denial of Service',
                        description: 'Denying or degrading service to valid users',
                        examples: ['DDoS attacks', 'Resource exhaustion', 'System overload'],
                        mitigations: ['Rate limiting', 'Load balancing', 'Resource monitoring']
                    },
                    {
                        name: 'Elevation of Privilege',
                        description: 'Gaining capabilities without proper authorization',
                        examples: ['Privilege escalation', 'Admin access', 'Root compromise'],
                        mitigations: ['Principle of least privilege', 'Access controls', 'Regular audits']
                    }
                ]
            }
        };
    }

    getSecureCodingContent() {
        return {
            introduction: {
                title: 'Secure Coding Fundamentals',
                content: `
                    Secure coding practices are essential for building applications that 
                    resist attacks and protect sensitive data. These practices should be 
                    integrated into every stage of the development lifecycle.
                `
            },
            principles: [
                {
                    name: 'Input Validation',
                    description: 'Validate all input from untrusted sources',
                    examples: [
                        'Sanitize user input to prevent XSS',
                        'Use parameterized queries to prevent SQL injection',
                        'Validate file uploads for malicious content'
                    ]
                },
                {
                    name: 'Authentication & Authorization',
                    description: 'Implement strong authentication and proper authorization',
                    examples: [
                        'Use multi-factor authentication',
                        'Implement role-based access control',
                        'Validate permissions for every operation'
                    ]
                },
                {
                    name: 'Cryptography',
                    description: 'Use cryptography correctly and securely',
                    examples: [
                        'Use established cryptographic libraries',
                        'Never implement your own crypto algorithms',
                        'Properly manage cryptographic keys'
                    ]
                }
            ]
        };
    }

    getIncidentResponseContent() {
        return {
            introduction: {
                title: 'Security Incident Response',
                content: `
                    Incident response is the organized approach to addressing and managing 
                    the aftermath of a security breach or cyberattack. The goal is to 
                    handle the situation in a way that limits damage and reduces recovery 
                    time and costs.
                `
            },
            phases: [
                {
                    phase: 'Preparation',
                    description: 'Establish incident response capabilities',
                    activities: [
                        'Create incident response plan',
                        'Form incident response team',
                        'Implement monitoring and detection tools',
                        'Conduct training and exercises'
                    ]
                },
                {
                    phase: 'Detection & Analysis',
                    description: 'Identify and analyze potential incidents',
                    activities: [
                        'Monitor security events',
                        'Analyze alerts and indicators',
                        'Determine incident scope and impact',
                        'Document findings'
                    ]
                },
                {
                    phase: 'Containment, Eradication & Recovery',
                    description: 'Stop the incident and restore normal operations',
                    activities: [
                        'Contain the incident to prevent spread',
                        'Remove malicious artifacts',
                        'Restore affected systems',
                        'Validate system integrity'
                    ]
                },
                {
                    phase: 'Post-Incident Activity',
                    description: 'Learn from the incident and improve',
                    activities: [
                        'Conduct lessons learned session',
                        'Update incident response procedures',
                        'Implement additional safeguards',
                        'Report to stakeholders'
                    ]
                }
            ]
        };
    }

    getComplianceContent() {
        return {
            introduction: {
                title: 'Security Compliance Fundamentals',
                content: `
                    Security compliance involves adhering to laws, regulations, guidelines, 
                    and specifications relevant to your business. Compliance helps ensure 
                    that organizations implement appropriate security controls and maintain 
                    them over time.
                `
            },
            frameworks: [
                {
                    name: 'GDPR',
                    description: 'European Union data protection regulation',
                    keyRequirements: [
                        'Data protection by design and by default',
                        'Data subject rights implementation',
                        'Breach notification within 72 hours',
                        'Data protection impact assessments'
                    ]
                },
                {
                    name: 'SOC 2',
                    description: 'Framework for managing customer data',
                    keyRequirements: [
                        'Security controls implementation',
                        'Availability and performance monitoring',
                        'Confidentiality protection',
                        'Regular audits and assessments'
                    ]
                },
                {
                    name: 'ISO 27001',
                    description: 'International information security standard',
                    keyRequirements: [
                        'Information security management system',
                        'Risk assessment and treatment',
                        'Security controls implementation',
                        'Continuous improvement process'
                    ]
                }
            ]
        };
    }

    getRiskAssessmentContent() {
        return {
            introduction: {
                title: 'Security Risk Assessment',
                content: `
                    Risk assessment is the process of identifying, analyzing, and evaluating 
                    security risks. It helps organizations understand their security posture 
                    and make informed decisions about risk treatment.
                `
            },
            process: [
                {
                    step: 'Asset Identification',
                    description: 'Identify and catalog all assets',
                    activities: [
                        'Hardware inventory',
                        'Software inventory',
                        'Data classification',
                        'Network mapping'
                    ]
                },
                {
                    step: 'Threat Identification',
                    description: 'Identify potential threats',
                    activities: [
                        'External threats analysis',
                        'Internal threats assessment',
                        'Natural disasters consideration',
                        'Human error evaluation'
                    ]
                },
                {
                    step: 'Vulnerability Assessment',
                    description: 'Identify system vulnerabilities',
                    activities: [
                        'Vulnerability scanning',
                        'Penetration testing',
                        'Code review',
                        'Configuration assessment'
                    ]
                },
                {
                    step: 'Risk Analysis',
                    description: 'Analyze and prioritize risks',
                    activities: [
                        'Impact assessment',
                        'Likelihood evaluation',
                        'Risk scoring',
                        'Risk prioritization'
                    ]
                }
            ]
        };
    }

    // Quiz content for each topic
    getZeroKnowledgeQuiz() {
        return [
            {
                question: 'What is the main principle of zero-knowledge encryption?',
                options: [
                    'The server encrypts all data',
                    'The client encrypts data before sending it to the server',
                    'Encryption keys are stored on the server',
                    'Data is encrypted during transmission only'
                ],
                correct: 1,
                explanation: 'In zero-knowledge encryption, the client encrypts data locally before sending it to the server, ensuring the server never sees plaintext data.'
            },
            {
                question: 'Why is PBKDF2 used in key derivation?',
                options: [
                    'It makes encryption faster',
                    'It reduces storage requirements',
                    'It makes brute force attacks more difficult',
                    'It eliminates the need for passwords'
                ],
                correct: 2,
                explanation: 'PBKDF2 uses many iterations to make brute force attacks computationally expensive and time-consuming.'
            },
            {
                question: 'What happens if a zero-knowledge service provider is compromised?',
                options: [
                    'All user data is immediately accessible',
                    'Attackers can decrypt data with server access',
                    'User data remains encrypted and protected',
                    'Only recent data is at risk'
                ],
                correct: 2,
                explanation: 'Since the service provider never has access to encryption keys, user data remains encrypted and protected even if servers are compromised.'
            }
        ];
    }

    getThreatModelingQuiz() {
        return [
            {
                question: 'What does the "S" in STRIDE stand for?',
                options: ['Security', 'Spoofing', 'System', 'Standard'],
                correct: 1,
                explanation: 'In STRIDE, "S" stands for Spoofing, which involves impersonating someone or something else.'
            },
            {
                question: 'Which STRIDE category covers data modification attacks?',
                options: ['Spoofing', 'Tampering', 'Repudiation', 'Information Disclosure'],
                correct: 1,
                explanation: 'Tampering covers attacks that involve modifying data or code without authorization.'
            }
        ];
    }

    getSecureCodingQuiz() {
        return [
            {
                question: 'What is the best way to prevent SQL injection?',
                options: [
                    'Input filtering',
                    'Parameterized queries',
                    'String concatenation',
                    'User education'
                ],
                correct: 1,
                explanation: 'Parameterized queries separate SQL code from data, preventing injection attacks.'
            }
        ];
    }

    getIncidentResponseQuiz() {
        return [
            {
                question: 'What is the first phase of incident response?',
                options: ['Detection', 'Preparation', 'Containment', 'Recovery'],
                correct: 1,
                explanation: 'Preparation is the first phase, involving establishing incident response capabilities before incidents occur.'
            }
        ];
    }

    getComplianceQuiz() {
        return [
            {
                question: 'How long does GDPR require for breach notification?',
                options: ['24 hours', '48 hours', '72 hours', '1 week'],
                correct: 2,
                explanation: 'GDPR requires breach notification to authorities within 72 hours of becoming aware of the breach.'
            }
        ];
    }

    getRiskAssessmentQuiz() {
        return [
            {
                question: 'What is the first step in risk assessment?',
                options: ['Threat identification', 'Asset identification', 'Vulnerability assessment', 'Risk analysis'],
                correct: 1,
                explanation: 'Asset identification is the first step - you need to know what you\'re protecting before assessing risks.'
            }
        ];
    }

    // Lab exercises
    getZeroKnowledgeLabExercises() {
        return [
            {
                id: 'zk-exercise-1',
                title: 'Key Derivation Practice',
                description: 'Practice deriving encryption keys from passwords',
                type: 'interactive',
                difficulty: 'beginner'
            },
            {
                id: 'zk-exercise-2',
                title: 'Encryption Implementation',
                description: 'Implement client-side encryption',
                type: 'coding',
                difficulty: 'intermediate'
            }
        ];
    }

    getThreatModelingLabExercises() {
        return [
            {
                id: 'tm-exercise-1',
                title: 'STRIDE Analysis',
                description: 'Apply STRIDE methodology to a sample application',
                type: 'analysis',
                difficulty: 'intermediate'
            }
        ];
    }

    getSecureCodingLabExercises() {
        return [
            {
                id: 'sc-exercise-1',
                title: 'Vulnerability Identification',
                description: 'Find security vulnerabilities in code samples',
                type: 'code-review',
                difficulty: 'intermediate'
            }
        ];
    }

    getIncidentResponseLabExercises() {
        return [
            {
                id: 'ir-exercise-1',
                title: 'Incident Response Simulation',
                description: 'Practice responding to a simulated security incident',
                type: 'simulation',
                difficulty: 'advanced'
            }
        ];
    }

    getComplianceLabExercises() {
        return [
            {
                id: 'comp-exercise-1',
                title: 'Compliance Gap Analysis',
                description: 'Identify compliance gaps in a sample organization',
                type: 'assessment',
                difficulty: 'intermediate'
            }
        ];
    }

    getRiskAssessmentLabExercises() {
        return [
            {
                id: 'ra-exercise-1',
                title: 'Risk Matrix Creation',
                description: 'Create a risk matrix for identified threats',
                type: 'analysis',
                difficulty: 'intermediate'
            }
        ];
    }

    // Utility methods
    calculateAverageScore() {
        if (this.userProgress.size === 0) return 0;
        
        const scores = Array.from(this.userProgress.values()).map(progress => progress.score);
        return scores.reduce((sum, score) => sum + score, 0) / scores.length;
    }

    calculateTimeSpent() {
        if (this.userProgress.size === 0) return 0;
        
        const times = Array.from(this.userProgress.values()).map(progress => progress.timeSpent);
        return times.reduce((sum, time) => sum + time, 0);
    }

    getUserCertificates() {
        // Return certificates earned based on completed topics and scores
        const certificates = [];
        const completedTopics = Array.from(this.userProgress.keys());
        
        if (completedTopics.includes('zero-knowledge') && 
            this.userProgress.get('zero-knowledge').score >= 80) {
            certificates.push({
                name: 'Zero-Knowledge Encryption Specialist',
                issueDate: new Date(),
                validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
            });
        }
        
        return certificates;
    }

    async startTopic(topicId) {
        const topic = this.educationTopics.find(t => t.id === topicId);
        if (!topic) {
            throw new Error(`Topic not found: ${topicId}`);
        }
        
        this.currentTopic = topic;
        return topic;
    }

    async completeQuiz(topicId, answers) {
        const topic = this.educationTopics.find(t => t.id === topicId);
        if (!topic) {
            throw new Error(`Topic not found: ${topicId}`);
        }
        
        const quiz = topic.quiz;
        let correctAnswers = 0;
        
        const results = quiz.map((question, index) => {
            const isCorrect = answers[index] === question.correct;
            if (isCorrect) correctAnswers++;
            
            return {
                question: question.question,
                userAnswer: answers[index],
                correctAnswer: question.correct,
                isCorrect: isCorrect,
                explanation: question.explanation
            };
        });
        
        const score = Math.round((correctAnswers / quiz.length) * 100);
        
        // Update user progress
        this.userProgress.set(topicId, {
            completed: true,
            score: score,
            completedAt: new Date(),
            timeSpent: this.calculateTopicTime(topic),
            quizResults: results
        });
        
        return {
            score: score,
            correctAnswers: correctAnswers,
            totalQuestions: quiz.length,
            results: results,
            passed: score >= 70,
            certificate: score >= 80 ? this.generateCertificate(topic, score) : null
        };
    }

    calculateTopicTime(topic) {
        // Convert duration string to minutes
        const durationMatch = topic.duration.match(/(\d+)/);
        return durationMatch ? parseInt(durationMatch[1]) : 15;
    }

    generateCertificate(topic, score) {
        return {
            title: `${topic.title} Certificate`,
            recipient: 'Demo User',
            score: score,
            issueDate: new Date(),
            certificateId: `CERT-${topic.id.toUpperCase()}-${Date.now()}`,
            validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecurityEducation;
}