import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BackupStatus, Photo, Contact, BackupItem } from '../../utils/types';

interface BackupState {
  status: BackupStatus;
  photos: Photo[];
  contacts: Contact[];
  backupQueue: BackupItem[];
  lastBackupDate?: string;
  totalStorageUsed: number;
}

const initialState: BackupState = {
  status: {
    isRunning: false,
    progress: 0,
    totalItems: 0,
    completedItems: 0,
  },
  photos: [],
  contacts: [],
  backupQueue: [],
  totalStorageUsed: 0,
};

const backupSlice = createSlice({
  name: 'backup',
  initialState,
  reducers: {
    setBackupStatus: (state, action: PayloadAction<Partial<BackupStatus>>) => {
      state.status = { ...state.status, ...action.payload };
    },
    setPhotos: (state, action: PayloadAction<Photo[]>) => {
      state.photos = action.payload;
    },
    setContacts: (state, action: PayloadAction<Contact[]>) => {
      state.contacts = action.payload;
    },
    addToBackupQueue: (state, action: PayloadAction<BackupItem>) => {
      state.backupQueue.push(action.payload);
    },
    removeFromBackupQueue: (state, action: PayloadAction<string>) => {
      state.backupQueue = state.backupQueue.filter(item => item.id !== action.payload);
    },
    updateBackupItem: (state, action: PayloadAction<{ id: string; status: BackupItem['status'] }>) => {
      const item = state.backupQueue.find(item => item.id === action.payload.id);
      if (item) {
        item.status = action.payload.status;
      }
    },
    setLastBackupDate: (state, action: PayloadAction<string>) => {
      state.lastBackupDate = action.payload;
    },
    updateStorageUsed: (state, action: PayloadAction<number>) => {
      state.totalStorageUsed = action.payload;
    },
    resetBackup: (state) => {
      state.status = initialState.status;
      state.backupQueue = [];
    },
  },
});

export const {
  setBackupStatus,
  setPhotos,
  setContacts,
  addToBackupQueue,
  removeFromBackupQueue,
  updateBackupItem,
  setLastBackupDate,
  updateStorageUsed,
  resetBackup,
} = backupSlice.actions;

export default backupSlice.reducer;
