"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const ServiceController_1 = require("../controllers/ServiceController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
const serviceController = new ServiceController_1.ServiceController();
router.get('/user/:userId', auth_1.authenticateToken, (req, res) => {
    serviceController.getUserServices(req, res);
});
router.post('/validate', auth_1.authenticateToken, (req, res) => {
    serviceController.validateServiceCombination(req, res);
});
router.get('/access/:userId/:serviceType', auth_1.authenticateToken, (req, res) => {
    serviceController.checkServiceAccess(req, res);
});
exports.default = router;
//# sourceMappingURL=services.js.map