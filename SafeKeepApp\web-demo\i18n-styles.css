/**
 * Internationalization (i18n) Styles
 * Comprehensive styling for multi-language support components
 */

/* Language Selector Component */
.language-selector {
    position: relative;
    display: inline-block;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.language-selector-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: space-between;
}

.language-selector-trigger:hover {
    border-color: #4facfe;
    background: #f8f9ff;
}

.language-selector-trigger:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.language-selector-trigger[aria-expanded="true"] {
    border-color: #4facfe;
    background: #f8f9ff;
}

.language-selector-trigger[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

.language-selector-trigger .flag {
    font-size: 1.2rem;
    line-height: 1;
}

.language-selector-trigger .language-name {
    flex: 1;
    text-align: left;
}

.language-selector-trigger .dropdown-arrow {
    font-size: 0.8rem;
    color: #666;
    transition: transform 0.3s ease;
}

/* Compact variant */
.language-selector.compact .language-selector-trigger {
    padding: 6px 8px;
    min-width: 80px;
    font-size: 0.8rem;
}

.language-selector.compact .language-selector-trigger .language-name {
    display: none;
}

/* Dropdown */
.language-selector-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    min-width: 280px;
    max-height: 400px;
    overflow: hidden;
}

.language-selector-dropdown.open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Position variants */
.language-selector[data-position="bottom-left"] .language-selector-dropdown {
    left: 0;
    right: auto;
}

.language-selector[data-position="bottom-right"] .language-selector-dropdown {
    left: auto;
    right: 0;
}

.language-selector[data-position="top-left"] .language-selector-dropdown {
    top: auto;
    bottom: 100%;
    left: 0;
    right: auto;
    transform: translateY(10px);
}

.language-selector[data-position="top-left"] .language-selector-dropdown.open {
    transform: translateY(0);
}

.language-selector[data-position="top-right"] .language-selector-dropdown {
    top: auto;
    bottom: 100%;
    left: auto;
    right: 0;
    transform: translateY(10px);
}

.language-selector[data-position="top-right"] .language-selector-dropdown.open {
    transform: translateY(0);
}

/* Dropdown header */
.dropdown-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.dropdown-header h3 {
    margin: 0 0 8px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.currency-info {
    font-size: 0.85rem;
    color: #666;
    margin: 0;
}

/* Language list */
.language-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 8px 0;
}

.language-option {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
    transition: all 0.2s ease;
    text-align: left;
}

.language-option:hover {
    background: #f8f9ff;
    color: #4facfe;
}

.language-option:focus {
    outline: none;
    background: #f0f8ff;
    color: #4facfe;
}

.language-option.active {
    background: #e3f2fd;
    color: #1976d2;
    font-weight: 600;
}

.language-option .flag {
    font-size: 1.3rem;
    line-height: 1;
    flex-shrink: 0;
}

.language-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.language-info .language-name {
    font-weight: 500;
    line-height: 1.2;
}

.language-info .native-name {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.2;
}

.language-option .currency-code {
    font-size: 0.8rem;
    color: #666;
    background: #f1f3f4;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.language-option .check-mark {
    color: #28a745;
    font-weight: bold;
    font-size: 1rem;
}

/* Currency converter */
.currency-converter {
    border-top: 1px solid #e9ecef;
    padding: 15px 20px;
    background: #fafbfc;
    border-radius: 0 0 10px 10px;
}

.converter-header {
    margin-bottom: 12px;
}

.converter-header h4 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

.converter-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.conversion-row {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
}

.amount-input {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.85rem;
    text-align: right;
}

.amount-input:focus {
    outline: none;
    border-color: #4facfe;
}

.base-currency,
.target-currency {
    font-weight: 600;
    color: #333;
    min-width: 35px;
}

.conversion-arrow {
    color: #4facfe;
    font-weight: bold;
}

.converted-amount {
    font-weight: 600;
    color: #4facfe;
    min-width: 60px;
}

.exchange-rate {
    font-size: 0.75rem;
    color: #666;
    text-align: center;
    padding: 4px 0;
    background: rgba(79, 172, 254, 0.1);
    border-radius: 4px;
}

/* RTL Support */
.rtl .language-selector-dropdown {
    left: auto;
    right: 0;
}

.rtl .language-selector[data-position="bottom-left"] .language-selector-dropdown {
    left: auto;
    right: 0;
}

.rtl .language-selector[data-position="bottom-right"] .language-selector-dropdown {
    left: 0;
    right: auto;
}

.rtl .language-option {
    text-align: right;
}

.rtl .conversion-row {
    flex-direction: row-reverse;
}

.rtl .conversion-arrow {
    transform: scaleX(-1);
}

/* Notification styles */
.language-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 0.9rem;
    max-width: 300px;
}

.language-notification.success {
    background: #28a745;
}

.language-notification.error {
    background: #dc3545;
}

.language-notification.info {
    background: #17a2b8;
}

.language-notification.warning {
    background: #ffc107;
    color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
    .language-selector-dropdown {
        min-width: 250px;
        max-height: 300px;
    }
    
    .language-option {
        padding: 10px 15px;
        font-size: 0.85rem;
    }
    
    .dropdown-header {
        padding: 12px 15px;
    }
    
    .currency-converter {
        padding: 12px 15px;
    }
    
    .conversion-row {
        flex-wrap: wrap;
        gap: 6px;
    }
    
    .amount-input {
        width: 70px;
    }
}

@media (max-width: 480px) {
    .language-selector-dropdown {
        min-width: 220px;
        left: 50%;
        right: auto;
        transform: translateX(-50%) translateY(-10px);
    }
    
    .language-selector-dropdown.open {
        transform: translateX(-50%) translateY(0);
    }
    
    .language-selector[data-position="top-left"] .language-selector-dropdown,
    .language-selector[data-position="top-right"] .language-selector-dropdown {
        transform: translateX(-50%) translateY(10px);
    }
    
    .language-selector[data-position="top-left"] .language-selector-dropdown.open,
    .language-selector[data-position="top-right"] .language-selector-dropdown.open {
        transform: translateX(-50%) translateY(0);
    }
    
    .language-notification {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .language-selector-trigger {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .language-selector-trigger:hover,
    .language-selector-trigger[aria-expanded="true"] {
        background: #374151;
        border-color: #4facfe;
    }
    
    .language-selector-dropdown {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .dropdown-header {
        background: #374151;
        border-color: #4a5568;
    }
    
    .dropdown-header h3 {
        color: #e2e8f0;
    }
    
    .currency-info {
        color: #a0aec0;
    }
    
    .language-option {
        color: #e2e8f0;
    }
    
    .language-option:hover,
    .language-option:focus {
        background: #4a5568;
        color: #4facfe;
    }
    
    .language-option.active {
        background: #2b6cb0;
        color: #e2e8f0;
    }
    
    .language-info .native-name {
        color: #a0aec0;
    }
    
    .language-option .currency-code {
        background: #4a5568;
        color: #a0aec0;
    }
    
    .currency-converter {
        background: #374151;
        border-color: #4a5568;
    }
    
    .converter-header h4 {
        color: #e2e8f0;
    }
    
    .amount-input {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .base-currency,
    .target-currency {
        color: #e2e8f0;
    }
    
    .converted-amount {
        color: #4facfe;
    }
    
    .exchange-rate {
        color: #a0aec0;
        background: rgba(79, 172, 254, 0.2);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .language-selector-trigger {
        border-width: 3px;
    }
    
    .language-selector-dropdown {
        border-width: 3px;
    }
    
    .language-option:hover,
    .language-option:focus {
        outline: 2px solid currentColor;
        outline-offset: -2px;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .language-selector-trigger,
    .language-selector-dropdown,
    .language-option,
    .dropdown-arrow {
        transition: none;
    }
    
    .language-notification {
        transition: none;
    }
}

/* Print styles */
@media print {
    .language-selector {
        display: none;
    }
    
    .language-notification {
        display: none;
    }
}

/* Language-specific adjustments */
.language-selector[data-current-lang="ar"] .language-selector-trigger,
.language-selector[data-current-lang="he"] .language-selector-trigger {
    direction: rtl;
}

.language-selector[data-current-lang="ar"] .language-selector-dropdown,
.language-selector[data-current-lang="he"] .language-selector-dropdown {
    direction: rtl;
}

/* Font adjustments for different languages */
.language-selector[data-current-lang="zh"] .language-name,
.language-selector[data-current-lang="ja"] .language-name,
.language-selector[data-current-lang="ko"] .language-name {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans CJK', 'Hiragino Sans', sans-serif;
}

.language-selector[data-current-lang="ar"] .language-name,
.language-selector[data-current-lang="he"] .language-name {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans Arabic', 'Tahoma', sans-serif;
}

.language-selector[data-current-lang="hi"] .language-name {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans Devanagari', sans-serif;
}

.language-selector[data-current-lang="ru"] .language-name {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans Cyrillic', sans-serif;
}

/* Accessibility improvements */
.language-selector-trigger:focus-visible {
    outline: 2px solid #4facfe;
    outline-offset: 2px;
}

.language-option:focus-visible {
    outline: 2px solid #4facfe;
    outline-offset: -2px;
}

/* Animation for language switching */
@keyframes languageSwitch {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    50% {
        opacity: 0.5;
        transform: translateY(-5px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.language-switching {
    animation: languageSwitch 0.5s ease-in-out;
}

/* Loading state */
.language-selector.loading .language-selector-trigger {
    opacity: 0.6;
    cursor: not-allowed;
}

.language-selector.loading .dropdown-arrow::after {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    border: 2px solid #4facfe;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom scrollbar for language list */
.language-list::-webkit-scrollbar {
    width: 6px;
}

.language-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.language-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.language-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Firefox scrollbar */
.language-list {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}