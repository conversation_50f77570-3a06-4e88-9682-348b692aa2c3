const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Uploads the fix_policy_syntax.sql file to Supabase storage
 */
async function uploadFixScript() {
  try {
    console.log('Reading SQL fix script...');
    const filePath = path.join(__dirname, 'fix_policy_syntax.sql');
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    console.log('Checking if app-scripts bucket exists...');
    const { data: buckets, error: bucketError } = await supabase
      .storage
      .listBuckets();
    
    if (bucketError) {
      console.error('Error checking buckets:', bucketError);
      return;
    }
    
    // Create app-scripts bucket if it doesn't exist
    const bucketExists = buckets.some(bucket => bucket.name === 'app-scripts');
    if (!bucketExists) {
      console.log('Creating app-scripts bucket...');
      const { error: createError } = await supabase
        .storage
        .createBucket('app-scripts', { public: false });
      
      if (createError) {
        console.error('Error creating bucket:', createError);
        return;
      }
    }
    
    console.log('Uploading fix script to Supabase storage...');
    const { error: uploadError } = await supabase
      .storage
      .from('app-scripts')
      .upload('fix_policy_syntax.sql', fileContent, {
        contentType: 'text/plain',
        upsert: true
      });
    
    if (uploadError) {
      console.error('Error uploading file:', uploadError);
      return;
    }
    
    console.log('Fix script uploaded successfully!');
    
    // Create RPC function to execute SQL if it doesn't exist
    console.log('Creating exec_sql RPC function if needed...');
    const createRpcFunctionSql = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT)
      RETURNS VOID AS $$
      BEGIN
        EXECUTE sql_query;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      -- Grant execute permission to authenticated users
      GRANT EXECUTE ON FUNCTION exec_sql(TEXT) TO authenticated;
      
      COMMENT ON FUNCTION exec_sql(TEXT) IS 'Executes SQL queries with elevated privileges';
    `;
    
    const { error: rpcError } = await supabase.rpc('exec_sql', { 
      sql_query: createRpcFunctionSql 
    }).catch(() => {
      // If the function doesn't exist yet, execute it directly
      return supabase.from('_rpc').select('*').rpc('exec_sql', { 
        sql_query: createRpcFunctionSql 
      });
    });
    
    if (rpcError) {
      console.error('Error creating RPC function:', rpcError);
      return;
    }
    
    console.log('Setup completed successfully!');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

uploadFixScript().catch(console.error);