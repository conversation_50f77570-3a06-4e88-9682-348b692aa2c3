import { Request, Response } from 'express';
import { ApiResponse, SubscriptionRequest, SubscriptionDetails, ModularSubscription } from '../types/modular-pricing';
import { SubscriptionManager } from '../services/SubscriptionManager';

export class SubscriptionController {
  private subscriptionManager: SubscriptionManager;

  constructor() {
    this.subscriptionManager = new SubscriptionManager();
  }
  /**
   * POST /api/subscriptions
   * Body: { userId: string, serviceIds: string[], paymentMethodId?: string }
   * Creates new subscription with service combination
   */
  async createSubscription(req: Request, res: Response): Promise<void> {
    try {
      const subscriptionRequest: SubscriptionRequest = req.body;
      
      // Input validation
      if (!subscriptionRequest.userId || typeof subscriptionRequest.userId !== 'string' || subscriptionRequest.userId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid User ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      if (!subscriptionRequest.serviceIds || !Array.isArray(subscriptionRequest.serviceIds) || subscriptionRequest.serviceIds.length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Service IDs array is required and cannot be empty',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Validate service IDs are strings
      if (!subscriptionRequest.serviceIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'All service IDs must be non-empty strings',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const subscription = await this.subscriptionManager.createSubscription(subscriptionRequest);
      
      const response: ApiResponse<ModularSubscription> = {
        success: true,
        data: subscription
      };
      
      res.status(201).json(response);
    } catch (error) {
      console.error('SubscriptionController.createSubscription error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'SUBSCRIPTION_CREATION_ERROR';
      let errorMessage = 'Failed to create subscription';

      if (error instanceof Error) {
        if (error.message.includes('Invalid service combination')) {
          statusCode = 400;
          errorCode = 'INVALID_SERVICE_COMBINATION';
          errorMessage = error.message;
        } else if (error.message.includes('already has an active subscription')) {
          statusCode = 409;
          errorCode = 'SUBSCRIPTION_EXISTS';
          errorMessage = error.message;
        } else if (error.message.includes('User ID is required') || error.message.includes('Service IDs are required')) {
          statusCode = 400;
          errorCode = 'INVALID_INPUT';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }

  /**
   * PUT /api/subscriptions/:subscriptionId
   * Body: { serviceIds: string[] }
   * Updates existing subscription services
   */
  async updateSubscription(req: Request, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;
      const { serviceIds } = req.body;
      
      // Input validation
      if (!subscriptionId || typeof subscriptionId !== 'string' || subscriptionId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid Subscription ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Service IDs array is required and cannot be empty',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Validate service IDs are strings
      if (!serviceIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'All service IDs must be non-empty strings',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const subscription = await this.subscriptionManager.updateSubscription(subscriptionId.trim(), serviceIds);
      
      const response: ApiResponse<ModularSubscription> = {
        success: true,
        data: subscription
      };
      
      res.json(response);
    } catch (error) {
      console.error('SubscriptionController.updateSubscription error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'SUBSCRIPTION_UPDATE_ERROR';
      let errorMessage = 'Failed to update subscription';

      if (error instanceof Error) {
        if (error.message.includes('Invalid service combination')) {
          statusCode = 400;
          errorCode = 'INVALID_SERVICE_COMBINATION';
          errorMessage = error.message;
        } else if (error.message.includes('Subscription not found')) {
          statusCode = 404;
          errorCode = 'SUBSCRIPTION_NOT_FOUND';
          errorMessage = error.message;
        } else if (error.message.includes('Cannot update inactive subscription')) {
          statusCode = 409;
          errorCode = 'SUBSCRIPTION_INACTIVE';
          errorMessage = error.message;
        } else if (error.message.includes('Subscription ID is required') || error.message.includes('Service IDs are required')) {
          statusCode = 400;
          errorCode = 'INVALID_INPUT';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }

  /**
   * GET /api/subscriptions/:userId
   * Returns user's current subscription details
   */
  async getSubscriptionDetails(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      
      // Input validation
      if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid User ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const subscriptionDetails = await this.subscriptionManager.getSubscriptionDetails(userId.trim());
      
      if (!subscriptionDetails) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'SUBSCRIPTION_NOT_FOUND',
            message: 'No active subscription found for this user',
            timestamp: new Date().toISOString()
          }
        };
        res.status(404).json(response);
        return;
      }
      
      const response: ApiResponse<SubscriptionDetails> = {
        success: true,
        data: subscriptionDetails
      };
      
      res.json(response);
    } catch (error) {
      console.error('SubscriptionController.getSubscriptionDetails error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'SUBSCRIPTION_RETRIEVAL_ERROR';
      let errorMessage = 'Failed to retrieve subscription details';

      if (error instanceof Error) {
        if (error.message.includes('User ID is required')) {
          statusCode = 400;
          errorCode = 'INVALID_INPUT';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }

  /**
   * DELETE /api/subscriptions/:subscriptionId
   * Cancels subscription
   */
  async cancelSubscription(req: Request, res: Response): Promise<void> {
    try {
      const { subscriptionId } = req.params;
      
      // Input validation
      if (!subscriptionId || typeof subscriptionId !== 'string' || subscriptionId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid Subscription ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const cancelled = await this.subscriptionManager.cancelSubscription(subscriptionId.trim());
      
      const response: ApiResponse<{ cancelled: boolean }> = {
        success: true,
        data: { cancelled }
      };
      
      res.json(response);
    } catch (error) {
      console.error('SubscriptionController.cancelSubscription error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'SUBSCRIPTION_CANCELLATION_ERROR';
      let errorMessage = 'Failed to cancel subscription';

      if (error instanceof Error) {
        if (error.message.includes('Subscription not found')) {
          statusCode = 404;
          errorCode = 'SUBSCRIPTION_NOT_FOUND';
          errorMessage = error.message;
        } else if (error.message.includes('Subscription ID is required')) {
          statusCode = 400;
          errorCode = 'INVALID_INPUT';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }
}