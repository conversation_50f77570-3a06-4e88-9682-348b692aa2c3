# SafeKeep Modular Pricing Migration Guide

This guide walks you through updating your SafeKeep database to support the new modular pricing structure with individual service selections.

## Overview

The new modular pricing system allows users to:
- Select individual services (Contacts, Messages, Photos)
- Choose combination packages for better value
- Upgrade/downgrade between plans seamlessly
- Track usage per service type

## Migration Scripts

### 1. `modular-pricing-migration.sql`
**Purpose**: Creates the new database schema for modular pricing
**Run First**: Yes, this is the foundation script

**What it does**:
- Creates `service_types` table (contacts, messages, photos)
- Creates `subscription_plans` table (7 new plans)
- Creates `plan_services` junction table
- Creates `user_service_selections` table
- Adds new columns to existing tables
- Sets up indexes and RLS policies

### 2. `migrate-existing-subscriptions.sql`
**Purpose**: Migrates existing user subscriptions to the new system
**Run Second**: After the foundation script

**What it does**:
- Maps old tiers (free/basic/premium) to new plans
- Creates service selections for existing users
- Updates storage quotas based on new plans
- Preserves backward compatibility
- Creates helper functions for pricing calculations

### 3. `pricing-calculation-functions.sql`
**Purpose**: Provides database functions for pricing logic
**Run Third**: After migration is complete

**What it does**:
- Creates functions for optimal pricing calculations
- Provides service combination recommendations
- Handles user service updates
- Calculates usage statistics
- Validates service access permissions

### 4. `verify-modular-pricing.sql`
**Purpose**: Verifies the migration was successful
**Run Last**: For testing and verification

**What it does**:
- Checks all tables and data exist
- Tests pricing calculation functions
- Verifies data integrity
- Runs sample pricing scenarios
- Performance testing

## Step-by-Step Migration Process

### Prerequisites
- Backup your database before starting
- Ensure you have admin access to your Supabase project
- Test in a development environment first

### Step 1: Run Foundation Migration
```sql
-- Copy and paste the entire content of modular-pricing-migration.sql
-- into your Supabase SQL Editor and execute
```

### Step 2: Migrate Existing Data
```sql
-- Copy and paste the entire content of migrate-existing-subscriptions.sql
-- into your Supabase SQL Editor and execute
```

### Step 3: Add Pricing Functions
```sql
-- Copy and paste the entire content of pricing-calculation-functions.sql
-- into your Supabase SQL Editor and execute
```

### Step 4: Verify Migration
```sql
-- Copy and paste the entire content of verify-modular-pricing.sql
-- into your Supabase SQL Editor and execute
-- Review the output to ensure everything is working correctly
```

## New Database Schema

### Core Tables

#### `service_types`
Defines the three backup services:
- `contacts`: $0.99/month, 1GB storage
- `messages`: $1.99/month, 2GB storage  
- `photos`: $4.99/month, 10GB storage

#### `subscription_plans`
Defines 7 pricing tiers:
- Individual: `contacts-only`, `messages-only`, `photos-only`
- Combinations: `contacts-messages`, `contacts-photos`, `messages-photos`
- Complete: `complete-backup` (all services)

#### `plan_services`
Junction table linking plans to included services

#### `user_service_selections`
Tracks which services each user has activated

### Updated Tables

#### `user_subscriptions`
New columns:
- `plan_id`: Links to new subscription plans
- `legacy_tier_id`: Preserves old tier for backward compatibility
- `total_price_cents`: Calculated total price
- `service_combination`: JSON array of selected services

#### `storage_usage`
New columns for service-specific tracking:
- `contacts_count`, `contacts_size_bytes`
- `messages_count`, `messages_size_bytes`
- `photos_count`, `photos_size_bytes`

## Pricing Logic

### Individual Services
- Contacts Only: $0.99/month
- Messages Only: $1.99/month
- Photos Only: $4.99/month

### Combination Packages (Better Value)
- Contacts + Messages: $2.49/month (save $0.49)
- Contacts + Photos: $5.49/month (save $0.49)
- Messages + Photos: $6.49/month (save $0.49) - **POPULAR**
- Complete Backup: $6.99/month (save $0.99)

### Automatic Optimization
The system automatically recommends the most cost-effective plan based on selected services.

## Key Functions

### `calculate_optimal_price(service_ids)`
Returns the best pricing option for selected services.

### `get_user_subscription_details(user_id)`
Gets complete subscription info including services and usage.

### `update_user_services(user_id, service_ids)`
Updates a user's service selections.

### `user_has_service_access(user_id, service_type)`
Validates if a user can access a specific service.

## Backward Compatibility

### Existing Users
- Free tier users → Contacts Only ($0.99 or free trial)
- Basic tier users → Contacts + Messages ($2.49)
- Premium tier users → Complete Backup ($6.99)

### Legacy Support
- Old tier IDs preserved in `legacy_tier_id` column
- Existing Stripe subscriptions continue to work
- Gradual migration path for users

## Frontend Integration

### Updated Configuration Files
The following files have been updated to support modular pricing:
- `SafeKeepApp/web-demo/subscription-tier-config.js`
- `SafeKeepApp/web-demo/stripe-manager.js`
- `SafeKeepApp/web-demo.html`
- `SafeKeepApp/src/services/StripeService.ts`

### API Changes
Your frontend should now:
1. Call `get_available_service_combinations()` to show pricing options
2. Use `calculate_optimal_price()` before checkout
3. Update `user_service_selections` after successful payment
4. Check `user_has_service_access()` before allowing backups

## Testing

### Test Scenarios
1. **New User Signup**: Select individual service, verify pricing
2. **Service Combination**: Select multiple services, verify discount applied
3. **Upgrade/Downgrade**: Change services, verify pricing updates
4. **Usage Tracking**: Upload files, verify service-specific counts
5. **Access Control**: Verify users can only backup selected services

### Test Data
The verification script includes sample scenarios you can run to test the system.

## Troubleshooting

### Common Issues

#### Migration Fails
- Check for foreign key constraints
- Ensure all prerequisite tables exist
- Verify user permissions

#### Pricing Calculations Wrong
- Run verification script to check function logic
- Verify service_types pricing is correct
- Check plan_services relationships

#### Users Can't Access Services
- Verify user_service_selections records exist
- Check RLS policies are correctly applied
- Ensure subscription status is 'active'

### Rollback Plan
If you need to rollback:
1. Restore from backup taken before migration
2. Or disable new tables and revert to legacy_tier_id usage

## Performance Considerations

### Indexes
All necessary indexes are created automatically:
- User-based lookups are optimized
- Service combination queries are fast
- Usage tracking queries are efficient

### Caching
Consider caching:
- Available service combinations
- User's current plan details
- Pricing calculations for common combinations

## Security

### Row Level Security (RLS)
- All new tables have RLS enabled
- Users can only access their own data
- Service types and plans are publicly readable
- Admin functions require proper authentication

### Data Privacy
- Service selections are private to each user
- Usage statistics are user-specific
- Payment information remains encrypted

## Monitoring

### Key Metrics to Track
- Plan distribution (which plans are most popular)
- Upgrade/downgrade patterns
- Service usage by type
- Revenue per user by plan

### Queries for Analytics
```sql
-- Plan popularity
SELECT plan_id, COUNT(*) FROM user_subscriptions 
WHERE status = 'active' GROUP BY plan_id;

-- Revenue by plan
SELECT plan_id, SUM(total_price_cents) FROM user_subscriptions 
WHERE status = 'active' GROUP BY plan_id;

-- Service usage distribution
SELECT service_type_id, COUNT(*) FROM user_service_selections 
WHERE is_active = TRUE GROUP BY service_type_id;
```

## Next Steps

After successful migration:
1. Update your frontend to use the new pricing structure
2. Test the complete user journey from signup to service usage
3. Update your Stripe webhook handlers for new plan IDs
4. Monitor the system for any issues
5. Consider A/B testing different pricing strategies

## Support

If you encounter issues during migration:
1. Check the verification script output
2. Review Supabase logs for errors
3. Test individual functions manually
4. Ensure all foreign key relationships are correct

The modular pricing system provides much more flexibility for your users while maintaining backward compatibility with existing subscriptions.