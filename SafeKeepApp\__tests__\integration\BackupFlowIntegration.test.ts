import BackupManager from '../../src/services/BackupManager';
import ContactBackupService from '../../src/services/ContactBackupService';
import MessageBackupService from '../../src/services/MessageBackupService';
import PhotoBackupService from '../../src/services/PhotoBackupService';
import AuthService from '../../src/services/AuthService';
import PermissionService from '../../src/services/PermissionService';
import CloudStorageService from '../../src/services/CloudStorageService';
import EncryptionService from '../../src/services/EncryptionService';
import NotificationService from '../../src/services/NotificationService';
import BackupRecoveryService from '../../src/services/BackupRecoveryService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupConfiguration, BackupServiceResult, BackupError } from '../../src/types/backup';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import { Platform, PermissionsAndroid } from 'react-native';

// Mock network and battery utilities
jest.mock('../../src/utils/helpers', () => ({
  isWiFiConnected: jest.fn().mockResolvedValue(true),
  hasSufficientBattery: jest.fn().mockResolvedValue(true),
  generateId: jest.fn().mockReturnValue('test-session-id'),
  delay: jest.fn().mockResolvedValue(undefined)
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockCameraRoll = CameraRoll as jest.Mocked<typeof CameraRoll>;
const mockRNFS = RNFS as jest.Mocked<typeof RNFS>;
const mockPermissionsAndroid = PermissionsAndroid as jest.Mocked<typeof PermissionsAndroid>;

describe('Backup Flow Integration Tests', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    aud: 'authenticated',
    role: 'authenticated',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    app_metadata: {},
    user_metadata: {}
  };

  const defaultConfiguration: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium'
  };

  // Mock data for testing
  const mockContacts = [
    {
      recordID: '1',
      displayName: 'John Doe',
      phoneNumbers: [{ label: 'mobile', number: '+1234567890' }],
      emailAddresses: [{ label: 'work', email: '<EMAIL>' }]
    },
    {
      recordID: '2',
      displayName: 'Jane Smith',
      phoneNumbers: [{ label: 'home', number: '+0987654321' }],
      emailAddresses: []
    }
  ];

  const mockMessages = [
    {
      id: '1',
      body: 'Hello world',
      address: '+1234567890',
      date: Date.now() - 1000,
      type: 'inbox'
    },
    {
      id: '2',
      body: 'Test message',
      address: '+0987654321',
      date: Date.now(),
      type: 'sent'
    }
  ];

  const mockMediaLibrary = {
    edges: [
      {
        node: {
          image: {
            uri: 'file://photo1.jpg',
            filename: 'photo1.jpg',
            fileSize: 1024000
          },
          type: 'image/jpeg',
          timestamp: new Date(Date.now() - 2000).toISOString()
        }
      },
      {
        node: {
          image: {
            uri: 'file://photo2.png',
            filename: 'photo2.png',
            fileSize: 2048000
          },
          type: 'image/png',
          timestamp: new Date(Date.now() - 1000).toISOString()
        }
      },
      {
        node: {
          image: {
            uri: 'file://video1.mp4',
            filename: 'video1.mp4',
            fileSize: 10240000
          },
          type: 'video/mp4',
          timestamp: new Date().toISOString()
        }
      }
    ]
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Setup authentication
    jest.spyOn(AuthService, 'getCurrentUser').mockReturnValue(mockUser);
    
    // Setup permissions
    jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
      contacts: { granted: true, denied: false, blocked: false, unavailable: false },
      sms: { granted: true, denied: false, blocked: false, unavailable: false },
      photos: { granted: true, denied: false, blocked: false, unavailable: false }
    });
    
    // Setup storage
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    
    // Setup permissions
    mockPermissionsAndroid.request.mockResolvedValue('granted');
    
    // Setup camera roll
    mockCameraRoll.getPhotos.mockResolvedValue(mockMediaLibrary);
    
    // Setup file system
    mockRNFS.stat.mockResolvedValue({
      size: 1024000,
      mtime: new Date(),
      isFile: () => true,
      isDirectory: () => false,
      path: 'file://photo1.jpg'
    } as any);
    mockRNFS.read.mockResolvedValue('base64data');
    mockRNFS.readFile.mockResolvedValue('base64filedata');
    
    // Setup encryption service
    jest.spyOn(EncryptionService, 'encrypt').mockResolvedValue({
      success: true,
      data: 'encrypted_data'
    });
    
    // Setup cloud storage service
    jest.spyOn(CloudStorageService, 'uploadFile').mockResolvedValue({
      success: true,
      fileId: 'uploaded_file_id'
    });
    
    // Setup contact service
    jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);
    jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async (contacts, onProgress) => {
      if (onProgress) {
        onProgress({
          totalContacts: contacts.length,
          processedContacts: 0,
          currentContact: contacts[0]?.displayName || '',
          percentage: 0,
          status: 'processing'
        });
        
        // Simulate progress
        for (let i = 0; i < contacts.length; i++) {
          await new Promise(resolve => setTimeout(resolve, 10));
          onProgress({
            totalContacts: contacts.length,
            processedContacts: i + 1,
            currentContact: contacts[i].displayName,
            percentage: Math.round(((i + 1) / contacts.length) * 100),
            status: i === contacts.length - 1 ? 'completed' : 'processing'
          });
        }
      }
      return { success: true, itemsProcessed: contacts.length, errors: [] };
    });
    
    // Setup message service
    jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue(mockMessages as any);
    jest.spyOn(MessageBackupService, 'backupMessages').mockImplementation(async (messages, onProgress) => {
      if (onProgress) {
        for (let i = 0; i < messages.length; i++) {
          await new Promise(resolve => setTimeout(resolve, 10));
          onProgress({
            totalMessages: messages.length,
            processedMessages: i + 1,
            currentMessage: `Message ${i + 1}`,
            percentage: Math.round(((i + 1) / messages.length) * 100),
            status: i === messages.length - 1 ? 'completed' : 'processing'
          });
        }
      }
      return { success: true, itemsProcessed: messages.length, errors: [] };
    });
    
    // Setup photo service
    jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
      photos: [
        {
          uri: 'file://photo1.jpg',
          filename: 'photo1.jpg',
          type: 'image/jpeg',
          fileSize: 1024000,
          timestamp: Date.now() - 2000
        },
        {
          uri: 'file://photo2.png',
          filename: 'photo2.png',
          type: 'image/png',
          fileSize: 2048000,
          timestamp: Date.now() - 1000
        }
      ],
      excludedVideos: [
        {
          uri: 'file://video1.mp4',
          filename: 'video1.mp4',
          type: 'video/mp4',
          fileSize: 10240000,
          timestamp: Date.now()
        }
      ],
      totalScanned: 3
    });
    
    jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async (photos, onProgress) => {
      if (onProgress) {
        onProgress({
          totalPhotos: photos.length,
          processedPhotos: 0,
          currentPhoto: 'Starting backup...',
          bytesUploaded: 0,
          totalBytes: photos.reduce((sum, p) => sum + p.fileSize, 0),
          percentage: 0,
          status: 'processing'
        });
        
        for (let i = 0; i < photos.length; i++) {
          await new Promise(resolve => setTimeout(resolve, 10));
          onProgress({
            totalPhotos: photos.length,
            processedPhotos: i + 1,
            currentPhoto: photos[i].filename,
            bytesUploaded: photos.slice(0, i + 1).reduce((sum, p) => sum + p.fileSize, 0),
            totalBytes: photos.reduce((sum, p) => sum + p.fileSize, 0),
            percentage: Math.round(((i + 1) / photos.length) * 100),
            status: i === photos.length - 1 ? 'completed' : 'uploading'
          });
        }
      }
      return { success: true, itemsProcessed: photos.length, errors: [] };
    });
    
    // Initialize BackupManager
    await BackupManager.initialize();
  });

  describe('End-to-End Backup Flow', () => {
    it('should complete full backup process from UI to storage', async () => {
      const progressUpdates: any[] = [];
      const progressCallback = jest.fn((progress) => {
        progressUpdates.push(progress);
      });

      const result = await BackupManager.startBackup(defaultConfiguration, progressCallback);

      // Verify backup completed successfully
      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(6); // 2 contacts + 2 messages + 2 photos
      expect(result.errors).toHaveLength(0);

      // Verify all services were called
      expect(ContactBackupService.scanContacts).toHaveBeenCalled();
      expect(ContactBackupService.backupContacts).toHaveBeenCalledWith(
        mockContacts,
        expect.any(Function)
      );
      
      expect(MessageBackupService.scanMessages).toHaveBeenCalled();
      expect(MessageBackupService.backupMessages).toHaveBeenCalledWith(
        mockMessages,
        expect.any(Function)
      );
      
      expect(PhotoBackupService.scanPhotoLibrary).toHaveBeenCalledWith(1000);
      expect(PhotoBackupService.backupPhotos).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ filename: 'photo1.jpg' }),
          expect.objectContaining({ filename: 'photo2.png' })
        ]),
        expect.any(Function)
      );

      // Verify progress updates were received
      expect(progressCallback).toHaveBeenCalled();
      expect(progressUpdates.length).toBeGreaterThan(0);
      
      // Verify final progress shows completion
      const finalProgress = progressUpdates[progressUpdates.length - 1];
      expect(finalProgress.overallProgress).toBe(100);

      // Verify session was created and tracked
      const session = BackupManager.getCurrentSession();
      expect(session).toBeTruthy();
      expect(session?.status).toBe('completed');
      expect(session?.totalItems).toBe(6);
      expect(session?.completedItems).toBe(6);

      // Verify state was saved
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'backup_manager_state',
        expect.any(String)
      );
    });

    it('should handle partial backup when some data types are disabled', async () => {
      const partialConfig: BackupConfiguration = {
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      };

      const result = await BackupManager.startBackup(partialConfig);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2); // Only contacts
      
      expect(ContactBackupService.scanContacts).toHaveBeenCalled();
      expect(MessageBackupService.scanMessages).not.toHaveBeenCalled();
      expect(PhotoBackupService.scanPhotoLibrary).not.toHaveBeenCalled();
    });

    it('should continue with available data types when one fails', async () => {
      // Make contacts fail
      jest.spyOn(ContactBackupService, 'scanContacts').mockRejectedValue(new Error('Contacts failed'));

      const result = await BackupManager.startBackup(defaultConfiguration);

      // Should still process messages and photos
      expect(MessageBackupService.scanMessages).toHaveBeenCalled();
      expect(PhotoBackupService.scanPhotoLibrary).toHaveBeenCalled();
      
      // Should have errors but still process other types
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.itemsProcessed).toBe(4); // 2 messages + 2 photos
    });
  });

  describe('Video Exclusion Functionality', () => {
    it('should exclude videos from photo backup in mixed media libraries', async () => {
      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2); // Only 2 photos, video excluded

      // Verify PhotoBackupService received only photos
      expect(PhotoBackupService.backupPhotos).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ 
            filename: 'photo1.jpg',
            type: 'image/jpeg'
          }),
          expect.objectContaining({ 
            filename: 'photo2.png',
            type: 'image/png'
          })
        ]),
        expect.any(Function)
      );

      // Verify no video files were passed to backup
      const backupCall = (PhotoBackupService.backupPhotos as jest.Mock).mock.calls[0];
      const photosToBackup = backupCall[0];
      expect(photosToBackup.every((photo: any) => !photo.type.startsWith('video/'))).toBe(true);
    });

    it('should properly identify and exclude various video formats', async () => {
      const mixedMediaLibrary = {
        edges: [
          {
            node: {
              image: { uri: 'file://photo.jpg', filename: 'photo.jpg', fileSize: 1000 },
              type: 'image/jpeg',
              timestamp: new Date().toISOString()
            }
          },
          {
            node: {
              image: { uri: 'file://video.mp4', filename: 'video.mp4', fileSize: 5000 },
              type: 'video/mp4',
              timestamp: new Date().toISOString()
            }
          },
          {
            node: {
              image: { uri: 'file://video.mov', filename: 'video.mov', fileSize: 6000 },
              type: 'video/quicktime',
              timestamp: new Date().toISOString()
            }
          },
          {
            node: {
              image: { uri: 'file://video.avi', filename: 'video.avi', fileSize: 7000 },
              type: 'video/x-msvideo',
              timestamp: new Date().toISOString()
            }
          }
        ]
      };

      mockCameraRoll.getPhotos.mockResolvedValue(mixedMediaLibrary);
      
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: [
          {
            uri: 'file://photo.jpg',
            filename: 'photo.jpg',
            type: 'image/jpeg',
            fileSize: 1000,
            timestamp: Date.now()
          }
        ],
        excludedVideos: [
          {
            uri: 'file://video.mp4',
            filename: 'video.mp4',
            type: 'video/mp4',
            fileSize: 5000,
            timestamp: Date.now()
          },
          {
            uri: 'file://video.mov',
            filename: 'video.mov',
            type: 'video/quicktime',
            fileSize: 6000,
            timestamp: Date.now()
          },
          {
            uri: 'file://video.avi',
            filename: 'video.avi',
            type: 'video/x-msvideo',
            fileSize: 7000,
            timestamp: Date.now()
          }
        ],
        totalScanned: 4
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(1); // Only 1 photo

      // Verify scan results
      const scanResult = await PhotoBackupService.scanPhotoLibrary();
      expect(scanResult.photos).toHaveLength(1);
      expect(scanResult.excludedVideos).toHaveLength(3);
      expect(scanResult.totalScanned).toBe(4);
    });
  });

  describe('Network Condition Handling', () => {
    it('should prevent backup when WiFi-only is enabled but not on WiFi', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('network');
      expect(result.errors[0].message).toContain('WiFi-only');
    });

    it('should show warning but allow backup on cellular when WiFi-only is disabled', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      const cellularConfig: BackupConfiguration = {
        ...defaultConfiguration,
        wifiOnly: false
      };

      const result = await BackupManager.startBackup(cellularConfig);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(6);
    });

    it('should handle network interruption during backup', async () => {
      // Start backup normally
      const backupPromise = BackupManager.startBackup(defaultConfiguration);

      // Simulate network failure during backup
      jest.spyOn(CloudStorageService, 'uploadFile').mockResolvedValueOnce({
        success: false,
        error: 'Network error'
      });

      const result = await backupPromise;

      // Should have some errors but continue with other items
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.type === 'network')).toBe(true);
    });
  });

  describe('Backup Interruption and Resume', () => {
    it('should handle backup interruption gracefully', async () => {
      // Start backup
      const backupPromise = BackupManager.startBackup(defaultConfiguration);

      // Simulate interruption after a short delay
      setTimeout(() => {
        BackupManager.pauseBackup();
      }, 50);

      const result = await backupPromise;

      expect(result).toBeDefined();
      expect(BackupManager.isPaused()).toBe(true);
    });

    it('should resume backup from interruption point', async () => {
      // Mock recovery service
      jest.spyOn(BackupRecoveryService, 'checkForResumableSession').mockResolvedValue({
        sessionId: 'resumable-session',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 1,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      });

      jest.spyOn(BackupRecoveryService, 'resumeBackup').mockResolvedValue({
        success: true,
        recoveredItems: 3,
        errors: []
      });

      // Initialize should detect resumable session
      await BackupManager.initialize();

      // Verify recovery service was called
      expect(BackupRecoveryService.checkForResumableSession).toHaveBeenCalled();
    });

    it('should handle backup cancellation', async () => {
      // Start backup
      const backupPromise = BackupManager.startBackup(defaultConfiguration);

      // Cancel immediately
      await BackupManager.cancelBackup();

      const result = await backupPromise;

      expect(BackupManager.isRunning()).toBe(false);
      expect(result).toBeDefined();
    });
  });

  describe('Error Scenarios and Recovery', () => {
    it('should handle authentication errors', async () => {
      jest.spyOn(AuthService, 'getCurrentUser').mockReturnValue(null);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('authentication required');
    });

    it('should handle permission denial errors', async () => {
      jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
        contacts: { granted: false, denied: true, blocked: false, unavailable: false },
        sms: { granted: false, denied: true, blocked: false, unavailable: false },
        photos: { granted: false, denied: true, blocked: false, unavailable: false }
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('Missing permissions');
    });

    it('should handle encryption failures', async () => {
      jest.spyOn(EncryptionService, 'encrypt').mockResolvedValue({
        success: false,
        error: 'Encryption failed'
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.message.includes('encrypt'))).toBe(true);
    });

    it('should handle storage quota exceeded', async () => {
      jest.spyOn(CloudStorageService, 'uploadFile').mockResolvedValue({
        success: false,
        error: 'Storage quota exceeded'
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.type === 'network')).toBe(true);
    });

    it('should handle low battery conditions', async () => {
      const { hasSufficientBattery } = require('../../src/utils/helpers');
      hasSufficientBattery.mockResolvedValue(false);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
      expect(result.errors[0].message).toContain('Battery level too low');
    });

    it('should provide retry functionality for failed items', async () => {
      // Mock some failures
      jest.spyOn(ContactBackupService, 'backupContacts').mockResolvedValue({
        success: false,
        itemsProcessed: 1,
        errors: [{
          id: 'contact_error_1',
          type: 'network',
          message: 'Network timeout',
          timestamp: new Date(),
          retryable: true
        }]
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.retryable)).toBe(true);

      // Test retry functionality
      const retryResult = await BackupManager.retryFailedItems();
      expect(retryResult.success).toBe(true);
    });

    it('should show appropriate error notifications', async () => {
      jest.spyOn(NotificationService, 'showErrorNotification').mockResolvedValue();

      // Cause an error
      jest.spyOn(ContactBackupService, 'scanContacts').mockRejectedValue(new Error('Service error'));

      await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      // Should show error notification
      expect(NotificationService.showErrorNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'platform',
          message: expect.stringContaining('Service error'),
          retryable: true
        })
      );
    });

    it('should handle concurrent backup attempts', async () => {
      // Start first backup
      const firstBackup = BackupManager.startBackup(defaultConfiguration);

      // Try to start second backup
      const secondBackup = await BackupManager.startBackup(defaultConfiguration);

      expect(secondBackup.success).toBe(false);
      expect(secondBackup.errors[0].message).toContain('already running');

      // Wait for first backup to complete
      await firstBackup;
    });
  });

  describe('Progress Tracking and Statistics', () => {
    it('should provide accurate progress updates throughout backup', async () => {
      const progressUpdates: any[] = [];
      const progressCallback = jest.fn((progress) => {
        progressUpdates.push({
          currentDataType: progress.currentDataType,
          overallProgress: progress.overallProgress,
          sessionStatus: progress.session.status
        });
      });

      await BackupManager.startBackup(defaultConfiguration, progressCallback);

      // Should have multiple progress updates
      expect(progressUpdates.length).toBeGreaterThan(3);

      // Should progress through different data types
      const dataTypes = progressUpdates.map(p => p.currentDataType).filter(Boolean);
      expect(dataTypes).toContain('contacts');
      expect(dataTypes).toContain('messages');
      expect(dataTypes).toContain('photos');

      // Progress should increase over time
      const progressValues = progressUpdates.map(p => p.overallProgress);
      expect(progressValues[progressValues.length - 1]).toBe(100);
    });

    it('should calculate estimated time remaining accurately', async () => {
      const progressUpdates: any[] = [];
      const progressCallback = jest.fn((progress) => {
        progressUpdates.push(progress);
      });

      await BackupManager.startBackup(defaultConfiguration, progressCallback);

      // Should have estimated time remaining in some updates
      const updatesWithETA = progressUpdates.filter(p => p.estimatedTimeRemaining !== undefined);
      expect(updatesWithETA.length).toBeGreaterThan(0);
    });

    it('should maintain backup statistics', async () => {
      await BackupManager.startBackup(defaultConfiguration);

      const stats = await BackupManager.getBackupStatistics();

      expect(stats.totalSessions).toBeGreaterThan(0);
      expect(stats.totalItemsBackedUp).toBe(6);
      expect(stats.lastBackupDate).toBeDefined();
      expect(stats.successRate).toBeGreaterThan(0);
    });
  });

  describe('Platform-Specific Behavior', () => {
    it('should handle iOS message backup limitations', async () => {
      (Platform as any).OS = 'ios';

      const result = await BackupManager.startBackup(defaultConfiguration);

      // Should still succeed but with message backup error
      expect(result.itemsProcessed).toBe(4); // 2 contacts + 2 photos, no messages
      expect(result.errors.some(e => e.message.includes('iOS') && e.message.includes('Message'))).toBe(true);
    });

    it('should handle Android SMS permission requirements', async () => {
      (Platform as any).OS = 'android';
      
      jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
        contacts: { granted: true, denied: false, blocked: false, unavailable: false },
        sms: { granted: false, denied: true, blocked: false, unavailable: false },
        photos: { granted: true, denied: false, blocked: false, unavailable: false }
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('messages'))).toBe(true);
    });
  });

  describe('Data Integrity and Validation', () => {
    it('should validate data integrity throughout backup process', async () => {
      // Mock checksum validation
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async (data, filename) => {
        // Simulate checksum validation
        expect(data).toBeDefined();
        expect(filename).toBeDefined();
        return { success: true, fileId: `validated_${filename}` };
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(true);
      expect(CloudStorageService.uploadFile).toHaveBeenCalledTimes(6); // All items uploaded
    });

    it('should handle corrupted data gracefully', async () => {
      // Mock file read failure
      mockRNFS.readFile.mockRejectedValueOnce(new Error('File corrupted'));

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      // Should handle error and continue with other files
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.message.includes('corrupted') || e.message.includes('File'))).toBe(true);
    });
  });

  describe('Memory and Performance', () => {
    it('should handle large datasets efficiently', async () => {
      // Mock large dataset
      const largeContactList = Array.from({ length: 1000 }, (_, i) => ({
        recordID: `${i}`,
        displayName: `Contact ${i}`,
        phoneNumbers: [{ label: 'mobile', number: `+123456789${i}` }],
        emailAddresses: []
      }));

      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(largeContactList as any);
      jest.spyOn(ContactBackupService, 'backupContacts').mockResolvedValue({
        success: true,
        itemsProcessed: largeContactList.length,
        errors: []
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(1000);
    });

    it('should process items in chunks to avoid memory issues', async () => {
      // This test verifies that the backup manager processes items efficiently
      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(true);
      // Verify that all services were called (indicating chunked processing worked)
      expect(ContactBackupService.backupContacts).toHaveBeenCalled();
      expect(MessageBackupService.backupMessages).toHaveBeenCalled();
      expect(PhotoBackupService.backupPhotos).toHaveBeenCalled();
    });
  });
});