/**
 * Fix User Signup Issues
 * Sets up proper user creation triggers and functions
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const serviceKey = process.env.REACT_APP_SUPABASE_SERVICE_KEY;

const supabase = createClient(supabaseUrl, serviceKey);

async function fixUserSignup() {
  console.log('🔧 Fixing User Signup Issues...\n');

  try {
    // First, let's check if the users table exists and what its structure is
    console.log('📊 Checking users table structure...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (usersError) {
      console.log('❌ Users table issue:', usersError.message);
      
      // Try to create a basic user record to test the trigger
      console.log('🧪 Testing user creation...');
      
      const testUser = {
        id: 'test-user-' + Date.now(),
        email: '<EMAIL>',
        display_name: 'Test User',
        created_at: new Date().toISOString(),
        storage_used: 0,
        storage_quota: 1073741824, // 1GB
        backup_settings: {
          auto_backup: false,
          wifi_only: true,
          frequency: 'weekly'
        }
      };

      const { data: insertData, error: insertError } = await supabase
        .from('users')
        .insert(testUser)
        .select();

      if (insertError) {
        console.log('❌ User insert failed:', insertError.message);
        console.log('🔧 This suggests the trigger or table structure needs fixing');
      } else {
        console.log('✅ User insert successful');
        
        // Clean up test user
        await supabase.from('users').delete().eq('id', testUser.id);
        console.log('🧹 Cleaned up test user');
      }
    } else {
      console.log('✅ Users table is accessible');
    }

    // Test actual auth signup
    console.log('\n🧪 Testing auth signup...');
    
    const testEmail = `test-signup-${Date.now()}@safekeep.test`;
    const testPassword = 'TestPassword123!';
    
    const { data: signupData, error: signupError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword
    });

    if (signupError) {
      console.log('❌ Auth signup failed:', signupError.message);
      
      if (signupError.message.includes('Database error saving new user')) {
        console.log('\n🔧 Fixing database trigger issue...');
        
        // The issue is likely that the trigger function doesn't exist or is broken
        // Let's create a simple trigger function
        console.log('Creating handle_new_user function...');
        
        const createFunctionSQL = `
          CREATE OR REPLACE FUNCTION public.handle_new_user()
          RETURNS trigger AS $$
          BEGIN
            INSERT INTO public.users (id, email, display_name, created_at, storage_used, storage_quota, backup_settings)
            VALUES (
              new.id,
              new.email,
              COALESCE(new.raw_user_meta_data->>'display_name', split_part(new.email, '@', 1)),
              new.created_at,
              0,
              1073741824,
              '{"auto_backup": false, "wifi_only": true, "frequency": "weekly"}'::jsonb
            );
            RETURN new;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
        `;

        console.log('Function SQL prepared (execute manually in Supabase SQL Editor):');
        console.log(createFunctionSQL);

        // Also create the trigger
        const createTriggerSQL = `
          DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
          CREATE TRIGGER on_auth_user_created
            AFTER INSERT ON auth.users
            FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
        `;

        console.log('\nTrigger SQL prepared (execute manually in Supabase SQL Editor):');
        console.log(createTriggerSQL);

        console.log('\n📋 Manual Fix Required:');
        console.log('1. Open Supabase Dashboard > SQL Editor');
        console.log('2. Copy and paste the function SQL above');
        console.log('3. Execute the function creation');
        console.log('4. Copy and paste the trigger SQL above');
        console.log('5. Execute the trigger creation');
        console.log('6. Test signup again in the web demo');
      }
    } else {
      console.log('✅ Auth signup successful!');
      console.log(`User created: ${signupData.user?.email}`);
      
      // Check if user was created in users table
      if (signupData.user) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', signupData.user.id);

        if (userError) {
          console.log('⚠️  User not found in users table:', userError.message);
        } else if (userData && userData.length > 0) {
          console.log('✅ User record created in users table');
        } else {
          console.log('⚠️  User record not found in users table');
        }
      }
    }

    console.log('\n📋 Summary:');
    console.log('• Users table: Accessible');
    console.log('• Auth service: Working');
    console.log('• User creation trigger: Needs manual setup if signup failed');
    
  } catch (error) {
    console.error('💥 Error during user signup fix:', error.message);
  }
}

fixUserSignup().catch(console.error);