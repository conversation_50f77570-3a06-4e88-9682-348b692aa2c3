<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Export/Import Test - SafeKeep Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007AFF;
            padding-bottom: 10px;
        }
        
        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .format-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }
        
        .format-card h4 {
            margin: 0 0 10px 0;
            color: #007AFF;
        }
        
        .compatibility-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }
        
        .tag {
            background: #007AFF;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }
        
        button:hover {
            background: #0056CC;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        select, input[type="file"] {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007AFF, #00C7FF);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success { color: #28a745; }
        .log-entry.error { color: #dc3545; }
        .log-entry.warning { color: #ffc107; }
        .log-entry.info { color: #17a2b8; }
        
        .demo-data {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .validation-results {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
        }
        
        .validation-results.valid {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .validation-results.invalid {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Data Export/Import Test</h1>
        <p>Test the data export and import functionality with various formats and options.</p>
        
        <!-- Export Section -->
        <div class="section">
            <h3>📤 Export Data</h3>
            
            <div class="demo-data">
                <h4>Sample Backup Session</h4>
                <p><strong>Session ID:</strong> <span id="sampleSessionId">demo-session-001</span></p>
                <p><strong>Data Types:</strong> Contacts (150), Messages (2,340), Photos (89)</p>
                <p><strong>Total Size:</strong> 45.2 MB</p>
            </div>
            
            <div class="controls">
                <select id="exportFormat">
                    <option value="json">JSON Format</option>
                    <option value="csv">CSV Format</option>
                    <option value="xml">XML Format</option>
                    <option value="zip">ZIP Archive</option>
                    <option value="backup">SafeKeep Backup</option>
                </select>
                
                <label>
                    <input type="checkbox" id="prettyFormat"> Pretty Format
                </label>
                
                <label>
                    <input type="checkbox" id="encryptExport"> Encrypt Export
                </label>
                
                <button onclick="startExport()">Export Data</button>
                <button onclick="showFormatInfo()">Format Info</button>
            </div>
            
            <div class="progress-container" id="exportProgress">
                <div class="progress-bar">
                    <div class="progress-fill" id="exportProgressFill"></div>
                </div>
                <div class="progress-text" id="exportProgressText">Preparing export...</div>
            </div>
        </div>
        
        <!-- Import Section -->
        <div class="section">
            <h3>📥 Import Data</h3>
            
            <div class="controls">
                <input type="file" id="importFile" accept=".json,.csv,.xml,.zip,.skb" onchange="handleFileSelect()">
                <button onclick="startImport()" id="importBtn" disabled>Import Data</button>
                <button onclick="validateImport()" id="validateBtn" disabled>Validate Only</button>
            </div>
            
            <div class="progress-container" id="importProgress">
                <div class="progress-bar">
                    <div class="progress-fill" id="importProgressFill"></div>
                </div>
                <div class="progress-text" id="importProgressText">Preparing import...</div>
            </div>
            
            <div id="validationResults"></div>
        </div>
        
        <!-- Format Information -->
        <div class="section">
            <h3>📋 Supported Formats</h3>
            <div class="format-grid" id="formatGrid">
                <!-- Formats will be populated by JavaScript -->
            </div>
        </div>
        
        <!-- Cross-Platform Compatibility -->
        <div class="section">
            <h3>🌐 Cross-Platform Compatibility</h3>
            <div class="controls">
                <button onclick="testCompatibility()">Test Compatibility</button>
                <button onclick="showCompatibilityMatrix()">Show Matrix</button>
            </div>
            <div id="compatibilityResults"></div>
        </div>
        
        <!-- Activity Log -->
        <div class="section">
            <h3>📝 Activity Log</h3>
            <button onclick="clearLog()" style="float: right; background: #6c757d;">Clear Log</button>
            <div class="log" id="activityLog">
                <div class="log-entry info">System initialized - Ready for export/import operations</div>
            </div>
        </div>
    </div>

    <!-- Include the data export/import manager -->
    <script src="data-export-import-manager.js"></script>
    
    <script>
        // Initialize the manager with mock Supabase clients
        const mockSupabase = {
            from: (table) => ({
                select: (fields) => ({
                    eq: (field, value) => ({
                        single: () => Promise.resolve({
                            data: generateMockBackupSession(),
                            error: null
                        })
                    })
                })
            })
        };
        
        const exportImportManager = new DataExportImportManager(mockSupabase, mockSupabase);
        let selectedFile = null;
        
        // Add event listeners
        exportImportManager.addEventListener((event) => {
            logActivity(event.event, event.data, getEventType(event.event));
            handleManagerEvent(event);
        });
        
        // Initialize format grid
        function initializeFormatGrid() {
            const formats = exportImportManager.getSupportedFormats();
            const grid = document.getElementById('formatGrid');
            
            Object.entries(formats).forEach(([key, format]) => {
                const card = document.createElement('div');
                card.className = 'format-card';
                card.innerHTML = `
                    <h4>${format.name}</h4>
                    <p><strong>Extension:</strong> ${format.extension}</p>
                    <p><strong>Description:</strong> ${format.description}</p>
                    <p><strong>Features:</strong></p>
                    <ul>
                        <li>Compression: ${format.compression ? '✅' : '❌'}</li>
                        <li>Encryption: ${format.encryption ? '✅' : '❌'}</li>
                    </ul>
                    <div class="compatibility-tags">
                        ${format.compatibility.map(comp => `<span class="tag">${comp}</span>`).join('')}
                    </div>
                `;
                grid.appendChild(card);
            });
        }
        
        // Generate mock backup session data
        function generateMockBackupSession() {
            return {
                id: 'demo-session-001',
                user_id: 'demo-user',
                status: 'completed',
                started_at: new Date(Date.now() - 3600000).toISOString(),
                completed_at: new Date().toISOString(),
                total_items: 2579,
                completed_items: 2579,
                failed_items: 0
            };
        }
        
        // Generate mock backup items
        function generateMockBackupItems() {
            const items = [];
            
            // Generate contacts
            for (let i = 0; i < 150; i++) {
                items.push({
                    id: `contact_${i}`,
                    data_type: 'contacts',
                    data: {
                        name: `Contact ${i + 1}`,
                        phone: `******-${String(i).padStart(4, '0')}`,
                        email: `contact${i + 1}@example.com`,
                        address: `${i + 1} Main St, City, State`
                    },
                    created_at: new Date().toISOString()
                });
            }
            
            // Generate messages
            for (let i = 0; i < 100; i++) {
                items.push({
                    id: `message_${i}`,
                    data_type: 'messages',
                    data: {
                        content: `This is sample message ${i + 1} for testing purposes.`,
                        sender: `Contact ${Math.floor(Math.random() * 150) + 1}`,
                        timestamp: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString(),
                        type: 'text'
                    },
                    created_at: new Date().toISOString()
                });
            }
            
            // Generate photos
            for (let i = 0; i < 89; i++) {
                items.push({
                    id: `photo_${i}`,
                    data_type: 'photos',
                    data: {
                        filename: `photo_${i + 1}.jpg`,
                        data: `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...`, // Truncated base64
                        metadata: {
                            size: Math.floor(Math.random() * 5000000) + 100000,
                            width: 1920,
                            height: 1080,
                            taken_at: new Date(Date.now() - Math.random() * 86400000 * 365).toISOString()
                        }
                    },
                    created_at: new Date().toISOString()
                });
            }
            
            return items;
        }
        
        // Mock the getBackupSessionData method
        exportImportManager.getBackupSessionData = async function(sessionId) {
            const session = generateMockBackupSession();
            const items = generateMockBackupItems();
            
            return {
                session: session,
                contacts: items.filter(item => item.data_type === 'contacts'),
                messages: items.filter(item => item.data_type === 'messages'),
                photos: items.filter(item => item.data_type === 'photos'),
                metadata: {
                    exportedAt: new Date().toISOString(),
                    version: '1.0',
                    totalItems: items.length,
                    dataTypes: ['contacts', 'messages', 'photos']
                }
            };
        };
        
        // Export functions
        async function startExport() {
            const format = document.getElementById('exportFormat').value;
            const pretty = document.getElementById('prettyFormat').checked;
            const encrypt = document.getElementById('encryptExport').checked;
            
            const options = { pretty, encrypt };
            
            showProgress('exportProgress');
            logActivity('export_started', { format, options }, 'info');
            
            try {
                await exportImportManager.exportBackupData('demo-session-001', format, options);
            } catch (error) {
                logActivity('export_failed', { error: error.message }, 'error');
                hideProgress('exportProgress');
            }
        }
        
        function showFormatInfo() {
            const format = document.getElementById('exportFormat').value;
            const formats = exportImportManager.getSupportedFormats();
            const info = formats[format];
            
            if (info) {
                const compatibility = info.compatibility.join(', ');
                alert(`Format: ${info.name}\nDescription: ${info.description}\nCompatibility: ${compatibility}\nCompression: ${info.compression ? 'Yes' : 'No'}\nEncryption: ${info.encryption ? 'Yes' : 'No'}`);
            }
        }
        
        // Import functions
        function handleFileSelect() {
            const fileInput = document.getElementById('importFile');
            selectedFile = fileInput.files[0];
            
            const importBtn = document.getElementById('importBtn');
            const validateBtn = document.getElementById('validateBtn');
            
            if (selectedFile) {
                importBtn.disabled = false;
                validateBtn.disabled = false;
                logActivity('file_selected', { 
                    filename: selectedFile.name, 
                    size: selectedFile.size,
                    type: selectedFile.type 
                }, 'info');
            } else {
                importBtn.disabled = true;
                validateBtn.disabled = true;
            }
        }
        
        async function startImport() {
            if (!selectedFile) return;
            
            showProgress('importProgress');
            logActivity('import_started', { filename: selectedFile.name }, 'info');
            
            try {
                await exportImportManager.importBackupData(selectedFile);
            } catch (error) {
                logActivity('import_failed', { error: error.message }, 'error');
                hideProgress('importProgress');
            }
        }
        
        async function validateImport() {
            if (!selectedFile) return;
            
            logActivity('validation_started', { filename: selectedFile.name }, 'info');
            
            try {
                // Read and parse file for validation
                const content = await readFileContent(selectedFile);
                const format = exportImportManager.detectFormat(selectedFile, content);
                const parsedData = await exportImportManager.parseImportData(content, format);
                const validationResult = await exportImportManager.validateImportData(parsedData);
                
                displayValidationResults(validationResult);
                logActivity('validation_completed', validationResult, validationResult.valid ? 'success' : 'warning');
                
            } catch (error) {
                logActivity('validation_failed', { error: error.message }, 'error');
            }
        }
        
        function readFileContent(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = (e) => reject(new Error('Failed to read file'));
                reader.readAsText(file);
            });
        }
        
        function displayValidationResults(result) {
            const container = document.getElementById('validationResults');
            const className = result.valid ? 'valid' : 'invalid';
            
            let html = `<div class="validation-results ${className}">`;
            html += `<h4>${result.valid ? '✅ Validation Passed' : '❌ Validation Failed'}</h4>`;
            
            if (result.errors.length > 0) {
                html += '<h5>Errors:</h5><ul>';
                result.errors.forEach(error => {
                    html += `<li>${error}</li>`;
                });
                html += '</ul>';
            }
            
            if (result.warnings.length > 0) {
                html += '<h5>Warnings:</h5><ul>';
                result.warnings.forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += '</ul>';
            }
            
            html += '</div>';
            container.innerHTML = html;
        }
        
        // Compatibility testing
        async function testCompatibility() {
            logActivity('compatibility_test_started', {}, 'info');
            
            try {
                const mockData = await exportImportManager.getBackupSessionData('demo-session-001');
                const results = await exportImportManager.testCrossPlatformCompatibility(mockData);
                
                displayCompatibilityResults(results);
                logActivity('compatibility_test_completed', results, 'success');
                
            } catch (error) {
                logActivity('compatibility_test_failed', { error: error.message }, 'error');
            }
        }
        
        function displayCompatibilityResults(results) {
            const container = document.getElementById('compatibilityResults');
            
            let html = '<div style="margin-top: 15px;">';
            
            Object.entries(results).forEach(([platform, result]) => {
                const statusIcon = result.compatible ? '✅' : '❌';
                html += `<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">`;
                html += `<h4>${statusIcon} ${platform}</h4>`;
                
                if (result.issues.length > 0) {
                    html += '<p><strong>Issues:</strong></p><ul>';
                    result.issues.forEach(issue => {
                        html += `<li style="color: #dc3545;">${issue}</li>`;
                    });
                    html += '</ul>';
                }
                
                if (result.recommendations.length > 0) {
                    html += '<p><strong>Recommendations:</strong></p><ul>';
                    result.recommendations.forEach(rec => {
                        html += `<li style="color: #28a745;">${rec}</li>`;
                    });
                    html += '</ul>';
                }
                
                html += '</div>';
            });
            
            html += '</div>';
            container.innerHTML = html;
        }
        
        function showCompatibilityMatrix() {
            const formats = exportImportManager.getSupportedFormats();
            
            let matrix = 'Format Compatibility Matrix:\n\n';
            matrix += 'Format'.padEnd(15) + 'Platforms\n';
            matrix += '-'.repeat(50) + '\n';
            
            Object.entries(formats).forEach(([key, format]) => {
                matrix += format.name.padEnd(15) + format.compatibility.join(', ') + '\n';
            });
            
            alert(matrix);
        }
        
        // Event handling
        function handleManagerEvent(event) {
            switch (event.event) {
                case 'export_started':
                    updateProgress('exportProgress', 0, 'Starting export...');
                    break;
                    
                case 'export_completed':
                    updateProgress('exportProgress', 100, `Export completed: ${event.data.filename}`);
                    setTimeout(() => hideProgress('exportProgress'), 2000);
                    break;
                    
                case 'import_started':
                    updateProgress('importProgress', 0, 'Starting import...');
                    break;
                    
                case 'import_progress':
                    updateProgress('importProgress', event.data.progress, event.data.message);
                    break;
                    
                case 'import_completed':
                    updateProgress('importProgress', 100, `Import completed: ${event.data.itemCount} items`);
                    setTimeout(() => hideProgress('importProgress'), 2000);
                    break;
            }
        }
        
        // UI utility functions
        function showProgress(containerId) {
            document.getElementById(containerId).style.display = 'block';
        }
        
        function hideProgress(containerId) {
            document.getElementById(containerId).style.display = 'none';
        }
        
        function updateProgress(containerId, percentage, message) {
            const fillElement = document.getElementById(containerId.replace('Progress', 'ProgressFill'));
            const textElement = document.getElementById(containerId.replace('Progress', 'ProgressText'));
            
            fillElement.style.width = percentage + '%';
            textElement.textContent = message;
        }
        
        function logActivity(event, data, type = 'info') {
            const log = document.getElementById('activityLog');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            const dataStr = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
            
            entry.innerHTML = `[${timestamp}] <strong>${event}:</strong> ${dataStr}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            const log = document.getElementById('activityLog');
            log.innerHTML = '<div class="log-entry info">Log cleared - Ready for new operations</div>';
        }
        
        function getEventType(event) {
            if (event.includes('failed') || event.includes('error')) return 'error';
            if (event.includes('completed') || event.includes('success')) return 'success';
            if (event.includes('warning')) return 'warning';
            return 'info';
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeFormatGrid();
            logActivity('system_ready', { timestamp: new Date().toISOString() }, 'success');
        });
    </script>
</body>
</html>