-- SafeKeep Modular Pricing Migration Script
-- This script updates the database schema to support the new modular pricing structure
-- Run this script in your Supabase SQL Editor

-- ============================================================================
-- STEP 1: Create new tables for modular pricing structure
-- ============================================================================

-- Create service_types table to define available backup services
CREATE TABLE IF NOT EXISTS public.service_types (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    base_price_cents INTEGER NOT NULL DEFAULT 0,
    storage_limit_gb INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription_plans table for the new modular structure
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_cents INTEGER NOT NULL DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'usd',
    billing_interval VARCHAR(20) NOT NULL DEFAULT 'month',
    total_storage_gb INTEGER NOT NULL DEFAULT 1,
    is_combination BOOLEAN NOT NULL DEFAULT FALSE,
    is_popular BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    stripe_product_id VARCHAR(100),
    stripe_price_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create plan_services junction table to define which services are included in each plan
CREATE TABLE IF NOT EXISTS public.plan_services (
    id SERIAL PRIMARY KEY,
    plan_id VARCHAR(50) NOT NULL,
    service_type_id VARCHAR(50) NOT NULL,
    is_included BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (service_type_id) REFERENCES public.service_types(id) ON DELETE CASCADE,
    UNIQUE(plan_id, service_type_id)
);

-- Create user_service_selections table to track which services each user has selected
CREATE TABLE IF NOT EXISTS public.user_service_selections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    service_type_id VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    activated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deactivated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (service_type_id) REFERENCES public.service_types(id) ON DELETE CASCADE,
    UNIQUE(user_id, service_type_id)
);

-- ============================================================================
-- STEP 2: Add new columns to existing tables for backward compatibility
-- ============================================================================

-- Add new columns to user_subscriptions table
ALTER TABLE public.user_subscriptions 
ADD COLUMN IF NOT EXISTS plan_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS legacy_tier_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS total_price_cents INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS service_combination TEXT; -- JSON array of selected services

-- Add foreign key constraint for plan_id (will be populated later)
-- ALTER TABLE public.user_subscriptions 
-- ADD CONSTRAINT fk_user_subscriptions_plan_id 
-- FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id);

-- Add service-specific usage tracking columns to storage_usage table
ALTER TABLE public.storage_usage 
ADD COLUMN IF NOT EXISTS contacts_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS messages_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS photos_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS contacts_size_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS messages_size_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS photos_size_bytes BIGINT DEFAULT 0;

-- ============================================================================
-- STEP 3: Insert service types
-- ============================================================================

INSERT INTO public.service_types (id, name, description, base_price_cents, storage_limit_gb) VALUES
('contacts', 'Contacts Backup', 'Secure backup and sync of your contacts', 99, 1),
('messages', 'Messages Backup', 'Secure backup and sync of your messages and SMS', 199, 2),
('photos', 'Photos Backup', 'Secure backup and sync of your photos and media', 499, 10)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    base_price_cents = EXCLUDED.base_price_cents,
    storage_limit_gb = EXCLUDED.storage_limit_gb,
    updated_at = NOW();

-- ============================================================================
-- STEP 4: Insert new subscription plans
-- ============================================================================

INSERT INTO public.subscription_plans (id, name, description, price_cents, total_storage_gb, is_combination, is_popular) VALUES
-- Individual Services
('contacts-only', 'Contacts Only', 'Backup and sync your contacts securely', 99, 1, FALSE, FALSE),
('messages-only', 'Messages Only', 'Backup and sync your messages and SMS', 199, 2, FALSE, FALSE),
('photos-only', 'Photos Only', 'Backup and sync your photos and media', 499, 10, FALSE, FALSE),

-- Combination Services
('contacts-messages', 'Contacts + Messages', 'Complete communication backup solution', 249, 3, TRUE, FALSE),
('contacts-photos', 'Contacts + Photos', 'Personal data and memories backup', 549, 11, TRUE, FALSE),
('messages-photos', 'Messages + Photos', 'Communication and media backup', 649, 12, TRUE, TRUE),
('complete-backup', 'Complete Backup', 'All-in-one backup solution for contacts, messages, and photos', 699, 15, TRUE, FALSE)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price_cents = EXCLUDED.price_cents,
    total_storage_gb = EXCLUDED.total_storage_gb,
    is_combination = EXCLUDED.is_combination,
    is_popular = EXCLUDED.is_popular,
    updated_at = NOW();

-- ============================================================================
-- STEP 5: Define which services are included in each plan
-- ============================================================================

INSERT INTO public.plan_services (plan_id, service_type_id, is_included) VALUES
-- Individual Services
('contacts-only', 'contacts', TRUE),
('messages-only', 'messages', TRUE),
('photos-only', 'photos', TRUE),

-- Combination Services
('contacts-messages', 'contacts', TRUE),
('contacts-messages', 'messages', TRUE),
('contacts-photos', 'contacts', TRUE),
('contacts-photos', 'photos', TRUE),
('messages-photos', 'messages', TRUE),
('messages-photos', 'photos', TRUE),
('complete-backup', 'contacts', TRUE),
('complete-backup', 'messages', TRUE),
('complete-backup', 'photos', TRUE)
ON CONFLICT (plan_id, service_type_id) DO UPDATE SET
    is_included = EXCLUDED.is_included;

-- ============================================================================
-- STEP 6: Create indexes for performance
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_service_types_active ON public.service_types(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_active ON public.subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_combination ON public.subscription_plans(is_combination);
CREATE INDEX IF NOT EXISTS idx_plan_services_plan_id ON public.plan_services(plan_id);
CREATE INDEX IF NOT EXISTS idx_plan_services_service_type_id ON public.plan_services(service_type_id);
CREATE INDEX IF NOT EXISTS idx_user_service_selections_user_id ON public.user_service_selections(user_id);
CREATE INDEX IF NOT EXISTS idx_user_service_selections_service_type ON public.user_service_selections(service_type_id);
CREATE INDEX IF NOT EXISTS idx_user_service_selections_active ON public.user_service_selections(is_active);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_plan_id ON public.user_subscriptions(plan_id);

-- ============================================================================
-- STEP 7: Create updated_at triggers for new tables
-- ============================================================================

CREATE TRIGGER update_service_types_updated_at BEFORE UPDATE ON public.service_types
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_plans_updated_at BEFORE UPDATE ON public.subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_service_selections_updated_at BEFORE UPDATE ON public.user_service_selections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- STEP 8: Enable RLS on new tables
-- ============================================================================

ALTER TABLE public.service_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plan_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_service_selections ENABLE ROW LEVEL SECURITY;

-- Service types policies (read-only for users)
CREATE POLICY "Anyone can view active service types" ON public.service_types
    FOR SELECT USING (is_active = TRUE);

-- Subscription plans policies (read-only for users)
CREATE POLICY "Anyone can view active subscription plans" ON public.subscription_plans
    FOR SELECT USING (is_active = TRUE);

-- Plan services policies (read-only for users)
CREATE POLICY "Anyone can view plan services" ON public.plan_services
    FOR SELECT USING (TRUE);

-- User service selections policies
CREATE POLICY "Users can view own service selections" ON public.user_service_selections
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own service selections" ON public.user_service_selections
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own service selections" ON public.user_service_selections
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own service selections" ON public.user_service_selections
    FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- STEP 9: Grant permissions
-- ============================================================================

GRANT SELECT ON public.service_types TO anon, authenticated;
GRANT SELECT ON public.subscription_plans TO anon, authenticated;
GRANT SELECT ON public.plan_services TO anon, authenticated;
GRANT ALL ON public.user_service_selections TO authenticated;
GRANT USAGE ON SEQUENCE public.plan_services_id_seq TO authenticated;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

SELECT 'Modular Pricing Migration Complete!' as status;
SELECT 'New tables created:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('service_types', 'subscription_plans', 'plan_services', 'user_service_selections')
ORDER BY table_name;