// Jest setup file for React Native testing
import 'react-native-get-random-values';

// Mock React Native modules
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android',
    select: jest.fn((obj) => obj.android || obj.default)
  },
  PermissionsAndroid: {
    request: jest.fn().mockResolvedValue('granted'),
    check: jest.fn().mockResolvedValue(true),
    PERMISSIONS: {
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      READ_SMS: 'android.permission.READ_SMS',
      READ_CONTACTS: 'android.permission.READ_CONTACTS',
      CAMERA: 'android.permission.CAMERA'
    },
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied',
      NEVER_ASK_AGAIN: 'never_ask_again'
    }
  },
  Alert: {
    alert: jest.fn()
  },
  Linking: {
    openURL: jest.fn().mockResolvedValue(true),
    canOpenURL: jest.fn().mockResolvedValue(true)
  },
  Dimensions: {
    get: jest.fn().mockReturnValue({ width: 375, height: 667 })
  },
  StyleSheet: {
    create: jest.fn((styles) => styles),
    flatten: jest.fn((style) => style)
  },
  View: 'View',
  Text: 'Text',
  TouchableOpacity: 'TouchableOpacity',
  ScrollView: 'ScrollView',
  FlatList: 'FlatList',
  Image: 'Image',
  TextInput: 'TextInput'
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn().mockResolvedValue(null),
  setItem: jest.fn().mockResolvedValue(),
  removeItem: jest.fn().mockResolvedValue(),
  clear: jest.fn().mockResolvedValue(),
  getAllKeys: jest.fn().mockResolvedValue([]),
  multiGet: jest.fn().mockResolvedValue([]),
  multiSet: jest.fn().mockResolvedValue(),
  multiRemove: jest.fn().mockResolvedValue()
}));

// Mock Camera Roll
jest.mock('@react-native-camera-roll/camera-roll', () => ({
  CameraRoll: {
    getPhotos: jest.fn().mockResolvedValue({
      edges: []
    }),
    save: jest.fn().mockResolvedValue('saved_uri')
  }
}));

// Mock React Native FS
jest.mock('react-native-fs', () => ({
  DocumentDirectoryPath: '/mock/documents',
  CachesDirectoryPath: '/mock/caches',
  readFile: jest.fn().mockResolvedValue('mock_file_content'),
  writeFile: jest.fn().mockResolvedValue(),
  readDir: jest.fn().mockResolvedValue([]),
  stat: jest.fn().mockResolvedValue({
    size: 1024,
    mtime: new Date(),
    isFile: () => true,
    isDirectory: () => false
  }),
  exists: jest.fn().mockResolvedValue(true),
  unlink: jest.fn().mockResolvedValue(),
  mkdir: jest.fn().mockResolvedValue(),
  read: jest.fn().mockResolvedValue('base64_data')
}));

// Mock React Native Contacts
jest.mock('react-native-contacts', () => ({
  getAll: jest.fn().mockResolvedValue([]),
  getContactById: jest.fn().mockResolvedValue(null),
  checkPermission: jest.fn().mockResolvedValue('authorized'),
  requestPermission: jest.fn().mockResolvedValue('authorized')
}));

// Mock React Native Keychain
jest.mock('react-native-keychain', () => ({
  setInternetCredentials: jest.fn().mockResolvedValue(),
  getInternetCredentials: jest.fn().mockResolvedValue({
    username: 'test_user',
    password: 'test_password'
  }),
  resetInternetCredentials: jest.fn().mockResolvedValue()
}));

// Mock React Native Permissions
jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    ANDROID: {
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      READ_SMS: 'android.permission.READ_SMS',
      READ_CONTACTS: 'android.permission.READ_CONTACTS',
      CAMERA: 'android.permission.CAMERA'
    },
    IOS: {
      PHOTO_LIBRARY: 'ios.permission.PHOTO_LIBRARY',
      CONTACTS: 'ios.permission.CONTACTS'
    }
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
    BLOCKED: 'blocked',
    UNAVAILABLE: 'unavailable'
  },
  request: jest.fn().mockResolvedValue('granted'),
  check: jest.fn().mockResolvedValue('granted')
}));

// Mock React Native Vector Icons
jest.mock('react-native-vector-icons/MaterialIcons', () => 'Icon');
jest.mock('react-native-vector-icons/Ionicons', () => 'Icon');

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    reset: jest.fn()
  }),
  useRoute: () => ({
    params: {}
  }),
  useFocusEffect: jest.fn()
}));

// Mock Redux
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => jest.fn(),
  Provider: ({ children }) => children
}));

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      signUp: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      signIn: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      signOut: jest.fn().mockResolvedValue({ error: null }),
      getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      onAuthStateChange: jest.fn(() => ({ data: { subscription: { unsubscribe: jest.fn() } } }))
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: null, error: null }),
      then: jest.fn().mockResolvedValue({ data: [], error: null })
    })),
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn().mockResolvedValue({ data: null, error: null }),
        download: jest.fn().mockResolvedValue({ data: null, error: null }),
        remove: jest.fn().mockResolvedValue({ data: null, error: null }),
        list: jest.fn().mockResolvedValue({ data: [], error: null })
      }))
    }
  }))
}));

// Mock Crypto JS with proper hash-like outputs
jest.mock('crypto-js', () => {
  const actualCrypto = jest.requireActual('crypto-js');
  
  return {
    ...actualCrypto,
    AES: {
      encrypt: jest.fn().mockImplementation((data, key, options) => ({
        toString: () => 'U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K14='
      })),
      decrypt: jest.fn().mockImplementation((encryptedData, key, options) => ({
        toString: (encoding) => {
          if (encoding && encoding.stringify) {
            return 'decrypted_data';
          }
          return 'decrypted_data';
        }
      }))
    },
    enc: {
      Utf8: {
        parse: actualCrypto.enc.Utf8.parse,
        stringify: actualCrypto.enc.Utf8.stringify
      },
      Base64: {
        parse: actualCrypto.enc.Base64.parse,
        stringify: actualCrypto.enc.Base64.stringify
      },
      Hex: actualCrypto.enc.Hex
    },
    SHA256: jest.fn().mockImplementation((data) => ({
      toString: (encoding) => {
        // Generate different hashes based on input to simulate real behavior
        const input = data.toString ? data.toString() : String(data);
        let hash = 0;
        for (let i = 0; i < input.length; i++) {
          const char = input.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32bit integer
        }
        // Convert to positive hex and pad to 64 characters
        const hexHash = Math.abs(hash).toString(16).padStart(16, '0');
        return hexHash.repeat(4).substring(0, 64);
      }
    })),
    SHA512: jest.fn().mockImplementation((data) => ({
      toString: (encoding) => {
        // Return a proper 128-character hex hash
        return 'b109f3bbbc244eb82441917ed06d618b9008dd09b3befd1b5e07394c706a8bb980b1d7785e5976ec049b46df5f1326af5a2ea6d103fd07c95385ffab0cacbc86';
      }
    })),
    MD5: jest.fn().mockImplementation((data) => ({
      toString: (encoding) => {
        // Return a proper 32-character hex hash
        return '5d41402abc4b2a76b9719d911017c592';
      }
    })),
    PBKDF2: actualCrypto.PBKDF2,
    lib: {
      WordArray: {
        random: jest.fn().mockImplementation((bytes) => ({
          toString: (encoding) => {
            if (encoding === actualCrypto.enc.Base64) {
              return 'dGVzdF9yYW5kb21fYnl0ZXM=';
            }
            return 'test_random_bytes';
          }
        })),
        create: actualCrypto.lib.WordArray.create
      }
    },
    mode: actualCrypto.mode,
    pad: actualCrypto.pad,
    algo: actualCrypto.algo
  };
});

// Global test utilities
global.mockAsyncStorage = {
  store: {},
  getItem: jest.fn((key) => Promise.resolve(global.mockAsyncStorage.store[key] || null)),
  setItem: jest.fn((key, value) => {
    global.mockAsyncStorage.store[key] = value;
    return Promise.resolve();
  }),
  removeItem: jest.fn((key) => {
    delete global.mockAsyncStorage.store[key];
    return Promise.resolve();
  }),
  clear: jest.fn(() => {
    global.mockAsyncStorage.store = {};
    return Promise.resolve();
  })
};

// Console suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  // Suppress React Native warnings in tests
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning:') || 
       args[0].includes('React Native') ||
       args[0].includes('VirtualizedList'))
    ) {
      return;
    }
    originalConsoleError.call(console, ...args);
  };

  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning:') || 
       args[0].includes('React Native') ||
       args[0].includes('componentWillReceiveProps'))
    ) {
      return;
    }
    originalConsoleWarn.call(console, ...args);
  };
});

afterEach(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  jest.clearAllMocks();
});

// Increase timeout for integration tests
jest.setTimeout(30000);