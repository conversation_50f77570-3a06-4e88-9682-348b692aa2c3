<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep Stripe Payment Test</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .plan-card {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .plan-price {
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        .plan-features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .plan-features li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        .plan-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }
        .payment-form {
            background: #f9f9f9;
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        #card-element {
            background: white;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            margin: 10px 0;
        }
        .button {
            background-color: #4A90E2;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            margin: 10px 0;
            transition: background-color 0.3s;
        }
        .button:hover:not(:disabled) {
            background-color: #357abd;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .test-cards {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .test-cards h4 {
            margin-top: 0;
            color: #1976d2;
        }
        .card-number {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            border: 1px solid #ddd;
        }
        .result {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        .result.success {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .result.error {
            background-color: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4A90E2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .security-note {
            background: #fff3e0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ff9800;
            margin: 20px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💳 SafeKeep Payment Test</h1>
            <p>Test your Stripe integration with real payment processing</p>
        </div>

        <!-- Plan Display -->
        <div class="plan-card">
            <h2>SafeKeep Premium</h2>
            <div class="plan-price">$9.99<span style="font-size: 0.4em;">/month</span></div>
            <ul class="plan-features">
                <li>50GB Secure Cloud Storage</li>
                <li>Advanced Backup Features</li>
                <li>Priority Support</li>
                <li>Family Sharing (up to 6 users)</li>
                <li>End-to-End Encryption</li>
            </ul>
        </div>

        <!-- Test Cards Info -->
        <div class="test-cards">
            <h4>🧪 Test Card Numbers</h4>
            <div><strong>✅ Successful Payment:</strong></div>
            <div class="card-number">4242 4242 4242 4242</div>
            
            <div style="margin-top: 10px;"><strong>❌ Declined Payment:</strong></div>
            <div class="card-number">4000 0000 0000 0002</div>
            
            <div style="margin-top: 10px;"><strong>💳 Other Details:</strong></div>
            <div>• Expiry: Any future date (e.g., 12/25)</div>
            <div>• CVC: Any 3 digits (e.g., 123)</div>
            <div>• ZIP: Any 5 digits (e.g., 12345)</div>
        </div>

        <!-- Payment Form -->
        <form id="payment-form" class="payment-form">
            <h3>💳 Payment Information</h3>
            
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" value="<EMAIL>" style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px;">
            </div>

            <div class="form-group">
                <label for="card-element">Card Details</label>
                <div id="card-element">
                    <!-- Stripe Elements will create form elements here -->
                </div>
                <div id="card-errors" role="alert" style="color: #f44336; margin-top: 10px;"></div>
            </div>

            <button type="submit" id="submit-button" class="button">
                <span id="button-text">Subscribe for $9.99/month</span>
                <span id="loading" class="loading" style="display: none;"></span>
            </button>
        </form>

        <!-- Results -->
        <div id="payment-result" class="result"></div>

        <!-- Security Note -->
        <div class="security-note">
            <strong>🔒 Security Note:</strong> This is a test environment using Stripe's test mode. 
            No real money will be charged. Your actual payment information is never stored on our servers.
        </div>
    </div>

    <script>
        // Initialize Stripe with your publishable key
        // Replace with your actual publishable key from the .env file
        const stripe = Stripe('pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP');
        
        const elements = stripe.elements();
        const cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
        });

        cardElement.mount('#card-element');

        // Handle real-time validation errors from the card Element
        cardElement.on('change', ({error}) => {
            const displayError = document.getElementById('card-errors');
            if (error) {
                displayError.textContent = error.message;
            } else {
                displayError.textContent = '';
            }
        });

        // Handle form submission
        const form = document.getElementById('payment-form');
        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            const submitButton = document.getElementById('submit-button');
            const buttonText = document.getElementById('button-text');
            const loading = document.getElementById('loading');
            const resultDiv = document.getElementById('payment-result');

            // Show loading state
            submitButton.disabled = true;
            buttonText.style.display = 'none';
            loading.style.display = 'inline-block';
            resultDiv.style.display = 'none';

            try {
                // Step 1: Create payment intent on your backend
                console.log('🔗 Creating payment intent...');
                
                const response = await fetch('http://localhost:3000/api/create-payment-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: 999, // $9.99 in cents
                        currency: 'usd',
                        description: 'SafeKeep Premium Subscription'
                    })
                });

                const { client_secret, payment_intent_id } = await response.json();

                if (!client_secret) {
                    throw new Error('Failed to create payment intent');
                }

                console.log('✅ Payment intent created:', payment_intent_id);

                // Step 2: Confirm payment with Stripe
                console.log('💳 Confirming payment...');
                
                const {error, paymentIntent} = await stripe.confirmCardPayment(client_secret, {
                    payment_method: {
                        card: cardElement,
                        billing_details: {
                            email: document.getElementById('email').value,
                        },
                    }
                });

                if (error) {
                    // Payment failed
                    console.error('❌ Payment failed:', error);
                    showResult('error', `
                        <h3>❌ Payment Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Please try again with a different card or check your card details.</p>
                    `);
                } else {
                    // Payment succeeded
                    console.log('🎉 Payment successful:', paymentIntent);
                    showResult('success', `
                        <h3>🎉 Payment Successful!</h3>
                        <p><strong>Payment ID:</strong> ${paymentIntent.id}</p>
                        <p><strong>Amount:</strong> $${(paymentIntent.amount / 100).toFixed(2)}</p>
                        <p><strong>Status:</strong> ${paymentIntent.status}</p>
                        <p>Welcome to SafeKeep Premium! Your subscription is now active.</p>
                    `);
                }

            } catch (error) {
                console.error('💥 Unexpected error:', error);
                showResult('error', `
                    <h3>💥 Unexpected Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Please make sure your backend server is running and try again.</p>
                `);
            } finally {
                // Reset button state
                submitButton.disabled = false;
                buttonText.style.display = 'inline';
                loading.style.display = 'none';
            }
        });

        function showResult(type, html) {
            const resultDiv = document.getElementById('payment-result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
            
            // Scroll to result
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // Test backend connection on page load
        window.onload = async function() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                if (data.status === 'OK' && data.stripe_configured) {
                    console.log('✅ Backend connected and Stripe configured');
                } else {
                    console.warn('⚠️ Backend or Stripe configuration issue');
                }
            } catch (error) {
                console.error('❌ Cannot connect to backend:', error);
                showResult('error', `
                    <h3>❌ Backend Connection Failed</h3>
                    <p>Cannot connect to the test server. Please make sure:</p>
                    <ol>
                        <li>Run <code>node test-server.js</code> in the SafeKeeping directory</li>
                        <li>Server is running on http://localhost:3000</li>
                        <li>No firewall is blocking the connection</li>
                    </ol>
                `);
            }
        };
    </script>
</body>
</html>
