import { supabase } from '../src/config/supabase';
import RecoveryService from '../src/services/RecoveryService';
import AuthService from '../src/services/AuthService';

jest.mock('../src/config/supabase');
jest.mock('../src/services/AuthService');

const mockSelect = jest.fn();
supabase.from = jest.fn(() => ({
  select: mockSelect,
}));

mockSelect.mockReturnValue({
  eq: jest.fn().mockReturnValue({
    in: jest.fn().mockReturnValue({
      order: jest.fn().mockReturnValue({
        then: jest.fn().mockImplementation((callback) => callback({ data: [], error: null })),
      }),
    }),
  }),
});

AuthService.getCurrentUser = jest.fn(() => ({ id: 'test-user-id' }));

describe('RecoveryService', () => {
  describe('getFilesToRestore', () => {
    it('should retrieve files to restore based on selected categories', async () => {
      const options = { categories: { photo: { selected: true }, contact: { selected: false } } };
      const files = await RecoveryService.getFilesToRestore(options);

      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(mockSelect).toHaveBeenCalledWith('*');
      expect(files).toEqual([]);
    });

    it('should handle errors gracefully', async () => {
      mockSelect.mockReturnValueOnce({
        eq: jest.fn().mockReturnValue({
          in: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              then: jest.fn().mockImplementation((callback) => callback({ data: null, error: new Error('Query failed') })),
            }),
          }),
        }),
      });

      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const options = { categories: { photo: { selected: true }, contact: { selected: false } } };
      const files = await RecoveryService.getFilesToRestore(options);

      expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching files to restore:', expect.any(Error));

      consoleErrorSpy.mockRestore();
    });
  });
});
