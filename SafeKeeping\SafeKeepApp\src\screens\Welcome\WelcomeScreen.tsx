import React from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Image,
} from 'react-native';
import {
  Button,
  Card,
  Text,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { COLORS, SPACING } from '../../utils/constants';
import { RootStackParamList } from '../../navigation/AppNavigator';

type WelcomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const WelcomeScreen = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();

  const handleSignIn = () => {
    navigation.navigate('Auth');
  };

  const handleSignUp = () => {
    navigation.navigate('Auth');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.logoContainer}>
            <Icon name="shield-lock" size={80} color={COLORS.primary} />
            <Text variant="headlineLarge" style={styles.appName}>
              SafeKeep
            </Text>
            <Text variant="titleMedium" style={styles.tagline}>
              Your Precious Data, Perfectly Protected
            </Text>
          </View>

          <Text variant="bodyLarge" style={styles.description}>
            Securely backup your photos, contacts, and messages with military-grade encryption. 
            Designed for everyone, especially grandparents and families.
          </Text>
        </View>

        {/* Features Preview */}
        <Card style={styles.featuresCard}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.featuresTitle}>
              Why Choose SafeKeep?
            </Text>
            
            <View style={styles.featuresList}>
              <View style={styles.feature}>
                <Icon name="camera" size={32} color={COLORS.primary} />
                <View style={styles.featureText}>
                  <Text variant="titleMedium" style={styles.featureTitle}>
                    Photo Backup
                  </Text>
                  <Text style={styles.featureDescription}>
                    Automatically backup all your precious photos and videos
                  </Text>
                </View>
              </View>

              <View style={styles.feature}>
                <Icon name="contacts" size={32} color={COLORS.primary} />
                <View style={styles.featureText}>
                  <Text variant="titleMedium" style={styles.featureTitle}>
                    Contact Protection
                  </Text>
                  <Text style={styles.featureDescription}>
                    Never lose important phone numbers and contact information
                  </Text>
                </View>
              </View>

              <View style={styles.feature}>
                <Icon name="message-text" size={32} color={COLORS.primary} />
                <View style={styles.featureText}>
                  <Text variant="titleMedium" style={styles.featureTitle}>
                    Message History
                  </Text>
                  <Text style={styles.featureDescription}>
                    Keep your important conversations safe and searchable
                  </Text>
                </View>
              </View>

              <View style={styles.feature}>
                <Icon name="shield-check" size={32} color={COLORS.success} />
                <View style={styles.featureText}>
                  <Text variant="titleMedium" style={styles.featureTitle}>
                    Military-Grade Security
                  </Text>
                  <Text style={styles.featureDescription}>
                    End-to-end encryption ensures only you can access your data
                  </Text>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <Text variant="titleMedium" style={styles.actionTitle}>
            Get Started Today
          </Text>
          
          <Button
            mode="contained"
            onPress={handleSignUp}
            style={styles.signUpButton}
            contentStyle={styles.buttonContent}
            icon="account-plus"
          >
            Create New Account
          </Button>

          <Button
            mode="outlined"
            onPress={handleSignIn}
            style={styles.signInButton}
            contentStyle={styles.buttonContent}
            icon="login"
          >
            Sign In to Existing Account
          </Button>

          <Text style={styles.subscriptionNote}>
            💳 New users can choose from flexible subscription plans starting at $2.99/month
          </Text>
        </View>

        {/* Trust Indicators */}
        <Card style={styles.trustCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.trustTitle}>
              🔐 Trusted by Families Worldwide
            </Text>
            
            <View style={styles.trustIndicators}>
              <View style={styles.trustItem}>
                <Icon name="shield-check" size={24} color={COLORS.success} />
                <Text style={styles.trustText}>AES-256 Encryption</Text>
              </View>
              
              <View style={styles.trustItem}>
                <Icon name="cloud-lock" size={24} color={COLORS.success} />
                <Text style={styles.trustText}>Secure Cloud Storage</Text>
              </View>
              
              <View style={styles.trustItem}>
                <Icon name="heart" size={24} color={COLORS.success} />
                <Text style={styles.trustText}>Family-Friendly Design</Text>
              </View>
              
              <View style={styles.trustItem}>
                <Icon name="headset" size={24} color={COLORS.success} />
                <Text style={styles.trustText}>24/7 Support</Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  heroSection: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
    paddingVertical: SPACING.xl,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  appName: {
    color: COLORS.text,
    fontWeight: 'bold',
    marginTop: SPACING.md,
  },
  tagline: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
  description: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: SPACING.md,
  },
  featuresCard: {
    marginBottom: SPACING.xl,
    elevation: 4,
  },
  featuresTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  featuresList: {
    gap: SPACING.lg,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: SPACING.md,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  featureDescription: {
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  actionSection: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  actionTitle: {
    color: COLORS.text,
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  signUpButton: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.md,
    width: '100%',
  },
  signInButton: {
    borderColor: COLORS.primary,
    marginBottom: SPACING.md,
    width: '100%',
  },
  buttonContent: {
    height: 50,
  },
  subscriptionNote: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
    paddingHorizontal: SPACING.md,
  },
  trustCard: {
    elevation: 4,
    marginBottom: SPACING.xl,
  },
  trustTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  trustIndicators: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: SPACING.md,
  },
  trustItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
    width: '48%',
  },
  trustText: {
    color: COLORS.textSecondary,
    fontSize: 14,
    flex: 1,
  },
});

export default WelcomeScreen;
