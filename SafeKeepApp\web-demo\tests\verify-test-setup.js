#!/usr/bin/env node

/**
 * Test Setup Verification
 * Verifies that all test dependencies and configurations are properly set up
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class TestSetupVerifier {
    constructor() {
        this.checks = [];
        this.errors = [];
        this.warnings = [];
    }

    async verify() {
        console.log('🔍 Verifying SafeKeep Web Demo Test Setup');
        console.log('==========================================');

        await this.checkNodeVersion();
        await this.checkDependencies();
        await this.checkTestFiles();
        await this.checkReportsDirectory();
        await this.checkServerAvailability();
        await this.checkBrowserSupport();
        await this.checkEnvironmentVariables();

        this.generateReport();
    }

    async checkNodeVersion() {
        console.log('\n📦 Checking Node.js version...');
        
        try {
            const nodeVersion = process.version;
            const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
            
            if (majorVersion >= 14) {
                this.addCheck('✅ Node.js version', `${nodeVersion} (supported)`);
            } else {
                this.addError('❌ Node.js version', `${nodeVersion} (requires Node.js 14+)`);
            }
        } catch (error) {
            this.addError('❌ Node.js version check failed', error.message);
        }
    }

    async checkDependencies() {
        console.log('\n📚 Checking dependencies...');
        
        const packageJsonPath = path.join(__dirname, '../package.json');
        
        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            const requiredDeps = ['puppeteer', 'playwright', 'jest', 'supertest', 'stripe'];
            const devDeps = packageJson.devDependencies || {};
            
            requiredDeps.forEach(dep => {
                if (devDeps[dep]) {
                    this.addCheck('✅ Dependency', `${dep}@${devDeps[dep]}`);
                } else {
                    this.addError('❌ Missing dependency', dep);
                }
            });

            // Check if node_modules exists
            const nodeModulesPath = path.join(__dirname, '../node_modules');
            if (fs.existsSync(nodeModulesPath)) {
                this.addCheck('✅ Dependencies installed', 'node_modules directory exists');
            } else {
                this.addError('❌ Dependencies not installed', 'Run npm install');
            }

        } catch (error) {
            this.addError('❌ Package.json check failed', error.message);
        }
    }

    async checkTestFiles() {
        console.log('\n📄 Checking test files...');
        
        const testFiles = [
            'integration-test-runner.js',
            'e2e-test-runner.js',
            'stripe-integration-tests.js',
            'subscription-lifecycle-tests.js',
            'performance-benchmark-tests.js',
            'cross-browser-tests.js',
            'user-journey-tests.js',
            'run-all-tests.js'
        ];

        testFiles.forEach(file => {
            const filePath = path.join(__dirname, file);
            if (fs.existsSync(filePath)) {
                this.addCheck('✅ Test file', file);
            } else {
                this.addError('❌ Missing test file', file);
            }
        });
    }

    async checkReportsDirectory() {
        console.log('\n📊 Checking reports directory...');
        
        const reportsDir = path.join(__dirname, '../test-reports');
        
        if (!fs.existsSync(reportsDir)) {
            try {
                fs.mkdirSync(reportsDir, { recursive: true });
                this.addCheck('✅ Reports directory', 'Created test-reports directory');
            } catch (error) {
                this.addError('❌ Reports directory', `Cannot create: ${error.message}`);
            }
        } else {
            this.addCheck('✅ Reports directory', 'test-reports directory exists');
        }

        // Check write permissions
        try {
            const testFile = path.join(reportsDir, 'test-write.tmp');
            fs.writeFileSync(testFile, 'test');
            fs.unlinkSync(testFile);
            this.addCheck('✅ Reports directory writable', 'Write permissions verified');
        } catch (error) {
            this.addError('❌ Reports directory not writable', error.message);
        }
    }

    async checkServerAvailability() {
        console.log('\n🌐 Checking server availability...');
        
        const servers = [
            { name: 'Demo Server', url: 'http://localhost:3000', required: true },
            { name: 'WebSocket Server', url: 'ws://localhost:8080', required: false }
        ];

        for (const server of servers) {
            try {
                const isAvailable = await this.checkServerConnection(server.url);
                if (isAvailable) {
                    this.addCheck('✅ Server available', server.name);
                } else if (server.required) {
                    this.addError('❌ Server unavailable', `${server.name} - Start with 'npm run start:both'`);
                } else {
                    this.addWarning('⚠️ Server unavailable', `${server.name} - Some tests may fail`);
                }
            } catch (error) {
                if (server.required) {
                    this.addError('❌ Server check failed', `${server.name}: ${error.message}`);
                } else {
                    this.addWarning('⚠️ Server check failed', `${server.name}: ${error.message}`);
                }
            }
        }
    }

    async checkServerConnection(url) {
        return new Promise((resolve) => {
            if (url.startsWith('ws://')) {
                // WebSocket check would require ws library
                resolve(false);
                return;
            }

            const http = require('http');
            const urlObj = new URL(url);
            
            const req = http.request({
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: '/',
                method: 'GET',
                timeout: 3000
            }, (res) => {
                resolve(res.statusCode < 400);
            });

            req.on('error', () => resolve(false));
            req.on('timeout', () => resolve(false));
            req.end();
        });
    }

    async checkBrowserSupport() {
        console.log('\n🌐 Checking browser support...');
        
        const browsers = ['chromium', 'firefox', 'webkit'];
        
        for (const browser of browsers) {
            try {
                // This is a simplified check - in reality, we'd try to launch the browser
                this.addCheck('✅ Browser support', `${browser} (assumed available)`);
            } catch (error) {
                this.addWarning('⚠️ Browser check', `${browser}: ${error.message}`);
            }
        }

        this.addWarning('ℹ️ Browser installation', 'Run "npx playwright install" if browsers are missing');
    }

    async checkEnvironmentVariables() {
        console.log('\n🔧 Checking environment variables...');
        
        const envVars = [
            { name: 'STRIPE_SECRET_KEY', required: false, description: 'For Stripe integration tests' },
            { name: 'STRIPE_PUBLISHABLE_KEY', required: false, description: 'For Stripe integration tests' },
            { name: 'NODE_ENV', required: false, description: 'Environment setting' }
        ];

        envVars.forEach(envVar => {
            const value = process.env[envVar.name];
            if (value) {
                const maskedValue = envVar.name.includes('KEY') ? 
                    `${value.substring(0, 8)}...` : value;
                this.addCheck('✅ Environment variable', `${envVar.name}=${maskedValue}`);
            } else if (envVar.required) {
                this.addError('❌ Missing required env var', `${envVar.name}: ${envVar.description}`);
            } else {
                this.addWarning('⚠️ Optional env var not set', `${envVar.name}: ${envVar.description}`);
            }
        });
    }

    addCheck(status, message) {
        this.checks.push({ status, message });
        console.log(`  ${status} ${message}`);
    }

    addError(status, message) {
        this.errors.push({ status, message });
        console.log(`  ${status} ${message}`);
    }

    addWarning(status, message) {
        this.warnings.push({ status, message });
        console.log(`  ${status} ${message}`);
    }

    generateReport() {
        console.log('\n📋 Verification Summary');
        console.log('=======================');
        console.log(`Total Checks: ${this.checks.length}`);
        console.log(`Errors: ${this.errors.length}`);
        console.log(`Warnings: ${this.warnings.length}`);

        if (this.errors.length > 0) {
            console.log('\n❌ Errors that need to be fixed:');
            this.errors.forEach(error => {
                console.log(`  ${error.status} ${error.message}`);
            });
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️ Warnings (tests may still work):');
            this.warnings.forEach(warning => {
                console.log(`  ${warning.status} ${warning.message}`);
            });
        }

        console.log('\n🎯 Next Steps:');
        if (this.errors.length === 0) {
            console.log('✅ Setup verification passed! You can run tests with:');
            console.log('   npm run test:all');
            console.log('   npm run test:integration');
            console.log('   npm run test:e2e');
        } else {
            console.log('❌ Please fix the errors above before running tests');
            console.log('💡 Common fixes:');
            console.log('   - Run: npm install');
            console.log('   - Start servers: npm run start:both');
            console.log('   - Install browsers: npx playwright install');
        }

        // Save verification report
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalChecks: this.checks.length,
                errors: this.errors.length,
                warnings: this.warnings.length,
                passed: this.errors.length === 0
            },
            checks: this.checks,
            errors: this.errors,
            warnings: this.warnings
        };

        const reportsDir = path.join(__dirname, '../test-reports');
        if (fs.existsSync(reportsDir)) {
            fs.writeFileSync(
                path.join(reportsDir, 'setup-verification-report.json'),
                JSON.stringify(report, null, 2)
            );
            console.log('\n📄 Verification report saved to test-reports/setup-verification-report.json');
        }

        process.exit(this.errors.length === 0 ? 0 : 1);
    }
}

// Run verification if called directly
if (require.main === module) {
    const verifier = new TestSetupVerifier();
    verifier.verify().catch(error => {
        console.error('❌ Verification failed:', error);
        process.exit(1);
    });
}

module.exports = TestSetupVerifier;