/**
 * Service Worker for SafeKeep Web Demo PWA
 * Handles caching, offline functionality, and background sync
 */

const CACHE_NAME = 'safekeep-demo-v1.0.0';
const STATIC_CACHE = 'safekeep-static-v1.0.0';
const DYNAMIC_CACHE = 'safekeep-dynamic-v1.0.0';

// Files to cache immediately
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/enhanced-ui-framework.css',
    '/theme-manager.js',
    '/responsive-layout-manager.js',
    '/accessibility-manager.js',
    '/pwa-manager.js',
    '/app.js',
    '/auth-manager.js',
    '/realtime-progress-manager.js',
    '/backup-console.js',
    '/encryption-manager.js',
    '/encryption-demo.js',
    '/backup-history-manager.js',
    '/backup-history-dashboard.js',
    '/restore-manager.js',
    '/restore-simulation.js',
    '/schedule-manager.js',
    '/schedule-console.js',
    '/stripe-manager.js',
    '/payment-processor.js',
    '/subscription-tier-config.js',
    '/premium-features-showcase.js',
    '/subscription-management-dashboard.js',
    '/payment-interface-styles.css',
    '/subscription-management-styles.css'
];

// Network-first resources (always try network first)
const NETWORK_FIRST = [
    '/api/',
    'https://api.stripe.com/',
    'https://unpkg.com/',
    'https://cdn.jsdelivr.net/',
    'https://js.stripe.com/'
];

// Cache-first resources (try cache first, fallback to network)
const CACHE_FIRST = [
    '/static/',
    '/images/',
    '/icons/',
    '.css',
    '.js',
    '.woff',
    '.woff2',
    '.ttf',
    '.eot'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Caching static assets...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Static assets cached successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Failed to cache static assets:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE && 
                            cacheName !== DYNAMIC_CACHE && 
                            cacheName !== CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    // Determine caching strategy based on URL
    if (isNetworkFirst(request.url)) {
        event.respondWith(networkFirst(request));
    } else if (isCacheFirst(request.url)) {
        event.respondWith(cacheFirst(request));
    } else {
        event.respondWith(staleWhileRevalidate(request));
    }
});

// Network-first strategy
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', request.url);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline fallback
        return getOfflineFallback(request);
    }
}

// Cache-first strategy
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network and cache failed for:', request.url);
        return getOfflineFallback(request);
    }
}

// Stale-while-revalidate strategy
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    // Fetch from network in background
    const networkResponsePromise = fetch(request)
        .then((networkResponse) => {
            if (networkResponse.ok) {
                cache.put(request, networkResponse.clone());
            }
            return networkResponse;
        })
        .catch((error) => {
            console.log('Background fetch failed:', request.url, error);
        });
    
    // Return cached version immediately if available
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // Otherwise wait for network
    try {
        return await networkResponsePromise;
    } catch (error) {
        return getOfflineFallback(request);
    }
}

// Check if URL should use network-first strategy
function isNetworkFirst(url) {
    return NETWORK_FIRST.some(pattern => url.includes(pattern));
}

// Check if URL should use cache-first strategy
function isCacheFirst(url) {
    return CACHE_FIRST.some(pattern => url.includes(pattern));
}

// Get offline fallback response
function getOfflineFallback(request) {
    const url = new URL(request.url);
    
    // HTML requests - return cached index.html or offline page
    if (request.headers.get('accept')?.includes('text/html')) {
        return caches.match('/index.html').then(response => {
            if (response) {
                // Notify client about offline fallback
                notifyClients('OFFLINE_FALLBACK', { url: request.url });
                return response;
            }
            
            // Return basic offline response
            return new Response(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>SafeKeep - Offline</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { 
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            min-height: 100vh;
                            margin: 0;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            text-align: center;
                            padding: 20px;
                        }
                        .offline-container {
                            max-width: 400px;
                            background: rgba(255, 255, 255, 0.1);
                            padding: 40px;
                            border-radius: 20px;
                            backdrop-filter: blur(10px);
                        }
                        h1 { margin-bottom: 20px; }
                        p { margin-bottom: 15px; line-height: 1.6; }
                        button {
                            background: white;
                            color: #667eea;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            font-weight: 600;
                            cursor: pointer;
                            margin-top: 20px;
                        }
                        button:hover { opacity: 0.9; }
                    </style>
                </head>
                <body>
                    <div class="offline-container">
                        <h1>You're Offline</h1>
                        <p>SafeKeep is currently unavailable, but don't worry!</p>
                        <p>Your data is safe and the app will work normally once you're back online.</p>
                        <button onclick="window.location.reload()">Try Again</button>
                    </div>
                </body>
                </html>
            `, {
                headers: { 'Content-Type': 'text/html' }
            });
        });
    }
    
    // API requests - return offline response
    if (url.pathname.startsWith('/api/')) {
        return new Response(JSON.stringify({
            error: 'Offline',
            message: 'This request is not available offline',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
    
    // Other requests - return generic offline response
    return new Response('Offline', { status: 503 });
}

// Background sync event
self.addEventListener('sync', (event) => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(handleBackgroundSync());
    }
});

// Handle background sync
async function handleBackgroundSync() {
    try {
        // Get pending data from IndexedDB or other storage
        const pendingData = await getPendingData();
        
        if (pendingData.length > 0) {
            console.log('Processing', pendingData.length, 'pending items');
            
            for (const item of pendingData) {
                try {
                    await processPendingItem(item);
                    await removePendingItem(item.id);
                } catch (error) {
                    console.error('Failed to process pending item:', error);
                }
            }
            
            // Notify clients about successful sync
            notifyClients('BACKGROUND_SYNC', { 
                success: true, 
                processed: pendingData.length 
            });
        }
    } catch (error) {
        console.error('Background sync failed:', error);
        
        notifyClients('BACKGROUND_SYNC', { 
            success: false, 
            error: error.message 
        });
    }
}

// Get pending data (placeholder - implement with IndexedDB)
async function getPendingData() {
    // This would typically use IndexedDB to store pending data
    // For demo purposes, return empty array
    return [];
}

// Process pending item (placeholder)
async function processPendingItem(item) {
    // Process individual pending items
    console.log('Processing pending item:', item);
    
    // Make network request to sync data
    const response = await fetch('/api/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(item)
    });
    
    if (!response.ok) {
        throw new Error(`Sync failed: ${response.status}`);
    }
    
    return response.json();
}

// Remove pending item (placeholder)
async function removePendingItem(id) {
    // Remove from IndexedDB
    console.log('Removing pending item:', id);
}

// Push notification event
self.addEventListener('push', (event) => {
    console.log('Push notification received:', event);
    
    const options = {
        body: 'SafeKeep has new updates available',
        icon: '/icon-192x192.png',
        badge: '/icon-72x72.png',
        tag: 'safekeep-update',
        requireInteraction: false,
        actions: [
            {
                action: 'view',
                title: 'View',
                icon: '/icon-72x72.png'
            },
            {
                action: 'dismiss',
                title: 'Dismiss'
            }
        ]
    };
    
    if (event.data) {
        try {
            const data = event.data.json();
            options.body = data.body || options.body;
            options.title = data.title || 'SafeKeep';
        } catch (error) {
            console.error('Failed to parse push data:', error);
        }
    }
    
    event.waitUntil(
        self.registration.showNotification('SafeKeep', options)
    );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'view') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message event - handle messages from main thread
self.addEventListener('message', (event) => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_VERSION':
            event.ports[0].postMessage({ version: CACHE_NAME });
            break;
            
        case 'CLEAR_CACHE':
            clearAllCaches().then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
            
        case 'CACHE_URLS':
            cacheUrls(payload.urls).then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
    }
});

// Clear all caches
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
    
    notifyClients('CACHE_CLEARED', { timestamp: Date.now() });
}

// Cache specific URLs
async function cacheUrls(urls) {
    const cache = await caches.open(DYNAMIC_CACHE);
    
    for (const url of urls) {
        try {
            const response = await fetch(url);
            if (response.ok) {
                await cache.put(url, response);
            }
        } catch (error) {
            console.error('Failed to cache URL:', url, error);
        }
    }
    
    notifyClients('URLS_CACHED', { urls, timestamp: Date.now() });
}

// Notify all clients
function notifyClients(type, payload) {
    self.clients.matchAll().then(clients => {
        clients.forEach(client => {
            client.postMessage({ type, payload });
        });
    });
}

// Periodic background sync (if supported)
self.addEventListener('periodicsync', (event) => {
    console.log('Periodic sync triggered:', event.tag);
    
    if (event.tag === 'content-sync') {
        event.waitUntil(handlePeriodicSync());
    }
});

// Handle periodic sync
async function handlePeriodicSync() {
    try {
        // Check for updates or sync data periodically
        console.log('Performing periodic sync...');
        
        // This could check for new content, sync user data, etc.
        const response = await fetch('/api/check-updates');
        
        if (response.ok) {
            const data = await response.json();
            
            if (data.hasUpdates) {
                // Show notification about updates
                self.registration.showNotification('SafeKeep Updates', {
                    body: 'New features and improvements are available',
                    icon: '/icon-192x192.png',
                    tag: 'periodic-update'
                });
            }
        }
    } catch (error) {
        console.error('Periodic sync failed:', error);
    }
}

// Error handling
self.addEventListener('error', (event) => {
    console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker unhandled rejection:', event.reason);
});

console.log('Service Worker loaded successfully');