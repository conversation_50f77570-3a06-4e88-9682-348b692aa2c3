-- Enhanced User Authentication System
-- Execute this in Supabase SQL Editor to fix user signup issues

-- First, ensure the users table has the correct structure
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS subscription_tier TEXT DEFAULT 'free',
ADD COLUMN IF NOT EXISTS trial_ends_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '14 days'),
ADD COLUMN IF NOT EXISTS features_enabled JSONB DEFAULT '{
  "backup_frequency": ["manual"],
  "storage_limit_gb": 1,
  "priority_support": false,
  "advanced_encryption": false,
  "multi_device_sync": false,
  "backup_history_days": 7
}'::jsonb;

-- Create a more robust user creation function with error handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
DECLARE
  default_display_name TEXT;
BEGIN
  -- Extract display name with fallback
  default_display_name := COALESCE(
    NEW.raw_user_meta_data->>'display_name',
    NEW.raw_user_meta_data->>'full_name',
    split_part(NEW.email, '@', 1)
  );

  -- Insert user record with comprehensive error handling
  BEGIN
    INSERT INTO public.users (
      id, 
      email, 
      display_name, 
      created_at, 
      last_login_at,
      storage_used, 
      storage_quota, 
      subscription_tier,
      trial_ends_at,
      features_enabled,
      backup_settings
    )
    VALUES (
      NEW.id,
      NEW.email,
      default_display_name,
      NEW.created_at,
      NEW.created_at,
      0,
      1073741824, -- 1GB for free tier
      'free',
      NOW() + INTERVAL '14 days',
      '{
        "backup_frequency": ["manual"],
        "storage_limit_gb": 1,
        "priority_support": false,
        "advanced_encryption": false,
        "multi_device_sync": false,
        "backup_history_days": 7
      }'::jsonb,
      '{
        "auto_backup": false,
        "wifi_only": true,
        "frequency": "weekly"
      }'::jsonb
    );
    
    -- Initialize storage usage tracking
    INSERT INTO public.storage_usage (user_id, category, file_count, total_size)
    VALUES 
      (NEW.id, 'photo', 0, 0),
      (NEW.id, 'contact', 0, 0),
      (NEW.id, 'message', 0, 0)
    ON CONFLICT (user_id, category) DO NOTHING;
    
    -- Initialize sync status
    INSERT INTO public.sync_status (user_id, sync_status)
    VALUES (NEW.id, 'synced')
    ON CONFLICT DO NOTHING;
    
    -- Log successful user creation
    RAISE NOTICE 'Successfully created user record for %', NEW.email;
    
  EXCEPTION
    WHEN unique_violation THEN
      -- User already exists, update last login
      UPDATE public.users 
      SET last_login_at = NEW.created_at
      WHERE id = NEW.id;
      RAISE NOTICE 'User % already exists, updated last login', NEW.email;
      
    WHEN OTHERS THEN
      -- Log error but don't fail the auth creation
      RAISE WARNING 'Failed to create user record for %: % %', NEW.email, SQLSTATE, SQLERRM;
      
      -- Try to create a minimal user record
      BEGIN
        INSERT INTO public.users (id, email, display_name, created_at)
        VALUES (NEW.id, NEW.email, default_display_name, NEW.created_at)
        ON CONFLICT (id) DO UPDATE SET last_login_at = NEW.created_at;
      EXCEPTION
        WHEN OTHERS THEN
          RAISE WARNING 'Failed to create minimal user record for %: % %', NEW.email, SQLSTATE, SQLERRM;
      END;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT INSERT, UPDATE ON public.users TO supabase_auth_admin;
GRANT INSERT ON public.storage_usage TO supabase_auth_admin;
GRANT INSERT ON public.sync_status TO supabase_auth_admin;

-- Create a function to test user creation
CREATE OR REPLACE FUNCTION public.test_user_creation(test_email TEXT)
RETURNS TEXT AS $$
DECLARE
  test_user_id UUID;
  result_message TEXT;
BEGIN
  -- Generate a test UUID
  test_user_id := gen_random_uuid();
  
  -- Try to create a user record directly
  BEGIN
    INSERT INTO public.users (
      id, email, display_name, created_at, storage_used, storage_quota
    ) VALUES (
      test_user_id, test_email, 'Test User', NOW(), 0, 1073741824
    );
    
    result_message := 'SUCCESS: User creation test passed for ' || test_email;
    
    -- Clean up test user
    DELETE FROM public.users WHERE id = test_user_id;
    
  EXCEPTION
    WHEN OTHERS THEN
      result_message := 'FAILED: User creation test failed: ' || SQLSTATE || ' ' || SQLERRM;
  END;
  
  RETURN result_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Test the user creation function
SELECT public.test_user_creation('<EMAIL>');

-- Verify the trigger exists
SELECT 
  trigger_name,
  event_object_table,
  action_timing,
  event_manipulation,
  action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- Verify the function exists
SELECT 
  routine_name,
  routine_type,
  security_type
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' AND routine_schema = 'public';