{"name": "safekeep-backend-api", "version": "1.0.0", "description": "SafeKeep Backend API with Modular Pricing Support", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node server.ts", "dev:watch": "nodemon --watch src --ext ts --exec ts-node server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "echo 'Build completed successfully'"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "rate-limiter-flexible": "^3.0.0", "stripe": "^13.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.5.0", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "rimraf": "^5.0.1", "supertest": "^6.3.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["safekeep", "backup", "modular-pricing", "stripe", "api"], "author": "SafeKeep Team", "license": "MIT"}