{"name": "safekeepapp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-camera-roll/camera-roll": "^7.10.1", "@react-native/new-app-screen": "0.80.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "@stripe/stripe-react-native": "^0.49.0", "@supabase/supabase-js": "^2.52.0", "buffer": "^6.0.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^17.0.1", "express": "^5.1.0", "native-base": "^3.4.28", "react": "19.1.0", "react-dom": "^19.1.0", "react-native": "0.80.1", "react-native-contacts": "^8.0.5", "react-native-dotenv": "^3.4.11", "react-native-elements": "^3.4.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.27.1", "react-native-get-random-values": "^1.9.0", "react-native-image-picker": "^8.2.1", "react-native-keychain": "^10.0.0", "react-native-paper": "^5.14.5", "react-native-permissions": "^5.4.1", "react-native-progress": "^5.0.1", "react-native-push-notification": "^8.1.1", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-redux": "^9.2.0", "stripe": "^18.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "description": "This is a new [**React Native**](https://reactnative.dev) project, bootstrapped using [`@react-native-community/cli`](https://github.com/react-native-community/cli).", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}