import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface PerformanceMetrics {
  id: string;
  sessionId: string;
  timestamp: Date;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  batteryLevel: number;
  batteryCharging: boolean;
  networkType: string;
  networkSpeed?: number; // Mbps
  cpuUsage?: number; // percentage
  uploadSpeed?: number; // bytes per second
  downloadSpeed?: number; // bytes per second
  itemsProcessedPerSecond: number;
  errorRate: number; // percentage
}

export interface BatteryMonitoringResult {
  level: number;
  isCharging: boolean;
  shouldPause: boolean;
  warning?: string;
}

export interface MemoryOptimizationResult {
  beforeOptimization: number;
  afterOptimization: number;
  freedMemory: number;
  success: boolean;
}

export interface NetworkOptimizationConfig {
  maxConcurrentUploads: number;
  chunkSize: number;
  adaptiveChunkSize: boolean;
  compressionEnabled: boolean;
  retryConfig: {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
  };
}

class PerformanceMonitoringService {
  private metrics: PerformanceMetrics[] = [];
  private monitoringInterval?: NodeJS.Timeout;
  private isMonitoring = false;
  private readonly STORAGE_KEY = 'performance_metrics';
  private readonly BATTERY_THRESHOLD = 15; // 15% minimum battery
  private readonly MEMORY_THRESHOLD = 80; // 80% maximum memory usage
  private readonly MAX_METRICS_STORED = 100;

  // Start performance monitoring
  async startMonitoring(sessionId: string): Promise<void> {
    if (this.isMonitoring) {
      console.log('⚡ Performance monitoring already active');
      return;
    }

    console.log('⚡ Starting performance monitoring...');
    this.isMonitoring = true;

    // Monitor every 5 seconds during backup
    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.collectMetrics(sessionId);
        this.metrics.push(metrics);

        // Keep only recent metrics to prevent memory bloat
        if (this.metrics.length > this.MAX_METRICS_STORED) {
          this.metrics = this.metrics.slice(-this.MAX_METRICS_STORED);
        }

        // Save metrics periodically
        await this.saveMetrics();

        // Check for performance issues
        await this.checkPerformanceThresholds(metrics);
      } catch (error) {
        console.error('Error collecting performance metrics:', error);
      }
    }, 5000);
  }

  // Stop performance monitoring
  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⚡ Stopping performance monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    // Save final metrics
    await this.saveMetrics();
  }

  // Collect current performance metrics
  private async collectMetrics(sessionId: string): Promise<PerformanceMetrics> {
    const timestamp = new Date();
    
    // Memory usage
    const memoryUsage = await this.getMemoryUsage();
    
    // Battery information
    const batteryLevel = await DeviceInfo.getBatteryLevel();
    const batteryCharging = await DeviceInfo.isBatteryCharging();
    
    // Network information
    const networkState = await NetInfo.fetch();
    
    const metrics: PerformanceMetrics = {
      id: `metrics_${timestamp.getTime()}`,
      sessionId,
      timestamp,
      memoryUsage,
      batteryLevel: Math.round(batteryLevel * 100),
      batteryCharging,
      networkType: networkState.type || 'unknown',
      networkSpeed: this.calculateNetworkSpeed(networkState),
      itemsProcessedPerSecond: this.calculateProcessingRate(),
      errorRate: this.calculateErrorRate()
    };

    return metrics;
  }

  // Get memory usage information
  private async getMemoryUsage(): Promise<{ used: number; total: number; percentage: number }> {
    try {
      if (Platform.OS === 'android') {
        const totalMemory = await DeviceInfo.getTotalMemory();
        const usedMemory = await DeviceInfo.getUsedMemory();
        
        return {
          used: usedMemory,
          total: totalMemory,
          percentage: Math.round((usedMemory / totalMemory) * 100)
        };
      } else {
        // iOS memory monitoring is more limited
        const totalMemory = await DeviceInfo.getTotalMemory();
        // Estimate used memory (iOS doesn't provide direct access)
        const estimatedUsed = totalMemory * 0.6; // Rough estimate
        
        return {
          used: estimatedUsed,
          total: totalMemory,
          percentage: 60 // Estimated
        };
      }
    } catch (error) {
      console.error('Error getting memory usage:', error);
      return { used: 0, total: 0, percentage: 0 };
    }
  }

  // Calculate network speed based on connection type
  private calculateNetworkSpeed(networkState: any): number | undefined {
    if (!networkState.isConnected) {
      return 0;
    }

    // Estimate speeds based on connection type
    switch (networkState.type) {
      case 'wifi':
        return 50; // 50 Mbps average WiFi
      case 'cellular':
        switch (networkState.details?.cellularGeneration) {
          case '5g':
            return 100;
          case '4g':
            return 25;
          case '3g':
            return 5;
          default:
            return 10;
        }
      default:
        return undefined;
    }
  }

  // Calculate items processed per second
  private calculateProcessingRate(): number {
    if (this.metrics.length < 2) {
      return 0;
    }

    const recent = this.metrics.slice(-5); // Last 5 measurements
    const timeSpan = recent[recent.length - 1].timestamp.getTime() - recent[0].timestamp.getTime();
    
    if (timeSpan === 0) {
      return 0;
    }

    // This would be calculated based on actual backup progress
    // For now, return a placeholder
    return 2.5; // 2.5 items per second average
  }

  // Calculate error rate percentage
  private calculateErrorRate(): number {
    // This would be calculated based on actual backup errors
    // For now, return a placeholder
    return 0.5; // 0.5% error rate
  }

  // Check performance thresholds and trigger warnings
  private async checkPerformanceThresholds(metrics: PerformanceMetrics): Promise<void> {
    const warnings: string[] = [];

    // Battery level check
    if (metrics.batteryLevel < this.BATTERY_THRESHOLD && !metrics.batteryCharging) {
      warnings.push(`Low battery: ${metrics.batteryLevel}%. Consider connecting to power.`);
    }

    // Memory usage check
    if (metrics.memoryUsage.percentage > this.MEMORY_THRESHOLD) {
      warnings.push(`High memory usage: ${metrics.memoryUsage.percentage}%. Performance may be affected.`);
      
      // Trigger memory optimization
      await this.optimizeMemoryUsage();
    }

    // Network speed check
    if (metrics.networkSpeed && metrics.networkSpeed < 1) {
      warnings.push('Slow network connection detected. Backup may take longer.');
    }

    // Error rate check
    if (metrics.errorRate > 5) {
      warnings.push(`High error rate: ${metrics.errorRate}%. Check network connection.`);
    }

    // Log warnings
    if (warnings.length > 0) {
      console.warn('⚠️ Performance warnings:', warnings);
    }
  }

  // Monitor battery consumption during backup
  async monitorBatteryConsumption(): Promise<BatteryMonitoringResult> {
    try {
      const batteryLevel = await DeviceInfo.getBatteryLevel();
      const isCharging = await DeviceInfo.isBatteryCharging();
      const levelPercentage = Math.round(batteryLevel * 100);

      const shouldPause = levelPercentage < this.BATTERY_THRESHOLD && !isCharging;
      
      let warning: string | undefined;
      if (shouldPause) {
        warning = `Battery critically low (${levelPercentage}%). Backup will be paused to preserve battery.`;
      } else if (levelPercentage < 25 && !isCharging) {
        warning = `Battery low (${levelPercentage}%). Consider connecting to power.`;
      }

      return {
        level: levelPercentage,
        isCharging,
        shouldPause,
        warning
      };
    } catch (error) {
      console.error('Error monitoring battery:', error);
      return {
        level: 100,
        isCharging: false,
        shouldPause: false
      };
    }
  }

  // Optimize memory usage for large datasets
  async optimizeMemoryUsage(): Promise<MemoryOptimizationResult> {
    try {
      const beforeOptimization = await this.getMemoryUsage();
      
      console.log('🧹 Starting memory optimization...');

      // Clear old metrics
      if (this.metrics.length > 50) {
        this.metrics = this.metrics.slice(-50);
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      // Clear any cached data
      await this.clearTemporaryData();

      const afterOptimization = await this.getMemoryUsage();
      const freedMemory = beforeOptimization.used - afterOptimization.used;

      console.log(`🧹 Memory optimization complete. Freed ${Math.round(freedMemory / 1024 / 1024)}MB`);

      return {
        beforeOptimization: beforeOptimization.used,
        afterOptimization: afterOptimization.used,
        freedMemory,
        success: freedMemory > 0
      };
    } catch (error) {
      console.error('Error optimizing memory:', error);
      return {
        beforeOptimization: 0,
        afterOptimization: 0,
        freedMemory: 0,
        success: false
      };
    }
  }

  // Clear temporary data to free memory
  private async clearTemporaryData(): Promise<void> {
    try {
      // Clear any temporary files or cached data
      // This would be implemented based on specific caching mechanisms
      console.log('🧹 Clearing temporary data...');
    } catch (error) {
      console.error('Error clearing temporary data:', error);
    }
  }

  // Get network bandwidth utilization optimization config
  getNetworkOptimizationConfig(networkType: string, networkSpeed?: number): NetworkOptimizationConfig {
    const baseConfig: NetworkOptimizationConfig = {
      maxConcurrentUploads: 3,
      chunkSize: 1024 * 1024, // 1MB
      adaptiveChunkSize: true,
      compressionEnabled: true,
      retryConfig: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 30000
      }
    };

    // Optimize based on network type
    switch (networkType) {
      case 'wifi':
        return {
          ...baseConfig,
          maxConcurrentUploads: 5,
          chunkSize: 2 * 1024 * 1024, // 2MB for WiFi
        };
      
      case 'cellular':
        return {
          ...baseConfig,
          maxConcurrentUploads: 2,
          chunkSize: 512 * 1024, // 512KB for cellular
          compressionEnabled: true
        };
      
      default:
        return {
          ...baseConfig,
          maxConcurrentUploads: 1,
          chunkSize: 256 * 1024, // 256KB for unknown/slow connections
        };
    }
  }

  // Get current performance metrics
  getCurrentMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  // Get performance summary
  getPerformanceSummary(): {
    averageMemoryUsage: number;
    averageBatteryLevel: number;
    averageProcessingRate: number;
    averageErrorRate: number;
    totalMonitoringTime: number;
  } {
    if (this.metrics.length === 0) {
      return {
        averageMemoryUsage: 0,
        averageBatteryLevel: 0,
        averageProcessingRate: 0,
        averageErrorRate: 0,
        totalMonitoringTime: 0
      };
    }

    const totalMemory = this.metrics.reduce((sum, m) => sum + m.memoryUsage.percentage, 0);
    const totalBattery = this.metrics.reduce((sum, m) => sum + m.batteryLevel, 0);
    const totalProcessingRate = this.metrics.reduce((sum, m) => sum + m.itemsProcessedPerSecond, 0);
    const totalErrorRate = this.metrics.reduce((sum, m) => sum + m.errorRate, 0);

    const firstMetric = this.metrics[0];
    const lastMetric = this.metrics[this.metrics.length - 1];
    const totalTime = lastMetric.timestamp.getTime() - firstMetric.timestamp.getTime();

    return {
      averageMemoryUsage: Math.round(totalMemory / this.metrics.length),
      averageBatteryLevel: Math.round(totalBattery / this.metrics.length),
      averageProcessingRate: Math.round((totalProcessingRate / this.metrics.length) * 100) / 100,
      averageErrorRate: Math.round((totalErrorRate / this.metrics.length) * 100) / 100,
      totalMonitoringTime: totalTime
    };
  }

  // Save metrics to local storage
  private async saveMetrics(): Promise<void> {
    try {
      const metricsData = {
        metrics: this.metrics.slice(-50), // Keep only recent metrics
        lastUpdated: new Date().toISOString()
      };
      
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(metricsData));
    } catch (error) {
      console.error('Error saving performance metrics:', error);
    }
  }

  // Load metrics from local storage
  async loadMetrics(): Promise<void> {
    try {
      const metricsJson = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (metricsJson) {
        const metricsData = JSON.parse(metricsJson);
        this.metrics = metricsData.metrics.map((m: any) => ({
          ...m,
          timestamp: new Date(m.timestamp)
        }));
      }
    } catch (error) {
      console.error('Error loading performance metrics:', error);
    }
  }

  // Clear stored metrics
  async clearMetrics(): Promise<void> {
    try {
      this.metrics = [];
      await AsyncStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing performance metrics:', error);
    }
  }
}

export default new PerformanceMonitoringService();