import { ModularSubscription, SubscriptionDetails, SubscriptionRequest } from '../types/modular-pricing';
export declare class SubscriptionManager {
    createSubscription(subscriptionRequest: SubscriptionRequest): Promise<ModularSubscription>;
    updateSubscription(subscriptionId: string, serviceIds: string[]): Promise<ModularSubscription>;
    getSubscriptionDetails(userId: string): Promise<SubscriptionDetails | null>;
    cancelSubscription(subscriptionId: string): Promise<boolean>;
}
//# sourceMappingURL=SubscriptionManager.d.ts.map