/**
 * Responsive Layout Manager for SafeKeep Web Demo
 * Handles responsive design, mobile layouts, and adaptive UI components
 */

class ResponsiveLayoutManager {
    constructor() {
        this.breakpoints = {
            xs: 0,
            sm: 576,
            md: 768,
            lg: 992,
            xl: 1200,
            xxl: 1400
        };
        
        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.isMobile = this.currentBreakpoint === 'xs' || this.currentBreakpoint === 'sm';
        this.isTablet = this.currentBreakpoint === 'md';
        this.isDesktop = this.currentBreakpoint === 'lg' || this.currentBreakpoint === 'xl' || this.currentBreakpoint === 'xxl';
        
        this.resizeObserver = null;
        this.mediaQueries = {};
        
        this.init();
    }
    
    init() {
        this.setupMediaQueries();
        this.setupResizeObserver();
        this.adaptInitialLayout();
        this.setupTouchHandling();
        this.setupKeyboardNavigation();
        
        // Listen for orientation changes
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleOrientationChange(), 100);
        });
        
        // Initial layout adaptation
        this.handleBreakpointChange();
    }
    
    getCurrentBreakpoint() {
        const width = window.innerWidth;
        
        if (width >= this.breakpoints.xxl) return 'xxl';
        if (width >= this.breakpoints.xl) return 'xl';
        if (width >= this.breakpoints.lg) return 'lg';
        if (width >= this.breakpoints.md) return 'md';
        if (width >= this.breakpoints.sm) return 'sm';
        return 'xs';
    }
    
    setupMediaQueries() {
        Object.keys(this.breakpoints).forEach(breakpoint => {
            const minWidth = this.breakpoints[breakpoint];
            const query = window.matchMedia(`(min-width: ${minWidth}px)`);
            
            this.mediaQueries[breakpoint] = query;
            
            query.addEventListener('change', () => {
                this.updateBreakpoint();
            });
        });
    }
    
    setupResizeObserver() {
        if ('ResizeObserver' in window) {
            this.resizeObserver = new ResizeObserver(entries => {
                this.handleResize(entries);
            });
            
            // Observe the main container
            const container = document.querySelector('.container');
            if (container) {
                this.resizeObserver.observe(container);
            }
        }
    }
    
    updateBreakpoint() {
        const newBreakpoint = this.getCurrentBreakpoint();
        
        if (newBreakpoint !== this.currentBreakpoint) {
            const oldBreakpoint = this.currentBreakpoint;
            this.currentBreakpoint = newBreakpoint;
            
            this.isMobile = newBreakpoint === 'xs' || newBreakpoint === 'sm';
            this.isTablet = newBreakpoint === 'md';
            this.isDesktop = newBreakpoint === 'lg' || newBreakpoint === 'xl' || newBreakpoint === 'xxl';
            
            this.handleBreakpointChange(oldBreakpoint, newBreakpoint);
        }
    }
    
    handleBreakpointChange(oldBreakpoint, newBreakpoint) {
        // Update body class for CSS targeting
        document.body.className = document.body.className
            .replace(/breakpoint-\w+/g, '')
            .trim();
        document.body.classList.add(`breakpoint-${this.currentBreakpoint}`);
        
        // Adapt layouts
        this.adaptGridLayouts();
        this.adaptNavigationLayout();
        this.adaptCardLayouts();
        this.adaptFormLayouts();
        this.adaptTableLayouts();
        
        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('breakpointchange', {
            detail: {
                oldBreakpoint,
                newBreakpoint: this.currentBreakpoint,
                isMobile: this.isMobile,
                isTablet: this.isTablet,
                isDesktop: this.isDesktop
            }
        }));
    }
    
    adaptInitialLayout() {
        // Add responsive classes to body
        document.body.classList.add(`breakpoint-${this.currentBreakpoint}`);
        
        if (this.isMobile) {
            document.body.classList.add('is-mobile');
        }
        if (this.isTablet) {
            document.body.classList.add('is-tablet');
        }
        if (this.isDesktop) {
            document.body.classList.add('is-desktop');
        }
    }
    
    adaptGridLayouts() {
        const grids = document.querySelectorAll('.demo-grid, .stats-grid, .metrics-grid');
        
        grids.forEach(grid => {
            if (this.isMobile) {
                grid.style.gridTemplateColumns = '1fr';
            } else if (this.isTablet) {
                grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
            } else {
                // Reset to original responsive behavior
                grid.style.gridTemplateColumns = '';
            }
        });
    }
    
    adaptNavigationLayout() {
        const nav = document.querySelector('.main-nav');
        if (!nav) return;
        
        if (this.isMobile) {
            this.createMobileNavigation(nav);
        } else {
            this.restoreDesktopNavigation(nav);
        }
    }
    
    createMobileNavigation(nav) {
        // Create hamburger menu if it doesn't exist
        let hamburger = document.querySelector('.mobile-nav-toggle');
        if (!hamburger) {
            hamburger = document.createElement('button');
            hamburger.className = 'mobile-nav-toggle btn btn-outline';
            hamburger.innerHTML = '☰';
            hamburger.setAttribute('aria-label', 'Toggle navigation menu');
            hamburger.setAttribute('aria-expanded', 'false');
            
            hamburger.addEventListener('click', () => {
                const isExpanded = hamburger.getAttribute('aria-expanded') === 'true';
                hamburger.setAttribute('aria-expanded', !isExpanded);
                nav.classList.toggle('nav-expanded');
                hamburger.innerHTML = isExpanded ? '☰' : '✕';
            });
            
            nav.parentNode.insertBefore(hamburger, nav);
        }
        
        nav.classList.add('mobile-nav');
    }
    
    restoreDesktopNavigation(nav) {
        const hamburger = document.querySelector('.mobile-nav-toggle');
        if (hamburger) {
            hamburger.remove();
        }
        
        nav.classList.remove('mobile-nav', 'nav-expanded');
    }
    
    adaptCardLayouts() {
        const cards = document.querySelectorAll('.demo-card, .plan-card, .metric-card');
        
        cards.forEach(card => {
            if (this.isMobile) {
                card.classList.add('card-mobile');
                
                // Stack card content vertically on mobile
                const cardBody = card.querySelector('.card-body');
                if (cardBody) {
                    cardBody.style.display = 'flex';
                    cardBody.style.flexDirection = 'column';
                    cardBody.style.gap = 'var(--spacing-sm)';
                }
            } else {
                card.classList.remove('card-mobile');
                
                // Reset card body styles
                const cardBody = card.querySelector('.card-body');
                if (cardBody) {
                    cardBody.style.display = '';
                    cardBody.style.flexDirection = '';
                    cardBody.style.gap = '';
                }
            }
        });
    }
    
    adaptFormLayouts() {
        const forms = document.querySelectorAll('form, .form-container');
        
        forms.forEach(form => {
            const formGroups = form.querySelectorAll('.form-group');
            
            formGroups.forEach(group => {
                if (this.isMobile) {
                    group.classList.add('form-group-mobile');
                    
                    // Stack form controls vertically
                    const controls = group.querySelectorAll('.form-control, .btn');
                    controls.forEach(control => {
                        control.style.width = '100%';
                        control.style.marginBottom = 'var(--spacing-sm)';
                    });
                } else {
                    group.classList.remove('form-group-mobile');
                    
                    // Reset form control styles
                    const controls = group.querySelectorAll('.form-control, .btn');
                    controls.forEach(control => {
                        control.style.width = '';
                        control.style.marginBottom = '';
                    });
                }
            });
        });
    }
    
    adaptTableLayouts() {
        const tables = document.querySelectorAll('table');
        
        tables.forEach(table => {
            if (this.isMobile) {
                this.createResponsiveTable(table);
            } else {
                this.restoreNormalTable(table);
            }
        });
    }
    
    createResponsiveTable(table) {
        if (!table.classList.contains('table-responsive-created')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive-wrapper';
            wrapper.style.cssText = `
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border: 1px solid var(--border-light);
                border-radius: var(--radius-md);
            `;
            
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
            table.classList.add('table-responsive-created');
        }
    }
    
    restoreNormalTable(table) {
        const wrapper = table.parentNode;
        if (wrapper && wrapper.classList.contains('table-responsive-wrapper')) {
            wrapper.parentNode.insertBefore(table, wrapper);
            wrapper.remove();
            table.classList.remove('table-responsive-created');
        }
    }
    
    setupTouchHandling() {
        if ('ontouchstart' in window) {
            document.body.classList.add('touch-device');
            
            // Improve touch targets
            const buttons = document.querySelectorAll('.btn, button, [role="button"]');
            buttons.forEach(btn => {
                if (!btn.style.minHeight) {
                    btn.style.minHeight = '44px';
                }
                if (!btn.style.minWidth) {
                    btn.style.minWidth = '44px';
                }
            });
            
            // Add touch feedback
            this.addTouchFeedback();
        }
    }
    
    addTouchFeedback() {
        const touchElements = document.querySelectorAll('.btn, .card, [role="button"]');
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.classList.add('touch-active');
            }, { passive: true });
            
            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            }, { passive: true });
        });
    }
    
    setupKeyboardNavigation() {
        // Improve keyboard navigation for mobile devices with external keyboards
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
        
        // Skip link for accessibility
        this.createSkipLink();
    }
    
    createSkipLink() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.className = 'skip-link';
        skipLink.textContent = 'Skip to main content';
        
        document.body.insertBefore(skipLink, document.body.firstChild);
        
        // Ensure main content has the ID
        let mainContent = document.getElementById('main-content');
        if (!mainContent) {
            mainContent = document.querySelector('.main-content, main, .container');
            if (mainContent) {
                mainContent.id = 'main-content';
            }
        }
    }
    
    handleOrientationChange() {
        // Force layout recalculation after orientation change
        this.updateBreakpoint();
        
        // Scroll to top to avoid layout issues
        window.scrollTo(0, 0);
        
        // Dispatch orientation change event
        window.dispatchEvent(new CustomEvent('orientationchange-handled', {
            detail: {
                orientation: screen.orientation ? screen.orientation.angle : window.orientation,
                breakpoint: this.currentBreakpoint
            }
        }));
    }
    
    handleResize(entries) {
        // Debounce resize handling
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            entries.forEach(entry => {
                // Handle specific resize logic for observed elements
                this.optimizeElementForSize(entry.target, entry.contentRect);
            });
        }, 100);
    }
    
    optimizeElementForSize(element, rect) {
        // Optimize specific elements based on their size
        if (element.classList.contains('container')) {
            if (rect.width < 600) {
                element.classList.add('container-narrow');
            } else {
                element.classList.remove('container-narrow');
            }
        }
    }
    
    // Utility methods for components
    isMobileDevice() {
        return this.isMobile;
    }
    
    isTabletDevice() {
        return this.isTablet;
    }
    
    isDesktopDevice() {
        return this.isDesktop;
    }
    
    getCurrentBreakpointName() {
        return this.currentBreakpoint;
    }
    
    onBreakpointChange(callback) {
        window.addEventListener('breakpointchange', callback);
        return () => window.removeEventListener('breakpointchange', callback);
    }
    
    // Method to manually trigger layout adaptation
    adaptLayout() {
        this.handleBreakpointChange();
    }
    
    destroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        
        Object.values(this.mediaQueries).forEach(query => {
            query.removeEventListener('change', this.updateBreakpoint);
        });
    }
}

// Add responsive CSS classes
const responsiveStyles = `
<style>
/* Mobile-specific styles */
.breakpoint-xs .container,
.breakpoint-sm .container {
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
}

.breakpoint-xs .demo-grid,
.breakpoint-sm .demo-grid {
    grid-template-columns: 1fr !important;
    gap: var(--spacing-md);
}

.breakpoint-xs .stats-grid,
.breakpoint-sm .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
}

/* Mobile navigation */
.mobile-nav {
    position: fixed;
    top: 0;
    left: -100%;
    width: 80%;
    height: 100vh;
    background: var(--bg-primary);
    border-right: 1px solid var(--border-light);
    transition: left var(--transition-normal);
    z-index: var(--z-modal);
    overflow-y: auto;
}

.mobile-nav.nav-expanded {
    left: 0;
}

.mobile-nav-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: calc(var(--z-modal) + 1);
    width: 44px;
    height: 44px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

/* Touch feedback */
.touch-device .touch-active {
    transform: scale(0.98);
    opacity: 0.8;
}

/* Keyboard navigation */
.keyboard-navigation *:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

/* Card mobile adaptations */
.card-mobile {
    margin-bottom: var(--spacing-lg);
    border-radius: var(--radius-md);
}

.card-mobile .card-header,
.card-mobile .card-body,
.card-mobile .card-footer {
    padding: var(--spacing-md);
}

/* Form mobile adaptations */
.form-group-mobile {
    margin-bottom: var(--spacing-lg);
}

.form-group-mobile .form-label {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

/* Table responsive wrapper */
.table-responsive-wrapper {
    margin-bottom: var(--spacing-lg);
}

.table-responsive-wrapper table {
    min-width: 600px;
    margin-bottom: 0;
}

/* Container narrow state */
.container-narrow {
    padding: var(--spacing-sm);
}

.container-narrow .header {
    padding: var(--spacing-lg) var(--spacing-sm);
}

.container-narrow .main-content {
    padding: var(--spacing-lg) var(--spacing-sm);
}

/* Responsive text sizes */
@media (max-width: 576px) {
    h1 { font-size: var(--font-size-3xl); }
    h2 { font-size: var(--font-size-2xl); }
    h3 { font-size: var(--font-size-xl); }
    
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }
    
    .btn-sm {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}

/* Tablet adaptations */
@media (min-width: 768px) and (max-width: 991px) {
    .demo-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Print optimizations */
@media print {
    .mobile-nav-toggle,
    .theme-toggle-container,
    .skip-link {
        display: none !important;
    }
    
    .container {
        box-shadow: none;
        border-radius: 0;
        max-width: none;
    }
}
</style>
`;

// Inject responsive styles
document.head.insertAdjacentHTML('beforeend', responsiveStyles);

// Initialize responsive layout manager when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.responsiveLayoutManager = new ResponsiveLayoutManager();
    });
} else {
    window.responsiveLayoutManager = new ResponsiveLayoutManager();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResponsiveLayoutManager;
}