import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import backupReducer, { startBackupSession } from '../../src/store/slices/backupSlice';
import {
  useBackupState,
  useCurrentBackupSession,
  useBackupProgress,
  useBackupActions,
  useOverallBackupProgress,
} from '../../src/store/hooks/backupHooks';
import { BackupSession } from '../../src/types/backup';
import { Text, View } from 'react-native';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('backupHooks', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        backup: backupReducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          serializableCheck: {
            ignoredActions: [
              'backup/startBackupSession',
              'backup/completeBackupSession',
              'backup/addProgressError',
            ],
            ignoredPaths: [
              'backup.currentSession.startTime',
              'backup.currentSession.endTime',
              'backup.backupHistory',
              'backup.realTimeProgress.contacts.errors',
              'backup.realTimeProgress.messages.errors',
              'backup.realTimeProgress.photos.errors',
              'backup.errors',
            ],
          },
        }),
    });
  });

  describe('basic selectors', () => {
    it('should return backup state', () => {
      let backupState: any;
      
      const TestComponent = () => {
        backupState = useBackupState();
        return <Text>Test</Text>;
      };

      render(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );
      
      expect(backupState.currentSession).toBeNull();
      expect(backupState.isBackupInProgress).toBe(false);
      expect(backupState.backupHistory).toEqual([]);
    });

    it('should return current backup session', () => {
      let currentSession: any;
      
      const TestComponent = () => {
        currentSession = useCurrentBackupSession();
        return <Text>Test</Text>;
      };

      render(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );
      
      expect(currentSession).toBeNull();
    });

    it('should return backup progress', () => {
      let backupProgress: any;
      
      const TestComponent = () => {
        backupProgress = useBackupProgress();
        return <Text>Test</Text>;
      };

      render(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );
      
      expect(backupProgress.contacts.total).toBe(0);
      expect(backupProgress.messages.total).toBe(0);
      expect(backupProgress.photos.total).toBe(0);
    });
  });

  describe('computed selectors', () => {
    it('should calculate overall backup progress', () => {
      const mockSession: BackupSession = {
        id: 'test-session',
        userId: 'user-123',
        startTime: new Date(),
        status: 'in_progress',
        totalItems: 100,
        completedItems: 0,
        configuration: {
          autoBackup: false,
          wifiOnly: true,
          includeContacts: true,
          includeMessages: true,
          includePhotos: true,
          compressionLevel: 'medium',
        },
        progress: {
          contacts: { total: 50, completed: 25, failed: 5, status: 'in_progress', errors: [] },
          messages: { total: 30, completed: 20, failed: 0, status: 'in_progress', errors: [] },
          photos: { total: 20, completed: 15, failed: 2, status: 'in_progress', errors: [] },
        },
      };

      store.dispatch(startBackupSession(mockSession));

      let overallProgress: any;
      
      const TestComponent = () => {
        overallProgress = useOverallBackupProgress();
        return <Text>Test</Text>;
      };

      render(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );
      
      expect(overallProgress.totalItems).toBe(100);
      expect(overallProgress.completedItems).toBe(60); // 25 + 20 + 15
      expect(overallProgress.failedItems).toBe(7); // 5 + 0 + 2
      expect(overallProgress.percentage).toBe(60);
      expect(overallProgress.isComplete).toBe(false);
      expect(overallProgress.hasErrors).toBe(true);
    });
  });

  describe('action hooks', () => {
    it('should provide backup actions', () => {
      let backupActions: any;
      
      const TestComponent = () => {
        backupActions = useBackupActions();
        return <Text>Test</Text>;
      };

      render(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );
      
      expect(typeof backupActions.startSession).toBe('function');
      expect(typeof backupActions.updateSession).toBe('function');
      expect(typeof backupActions.completeSession).toBe('function');
      expect(typeof backupActions.cancelSession).toBe('function');
      expect(typeof backupActions.updateProgress).toBe('function');
      expect(typeof backupActions.addError).toBe('function');
      expect(typeof backupActions.updateConfig).toBe('function');
      expect(typeof backupActions.calculateStats).toBe('function');
      expect(typeof backupActions.clearAllErrors).toBe('function');
      expect(typeof backupActions.removeErrorById).toBe('function');
      expect(typeof backupActions.setLoadingState).toBe('function');
      expect(typeof backupActions.resetState).toBe('function');
      expect(typeof backupActions.loadHistory).toBe('function');
      expect(typeof backupActions.saveHistory).toBe('function');
    });

    it('should execute actions correctly', () => {
      let backupActions: any;
      let currentSession: any;
      
      const TestComponent = () => {
        backupActions = useBackupActions();
        currentSession = useCurrentBackupSession();
        return <Text>Test</Text>;
      };

      const mockSession: BackupSession = {
        id: 'test-session',
        userId: 'user-123',
        startTime: new Date(),
        status: 'in_progress',
        totalItems: 100,
        completedItems: 0,
        configuration: {
          autoBackup: false,
          wifiOnly: true,
          includeContacts: true,
          includeMessages: true,
          includePhotos: true,
          compressionLevel: 'medium',
        },
        progress: {
          contacts: { total: 50, completed: 0, failed: 0, status: 'pending', errors: [] },
          messages: { total: 30, completed: 0, failed: 0, status: 'pending', errors: [] },
          photos: { total: 20, completed: 0, failed: 0, status: 'pending', errors: [] },
        },
      };

      const { rerender } = render(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );

      // Execute the action
      backupActions.startSession(mockSession);
      
      // Re-render to get updated state
      rerender(
        <Provider store={store}>
          <TestComponent />
        </Provider>
      );

      expect(currentSession).toEqual(mockSession);
    });
  });
});