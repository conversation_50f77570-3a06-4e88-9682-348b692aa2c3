/**
 * Enhanced Authentication Manager
 * Handles user authentication, profile management, and demo user creation
 */

class AuthManager {
    constructor(supabaseClient, adminClient) {
        this.supabase = supabaseClient;
        this.admin = adminClient;
        this.currentUser = null;
        this.authListeners = [];
        this.setupAuthListener();
    }

    // Set up authentication state listener
    setupAuthListener() {
        this.supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth state changed:', event, session?.user?.email);
            
            if (event === 'SIGNED_IN' && session?.user) {
                this.currentUser = session.user;
                this.loadUserProfile();
                this.notifyListeners('signed_in', session.user);
            } else if (event === 'SIGNED_OUT') {
                this.currentUser = null;
                this.notifyListeners('signed_out', null);
            }
        });
    }

    // Add authentication event listener
    addListener(callback) {
        this.authListeners.push(callback);
    }

    // Notify all listeners of auth events
    notifyListeners(event, user) {
        this.authListeners.forEach(callback => {
            try {
                callback(event, user);
            } catch (error) {
                console.error('Auth listener error:', error);
            }
        });
    }

    // Enhanced sign up with better error handling
    async signUp(email, password, options = {}) {
        try {
            log(`🔐 Attempting to sign up user: ${email}`);
            
            const { data, error } = await this.supabase.auth.signUp({
                email: email,
                password: password,
                options: {
                    data: {
                        display_name: options.displayName || email.split('@')[0],
                        demo_user: options.isDemo || false
                    }
                }
            });

            if (error) {
                log(`❌ Sign up failed: ${error.message}`, 'error');
                
                // Check if it's the database trigger error
                if (error.message.includes('Database error saving new user')) {
                    log('🔧 Attempting to fix user creation issue...', 'info');
                    
                    // Try to create user record manually if auth succeeded
                    if (data.user) {
                        await this.createUserRecordManually(data.user, options);
                        log('✅ User record created manually after auth success', 'success');
                        return { data, error: null };
                    }
                }
                
                return { data, error };
            }

            log(`✅ Sign up successful for ${email}`, 'success');
            
            // Wait a moment for trigger to execute, then verify user record
            setTimeout(() => this.verifyUserRecord(data.user), 2000);
            
            return { data, error: null };

        } catch (err) {
            log(`❌ Sign up error: ${err.message}`, 'error');
            return { data: null, error: err };
        }
    }

    // Enhanced sign in
    async signIn(email, password) {
        try {
            log(`🔐 Attempting to sign in user: ${email}`);
            
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) {
                log(`❌ Sign in failed: ${error.message}`, 'error');
                return { data, error };
            }

            log(`✅ Sign in successful for ${email}`, 'success');
            return { data, error: null };

        } catch (err) {
            log(`❌ Sign in error: ${err.message}`, 'error');
            return { data: null, error: err };
        }
    }

    // Sign out
    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            
            if (error) {
                log(`❌ Sign out failed: ${error.message}`, 'error');
                return { error };
            }

            log('✅ User signed out successfully', 'success');
            return { error: null };

        } catch (err) {
            log(`❌ Sign out error: ${err.message}`, 'error');
            return { error: err };
        }
    }

    // Create demo user for testing
    async createDemoUser() {
        const demoEmail = `demo-${Date.now()}@safekeep.demo`;
        const demoPassword = 'DemoPassword123!';
        
        log('🎭 Creating demo user for testing...');
        
        const result = await this.signUp(demoEmail, demoPassword, {
            displayName: 'Demo User',
            isDemo: true
        });

        if (result.error) {
            log(`❌ Demo user creation failed: ${result.error.message}`, 'error');
        } else {
            log(`✅ Demo user created: ${demoEmail}`, 'success');
            log('🔑 Demo credentials saved for testing', 'info');
            
            // Store demo credentials for easy access
            localStorage.setItem('safekeep_demo_credentials', JSON.stringify({
                email: demoEmail,
                password: demoPassword,
                createdAt: new Date().toISOString()
            }));
        }

        return result;
    }

    // Load demo credentials
    getDemoCredentials() {
        const stored = localStorage.getItem('safekeep_demo_credentials');
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (err) {
                console.error('Failed to parse demo credentials:', err);
            }
        }
        return null;
    }

    // Manually create user record (fallback for trigger failures)
    async createUserRecordManually(authUser, options = {}) {
        try {
            const userRecord = {
                id: authUser.id,
                email: authUser.email,
                display_name: options.displayName || authUser.email.split('@')[0],
                created_at: authUser.created_at,
                last_login_at: authUser.created_at,
                storage_used: 0,
                storage_quota: 1073741824, // 1GB
                subscription_tier: 'free',
                trial_ends_at: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
                features_enabled: {
                    backup_frequency: ['manual'],
                    storage_limit_gb: 1,
                    priority_support: false,
                    advanced_encryption: false,
                    multi_device_sync: false,
                    backup_history_days: 7
                },
                backup_settings: {
                    auto_backup: false,
                    wifi_only: true,
                    frequency: 'weekly'
                }
            };

            const { data, error } = await this.admin
                .from('users')
                .insert(userRecord)
                .select();

            if (error) {
                throw error;
            }

            // Initialize storage usage
            await this.admin.from('storage_usage').insert([
                { user_id: authUser.id, category: 'photo', file_count: 0, total_size: 0 },
                { user_id: authUser.id, category: 'contact', file_count: 0, total_size: 0 },
                { user_id: authUser.id, category: 'message', file_count: 0, total_size: 0 }
            ]);

            // Initialize sync status
            await this.admin.from('sync_status').insert({
                user_id: authUser.id,
                sync_status: 'synced'
            });

            log('✅ User record created manually', 'success');
            return data[0];

        } catch (error) {
            log(`❌ Failed to create user record manually: ${error.message}`, 'error');
            throw error;
        }
    }

    // Verify user record exists after signup
    async verifyUserRecord(authUser) {
        try {
            const { data, error } = await this.admin
                .from('users')
                .select('*')
                .eq('id', authUser.id)
                .single();

            if (error || !data) {
                log('⚠️ User record not found, creating manually...', 'info');
                await this.createUserRecordManually(authUser);
            } else {
                log('✅ User record verified in database', 'success');
            }
        } catch (error) {
            log(`❌ Error verifying user record: ${error.message}`, 'error');
        }
    }

    // Load user profile data
    async loadUserProfile() {
        if (!this.currentUser) return null;

        try {
            const { data, error } = await this.admin
                .from('users')
                .select(`
                    *,
                    storage_usage(*),
                    sync_status(*)
                `)
                .eq('id', this.currentUser.id)
                .single();

            if (error) {
                log(`❌ Failed to load user profile: ${error.message}`, 'error');
                return null;
            }

            log('✅ User profile loaded', 'success');
            return data;

        } catch (error) {
            log(`❌ Error loading user profile: ${error.message}`, 'error');
            return null;
        }
    }

    // Get current user info
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return !!this.currentUser;
    }

    // Get user subscription tier
    async getUserSubscriptionTier() {
        if (!this.currentUser) return 'free';

        try {
            const { data, error } = await this.admin
                .from('users')
                .select('subscription_tier, trial_ends_at, features_enabled')
                .eq('id', this.currentUser.id)
                .single();

            if (error) {
                log(`❌ Failed to get subscription tier: ${error.message}`, 'error');
                return 'free';
            }

            return {
                tier: data.subscription_tier,
                trialEndsAt: data.trial_ends_at,
                features: data.features_enabled
            };

        } catch (error) {
            log(`❌ Error getting subscription tier: ${error.message}`, 'error');
            return 'free';
        }
    }

    // Update user profile
    async updateProfile(updates) {
        if (!this.currentUser) {
            throw new Error('No authenticated user');
        }

        try {
            const { data, error } = await this.admin
                .from('users')
                .update(updates)
                .eq('id', this.currentUser.id)
                .select()
                .single();

            if (error) {
                throw error;
            }

            log('✅ User profile updated', 'success');
            return data;

        } catch (error) {
            log(`❌ Failed to update profile: ${error.message}`, 'error');
            throw error;
        }
    }
}

// Export for use in main app
window.AuthManager = AuthManager;