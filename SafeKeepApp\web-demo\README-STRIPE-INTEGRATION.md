# SafeKeep Stripe Integration Infrastructure

This document describes the complete Stripe integration infrastructure for the SafeKeep web demo, including payment processing, subscription management, and webhook handling.

## 🏗️ Architecture Overview

The Stripe integration consists of several key components:

### Core Components

1. **StripeConfig** (`stripe-config.js`)
   - Centralized configuration management
   - Subscription tier definitions
   - Test card configurations
   - Webhook event types

2. **StripeManager** (`stripe-manager.js`)
   - Client-side Stripe operations
   - Payment intent creation and confirmation
   - Customer management
   - Payment method handling

3. **StripeServer** (`stripe-server.js`)
   - Server-side Stripe operations
   - Webhook processing
   - API endpoint handling
   - Demo data simulation

4. **SubscriptionManager** (`subscription-manager.js`)
   - Subscription tier management
   - Feature access control
   - Usage tracking
   - Billing interface

## 🚀 Quick Start

### 1. Start the Stripe Server

```bash
# Start the Stripe server on default port 3001
node start-stripe-server.js

# Or specify a custom port
node start-stripe-server.js --port 3002
```

### 2. Start the Web Demo

```bash
# Start the main web demo server
node server.js
```

### 3. Run Integration Tests

Open `stripe-integration-test.html` in your browser to run comprehensive tests.

## 🔧 Configuration

### Stripe Keys

Update the keys in `stripe-config.js`:

```javascript
this.config = {
    publishableKey: 'pk_test_your_publishable_key_here',
    secretKey: 'sk_test_your_secret_key_here',
    webhookSecret: 'whsec_your_webhook_secret_here'
};
```

### Subscription Tiers

The system supports three subscription tiers:

#### Free Tier
- **Price**: $0/month
- **Storage**: 1GB
- **Backups**: 5/month (manual only)
- **Features**: Basic encryption, standard support

#### Basic Tier
- **Price**: $2.99/month
- **Storage**: 10GB
- **Backups**: 50/month (manual, daily)
- **Features**: Advanced encryption, standard support

#### Premium Tier
- **Price**: $9.99/month
- **Storage**: 100GB
- **Backups**: Unlimited (manual, daily, weekly, custom)
- **Features**: Advanced encryption, priority support, multi-device sync

## 📡 API Endpoints

The Stripe server provides the following endpoints:

### Customer Management
- `POST /api/stripe/create-customer` - Create new customer
- `GET /api/stripe/customer/:id` - Get customer details
- `PUT /api/stripe/customer/:id` - Update customer

### Payment Processing
- `POST /api/stripe/create-payment-intent` - Create payment intent
- `POST /api/stripe/confirm-payment` - Confirm payment

### Subscription Management
- `POST /api/stripe/create-subscription` - Create subscription
- `GET /api/stripe/subscription/:id` - Get subscription details
- `PUT /api/stripe/subscription/:id` - Update subscription
- `DELETE /api/stripe/subscription/:id` - Cancel subscription

### Webhooks
- `POST /api/stripe/webhook` - Process Stripe webhooks

### Status
- `GET /api/stripe/status` - Get server status and configuration

## 🎯 Key Features

### 1. Customer Management System

```javascript
// Create customer
const customer = await stripeManager.createCustomer({
    email: '<EMAIL>',
    name: 'John Doe',
    metadata: { source: 'web_demo' }
});

// Get customer
const customer = await stripeManager.getCustomer(customerId);
```

### 2. Secure Payment Processing

```javascript
// Create payment intent
const paymentIntent = await stripeManager.createPaymentIntent({
    amount: 999, // $9.99 in cents
    currency: 'usd',
    description: 'SafeKeep Premium Subscription'
});

// Confirm payment with Stripe Elements
const result = await stripe.confirmCardPayment(
    paymentIntent.client_secret,
    {
        payment_method: {
            card: cardElement,
            billing_details: { email: '<EMAIL>' }
        }
    }
);
```

### 3. Subscription Management

```javascript
// Create subscription
const subscription = await stripeManager.createSubscription({
    customerId: customer.id,
    tierId: 'premium',
    paymentMethodId: paymentMethod.id
});

// Update subscription
const updated = await stripeManager.updateSubscription(
    subscription.id,
    { tierId: 'basic' }
);

// Cancel subscription
const canceled = await stripeManager.cancelSubscription(
    subscription.id,
    true // cancel at period end
);
```

### 4. Payment Method Management

```javascript
// Add payment method
const paymentMethod = await stripeManager.addPaymentMethod(
    customerId,
    {
        type: 'card',
        card: { /* card details */ }
    }
);

// Get customer payment methods
const methods = await stripeManager.getCustomerPaymentMethods(customerId);

// Remove payment method
await stripeManager.removePaymentMethod(paymentMethodId);
```

### 5. Webhook Processing

The system handles these webhook events:

- `customer.created` - New customer registration
- `customer.updated` - Customer information changes
- `customer.subscription.created` - New subscription
- `customer.subscription.updated` - Subscription changes
- `customer.subscription.deleted` - Subscription cancellation
- `payment_intent.succeeded` - Successful payment
- `payment_intent.payment_failed` - Failed payment
- `invoice.payment_succeeded` - Recurring payment success
- `invoice.payment_failed` - Recurring payment failure

### 6. Feature Access Control

```javascript
// Check feature access
const hasAdvancedEncryption = subscriptionManager.hasFeatureAccess('advancedEncryption');
const canBackup = subscriptionManager.checkUsageLimit('backup');

// Track usage
subscriptionManager.trackUsage('storage', 0.5); // 0.5GB used
subscriptionManager.trackUsage('backup', 1); // 1 backup performed
```

## 🧪 Testing

### Automated Testing

Run the comprehensive test suite:

```bash
# Open in browser
open stripe-integration-test.html
```

The test suite covers:

- Configuration validation
- Customer management
- Payment processing
- Subscription lifecycle
- Webhook handling
- Feature access control
- Full payment flows

### Manual Testing

Use these test card numbers:

#### Successful Payments
- **Visa**: `****************`
- **Mastercard**: `****************`
- **American Express**: `***************`

#### Failed Payments
- **Declined**: `****************`
- **Expired**: `****************`
- **Incorrect CVC**: `****************`
- **Insufficient Funds**: `****************`

#### Test Details
- **Expiry**: Any future date (e.g., 12/25)
- **CVC**: Any 3 digits (e.g., 123)
- **ZIP**: Any 5 digits (e.g., 12345)

## 🔒 Security Features

### 1. Secure Key Management
- Publishable keys for client-side operations
- Secret keys for server-side operations
- Webhook signature verification

### 2. Payment Security
- PCI DSS compliant with Stripe Elements
- No card data stored locally
- Secure payment intent flow

### 3. Webhook Security
- Signature verification for webhook authenticity
- Idempotent webhook processing
- Error handling and retry logic

## 📊 Monitoring and Analytics

### Server Status Monitoring

```javascript
// Get server status
const status = await stripeManager.getServerStatus();
console.log('Customers:', status.customers_count);
console.log('Subscriptions:', status.subscriptions_count);
```

### Usage Tracking

The system tracks:
- Storage usage per user
- Backup frequency and success rates
- Payment success/failure rates
- Subscription upgrade/downgrade patterns

## 🚨 Error Handling

### Payment Errors

```javascript
try {
    const result = await stripe.confirmCardPayment(clientSecret, paymentMethod);
    if (result.error) {
        // Handle payment error
        console.error('Payment failed:', result.error.message);
    } else {
        // Payment succeeded
        console.log('Payment successful:', result.paymentIntent.id);
    }
} catch (error) {
    console.error('Unexpected error:', error);
}
```

### Webhook Errors

```javascript
// Webhook handlers include error recovery
async handlePaymentFailed(paymentIntent) {
    try {
        // Send notification to user
        // Update subscription status
        // Implement retry logic
    } catch (error) {
        console.error('Webhook handling failed:', error);
        // Log for manual review
    }
}
```

## 🔄 Demo Mode vs Production

### Demo Mode Features
- In-memory data storage
- Simulated network delays
- Test card processing
- Local webhook simulation

### Production Considerations
- Use real Stripe API endpoints
- Implement proper database storage
- Set up webhook endpoints
- Configure proper error monitoring

## 📝 Development Workflow

### 1. Local Development

```bash
# Terminal 1: Start Stripe server
node start-stripe-server.js

# Terminal 2: Start web demo
node server.js

# Terminal 3: Run tests
open stripe-integration-test.html
```

### 2. Testing Workflow

1. Run configuration tests
2. Test customer creation
3. Test payment processing
4. Test subscription management
5. Test webhook handling
6. Run full integration tests

### 3. Debugging

Enable debug logging:

```javascript
// In stripe-config.js
this.config = {
    // ...
    debug: true,
    logLevel: 'verbose'
};
```

## 🤝 Integration with Other Components

### Authentication Manager
- Links Stripe customers to authenticated users
- Manages user session with subscription data

### Backup System
- Enforces storage limits based on subscription tier
- Tracks usage for billing purposes

### Restore System
- Applies restore speed based on subscription level
- Enforces restore limits for free users

## 📚 Additional Resources

- [Stripe Documentation](https://stripe.com/docs)
- [Stripe Elements Guide](https://stripe.com/docs/stripe-js)
- [Webhook Best Practices](https://stripe.com/docs/webhooks/best-practices)
- [Testing with Stripe](https://stripe.com/docs/testing)

## 🐛 Troubleshooting

### Common Issues

1. **Server Connection Failed**
   - Ensure Stripe server is running on port 3001
   - Check firewall settings
   - Verify configuration keys

2. **Payment Processing Errors**
   - Verify publishable key is correct
   - Check test card numbers
   - Ensure Stripe Elements is loaded

3. **Webhook Processing Issues**
   - Verify webhook secret
   - Check endpoint URL configuration
   - Review webhook event types

### Debug Commands

```bash
# Check server status
curl http://localhost:3001/api/stripe/status

# Test customer creation
curl -X POST http://localhost:3001/api/stripe/create-customer \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","name":"Test User"}'
```

## 🎯 Next Steps

1. **Enhanced Security**: Implement additional fraud detection
2. **Analytics**: Add detailed payment and subscription analytics
3. **Multi-currency**: Support for international payments
4. **Mobile Integration**: Extend to React Native app
5. **Advanced Features**: Implement proration, trials, and discounts

---

For questions or issues, please refer to the test suite or check the server logs for detailed error information.