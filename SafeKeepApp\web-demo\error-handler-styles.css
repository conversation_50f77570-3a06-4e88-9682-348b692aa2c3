/**
 * Error <PERSON> Notification Styles
 */

.safekeep-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    pointer-events: none;
}

.safekeep-notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 12px;
    padding: 16px;
    position: relative;
    pointer-events: auto;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid #007bff;
    max-width: 100%;
    word-wrap: break-word;
}

.safekeep-notification--success {
    border-left-color: #28a745;
    background: #f8fff9;
}

.safekeep-notification--warning {
    border-left-color: #ffc107;
    background: #fffef8;
}

.safekeep-notification--error {
    border-left-color: #dc3545;
    background: #fff8f8;
}

.safekeep-notification--info {
    border-left-color: #17a2b8;
    background: #f8feff;
}

.safekeep-notification__content {
    padding-right: 30px;
}

.safekeep-notification__title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #333;
}

.safekeep-notification__message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 8px;
}

.safekeep-notification__actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.safekeep-notification__action {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.safekeep-notification__action:hover {
    background: #0056b3;
}

.safekeep-notification--success .safekeep-notification__action {
    background: #28a745;
}

.safekeep-notification--success .safekeep-notification__action:hover {
    background: #1e7e34;
}

.safekeep-notification--warning .safekeep-notification__action {
    background: #ffc107;
    color: #333;
}

.safekeep-notification--warning .safekeep-notification__action:hover {
    background: #e0a800;
}

.safekeep-notification--error .safekeep-notification__action {
    background: #dc3545;
}

.safekeep-notification--error .safekeep-notification__action:hover {
    background: #c82333;
}

.safekeep-notification--info .safekeep-notification__action {
    background: #17a2b8;
}

.safekeep-notification--info .safekeep-notification__action:hover {
    background: #138496;
}

.safekeep-notification__close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.safekeep-notification__close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #666;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.safekeep-notification.removing {
    animation: slideOutRight 0.3s ease-in forwards;
}

/* Error overlay for critical errors */
.safekeep-error-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.safekeep-error-dialog {
    background: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.safekeep-error-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #dc3545;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.safekeep-error-dialog__title::before {
    content: "⚠️";
    font-size: 20px;
}

.safekeep-error-dialog__message {
    color: #666;
    line-height: 1.5;
    margin-bottom: 20px;
}

.safekeep-error-dialog__actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.safekeep-error-dialog__action {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.safekeep-error-dialog__action--primary {
    background: #007bff;
    color: white;
}

.safekeep-error-dialog__action--primary:hover {
    background: #0056b3;
}

.safekeep-error-dialog__action--secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.safekeep-error-dialog__action--secondary:hover {
    background: #e9ecef;
}

/* Offline indicator */
.safekeep-offline-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #ffc107;
    color: #333;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.safekeep-offline-indicator::before {
    content: "📡";
    font-size: 14px;
}

/* Error log viewer */
.safekeep-error-log {
    position: fixed;
    top: 50px;
    right: 20px;
    width: 400px;
    max-height: 500px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 9998;
    display: none;
}

.safekeep-error-log.visible {
    display: block;
}

.safekeep-error-log__header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.safekeep-error-log__title {
    font-weight: 600;
    color: #333;
}

.safekeep-error-log__close {
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
}

.safekeep-error-log__content {
    max-height: 400px;
    overflow-y: auto;
    padding: 16px;
}

.safekeep-error-log__entry {
    padding: 12px;
    border-left: 3px solid #ddd;
    margin-bottom: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 12px;
}

.safekeep-error-log__entry--high {
    border-left-color: #dc3545;
}

.safekeep-error-log__entry--medium {
    border-left-color: #ffc107;
}

.safekeep-error-log__entry--low {
    border-left-color: #28a745;
}

.safekeep-error-log__entry-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.safekeep-error-log__entry-category {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.safekeep-error-log__entry-time {
    color: #666;
    font-size: 10px;
}

.safekeep-error-log__entry-message {
    color: #333;
    line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
    .safekeep-notifications {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .safekeep-notification {
        margin-bottom: 8px;
    }
    
    .safekeep-error-log {
        top: 10px;
        right: 10px;
        left: 10px;
        width: auto;
    }
    
    .safekeep-offline-indicator {
        bottom: 10px;
        left: 10px;
        right: 10px;
        text-align: center;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .safekeep-notification {
        background: #2d3748;
        color: #e2e8f0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    .safekeep-notification__title {
        color: #f7fafc;
    }
    
    .safekeep-notification__message {
        color: #cbd5e0;
    }
    
    .safekeep-error-dialog {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .safekeep-error-log {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .safekeep-error-log__entry {
        background: #4a5568;
    }
}