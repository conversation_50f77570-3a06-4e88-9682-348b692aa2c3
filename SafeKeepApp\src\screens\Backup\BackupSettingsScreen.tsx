import React, { useCallback, useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Switch,
  Card,
  Divider,
  Button,
  SegmentedButtons,
  Banner,
  ActivityIndicator,
  List,
  IconButton,
} from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { updateSettings } from '../../store/slices/settingsSlice';
import { BackupConfiguration } from '../../types/backup';
import { BackupConfigurationService } from '../../services/BackupConfigurationService';
import { COLORS, SPACING } from '../../utils/constants';

const defaultConfiguration: BackupConfiguration = {
  autoBackup: false,
  wifiOnly: true,
  includeContacts: true,
  includeMessages: true,
  includePhotos: true,
  compressionLevel: 'medium',
};

export default function BackupSettingsScreen() {
  const [configuration, setConfiguration] = useState<BackupConfiguration>(defaultConfiguration);
  const [loading, setLoading] = useState(true);
  const [showDataWarning, setShowDataWarning] = useState(false);
  const [scheduleFrequency, setScheduleFrequency] = useState('daily');
  
  const dispatch = useDispatch();
  const { settings } = useSelector((state: RootState) => state.settings);

  // Load configuration from AsyncStorage on mount
  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      const config = await BackupConfigurationService.loadConfiguration();
      setConfiguration(config);
    } catch (error) {
      console.error('Failed to load backup configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveConfiguration = async (newConfig: BackupConfiguration) => {
    try {
      await BackupConfigurationService.saveConfiguration(newConfig);
      setConfiguration(newConfig);
      
      // Also update Redux store for app-wide settings
      dispatch(updateSettings({
        autoBackup: newConfig.autoBackup,
        wifiOnly: newConfig.wifiOnly,
      }));
    } catch (error) {
      console.error('Failed to save backup configuration:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const updateSetting = <K extends keyof BackupConfiguration>(
    key: K,
    value: BackupConfiguration[K]
  ) => {
    const newConfig = { ...configuration, [key]: value };
    
    // Show data warning when switching from WiFi-only to cellular
    if (key === 'wifiOnly' && value === false) {
      setShowDataWarning(true);
    } else if (key === 'wifiOnly' && value === true) {
      setShowDataWarning(false);
    }
    
    saveConfiguration(newConfig);
  };

  const handleScheduleSetup = useCallback(() => {
    if (!configuration.autoBackup) {
      Alert.alert(
        'Enable Automatic Backup',
        'Please enable automatic backup first to set up scheduling.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Backup Schedule',
      `Set automatic backup frequency to ${scheduleFrequency}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            // Update app settings with frequency
            dispatch(updateSettings({ 
              backupFrequency: scheduleFrequency as 'daily' | 'weekly' | 'monthly' | 'manual'
            }));
            Alert.alert('Success', `Automatic backup scheduled ${scheduleFrequency}.`);
          }
        }
      ]
    );
  }, [configuration.autoBackup, scheduleFrequency, dispatch]);

  const getDataTypeCount = () => {
    let count = 0;
    if (configuration.includeContacts) count++;
    if (configuration.includeMessages) count++;
    if (configuration.includePhotos) count++;
    return count;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Data Usage Warning Banner */}
      {showDataWarning && (
        <Banner
          visible={showDataWarning}
          actions={[
            {
              label: 'Dismiss',
              onPress: () => setShowDataWarning(false),
            },
          ]}
          icon="alert-circle"
          style={styles.warningBanner}
        >
          <Text style={styles.warningText}>
            Backing up over cellular data may consume significant data allowance. 
            Photos and messages can use substantial bandwidth.
          </Text>
        </Banner>
      )}

      {/* Data Types Section */}
      <Card style={styles.card}>
        <Card.Title 
          title="Data Types to Backup" 
          subtitle={`${getDataTypeCount()} of 3 types selected`}
          left={(props) => <List.Icon {...props} icon="database" />}
        />
        <Card.Content>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Contacts</Text>
              <Text style={styles.settingSubtitle}>
                Names, phone numbers, email addresses
              </Text>
            </View>
            <Switch
              value={configuration.includeContacts}
              onValueChange={(value) => updateSetting('includeContacts', value)}
              color={COLORS.primary}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Messages</Text>
              <Text style={styles.settingSubtitle}>
                SMS text messages and conversations
              </Text>
            </View>
            <Switch
              value={configuration.includeMessages}
              onValueChange={(value) => updateSetting('includeMessages', value)}
              color={COLORS.primary}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Photos</Text>
              <Text style={styles.settingSubtitle}>
                Camera roll images (videos excluded)
              </Text>
            </View>
            <Switch
              value={configuration.includePhotos}
              onValueChange={(value) => updateSetting('includePhotos', value)}
              color={COLORS.primary}
            />
          </View>
        </Card.Content>
      </Card>

      {/* Network Preferences Section */}
      <Card style={styles.card}>
        <Card.Title 
          title="Network Preferences" 
          left={(props) => <List.Icon {...props} icon="wifi" />}
        />
        <Card.Content>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>WiFi Only</Text>
              <Text style={styles.settingSubtitle}>
                {configuration.wifiOnly 
                  ? 'Backup only when connected to WiFi'
                  : 'Allow backup over cellular data'
                }
              </Text>
            </View>
            <Switch
              value={configuration.wifiOnly}
              onValueChange={(value) => updateSetting('wifiOnly', value)}
              color={COLORS.primary}
            />
          </View>

          {!configuration.wifiOnly && (
            <View style={styles.dataWarningContainer}>
              <List.Icon icon="alert-circle" color={COLORS.warning} />
              <Text style={styles.dataWarningText}>
                Cellular backups may use significant data. Monitor your usage.
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Automatic Backup Section */}
      <Card style={styles.card}>
        <Card.Title 
          title="Automatic Backup" 
          left={(props) => <List.Icon {...props} icon="backup-restore" />}
        />
        <Card.Content>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Enable Automatic Backup</Text>
              <Text style={styles.settingSubtitle}>
                Automatically backup data based on schedule
              </Text>
            </View>
            <Switch
              value={configuration.autoBackup}
              onValueChange={(value) => updateSetting('autoBackup', value)}
              color={COLORS.primary}
            />
          </View>

          {configuration.autoBackup && (
            <>
              <Divider style={styles.divider} />
              
              <Text style={styles.sectionSubtitle}>Backup Frequency</Text>
              <SegmentedButtons
                value={scheduleFrequency}
                onValueChange={setScheduleFrequency}
                buttons={[
                  { value: 'daily', label: 'Daily' },
                  { value: 'weekly', label: 'Weekly' },
                  { value: 'monthly', label: 'Monthly' },
                ]}
                style={styles.segmentedButtons}
              />

              <Button
                mode="contained"
                onPress={handleScheduleSetup}
                style={styles.scheduleButton}
                icon="calendar-clock"
              >
                Update Schedule
              </Button>
            </>
          )}
        </Card.Content>
      </Card>

      {/* Compression Settings Section */}
      <Card style={styles.card}>
        <Card.Title 
          title="Compression Settings" 
          left={(props) => <List.Icon {...props} icon="zip-box" />}
        />
        <Card.Content>
          <Text style={styles.sectionSubtitle}>Photo Compression Level</Text>
          <SegmentedButtons
            value={configuration.compressionLevel}
            onValueChange={(value) => updateSetting('compressionLevel', value as BackupConfiguration['compressionLevel'])}
            buttons={[
              { value: 'none', label: 'None' },
              { value: 'low', label: 'Low' },
              { value: 'medium', label: 'Medium' },
              { value: 'high', label: 'High' },
            ]}
            style={styles.segmentedButtons}
          />
          
          <Text style={styles.compressionNote}>
            Higher compression reduces storage usage but may affect image quality.
          </Text>
        </Card.Content>
      </Card>

      {/* Current Settings Summary */}
      <Card style={styles.card}>
        <Card.Title 
          title="Settings Summary" 
          left={(props) => <List.Icon {...props} icon="information" />}
        />
        <Card.Content>
          <Text style={styles.summaryText}>
            • Data types: {getDataTypeCount()} of 3 selected{'\n'}
            • Network: {configuration.wifiOnly ? 'WiFi only' : 'WiFi + Cellular'}{'\n'}
            • Auto backup: {configuration.autoBackup ? 'Enabled' : 'Disabled'}{'\n'}
            • Compression: {configuration.compressionLevel}
          </Text>
        </Card.Content>
      </Card>
    </ScrollView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  warningBanner: {
    margin: SPACING.md,
    backgroundColor: COLORS.warning + '20',
  },
  warningText: {
    color: COLORS.text,
    fontSize: 14,
  },
  card: {
    margin: SPACING.md,
    marginBottom: SPACING.sm,
    backgroundColor: COLORS.surface,
    elevation: 2,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  settingInfo: {
    flex: 1,
    marginRight: SPACING.md,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    lineHeight: 18,
  },
  divider: {
    marginVertical: SPACING.sm,
    backgroundColor: COLORS.border,
  },
  dataWarningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.sm,
    padding: SPACING.sm,
    backgroundColor: COLORS.warning + '10',
    borderRadius: 8,
  },
  dataWarningText: {
    flex: 1,
    marginLeft: SPACING.sm,
    fontSize: 14,
    color: COLORS.warning,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm,
  },
  segmentedButtons: {
    marginBottom: SPACING.md,
  },
  scheduleButton: {
    marginTop: SPACING.sm,
    backgroundColor: COLORS.primary,
  },
  compressionNote: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: SPACING.sm,
    fontStyle: 'italic',
  },
  summaryText: {
    fontSize: 14,
    color: COLORS.text,
    lineHeight: 20,
  },
});