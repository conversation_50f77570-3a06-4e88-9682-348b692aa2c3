// Backup Session Manager for SafeKeep
// Handles tracking and managing backup operations

import { supabase } from '../config/supabase';
import { getCurrentUserId } from './supabaseHelpers';

export interface BackupSession {
  id: string;
  user_id: string;
  session_type: 'manual' | 'automatic';
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at: string | null;
  total_files: number;
  processed_files: number;
  failed_files: number;
  total_bytes: number;
  processed_bytes: number;
  error_message: string | null;
  device_id?: string;
}

export interface BackupProgress {
  sessionId: string;
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  totalBytes: number;
  processedBytes: number;
  percentage: number;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime: Date | null;
  errorMessage: string | null;
}

/**
 * Create a new backup session
 */
export const createBackupSession = async (
  sessionType: 'manual' | 'automatic',
  totalFiles: number,
  totalBytes: number,
  deviceId?: string
): Promise<{ sessionId: string | null; error: string | null }> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { sessionId: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('backup_sessions')
      .insert({
        user_id: userId,
        session_type: sessionType,
        status: 'running',
        total_files: totalFiles,
        processed_files: 0,
        failed_files: 0,
        total_bytes: totalBytes,
        processed_bytes: 0,
        device_id: deviceId || 'unknown'
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error creating backup session:', error);
      return { sessionId: null, error: error.message };
    }

    return { sessionId: data.id, error: null };
  } catch (error) {
    console.error('Exception creating backup session:', error);
    return { sessionId: null, error: error.message };
  }
};

/**
 * Update backup session progress
 */
export const updateBackupProgress = async (
  sessionId: string,
  progress: {
    processedFiles?: number;
    failedFiles?: number;
    processedBytes?: number;
    status?: 'running' | 'completed' | 'failed' | 'cancelled';
    errorMessage?: string;
  }
): Promise<{ success: boolean; error: string | null }> => {
  try {
    const updateData: any = { ...progress };
    
    if (progress.status === 'completed' || progress.status === 'failed' || progress.status === 'cancelled') {
      updateData.completed_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('backup_sessions')
      .update(updateData)
      .eq('id', sessionId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Get backup session details
 */
export const getBackupSession = async (sessionId: string): Promise<{ 
  session: BackupSession | null; 
  error: string | null 
}> => {
  try {
    const { data, error } = await supabase
      .from('backup_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error) {
      return { session: null, error: error.message };
    }

    return { session: data as BackupSession, error: null };
  } catch (error) {
    return { session: null, error: error.message };
  }
};

/**
 * Get backup session progress
 */
export const getBackupProgress = async (sessionId: string): Promise<{ 
  progress: BackupProgress | null; 
  error: string | null 
}> => {
  try {
    const { session, error } = await getBackupSession(sessionId);
    
    if (error || !session) {
      return { progress: null, error: error || 'Session not found' };
    }

    const progress: BackupProgress = {
      sessionId: session.id,
      totalFiles: session.total_files,
      processedFiles: session.processed_files,
      failedFiles: session.failed_files,
      totalBytes: session.total_bytes,
      processedBytes: session.processed_bytes,
      percentage: session.total_files > 0 
        ? Math.round((session.processed_files / session.total_files) * 100) 
        : 0,
      status: session.status,
      startTime: new Date(session.started_at),
      endTime: session.completed_at ? new Date(session.completed_at) : null,
      errorMessage: session.error_message
    };

    return { progress, error: null };
  } catch (error) {
    return { progress: null, error: error.message };
  }
};

/**
 * Get user's backup history
 */
export const getBackupHistory = async (limit: number = 10): Promise<{ 
  sessions: BackupSession[]; 
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { sessions: [], error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('backup_sessions')
      .select('*')
      .eq('user_id', userId)
      .order('started_at', { ascending: false })
      .limit(limit);

    if (error) {
      return { sessions: [], error: error.message };
    }

    return { sessions: data as BackupSession[], error: null };
  } catch (error) {
    return { sessions: [], error: error.message };
  }
};

/**
 * Cancel an active backup session
 */
export const cancelBackupSession = async (sessionId: string): Promise<{ 
  success: boolean; 
  error: string | null 
}> => {
  try {
    const { error } = await supabase
      .from('backup_sessions')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString(),
        error_message: 'Cancelled by user'
      })
      .eq('id', sessionId)
      .eq('status', 'running');

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Get backup statistics
 */
export const getBackupStats = async (): Promise<{
  totalBackups: number;
  lastBackupDate: Date | null;
  totalBackedUpFiles: number;
  error: string | null;
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        totalBackups: 0, 
        lastBackupDate: null, 
        totalBackedUpFiles: 0, 
        error: 'User not authenticated' 
      };
    }

    // Get total completed backups
    const { count: totalBackups, error: countError } = await supabase
      .from('backup_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('status', 'completed');

    if (countError) {
      return { 
        totalBackups: 0, 
        lastBackupDate: null, 
        totalBackedUpFiles: 0, 
        error: countError.message 
      };
    }

    // Get last successful backup
    const { data: lastBackup, error: lastError } = await supabase
      .from('backup_sessions')
      .select('completed_at')
      .eq('user_id', userId)
      .eq('status', 'completed')
      .order('completed_at', { ascending: false })
      .limit(1)
      .single();

    // Get total backed up files
    const { data: fileCount, error: fileError } = await supabase
      .from('file_metadata')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_backed_up', true);

    if (fileError) {
      return { 
        totalBackups: totalBackups || 0, 
        lastBackupDate: lastBackup ? new Date(lastBackup.completed_at) : null, 
        totalBackedUpFiles: 0, 
        error: fileError.message 
      };
    }

    return {
      totalBackups: totalBackups || 0,
      lastBackupDate: lastBackup ? new Date(lastBackup.completed_at) : null,
      totalBackedUpFiles: fileCount || 0,
      error: null
    };
  } catch (error) {
    return { 
      totalBackups: 0, 
      lastBackupDate: null, 
      totalBackedUpFiles: 0, 
      error: error.message 
    };
  }
};