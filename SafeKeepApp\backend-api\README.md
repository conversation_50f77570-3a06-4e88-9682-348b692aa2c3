# SafeKeep Modular Pricing Backend API

This backend API supports SafeKeep's modular pricing system, allowing users to select individual services (Contacts, Messages, Photos) or combination packages.

## Project Structure

```
SafeKeepApp/backend-api/
├── src/
│   ├── types/
│   │   └── modular-pricing.ts      # TypeScript interfaces and types
│   ├── controllers/
│   │   ├── PricingController.ts    # Pricing calculation endpoints
│   │   ├── SubscriptionController.ts # Subscription management endpoints
│   │   ├── ServiceController.ts    # Service validation and access endpoints
│   │   └── BillingController.ts    # Billing and payment endpoints
│   ├── services/
│   │   ├── PricingEngine.ts        # Pricing calculation business logic
│   │   ├── SubscriptionManager.ts  # Subscription lifecycle management
│   │   ├── ServiceValidator.ts     # Service combination validation
│   │   └── BillingIntegration.ts   # Stripe integration service
│   ├── middleware/
│   │   ├── auth.ts                 # Authentication and authorization
│   │   └── errorHandler.ts         # Error handling middleware
│   ├── routes/
│   │   ├── pricing.ts              # Pricing routes
│   │   ├── subscriptions.ts        # Subscription routes
│   │   ├── services.ts             # Service routes
│   │   ├── billing.ts              # Billing routes
│   │   └── index.ts                # Main routes configuration
│   └── app.ts                      # Express app configuration
├── server.ts                       # Server startup
├── package.json                    # Dependencies and scripts
├── tsconfig.json                   # TypeScript configuration
└── README.md                       # This file
```

## API Endpoints

### Pricing Endpoints
- `GET /api/pricing/combinations` - Get available service combinations
- `POST /api/pricing/calculate` - Calculate optimal pricing for selected services
- `GET /api/pricing/recommendations/:userId` - Get plan recommendations

### Subscription Endpoints
- `POST /api/subscriptions` - Create new subscription
- `PUT /api/subscriptions/:subscriptionId` - Update subscription services
- `GET /api/subscriptions/:userId` - Get subscription details
- `DELETE /api/subscriptions/:subscriptionId` - Cancel subscription

### Service Endpoints
- `GET /api/services/user/:userId` - Get user's active services
- `POST /api/services/validate` - Validate service combination
- `GET /api/services/access/:userId/:serviceType` - Check service access

### Billing Endpoints
- `POST /api/billing/payment-intent` - Create Stripe payment intent
- `POST /api/billing/webhook` - Handle Stripe webhooks
- `GET /api/billing/status/:userId` - Get billing status

## Development

### Prerequisites
- Node.js 18+
- npm or yarn
- TypeScript

### Installation
```bash
cd SafeKeepApp/backend-api
npm install
```

### Development Scripts
```bash
npm run dev          # Start development server with hot reload
npm run dev:watch    # Start with file watching
npm run build        # Build TypeScript to JavaScript
npm run start        # Start production server
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
```

### Environment Variables
Create a `.env` file in the backend-api directory:
```env
PORT=3000
NODE_ENV=development
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_webhook_secret
JWT_SECRET=your_jwt_secret
DATABASE_URL=your_database_url
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081
```

## Implementation Status

This is the initial structure setup. Individual components will be implemented in subsequent tasks:

- ✅ Task 1: Set up modular pricing backend API structure
- ⏳ Task 2: Implement Pricing Engine service
- ⏳ Task 3: Implement Service Validator
- ⏳ Task 4: Implement Subscription Manager
- ⏳ Task 5: Implement Billing Integration service
- ⏳ Task 6: Create API controllers and routes
- ⏳ Task 7: Add middleware and error handling
- ⏳ Task 8: Update existing backend API integration
- ⏳ Task 9: Write integration tests
- ⏳ Task 10: Create end-to-end API tests

## Architecture

The API follows a layered architecture:
1. **Routes** - Handle HTTP requests and responses
2. **Controllers** - Coordinate between routes and services
3. **Services** - Contain business logic
4. **Middleware** - Handle cross-cutting concerns (auth, errors)
5. **Types** - TypeScript interfaces and type definitions

All endpoints require authentication except for Stripe webhooks. The API uses JWT tokens for authentication and includes comprehensive error handling.