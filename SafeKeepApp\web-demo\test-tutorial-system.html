<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial System Test - SafeKeep Demo</title>
    <link rel="stylesheet" href="tutorial-system-styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .main-content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .test-section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4facfe;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .demo-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.2s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
        }

        .demo-card h3 {
            color: #4facfe;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .demo-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.2s ease;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .auth-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .auth-form {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .auth-form input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .backup-console {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .encryption-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            display: none;
        }

        .algorithm-selector {
            display: grid;
            gap: 10px;
            margin-bottom: 20px;
        }

        .subscription-dashboard {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            display: none;
        }

        .test-controls {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-controls h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .control-btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: #1565c0;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.success {
            background: #28a745;
        }

        .status-indicator.error {
            background: #dc3545;
        }

        .status-indicator.warning {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>Tutorial System Test</h1>
            <p>Interactive testing environment for the comprehensive tutorial system</p>
        </div>

        <div class="main-content">
            <div class="test-controls">
                <h3>Tutorial System Controls</h3>
                <div class="control-grid">
                    <button class="control-btn" onclick="testTutorialSystem()">Initialize Tutorial System</button>
                    <button class="control-btn" onclick="startGettingStartedTutorial()">Start Getting Started</button>
                    <button class="control-btn" onclick="startBackupTutorial()">Start Backup Tutorial</button>
                    <button class="control-btn" onclick="startEncryptionTutorial()">Start Encryption Tutorial</button>
                    <button class="control-btn" onclick="showTooltipTest()">Test Tooltip System</button>
                    <button class="control-btn" onclick="testProgressTracking()">Test Progress Tracking</button>
                    <button class="control-btn" onclick="testContextualHelp()">Test Contextual Help</button>
                    <button class="control-btn" onclick="resetTutorialState()">Reset Tutorial State</button>
                </div>
            </div>

            <div class="auth-section">
                <h3>Authentication System</h3>
                <p>Sign up or sign in to test personalized tutorial features</p>
                <div class="auth-form">
                    <input type="email" id="email" placeholder="Email address">
                    <input type="password" id="password" placeholder="Password">
                    <button class="btn" onclick="simulateAuth()">Sign In</button>
                </div>
                <div id="auth-status"></div>
            </div>

            <div class="test-section">
                <h2>Demo Features</h2>
                <div class="demo-grid">
                    <div class="demo-card">
                        <h3>Real-time Backup</h3>
                        <p>Experience live backup progress with detailed metrics and real-time updates.</p>
                        <button class="btn" onclick="startRealtimeBackupDemo()">Start Real-time Backup Demo</button>
                    </div>

                    <div class="demo-card">
                        <h3>Encryption Demo</h3>
                        <p>See how your data is encrypted and protected with advanced security algorithms.</p>
                        <button class="btn" onclick="startEncryptionDemo()">Start Encryption Demo</button>
                    </div>

                    <div class="demo-card">
                        <h3>Backup History</h3>
                        <p>View comprehensive backup history with detailed session information.</p>
                        <button class="btn" onclick="showBackupHistory()">View Backup History</button>
                    </div>

                    <div class="demo-card">
                        <h3>Backup Scheduling</h3>
                        <p>Configure automated backups with advanced scheduling options.</p>
                        <button class="btn" onclick="showScheduleDemo()">Configure Backup Schedule</button>
                    </div>

                    <div class="demo-card">
                        <h3>Subscription Management</h3>
                        <p>Explore premium features and subscription options.</p>
                        <button class="btn" onclick="showSubscriptionDemo()">Manage Subscription</button>
                    </div>

                    <div class="demo-card">
                        <h3>Data Restoration</h3>
                        <p>Learn how to restore your backed-up data securely.</p>
                        <button class="btn" onclick="startRestoreDemo()">Start Restore Demo</button>
                    </div>
                </div>
            </div>

            <!-- Demo Components (initially hidden) -->
            <div class="backup-console" id="backup-console">
                <h3>Real-time Backup Console</h3>
                <div class="phase-progress">
                    <div class="phase-section">
                        <div class="phase-header">
                            <span>Contacts Backup</span>
                            <span>75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                    </div>
                </div>
                <div class="performance-metrics">
                    <h4>Performance Metrics</h4>
                    <div class="metrics-grid">
                        <div class="metric">
                            <div class="metric-label">Transfer Rate</div>
                            <div class="metric-value">2.5 MB/s</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Files Processed</div>
                            <div class="metric-value">1,247</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="encryption-demo" id="encryption-demo">
                <h3>Encryption Demonstration</h3>
                <div class="algorithm-selector">
                    <div class="algorithm-option">
                        <input type="radio" name="algorithm" value="aes-256" checked>
                        <div class="algorithm-label">
                            <strong>AES-256-GCM</strong>
                            <small>Industry standard encryption with authenticated encryption</small>
                        </div>
                    </div>
                </div>
                <div class="key-section">
                    <h4>Encryption Keys</h4>
                    <p>Keys are generated securely and never stored in plain text.</p>
                </div>
                <div class="comparison-section">
                    <h4>Data Comparison</h4>
                    <p>See the difference between original and encrypted data.</p>
                </div>
            </div>

            <div class="subscription-dashboard" id="subscription-dashboard">
                <h3>Subscription Management</h3>
                <div class="subscription-tiers">
                    <h4>Available Plans</h4>
                    <p>Compare different subscription tiers and features.</p>
                </div>
                <div class="feature-comparison">
                    <h4>Feature Comparison</h4>
                    <p>See what additional features you get with premium subscriptions.</p>
                </div>
                <div class="payment-interface">
                    <h4>Payment Processing</h4>
                    <p>Secure payment flow powered by Stripe.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="tutorial-system.js"></script>
    <script>
        // Global variables for testing
        let currentUser = null;
        let tutorialSystemInstance = null;

        // Test functions
        function testTutorialSystem() {
            console.log('Testing tutorial system initialization...');
            
            // Check if tutorial system is loaded
            if (typeof TutorialSystem !== 'undefined') {
                console.log('✅ Tutorial System class is available');
                
                // Initialize if not already done
                if (!window.tutorialSystem) {
                    window.tutorialSystem = new TutorialSystem();
                    tutorialSystemInstance = window.tutorialSystem;
                }
                
                console.log('✅ Tutorial System initialized');
                showStatus('Tutorial System initialized successfully', 'success');
            } else {
                console.error('❌ Tutorial System class not found');
                showStatus('Tutorial System failed to load', 'error');
            }
        }

        function startGettingStartedTutorial() {
            if (tutorialSystemInstance) {
                tutorialSystemInstance.startTutorial('getting-started');
                showStatus('Started Getting Started tutorial', 'success');
            } else {
                showStatus('Tutorial system not initialized', 'error');
            }
        }

        function startBackupTutorial() {
            if (tutorialSystemInstance) {
                tutorialSystemInstance.startTutorial('backup-process');
                showStatus('Started Backup Process tutorial', 'success');
            } else {
                showStatus('Tutorial system not initialized', 'error');
            }
        }

        function startEncryptionTutorial() {
            if (tutorialSystemInstance) {
                tutorialSystemInstance.startTutorial('encryption-security');
                showStatus('Started Encryption Security tutorial', 'success');
            } else {
                showStatus('Tutorial system not initialized', 'error');
            }
        }

        function showTooltipTest() {
            if (tutorialSystemInstance) {
                const testElement = document.querySelector('.demo-card');
                tutorialSystemInstance.showTooltip(
                    testElement,
                    'Test Tooltip',
                    'This is a test tooltip to demonstrate the tooltip system functionality.',
                    'top'
                );
                showStatus('Tooltip test displayed', 'success');
            } else {
                showStatus('Tutorial system not initialized', 'error');
            }
        }

        function testProgressTracking() {
            if (tutorialSystemInstance) {
                tutorialSystemInstance.updateProgress();
                showStatus('Progress tracking test completed', 'success');
            } else {
                showStatus('Tutorial system not initialized', 'error');
            }
        }

        function testContextualHelp() {
            if (tutorialSystemInstance && tutorialSystemInstance.contextualHelp) {
                const testElement = document.querySelector('.auth-section');
                const helpData = tutorialSystemInstance.contextualHelp.getHelpForElement(testElement);
                
                if (helpData) {
                    showStatus('Contextual help data found', 'success');
                } else {
                    showStatus('No contextual help data for element', 'warning');
                }
            } else {
                showStatus('Tutorial system not initialized', 'error');
            }
        }

        function resetTutorialState() {
            if (tutorialSystemInstance) {
                tutorialSystemInstance.resetAllProgress();
                showStatus('Tutorial state reset', 'success');
            } else {
                showStatus('Tutorial system not initialized', 'error');
            }
        }

        function simulateAuth() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (email && password) {
                currentUser = { email, id: 'test-user-123' };
                document.getElementById('auth-status').innerHTML = 
                    '<div style="color: green; margin-top: 10px;">✅ Signed in as ' + email + '</div>';
                showStatus('Authentication successful', 'success');
            } else {
                showStatus('Please enter email and password', 'error');
            }
        }

        function startRealtimeBackupDemo() {
            document.getElementById('backup-console').style.display = 'block';
            showStatus('Real-time backup demo started', 'success');
        }

        function startEncryptionDemo() {
            document.getElementById('encryption-demo').style.display = 'block';
            showStatus('Encryption demo started', 'success');
        }

        function showBackupHistory() {
            showStatus('Backup history would be displayed here', 'success');
        }

        function showScheduleDemo() {
            showStatus('Schedule demo would be displayed here', 'success');
        }

        function showSubscriptionDemo() {
            document.getElementById('subscription-dashboard').style.display = 'block';
            showStatus('Subscription demo started', 'success');
        }

        function startRestoreDemo() {
            showStatus('Restore demo would be displayed here', 'success');
        }

        function showStatus(message, type) {
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10003;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            
            switch (type) {
                case 'success':
                    statusDiv.style.background = '#28a745';
                    break;
                case 'error':
                    statusDiv.style.background = '#dc3545';
                    break;
                case 'warning':
                    statusDiv.style.background = '#ffc107';
                    statusDiv.style.color = '#000';
                    break;
                default:
                    statusDiv.style.background = '#17a2b8';
            }
            
            statusDiv.textContent = message;
            document.body.appendChild(statusDiv);
            
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.remove();
                }
            }, 3000);
        }

        // Initialize tutorial system when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testTutorialSystem();
            }, 1000);
        });

        // Add keyboard shortcuts for testing
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey) {
                switch (e.key) {
                    case 'T':
                        e.preventDefault();
                        testTutorialSystem();
                        break;
                    case 'G':
                        e.preventDefault();
                        startGettingStartedTutorial();
                        break;
                    case 'B':
                        e.preventDefault();
                        startBackupTutorial();
                        break;
                    case 'E':
                        e.preventDefault();
                        startEncryptionTutorial();
                        break;
                    case 'R':
                        e.preventDefault();
                        resetTutorialState();
                        break;
                }
            }
        });
    </script>
</body>
</html>