/**
 * Language Selector Component
 * Interactive language selection with currency conversion
 */

class LanguageSelector {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.options = {
            showFlags: true,
            showNativeNames: true,
            showCurrency: true,
            compact: false,
            position: 'bottom-right',
            ...options
        };
        
        this.isOpen = false;
        this.currencyRates = new Map();
        this.baseCurrency = 'USD';
        
        this.init();
    }

    init() {
        this.loadCurrencyRates();
        this.render();
        this.bindEvents();
        
        // Listen for language changes
        window.addEventListener('languageChanged', (event) => {
            this.updateDisplay();
            this.updateCurrencyDisplay();
        });
    }

    loadCurrencyRates() {
        // Static currency rates (in a real app, these would be fetched from an API)
        // Rates relative to USD
        this.currencyRates.set('USD', 1.00);
        this.currencyRates.set('EUR', 0.85);
        this.currencyRates.set('GBP', 0.73);
        this.currencyRates.set('JPY', 110.0);
        this.currencyRates.set('CNY', 6.45);
        this.currencyRates.set('KRW', 1180.0);
        this.currencyRates.set('INR', 74.5);
        this.currencyRates.set('RUB', 73.2);
        this.currencyRates.set('SAR', 3.75);
        this.currencyRates.set('CAD', 1.25);
        this.currencyRates.set('AUD', 1.35);
        this.currencyRates.set('CHF', 0.92);
        this.currencyRates.set('SEK', 8.45);
        this.currencyRates.set('NOK', 8.65);
        this.currencyRates.set('DKK', 6.32);
        this.currencyRates.set('PLN', 3.85);
        this.currencyRates.set('CZK', 21.5);
        this.currencyRates.set('HUF', 295.0);
        this.currencyRates.set('RON', 4.15);
        this.currencyRates.set('BGN', 1.66);
        this.currencyRates.set('HRK', 6.42);
        this.currencyRates.set('TRY', 8.45);
        this.currencyRates.set('ILS', 3.25);
        this.currencyRates.set('ZAR', 14.8);
        this.currencyRates.set('BRL', 5.25);
        this.currencyRates.set('MXN', 20.1);
        this.currencyRates.set('ARS', 98.5);
        this.currencyRates.set('CLP', 785.0);
        this.currencyRates.set('COP', 3650.0);
        this.currencyRates.set('PEN', 3.95);
        this.currencyRates.set('UYU', 43.2);
        this.currencyRates.set('THB', 31.5);
        this.currencyRates.set('SGD', 1.35);
        this.currencyRates.set('MYR', 4.15);
        this.currencyRates.set('IDR', 14250.0);
        this.currencyRates.set('PHP', 50.5);
        this.currencyRates.set('VND', 23100.0);
        this.currencyRates.set('EGP', 15.7);
        this.currencyRates.set('MAD', 8.95);
        this.currencyRates.set('TND', 2.75);
        this.currencyRates.set('DZD', 135.0);
        this.currencyRates.set('NGN', 411.0);
        this.currencyRates.set('KES', 108.0);
        this.currencyRates.set('GHS', 5.85);
        this.currencyRates.set('UGX', 3550.0);
        this.currencyRates.set('TZS', 2315.0);
        this.currencyRates.set('ZMW', 16.8);
        this.currencyRates.set('BWP', 11.2);
        this.currencyRates.set('MUR', 42.5);
        this.currencyRates.set('SCR', 15.1);
    }

    render() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`Language selector container not found: ${this.containerId}`);
            return;
        }

        const currentLang = i18n.getCurrentLanguageInfo();
        
        container.innerHTML = `
            <div class="language-selector ${this.options.compact ? 'compact' : ''}" data-position="${this.options.position}">
                <button class="language-selector-trigger" aria-label="Select Language" aria-expanded="false">
                    ${this.options.showFlags ? `<span class="flag">${currentLang.flag}</span>` : ''}
                    <span class="language-name">${this.options.showNativeNames ? currentLang.nativeName : currentLang.name}</span>
                    <span class="dropdown-arrow">▼</span>
                </button>
                
                <div class="language-selector-dropdown" role="menu">
                    <div class="dropdown-header">
                        <h3 data-i18n="common.language">Language</h3>
                        ${this.options.showCurrency ? '<div class="currency-info" id="currency-info"></div>' : ''}
                    </div>
                    
                    <div class="language-list" role="group">
                        ${this.renderLanguageList()}
                    </div>
                    
                    ${this.options.showCurrency ? this.renderCurrencyConverter() : ''}
                </div>
            </div>
        `;

        this.updateCurrencyDisplay();
    }

    renderLanguageList() {
        const languages = i18n.getSupportedLanguages();
        const currentLang = i18n.getCurrentLanguage();
        
        return languages.map(lang => `
            <button class="language-option ${lang.code === currentLang ? 'active' : ''}" 
                    data-language="${lang.code}" 
                    role="menuitem"
                    aria-selected="${lang.code === currentLang}">
                ${this.options.showFlags ? `<span class="flag">${lang.flag}</span>` : ''}
                <div class="language-info">
                    <span class="language-name">${lang.name}</span>
                    ${this.options.showNativeNames ? `<span class="native-name">${lang.nativeName}</span>` : ''}
                </div>
                ${this.options.showCurrency ? `<span class="currency-code">${lang.currency}</span>` : ''}
                ${lang.code === currentLang ? '<span class="check-mark">✓</span>' : ''}
            </button>
        `).join('');
    }

    renderCurrencyConverter() {
        return `
            <div class="currency-converter">
                <div class="converter-header">
                    <h4 data-i18n="common.currency">Currency Converter</h4>
                </div>
                <div class="converter-content">
                    <div class="conversion-row">
                        <input type="number" id="amount-input" value="100" min="0" step="0.01" class="amount-input">
                        <span class="base-currency">USD</span>
                        <span class="conversion-arrow">→</span>
                        <span class="converted-amount" id="converted-amount">-</span>
                        <span class="target-currency" id="target-currency">-</span>
                    </div>
                    <div class="exchange-rate" id="exchange-rate">-</div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        const container = document.getElementById(this.containerId);
        if (!container) return;

        // Toggle dropdown
        const trigger = container.querySelector('.language-selector-trigger');
        if (trigger) {
            trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggle();
            });
        }

        // Language selection
        const languageOptions = container.querySelectorAll('.language-option');
        languageOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const languageCode = e.currentTarget.dataset.language;
                this.selectLanguage(languageCode);
            });
        });

        // Currency converter
        const amountInput = container.querySelector('#amount-input');
        if (amountInput) {
            amountInput.addEventListener('input', () => {
                this.updateCurrencyConversion();
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                this.close();
            }
        });

        // Keyboard navigation
        container.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });

        // Close on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        const container = document.getElementById(this.containerId);
        const dropdown = container.querySelector('.language-selector-dropdown');
        const trigger = container.querySelector('.language-selector-trigger');
        
        dropdown.classList.add('open');
        trigger.setAttribute('aria-expanded', 'true');
        this.isOpen = true;

        // Focus first language option
        const firstOption = dropdown.querySelector('.language-option');
        if (firstOption) {
            firstOption.focus();
        }

        // Update currency conversion
        this.updateCurrencyConversion();
    }

    close() {
        const container = document.getElementById(this.containerId);
        const dropdown = container.querySelector('.language-selector-dropdown');
        const trigger = container.querySelector('.language-selector-trigger');
        
        dropdown.classList.remove('open');
        trigger.setAttribute('aria-expanded', 'false');
        this.isOpen = false;
    }

    async selectLanguage(languageCode) {
        try {
            const success = await i18n.setLanguage(languageCode);
            if (success) {
                this.updateDisplay();
                this.updateCurrencyDisplay();
                this.close();
                
                // Show success message
                this.showNotification(i18n.translate('messages.settings_saved'), 'success');
            } else {
                this.showNotification(i18n.translate('messages.settings_failed'), 'error');
            }
        } catch (error) {
            console.error('Failed to select language:', error);
            this.showNotification(i18n.translate('messages.settings_failed'), 'error');
        }
    }

    updateDisplay() {
        const container = document.getElementById(this.containerId);
        if (!container) return;

        const currentLang = i18n.getCurrentLanguageInfo();
        const trigger = container.querySelector('.language-selector-trigger');
        
        if (trigger) {
            const flag = trigger.querySelector('.flag');
            const name = trigger.querySelector('.language-name');
            
            if (flag) flag.textContent = currentLang.flag;
            if (name) name.textContent = this.options.showNativeNames ? currentLang.nativeName : currentLang.name;
        }

        // Update active state in dropdown
        const options = container.querySelectorAll('.language-option');
        options.forEach(option => {
            const isActive = option.dataset.language === currentLang.code;
            option.classList.toggle('active', isActive);
            option.setAttribute('aria-selected', isActive);
            
            const checkMark = option.querySelector('.check-mark');
            if (checkMark) {
                checkMark.style.display = isActive ? 'inline' : 'none';
            }
        });

        // Translate the dropdown
        i18n.translatePage();
    }

    updateCurrencyDisplay() {
        if (!this.options.showCurrency) return;

        const container = document.getElementById(this.containerId);
        if (!container) return;

        const currentLang = i18n.getCurrentLanguageInfo();
        const currencyInfo = container.querySelector('#currency-info');
        const targetCurrency = container.querySelector('#target-currency');
        
        if (currencyInfo) {
            currencyInfo.textContent = `Currency: ${currentLang.currency}`;
        }
        
        if (targetCurrency) {
            targetCurrency.textContent = currentLang.currency;
        }

        this.updateCurrencyConversion();
    }

    updateCurrencyConversion() {
        if (!this.options.showCurrency) return;

        const container = document.getElementById(this.containerId);
        if (!container) return;

        const amountInput = container.querySelector('#amount-input');
        const convertedAmount = container.querySelector('#converted-amount');
        const exchangeRate = container.querySelector('#exchange-rate');
        
        if (!amountInput || !convertedAmount || !exchangeRate) return;

        const amount = parseFloat(amountInput.value) || 0;
        const currentLang = i18n.getCurrentLanguageInfo();
        const targetCurrency = currentLang.currency;
        
        const rate = this.currencyRates.get(targetCurrency) || 1;
        const converted = amount * rate;
        
        convertedAmount.textContent = i18n.formatCurrency(converted, targetCurrency);
        exchangeRate.textContent = `1 USD = ${rate} ${targetCurrency}`;
    }

    handleKeyboard(e) {
        if (!this.isOpen) return;

        const container = document.getElementById(this.containerId);
        const options = Array.from(container.querySelectorAll('.language-option'));
        const currentFocus = document.activeElement;
        const currentIndex = options.indexOf(currentFocus);

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                const nextIndex = (currentIndex + 1) % options.length;
                options[nextIndex].focus();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
                options[prevIndex].focus();
                break;
                
            case 'Enter':
            case ' ':
                e.preventDefault();
                if (currentFocus && currentFocus.classList.contains('language-option')) {
                    const languageCode = currentFocus.dataset.language;
                    this.selectLanguage(languageCode);
                }
                break;
                
            case 'Escape':
                e.preventDefault();
                this.close();
                break;
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `language-notification ${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            backgroundColor: type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'
        });
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Public API methods
    getCurrentLanguage() {
        return i18n.getCurrentLanguage();
    }

    getSupportedLanguages() {
        return i18n.getSupportedLanguages();
    }

    convertCurrency(amount, fromCurrency, toCurrency) {
        const fromRate = this.currencyRates.get(fromCurrency) || 1;
        const toRate = this.currencyRates.get(toCurrency) || 1;
        
        // Convert to USD first, then to target currency
        const usdAmount = amount / fromRate;
        return usdAmount * toRate;
    }

    getExchangeRate(fromCurrency, toCurrency) {
        const fromRate = this.currencyRates.get(fromCurrency) || 1;
        const toRate = this.currencyRates.get(toCurrency) || 1;
        
        return toRate / fromRate;
    }

    updateCurrencyRates(rates) {
        // Allow external updates to currency rates
        Object.entries(rates).forEach(([currency, rate]) => {
            this.currencyRates.set(currency, rate);
        });
        
        this.updateCurrencyConversion();
    }

    destroy() {
        const container = document.getElementById(this.containerId);
        if (container) {
            container.innerHTML = '';
        }
        
        // Remove event listeners
        window.removeEventListener('languageChanged', this.updateDisplay);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageSelector;
}