/**
 * Backup History and Analytics Dashboard
 * Provides comprehensive backup history visualization and analytics
 */

class BackupHistoryDashboard {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.historyManager = null;
        this.charts = {};
        this.currentView = 'overview';
        this.selectedSession = null;
        
        if (!this.container) {
            throw new Error(`Container with ID '${containerId}' not found`);
        }
        
        this.render();
    }

    /**
     * Set the backup history manager
     */
    setHistoryManager(historyManager) {
        this.historyManager = historyManager;
        
        // Listen for updates
        this.historyManager.addListener((event, data) => {
            this.handleHistoryUpdate(event, data);
        });
        
        // Initial render with data - delay to ensure DOM is ready
        setTimeout(() => {
            this.updateDashboard();
        }, 100);
    }

    /**
     * Handle history manager updates
     */
    handleHistoryUpdate(event, data) {
        switch (event) {
            case 'analytics_updated':
                this.updateAnalytics();
                this.updateCharts();
                break;
            case 'session_added':
            case 'session_updated':
                this.updateSessionsList();
                this.updateAnalytics();
                this.updateCharts();
                break;
            case 'restore_started':
            case 'restore_progress':
            case 'restore_completed':
                this.updateRestoreStatus(data);
                break;
        }
    }

    /**
     * Render the dashboard structure
     */
    render() {
        this.container.innerHTML = `
            <div class="backup-history-dashboard">
                <div class="dashboard-header">
                    <h3>📊 Backup History & Analytics</h3>
                    <div class="dashboard-controls">
                        <button class="btn" onclick="backupHistoryDashboard.switchView('overview')" id="overview-btn">Overview</button>
                        <button class="btn secondary" onclick="backupHistoryDashboard.switchView('sessions')" id="sessions-btn">Sessions</button>
                        <button class="btn secondary" onclick="backupHistoryDashboard.switchView('analytics')" id="analytics-btn">Analytics</button>
                        <button class="btn secondary" onclick="backupHistoryDashboard.switchView('restore')" id="restore-btn">Restore</button>
                    </div>
                </div>

                <div class="dashboard-content">
                    <!-- Overview Tab -->
                    <div id="overview-tab" class="dashboard-tab active">
                        <div class="analytics-summary">
                            <div class="summary-cards">
                                <div class="summary-card">
                                    <div class="card-icon">📈</div>
                                    <div class="card-content">
                                        <div class="card-value" id="total-sessions-count">0</div>
                                        <div class="card-label">Total Sessions</div>
                                    </div>
                                </div>
                                <div class="summary-card success">
                                    <div class="card-icon">✅</div>
                                    <div class="card-content">
                                        <div class="card-value" id="success-rate-percent">0%</div>
                                        <div class="card-label">Success Rate</div>
                                    </div>
                                </div>
                                <div class="summary-card info">
                                    <div class="card-icon">💾</div>
                                    <div class="card-content">
                                        <div class="card-value" id="total-data-size">0 MB</div>
                                        <div class="card-label">Data Backed Up</div>
                                    </div>
                                </div>
                                <div class="summary-card warning">
                                    <div class="card-icon">⏱️</div>
                                    <div class="card-content">
                                        <div class="card-value" id="avg-backup-time">0m</div>
                                        <div class="card-label">Avg. Backup Time</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="charts-section">
                            <div class="chart-container">
                                <h4>Success Rate Trend</h4>
                                <canvas id="success-rate-chart" width="400" height="200"></canvas>
                            </div>
                            <div class="chart-container">
                                <h4>Storage Usage</h4>
                                <canvas id="storage-usage-chart" width="400" height="200"></canvas>
                            </div>
                        </div>

                        <div class="recent-sessions">
                            <h4>Recent Sessions</h4>
                            <div id="recent-sessions-list" class="sessions-preview">
                                <div class="loading-placeholder">Loading sessions...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Sessions Tab -->
                    <div id="sessions-tab" class="dashboard-tab">
                        <div class="sessions-controls">
                            <div class="filter-controls">
                                <select id="status-filter" onchange="backupHistoryDashboard.applyFilters()">
                                    <option value="">All Statuses</option>
                                    <option value="completed">Completed</option>
                                    <option value="failed">Failed</option>
                                    <option value="running">Running</option>
                                </select>
                                <select id="type-filter" onchange="backupHistoryDashboard.applyFilters()">
                                    <option value="">All Types</option>
                                    <option value="manual">Manual</option>
                                    <option value="scheduled">Scheduled</option>
                                    <option value="demo">Demo</option>
                                </select>
                                <button class="btn" onclick="backupHistoryDashboard.refreshSessions()">🔄 Refresh</button>
                            </div>
                        </div>

                        <div id="sessions-list" class="sessions-list">
                            <div class="loading-placeholder">Loading sessions...</div>
                        </div>

                        <!-- Session Details Modal -->
                        <div id="session-details-modal" class="modal" style="display: none;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 id="session-details-title">Session Details</h4>
                                    <button class="modal-close" onclick="backupHistoryDashboard.closeSessionDetails()">&times;</button>
                                </div>
                                <div class="modal-body" id="session-details-content">
                                    <!-- Session details will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Tab -->
                    <div id="analytics-tab" class="dashboard-tab">
                        <div class="analytics-charts">
                            <div class="chart-row">
                                <div class="chart-container">
                                    <h4>Performance Trends</h4>
                                    <canvas id="performance-chart" width="600" height="300"></canvas>
                                </div>
                            </div>
                            <div class="chart-row">
                                <div class="chart-container half">
                                    <h4>Data Type Breakdown</h4>
                                    <canvas id="data-breakdown-chart" width="300" height="300"></canvas>
                                </div>
                                <div class="chart-container half">
                                    <h4>Error Rate Analysis</h4>
                                    <canvas id="error-rate-chart" width="300" height="300"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="storage-quota-section">
                            <h4>Storage Quota Management</h4>
                            <div class="quota-display">
                                <div class="quota-bar">
                                    <div class="quota-fill" id="quota-fill-bar"></div>
                                </div>
                                <div class="quota-details">
                                    <span id="quota-used">0 MB</span> / <span id="quota-total">5 GB</span>
                                    (<span id="quota-percentage">0%</span> used)
                                </div>
                            </div>
                            <div class="quota-breakdown">
                                <div class="breakdown-item">
                                    <span class="breakdown-color contacts"></span>
                                    <span>Contacts: <span id="contacts-size">0 MB</span></span>
                                </div>
                                <div class="breakdown-item">
                                    <span class="breakdown-color messages"></span>
                                    <span>Messages: <span id="messages-size">0 MB</span></span>
                                </div>
                                <div class="breakdown-item">
                                    <span class="breakdown-color photos"></span>
                                    <span>Photos: <span id="photos-size">0 MB</span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Restore Tab -->
                    <div id="restore-tab" class="dashboard-tab">
                        <div class="restore-section">
                            <h4>Data Restore Simulation</h4>
                            <p>Select a completed backup session to simulate data restoration:</p>
                            
                            <div id="restore-sessions-list" class="restore-sessions">
                                <div class="loading-placeholder">Loading available backups...</div>
                            </div>

                            <div id="restore-progress-section" class="restore-progress" style="display: none;">
                                <h5>Restore Progress</h5>
                                <div class="restore-phases">
                                    <div class="restore-phase">
                                        <span>Downloading:</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="download-progress"></div>
                                        </div>
                                        <span id="download-percent">0%</span>
                                    </div>
                                    <div class="restore-phase">
                                        <span>Decrypting:</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="decryption-progress"></div>
                                        </div>
                                        <span id="decryption-percent">0%</span>
                                    </div>
                                    <div class="restore-phase">
                                        <span>Verifying:</span>
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="verification-progress"></div>
                                        </div>
                                        <span id="verification-percent">0%</span>
                                    </div>
                                </div>
                            </div>

                            <div id="restore-results-section" class="restore-results" style="display: none;">
                                <h5>Restored Data Preview</h5>
                                <div id="restored-data-content">
                                    <!-- Restored data will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.addStyles();
    }

    /**
     * Add CSS styles for the dashboard
     */
    addStyles() {
        if (document.getElementById('backup-history-dashboard-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'backup-history-dashboard-styles';
        styles.textContent = `
            .backup-history-dashboard {
                background: white;
                border-radius: 15px;
                padding: 25px;
                margin: 20px 0;
                box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            }

            .dashboard-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 25px;
                padding-bottom: 15px;
                border-bottom: 2px solid #e9ecef;
            }

            .dashboard-header h3 {
                color: #4facfe;
                margin: 0;
                font-size: 1.5rem;
            }

            .dashboard-controls {
                display: flex;
                gap: 10px;
            }

            .dashboard-controls .btn {
                padding: 8px 16px;
                font-size: 0.9rem;
                min-width: auto;
            }

            .dashboard-tab {
                display: none;
            }

            .dashboard-tab.active {
                display: block;
            }

            .analytics-summary {
                margin-bottom: 30px;
            }

            .summary-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
            }

            .summary-card {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                display: flex;
                align-items: center;
                gap: 15px;
                border-left: 4px solid #4facfe;
            }

            .summary-card.success {
                border-left-color: #28a745;
            }

            .summary-card.info {
                border-left-color: #17a2b8;
            }

            .summary-card.warning {
                border-left-color: #ffc107;
            }

            .card-icon {
                font-size: 2rem;
            }

            .card-content {
                flex: 1;
            }

            .card-value {
                font-size: 1.8rem;
                font-weight: bold;
                color: #333;
                line-height: 1;
            }

            .card-label {
                font-size: 0.9rem;
                color: #666;
                margin-top: 5px;
            }

            .charts-section {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin-bottom: 30px;
            }

            .chart-container {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
            }

            .chart-container h4 {
                margin: 0 0 15px 0;
                color: #333;
                font-size: 1.1rem;
            }

            .chart-row {
                display: flex;
                gap: 20px;
                margin-bottom: 20px;
            }

            .chart-container.half {
                flex: 1;
            }

            .sessions-controls {
                margin-bottom: 20px;
            }

            .filter-controls {
                display: flex;
                gap: 15px;
                align-items: center;
            }

            .filter-controls select {
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 0.9rem;
            }

            .sessions-list {
                max-height: 600px;
                overflow-y: auto;
            }

            .session-item {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 15px;
                border-left: 4px solid #4facfe;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .session-item:hover {
                background: #e9ecef;
                transform: translateX(5px);
            }

            .session-item.failed {
                border-left-color: #dc3545;
            }

            .session-item.completed {
                border-left-color: #28a745;
            }

            .session-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .session-id {
                font-weight: bold;
                color: #333;
            }

            .session-status {
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 0.8rem;
                font-weight: 600;
            }

            .session-status.completed {
                background: #d4edda;
                color: #155724;
            }

            .session-status.failed {
                background: #f8d7da;
                color: #721c24;
            }

            .session-status.running {
                background: #fff3cd;
                color: #856404;
            }

            .session-details {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
                font-size: 0.9rem;
                color: #666;
            }

            .session-metric {
                text-align: center;
            }

            .metric-value {
                font-weight: bold;
                color: #333;
                display: block;
            }

            .metric-label {
                font-size: 0.8rem;
                margin-top: 2px;
            }

            .sessions-preview .session-item {
                padding: 15px;
                margin-bottom: 10px;
            }

            .loading-placeholder {
                text-align: center;
                padding: 40px;
                color: #666;
                font-style: italic;
            }

            .modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .modal-content {
                background: white;
                border-radius: 15px;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px 25px;
                border-bottom: 1px solid #e9ecef;
            }

            .modal-header h4 {
                margin: 0;
                color: #333;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .modal-body {
                padding: 25px;
            }

            .storage-quota-section {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin-top: 20px;
            }

            .quota-display {
                margin-bottom: 20px;
            }

            .quota-bar {
                width: 100%;
                height: 20px;
                background: #e9ecef;
                border-radius: 10px;
                overflow: hidden;
                margin-bottom: 10px;
            }

            .quota-fill {
                height: 100%;
                background: linear-gradient(90deg, #28a745, #20c997);
                width: 0%;
                transition: width 0.3s ease;
            }

            .quota-details {
                text-align: center;
                font-size: 1.1rem;
                color: #333;
            }

            .quota-breakdown {
                display: flex;
                gap: 20px;
                justify-content: center;
            }

            .breakdown-item {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 0.9rem;
            }

            .breakdown-color {
                width: 12px;
                height: 12px;
                border-radius: 50%;
            }

            .breakdown-color.contacts {
                background: #4facfe;
            }

            .breakdown-color.messages {
                background: #28a745;
            }

            .breakdown-color.photos {
                background: #ffc107;
            }

            .restore-sessions {
                max-height: 300px;
                overflow-y: auto;
                margin-bottom: 20px;
            }

            .restore-session-item {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
                cursor: pointer;
                transition: background 0.2s ease;
            }

            .restore-session-item:hover {
                background: #e9ecef;
            }

            .restore-session-item.selected {
                background: #d4edda;
                border: 2px solid #28a745;
            }

            .restore-progress {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .restore-phases {
                display: grid;
                gap: 15px;
            }

            .restore-phase {
                display: grid;
                grid-template-columns: 100px 1fr 60px;
                gap: 15px;
                align-items: center;
            }

            .restore-results {
                background: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
            }

            .restored-data-type {
                margin-bottom: 20px;
            }

            .restored-data-type h6 {
                color: #4facfe;
                margin-bottom: 10px;
                font-size: 1rem;
            }

            .restored-items {
                background: white;
                border-radius: 5px;
                padding: 15px;
                max-height: 200px;
                overflow-y: auto;
            }

            .restored-item {
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
                font-size: 0.9rem;
            }

            .restored-item:last-child {
                border-bottom: none;
            }

            .session-detail-sections {
                display: grid;
                gap: 20px;
            }

            .detail-section {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
            }

            .detail-section h5 {
                color: #4facfe;
                margin: 0 0 15px 0;
                font-size: 1.1rem;
            }

            .detail-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                font-size: 0.9rem;
            }

            .detail-grid > div {
                background: white;
                padding: 10px;
                border-radius: 5px;
                border-left: 3px solid #4facfe;
            }

            .progress-breakdown {
                display: grid;
                gap: 10px;
            }

            .progress-item {
                display: grid;
                grid-template-columns: 100px 1fr auto;
                gap: 15px;
                align-items: center;
                padding: 8px;
                background: white;
                border-radius: 5px;
            }

            .progress-item.total {
                background: #e3f2fd;
                border: 1px solid #4facfe;
            }

            .errors-list {
                max-height: 200px;
                overflow-y: auto;
            }

            .restore-action {
                text-align: center;
                padding: 20px;
            }

            .restore-button {
                text-align: center;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;
                margin-top: 15px;
            }

            @media (max-width: 768px) {
                .charts-section {
                    grid-template-columns: 1fr;
                }
                
                .chart-row {
                    flex-direction: column;
                }
                
                .dashboard-controls {
                    flex-wrap: wrap;
                }
                
                .filter-controls {
                    flex-wrap: wrap;
                }
                
                .quota-breakdown {
                    flex-direction: column;
                    align-items: center;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * Switch between dashboard views
     */
    switchView(view) {
        this.currentView = view;
        
        // Update button states
        document.querySelectorAll('.dashboard-controls .btn').forEach(btn => {
            btn.className = 'btn secondary';
        });
        document.getElementById(`${view}-btn`).className = 'btn';
        
        // Update tab visibility
        document.querySelectorAll('.dashboard-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.getElementById(`${view}-tab`).classList.add('active');
        
        // Load view-specific data
        switch (view) {
            case 'overview':
                this.updateOverview();
                break;
            case 'sessions':
                this.updateSessionsList();
                break;
            case 'analytics':
                this.updateAnalyticsCharts();
                break;
            case 'restore':
                this.updateRestoreList();
                break;
        }
    }

    /**
     * Check if dashboard is properly initialized
     */
    isDashboardReady() {
        const requiredElements = [
            'total-sessions-count',
            'success-rate-percent', 
            'total-data-size',
            'avg-backup-time'
        ];
        
        return requiredElements.every(id => document.getElementById(id) !== null);
    }

    /**
     * Update the entire dashboard
     */
    updateDashboard() {
        if (!this.historyManager) {
            console.warn('History manager not available');
            return;
        }
        
        if (!this.isDashboardReady()) {
            console.warn('Dashboard elements not ready, retrying in 500ms');
            setTimeout(() => this.updateDashboard(), 500);
            return;
        }
        
        try {
            this.updateAnalytics();
            this.updateOverview();
            this.updateSessionsList();
            this.updateCharts();
        } catch (error) {
            console.error('Error updating dashboard:', error);
        }
    }

    /**
     * Update analytics summary
     */
    updateAnalytics() {
        if (!this.historyManager) return;
        
        const analytics = this.historyManager.getAnalytics();
        
        // Update summary cards with error handling
        const totalSessionsEl = document.getElementById('total-sessions-count');
        if (totalSessionsEl) {
            totalSessionsEl.textContent = analytics.totalSessions;
        }
        
        const successRate = analytics.totalSessions > 0 ? 
            Math.round((analytics.successfulSessions / analytics.totalSessions) * 100) : 0;
        const successRateEl = document.getElementById('success-rate-percent');
        if (successRateEl) {
            successRateEl.textContent = `${successRate}%`;
        }
        
        const totalDataEl = document.getElementById('total-data-size');
        if (totalDataEl) {
            totalDataEl.textContent = BackupHistoryManager.formatFileSize(analytics.totalDataBackedUp);
        }
        
        const avgTimeEl = document.getElementById('avg-backup-time');
        if (avgTimeEl) {
            avgTimeEl.textContent = BackupHistoryManager.formatDuration(analytics.averageBackupTime);
        }
        
        // Update storage quota
        this.updateStorageQuota(analytics.storageUsage);
    }

    /**
     * Update storage quota display
     */
    updateStorageQuota(storageUsage) {
        const usedPercent = (storageUsage.used / storageUsage.quota) * 100;
        
        const quotaFillEl = document.getElementById('quota-fill-bar');
        if (quotaFillEl) {
            quotaFillEl.style.width = `${Math.min(usedPercent, 100)}%`;
        }
        
        const quotaUsedEl = document.getElementById('quota-used');
        if (quotaUsedEl) {
            quotaUsedEl.textContent = BackupHistoryManager.formatFileSize(storageUsage.used);
        }
        
        const quotaTotalEl = document.getElementById('quota-total');
        if (quotaTotalEl) {
            quotaTotalEl.textContent = BackupHistoryManager.formatFileSize(storageUsage.quota);
        }
        
        const quotaPercentEl = document.getElementById('quota-percentage');
        if (quotaPercentEl) {
            quotaPercentEl.textContent = `${Math.round(usedPercent)}`;
        }
        
        // Update breakdown with error handling
        const contactsSizeEl = document.getElementById('contacts-size');
        if (contactsSizeEl) {
            contactsSizeEl.textContent = BackupHistoryManager.formatFileSize(storageUsage.breakdown.contacts);
        }
        
        const messagesSizeEl = document.getElementById('messages-size');
        if (messagesSizeEl) {
            messagesSizeEl.textContent = BackupHistoryManager.formatFileSize(storageUsage.breakdown.messages);
        }
        
        const photosSizeEl = document.getElementById('photos-size');
        if (photosSizeEl) {
            photosSizeEl.textContent = BackupHistoryManager.formatFileSize(storageUsage.breakdown.photos);
        }
    }

    /**
     * Update overview tab
     */
    updateOverview() {
        if (!this.historyManager) return;
        
        // Update recent sessions
        const recentSessions = this.historyManager.getSessions().slice(0, 3);
        const recentSessionsList = document.getElementById('recent-sessions-list');
        
        if (recentSessions.length === 0) {
            recentSessionsList.innerHTML = '<div class="loading-placeholder">No backup sessions found</div>';
            return;
        }
        
        recentSessionsList.innerHTML = recentSessions.map(session => `
            <div class="session-item ${session.status}" onclick="backupHistoryDashboard.showSessionDetails('${session.id}')">
                <div class="session-header">
                    <span class="session-id">${session.id}</span>
                    <span class="session-status ${session.status}">${session.status.toUpperCase()}</span>
                </div>
                <div class="session-details">
                    <div class="session-metric">
                        <span class="metric-value">${session.startTime.toLocaleDateString()}</span>
                        <span class="metric-label">Date</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${session.progress.overall.completed}</span>
                        <span class="metric-label">Files</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${BackupHistoryManager.formatFileSize(session.encryption?.originalSize || 0)}</span>
                        <span class="metric-label">Size</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${session.endTime ? BackupHistoryManager.formatDuration(session.endTime.getTime() - session.startTime.getTime()) : 'N/A'}</span>
                        <span class="metric-label">Duration</span>
                    </div>
                </div>
            </div>
        `).join('');
        
        // Update charts
        this.updateCharts();
    }

    /**
     * Update sessions list
     */
    updateSessionsList() {
        if (!this.historyManager) return;
        
        const sessions = this.historyManager.getSessions();
        const sessionsList = document.getElementById('sessions-list');
        
        if (sessions.length === 0) {
            sessionsList.innerHTML = '<div class="loading-placeholder">No backup sessions found</div>';
            return;
        }
        
        sessionsList.innerHTML = sessions.map(session => `
            <div class="session-item ${session.status}" onclick="backupHistoryDashboard.showSessionDetails('${session.id}')">
                <div class="session-header">
                    <span class="session-id">${session.id}</span>
                    <span class="session-status ${session.status}">${session.status.toUpperCase()}</span>
                </div>
                <div class="session-details">
                    <div class="session-metric">
                        <span class="metric-value">${session.startTime.toLocaleString()}</span>
                        <span class="metric-label">Started</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${session.type}</span>
                        <span class="metric-label">Type</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${session.progress.overall.completed}/${session.progress.overall.total}</span>
                        <span class="metric-label">Files</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${BackupHistoryManager.formatFileSize(session.encryption?.originalSize || 0)}</span>
                        <span class="metric-label">Size</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${session.endTime ? BackupHistoryManager.formatDuration(session.endTime.getTime() - session.startTime.getTime()) : 'Running...'}</span>
                        <span class="metric-label">Duration</span>
                    </div>
                    <div class="session-metric">
                        <span class="metric-value">${session.performance.errorRate.toFixed(1)}%</span>
                        <span class="metric-label">Error Rate</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Show detailed session information
     */
    showSessionDetails(sessionId) {
        const session = this.historyManager.getSession(sessionId);
        if (!session) return;
        
        this.selectedSession = session;
        
        const modal = document.getElementById('session-details-modal');
        const title = document.getElementById('session-details-title');
        const content = document.getElementById('session-details-content');
        
        title.textContent = `Session Details - ${session.id}`;
        
        content.innerHTML = `
            <div class="session-detail-sections">
                <div class="detail-section">
                    <h5>Session Information</h5>
                    <div class="detail-grid">
                        <div><strong>ID:</strong> ${session.id}</div>
                        <div><strong>Type:</strong> ${session.type}</div>
                        <div><strong>Status:</strong> <span class="session-status ${session.status}">${session.status.toUpperCase()}</span></div>
                        <div><strong>Started:</strong> ${session.startTime.toLocaleString()}</div>
                        <div><strong>Ended:</strong> ${session.endTime ? session.endTime.toLocaleString() : 'N/A'}</div>
                        <div><strong>Duration:</strong> ${session.endTime ? BackupHistoryManager.formatDuration(session.endTime.getTime() - session.startTime.getTime()) : 'N/A'}</div>
                    </div>
                </div>

                <div class="detail-section">
                    <h5>Progress Summary</h5>
                    <div class="progress-breakdown">
                        <div class="progress-item">
                            <span>Contacts:</span>
                            <span>${session.progress.contacts.completed}/${session.progress.contacts.total}</span>
                            <span>(${session.progress.contacts.failed} failed)</span>
                        </div>
                        <div class="progress-item">
                            <span>Messages:</span>
                            <span>${session.progress.messages.completed}/${session.progress.messages.total}</span>
                            <span>(${session.progress.messages.failed} failed)</span>
                        </div>
                        <div class="progress-item">
                            <span>Photos:</span>
                            <span>${session.progress.photos.completed}/${session.progress.photos.total}</span>
                            <span>(${session.progress.photos.failed} failed)</span>
                        </div>
                        <div class="progress-item total">
                            <span><strong>Total:</strong></span>
                            <span><strong>${session.progress.overall.completed}/${session.progress.overall.total}</strong></span>
                            <span><strong>(${session.progress.overall.failed} failed)</strong></span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h5>Encryption Details</h5>
                    <div class="detail-grid">
                        <div><strong>Algorithm:</strong> ${session.encryption?.algorithm || 'N/A'}</div>
                        <div><strong>Key ID:</strong> ${session.encryption?.keyId || 'N/A'}</div>
                        <div><strong>Original Size:</strong> ${BackupHistoryManager.formatFileSize(session.encryption?.originalSize || 0)}</div>
                        <div><strong>Encrypted Size:</strong> ${BackupHistoryManager.formatFileSize(session.encryption?.encryptedSize || 0)}</div>
                        <div><strong>Overhead:</strong> ${session.encryption?.originalSize ? (((session.encryption.encryptedSize - session.encryption.originalSize) / session.encryption.originalSize) * 100).toFixed(1) + '%' : 'N/A'}</div>
                    </div>
                </div>

                <div class="detail-section">
                    <h5>Performance Metrics</h5>
                    <div class="detail-grid">
                        <div><strong>Transfer Rate:</strong> ${BackupHistoryManager.formatFileSize(session.performance?.transferRate || 0)}/s</div>
                        <div><strong>Average File Size:</strong> ${BackupHistoryManager.formatFileSize(session.performance?.averageFileSize || 0)}</div>
                        <div><strong>Error Rate:</strong> ${(session.performance?.errorRate || 0).toFixed(2)}%</div>
                    </div>
                </div>

                ${session.errors && session.errors.length > 0 ? `
                <div class="detail-section">
                    <h5>Errors (${session.errors.length})</h5>
                    <div class="errors-list">
                        ${session.errors.map(error => `
                            <div class="error-item">
                                <div class="error-time">${error.time.toLocaleTimeString()}</div>
                                <div class="error-phase">${error.phase}</div>
                                <div class="error-message">${error.message}</div>
                                <div class="error-item-name">${error.itemName}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}

                ${session.status === 'completed' ? `
                <div class="detail-section">
                    <div class="restore-action">
                        <button class="btn" onclick="backupHistoryDashboard.initiateRestore('${session.id}')">
                            🔄 Simulate Restore
                        </button>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
        
        modal.style.display = 'flex';
    }

    /**
     * Close session details modal
     */
    closeSessionDetails() {
        document.getElementById('session-details-modal').style.display = 'none';
        this.selectedSession = null;
    }

    /**
     * Apply filters to sessions list
     */
    applyFilters() {
        // This would filter the sessions based on selected criteria
        // For now, just refresh the list
        this.updateSessionsList();
    }

    /**
     * Refresh sessions list
     */
    refreshSessions() {
        this.updateSessionsList();
    }

    /**
     * Update charts
     */
    updateCharts() {
        if (!this.historyManager) return;
        
        // Simple chart implementation without Chart.js for now
        // In a real implementation, you would use Chart.js here
        this.updateSimpleCharts();
    }

    /**
     * Update analytics charts
     */
    updateAnalyticsCharts() {
        this.updateCharts();
    }

    /**
     * Update charts using Chart.js
     */
    updateSimpleCharts() {
        if (!window.Chart) {
            console.warn('Chart.js not loaded, using fallback charts');
            this.updateFallbackCharts();
            return;
        }
        
        if (!this.historyManager) {
            console.warn('History manager not available, skipping chart updates');
            return;
        }
        
        try {
            const metrics = this.historyManager.getPerformanceMetrics();
            
            // Success Rate Trend Chart
            this.updateSuccessRateChart(metrics);
            
            // Storage Usage Chart
            this.updateStorageUsageChart();
            
            // Performance Chart (for analytics tab)
            this.updatePerformanceChart(metrics);
            
            // Data Breakdown Chart
            this.updateDataBreakdownChart();
            
            // Error Rate Chart
            this.updateErrorRateChart(metrics);
        } catch (error) {
            console.error('Error updating charts:', error);
        }
    }

    /**
     * Update success rate trend chart
     */
    updateSuccessRateChart(metrics) {
        const canvas = document.getElementById('success-rate-chart');
        if (!canvas) {
            console.warn('Success rate chart canvas not found');
            return;
        }
        
        try {
            // Destroy existing chart
            if (this.charts.successRate) {
                this.charts.successRate.destroy();
            }
            
            const ctx = canvas.getContext('2d');
        
        this.charts.successRate = new Chart(ctx, {
            type: 'line',
            data: {
                labels: metrics.map(m => m.date.toLocaleDateString()),
                datasets: [{
                    label: 'Success Rate (%)',
                    data: metrics.map(m => m.successRate),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        } catch (error) {
            console.error('Error creating success rate chart:', error);
        }
    }

    /**
     * Update storage usage chart
     */
    updateStorageUsageChart() {
        const canvas = document.getElementById('storage-usage-chart');
        if (!canvas) {
            console.warn('Storage usage chart canvas not found');
            return;
        }
        
        try {
            // Destroy existing chart
            if (this.charts.storageUsage) {
                this.charts.storageUsage.destroy();
            }
            
            const ctx = canvas.getContext('2d');
            const analytics = this.historyManager.getAnalytics();
            const breakdown = analytics.storageUsage.breakdown;
        
        this.charts.storageUsage = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Contacts', 'Messages', 'Photos', 'Free Space'],
                datasets: [{
                    data: [
                        breakdown.contacts,
                        breakdown.messages,
                        breakdown.photos,
                        Math.max(0, analytics.storageUsage.quota - analytics.storageUsage.used)
                    ],
                    backgroundColor: [
                        '#4facfe',
                        '#28a745',
                        '#ffc107',
                        '#e9ecef'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = BackupHistoryManager.formatFileSize(context.raw);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
        } catch (error) {
            console.error('Error creating storage usage chart:', error);
        }
    }

    /**
     * Update performance chart
     */
    updatePerformanceChart(metrics) {
        const canvas = document.getElementById('performance-chart');
        if (!canvas) {
            console.warn('Performance chart canvas not found');
            return;
        }
        
        try {
            // Destroy existing chart
            if (this.charts.performance) {
                this.charts.performance.destroy();
            }
            
            const ctx = canvas.getContext('2d');
        
        this.charts.performance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: metrics.map(m => m.date.toLocaleDateString()),
                datasets: [
                    {
                        label: 'Transfer Rate (MB/s)',
                        data: metrics.map(m => (m.transferRate / 1024 / 1024).toFixed(1)),
                        borderColor: '#4facfe',
                        backgroundColor: 'rgba(79, 172, 254, 0.1)',
                        yAxisID: 'y'
                    },
                    {
                        label: 'Duration (minutes)',
                        data: metrics.map(m => (m.duration / 60).toFixed(1)),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Transfer Rate (MB/s)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Duration (minutes)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }

    /**
     * Update data breakdown chart
     */
    updateDataBreakdownChart() {
        const canvas = document.getElementById('data-breakdown-chart');
        if (!canvas) return;
        
        // Destroy existing chart
        if (this.charts.dataBreakdown) {
            this.charts.dataBreakdown.destroy();
        }
        
        const ctx = canvas.getContext('2d');
        const analytics = this.historyManager.getAnalytics();
        const breakdown = analytics.storageUsage.breakdown;
        
        this.charts.dataBreakdown = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['Contacts', 'Messages', 'Photos'],
                datasets: [{
                    data: [
                        breakdown.contacts,
                        breakdown.messages,
                        breakdown.photos
                    ],
                    backgroundColor: [
                        '#4facfe',
                        '#28a745',
                        '#ffc107'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = BackupHistoryManager.formatFileSize(context.raw);
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.raw / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Update error rate chart
     */
    updateErrorRateChart(metrics) {
        const canvas = document.getElementById('error-rate-chart');
        if (!canvas) return;
        
        // Destroy existing chart
        if (this.charts.errorRate) {
            this.charts.errorRate.destroy();
        }
        
        const ctx = canvas.getContext('2d');
        
        this.charts.errorRate = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: metrics.map(m => m.date.toLocaleDateString()),
                datasets: [{
                    label: 'Error Rate (%)',
                    data: metrics.map(m => m.errorRate),
                    backgroundColor: metrics.map(m => 
                        m.errorRate > 10 ? '#dc3545' : 
                        m.errorRate > 5 ? '#ffc107' : '#28a745'
                    ),
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    /**
     * Update restore list
     */
    updateRestoreList() {
        if (!this.historyManager) return;
        
        const completedSessions = this.historyManager.getSessions({ status: 'completed' });
        const restoreList = document.getElementById('restore-sessions-list');
        
        if (completedSessions.length === 0) {
            restoreList.innerHTML = '<div class="loading-placeholder">No completed backup sessions available for restore</div>';
            return;
        }
        
        restoreList.innerHTML = completedSessions.map(session => `
            <div class="restore-session-item" onclick="backupHistoryDashboard.selectRestoreSession('${session.id}')">
                <div class="session-header">
                    <span class="session-id">${session.id}</span>
                    <span class="session-date">${session.startTime.toLocaleDateString()}</span>
                </div>
                <div class="session-summary">
                    ${session.progress.overall.completed} files • ${BackupHistoryManager.formatFileSize(session.encryption?.originalSize || 0)}
                </div>
            </div>
        `).join('');
    }

    /**
     * Select a session for restore
     */
    selectRestoreSession(sessionId) {
        // Remove previous selection
        document.querySelectorAll('.restore-session-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Add selection to clicked item
        event.target.closest('.restore-session-item').classList.add('selected');
        
        // Show restore button
        const restoreList = document.getElementById('restore-sessions-list');
        let restoreButton = restoreList.querySelector('.restore-button');
        
        if (!restoreButton) {
            restoreButton = document.createElement('div');
            restoreButton.className = 'restore-button';
            restoreButton.innerHTML = `
                <button class="btn" onclick="backupHistoryDashboard.initiateRestore('${sessionId}')">
                    🔄 Start Restore Simulation
                </button>
            `;
            restoreList.appendChild(restoreButton);
        } else {
            restoreButton.innerHTML = `
                <button class="btn" onclick="backupHistoryDashboard.initiateRestore('${sessionId}')">
                    🔄 Start Restore Simulation
                </button>
            `;
        }
    }

    /**
     * Initiate restore simulation
     */
    async initiateRestore(sessionId) {
        if (!this.historyManager) return;
        
        try {
            // Show progress section
            document.getElementById('restore-progress-section').style.display = 'block';
            document.getElementById('restore-results-section').style.display = 'none';
            
            // Start restore simulation
            const restoreSession = await this.historyManager.simulateRestore(sessionId);
            
            // Close modal if open
            this.closeSessionDetails();
            
            // Switch to restore tab if not already there
            if (this.currentView !== 'restore') {
                this.switchView('restore');
            }
            
        } catch (error) {
            alert(`Restore failed: ${error.message}`);
        }
    }

    /**
     * Update restore status
     */
    updateRestoreStatus(restoreSession) {
        const progressSection = document.getElementById('restore-progress-section');
        const resultsSection = document.getElementById('restore-results-section');
        
        if (restoreSession.status === 'completed') {
            // Hide progress, show results
            progressSection.style.display = 'none';
            resultsSection.style.display = 'block';
            
            // Display restored data
            this.displayRestoredData(restoreSession.restoredData);
        } else {
            // Update progress bars
            document.getElementById('download-progress').style.width = `${restoreSession.progress.download}%`;
            document.getElementById('download-percent').textContent = `${restoreSession.progress.download}%`;
            
            document.getElementById('decryption-progress').style.width = `${restoreSession.progress.decryption}%`;
            document.getElementById('decryption-percent').textContent = `${restoreSession.progress.decryption}%`;
            
            document.getElementById('verification-progress').style.width = `${restoreSession.progress.verification}%`;
            document.getElementById('verification-percent').textContent = `${restoreSession.progress.verification}%`;
        }
    }

    /**
     * Display restored data
     */
    displayRestoredData(restoredData) {
        const content = document.getElementById('restored-data-content');
        
        let html = '';
        
        if (restoredData.contacts) {
            html += `
                <div class="restored-data-type">
                    <h6>📞 Contacts (${restoredData.contacts.length})</h6>
                    <div class="restored-items">
                        ${restoredData.contacts.map(contact => `
                            <div class="restored-item">
                                <strong>${contact.name}</strong> - ${contact.phone} - ${contact.email}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
        
        if (restoredData.messages) {
            html += `
                <div class="restored-data-type">
                    <h6>💬 Messages (${restoredData.messages.length})</h6>
                    <div class="restored-items">
                        ${restoredData.messages.map(message => `
                            <div class="restored-item">
                                <strong>${message.from}:</strong> ${message.message}
                                <small style="color: #666; display: block;">${new Date(message.timestamp).toLocaleString()}</small>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
        
        if (restoredData.photos) {
            html += `
                <div class="restored-data-type">
                    <h6>📸 Photos (${restoredData.photos.length})</h6>
                    <div class="restored-items">
                        ${restoredData.photos.map(photo => `
                            <div class="restored-item">
                                <img src="${photo.thumbnail}" alt="${photo.filename}" style="width: 40px; height: 40px; margin-right: 10px; vertical-align: middle;">
                                <strong>${photo.filename}</strong> - ${BackupHistoryManager.formatFileSize(photo.size)}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
        
        content.innerHTML = html || '<div class="loading-placeholder">No data to display</div>';
    }

    /**
     * Fallback charts when Chart.js is not available
     */
    updateFallbackCharts() {
        if (!this.historyManager) return;
        
        const analytics = this.historyManager.getAnalytics();
        
        // Success Rate Chart Fallback
        const successChart = document.getElementById('success-rate-chart');
        if (successChart) {
            const ctx = successChart.getContext('2d');
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, successChart.width, successChart.height);
            ctx.fillStyle = '#4facfe';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            
            const successRate = analytics.totalSessions > 0 ? 
                Math.round((analytics.successfulSessions / analytics.totalSessions) * 100) : 0;
            
            ctx.fillText('Success Rate', successChart.width / 2, successChart.height / 2 - 20);
            ctx.font = 'bold 24px Arial';
            ctx.fillText(`${successRate}%`, successChart.width / 2, successChart.height / 2 + 10);
        }
        
        // Storage Usage Chart Fallback
        const storageChart = document.getElementById('storage-usage-chart');
        if (storageChart) {
            const ctx = storageChart.getContext('2d');
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, storageChart.width, storageChart.height);
            ctx.fillStyle = '#28a745';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            
            const usedPercent = (analytics.storageUsage.used / analytics.storageUsage.quota) * 100;
            
            ctx.fillText('Storage Usage', storageChart.width / 2, storageChart.height / 2 - 20);
            ctx.font = 'bold 24px Arial';
            ctx.fillText(`${Math.round(usedPercent)}%`, storageChart.width / 2, storageChart.height / 2 + 10);
        }
    }
}

// Make it available globally
window.BackupHistoryDashboard = BackupHistoryDashboard;