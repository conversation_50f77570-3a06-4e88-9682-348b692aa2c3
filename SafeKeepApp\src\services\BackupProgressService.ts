import { store } from '../store';
import {
  startBackupSession,
  updateDataTypeProgress,
  addProgressError,
  completeBackupSession,
  calculateStatistics,
  saveBackupHistory,
} from '../store/slices/backupSlice';
import { BackupSession, BackupProgress, BackupError, BackupConfiguration } from '../types/backup';

export class BackupProgressService {
  private static instance: BackupProgressService;
  private progressUpdateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL = 500; // Update every 500ms

  private constructor() {}

  public static getInstance(): BackupProgressService {
    if (!BackupProgressService.instance) {
      BackupProgressService.instance = new BackupProgressService();
    }
    return BackupProgressService.instance;
  }

  /**
   * Initialize a new backup session
   */
  public initializeSession(
    userId: string,
    configuration: BackupConfiguration,
    totalCounts: { contacts: number; messages: number; photos: number }
  ): BackupSession {
    const session: BackupSession = {
      id: this.generateSessionId(),
      userId,
      startTime: new Date(),
      status: 'in_progress',
      configuration,
      totalItems: totalCounts.contacts + totalCounts.messages + totalCounts.photos,
      completedItems: 0,
      progress: {
        contacts: {
          total: totalCounts.contacts,
          completed: 0,
          failed: 0,
          status: configuration.includeContacts ? 'pending' : 'completed',
          errors: [],
        },
        messages: {
          total: totalCounts.messages,
          completed: 0,
          failed: 0,
          status: configuration.includeMessages ? 'pending' : 'completed',
          errors: [],
        },
        photos: {
          total: totalCounts.photos,
          completed: 0,
          failed: 0,
          status: configuration.includePhotos ? 'pending' : 'completed',
          errors: [],
        },
      },
    };

    store.dispatch(startBackupSession(session));
    this.startProgressTracking();
    
    return session;
  }

  /**
   * Update progress for a specific data type
   */
  public updateProgress(
    dataType: 'contacts' | 'messages' | 'photos',
    updates: {
      completed?: number;
      failed?: number;
      status?: BackupProgress['status'];
      currentItem?: string;
    }
  ): void {
    const currentState = store.getState().backup;
    const currentProgress = currentState.realTimeProgress[dataType];

    const updatedProgress: Partial<BackupProgress> = {
      ...updates,
    };

    // Ensure we don't exceed total
    if (updates.completed !== undefined) {
      updatedProgress.completed = Math.min(updates.completed, currentProgress.total);
    }

    if (updates.failed !== undefined) {
      updatedProgress.failed = Math.min(updates.failed, currentProgress.total);
    }

    // Auto-complete if all items are processed
    const newCompleted = updatedProgress.completed ?? currentProgress.completed;
    const newFailed = updatedProgress.failed ?? currentProgress.failed;
    
    if (newCompleted + newFailed >= currentProgress.total && !updatedProgress.status) {
      updatedProgress.status = newFailed > 0 ? 'failed' : 'completed';
    }

    store.dispatch(updateDataTypeProgress({ dataType, progress: updatedProgress }));
  }

  /**
   * Increment progress for a specific data type
   */
  public incrementProgress(
    dataType: 'contacts' | 'messages' | 'photos',
    type: 'completed' | 'failed' = 'completed'
  ): void {
    const currentState = store.getState().backup;
    const currentProgress = currentState.realTimeProgress[dataType];

    const updates: any = {};
    if (type === 'completed') {
      updates.completed = currentProgress.completed + 1;
    } else {
      updates.failed = currentProgress.failed + 1;
    }

    this.updateProgress(dataType, updates);
  }

  /**
   * Set data type status
   */
  public setDataTypeStatus(
    dataType: 'contacts' | 'messages' | 'photos',
    status: BackupProgress['status']
  ): void {
    this.updateProgress(dataType, { status });
  }

  /**
   * Add an error for a specific data type
   */
  public addError(
    dataType: 'contacts' | 'messages' | 'photos',
    error: Omit<BackupError, 'id' | 'timestamp'>
  ): void {
    const backupError: BackupError = {
      ...error,
      id: this.generateErrorId(),
      timestamp: new Date(),
    };

    store.dispatch(addProgressError({ dataType, error: backupError }));
    
    // Also increment failed count
    this.incrementProgress(dataType, 'failed');
  }

  /**
   * Complete the current backup session
   */
  public async completeSession(status: BackupSession['status'] = 'completed'): Promise<void> {
    const endTime = new Date();
    
    // Stop progress tracking
    this.stopProgressTracking();
    
    // Complete the session
    store.dispatch(completeBackupSession({ endTime, status }));
    
    // Calculate updated statistics
    store.dispatch(calculateStatistics());
    
    // Save to persistent storage
    const currentState = store.getState().backup;
    await store.dispatch(saveBackupHistory({
      history: currentState.backupHistory,
      statistics: currentState.statistics,
      configuration: currentState.configuration,
    }));
  }

  /**
   * Cancel the current backup session
   */
  public async cancelSession(): Promise<void> {
    await this.completeSession('cancelled');
  }

  /**
   * Get current session progress summary
   */
  public getProgressSummary(): {
    overallProgress: number;
    dataTypeProgress: Record<string, { progress: number; status: string }>;
    estimatedTimeRemaining: number | null;
    currentItem: string | null;
  } {
    const currentState = store.getState().backup;
    const { realTimeProgress, currentSession } = currentState;

    const totalItems = Object.values(realTimeProgress).reduce((sum, p) => sum + p.total, 0);
    const completedItems = Object.values(realTimeProgress).reduce((sum, p) => sum + p.completed, 0);
    
    const overallProgress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

    const dataTypeProgress = Object.entries(realTimeProgress).reduce((acc, [key, progress]) => {
      acc[key] = {
        progress: progress.total > 0 ? Math.round((progress.completed / progress.total) * 100) : 0,
        status: progress.status,
      };
      return acc;
    }, {} as Record<string, { progress: number; status: string }>);

    // Calculate estimated time remaining
    let estimatedTimeRemaining: number | null = null;
    if (currentSession && completedItems > 0) {
      const elapsedTime = Date.now() - new Date(currentSession.startTime).getTime();
      const averageTimePerItem = elapsedTime / completedItems;
      const remainingItems = totalItems - completedItems;
      estimatedTimeRemaining = Math.floor((remainingItems * averageTimePerItem) / 1000);
    }

    return {
      overallProgress,
      dataTypeProgress,
      estimatedTimeRemaining,
      currentItem: null, // This would be set by individual services
    };
  }

  /**
   * Check if backup is currently in progress
   */
  public isBackupInProgress(): boolean {
    const currentState = store.getState().backup;
    return currentState.isBackupInProgress;
  }

  /**
   * Get current session ID
   */
  public getCurrentSessionId(): string | null {
    const currentState = store.getState().backup;
    return currentState.currentSession?.id || null;
  }

  /**
   * Start real-time progress tracking
   */
  private startProgressTracking(): void {
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval);
    }

    this.progressUpdateInterval = setInterval(() => {
      // This could be used for periodic updates or cleanup
      // For now, we'll just ensure the session is still valid
      const currentState = store.getState().backup;
      if (!currentState.isBackupInProgress) {
        this.stopProgressTracking();
      }
    }, this.UPDATE_INTERVAL);
  }

  /**
   * Stop real-time progress tracking
   */
  private stopProgressTracking(): void {
    if (this.progressUpdateInterval) {
      clearInterval(this.progressUpdateInterval);
      this.progressUpdateInterval = null;
    }
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Reset progress tracking (for testing or cleanup)
   */
  public reset(): void {
    this.stopProgressTracking();
  }

  /**
   * Get backup history statistics
   */
  public getHistoryStatistics(): {
    totalSessions: number;
    successfulSessions: number;
    failedSessions: number;
    averageSessionDuration: number;
    totalItemsBackedUp: number;
  } {
    const currentState = store.getState().backup;
    const { backupHistory } = currentState;

    const successfulSessions = backupHistory.filter(s => s.status === 'completed');
    const failedSessions = backupHistory.filter(s => s.status === 'failed');

    const totalSessionDuration = backupHistory.reduce((sum, session) => {
      if (session.endTime && session.startTime) {
        return sum + (new Date(session.endTime).getTime() - new Date(session.startTime).getTime());
      }
      return sum;
    }, 0);

    const averageSessionDuration = backupHistory.length > 0 ? totalSessionDuration / backupHistory.length : 0;
    const totalItemsBackedUp = successfulSessions.reduce((sum, session) => sum + session.completedItems, 0);

    return {
      totalSessions: backupHistory.length,
      successfulSessions: successfulSessions.length,
      failedSessions: failedSessions.length,
      averageSessionDuration,
      totalItemsBackedUp,
    };
  }
}

export default BackupProgressService;