/**
 * Subscription Flow Manager for SafeKeep Web Demo
 * Handles upgrade/downgrade flows with payment processing
 */

class SubscriptionFlowManager {
    constructor(subscriptionManager, paymentProcessor, paymentMethodsManager) {
        this.subscriptionManager = subscriptionManager;
        this.paymentProcessor = paymentProcessor;
        this.paymentMethodsManager = paymentMethodsManager;
        this.currentFlow = null;
        this.flowData = {};
        
        this.setupEventListeners();
    }

    /**
     * Initialize subscription flow manager
     */
    async initialize() {
        try {
            this.setupFlowUI();
            console.log('✅ Subscription Flow Manager initialized');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Subscription Flow Manager:', error);
            throw error;
        }
    }

    /**
     * Setup flow UI components
     */
    setupFlowUI() {
        this.createUpgradeFlowModal();
        this.createDowngradeFlowModal();
        this.createFlowConfirmationModal();
    }

    /**
     * Create upgrade flow modal
     */
    createUpgradeFlowModal() {
        const modal = document.createElement('div');
        modal.id = 'upgrade-flow-modal';
        modal.className = 'subscription-flow-modal';
        modal.style.display = 'none';

        modal.innerHTML = `
            <div class="modal-overlay" onclick="subscriptionFlowManager.cancelFlow()"></div>
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>🚀 Upgrade Your Plan</h3>
                    <button class="close-btn" onclick="subscriptionFlowManager.cancelFlow()">&times;</button>
                </div>
                
                <div class="modal-body">
                    <div class="upgrade-flow-steps">
                        <!-- Step 1: Plan Selection -->
                        <div class="flow-step active" id="upgrade-step-1">
                            <div class="step-header">
                                <div class="step-number">1</div>
                                <h4>Choose Your New Plan</h4>
                            </div>
                            <div class="step-content">
                                <div class="plan-comparison" id="upgrade-plan-comparison">
                                    <!-- Plan comparison will be populated -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 2: Payment Method -->
                        <div class="flow-step" id="upgrade-step-2">
                            <div class="step-header">
                                <div class="step-number">2</div>
                                <h4>Payment Method</h4>
                            </div>
                            <div class="step-content">
                                <div class="payment-method-selection" id="upgrade-payment-method">
                                    <!-- Payment method selection will be populated -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 3: Billing & Tax -->
                        <div class="flow-step" id="upgrade-step-3">
                            <div class="step-header">
                                <div class="step-number">3</div>
                                <h4>Billing Information</h4>
                            </div>
                            <div class="step-content">
                                <div class="billing-information" id="upgrade-billing-info">
                                    <!-- Billing information will be populated -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 4: Review & Confirm -->
                        <div class="flow-step" id="upgrade-step-4">
                            <div class="step-header">
                                <div class="step-number">4</div>
                                <h4>Review & Confirm</h4>
                            </div>
                            <div class="step-content">
                                <div class="upgrade-summary" id="upgrade-summary">
                                    <!-- Upgrade summary will be populated -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <div class="flow-navigation">
                        <button class="btn secondary" id="upgrade-back-btn" onclick="subscriptionFlowManager.previousStep()" style="display: none;">
                            ← Back
                        </button>
                        <div class="flow-progress">
                            <span id="upgrade-step-indicator">Step 1 of 4</span>
                        </div>
                        <button class="btn primary" id="upgrade-next-btn" onclick="subscriptionFlowManager.nextStep()">
                            Continue →
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * Create downgrade flow modal
     */
    createDowngradeFlowModal() {
        const modal = document.createElement('div');
        modal.id = 'downgrade-flow-modal';
        modal.className = 'subscription-flow-modal';
        modal.style.display = 'none';

        modal.innerHTML = `
            <div class="modal-overlay" onclick="subscriptionFlowManager.cancelFlow()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>⬇️ Downgrade Your Plan</h3>
                    <button class="close-btn" onclick="subscriptionFlowManager.cancelFlow()">&times;</button>
                </div>
                
                <div class="modal-body">
                    <div class="downgrade-warning">
                        <div class="warning-icon">⚠️</div>
                        <div class="warning-content">
                            <h4>Before you downgrade...</h4>
                            <p>Please review what you'll lose access to with your new plan.</p>
                        </div>
                    </div>
                    
                    <div class="feature-comparison" id="downgrade-feature-comparison">
                        <!-- Feature comparison will be populated -->
                    </div>
                    
                    <div class="downgrade-options" id="downgrade-options">
                        <!-- Downgrade options will be populated -->
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button class="btn secondary" onclick="subscriptionFlowManager.cancelFlow()">
                        Keep Current Plan
                    </button>
                    <button class="btn danger" id="confirm-downgrade-btn" onclick="subscriptionFlowManager.confirmDowngrade()">
                        Confirm Downgrade
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * Create flow confirmation modal
     */
    createFlowConfirmationModal() {
        const modal = document.createElement('div');
        modal.id = 'flow-confirmation-modal';
        modal.className = 'subscription-flow-modal';
        modal.style.display = 'none';

        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-body">
                    <div class="confirmation-content" id="confirmation-content">
                        <!-- Confirmation content will be populated -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn primary" onclick="subscriptionFlowManager.closeConfirmation()">
                        Continue
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    /**
     * Start upgrade flow
     */
    async startUpgradeFlow(targetTierId) {
        try {
            this.currentFlow = 'upgrade';
            this.flowData = {
                targetTierId,
                currentStep: 1,
                totalSteps: 4,
                selectedPaymentMethod: null,
                billingAddress: null,
                prorationAmount: 0
            };

            await this.showUpgradeFlow();
        } catch (error) {
            console.error('❌ Failed to start upgrade flow:', error);
            this.showErrorMessage('Failed to start upgrade process');
        }
    }

    /**
     * Show upgrade flow
     */
    async showUpgradeFlow() {
        const modal = document.getElementById('upgrade-flow-modal');
        if (!modal) return;

        // Populate step 1 - Plan comparison
        await this.populatePlanComparison();
        
        // Show modal
        modal.style.display = 'flex';
        this.updateUpgradeStepDisplay();
    }

    /**
     * Populate plan comparison
     */
    async populatePlanComparison() {
        const container = document.getElementById('upgrade-plan-comparison');
        if (!container) return;

        const currentSub = this.subscriptionManager.getCurrentSubscription();
        const currentTier = this.subscriptionManager.stripeManager.getSubscriptionTier(currentSub.tierId);
        const targetTier = this.subscriptionManager.stripeManager.getSubscriptionTier(this.flowData.targetTierId);

        if (!currentTier || !targetTier) return;

        container.innerHTML = `
            <div class="plan-comparison-grid">
                <div class="plan-column current">
                    <div class="plan-header">
                        <h5>Current Plan</h5>
                        <div class="plan-name">${currentTier.name}</div>
                        <div class="plan-price">$${(currentTier.price / 100).toFixed(2)}/month</div>
                    </div>
                    <div class="plan-features">
                        ${this.renderPlanFeatures(currentSub.tierId)}
                    </div>
                </div>
                
                <div class="plan-arrow">→</div>
                
                <div class="plan-column target">
                    <div class="plan-header">
                        <h5>New Plan</h5>
                        <div class="plan-name">${targetTier.name}</div>
                        <div class="plan-price">$${(targetTier.price / 100).toFixed(2)}/month</div>
                    </div>
                    <div class="plan-features">
                        ${this.renderPlanFeatures(this.flowData.targetTierId)}
                    </div>
                </div>
            </div>
            
            <div class="upgrade-benefits">
                <h5>✨ What you'll get with ${targetTier.name}:</h5>
                <div class="benefits-list">
                    ${this.renderUpgradeBenefits(currentSub.tierId, this.flowData.targetTierId)}
                </div>
            </div>
        `;
    }

    /**
     * Render plan features
     */
    renderPlanFeatures(tierId) {
        const tierConfig = this.subscriptionManager.stripeManager.tierConfig || 
                          new SubscriptionTierConfig();
        const features = tierConfig.getTierFeatures(tierId);
        const limits = tierConfig.getTierLimits(tierId);

        return `
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">💾</span>
                    <span>${limits.maxStorageGB}GB Storage</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔄</span>
                    <span>${limits.maxBackupsPerMonth === -1 ? 'Unlimited' : limits.maxBackupsPerMonth} Backups/month</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔐</span>
                    <span>${features.advanced_encryption ? 'Advanced' : 'Standard'} Encryption</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🚀</span>
                    <span>${features.priority_support ? 'Priority' : 'Standard'} Support</span>
                </div>
            </div>
        `;
    }

    /**
     * Render upgrade benefits
     */
    renderUpgradeBenefits(currentTierId, targetTierId) {
        const tierConfig = new SubscriptionTierConfig();
        const currentFeatures = tierConfig.getTierFeatures(currentTierId);
        const targetFeatures = tierConfig.getTierFeatures(targetTierId);
        const currentLimits = tierConfig.getTierLimits(currentTierId);
        const targetLimits = tierConfig.getTierLimits(targetTierId);

        const benefits = [];

        // Storage increase
        if (targetLimits.maxStorageGB > currentLimits.maxStorageGB) {
            benefits.push(`${targetLimits.maxStorageGB - currentLimits.maxStorageGB}GB additional storage`);
        }

        // Backup increase
        if (targetLimits.maxBackupsPerMonth === -1 || targetLimits.maxBackupsPerMonth > currentLimits.maxBackupsPerMonth) {
            benefits.push(targetLimits.maxBackupsPerMonth === -1 ? 'Unlimited backups' : 
                         `${targetLimits.maxBackupsPerMonth - currentLimits.maxBackupsPerMonth} more backups per month`);
        }

        // New features
        Object.keys(targetFeatures).forEach(feature => {
            if (targetFeatures[feature] && !currentFeatures[feature]) {
                const featureNames = {
                    'advanced_encryption': 'Advanced encryption',
                    'priority_support': 'Priority support',
                    'multi_device_sync': 'Multi-device sync',
                    'backup_scheduling': 'Automated backup scheduling'
                };
                if (featureNames[feature]) {
                    benefits.push(featureNames[feature]);
                }
            }
        });

        return benefits.map(benefit => `<div class="benefit-item">✓ ${benefit}</div>`).join('');
    }

    /**
     * Next step in upgrade flow
     */
    async nextStep() {
        if (this.currentFlow !== 'upgrade') return;

        const currentStep = this.flowData.currentStep;

        try {
            switch (currentStep) {
                case 1:
                    // Validate plan selection and move to payment method
                    await this.populatePaymentMethodStep();
                    this.flowData.currentStep = 2;
                    break;
                case 2:
                    // Validate payment method and move to billing
                    if (!await this.validatePaymentMethodStep()) return;
                    await this.populateBillingStep();
                    this.flowData.currentStep = 3;
                    break;
                case 3:
                    // Validate billing and move to review
                    if (!await this.validateBillingStep()) return;
                    await this.populateReviewStep();
                    this.flowData.currentStep = 4;
                    break;
                case 4:
                    // Process upgrade
                    await this.processUpgrade();
                    return;
            }

            this.updateUpgradeStepDisplay();
        } catch (error) {
            console.error('❌ Error in upgrade flow:', error);
            this.showErrorMessage('An error occurred during the upgrade process');
        }
    }

    /**
     * Previous step in upgrade flow
     */
    previousStep() {
        if (this.currentFlow !== 'upgrade' || this.flowData.currentStep <= 1) return;

        this.flowData.currentStep--;
        this.updateUpgradeStepDisplay();
    }

    /**
     * Update upgrade step display
     */
    updateUpgradeStepDisplay() {
        const currentStep = this.flowData.currentStep;
        const totalSteps = this.flowData.totalSteps;

        // Update step indicator
        const indicator = document.getElementById('upgrade-step-indicator');
        if (indicator) {
            indicator.textContent = `Step ${currentStep} of ${totalSteps}`;
        }

        // Update back button visibility
        const backBtn = document.getElementById('upgrade-back-btn');
        if (backBtn) {
            backBtn.style.display = currentStep > 1 ? 'inline-block' : 'none';
        }

        // Update next button text
        const nextBtn = document.getElementById('upgrade-next-btn');
        if (nextBtn) {
            nextBtn.textContent = currentStep === totalSteps ? 'Complete Upgrade' : 'Continue →';
        }

        // Show/hide steps
        for (let i = 1; i <= totalSteps; i++) {
            const step = document.getElementById(`upgrade-step-${i}`);
            if (step) {
                step.classList.toggle('active', i === currentStep);
                step.style.display = i === currentStep ? 'block' : 'none';
            }
        }
    }

    /**
     * Populate payment method step
     */
    async populatePaymentMethodStep() {
        const container = document.getElementById('upgrade-payment-method');
        if (!container) return;

        const paymentMethods = this.paymentMethodsManager.getAllPaymentMethods();
        const defaultMethod = this.paymentMethodsManager.getDefaultPaymentMethod();

        container.innerHTML = `
            <div class="payment-method-options">
                ${paymentMethods.length > 0 ? `
                    <div class="existing-payment-methods">
                        <h5>Select Payment Method</h5>
                        ${paymentMethods.map(method => `
                            <label class="payment-method-option">
                                <input type="radio" name="payment-method" value="${method.id}" 
                                       ${method.id === defaultMethod?.id ? 'checked' : ''}>
                                <div class="payment-method-display">
                                    <span class="card-info">
                                        ${this.formatCardBrand(method.card?.brand)} •••• ${method.card?.last4}
                                    </span>
                                    <span class="expiry">Expires ${method.card?.exp_month}/${method.card?.exp_year}</span>
                                </div>
                            </label>
                        `).join('')}
                    </div>
                    <div class="payment-method-divider">
                        <span>or</span>
                    </div>
                ` : ''}
                
                <div class="new-payment-method">
                    <label class="payment-method-option">
                        <input type="radio" name="payment-method" value="new" 
                               ${paymentMethods.length === 0 ? 'checked' : ''}>
                        <div class="payment-method-display">
                            <span class="new-method-text">Add new payment method</span>
                        </div>
                    </label>
                    
                    <div class="new-payment-form" id="new-payment-form" style="display: ${paymentMethods.length === 0 ? 'block' : 'none'};">
                        <div id="upgrade-payment-element">
                            <!-- Stripe Elements will be mounted here -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Setup payment method selection
        this.setupPaymentMethodSelection();
    }

    /**
     * Setup payment method selection
     */
    setupPaymentMethodSelection() {
        const radios = document.querySelectorAll('input[name="payment-method"]');
        const newPaymentForm = document.getElementById('new-payment-form');

        radios.forEach(radio => {
            radio.addEventListener('change', () => {
                if (newPaymentForm) {
                    newPaymentForm.style.display = radio.value === 'new' ? 'block' : 'none';
                }

                if (radio.value === 'new' && !this.paymentProcessor.paymentElement) {
                    // Mount payment elements for new payment method
                    this.paymentProcessor.mountPaymentElements('upgrade-payment-element', true);
                }
            });
        });
    }

    /**
     * Validate payment method step
     */
    async validatePaymentMethodStep() {
        const selectedMethod = document.querySelector('input[name="payment-method"]:checked');
        if (!selectedMethod) {
            this.showErrorMessage('Please select a payment method');
            return false;
        }

        this.flowData.selectedPaymentMethod = selectedMethod.value;
        return true;
    }

    /**
     * Populate billing step
     */
    async populateBillingStep() {
        const container = document.getElementById('upgrade-billing-info');
        if (!container) return;

        // If using existing payment method, show existing billing info
        if (this.flowData.selectedPaymentMethod !== 'new') {
            const method = this.paymentMethodsManager.paymentMethods.get(this.flowData.selectedPaymentMethod);
            if (method && method.billing_details) {
                container.innerHTML = `
                    <div class="existing-billing-info">
                        <h5>Billing Address</h5>
                        <div class="billing-address-display">
                            ${this.formatBillingAddress(method.billing_details)}
                        </div>
                        <button type="button" class="btn secondary" onclick="subscriptionFlowManager.editBillingAddress()">
                            Edit Billing Address
                        </button>
                    </div>
                    <div class="tax-calculation" id="upgrade-tax-calculation">
                        <!-- Tax calculation will be populated -->
                    </div>
                `;
                
                this.flowData.billingAddress = method.billing_details;
                await this.calculateUpgradeTax();
                return;
            }
        }

        // Show billing address form for new payment method
        container.innerHTML = `
            <div class="billing-address-section">
                <div id="upgrade-billing-address-form">
                    <!-- Billing address form will be created here -->
                </div>
            </div>
            <div class="tax-calculation" id="upgrade-tax-calculation">
                <!-- Tax calculation will be populated -->
            </div>
        `;

        this.paymentProcessor.createBillingAddressForm('upgrade-billing-address-form');
    }

    /**
     * Validate billing step
     */
    async validateBillingStep() {
        if (this.flowData.selectedPaymentMethod === 'new') {
            if (!this.paymentProcessor.validateBillingAddress()) {
                return false;
            }
            this.flowData.billingAddress = this.paymentProcessor.billingAddress;
        }

        await this.calculateUpgradeTax();
        return true;
    }

    /**
     * Calculate upgrade tax
     */
    async calculateUpgradeTax() {
        if (!this.flowData.billingAddress) return;

        const targetTier = this.subscriptionManager.stripeManager.getSubscriptionTier(this.flowData.targetTierId);
        const taxCalculator = new TaxCalculator();
        
        const taxAmount = await taxCalculator.calculateTax(targetTier.price, this.flowData.billingAddress);
        this.flowData.taxAmount = taxAmount;
        this.flowData.totalAmount = targetTier.price + taxAmount;

        // Update tax display
        const taxContainer = document.getElementById('upgrade-tax-calculation');
        if (taxContainer) {
            taxContainer.innerHTML = `
                <div class="tax-breakdown">
                    <div class="tax-line">
                        <span>Plan Cost:</span>
                        <span>$${(targetTier.price / 100).toFixed(2)}/month</span>
                    </div>
                    ${taxAmount > 0 ? `
                        <div class="tax-line">
                            <span>Tax:</span>
                            <span>$${(taxAmount / 100).toFixed(2)}</span>
                        </div>
                    ` : ''}
                    <div class="tax-line total">
                        <span><strong>Total:</strong></span>
                        <span><strong>$${(this.flowData.totalAmount / 100).toFixed(2)}/month</strong></span>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Populate review step
     */
    async populateReviewStep() {
        const container = document.getElementById('upgrade-summary');
        if (!container) return;

        const currentSub = this.subscriptionManager.getCurrentSubscription();
        const currentTier = this.subscriptionManager.stripeManager.getSubscriptionTier(currentSub.tierId);
        const targetTier = this.subscriptionManager.stripeManager.getSubscriptionTier(this.flowData.targetTierId);

        container.innerHTML = `
            <div class="upgrade-review">
                <div class="review-section">
                    <h5>Plan Change</h5>
                    <div class="plan-change-summary">
                        <div class="change-from">
                            <span class="label">From:</span>
                            <span class="plan-name">${currentTier.name}</span>
                            <span class="plan-price">$${(currentTier.price / 100).toFixed(2)}/month</span>
                        </div>
                        <div class="change-to">
                            <span class="label">To:</span>
                            <span class="plan-name">${targetTier.name}</span>
                            <span class="plan-price">$${(targetTier.price / 100).toFixed(2)}/month</span>
                        </div>
                    </div>
                </div>
                
                <div class="review-section">
                    <h5>Payment Method</h5>
                    <div class="payment-method-summary">
                        ${this.renderSelectedPaymentMethod()}
                    </div>
                </div>
                
                <div class="review-section">
                    <h5>Billing Summary</h5>
                    <div class="billing-summary">
                        <div class="billing-line">
                            <span>New Plan Cost:</span>
                            <span>$${(targetTier.price / 100).toFixed(2)}/month</span>
                        </div>
                        ${this.flowData.taxAmount > 0 ? `
                            <div class="billing-line">
                                <span>Tax:</span>
                                <span>$${(this.flowData.taxAmount / 100).toFixed(2)}</span>
                            </div>
                        ` : ''}
                        <div class="billing-line total">
                            <span><strong>Total:</strong></span>
                            <span><strong>$${(this.flowData.totalAmount / 100).toFixed(2)}/month</strong></span>
                        </div>
                    </div>
                </div>
                
                <div class="review-section">
                    <div class="upgrade-confirmation">
                        <label class="checkbox-label">
                            <input type="checkbox" id="upgrade-terms-agreement" required>
                            <span class="checkmark"></span>
                            I agree to the updated billing amount and terms of service
                        </label>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render selected payment method
     */
    renderSelectedPaymentMethod() {
        if (this.flowData.selectedPaymentMethod === 'new') {
            return '<span class="new-payment-method">New payment method will be added</span>';
        } else {
            const method = this.paymentMethodsManager.paymentMethods.get(this.flowData.selectedPaymentMethod);
            if (method) {
                return `
                    <span class="existing-payment-method">
                        ${this.formatCardBrand(method.card?.brand)} •••• ${method.card?.last4}
                    </span>
                `;
            }
        }
        return '<span class="unknown-payment-method">Unknown payment method</span>';
    }

    /**
     * Process upgrade
     */
    async processUpgrade() {
        const termsAgreement = document.getElementById('upgrade-terms-agreement');
        if (!termsAgreement?.checked) {
            this.showErrorMessage('Please agree to the terms to continue');
            return;
        }

        const nextBtn = document.getElementById('upgrade-next-btn');
        const originalText = nextBtn.textContent;
        
        try {
            // Show loading state
            nextBtn.disabled = true;
            nextBtn.textContent = 'Processing...';

            // Process payment and upgrade
            const result = await this.paymentProcessor.processSubscriptionPayment(
                this.flowData.targetTierId,
                this.flowData.billingAddress
            );

            if (result.success) {
                this.showUpgradeSuccess();
            } else {
                throw new Error(result.message || 'Upgrade failed');
            }

        } catch (error) {
            console.error('❌ Upgrade processing failed:', error);
            this.showErrorMessage(`Upgrade failed: ${error.message}`);
        } finally {
            nextBtn.disabled = false;
            nextBtn.textContent = originalText;
        }
    }

    /**
     * Show upgrade success
     */
    showUpgradeSuccess() {
        this.hideUpgradeFlow();
        
        const targetTier = this.subscriptionManager.stripeManager.getSubscriptionTier(this.flowData.targetTierId);
        
        this.showFlowConfirmation({
            type: 'success',
            title: '🎉 Upgrade Successful!',
            message: `You've successfully upgraded to the ${targetTier.name} plan.`,
            details: [
                'Your new features are now active',
                'Billing will begin on your next cycle',
                'You can manage your subscription anytime'
            ]
        });
    }

    /**
     * Start downgrade flow
     */
    async startDowngradeFlow(targetTierId) {
        try {
            this.currentFlow = 'downgrade';
            this.flowData = {
                targetTierId,
                currentTierId: this.subscriptionManager.getCurrentSubscription().tierId
            };

            await this.showDowngradeFlow();
        } catch (error) {
            console.error('❌ Failed to start downgrade flow:', error);
            this.showErrorMessage('Failed to start downgrade process');
        }
    }

    /**
     * Show downgrade flow
     */
    async showDowngradeFlow() {
        const modal = document.getElementById('downgrade-flow-modal');
        if (!modal) return;

        await this.populateDowngradeComparison();
        modal.style.display = 'flex';
    }

    /**
     * Populate downgrade comparison
     */
    async populateDowngradeComparison() {
        const container = document.getElementById('downgrade-feature-comparison');
        if (!container) return;

        const currentTierId = this.flowData.currentTierId;
        const targetTierId = this.flowData.targetTierId;
        
        const tierConfig = new SubscriptionTierConfig();
        const currentFeatures = tierConfig.getTierFeatures(currentTierId);
        const targetFeatures = tierConfig.getTierFeatures(targetTierId);
        const currentLimits = tierConfig.getTierLimits(currentTierId);
        const targetLimits = tierConfig.getTierLimits(targetTierId);

        // Find features that will be lost
        const lostFeatures = [];
        Object.keys(currentFeatures).forEach(feature => {
            if (currentFeatures[feature] && !targetFeatures[feature]) {
                lostFeatures.push(feature);
            }
        });

        // Find reduced limits
        const reducedLimits = [];
        if (targetLimits.maxStorageGB < currentLimits.maxStorageGB) {
            reducedLimits.push(`Storage reduced from ${currentLimits.maxStorageGB}GB to ${targetLimits.maxStorageGB}GB`);
        }
        if (currentLimits.maxBackupsPerMonth === -1 && targetLimits.maxBackupsPerMonth !== -1) {
            reducedLimits.push(`Backups limited to ${targetLimits.maxBackupsPerMonth} per month`);
        }

        container.innerHTML = `
            <div class="downgrade-impact">
                ${lostFeatures.length > 0 ? `
                    <div class="lost-features">
                        <h5>❌ Features you'll lose:</h5>
                        <ul>
                            ${lostFeatures.map(feature => `
                                <li>${this.getFeatureName(feature)}</li>
                            `).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                ${reducedLimits.length > 0 ? `
                    <div class="reduced-limits">
                        <h5>⬇️ Reduced limits:</h5>
                        <ul>
                            ${reducedLimits.map(limit => `<li>${limit}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                <div class="downgrade-timing">
                    <h5>📅 When changes take effect:</h5>
                    <p>Your plan will be downgraded at the end of your current billing period. You'll continue to have access to all current features until then.</p>
                </div>
            </div>
        `;
    }

    /**
     * Get feature display name
     */
    getFeatureName(featureKey) {
        const names = {
            'advanced_encryption': 'Advanced Encryption',
            'priority_support': 'Priority Support',
            'multi_device_sync': 'Multi-Device Sync',
            'backup_scheduling': 'Automated Backup Scheduling'
        };
        return names[featureKey] || featureKey;
    }

    /**
     * Confirm downgrade
     */
    async confirmDowngrade() {
        const confirmBtn = document.getElementById('confirm-downgrade-btn');
        const originalText = confirmBtn.textContent;

        try {
            confirmBtn.disabled = true;
            confirmBtn.textContent = 'Processing...';

            // Process downgrade
            if (this.flowData.targetTierId === 'free') {
                await this.subscriptionManager.downgradeToFree();
            } else {
                await this.subscriptionManager.upgradeSubscription(this.flowData.targetTierId);
            }

            this.showDowngradeSuccess();

        } catch (error) {
            console.error('❌ Downgrade failed:', error);
            this.showErrorMessage(`Downgrade failed: ${error.message}`);
        } finally {
            confirmBtn.disabled = false;
            confirmBtn.textContent = originalText;
        }
    }

    /**
     * Show downgrade success
     */
    showDowngradeSuccess() {
        this.hideDowngradeFlow();
        
        const targetTier = this.subscriptionManager.stripeManager.getSubscriptionTier(this.flowData.targetTierId);
        
        this.showFlowConfirmation({
            type: 'success',
            title: '✅ Downgrade Scheduled',
            message: `Your plan will be changed to ${targetTier.name} at the end of your billing period.`,
            details: [
                'You\'ll keep current features until then',
                'No immediate changes to your account',
                'You can upgrade again anytime'
            ]
        });
    }

    /**
     * Show flow confirmation
     */
    showFlowConfirmation(config) {
        const modal = document.getElementById('flow-confirmation-modal');
        const content = document.getElementById('confirmation-content');
        
        if (!modal || !content) return;

        content.innerHTML = `
            <div class="confirmation-${config.type}">
                <div class="confirmation-icon">${config.type === 'success' ? '✅' : '❌'}</div>
                <h3>${config.title}</h3>
                <p>${config.message}</p>
                ${config.details ? `
                    <ul class="confirmation-details">
                        ${config.details.map(detail => `<li>${detail}</li>`).join('')}
                    </ul>
                ` : ''}
            </div>
        `;

        modal.style.display = 'flex';
    }

    /**
     * Close confirmation modal
     */
    closeConfirmation() {
        const modal = document.getElementById('flow-confirmation-modal');
        if (modal) {
            modal.style.display = 'none';
        }
        
        // Refresh the page or update UI
        window.location.reload();
    }

    /**
     * Hide upgrade flow
     */
    hideUpgradeFlow() {
        const modal = document.getElementById('upgrade-flow-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * Hide downgrade flow
     */
    hideDowngradeFlow() {
        const modal = document.getElementById('downgrade-flow-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * Cancel current flow
     */
    cancelFlow() {
        this.hideUpgradeFlow();
        this.hideDowngradeFlow();
        this.currentFlow = null;
        this.flowData = {};
    }

    /**
     * Format card brand
     */
    formatCardBrand(brand) {
        if (!brand) return 'Card';
        return brand.charAt(0).toUpperCase() + brand.slice(1);
    }

    /**
     * Format billing address
     */
    formatBillingAddress(billingDetails) {
        if (!billingDetails) return 'No billing address';

        const { name, address } = billingDetails;
        if (!address) return name || 'No billing address';

        return `
            <div>${name || ''}</div>
            <div>${address.line1 || ''}</div>
            ${address.line2 ? `<div>${address.line2}</div>` : ''}
            <div>${address.city || ''}, ${address.state || ''} ${address.postal_code || ''}</div>
            <div>${address.country || ''}</div>
        `;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for subscription events
        document.addEventListener('subscription-upgrade-requested', (event) => {
            this.startUpgradeFlow(event.detail.tierId);
        });

        document.addEventListener('subscription-downgrade-requested', (event) => {
            this.startDowngradeFlow(event.detail.tierId);
        });
    }

    /**
     * Show error message
     */
    showErrorMessage(message) {
        // Create and show error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'flow-error-message';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10001;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        `;

        document.body.appendChild(errorDiv);

        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.cancelFlow();
        console.log('🧹 Subscription Flow Manager cleaned up');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.SubscriptionFlowManager = SubscriptionFlowManager;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionFlowManager;
}