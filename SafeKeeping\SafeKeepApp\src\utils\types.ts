// Core types for SafeKeep app

export interface Photo {
  id: string;
  uri: string;
  fileName: string;
  fileSize: number;
  timestamp: number;
  isBackedUp: boolean;
  hash?: string;
}

export interface Contact {
  id: string;
  displayName: string;
  phoneNumbers: string[];
  emailAddresses: string[];
  isBackedUp: boolean;
}

export interface SmsMessage {
  id: string;
  threadId: string;
  address: string;
  body: string;
  date: number;
  type: 'sent' | 'received';
  isBackedUp: boolean;
}

export interface BackupStatus {
  isRunning: boolean;
  progress: number;
  totalItems: number;
  completedItems: number;
  currentItem?: string;
  error?: string;
}

export interface User {
  id: string;
  email: string;
  isAuthenticated: boolean;
  storageUsed: number;
  storageLimit: number;
}

export interface AppSettings {
  autoBackup: boolean;
  wifiOnly: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly' | 'manual';
  notifications: boolean;
}

export type BackupType = 'photos' | 'contacts' | 'sms';

export interface BackupItem {
  id: string;
  type: BackupType;
  data: Photo | Contact | SmsMessage;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
}
