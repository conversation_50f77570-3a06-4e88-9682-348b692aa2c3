/**
 * Tutorial System Verification Script
 * Comprehensive testing and verification of tutorial system functionality
 */

class TutorialSystemVerifier {
    constructor() {
        this.testResults = [];
        this.tutorialSystem = null;
        this.testStartTime = Date.now();
    }

    async runAllTests() {
        console.log('🚀 Starting Tutorial System Verification...');
        
        try {
            await this.testSystemInitialization();
            await this.testTutorialDefinitions();
            await this.testOverlaySystem();
            await this.testTooltipSystem();
            await this.testProgressTracking();
            await this.testContextualHelp();
            await this.testNavigationControls();
            await this.testKeyboardShortcuts();
            await this.testResponsiveDesign();
            await this.testAccessibility();
            await this.testStateManagement();
            await this.testErrorHandling();
            
            this.generateReport();
        } catch (error) {
            console.error('❌ Verification failed:', error);
            this.addResult('System Verification', false, `Failed with error: ${error.message}`);
            this.generateReport();
        }
    }

    async testSystemInitialization() {
        console.log('Testing system initialization...');
        
        // Test class availability
        this.addResult(
            'TutorialSystem Class Available',
            typeof TutorialSystem !== 'undefined',
            'TutorialSystem class should be globally available'
        );

        // Test initialization
        try {
            this.tutorialSystem = new TutorialSystem();
            this.addResult(
                'System Initialization',
                this.tutorialSystem instanceof TutorialSystem,
                'TutorialSystem should initialize without errors'
            );
        } catch (error) {
            this.addResult(
                'System Initialization',
                false,
                `Initialization failed: ${error.message}`
            );
            return;
        }

        // Test DOM elements creation
        const overlayExists = document.getElementById('tutorial-overlay') !== null;
        const tooltipExists = document.getElementById('tutorial-tooltip') !== null;
        const progressExists = document.getElementById('tutorial-progress-indicator') !== null;
        const menuExists = document.getElementById('tutorial-menu') !== null;

        this.addResult('Overlay Element Created', overlayExists, 'Tutorial overlay should be created in DOM');
        this.addResult('Tooltip Element Created', tooltipExists, 'Tooltip element should be created in DOM');
        this.addResult('Progress Indicator Created', progressExists, 'Progress indicator should be created in DOM');
        this.addResult('Tutorial Menu Created', menuExists, 'Tutorial menu should be created in DOM');
    }

    async testTutorialDefinitions() {
        console.log('Testing tutorial definitions...');
        
        if (!this.tutorialSystem) return;

        const tutorials = this.tutorialSystem.getTutorials();
        
        // Test tutorial structure
        this.addResult(
            'Tutorial Definitions Exist',
            Object.keys(tutorials).length > 0,
            'Should have tutorial definitions'
        );

        // Test required tutorials
        const requiredTutorials = [
            'getting-started',
            'backup-process',
            'encryption-security',
            'restore-process',
            'scheduling',
            'subscription-features'
        ];

        requiredTutorials.forEach(tutorialId => {
            const exists = tutorials[tutorialId] !== undefined;
            this.addResult(
                `Tutorial "${tutorialId}" Exists`,
                exists,
                `Required tutorial ${tutorialId} should be defined`
            );

            if (exists) {
                const tutorial = tutorials[tutorialId];
                const hasRequiredFields = tutorial.title && tutorial.description && 
                                        tutorial.category && tutorial.steps && 
                                        Array.isArray(tutorial.steps);
                
                this.addResult(
                    `Tutorial "${tutorialId}" Structure`,
                    hasRequiredFields,
                    `Tutorial ${tutorialId} should have required fields`
                );

                // Test step structure
                if (tutorial.steps && tutorial.steps.length > 0) {
                    const firstStep = tutorial.steps[0];
                    const hasStepFields = firstStep.title && firstStep.description;
                    
                    this.addResult(
                        `Tutorial "${tutorialId}" Step Structure`,
                        hasStepFields,
                        `Tutorial ${tutorialId} steps should have required fields`
                    );
                }
            }
        });
    }

    async testOverlaySystem() {
        console.log('Testing overlay system...');
        
        if (!this.tutorialSystem) return;

        const overlay = document.getElementById('tutorial-overlay');
        
        // Test overlay elements
        const hasBackdrop = overlay.querySelector('.tutorial-backdrop') !== null;
        const hasSpotlight = overlay.querySelector('.tutorial-spotlight') !== null;
        const hasContent = overlay.querySelector('.tutorial-content') !== null;
        const hasControls = overlay.querySelector('.tutorial-controls') !== null;

        this.addResult('Overlay Backdrop', hasBackdrop, 'Overlay should have backdrop element');
        this.addResult('Overlay Spotlight', hasSpotlight, 'Overlay should have spotlight element');
        this.addResult('Overlay Content', hasContent, 'Overlay should have content element');
        this.addResult('Overlay Controls', hasControls, 'Overlay should have control buttons');

        // Test overlay visibility
        const initiallyHidden = overlay.style.display === 'none' || 
                               getComputedStyle(overlay).display === 'none';
        
        this.addResult(
            'Overlay Initially Hidden',
            initiallyHidden,
            'Overlay should be hidden initially'
        );

        // Test tutorial start
        try {
            this.tutorialSystem.startTutorial('getting-started');
            
            // Wait for animation
            await this.wait(500);
            
            const nowVisible = overlay.style.display === 'flex' || 
                              getComputedStyle(overlay).display === 'flex';
            
            this.addResult(
                'Overlay Shows on Tutorial Start',
                nowVisible,
                'Overlay should become visible when tutorial starts'
            );

            // Test close functionality
            this.tutorialSystem.closeTutorial();
            
            await this.wait(300);
            
            const hiddenAfterClose = overlay.style.display === 'none' || 
                                   getComputedStyle(overlay).display === 'none';
            
            this.addResult(
                'Overlay Hides on Close',
                hiddenAfterClose,
                'Overlay should hide when tutorial is closed'
            );
            
        } catch (error) {
            this.addResult(
                'Overlay Functionality',
                false,
                `Overlay functionality failed: ${error.message}`
            );
        }
    }

    async testTooltipSystem() {
        console.log('Testing tooltip system...');
        
        if (!this.tutorialSystem) return;

        const tooltip = document.getElementById('tutorial-tooltip');
        
        // Test tooltip elements
        const hasContent = tooltip.querySelector('.tooltip-content') !== null;
        const hasTitle = tooltip.querySelector('.tooltip-title') !== null;
        const hasDescription = tooltip.querySelector('.tooltip-description') !== null;
        const hasActions = tooltip.querySelector('.tooltip-actions') !== null;
        const hasArrow = tooltip.querySelector('.tooltip-arrow') !== null;

        this.addResult('Tooltip Content', hasContent, 'Tooltip should have content element');
        this.addResult('Tooltip Title', hasTitle, 'Tooltip should have title element');
        this.addResult('Tooltip Description', hasDescription, 'Tooltip should have description element');
        this.addResult('Tooltip Actions', hasActions, 'Tooltip should have action buttons');
        this.addResult('Tooltip Arrow', hasArrow, 'Tooltip should have arrow element');

        // Test tooltip functionality
        try {
            const testElement = document.body;
            this.tutorialSystem.showTooltip(
                testElement,
                'Test Title',
                'Test Description',
                'top'
            );

            const isVisible = tooltip.style.display === 'block';
            this.addResult(
                'Tooltip Show Functionality',
                isVisible,
                'Tooltip should become visible when shown'
            );

            this.tutorialSystem.hideTooltip();
            
            const isHidden = tooltip.style.display === 'none';
            this.addResult(
                'Tooltip Hide Functionality',
                isHidden,
                'Tooltip should hide when hideTooltip is called'
            );
            
        } catch (error) {
            this.addResult(
                'Tooltip Functionality',
                false,
                `Tooltip functionality failed: ${error.message}`
            );
        }
    }

    async testProgressTracking() {
        console.log('Testing progress tracking...');
        
        if (!this.tutorialSystem) return;

        // Test progress tracker initialization
        const hasProgressTracker = this.tutorialSystem.progressTracker !== null;
        this.addResult(
            'Progress Tracker Initialized',
            hasProgressTracker,
            'Progress tracker should be initialized'
        );

        // Test progress storage
        try {
            const testProgress = { 'test-tutorial': { completed: true, progress: 100 } };
            this.tutorialSystem.tutorialProgress = testProgress;
            this.tutorialSystem.saveProgress();

            // Clear and reload
            this.tutorialSystem.tutorialProgress = {};
            this.tutorialSystem.loadProgress();

            const progressLoaded = this.tutorialSystem.tutorialProgress['test-tutorial'] !== undefined;
            this.addResult(
                'Progress Persistence',
                progressLoaded,
                'Progress should persist in localStorage'
            );

            // Clean up
            delete this.tutorialSystem.tutorialProgress['test-tutorial'];
            this.tutorialSystem.saveProgress();
            
        } catch (error) {
            this.addResult(
                'Progress Tracking',
                false,
                `Progress tracking failed: ${error.message}`
            );
        }

        // Test progress indicator updates
        const progressIndicator = document.getElementById('tutorial-progress-indicator');
        const hasProgressRing = progressIndicator.querySelector('.progress-ring-progress') !== null;
        const hasProgressPercentage = progressIndicator.querySelector('.progress-percentage') !== null;

        this.addResult('Progress Ring Element', hasProgressRing, 'Progress indicator should have ring element');
        this.addResult('Progress Percentage Element', hasProgressPercentage, 'Progress indicator should have percentage element');
    }

    async testContextualHelp() {
        console.log('Testing contextual help...');
        
        if (!this.tutorialSystem) return;

        // Test contextual help manager
        const hasContextualHelp = this.tutorialSystem.contextualHelp !== null;
        this.addResult(
            'Contextual Help Manager',
            hasContextualHelp,
            'Contextual help manager should be initialized'
        );

        if (hasContextualHelp) {
            // Test help data
            const helpData = this.tutorialSystem.contextualHelp.helpData;
            const hasHelpData = Object.keys(helpData).length > 0;
            
            this.addResult(
                'Contextual Help Data',
                hasHelpData,
                'Contextual help should have predefined help data'
            );

            // Test help data addition
            try {
                this.tutorialSystem.contextualHelp.addHelpData('.test-element', {
                    title: 'Test Help',
                    description: 'Test description',
                    position: 'top'
                });

                const testHelpExists = helpData['.test-element'] !== undefined;
                this.addResult(
                    'Help Data Addition',
                    testHelpExists,
                    'Should be able to add new help data'
                );

                // Clean up
                delete helpData['.test-element'];
                
            } catch (error) {
                this.addResult(
                    'Help Data Addition',
                    false,
                    `Help data addition failed: ${error.message}`
                );
            }
        }
    }

    async testNavigationControls() {
        console.log('Testing navigation controls...');
        
        if (!this.tutorialSystem) return;

        const overlay = document.getElementById('tutorial-overlay');
        const prevBtn = overlay.querySelector('.btn-prev');
        const nextBtn = overlay.querySelector('.btn-next');
        const skipBtn = overlay.querySelector('.btn-skip');
        const finishBtn = overlay.querySelector('.btn-finish');
        const closeBtn = overlay.querySelector('.tutorial-close');

        // Test button existence
        this.addResult('Previous Button', prevBtn !== null, 'Should have previous button');
        this.addResult('Next Button', nextBtn !== null, 'Should have next button');
        this.addResult('Skip Button', skipBtn !== null, 'Should have skip button');
        this.addResult('Finish Button', finishBtn !== null, 'Should have finish button');
        this.addResult('Close Button', closeBtn !== null, 'Should have close button');

        // Test navigation functionality
        try {
            this.tutorialSystem.startTutorial('getting-started');
            await this.wait(300);

            const initialStep = this.tutorialSystem.currentStep;
            
            // Test next step
            this.tutorialSystem.nextStep();
            const afterNext = this.tutorialSystem.currentStep;
            
            this.addResult(
                'Next Step Navigation',
                afterNext > initialStep,
                'Next step should advance tutorial'
            );

            // Test previous step
            this.tutorialSystem.previousStep();
            const afterPrev = this.tutorialSystem.currentStep;
            
            this.addResult(
                'Previous Step Navigation',
                afterPrev < afterNext,
                'Previous step should go back in tutorial'
            );

            this.tutorialSystem.closeTutorial();
            
        } catch (error) {
            this.addResult(
                'Navigation Controls',
                false,
                `Navigation controls failed: ${error.message}`
            );
        }
    }

    async testKeyboardShortcuts() {
        console.log('Testing keyboard shortcuts...');
        
        if (!this.tutorialSystem) return;

        try {
            // Start a tutorial
            this.tutorialSystem.startTutorial('getting-started');
            await this.wait(300);

            // Test Escape key
            const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
            document.dispatchEvent(escapeEvent);
            
            await this.wait(300);
            
            const closedByEscape = !this.tutorialSystem.isActive;
            this.addResult(
                'Escape Key Closes Tutorial',
                closedByEscape,
                'Escape key should close active tutorial'
            );

            // Test arrow key navigation
            this.tutorialSystem.startTutorial('getting-started');
            await this.wait(300);

            const initialStep = this.tutorialSystem.currentStep;
            
            const rightArrowEvent = new KeyboardEvent('keydown', { 
                key: 'ArrowRight', 
                ctrlKey: true 
            });
            document.dispatchEvent(rightArrowEvent);
            
            await this.wait(100);
            
            const advancedByArrow = this.tutorialSystem.currentStep > initialStep;
            this.addResult(
                'Ctrl+Right Arrow Navigation',
                advancedByArrow,
                'Ctrl+Right Arrow should advance tutorial step'
            );

            this.tutorialSystem.closeTutorial();
            
        } catch (error) {
            this.addResult(
                'Keyboard Shortcuts',
                false,
                `Keyboard shortcuts failed: ${error.message}`
            );
        }
    }

    async testResponsiveDesign() {
        console.log('Testing responsive design...');
        
        // Test mobile viewport
        const originalWidth = window.innerWidth;
        
        try {
            // Simulate mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375
            });
            
            window.dispatchEvent(new Event('resize'));
            await this.wait(100);

            const overlay = document.getElementById('tutorial-overlay');
            const content = overlay.querySelector('.tutorial-content');
            const menu = document.getElementById('tutorial-menu');
            
            // Check mobile styles are applied
            const contentStyles = getComputedStyle(content);
            const menuStyles = getComputedStyle(menu.querySelector('.tutorial-menu-content'));
            
            this.addResult(
                'Mobile Content Sizing',
                contentStyles.maxWidth.includes('90vw') || contentStyles.maxWidth.includes('calc'),
                'Tutorial content should adapt to mobile viewport'
            );

            // Restore original width
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: originalWidth
            });
            
            window.dispatchEvent(new Event('resize'));
            
        } catch (error) {
            this.addResult(
                'Responsive Design',
                false,
                `Responsive design test failed: ${error.message}`
            );
        }
    }

    async testAccessibility() {
        console.log('Testing accessibility features...');
        
        const overlay = document.getElementById('tutorial-overlay');
        const tooltip = document.getElementById('tutorial-tooltip');
        const menu = document.getElementById('tutorial-menu');

        // Test ARIA labels
        const closeBtn = overlay.querySelector('.tutorial-close');
        const menuToggle = menu.querySelector('.tutorial-menu-toggle');
        
        const hasCloseAriaLabel = closeBtn.hasAttribute('aria-label');
        const hasMenuAriaLabel = menuToggle.hasAttribute('aria-label');
        
        this.addResult('Close Button ARIA Label', hasCloseAriaLabel, 'Close button should have aria-label');
        this.addResult('Menu Toggle ARIA Label', hasMenuAriaLabel, 'Menu toggle should have aria-label');

        // Test focus management
        try {
            this.tutorialSystem.startTutorial('getting-started');
            await this.wait(300);

            const focusableElements = overlay.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            this.addResult(
                'Focusable Elements in Overlay',
                focusableElements.length > 0,
                'Overlay should contain focusable elements'
            );

            this.tutorialSystem.closeTutorial();
            
        } catch (error) {
            this.addResult(
                'Accessibility Features',
                false,
                `Accessibility test failed: ${error.message}`
            );
        }

        // Test minimum touch target sizes
        const buttons = document.querySelectorAll('.btn-tutorial, .btn-tooltip, .tutorial-menu-toggle');
        let minSizesMet = true;
        
        buttons.forEach(button => {
            const styles = getComputedStyle(button);
            const minHeight = parseInt(styles.minHeight) || parseInt(styles.height);
            const minWidth = parseInt(styles.minWidth) || parseInt(styles.width);
            
            if (minHeight < 44 || minWidth < 44) {
                minSizesMet = false;
            }
        });
        
        this.addResult(
            'Minimum Touch Target Sizes',
            minSizesMet,
            'Interactive elements should meet minimum 44px touch target size'
        );
    }

    async testStateManagement() {
        console.log('Testing state management...');
        
        if (!this.tutorialSystem) return;

        try {
            // Test tutorial state
            this.tutorialSystem.startTutorial('getting-started');
            
            this.addResult(
                'Tutorial State Active',
                this.tutorialSystem.isActive === true,
                'Tutorial should be marked as active when started'
            );

            this.addResult(
                'Current Tutorial Set',
                this.tutorialSystem.currentTutorial === 'getting-started',
                'Current tutorial should be set correctly'
            );

            this.addResult(
                'Current Step Initialized',
                this.tutorialSystem.currentStep === 0,
                'Current step should start at 0'
            );

            // Test state cleanup
            this.tutorialSystem.closeTutorial();
            
            this.addResult(
                'Tutorial State Inactive',
                this.tutorialSystem.isActive === false,
                'Tutorial should be marked as inactive when closed'
            );

            // Test reset functionality
            this.tutorialSystem.tutorialProgress = { 'test': { completed: true } };
            this.tutorialSystem.resetAllProgress();
            
            const progressReset = Object.keys(this.tutorialSystem.tutorialProgress).length === 0;
            this.addResult(
                'Progress Reset Functionality',
                progressReset,
                'Reset should clear all tutorial progress'
            );
            
        } catch (error) {
            this.addResult(
                'State Management',
                false,
                `State management failed: ${error.message}`
            );
        }
    }

    async testErrorHandling() {
        console.log('Testing error handling...');
        
        if (!this.tutorialSystem) return;

        // Test invalid tutorial ID
        try {
            this.tutorialSystem.startTutorial('non-existent-tutorial');
            
            this.addResult(
                'Invalid Tutorial ID Handling',
                !this.tutorialSystem.isActive,
                'Should handle invalid tutorial IDs gracefully'
            );
            
        } catch (error) {
            this.addResult(
                'Invalid Tutorial ID Handling',
                true,
                'Should handle invalid tutorial IDs without crashing'
            );
        }

        // Test missing DOM elements
        try {
            const originalElement = document.getElementById('tutorial-overlay');
            originalElement.remove();
            
            // Try to start tutorial without overlay
            this.tutorialSystem.startTutorial('getting-started');
            
            this.addResult(
                'Missing DOM Elements Handling',
                true,
                'Should handle missing DOM elements gracefully'
            );

            // Restore element
            document.body.appendChild(originalElement);
            
        } catch (error) {
            this.addResult(
                'Missing DOM Elements Handling',
                false,
                `Error handling failed: ${error.message}`
            );
        }

        // Test localStorage errors
        try {
            const originalSetItem = localStorage.setItem;
            localStorage.setItem = () => { throw new Error('Storage quota exceeded'); };
            
            this.tutorialSystem.saveProgress();
            
            this.addResult(
                'localStorage Error Handling',
                true,
                'Should handle localStorage errors gracefully'
            );

            // Restore localStorage
            localStorage.setItem = originalSetItem;
            
        } catch (error) {
            this.addResult(
                'localStorage Error Handling',
                false,
                `localStorage error handling failed: ${error.message}`
            );
        }
    }

    addResult(testName, passed, description) {
        this.testResults.push({
            name: testName,
            passed,
            description,
            timestamp: Date.now()
        });

        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${description}`);
    }

    generateReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const successRate = ((passedTests / totalTests) * 100).toFixed(1);
        const duration = Date.now() - this.testStartTime;

        console.log('\n📊 TUTORIAL SYSTEM VERIFICATION REPORT');
        console.log('=====================================');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${successRate}%`);
        console.log(`Duration: ${duration}ms`);
        console.log('=====================================');

        if (failedTests > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(result => {
                    console.log(`- ${result.name}: ${result.description}`);
                });
        }

        // Create visual report
        this.createVisualReport(totalTests, passedTests, failedTests, successRate, duration);
    }

    createVisualReport(total, passed, failed, successRate, duration) {
        const reportDiv = document.createElement('div');
        reportDiv.id = 'verification-report';
        reportDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10004;
            max-width: 400px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        reportDiv.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; color: #333;">Verification Report</h3>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
            </div>
            <div style="margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span>Success Rate:</span>
                    <strong style="color: ${successRate >= 90 ? '#28a745' : successRate >= 70 ? '#ffc107' : '#dc3545'};">${successRate}%</strong>
                </div>
                <div style="background: #e9ecef; border-radius: 10px; height: 8px; overflow: hidden;">
                    <div style="background: ${successRate >= 90 ? '#28a745' : successRate >= 70 ? '#ffc107' : '#dc3545'}; height: 100%; width: ${successRate}%; transition: width 0.3s ease;"></div>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 15px; text-align: center;">
                <div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #333;">${total}</div>
                    <div style="font-size: 0.8rem; color: #666;">Total</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">${passed}</div>
                    <div style="font-size: 0.8rem; color: #666;">Passed</div>
                </div>
                <div>
                    <div style="font-size: 1.5rem; font-weight: bold; color: #dc3545;">${failed}</div>
                    <div style="font-size: 0.8rem; color: #666;">Failed</div>
                </div>
            </div>
            <div style="font-size: 0.9rem; color: #666; text-align: center;">
                Completed in ${duration}ms
            </div>
            ${failed > 0 ? `
                <details style="margin-top: 15px;">
                    <summary style="cursor: pointer; color: #dc3545; font-weight: 500;">View Failed Tests</summary>
                    <div style="margin-top: 10px; max-height: 200px; overflow-y: auto;">
                        ${this.testResults.filter(r => !r.passed).map(result => `
                            <div style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-bottom: 5px; font-size: 0.8rem;">
                                <strong>${result.name}</strong><br>
                                <span style="color: #666;">${result.description}</span>
                            </div>
                        `).join('')}
                    </div>
                </details>
            ` : ''}
        `;

        // Remove existing report
        const existingReport = document.getElementById('verification-report');
        if (existingReport) {
            existingReport.remove();
        }

        document.body.appendChild(reportDiv);
    }

    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Auto-run verification when script loads
document.addEventListener('DOMContentLoaded', () => {
    // Wait for tutorial system to initialize
    setTimeout(() => {
        const verifier = new TutorialSystemVerifier();
        verifier.runAllTests();
    }, 2000);
});

// Export for manual testing
window.TutorialSystemVerifier = TutorialSystemVerifier;