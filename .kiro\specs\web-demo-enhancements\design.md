# Design Document - Web Demo Enhancements

## Overview

The enhanced SafeKeep web demo will transform the current basic demonstration into a comprehensive showcase of all SafeKeep capabilities. The design focuses on creating a production-like experience that demonstrates real-time operations, user authentication, data encryption, backup scheduling, and analytics - all within a web browser environment.

The enhanced demo will serve as both a testing platform and a sales/demonstration tool, showing potential users exactly how SafeKeep works without requiring mobile device installation.

## Architecture

### Enhanced Architecture Diagram

```mermaid
graph TB
    UI[Enhanced Web UI] --> AM[Authentication Manager]
    UI --> BM[Backup Manager]
    UI --> RM[Restore Manager]
    UI --> SM[Schedule Manager]
    UI --> EM[Encryption Manager]
    UI --> DM[Demo Manager]
    UI --> PM[Payment Manager]
    UI --> SM[Subscription Manager]
    
    AM --> SA[Supabase Auth]
    BM --> RT[Real-time Engine]
    RM --> FS[File System Simulator]
    SM --> TS[Task Scheduler]
    EM --> CE[Crypto Engine]
    DM --> DS[Demo State]
    PM --> ST[Stripe API]
    SM --> SB[Subscription Backend]
    
    RT --> WS[WebSocket/Polling]
    FS --> SB[Supabase Storage]
    TS --> TM[Timer Manager]
    CE --> CL[Crypto Libraries]
    DS --> LS[Local Storage]
    
    SB --> DB[(Database)]
    SB --> ST[(Storage)]
```

### Component Architecture

The enhanced demo follows a modular architecture with specialized managers for each major feature area:

1. **Authentication Manager**: Handles user signup, signin, and profile management
2. **Backup Manager**: Orchestrates backup operations with real-time progress
3. **Restore Manager**: Simulates data restoration and decryption
4. **Schedule Manager**: Manages automated backup scheduling
5. **Encryption Manager**: Demonstrates encryption/decryption processes
6. **Demo Manager**: Coordinates demo state and user experience flow
7. **Payment Manager**: Handles Stripe integration and payment processing
8. **Subscription Manager**: Manages subscription tiers and feature access control

## Components and Interfaces

### Core Enhanced Interfaces

```typescript
interface EnhancedBackupSession {
  id: string;
  userId: string;
  type: 'manual' | 'scheduled' | 'demo';
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  progress: {
    contacts: DetailedProgress;
    messages: DetailedProgress;
    photos: DetailedProgress;
    overall: DetailedProgress;
  };
  encryption: {
    algorithm: string;
    keyId: string;
    encryptedSize: number;
    originalSize: number;
  };
  performance: {
    transferRate: number;
    averageFileSize: number;
    errorRate: number;
  };
}

interface DetailedProgress {
  total: number;
  completed: number;
  failed: number;
  inProgress: number;
  currentItem?: string;
  estimatedTimeRemaining?: number;
  transferRate?: number;
  errors: BackupError[];
}

interface BackupSchedule {
  id: string;
  userId: string;
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly' | 'custom';
  time: string; // HH:MM format
  days?: number[]; // 0-6, Sunday-Saturday
  conditions: {
    wifiOnly: boolean;
    batteryLevel: number;
    storageThreshold: number;
  };
  lastRun?: Date;
  nextRun?: Date;
}

interface EncryptionDemo {
  originalData: any;
  encryptedData: string;
  algorithm: string;
  keySize: number;
  iv: string;
  salt: string;
  encryptionTime: number;
  compressionRatio: number;
}

interface RestoreSession {
  id: string;
  userId: string;
  backupSessionId: string;
  dataTypes: ('contacts' | 'messages' | 'photos')[];
  status: 'pending' | 'downloading' | 'decrypting' | 'completed' | 'failed';
  progress: {
    download: number;
    decryption: number;
    verification: number;
  };
  restoredData: {
    contacts?: any[];
    messages?: any[];
    photos?: any[];
  };
}

interface SubscriptionTier {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: {
    storageLimit: number; // in GB
    backupFrequency: string[];
    prioritySupport: boolean;
    advancedEncryption: boolean;
    multiDeviceSync: boolean;
    backupHistory: number; // days
    restoreSpeed: 'standard' | 'priority';
  };
  stripeProductId: string;
  stripePriceId: string;
}

interface UserSubscription {
  id: string;
  userId: string;
  tierId: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'trialing';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  paymentMethod?: {
    type: string;
    last4: string;
    brand: string;
    expiryMonth: number;
    expiryYear: number;
  };
}

interface PaymentSession {
  id: string;
  userId: string;
  tierId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  stripeSessionId: string;
  createdAt: Date;
  completedAt?: Date;
}
```

### Enhanced UI Components

#### 1. Authentication Dashboard
- **Purpose**: Complete user authentication and profile management
- **Features**:
  - Fixed signup with proper error handling
  - User profile display with storage usage
  - Session management and security settings
  - Demo user creation for testing

#### 2. Real-time Backup Console
- **Purpose**: Live backup monitoring with detailed progress
- **Features**:
  - Real-time progress bars with file-level detail
  - Live transfer rate and ETA calculations
  - Error handling with retry mechanisms
  - Pause/resume backup functionality
  - Network condition simulation

#### 3. Encryption Demonstration Panel
- **Purpose**: Visual demonstration of encryption/decryption
- **Features**:
  - Before/after data comparison
  - Encryption algorithm selection
  - Key generation and management
  - Performance metrics display
  - Security strength indicators

#### 4. Backup History and Analytics
- **Purpose**: Comprehensive backup history and performance analytics
- **Features**:
  - Chronological backup session list
  - Detailed session information
  - Success/failure analytics
  - Storage usage trends
  - Performance charts and graphs

#### 5. Restore Simulation Interface
- **Purpose**: Demonstrate data restoration capabilities
- **Features**:
  - Backup selection for restoration
  - Decryption progress visualization
  - Restored data preview
  - Data integrity verification
  - Selective restore options

#### 6. Advanced Scheduling Console
- **Purpose**: Backup scheduling and automation
- **Features**:
  - Schedule configuration interface
  - Condition-based backup triggers
  - Schedule simulation and testing
  - Next run predictions
  - Schedule conflict resolution

#### 7. Subscription Management Dashboard
- **Purpose**: Demonstrate subscription tiers and payment processing
- **Features**:
  - Pricing tier comparison table
  - Feature access visualization
  - Stripe payment integration
  - Subscription status management
  - Usage limits and quota tracking

#### 8. Payment Processing Interface
- **Purpose**: Secure payment handling with Stripe
- **Features**:
  - Stripe Elements integration
  - Payment method management
  - Invoice history and downloads
  - Billing address management
  - Tax calculation and compliance

#### 9. Premium Features Showcase
- **Purpose**: Demonstrate value of paid subscriptions
- **Features**:
  - Feature comparison matrix
  - Premium-only functionality demos
  - Usage analytics and insights
  - Priority support simulation
  - Advanced security features

## Data Models

### Enhanced Database Schema

```sql
-- Enhanced backup sessions with detailed tracking
CREATE TABLE enhanced_backup_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  session_type TEXT CHECK (session_type IN ('manual', 'scheduled', 'demo')),
  status TEXT CHECK (status IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  
  -- Progress tracking
  total_items INTEGER DEFAULT 0,
  completed_items INTEGER DEFAULT 0,
  failed_items INTEGER DEFAULT 0,
  in_progress_items INTEGER DEFAULT 0,
  
  -- Performance metrics
  transfer_rate BIGINT DEFAULT 0, -- bytes per second
  average_file_size BIGINT DEFAULT 0,
  error_rate DECIMAL(5,2) DEFAULT 0,
  
  -- Encryption details
  encryption_algorithm TEXT DEFAULT 'AES-256-GCM',
  encryption_key_id TEXT,
  original_size BIGINT DEFAULT 0,
  encrypted_size BIGINT DEFAULT 0,
  compression_ratio DECIMAL(5,2) DEFAULT 1.0,
  
  -- Metadata
  configuration JSONB,
  error_log JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Backup schedules
CREATE TABLE backup_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  name TEXT NOT NULL,
  enabled BOOLEAN DEFAULT true,
  frequency TEXT CHECK (frequency IN ('daily', 'weekly', 'monthly', 'custom')),
  schedule_time TIME NOT NULL,
  schedule_days INTEGER[], -- Array of days 0-6
  
  -- Conditions
  wifi_only BOOLEAN DEFAULT true,
  min_battery_level INTEGER DEFAULT 20,
  max_storage_usage DECIMAL(5,2) DEFAULT 80.0,
  
  -- Tracking
  last_run_at TIMESTAMPTZ,
  next_run_at TIMESTAMPTZ,
  run_count INTEGER DEFAULT 0,
  success_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Restore sessions
CREATE TABLE restore_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  backup_session_id UUID REFERENCES enhanced_backup_sessions(id),
  status TEXT CHECK (status IN ('pending', 'downloading', 'decrypting', 'completed', 'failed')),
  
  -- Data types to restore
  restore_contacts BOOLEAN DEFAULT false,
  restore_messages BOOLEAN DEFAULT false,
  restore_photos BOOLEAN DEFAULT false,
  
  -- Progress
  download_progress DECIMAL(5,2) DEFAULT 0,
  decryption_progress DECIMAL(5,2) DEFAULT 0,
  verification_progress DECIMAL(5,2) DEFAULT 0,
  
  -- Results
  restored_data JSONB,
  verification_results JSONB,
  
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Demo state tracking
CREATE TABLE demo_state (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  demo_type TEXT NOT NULL,
  state_data JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, demo_type)
);

-- Subscription tiers
CREATE TABLE subscription_tiers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  price_monthly DECIMAL(10,2) NOT NULL,
  price_yearly DECIMAL(10,2) NOT NULL,
  
  -- Features
  storage_limit_gb INTEGER NOT NULL,
  backup_frequency TEXT[] NOT NULL,
  priority_support BOOLEAN DEFAULT false,
  advanced_encryption BOOLEAN DEFAULT false,
  multi_device_sync BOOLEAN DEFAULT false,
  backup_history_days INTEGER DEFAULT 30,
  restore_speed TEXT DEFAULT 'standard',
  
  -- Stripe integration
  stripe_product_id TEXT NOT NULL,
  stripe_price_monthly_id TEXT NOT NULL,
  stripe_price_yearly_id TEXT NOT NULL,
  
  -- Status
  active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User subscriptions
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) UNIQUE,
  tier_id UUID REFERENCES subscription_tiers(id),
  
  -- Subscription details
  status TEXT CHECK (status IN ('active', 'canceled', 'past_due', 'unpaid', 'trialing')) DEFAULT 'trialing',
  billing_interval TEXT CHECK (billing_interval IN ('month', 'year')) DEFAULT 'month',
  
  -- Stripe details
  stripe_customer_id TEXT NOT NULL,
  stripe_subscription_id TEXT,
  stripe_payment_method_id TEXT,
  
  -- Billing periods
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  trial_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN DEFAULT false,
  canceled_at TIMESTAMPTZ,
  
  -- Usage tracking
  storage_used_gb DECIMAL(10,2) DEFAULT 0,
  backup_count_this_period INTEGER DEFAULT 0,
  last_backup_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Payment sessions (for tracking Stripe checkout sessions)
CREATE TABLE payment_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  tier_id UUID REFERENCES subscription_tiers(id),
  
  -- Payment details
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'usd',
  billing_interval TEXT CHECK (billing_interval IN ('month', 'year')),
  
  -- Stripe details
  stripe_session_id TEXT NOT NULL UNIQUE,
  stripe_payment_intent_id TEXT,
  
  -- Status
  status TEXT CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled')) DEFAULT 'pending',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours')
);

-- Invoices (for demo purposes)
CREATE TABLE demo_invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  subscription_id UUID REFERENCES user_subscriptions(id),
  
  -- Invoice details
  invoice_number TEXT NOT NULL UNIQUE,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'usd',
  tax_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  
  -- Status
  status TEXT CHECK (status IN ('draft', 'open', 'paid', 'void', 'uncollectible')) DEFAULT 'paid',
  
  -- Stripe details
  stripe_invoice_id TEXT,
  stripe_charge_id TEXT,
  
  -- Dates
  invoice_date DATE NOT NULL,
  due_date DATE NOT NULL,
  paid_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Local Storage Schema

```typescript
interface EnhancedDemoState {
  currentUser: {
    id: string;
    email: string;
    profile: UserProfile;
    preferences: UserPreferences;
  };
  
  activeSessions: {
    backup?: string;
    restore?: string;
    encryption?: string;
  };
  
  demoProgress: {
    completedFeatures: string[];
    currentTutorialStep: number;
    userExperience: 'beginner' | 'intermediate' | 'advanced';
  };
  
  performanceMetrics: {
    sessionCount: number;
    totalDataBackedUp: number;
    averageBackupTime: number;
    successRate: number;
  };
  
  scheduledTasks: {
    nextBackup?: Date;
    activeSchedules: BackupSchedule[];
    simulatedConditions: {
      networkType: 'wifi' | 'cellular' | 'offline';
      batteryLevel: number;
      storageUsage: number;
    };
  };
  
  subscription: {
    currentTier: SubscriptionTier;
    status: 'free' | 'trial' | 'active' | 'past_due' | 'canceled';
    usageThisPeriod: {
      storageUsed: number;
      backupCount: number;
      restoreCount: number;
    };
    paymentMethod?: {
      type: string;
      last4: string;
      brand: string;
    };
    billingHistory: {
      invoices: any[];
      nextBillingDate?: Date;
    };
  };
}
```

## Error Handling

### Enhanced Error Categories

1. **Authentication Errors**: Signup failures, login issues, session management
2. **Real-time Communication Errors**: WebSocket disconnections, polling failures
3. **Encryption Errors**: Key generation failures, encryption/decryption errors
4. **Scheduling Errors**: Timer conflicts, condition evaluation failures
5. **Restore Errors**: Download failures, decryption errors, data corruption
6. **Demo State Errors**: State synchronization issues, tutorial flow problems

### Error Recovery Strategies

1. **Graceful Degradation**: Continue demo functionality even if some features fail
2. **Automatic Retry**: Intelligent retry logic for transient failures
3. **User Guidance**: Clear error messages with suggested actions
4. **Demo Reset**: Ability to reset demo state and start fresh
5. **Offline Mode**: Continue demo functionality without network connectivity

## Testing Strategy

### Enhanced Testing Approach

1. **User Experience Testing**
   - Complete user journey from signup to restore
   - Tutorial flow and feature discovery
   - Performance under various simulated conditions

2. **Real-time Feature Testing**
   - WebSocket connection stability
   - Progress update accuracy
   - Concurrent session handling

3. **Encryption Demonstration Testing**
   - Encryption/decryption accuracy
   - Key management security
   - Performance impact measurement

4. **Scheduling System Testing**
   - Schedule execution accuracy
   - Condition evaluation correctness
   - Conflict resolution logic

5. **Cross-browser Compatibility**
   - Modern browser support
   - Mobile browser responsiveness
   - Progressive web app features

### Performance Requirements

1. **Real-time Updates**: Progress updates within 100ms
2. **Encryption Speed**: Demonstrate encryption of 1MB in under 1 second
3. **UI Responsiveness**: All interactions respond within 200ms
4. **Memory Usage**: Maintain under 100MB memory usage
5. **Network Efficiency**: Minimize API calls through intelligent caching