/**
 * Subscription Management Dashboard for SafeKeep Web Demo
 * Comprehensive subscription status display, billing history, and management interface
 */

class SubscriptionManagementDashboard {
    constructor(stripeManager, subscriptionTierConfig, usageQuotaManager) {
        this.stripeManager = stripeManager;
        this.tierConfig = subscriptionTierConfig;
        this.usageManager = usageQuotaManager;
        
        // Dashboard state
        this.currentUser = null;
        this.currentSubscription = null;
        this.billingHistory = [];
        this.paymentMethods = [];
        this.usageStats = null;
        
        // UI elements
        this.dashboardContainer = null;
        this.statusSection = null;
        this.billingSection = null;
        this.paymentMethodsSection = null;
        this.usageSection = null;
        this.modificationSection = null;
        
        // Event listeners
        this.eventListeners = new Map();
        
        this.initialize();
    }

    /**
     * Initialize the subscription management dashboard
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Subscription Management Dashboard...');
            
            // Create dashboard container
            this.createDashboardContainer();
            
            // Load current user and subscription data
            await this.loadUserData();
            
            // Render dashboard sections
            this.renderDashboard();
            
            // Setup event listeners
            this.setupEventListeners();
            
            console.log('✅ Subscription Management Dashboard initialized');
        } catch (error) {
            console.error('❌ Failed to initialize Subscription Management Dashboard:', error);
            this.showError('Failed to initialize dashboard', error.message);
        }
    }

    /**
     * Create dashboard container
     */
    createDashboardContainer() {
        this.dashboardContainer = document.createElement('div');
        this.dashboardContainer.className = 'subscription-dashboard';
        this.dashboardContainer.innerHTML = `
            <div class="subscription-header">
                <h3>Subscription Management</h3>
                <div class="subscription-status">
                    <div class="status-badge" id="subscription-status-badge">Loading...</div>
                </div>
            </div>
            
            <div class="dashboard-content">
                <div class="dashboard-section" id="current-plan-section">
                    <h4>Current Plan</h4>
                    <div id="current-plan-content">Loading...</div>
                </div>
                
                <div class="dashboard-section" id="usage-tracking-section">
                    <h4>Usage & Quotas</h4>
                    <div id="usage-tracking-content">Loading...</div>
                </div>
                
                <div class="dashboard-section" id="billing-history-section">
                    <h4>Billing History</h4>
                    <div id="billing-history-content">Loading...</div>
                </div>
                
                <div class="dashboard-section" id="payment-methods-section">
                    <h4>Payment Methods</h4>
                    <div id="payment-methods-content">Loading...</div>
                </div>
                
                <div class="dashboard-section" id="subscription-modification-section">
                    <h4>Manage Subscription</h4>
                    <div id="subscription-modification-content">Loading...</div>
                </div>
            </div>
        `;
    }

    /**
     * Load user data
     */
    async loadUserData() {
        try {
            // Get current user (demo user for now)
            this.currentUser = {
                id: 'demo_user_' + Date.now(),
                email: '<EMAIL>',
                name: 'Demo User'
            };
            
            // Load or create subscription
            await this.loadSubscriptionData();
            
            // Load billing history
            await this.loadBillingHistory();
            
            // Load payment methods
            await this.loadPaymentMethods();
            
            // Load usage statistics
            await this.loadUsageStatistics();
            
        } catch (error) {
            console.error('❌ Failed to load user data:', error);
            throw error;
        }
    }

    /**
     * Load subscription data
     */
    async loadSubscriptionData() {
        try {
            // Check for existing subscription in localStorage
            const savedSubscription = localStorage.getItem('demo_subscription');
            
            if (savedSubscription) {
                this.currentSubscription = JSON.parse(savedSubscription);
            } else {
                // Create default free subscription
                this.currentSubscription = {
                    id: 'sub_demo_' + Date.now(),
                    userId: this.currentUser.id,
                    tierId: 'free',
                    status: 'active',
                    currentPeriodStart: new Date(),
                    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    cancelAtPeriodEnd: false,
                    createdAt: new Date(),
                    tier: this.tierConfig.getTier('free')
                };
                
                // Save to localStorage
                localStorage.setItem('demo_subscription', JSON.stringify(this.currentSubscription));
            }
            
            console.log('✅ Subscription data loaded:', this.currentSubscription.tierId);
        } catch (error) {
            console.error('❌ Failed to load subscription data:', error);
            throw error;
        }
    }

    /**
     * Load billing history
     */
    async loadBillingHistory() {
        try {
            // Check for existing billing history in localStorage
            const savedHistory = localStorage.getItem('demo_billing_history');
            
            if (savedHistory) {
                this.billingHistory = JSON.parse(savedHistory);
            } else {
                // Generate demo billing history
                this.billingHistory = this.generateDemoBillingHistory();
                localStorage.setItem('demo_billing_history', JSON.stringify(this.billingHistory));
            }
            
            console.log('✅ Billing history loaded:', this.billingHistory.length, 'invoices');
        } catch (error) {
            console.error('❌ Failed to load billing history:', error);
            throw error;
        }
    }

    /**
     * Generate demo billing history
     */
    generateDemoBillingHistory() {
        const history = [];
        const currentDate = new Date();
        
        // Generate 6 months of billing history
        for (let i = 0; i < 6; i++) {
            const invoiceDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const tier = i < 2 ? 'basic' : 'free'; // Upgraded to basic 2 months ago
            const tierInfo = this.tierConfig.getTier(tier);
            
            if (tierInfo && tierInfo.price > 0) {
                history.push({
                    id: `inv_demo_${Date.now()}_${i}`,
                    invoiceNumber: `INV-${invoiceDate.getFullYear()}-${String(invoiceDate.getMonth() + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
                    date: invoiceDate,
                    amount: tierInfo.price,
                    currency: tierInfo.currency,
                    status: 'paid',
                    description: `${tierInfo.name} Plan - Monthly Subscription`,
                    downloadUrl: '#',
                    paymentMethod: {
                        type: 'card',
                        last4: '4242',
                        brand: 'visa'
                    }
                });
            }
        }
        
        return history.reverse(); // Most recent first
    }

    /**
     * Load payment methods
     */
    async loadPaymentMethods() {
        try {
            // Check for existing payment methods in localStorage
            const savedMethods = localStorage.getItem('demo_payment_methods');
            
            if (savedMethods) {
                this.paymentMethods = JSON.parse(savedMethods);
            } else {
                // Generate demo payment methods
                this.paymentMethods = [
                    {
                        id: 'pm_demo_' + Date.now(),
                        type: 'card',
                        card: {
                            brand: 'visa',
                            last4: '4242',
                            expMonth: 12,
                            expYear: 2025
                        },
                        isDefault: true,
                        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                    }
                ];
                
                localStorage.setItem('demo_payment_methods', JSON.stringify(this.paymentMethods));
            }
            
            console.log('✅ Payment methods loaded:', this.paymentMethods.length, 'methods');
        } catch (error) {
            console.error('❌ Failed to load payment methods:', error);
            throw error;
        }
    }

    /**
     * Load usage statistics
     */
    async loadUsageStatistics() {
        try {
            if (this.usageManager) {
                this.usageStats = this.usageManager.getUsageStatistics(this.currentUser.id);
            } else {
                // Generate demo usage statistics
                this.usageStats = this.generateDemoUsageStats();
            }
            
            console.log('✅ Usage statistics loaded');
        } catch (error) {
            console.error('❌ Failed to load usage statistics:', error);
            throw error;
        }
    }

    /**
     * Generate demo usage statistics
     */
    generateDemoUsageStats() {
        const tier = this.tierConfig.getTier(this.currentSubscription.tierId);
        const limits = tier.limits;
        
        return {
            current: {
                storageUsed: Math.random() * limits.maxStorageGB * 0.7, // 70% of limit
                backupCount: Math.floor(Math.random() * (limits.maxBackupsPerMonth || 10) * 0.6),
                restoreCount: Math.floor(Math.random() * (limits.maxRestoresPerMonth || 5) * 0.4),
                periodStart: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                periodEnd: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)
            },
            limits: {
                storage: {
                    current: Math.random() * limits.maxStorageGB * 0.7,
                    limit: limits.maxStorageGB,
                    percentage: Math.random() * 70,
                    allowed: true
                },
                backup: {
                    current: Math.floor(Math.random() * (limits.maxBackupsPerMonth || 10) * 0.6),
                    limit: limits.maxBackupsPerMonth,
                    percentage: Math.random() * 60,
                    allowed: true
                },
                restore: {
                    current: Math.floor(Math.random() * (limits.maxRestoresPerMonth || 5) * 0.4),
                    limit: limits.maxRestoresPerMonth,
                    percentage: Math.random() * 40,
                    allowed: true
                }
            },
            warnings: [],
            recommendations: []
        };
    }

    /**
     * Render the complete dashboard
     */
    renderDashboard() {
        this.renderSubscriptionStatus();
        this.renderCurrentPlan();
        this.renderUsageTracking();
        this.renderBillingHistory();
        this.renderPaymentMethods();
        this.renderSubscriptionModification();
    }

    /**
     * Render subscription status
     */
    renderSubscriptionStatus() {
        const statusBadge = document.getElementById('subscription-status-badge');
        if (!statusBadge) return;
        
        const tier = this.tierConfig.getTier(this.currentSubscription.tierId);
        statusBadge.className = `status-badge ${this.currentSubscription.tierId}`;
        statusBadge.textContent = `${tier.name} Plan - ${this.currentSubscription.status}`;
    }

    /**
     * Render current plan section
     */
    renderCurrentPlan() {
        const content = document.getElementById('current-plan-content');
        if (!content) return;
        
        const tier = this.tierConfig.getTier(this.currentSubscription.tierId);
        const pricing = this.tierConfig.getTierPricing(this.currentSubscription.tierId);
        
        content.innerHTML = `
            <div class="plan-card current">
                <div class="plan-header">
                    <h5>${tier.name} Plan</h5>
                    <div class="plan-price">${pricing.formattedPrice}</div>
                </div>
                
                <div class="plan-details">
                    <div class="plan-info">
                        <div class="info-item">
                            <span class="label">Status:</span>
                            <span class="value status-${this.currentSubscription.status}">${this.currentSubscription.status}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Current Period:</span>
                            <span class="value">${this.formatDate(this.currentSubscription.currentPeriodStart)} - ${this.formatDate(this.currentSubscription.currentPeriodEnd)}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Next Billing:</span>
                            <span class="value">${this.formatDate(this.currentSubscription.currentPeriodEnd)}</span>
                        </div>
                        ${this.currentSubscription.cancelAtPeriodEnd ? `
                        <div class="info-item">
                            <span class="label">Cancellation:</span>
                            <span class="value warning">Will cancel at period end</span>
                        </div>
                        ` : ''}
                    </div>
                    
                    <div class="plan-features">
                        <h6>Plan Features</h6>
                        <div class="feature-list">
                            <div class="feature-item">
                                <span class="feature-icon">💾</span>
                                <span>${tier.limits.maxStorageGB}GB Storage</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🔄</span>
                                <span>${tier.limits.maxBackupsPerMonth === -1 ? 'Unlimited' : tier.limits.maxBackupsPerMonth} Backups/month</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">📥</span>
                                <span>${tier.limits.maxRestoresPerMonth === -1 ? 'Unlimited' : tier.limits.maxRestoresPerMonth} Restores/month</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🔒</span>
                                <span>${tier.features.advanced_encryption ? 'Advanced' : 'Basic'} Encryption</span>
                            </div>
                            <div class="feature-item">
                                <span class="feature-icon">🎧</span>
                                <span>${tier.features.priority_support ? 'Priority' : 'Standard'} Support</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render usage tracking section
     */
    renderUsageTracking() {
        const content = document.getElementById('usage-tracking-content');
        if (!content) return;
        
        const stats = this.usageStats;
        const tier = this.tierConfig.getTier(this.currentSubscription.tierId);
        
        content.innerHTML = `
            <div class="usage-overview">
                <div class="usage-period">
                    <span class="period-label">Current Billing Period:</span>
                    <span class="period-dates">${this.formatDate(stats.current.periodStart)} - ${this.formatDate(stats.current.periodEnd)}</span>
                </div>
                
                <div class="usage-metrics">
                    <div class="usage-metric">
                        <div class="metric-header">
                            <span class="metric-label">Storage Usage</span>
                            <span class="metric-value">${stats.limits.storage.current.toFixed(2)}GB / ${stats.limits.storage.limit}GB</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${stats.limits.storage.percentage}%"></div>
                        </div>
                        <div class="metric-details">
                            <span>${stats.limits.storage.percentage.toFixed(1)}% used</span>
                            <span>${(stats.limits.storage.limit - stats.limits.storage.current).toFixed(2)}GB remaining</span>
                        </div>
                    </div>
                    
                    <div class="usage-metric">
                        <div class="metric-header">
                            <span class="metric-label">Backups This Month</span>
                            <span class="metric-value">${stats.limits.backup.current} / ${stats.limits.backup.limit === -1 ? '∞' : stats.limits.backup.limit}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${stats.limits.backup.percentage}%"></div>
                        </div>
                        <div class="metric-details">
                            <span>${stats.limits.backup.percentage.toFixed(1)}% used</span>
                            <span>${stats.limits.backup.limit === -1 ? 'Unlimited' : (stats.limits.backup.limit - stats.limits.backup.current)} remaining</span>
                        </div>
                    </div>
                    
                    <div class="usage-metric">
                        <div class="metric-header">
                            <span class="metric-label">Restores This Month</span>
                            <span class="metric-value">${stats.limits.restore.current} / ${stats.limits.restore.limit === -1 ? '∞' : stats.limits.restore.limit}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${stats.limits.restore.percentage}%"></div>
                        </div>
                        <div class="metric-details">
                            <span>${stats.limits.restore.percentage.toFixed(1)}% used</span>
                            <span>${stats.limits.restore.limit === -1 ? 'Unlimited' : (stats.limits.restore.limit - stats.limits.restore.current)} remaining</span>
                        </div>
                    </div>
                </div>
                
                ${stats.warnings.length > 0 ? `
                <div class="usage-warnings">
                    <h6>Usage Warnings</h6>
                    ${stats.warnings.map(warning => `
                        <div class="warning-item ${warning.level}">
                            <span class="warning-icon">⚠️</span>
                            <span class="warning-message">${warning.message}</span>
                        </div>
                    `).join('')}
                </div>
                ` : ''}
                
                ${stats.recommendations.length > 0 ? `
                <div class="usage-recommendations">
                    <h6>Recommendations</h6>
                    ${stats.recommendations.map(rec => `
                        <div class="recommendation-item">
                            <span class="rec-icon">💡</span>
                            <span class="rec-message">${rec.message}</span>
                            <button class="btn btn-sm" onclick="subscriptionDashboard.handleRecommendation('${rec.action}', '${rec.type}')">
                                ${rec.action === 'upgrade' ? 'Upgrade Plan' : 'Learn More'}
                            </button>
                        </div>
                    `).join('')}
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Render billing history section
     */
    renderBillingHistory() {
        const content = document.getElementById('billing-history-content');
        if (!content) return;
        
        content.innerHTML = `
            <div class="billing-overview">
                <div class="billing-summary">
                    <div class="summary-item">
                        <span class="summary-label">Total Invoices:</span>
                        <span class="summary-value">${this.billingHistory.length}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Total Paid:</span>
                        <span class="summary-value">$${this.calculateTotalPaid().toFixed(2)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Next Payment:</span>
                        <span class="summary-value">${this.formatDate(this.currentSubscription.currentPeriodEnd)}</span>
                    </div>
                </div>
                
                <div class="billing-actions">
                    <button class="btn secondary" onclick="subscriptionDashboard.downloadAllInvoices()">
                        📄 Download All Invoices
                    </button>
                    <button class="btn secondary" onclick="subscriptionDashboard.updateBillingAddress()">
                        📍 Update Billing Address
                    </button>
                </div>
            </div>
            
            <div class="invoice-list">
                <div class="invoice-header">
                    <span>Invoice</span>
                    <span>Date</span>
                    <span>Amount</span>
                    <span>Status</span>
                    <span>Actions</span>
                </div>
                
                ${this.billingHistory.length === 0 ? `
                    <div class="no-invoices">
                        <p>No billing history available</p>
                        <small>Invoices will appear here after your first payment</small>
                    </div>
                ` : this.billingHistory.map(invoice => `
                    <div class="invoice-item">
                        <span class="invoice-number">${invoice.invoiceNumber}</span>
                        <span class="invoice-date">${this.formatDate(invoice.date)}</span>
                        <span class="invoice-amount">$${(invoice.amount / 100).toFixed(2)}</span>
                        <span class="invoice-status status-${invoice.status}">${invoice.status}</span>
                        <div class="invoice-actions">
                            <button class="btn btn-sm" onclick="subscriptionDashboard.downloadInvoice('${invoice.id}')">
                                📥 Download
                            </button>
                            <button class="btn btn-sm secondary" onclick="subscriptionDashboard.viewInvoice('${invoice.id}')">
                                👁️ View
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Render payment methods section
     */
    renderPaymentMethods() {
        const content = document.getElementById('payment-methods-content');
        if (!content) return;
        
        content.innerHTML = `
            <div class="payment-methods-overview">
                <div class="payment-summary">
                    <span>${this.paymentMethods.length} payment method${this.paymentMethods.length !== 1 ? 's' : ''} on file</span>
                    <button class="btn" onclick="subscriptionDashboard.addPaymentMethod()">
                        ➕ Add Payment Method
                    </button>
                </div>
            </div>
            
            <div class="payment-methods-list">
                ${this.paymentMethods.length === 0 ? `
                    <div class="no-payment-methods">
                        <p>No payment methods on file</p>
                        <small>Add a payment method to enable automatic billing</small>
                        <button class="btn" onclick="subscriptionDashboard.addPaymentMethod()">
                            ➕ Add Payment Method
                        </button>
                    </div>
                ` : this.paymentMethods.map(method => `
                    <div class="payment-method-item ${method.isDefault ? 'default' : ''}">
                        <div class="payment-method-info">
                            <div class="card-info">
                                <span class="card-brand">${method.card.brand.toUpperCase()}</span>
                                <span class="card-number">•••• •••• •••• ${method.card.last4}</span>
                                <span class="card-expiry">${method.card.expMonth}/${method.card.expYear}</span>
                            </div>
                            ${method.isDefault ? '<span class="default-badge">Default</span>' : ''}
                        </div>
                        
                        <div class="payment-method-actions">
                            ${!method.isDefault ? `
                                <button class="btn btn-sm" onclick="subscriptionDashboard.setDefaultPaymentMethod('${method.id}')">
                                    Set Default
                                </button>
                            ` : ''}
                            <button class="btn btn-sm secondary" onclick="subscriptionDashboard.editPaymentMethod('${method.id}')">
                                ✏️ Edit
                            </button>
                            <button class="btn btn-sm danger" onclick="subscriptionDashboard.removePaymentMethod('${method.id}')">
                                🗑️ Remove
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Render subscription modification section
     */
    renderSubscriptionModification() {
        const content = document.getElementById('subscription-modification-content');
        if (!content) return;
        
        const currentTier = this.tierConfig.getTier(this.currentSubscription.tierId);
        const allTiers = this.tierConfig.getAllTiers();
        const upgradePath = this.tierConfig.getUpgradePath(this.currentSubscription.tierId);
        const downgradePath = this.tierConfig.getDowngradePath(this.currentSubscription.tierId);
        
        content.innerHTML = `
            <div class="subscription-actions">
                <div class="action-section">
                    <h6>Plan Changes</h6>
                    <div class="plan-options">
                        ${upgradePath.length > 0 ? `
                            <div class="upgrade-options">
                                <h7>Upgrade Options</h7>
                                ${upgradePath.map(tier => {
                                    const pricing = this.tierConfig.getTierPricing(tier.id);
                                    return `
                                        <div class="plan-option upgrade">
                                            <div class="plan-info">
                                                <span class="plan-name">${tier.name}</span>
                                                <span class="plan-price">${pricing.formattedPrice}</span>
                                            </div>
                                            <div class="plan-benefits">
                                                <span>+${tier.limits.maxStorageGB - currentTier.limits.maxStorageGB}GB Storage</span>
                                                ${tier.features.priority_support && !currentTier.features.priority_support ? '<span>Priority Support</span>' : ''}
                                                ${tier.features.advanced_encryption && !currentTier.features.advanced_encryption ? '<span>Advanced Encryption</span>' : ''}
                                            </div>
                                            <button class="btn" onclick="subscriptionDashboard.upgradePlan('${tier.id}')">
                                                Upgrade to ${tier.name}
                                            </button>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        ` : ''}
                        
                        ${downgradePath.length > 0 ? `
                            <div class="downgrade-options">
                                <h7>Downgrade Options</h7>
                                ${downgradePath.map(tier => {
                                    const pricing = this.tierConfig.getTierPricing(tier.id);
                                    return `
                                        <div class="plan-option downgrade">
                                            <div class="plan-info">
                                                <span class="plan-name">${tier.name}</span>
                                                <span class="plan-price">${pricing.formattedPrice}</span>
                                            </div>
                                            <div class="plan-limitations">
                                                <span>${tier.limits.maxStorageGB}GB Storage (${currentTier.limits.maxStorageGB - tier.limits.maxStorageGB}GB less)</span>
                                                ${!tier.features.priority_support && currentTier.features.priority_support ? '<span>No Priority Support</span>' : ''}
                                                ${!tier.features.advanced_encryption && currentTier.features.advanced_encryption ? '<span>Basic Encryption Only</span>' : ''}
                                            </div>
                                            <button class="btn secondary" onclick="subscriptionDashboard.downgradePlan('${tier.id}')">
                                                Downgrade to ${tier.name}
                                            </button>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="action-section">
                    <h6>Subscription Management</h6>
                    <div class="management-actions">
                        ${!this.currentSubscription.cancelAtPeriodEnd ? `
                            <div class="action-item">
                                <div class="action-info">
                                    <span class="action-title">Pause Subscription</span>
                                    <span class="action-description">Temporarily pause your subscription and billing</span>
                                </div>
                                <button class="btn secondary" onclick="subscriptionDashboard.pauseSubscription()">
                                    ⏸️ Pause
                                </button>
                            </div>
                            
                            <div class="action-item">
                                <div class="action-info">
                                    <span class="action-title">Cancel Subscription</span>
                                    <span class="action-description">Cancel at the end of current billing period</span>
                                </div>
                                <button class="btn danger" onclick="subscriptionDashboard.cancelSubscription()">
                                    ❌ Cancel
                                </button>
                            </div>
                        ` : `
                            <div class="action-item">
                                <div class="action-info">
                                    <span class="action-title">Reactivate Subscription</span>
                                    <span class="action-description">Resume your subscription and billing</span>
                                </div>
                                <button class="btn success" onclick="subscriptionDashboard.reactivateSubscription()">
                                    ✅ Reactivate
                                </button>
                            </div>
                        `}
                        
                        <div class="action-item">
                            <div class="action-info">
                                <span class="action-title">Update Billing Cycle</span>
                                <span class="action-description">Switch between monthly and annual billing</span>
                            </div>
                            <button class="btn secondary" onclick="subscriptionDashboard.updateBillingCycle()">
                                🔄 Change Cycle
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Global dashboard reference for onclick handlers
        window.subscriptionDashboard = this;
        
        // Add refresh listener
        this.eventListeners.set('refresh', () => {
            this.refreshDashboard();
        });
        
        // Add usage update listener
        if (this.usageManager) {
            this.usageManager.addUsageListener((data) => {
                this.handleUsageUpdate(data);
            });
        }
    }

    /**
     * Handle recommendation actions
     */
    async handleRecommendation(action, type) {
        try {
            if (action === 'upgrade') {
                const upgradePath = this.tierConfig.getUpgradePath(this.currentSubscription.tierId);
                if (upgradePath.length > 0) {
                    await this.upgradePlan(upgradePath[0].id);
                }
            } else if (action === 'optimize') {
                this.showOptimizationTips(type);
            }
        } catch (error) {
            console.error('❌ Failed to handle recommendation:', error);
            this.showError('Failed to process recommendation', error.message);
        }
    }

    /**
     * Upgrade plan
     */
    async upgradePlan(tierId) {
        try {
            const tier = this.tierConfig.getTier(tierId);
            const pricing = this.tierConfig.getTierPricing(tierId);
            
            const confirmed = confirm(`Upgrade to ${tier.name} plan for ${pricing.formattedPrice}?`);
            if (!confirmed) return;
            
            // Simulate upgrade process
            this.showLoading('Upgrading subscription...');
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Update subscription
            this.currentSubscription.tierId = tierId;
            this.currentSubscription.tier = tier;
            this.currentSubscription.updatedAt = new Date();
            
            // Save to localStorage
            localStorage.setItem('demo_subscription', JSON.stringify(this.currentSubscription));
            
            // Refresh dashboard
            await this.refreshDashboard();
            
            this.hideLoading();
            this.showSuccess(`Successfully upgraded to ${tier.name} plan!`);
            
        } catch (error) {
            console.error('❌ Failed to upgrade plan:', error);
            this.hideLoading();
            this.showError('Failed to upgrade plan', error.message);
        }
    }

    /**
     * Downgrade plan
     */
    async downgradePlan(tierId) {
        try {
            const tier = this.tierConfig.getTier(tierId);
            const pricing = this.tierConfig.getTierPricing(tierId);
            
            const confirmed = confirm(`Downgrade to ${tier.name} plan for ${pricing.formattedPrice}? This will take effect at the end of your current billing period.`);
            if (!confirmed) return;
            
            // Simulate downgrade process
            this.showLoading('Scheduling downgrade...');
            
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Update subscription (scheduled for period end)
            this.currentSubscription.scheduledTierId = tierId;
            this.currentSubscription.scheduledTier = tier;
            this.currentSubscription.updatedAt = new Date();
            
            // Save to localStorage
            localStorage.setItem('demo_subscription', JSON.stringify(this.currentSubscription));
            
            // Refresh dashboard
            await this.refreshDashboard();
            
            this.hideLoading();
            this.showSuccess(`Downgrade to ${tier.name} plan scheduled for ${this.formatDate(this.currentSubscription.currentPeriodEnd)}`);
            
        } catch (error) {
            console.error('❌ Failed to downgrade plan:', error);
            this.hideLoading();
            this.showError('Failed to downgrade plan', error.message);
        }
    }

    /**
     * Cancel subscription
     */
    async cancelSubscription() {
        try {
            const confirmed = confirm('Are you sure you want to cancel your subscription? You will retain access until the end of your current billing period.');
            if (!confirmed) return;
            
            this.showLoading('Canceling subscription...');
            
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Update subscription
            this.currentSubscription.cancelAtPeriodEnd = true;
            this.currentSubscription.updatedAt = new Date();
            
            // Save to localStorage
            localStorage.setItem('demo_subscription', JSON.stringify(this.currentSubscription));
            
            // Refresh dashboard
            await this.refreshDashboard();
            
            this.hideLoading();
            this.showSuccess('Subscription canceled. Access will continue until ' + this.formatDate(this.currentSubscription.currentPeriodEnd));
            
        } catch (error) {
            console.error('❌ Failed to cancel subscription:', error);
            this.hideLoading();
            this.showError('Failed to cancel subscription', error.message);
        }
    }

    /**
     * Reactivate subscription
     */
    async reactivateSubscription() {
        try {
            const confirmed = confirm('Reactivate your subscription and resume billing?');
            if (!confirmed) return;
            
            this.showLoading('Reactivating subscription...');
            
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Update subscription
            this.currentSubscription.cancelAtPeriodEnd = false;
            this.currentSubscription.status = 'active';
            this.currentSubscription.updatedAt = new Date();
            
            // Save to localStorage
            localStorage.setItem('demo_subscription', JSON.stringify(this.currentSubscription));
            
            // Refresh dashboard
            await this.refreshDashboard();
            
            this.hideLoading();
            this.showSuccess('Subscription reactivated successfully!');
            
        } catch (error) {
            console.error('❌ Failed to reactivate subscription:', error);
            this.hideLoading();
            this.showError('Failed to reactivate subscription', error.message);
        }
    }

    /**
     * Pause subscription
     */
    async pauseSubscription() {
        try {
            const confirmed = confirm('Pause your subscription? Billing will be suspended and you will lose access to premium features.');
            if (!confirmed) return;
            
            this.showLoading('Pausing subscription...');
            
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Update subscription
            this.currentSubscription.status = 'paused';
            this.currentSubscription.pausedAt = new Date();
            this.currentSubscription.updatedAt = new Date();
            
            // Save to localStorage
            localStorage.setItem('demo_subscription', JSON.stringify(this.currentSubscription));
            
            // Refresh dashboard
            await this.refreshDashboard();
            
            this.hideLoading();
            this.showSuccess('Subscription paused successfully');
            
        } catch (error) {
            console.error('❌ Failed to pause subscription:', error);
            this.hideLoading();
            this.showError('Failed to pause subscription', error.message);
        }
    }

    /**
     * Add payment method
     */
    async addPaymentMethod() {
        try {
            // Show payment method form (simplified for demo)
            const cardNumber = prompt('Enter card number (use **************** for demo):');
            if (!cardNumber) return;
            
            const expiry = prompt('Enter expiry (MM/YY):');
            if (!expiry) return;
            
            const [expMonth, expYear] = expiry.split('/').map(s => parseInt(s.trim()));
            
            this.showLoading('Adding payment method...');
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Add new payment method
            const newMethod = {
                id: 'pm_demo_' + Date.now(),
                type: 'card',
                card: {
                    brand: 'visa',
                    last4: cardNumber.slice(-4),
                    expMonth: expMonth,
                    expYear: 2000 + expYear
                },
                isDefault: this.paymentMethods.length === 0,
                createdAt: new Date()
            };
            
            this.paymentMethods.push(newMethod);
            localStorage.setItem('demo_payment_methods', JSON.stringify(this.paymentMethods));
            
            // Refresh payment methods section
            this.renderPaymentMethods();
            
            this.hideLoading();
            this.showSuccess('Payment method added successfully!');
            
        } catch (error) {
            console.error('❌ Failed to add payment method:', error);
            this.hideLoading();
            this.showError('Failed to add payment method', error.message);
        }
    }

    /**
     * Remove payment method
     */
    async removePaymentMethod(methodId) {
        try {
            const method = this.paymentMethods.find(m => m.id === methodId);
            if (!method) return;
            
            const confirmed = confirm(`Remove payment method ending in ${method.card.last4}?`);
            if (!confirmed) return;
            
            this.showLoading('Removing payment method...');
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Remove payment method
            this.paymentMethods = this.paymentMethods.filter(m => m.id !== methodId);
            
            // Set new default if needed
            if (method.isDefault && this.paymentMethods.length > 0) {
                this.paymentMethods[0].isDefault = true;
            }
            
            localStorage.setItem('demo_payment_methods', JSON.stringify(this.paymentMethods));
            
            // Refresh payment methods section
            this.renderPaymentMethods();
            
            this.hideLoading();
            this.showSuccess('Payment method removed successfully!');
            
        } catch (error) {
            console.error('❌ Failed to remove payment method:', error);
            this.hideLoading();
            this.showError('Failed to remove payment method', error.message);
        }
    }

    /**
     * Set default payment method
     */
    async setDefaultPaymentMethod(methodId) {
        try {
            this.showLoading('Updating default payment method...');
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Update default payment method
            this.paymentMethods.forEach(method => {
                method.isDefault = method.id === methodId;
            });
            
            localStorage.setItem('demo_payment_methods', JSON.stringify(this.paymentMethods));
            
            // Refresh payment methods section
            this.renderPaymentMethods();
            
            this.hideLoading();
            this.showSuccess('Default payment method updated!');
            
        } catch (error) {
            console.error('❌ Failed to set default payment method:', error);
            this.hideLoading();
            this.showError('Failed to update default payment method', error.message);
        }
    }

    /**
     * Download invoice
     */
    downloadInvoice(invoiceId) {
        const invoice = this.billingHistory.find(inv => inv.id === invoiceId);
        if (!invoice) return;
        
        // Simulate invoice download
        const invoiceData = this.generateInvoicePDF(invoice);
        const blob = new Blob([invoiceData], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${invoice.invoiceNumber}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('Invoice downloaded successfully!');
    }

    /**
     * Generate invoice PDF data (simplified)
     */
    generateInvoicePDF(invoice) {
        return `Invoice: ${invoice.invoiceNumber}
Date: ${this.formatDate(invoice.date)}
Amount: $${(invoice.amount / 100).toFixed(2)}
Status: ${invoice.status}
Description: ${invoice.description}

Thank you for using SafeKeep!`;
    }

    /**
     * Calculate total paid
     */
    calculateTotalPaid() {
        return this.billingHistory
            .filter(invoice => invoice.status === 'paid')
            .reduce((total, invoice) => total + invoice.amount, 0) / 100;
    }

    /**
     * Refresh dashboard
     */
    async refreshDashboard() {
        try {
            this.showLoading('Refreshing dashboard...');
            
            // Reload data
            await this.loadUserData();
            
            // Re-render dashboard
            this.renderDashboard();
            
            this.hideLoading();
            
        } catch (error) {
            console.error('❌ Failed to refresh dashboard:', error);
            this.hideLoading();
            this.showError('Failed to refresh dashboard', error.message);
        }
    }

    /**
     * Handle usage updates
     */
    handleUsageUpdate(data) {
        // Update usage statistics
        this.loadUsageStatistics().then(() => {
            this.renderUsageTracking();
        });
    }

    /**
     * Utility methods
     */
    formatDate(date) {
        if (typeof date === 'string') {
            date = new Date(date);
        }
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    showLoading(message) {
        // Create or update loading overlay
        let overlay = document.getElementById('dashboard-loading');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'dashboard-loading';
            overlay.className = 'loading-overlay';
            overlay.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-message">${message}</div>
                </div>
            `;
            document.body.appendChild(overlay);
        } else {
            overlay.querySelector('.loading-message').textContent = message;
            overlay.style.display = 'flex';
        }
    }

    hideLoading() {
        const overlay = document.getElementById('dashboard-loading');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(title, message) {
        this.showNotification(`${title}: ${message}`, 'error');
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    /**
     * Get dashboard container for mounting
     */
    getDashboardContainer() {
        return this.dashboardContainer;
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        // Clear event listeners
        this.eventListeners.clear();
        
        // Remove global reference
        if (window.subscriptionDashboard === this) {
            delete window.subscriptionDashboard;
        }
        
        console.log('🧹 Subscription Management Dashboard cleaned up');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.SubscriptionManagementDashboard = SubscriptionManagementDashboard;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SubscriptionManagementDashboard;
}