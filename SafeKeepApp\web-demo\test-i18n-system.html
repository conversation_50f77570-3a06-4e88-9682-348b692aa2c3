<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Multi-Language Support Demo</title>
    <link rel="stylesheet" href="i18n-styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .language-selector-container {
            position: absolute;
            top: 20px;
            right: 20px;
        }

        .header h1 {
            font-size: 3rem;
            margin: 0 0 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin: 0;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .demo-card h3 {
            color: #333;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .demo-icon {
            font-size: 2rem;
        }

        .demo-content {
            color: #666;
            line-height: 1.6;
        }

        .format-example {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            border-left: 4px solid #4facfe;
        }

        .format-label {
            font-weight: 600;
            color: #4facfe;
            margin-bottom: 5px;
        }

        .format-value {
            color: #333;
        }

        .translation-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }

        .translation-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid #4facfe;
        }

        .translation-key {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 5px;
            font-family: 'Courier New', monospace;
        }

        .translation-value {
            font-weight: 500;
            color: #333;
        }

        .rtl-demo {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .rtl-demo.rtl {
            direction: rtl;
            text-align: right;
        }

        .currency-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .currency-item {
            background: #e8f5e8;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #c3e6c3;
        }

        .currency-code {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 5px;
        }

        .currency-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #28a745;
        }

        .date-demo {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin: 15px 0;
        }

        .date-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f0f8ff;
            padding: 10px 15px;
            border-radius: 6px;
            border-left: 3px solid #4facfe;
        }

        .date-format {
            font-size: 0.8rem;
            color: #666;
        }

        .date-value {
            font-weight: 500;
            color: #333;
        }

        .controls {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .controls h3 {
            color: #333;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
        }

        .control-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .control-input {
            padding: 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .control-input:focus {
            outline: none;
            border-color: #4facfe;
        }

        .control-button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .results {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .results h3 {
            color: #333;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
        }

        .result-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #4facfe;
        }

        .result-label {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .result-value {
            font-size: 1.1rem;
            font-weight: 500;
            color: #333;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
        }

        /* RTL Support */
        .rtl {
            direction: rtl;
        }

        .rtl .demo-grid {
            direction: rtl;
        }

        .rtl .translation-demo {
            direction: rtl;
        }

        .rtl .control-group {
            direction: rtl;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
                color: #e2e8f0;
            }

            .header,
            .demo-card,
            .controls,
            .results {
                background: #2d3748;
                color: #e2e8f0;
            }

            .header h1 {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .demo-card h3,
            .controls h3,
            .results h3 {
                color: #e2e8f0;
            }

            .format-example,
            .translation-item,
            .date-item,
            .result-item {
                background: #374151;
                color: #e2e8f0;
            }

            .control-input {
                background: #374151;
                border-color: #4a5568;
                color: #e2e8f0;
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .translation-demo {
                grid-template-columns: 1fr;
            }

            .currency-demo {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .control-group {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .language-selector-container {
                position: static;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="language-selector-container">
                <div id="language-selector"></div>
            </div>
            <h1 data-i18n="safekeep.appName">SafeKeep</h1>
            <p data-i18n="safekeep.tagline">Your Data, Secured Forever</p>
        </div>

        <div class="demo-grid">
            <!-- Translation Demo -->
            <div class="demo-card">
                <h3>
                    <span class="demo-icon">🌐</span>
                    <span data-i18n="navigation.translations">Translations</span>
                </h3>
                <div class="demo-content">
                    <p data-i18n="demo.translation_description">See how text changes based on the selected language.</p>
                    
                    <div class="translation-demo">
                        <div class="translation-item">
                            <div class="translation-key">common.loading</div>
                            <div class="translation-value" data-i18n="common.loading">Loading...</div>
                        </div>
                        <div class="translation-item">
                            <div class="translation-key">common.save</div>
                            <div class="translation-value" data-i18n="common.save">Save</div>
                        </div>
                        <div class="translation-item">
                            <div class="translation-key">safekeep.backup</div>
                            <div class="translation-value" data-i18n="safekeep.backup">Backup</div>
                        </div>
                        <div class="translation-item">
                            <div class="translation-key">safekeep.security</div>
                            <div class="translation-value" data-i18n="safekeep.security">Security</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Number Formatting Demo -->
            <div class="demo-card">
                <h3>
                    <span class="demo-icon">🔢</span>
                    <span data-i18n="demo.number_formatting">Number Formatting</span>
                </h3>
                <div class="demo-content">
                    <p data-i18n="demo.number_description">Numbers formatted according to locale conventions.</p>
                    
                    <div class="format-example">
                        <div class="format-label" data-i18n="demo.large_number">Large Number:</div>
                        <div class="format-value" id="number-demo">1,234,567.89</div>
                    </div>
                    
                    <div class="format-example">
                        <div class="format-label" data-i18n="demo.percentage">Percentage:</div>
                        <div class="format-value" id="percentage-demo">75.5%</div>
                    </div>
                </div>
            </div>

            <!-- Currency Demo -->
            <div class="demo-card">
                <h3>
                    <span class="demo-icon">💰</span>
                    <span data-i18n="demo.currency_formatting">Currency Formatting</span>
                </h3>
                <div class="demo-content">
                    <p data-i18n="demo.currency_description">Currency values in different locales.</p>
                    
                    <div class="currency-demo" id="currency-demo">
                        <!-- Currency examples will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Date & Time Demo -->
            <div class="demo-card">
                <h3>
                    <span class="demo-icon">📅</span>
                    <span data-i18n="demo.date_formatting">Date & Time Formatting</span>
                </h3>
                <div class="demo-content">
                    <p data-i18n="demo.date_description">Dates and times formatted for different locales.</p>
                    
                    <div class="date-demo" id="date-demo">
                        <!-- Date examples will be populated here -->
                    </div>
                </div>
            </div>

            <!-- RTL Support Demo -->
            <div class="demo-card">
                <h3>
                    <span class="demo-icon">↔️</span>
                    <span data-i18n="demo.rtl_support">RTL Support</span>
                </h3>
                <div class="demo-content">
                    <p data-i18n="demo.rtl_description">Right-to-left language support demonstration.</p>
                    
                    <div class="rtl-demo" id="rtl-demo">
                        <p data-i18n="demo.rtl_text">This text changes direction based on the selected language.</p>
                    </div>
                </div>
            </div>

            <!-- Address Formatting Demo -->
            <div class="demo-card">
                <h3>
                    <span class="demo-icon">🏠</span>
                    <span data-i18n="demo.address_formatting">Address Formatting</span>
                </h3>
                <div class="demo-content">
                    <p data-i18n="demo.address_description">Address formats vary by country and locale.</p>
                    
                    <div class="format-example" id="address-demo">
                        <!-- Address example will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Controls -->
        <div class="controls">
            <h3 data-i18n="demo.interactive_controls">Interactive Controls</h3>
            
            <div class="control-group">
                <div class="control-item">
                    <label class="control-label" data-i18n="demo.test_number">Test Number:</label>
                    <input type="number" class="control-input" id="test-number" value="1234567.89" step="0.01">
                </div>
                
                <div class="control-item">
                    <label class="control-label" data-i18n="demo.test_currency">Test Currency Amount:</label>
                    <input type="number" class="control-input" id="test-currency" value="99.99" step="0.01">
                </div>
                
                <div class="control-item">
                    <label class="control-label" data-i18n="demo.test_date">Test Date:</label>
                    <input type="date" class="control-input" id="test-date">
                </div>
                
                <div class="control-item">
                    <button class="control-button" onclick="updateFormattingExamples()" data-i18n="demo.update_examples">Update Examples</button>
                </div>
            </div>
        </div>

        <!-- Results -->
        <div class="results">
            <h3 data-i18n="demo.formatting_results">Formatting Results</h3>
            <div id="formatting-results">
                <!-- Results will be populated here -->
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 SafeKeep. Multi-Language Support Demo. All rights reserved.</p>
        </div>
    </div>

    <!-- Include the i18n system -->
    <script src="i18n-manager.js"></script>
    <script src="language-selector.js"></script>
    <script src="locale-formatter.js"></script>

    <script>
        let languageSelector;

        // Initialize the demo when page loads
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Initializing i18n demo...');
            
            // Initialize i18n system
            await i18n.initialize();
            
            // Create language selector
            languageSelector = new LanguageSelector('language-selector', {
                showFlags: true,
                showNativeNames: true,
                showCurrency: true,
                compact: false,
                position: 'bottom-right'
            });
            
            // Set up event listeners
            window.addEventListener('languageChanged', handleLanguageChange);
            
            // Initialize demo content
            updateAllDemos();
            
            // Set default date
            document.getElementById('test-date').value = new Date().toISOString().split('T')[0];
            
            console.log('i18n demo initialized successfully');
        });

        function handleLanguageChange(event) {
            console.log('Language changed to:', event.detail.language);
            
            // Update all demo content
            updateAllDemos();
            
            // Apply RTL if needed
            const isRTL = i18n.isRTL();
            document.body.classList.toggle('rtl', isRTL);
            document.body.classList.toggle('ltr', !isRTL);
            
            // Update RTL demo
            const rtlDemo = document.getElementById('rtl-demo');
            if (rtlDemo) {
                rtlDemo.classList.toggle('rtl', isRTL);
            }
            
            // Translate the page
            i18n.translatePage();
        }

        function updateAllDemos() {
            updateNumberDemo();
            updateCurrencyDemo();
            updateDateDemo();
            updateAddressDemo();
            updateFormattingExamples();
        }

        function updateNumberDemo() {
            const currentLang = i18n.getCurrentLanguage();
            const numberDemo = document.getElementById('number-demo');
            const percentageDemo = document.getElementById('percentage-demo');
            
            if (numberDemo) {
                const formatted = localeFormatter.formatNumber(1234567.89, currentLang);
                numberDemo.textContent = formatted;
            }
            
            if (percentageDemo) {
                const formatted = localeFormatter.formatNumber(0.755, currentLang, {
                    style: 'percent',
                    minimumFractionDigits: 1
                });
                percentageDemo.textContent = formatted;
            }
        }

        function updateCurrencyDemo() {
            const currentLang = i18n.getCurrentLanguage();
            const currencyDemo = document.getElementById('currency-demo');
            
            if (!currencyDemo) return;
            
            const amounts = [99.99, 1234.56, 0.99];
            const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY'];
            
            let html = '';
            amounts.forEach(amount => {
                currencies.forEach(currency => {
                    try {
                        const formatted = localeFormatter.formatCurrency(amount, currentLang, currency);
                        html += `
                            <div class="currency-item">
                                <div class="currency-code">${currency}</div>
                                <div class="currency-value">${formatted}</div>
                            </div>
                        `;
                    } catch (error) {
                        console.warn(`Failed to format ${amount} ${currency}:`, error);
                    }
                });
            });
            
            currencyDemo.innerHTML = html;
        }

        function updateDateDemo() {
            const currentLang = i18n.getCurrentLanguage();
            const dateDemo = document.getElementById('date-demo');
            
            if (!dateDemo) return;
            
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            
            const formats = [
                { label: 'Short Date', style: 'short', date: now },
                { label: 'Medium Date', style: 'medium', date: now },
                { label: 'Long Date', style: 'long', date: now },
                { label: 'Time', style: 'short', date: now, type: 'time' },
                { label: 'Relative (Yesterday)', date: yesterday, type: 'relative' },
                { label: 'Relative (Next Week)', date: nextWeek, type: 'relative' }
            ];
            
            let html = '';
            formats.forEach(format => {
                let formatted;
                try {
                    if (format.type === 'time') {
                        formatted = localeFormatter.formatTime(format.date, currentLang, format.style);
                    } else if (format.type === 'relative') {
                        formatted = localeFormatter.formatRelativeTime(format.date, currentLang);
                    } else {
                        formatted = localeFormatter.formatDate(format.date, currentLang, format.style);
                    }
                    
                    html += `
                        <div class="date-item">
                            <span class="date-format">${format.label}:</span>
                            <span class="date-value">${formatted}</span>
                        </div>
                    `;
                } catch (error) {
                    console.warn(`Failed to format date with ${format.label}:`, error);
                }
            });
            
            dateDemo.innerHTML = html;
        }

        function updateAddressDemo() {
            const currentLang = i18n.getCurrentLanguage();
            const addressDemo = document.getElementById('address-demo');
            
            if (!addressDemo) return;
            
            const sampleAddress = {
                street: '123 Main Street',
                city: 'New York',
                state: 'NY',
                postalCode: '10001',
                country: 'United States'
            };
            
            try {
                const formatted = localeFormatter.formatAddress(sampleAddress, currentLang);
                addressDemo.innerHTML = `<pre>${formatted}</pre>`;
            } catch (error) {
                console.warn('Failed to format address:', error);
                addressDemo.innerHTML = '<pre>Address formatting not available</pre>';
            }
        }

        function updateFormattingExamples() {
            const currentLang = i18n.getCurrentLanguage();
            const resultsContainer = document.getElementById('formatting-results');
            
            if (!resultsContainer) return;
            
            // Get values from inputs
            const testNumber = parseFloat(document.getElementById('test-number').value) || 0;
            const testCurrency = parseFloat(document.getElementById('test-currency').value) || 0;
            const testDateValue = document.getElementById('test-date').value;
            const testDate = testDateValue ? new Date(testDateValue) : new Date();
            
            let html = '';
            
            // Number formatting
            try {
                const formattedNumber = localeFormatter.formatNumber(testNumber, currentLang);
                html += `
                    <div class="result-item">
                        <div class="result-label">Formatted Number</div>
                        <div class="result-value">${formattedNumber}</div>
                    </div>
                `;
            } catch (error) {
                console.warn('Number formatting failed:', error);
            }
            
            // Currency formatting
            try {
                const langInfo = i18n.getCurrentLanguageInfo();
                const formattedCurrency = localeFormatter.formatCurrency(testCurrency, currentLang, langInfo.currency);
                html += `
                    <div class="result-item">
                        <div class="result-label">Formatted Currency (${langInfo.currency})</div>
                        <div class="result-value">${formattedCurrency}</div>
                    </div>
                `;
            } catch (error) {
                console.warn('Currency formatting failed:', error);
            }
            
            // Date formatting
            try {
                const formattedDate = localeFormatter.formatDate(testDate, currentLang, 'full');
                html += `
                    <div class="result-item">
                        <div class="result-label">Formatted Date</div>
                        <div class="result-value">${formattedDate}</div>
                    </div>
                `;
            } catch (error) {
                console.warn('Date formatting failed:', error);
            }
            
            // File size formatting
            try {
                const fileSize = 1024 * 1024 * 2.5; // 2.5 MB
                const formattedSize = localeFormatter.formatFileSize(fileSize, currentLang);
                html += `
                    <div class="result-item">
                        <div class="result-label">File Size (2.5 MB)</div>
                        <div class="result-value">${formattedSize}</div>
                    </div>
                `;
            } catch (error) {
                console.warn('File size formatting failed:', error);
            }
            
            // List formatting
            try {
                const items = ['Apple', 'Orange', 'Banana'];
                const formattedList = localeFormatter.formatList(items, currentLang);
                html += `
                    <div class="result-item">
                        <div class="result-label">List Formatting</div>
                        <div class="result-value">${formattedList}</div>
                    </div>
                `;
            } catch (error) {
                console.warn('List formatting failed:', error);
            }
            
            resultsContainer.innerHTML = html;
        }

        // Test translation function
        function testTranslation() {
            const key = 'common.loading';
            const translated = i18n.translate(key);
            console.log(`Translation test: ${key} = ${translated}`);
            return translated;
        }

        // Test formatting function
        function testFormatting() {
            const currentLang = i18n.getCurrentLanguage();
            const number = 1234567.89;
            const formatted = localeFormatter.formatNumber(number, currentLang);
            console.log(`Formatting test: ${number} = ${formatted} (${currentLang})`);
            return formatted;
        }

        // Make functions available globally for testing
        window.testTranslation = testTranslation;
        window.testFormatting = testFormatting;
        window.updateFormattingExamples = updateFormattingExamples;
        
        // Add some demo translations for testing
        window.addEventListener('load', function() {
            // Add demo translations that aren't in the main translation files
            const demoTranslations = {
                demo: {
                    translation_description: 'See how text changes based on the selected language.',
                    number_formatting: 'Number Formatting',
                    number_description: 'Numbers formatted according to locale conventions.',
                    large_number: 'Large Number',
                    percentage: 'Percentage',
                    currency_formatting: 'Currency Formatting',
                    currency_description: 'Currency values in different locales.',
                    date_formatting: 'Date & Time Formatting',
                    date_description: 'Dates and times formatted for different locales.',
                    rtl_support: 'RTL Support',
                    rtl_description: 'Right-to-left language support demonstration.',
                    rtl_text: 'This text changes direction based on the selected language.',
                    address_formatting: 'Address Formatting',
                    address_description: 'Address formats vary by country and locale.',
                    interactive_controls: 'Interactive Controls',
                    test_number: 'Test Number',
                    test_currency: 'Test Currency Amount',
                    test_date: 'Test Date',
                    update_examples: 'Update Examples',
                    formatting_results: 'Formatting Results'
                },
                navigation: {
                    translations: 'Translations'
                }
            };
            
            // This would normally be added to the translation files
            console.log('Demo translations loaded:', demoTranslations);
        });
    </script>
</body>
</html>