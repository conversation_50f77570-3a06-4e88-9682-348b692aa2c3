import { Platform, Alert, Linking } from 'react-native';
import { 
  PERMISSIONS, 
  RESULTS, 
  request, 
  check, 
  openSettings,
  Permission 
} from 'react-native-permissions';

export interface PermissionStatus {
  granted: boolean;
  denied: boolean;
  blocked: boolean;
  unavailable: boolean;
}

export interface PermissionExplanation {
  title: string;
  message: string;
  icon: string;
  benefits: string[];
}

class PermissionService {
  // Permission mappings for different platforms
  private getPermissions() {
    return {
      photos: Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.PHOTO_LIBRARY 
        : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
      contacts: Platform.OS === 'ios'
        ? PERMISSIONS.IOS.CONTACTS
        : PERMISSIONS.ANDROID.READ_CONTACTS,
      sms: Platform.OS === 'android'
        ? PERMISSIONS.ANDROID.READ_SMS
        : null, // iOS doesn't allow SMS access
      camera: Platform.OS === 'ios'
        ? PERMISSIONS.IOS.CAMERA
        : PERMISSIONS.ANDROID.CAMERA,
    };
  }

  // Grandparent-friendly explanations for each permission
  private getPermissionExplanations(): Record<string, PermissionExplanation> {
    return {
      photos: {
        title: "Access Your Photos",
        message: "SafeKeep needs to see your photos so we can back them up safely to the cloud. This keeps your precious memories safe forever!",
        icon: "📸",
        benefits: [
          "Backup all your family photos automatically",
          "Never lose precious memories again", 
          "Access photos from any device",
          "Share photos easily with family"
        ]
      },
      contacts: {
        title: "Access Your Contacts",
        message: "SafeKeep needs to see your contacts so we can back them up. This way you'll never lose important phone numbers!",
        icon: "📞",
        benefits: [
          "Backup all your important phone numbers",
          "Never lose contact information",
          "Restore contacts if you get a new phone",
          "Keep family and friends' numbers safe"
        ]
      },
      sms: {
        title: "Access Your Text Messages",
        message: "SafeKeep can backup your text messages to save important conversations with family and friends.",
        icon: "💬",
        benefits: [
          "Save important family conversations",
          "Backup messages from loved ones",
          "Never lose precious text memories",
          "Keep important information safe"
        ]
      },
      camera: {
        title: "Access Your Camera",
        message: "SafeKeep needs camera access so you can take new photos that will be automatically backed up.",
        icon: "📷",
        benefits: [
          "Take photos that backup automatically",
          "Never worry about losing new pictures",
          "Instant backup of new memories",
          "Peace of mind for new photos"
        ]
      }
    };
  }

  // Check current permission status
  async checkPermission(permissionType: keyof ReturnType<typeof this.getPermissions>): Promise<PermissionStatus> {
    const permissions = this.getPermissions();
    const permission = permissions[permissionType];
    
    if (!permission) {
      return { granted: false, denied: false, blocked: false, unavailable: true };
    }

    try {
      const result = await check(permission);
      
      return {
        granted: result === RESULTS.GRANTED,
        denied: result === RESULTS.DENIED,
        blocked: result === RESULTS.BLOCKED,
        unavailable: result === RESULTS.UNAVAILABLE
      };
    } catch (error) {
      console.error(`Error checking ${permissionType} permission:`, error);
      return { granted: false, denied: true, blocked: false, unavailable: false };
    }
  }

  // Request permission with grandparent-friendly explanation
  async requestPermission(permissionType: keyof ReturnType<typeof this.getPermissions>): Promise<PermissionStatus> {
    const permissions = this.getPermissions();
    const permission = permissions[permissionType];
    const explanation = this.getPermissionExplanations()[permissionType];
    
    if (!permission) {
      return { granted: false, denied: false, blocked: false, unavailable: true };
    }

    // First, show our friendly explanation
    const userWantsToGrant = await this.showPermissionExplanation(explanation);
    
    if (!userWantsToGrant) {
      return { granted: false, denied: true, blocked: false, unavailable: false };
    }

    try {
      const result = await request(permission);
      
      const status = {
        granted: result === RESULTS.GRANTED,
        denied: result === RESULTS.DENIED,
        blocked: result === RESULTS.BLOCKED,
        unavailable: result === RESULTS.UNAVAILABLE
      };

      // Handle blocked permissions with helpful guidance
      if (status.blocked) {
        await this.showBlockedPermissionHelp(explanation);
      }

      return status;
    } catch (error) {
      console.error(`Error requesting ${permissionType} permission:`, error);
      return { granted: false, denied: true, blocked: false, unavailable: false };
    }
  }

  // Show grandparent-friendly permission explanation
  private showPermissionExplanation(explanation: PermissionExplanation): Promise<boolean> {
    return new Promise((resolve) => {
      const benefitsList = explanation.benefits.map((benefit, index) => `${index + 1}. ${benefit}`).join('\n');
      
      Alert.alert(
        `${explanation.icon} ${explanation.title}`,
        `${explanation.message}\n\nHere's what this helps you do:\n${benefitsList}`,
        [
          {
            text: "Not Now",
            style: "cancel",
            onPress: () => resolve(false)
          },
          {
            text: "Allow Access",
            style: "default",
            onPress: () => resolve(true)
          }
        ],
        { cancelable: false }
      );
    });
  }

  // Help users with blocked permissions
  private showBlockedPermissionHelp(explanation: PermissionExplanation): Promise<void> {
    return new Promise((resolve) => {
      Alert.alert(
        "Permission Needed",
        `To use ${explanation.title.toLowerCase()}, please:\n\n1. Tap "Open Settings" below\n2. Find "SafeKeep" in the list\n3. Turn ON the permission\n4. Come back to SafeKeep\n\nDon't worry - we'll guide you through it!`,
        [
          {
            text: "Maybe Later",
            style: "cancel",
            onPress: () => resolve()
          },
          {
            text: "Open Settings",
            style: "default",
            onPress: () => {
              openSettings().catch(() => {
                // Fallback to general settings if app settings fail
                Linking.openSettings();
              });
              resolve();
            }
          }
        ]
      );
    });
  }

  // Check all required permissions at once
  async checkAllPermissions(): Promise<Record<string, PermissionStatus>> {
    const permissionTypes = ['photos', 'contacts', 'sms'] as const;
    const results: Record<string, PermissionStatus> = {};

    for (const type of permissionTypes) {
      results[type] = await this.checkPermission(type);
    }

    return results;
  }

  // Request all required permissions with proper flow
  async requestAllPermissions(): Promise<Record<string, PermissionStatus>> {
    const permissionTypes = ['photos', 'contacts', 'sms'] as const;
    const results: Record<string, PermissionStatus> = {};

    // Request permissions one by one to avoid overwhelming users
    for (const type of permissionTypes) {
      // Skip SMS on iOS since it's not available
      if (type === 'sms' && Platform.OS === 'ios') {
        results[type] = { granted: false, denied: false, blocked: false, unavailable: true };
        continue;
      }

      results[type] = await this.requestPermission(type);
      
      // Small delay between requests to make it less overwhelming
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return results;
  }

  // Get summary of permission status for UI
  getPermissionSummary(permissions: Record<string, PermissionStatus>): {
    allGranted: boolean;
    grantedCount: number;
    totalCount: number;
    missingPermissions: string[];
  } {
    const entries = Object.entries(permissions);
    const availableEntries = entries.filter(([_, status]) => !status.unavailable);
    const grantedEntries = availableEntries.filter(([_, status]) => status.granted);
    
    return {
      allGranted: grantedEntries.length === availableEntries.length,
      grantedCount: grantedEntries.length,
      totalCount: availableEntries.length,
      missingPermissions: availableEntries
        .filter(([_, status]) => !status.granted)
        .map(([type]) => type)
    };
  }
}

export default new PermissionService();
