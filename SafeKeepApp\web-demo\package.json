{"name": "safekeep-web-demo", "version": "1.0.0", "description": "SafeKeep Web Demo with Real-time Progress", "main": "server.js", "scripts": {"start": "node server.js", "start:websocket": "node websocket-server.js", "start:both": "concurrently \"npm run start\" \"npm run start:websocket\"", "test": "node test-realtime-progress.js", "test:verify": "node tests/verify-test-setup.js", "test:integration": "node tests/integration-test-runner.js", "test:e2e": "node tests/e2e-test-runner.js", "test:stripe": "node tests/stripe-integration-tests.js", "test:subscription": "node tests/subscription-lifecycle-tests.js", "test:performance": "node tests/performance-benchmark-tests.js", "test:cross-browser": "node tests/cross-browser-tests.js", "test:all": "node tests/run-all-tests.js", "test:quick": "npm run test:integration && npm run test:e2e"}, "dependencies": {"ws": "^8.18.0", "concurrently": "^8.2.2", "chart.js": "^4.4.0"}, "devDependencies": {"puppeteer": "^21.0.0", "playwright": "^1.40.0", "jest": "^29.7.0", "supertest": "^6.3.3", "stripe": "^14.0.0"}, "keywords": ["safekeep", "backup", "demo", "websocket", "realtime"], "author": "SafeKeep Team", "license": "MIT"}