/**
 * Advanced Backup Scheduling Console
 * UI component for managing backup schedules
 */

class ScheduleConsole {
    constructor(containerId) {
        this.containerId = containerId;
        this.scheduleManager = null;
        this.currentEditingSchedule = null;
        this.isInitialized = false;
        
        // Bind methods
        this.handleScheduleEvent = this.handleScheduleEvent.bind(this);
        this.render = this.render.bind(this);
    }

    /**
     * Set the schedule manager instance
     */
    setScheduleManager(scheduleManager) {
        this.scheduleManager = scheduleManager;
        
        // Add event listener
        this.scheduleManager.addListener(this.handleScheduleEvent);
        
        if (!this.isInitialized) {
            this.initialize();
        }
    }

    /**
     * Initialize the console
     */
    async initialize() {
        if (this.isInitialized) return;
        
        this.render();
        this.isInitialized = true;
        
        // Load initial data if schedule manager is available
        if (this.scheduleManager) {
            this.updateSchedulesList();
            this.updateConditionsDisplay();
            this.updateStatsDisplay();
        }
    }

    /**
     * Handle events from schedule manager
     */
    handleScheduleEvent(event, data) {
        switch (event) {
            case 'initialized':
            case 'schedule_created':
            case 'schedule_updated':
            case 'schedule_deleted':
                this.updateSchedulesList();
                this.updateStatsDisplay();
                break;
                
            case 'conditions_updated':
                this.updateConditionsDisplay();
                break;
                
            case 'schedule_evaluating':
                this.showScheduleEvaluation(data);
                break;
                
            case 'scheduled_backup_starting':
                this.showBackupStarting(data);
                break;
                
            case 'scheduled_backup_completed':
                this.showBackupCompleted(data);
                break;
                
            case 'scheduled_backup_failed':
                this.showBackupFailed(data);
                break;
                
            case 'schedule_conditions_not_met':
                this.showConditionsNotMet(data);
                break;
        }
    }

    /**
     * Render the main console interface
     */
    render() {
        const container = document.getElementById(this.containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="schedule-console">
                <div class="console-header">
                    <h3>🕒 Advanced Backup Scheduling</h3>
                    <div class="console-controls">
                        <button class="btn" onclick="scheduleConsole.showCreateScheduleModal()">
                            ➕ New Schedule
                        </button>
                        <button class="btn secondary" onclick="scheduleConsole.showConditionsModal()">
                            ⚙️ Conditions
                        </button>
                        <button class="btn secondary" onclick="scheduleConsole.refreshData()">
                            🔄 Refresh
                        </button>
                    </div>
                </div>

                <div class="console-body">
                    <!-- Statistics Overview -->
                    <div class="stats-overview">
                        <h4>📊 Schedule Statistics</h4>
                        <div class="stats-grid" id="schedule-stats-grid">
                            <div class="stat-card">
                                <span class="stat-number" id="total-schedules">0</span>
                                <span class="stat-label">Total Schedules</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number" id="enabled-schedules">0</span>
                                <span class="stat-label">Enabled</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number" id="success-rate">0%</span>
                                <span class="stat-label">Success Rate</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number" id="next-run-time">--</span>
                                <span class="stat-label">Next Run</span>
                            </div>
                        </div>
                    </div>

                    <!-- Current Conditions -->
                    <div class="conditions-overview">
                        <h4>📱 Current Device Conditions</h4>
                        <div class="conditions-grid" id="conditions-grid">
                            <!-- Conditions will be populated here -->
                        </div>
                    </div>

                    <!-- Schedules List -->
                    <div class="schedules-section">
                        <h4>📅 Backup Schedules</h4>
                        <div class="schedules-list" id="schedules-list">
                            <div class="schedule-placeholder">
                                No schedules created yet. Click "New Schedule" to get started.
                            </div>
                        </div>
                    </div>

                    <!-- Activity Log -->
                    <div class="schedule-activity">
                        <h4>📋 Recent Activity</h4>
                        <div class="activity-log" id="schedule-activity-log">
                            <div class="activity-placeholder">
                                Schedule activity will appear here...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create/Edit Schedule Modal -->
            <div class="modal-overlay" id="schedule-modal-overlay" style="display: none;">
                <div class="modal-content schedule-modal">
                    <div class="modal-header">
                        <h3 id="schedule-modal-title">Create New Schedule</h3>
                        <button class="modal-close" onclick="scheduleConsole.closeScheduleModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <form id="schedule-form">
                            <!-- Basic Settings -->
                            <div class="form-section">
                                <h4>Basic Settings</h4>
                                <div class="form-group">
                                    <label for="schedule-name">Schedule Name</label>
                                    <input type="text" id="schedule-name" placeholder="e.g., Daily Evening Backup" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="schedule-frequency">Frequency</label>
                                    <select id="schedule-frequency" onchange="scheduleConsole.updateFrequencyOptions()">
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                        <option value="custom">Custom</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="schedule-time">Time</label>
                                    <input type="time" id="schedule-time" value="02:00">
                                </div>
                                
                                <div class="form-group" id="schedule-days-group">
                                    <label>Days of Week</label>
                                    <div class="days-selector">
                                        <label><input type="checkbox" value="0" checked> Sunday</label>
                                        <label><input type="checkbox" value="1" checked> Monday</label>
                                        <label><input type="checkbox" value="2" checked> Tuesday</label>
                                        <label><input type="checkbox" value="3" checked> Wednesday</label>
                                        <label><input type="checkbox" value="4" checked> Thursday</label>
                                        <label><input type="checkbox" value="5" checked> Friday</label>
                                        <label><input type="checkbox" value="6" checked> Saturday</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Types -->
                            <div class="form-section">
                                <h4>Data to Backup</h4>
                                <div class="data-types-selector">
                                    <label><input type="checkbox" value="contacts" checked> 📞 Contacts</label>
                                    <label><input type="checkbox" value="messages" checked> 💬 Messages</label>
                                    <label><input type="checkbox" value="photos" checked> 📸 Photos</label>
                                </div>
                            </div>

                            <!-- Conditions -->
                            <div class="form-section">
                                <h4>Backup Conditions</h4>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="wifi-only" checked>
                                        WiFi Only - Only backup when connected to WiFi
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label for="min-battery">Minimum Battery Level (%)</label>
                                    <input type="range" id="min-battery" min="0" max="100" value="20" 
                                           oninput="document.getElementById('battery-value').textContent = this.value + '%'">
                                    <span id="battery-value">20%</span>
                                </div>
                                
                                <div class="form-group">
                                    <label for="max-storage">Maximum Storage Usage (%)</label>
                                    <input type="range" id="max-storage" min="0" max="100" value="80"
                                           oninput="document.getElementById('storage-value').textContent = this.value + '%'">
                                    <span id="storage-value">80%</span>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="require-charging">
                                        Require Charging - Only backup when device is charging
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="require-idle">
                                        Require Idle - Only backup when device is not in use
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn secondary" onclick="scheduleConsole.testSchedule()">
                            🧪 Test Schedule
                        </button>
                        <button class="btn" onclick="scheduleConsole.saveSchedule()">
                            💾 Save Schedule
                        </button>
                    </div>
                </div>
            </div>

            <!-- Conditions Modal -->
            <div class="modal-overlay" id="conditions-modal-overlay" style="display: none;">
                <div class="modal-content conditions-modal">
                    <div class="modal-header">
                        <h3>📱 Simulate Device Conditions</h3>
                        <button class="modal-close" onclick="scheduleConsole.closeConditionsModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="conditions-simulator">
                            <div class="form-group">
                                <label for="sim-network">Network Type</label>
                                <select id="sim-network">
                                    <option value="wifi">📶 WiFi</option>
                                    <option value="cellular">📱 Cellular</option>
                                    <option value="offline">❌ Offline</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="sim-battery">Battery Level (%)</label>
                                <input type="range" id="sim-battery" min="0" max="100" value="85"
                                       oninput="document.getElementById('sim-battery-value').textContent = this.value + '%'">
                                <span id="sim-battery-value">85%</span>
                            </div>
                            
                            <div class="form-group">
                                <label for="sim-storage">Storage Usage (%)</label>
                                <input type="range" id="sim-storage" min="0" max="100" value="45"
                                       oninput="document.getElementById('sim-storage-value').textContent = this.value + '%'">
                                <span id="sim-storage-value">45%</span>
                            </div>
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="sim-charging">
                                    🔌 Device is charging
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="sim-idle" checked>
                                    😴 Device is idle
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn" onclick="scheduleConsole.updateConditions()">
                            ✅ Apply Conditions
                        </button>
                    </div>
                </div>
            </div>

            <!-- Test Results Modal -->
            <div class="modal-overlay" id="test-results-modal-overlay" style="display: none;">
                <div class="modal-content test-results-modal">
                    <div class="modal-header">
                        <h3>🧪 Schedule Test Results</h3>
                        <button class="modal-close" onclick="scheduleConsole.closeTestResultsModal()">×</button>
                    </div>
                    <div class="modal-body" id="test-results-content">
                        <!-- Test results will be populated here -->
                    </div>
                    <div class="modal-footer">
                        <button class="btn" onclick="scheduleConsole.closeTestResultsModal()">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.addStyles();
    }

    /**
     * Add CSS styles for the schedule console
     */
    addStyles() {
        if (document.getElementById('schedule-console-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'schedule-console-styles';
        styles.textContent = `
            .schedule-console {
                background: #f8f9fa;
                border-radius: 15px;
                padding: 25px;
                margin: 20px 0;
            }

            .console-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 25px;
                padding-bottom: 15px;
                border-bottom: 2px solid #e9ecef;
            }

            .console-header h3 {
                color: #4facfe;
                margin: 0;
                font-size: 1.5rem;
            }

            .console-controls {
                display: flex;
                gap: 10px;
            }

            .console-controls .btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .stats-overview, .conditions-overview, .schedules-section, .schedule-activity {
                background: white;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
                border: 1px solid #e9ecef;
            }

            .stats-overview h4, .conditions-overview h4, .schedules-section h4, .schedule-activity h4 {
                color: #333;
                margin-bottom: 15px;
                font-size: 1.1rem;
            }

            .conditions-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }

            .condition-card {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                text-align: center;
                border: 1px solid #e9ecef;
            }

            .condition-icon {
                font-size: 2rem;
                margin-bottom: 10px;
                display: block;
            }

            .condition-label {
                font-size: 0.9rem;
                color: #666;
                margin-bottom: 5px;
            }

            .condition-value {
                font-size: 1.1rem;
                font-weight: 600;
                color: #333;
            }

            .condition-status {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 600;
                margin-top: 8px;
                display: inline-block;
            }

            .condition-status.good {
                background: #d4edda;
                color: #155724;
            }

            .condition-status.warning {
                background: #fff3cd;
                color: #856404;
            }

            .condition-status.bad {
                background: #f8d7da;
                color: #721c24;
            }

            .schedules-list {
                max-height: 400px;
                overflow-y: auto;
            }

            .schedule-item {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 15px;
                border-left: 4px solid #4facfe;
                position: relative;
            }

            .schedule-item.disabled {
                opacity: 0.6;
                border-left-color: #ccc;
            }

            .schedule-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }

            .schedule-name {
                font-size: 1.1rem;
                font-weight: 600;
                color: #333;
                margin: 0;
            }

            .schedule-controls {
                display: flex;
                gap: 8px;
            }

            .schedule-controls .btn {
                padding: 4px 8px;
                font-size: 0.8rem;
            }

            .schedule-details {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
                margin-bottom: 15px;
            }

            .schedule-detail {
                text-align: center;
            }

            .schedule-detail-label {
                font-size: 0.8rem;
                color: #666;
                margin-bottom: 5px;
            }

            .schedule-detail-value {
                font-size: 0.9rem;
                font-weight: 600;
                color: #333;
            }

            .schedule-conditions {
                background: white;
                border-radius: 6px;
                padding: 15px;
                margin-top: 15px;
            }

            .conditions-summary {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }

            .condition-tag {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                background: #e9ecef;
                color: #495057;
            }

            .condition-tag.active {
                background: #d4edda;
                color: #155724;
            }

            .schedule-placeholder, .activity-placeholder {
                text-align: center;
                padding: 40px;
                color: #666;
                font-style: italic;
            }

            .activity-log {
                max-height: 200px;
                overflow-y: auto;
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
            }

            .activity-item {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
                font-size: 0.9rem;
            }

            .activity-item:last-child {
                border-bottom: none;
            }

            .activity-time {
                color: #666;
                font-size: 0.8rem;
                min-width: 60px;
            }

            .activity-icon {
                font-size: 1.2rem;
            }

            .activity-message {
                flex: 1;
                color: #333;
            }

            /* Modal Styles */
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }

            .modal-content {
                background: white;
                border-radius: 15px;
                max-width: 600px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #e9ecef;
            }

            .modal-header h3 {
                margin: 0;
                color: #333;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-footer {
                padding: 20px;
                border-top: 1px solid #e9ecef;
                display: flex;
                gap: 10px;
                justify-content: flex-end;
            }

            .form-section {
                margin-bottom: 25px;
                padding-bottom: 20px;
                border-bottom: 1px solid #e9ecef;
            }

            .form-section:last-child {
                border-bottom: none;
            }

            .form-section h4 {
                color: #4facfe;
                margin-bottom: 15px;
                font-size: 1.1rem;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: 600;
                color: #333;
            }

            .form-group input, .form-group select {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 1rem;
            }

            .form-group input:focus, .form-group select:focus {
                outline: none;
                border-color: #4facfe;
                box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
            }

            .days-selector, .data-types-selector {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 10px;
            }

            .days-selector label, .data-types-selector label {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px;
                border: 1px solid #e9ecef;
                border-radius: 5px;
                cursor: pointer;
                font-weight: normal;
            }

            .days-selector label:hover, .data-types-selector label:hover {
                background: #f8f9fa;
            }

            .conditions-simulator .form-group {
                margin-bottom: 20px;
            }

            .test-results-content {
                max-height: 400px;
                overflow-y: auto;
            }

            .test-result-section {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }

            .test-result-section h5 {
                color: #333;
                margin-bottom: 10px;
            }

            .test-result-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
            }

            .test-result-item:last-child {
                border-bottom: none;
            }

            .test-result-status {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 600;
            }

            .test-result-status.pass {
                background: #d4edda;
                color: #155724;
            }

            .test-result-status.fail {
                background: #f8d7da;
                color: #721c24;
            }

            @media (max-width: 768px) {
                .console-header {
                    flex-direction: column;
                    gap: 15px;
                    align-items: stretch;
                }

                .console-controls {
                    justify-content: center;
                }

                .schedule-details {
                    grid-template-columns: 1fr;
                }

                .conditions-grid {
                    grid-template-columns: 1fr;
                }

                .modal-content {
                    width: 95%;
                    margin: 20px;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * Show create schedule modal
     */
    showCreateScheduleModal() {
        this.currentEditingSchedule = null;
        document.getElementById('schedule-modal-title').textContent = 'Create New Schedule';
        document.getElementById('schedule-form').reset();
        document.getElementById('schedule-modal-overlay').style.display = 'flex';
        this.updateFrequencyOptions();
    }

    /**
     * Show edit schedule modal
     */
    showEditScheduleModal(scheduleId) {
        const schedule = this.scheduleManager.getSchedule(scheduleId);
        if (!schedule) return;

        this.currentEditingSchedule = schedule;
        document.getElementById('schedule-modal-title').textContent = 'Edit Schedule';
        
        // Populate form with schedule data
        document.getElementById('schedule-name').value = schedule.name;
        document.getElementById('schedule-frequency').value = schedule.frequency;
        document.getElementById('schedule-time').value = schedule.time;
        
        // Set days checkboxes
        const dayCheckboxes = document.querySelectorAll('#schedule-days-group input[type="checkbox"]');
        dayCheckboxes.forEach(checkbox => {
            checkbox.checked = schedule.days.includes(parseInt(checkbox.value));
        });
        
        // Set data types checkboxes
        const dataTypeCheckboxes = document.querySelectorAll('.data-types-selector input[type="checkbox"]');
        dataTypeCheckboxes.forEach(checkbox => {
            checkbox.checked = schedule.dataTypes.includes(checkbox.value);
        });
        
        // Set conditions
        document.getElementById('wifi-only').checked = schedule.conditions.wifiOnly;
        document.getElementById('min-battery').value = schedule.conditions.minBatteryLevel;
        document.getElementById('battery-value').textContent = schedule.conditions.minBatteryLevel + '%';
        document.getElementById('max-storage').value = schedule.conditions.maxStorageUsage;
        document.getElementById('storage-value').textContent = schedule.conditions.maxStorageUsage + '%';
        document.getElementById('require-charging').checked = schedule.conditions.requireCharging;
        document.getElementById('require-idle').checked = schedule.conditions.requireIdle;
        
        document.getElementById('schedule-modal-overlay').style.display = 'flex';
        this.updateFrequencyOptions();
    }

    /**
     * Close schedule modal
     */
    closeScheduleModal() {
        document.getElementById('schedule-modal-overlay').style.display = 'none';
        this.currentEditingSchedule = null;
    }

    /**
     * Show conditions modal
     */
    showConditionsModal() {
        const conditions = this.scheduleManager.getSimulatedConditions();
        
        document.getElementById('sim-network').value = conditions.networkType;
        document.getElementById('sim-battery').value = conditions.batteryLevel;
        document.getElementById('sim-battery-value').textContent = conditions.batteryLevel + '%';
        document.getElementById('sim-storage').value = conditions.storageUsage;
        document.getElementById('sim-storage-value').textContent = conditions.storageUsage + '%';
        document.getElementById('sim-charging').checked = conditions.isCharging;
        document.getElementById('sim-idle').checked = conditions.deviceIdle;
        
        document.getElementById('conditions-modal-overlay').style.display = 'flex';
    }

    /**
     * Close conditions modal
     */
    closeConditionsModal() {
        document.getElementById('conditions-modal-overlay').style.display = 'none';
    }

    /**
     * Update frequency options based on selection
     */
    updateFrequencyOptions() {
        const frequency = document.getElementById('schedule-frequency').value;
        const daysGroup = document.getElementById('schedule-days-group');
        
        switch (frequency) {
            case 'daily':
                daysGroup.style.display = 'block';
                daysGroup.querySelector('label').textContent = 'Days of Week (select which days to run daily)';
                break;
            case 'weekly':
                daysGroup.style.display = 'block';
                daysGroup.querySelector('label').textContent = 'Day of Week (select one day for weekly backup)';
                break;
            case 'monthly':
                daysGroup.style.display = 'none';
                break;
            case 'custom':
                daysGroup.style.display = 'block';
                daysGroup.querySelector('label').textContent = 'Custom Days (select specific days)';
                break;
        }
    }

    /**
     * Test schedule configuration
     */
    testSchedule() {
        const scheduleData = this.getScheduleFormData();
        const testResults = this.scheduleManager.testSchedule(scheduleData);
        
        this.showTestResults(testResults, scheduleData);
    }

    /**
     * Save schedule
     */
    async saveSchedule() {
        try {
            const scheduleData = this.getScheduleFormData();
            
            if (this.currentEditingSchedule) {
                await this.scheduleManager.updateSchedule(this.currentEditingSchedule.id, scheduleData);
                this.logActivity('✏️', `Schedule "${scheduleData.name}" updated`);
            } else {
                await this.scheduleManager.createSchedule(scheduleData);
                this.logActivity('➕', `Schedule "${scheduleData.name}" created`);
            }
            
            this.closeScheduleModal();
        } catch (error) {
            alert('Failed to save schedule: ' + error.message);
        }
    }

    /**
     * Get form data as schedule object
     */
    getScheduleFormData() {
        const selectedDays = Array.from(document.querySelectorAll('#schedule-days-group input[type="checkbox"]:checked'))
            .map(cb => parseInt(cb.value));
        
        const selectedDataTypes = Array.from(document.querySelectorAll('.data-types-selector input[type="checkbox"]:checked'))
            .map(cb => cb.value);
        
        return {
            name: document.getElementById('schedule-name').value,
            frequency: document.getElementById('schedule-frequency').value,
            time: document.getElementById('schedule-time').value,
            days: selectedDays,
            dataTypes: selectedDataTypes,
            conditions: {
                wifiOnly: document.getElementById('wifi-only').checked,
                minBatteryLevel: parseInt(document.getElementById('min-battery').value),
                maxStorageUsage: parseInt(document.getElementById('max-storage').value),
                requireCharging: document.getElementById('require-charging').checked,
                requireIdle: document.getElementById('require-idle').checked
            }
        };
    }

    /**
     * Update simulated conditions
     */
    updateConditions() {
        const conditions = {
            networkType: document.getElementById('sim-network').value,
            batteryLevel: parseInt(document.getElementById('sim-battery').value),
            storageUsage: parseInt(document.getElementById('sim-storage').value),
            isCharging: document.getElementById('sim-charging').checked,
            deviceIdle: document.getElementById('sim-idle').checked
        };
        
        this.scheduleManager.updateSimulatedConditions(conditions);
        this.closeConditionsModal();
        this.logActivity('⚙️', 'Device conditions updated');
    }

    /**
     * Show test results modal
     */
    showTestResults(testResults, scheduleData) {
        const content = document.getElementById('test-results-content');
        
        content.innerHTML = `
            <div class="test-result-section">
                <h5>📋 Schedule Configuration</h5>
                <div class="test-result-item">
                    <span>Name:</span>
                    <span>${scheduleData.name}</span>
                </div>
                <div class="test-result-item">
                    <span>Frequency:</span>
                    <span>${scheduleData.frequency}</span>
                </div>
                <div class="test-result-item">
                    <span>Time:</span>
                    <span>${scheduleData.time}</span>
                </div>
                <div class="test-result-item">
                    <span>Next Run:</span>
                    <span>${testResults.nextRun ? testResults.nextRun.toLocaleString() : 'Not scheduled'}</span>
                </div>
            </div>

            <div class="test-result-section">
                <h5>🔍 Condition Check</h5>
                <div class="test-result-item">
                    <span>Can Run Now:</span>
                    <span class="test-result-status ${testResults.canRunNow ? 'pass' : 'fail'}">
                        ${testResults.canRunNow ? 'YES' : 'NO'}
                    </span>
                </div>
                ${testResults.failedConditions.map(condition => `
                    <div class="test-result-item">
                        <span>${condition.condition}:</span>
                        <span class="test-result-status fail">
                            Required: ${condition.required}, Current: ${condition.current}
                        </span>
                    </div>
                `).join('')}
            </div>

            <div class="test-result-section">
                <h5>📱 Current Conditions</h5>
                <div class="test-result-item">
                    <span>Network:</span>
                    <span>${testResults.currentConditions.networkType}</span>
                </div>
                <div class="test-result-item">
                    <span>Battery:</span>
                    <span>${testResults.currentConditions.batteryLevel}%</span>
                </div>
                <div class="test-result-item">
                    <span>Storage:</span>
                    <span>${testResults.currentConditions.storageUsage}%</span>
                </div>
                <div class="test-result-item">
                    <span>Charging:</span>
                    <span>${testResults.currentConditions.isCharging ? 'Yes' : 'No'}</span>
                </div>
                <div class="test-result-item">
                    <span>Idle:</span>
                    <span>${testResults.currentConditions.deviceIdle ? 'Yes' : 'No'}</span>
                </div>
            </div>
        `;
        
        document.getElementById('test-results-modal-overlay').style.display = 'flex';
    }

    /**
     * Close test results modal
     */
    closeTestResultsModal() {
        document.getElementById('test-results-modal-overlay').style.display = 'none';
    }

    /**
     * Delete schedule
     */
    async deleteSchedule(scheduleId) {
        const schedule = this.scheduleManager.getSchedule(scheduleId);
        if (!schedule) return;
        
        if (confirm(`Are you sure you want to delete the schedule "${schedule.name}"?`)) {
            try {
                await this.scheduleManager.deleteSchedule(scheduleId);
                this.logActivity('🗑️', `Schedule "${schedule.name}" deleted`);
            } catch (error) {
                alert('Failed to delete schedule: ' + error.message);
            }
        }
    }

    /**
     * Toggle schedule enabled/disabled
     */
    async toggleSchedule(scheduleId) {
        try {
            const schedule = await this.scheduleManager.toggleSchedule(scheduleId);
            const action = schedule.enabled ? 'enabled' : 'disabled';
            this.logActivity(schedule.enabled ? '✅' : '⏸️', `Schedule "${schedule.name}" ${action}`);
        } catch (error) {
            alert('Failed to toggle schedule: ' + error.message);
        }
    }

    /**
     * Update schedules list display
     */
    updateSchedulesList() {
        const container = document.getElementById('schedules-list');
        if (!container) return;

        const schedules = this.scheduleManager.getSchedules();
        
        if (schedules.length === 0) {
            container.innerHTML = `
                <div class="schedule-placeholder">
                    No schedules created yet. Click "New Schedule" to get started.
                </div>
            `;
            return;
        }

        container.innerHTML = schedules.map(schedule => `
            <div class="schedule-item ${!schedule.enabled ? 'disabled' : ''}">
                <div class="schedule-header">
                    <h5 class="schedule-name">${schedule.name}</h5>
                    <div class="schedule-controls">
                        <button class="btn ${schedule.enabled ? 'secondary' : 'success'}" 
                                onclick="scheduleConsole.toggleSchedule('${schedule.id}')"
                                title="${schedule.enabled ? 'Disable' : 'Enable'} schedule">
                            ${schedule.enabled ? '⏸️' : '▶️'}
                        </button>
                        <button class="btn secondary" 
                                onclick="scheduleConsole.showEditScheduleModal('${schedule.id}')"
                                title="Edit schedule">
                            ✏️
                        </button>
                        <button class="btn danger" 
                                onclick="scheduleConsole.deleteSchedule('${schedule.id}')"
                                title="Delete schedule">
                            🗑️
                        </button>
                    </div>
                </div>
                
                <div class="schedule-details">
                    <div class="schedule-detail">
                        <div class="schedule-detail-label">Frequency</div>
                        <div class="schedule-detail-value">${schedule.frequency}</div>
                    </div>
                    <div class="schedule-detail">
                        <div class="schedule-detail-label">Time</div>
                        <div class="schedule-detail-value">${schedule.time}</div>
                    </div>
                    <div class="schedule-detail">
                        <div class="schedule-detail-label">Next Run</div>
                        <div class="schedule-detail-value">
                            ${schedule.nextRun ? schedule.nextRun.toLocaleDateString() + ' ' + schedule.nextRun.toLocaleTimeString() : 'Not scheduled'}
                        </div>
                    </div>
                    <div class="schedule-detail">
                        <div class="schedule-detail-label">Success Rate</div>
                        <div class="schedule-detail-value">
                            ${schedule.runCount > 0 ? Math.round((schedule.successCount / schedule.runCount) * 100) : 0}%
                        </div>
                    </div>
                </div>
                
                <div class="schedule-conditions">
                    <div class="conditions-summary">
                        ${schedule.conditions.wifiOnly ? '<span class="condition-tag active">📶 WiFi Only</span>' : ''}
                        <span class="condition-tag">🔋 Battery ≥${schedule.conditions.minBatteryLevel}%</span>
                        <span class="condition-tag">💾 Storage ≤${schedule.conditions.maxStorageUsage}%</span>
                        ${schedule.conditions.requireCharging ? '<span class="condition-tag active">🔌 Charging</span>' : ''}
                        ${schedule.conditions.requireIdle ? '<span class="condition-tag active">😴 Idle</span>' : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Update conditions display
     */
    updateConditionsDisplay() {
        const container = document.getElementById('conditions-grid');
        if (!container) return;

        const conditions = this.scheduleManager.getSimulatedConditions();
        
        container.innerHTML = `
            <div class="condition-card">
                <span class="condition-icon">📶</span>
                <div class="condition-label">Network</div>
                <div class="condition-value">${conditions.networkType.toUpperCase()}</div>
                <span class="condition-status ${conditions.networkType === 'wifi' ? 'good' : conditions.networkType === 'cellular' ? 'warning' : 'bad'}">
                    ${conditions.networkType === 'wifi' ? 'Optimal' : conditions.networkType === 'cellular' ? 'Limited' : 'Offline'}
                </span>
            </div>
            
            <div class="condition-card">
                <span class="condition-icon">🔋</span>
                <div class="condition-label">Battery</div>
                <div class="condition-value">${conditions.batteryLevel}%</div>
                <span class="condition-status ${conditions.batteryLevel >= 50 ? 'good' : conditions.batteryLevel >= 20 ? 'warning' : 'bad'}">
                    ${conditions.batteryLevel >= 50 ? 'Good' : conditions.batteryLevel >= 20 ? 'Low' : 'Critical'}
                </span>
            </div>
            
            <div class="condition-card">
                <span class="condition-icon">💾</span>
                <div class="condition-label">Storage</div>
                <div class="condition-value">${conditions.storageUsage}%</div>
                <span class="condition-status ${conditions.storageUsage <= 70 ? 'good' : conditions.storageUsage <= 85 ? 'warning' : 'bad'}">
                    ${conditions.storageUsage <= 70 ? 'Available' : conditions.storageUsage <= 85 ? 'Limited' : 'Full'}
                </span>
            </div>
            
            <div class="condition-card">
                <span class="condition-icon">${conditions.isCharging ? '🔌' : '🔋'}</span>
                <div class="condition-label">Power</div>
                <div class="condition-value">${conditions.isCharging ? 'Charging' : 'Battery'}</div>
                <span class="condition-status ${conditions.isCharging ? 'good' : 'warning'}">
                    ${conditions.isCharging ? 'Plugged In' : 'On Battery'}
                </span>
            </div>
            
            <div class="condition-card">
                <span class="condition-icon">${conditions.deviceIdle ? '😴' : '👤'}</span>
                <div class="condition-label">Usage</div>
                <div class="condition-value">${conditions.deviceIdle ? 'Idle' : 'Active'}</div>
                <span class="condition-status ${conditions.deviceIdle ? 'good' : 'warning'}">
                    ${conditions.deviceIdle ? 'Not In Use' : 'In Use'}
                </span>
            </div>
        `;
    }

    /**
     * Update statistics display
     */
    updateStatsDisplay() {
        if (!this.scheduleManager) return;

        const stats = this.scheduleManager.getScheduleStats();
        
        document.getElementById('total-schedules').textContent = stats.totalSchedules;
        document.getElementById('enabled-schedules').textContent = stats.enabledSchedules;
        document.getElementById('success-rate').textContent = Math.round(stats.successRate) + '%';
        
        const nextRun = stats.nextScheduledRun;
        if (nextRun && nextRun.nextRun) {
            const timeUntil = nextRun.nextRun.getTime() - new Date().getTime();
            const hours = Math.floor(timeUntil / (1000 * 60 * 60));
            const minutes = Math.floor((timeUntil % (1000 * 60 * 60)) / (1000 * 60));
            
            if (hours > 24) {
                document.getElementById('next-run-time').textContent = nextRun.nextRun.toLocaleDateString();
            } else if (hours > 0) {
                document.getElementById('next-run-time').textContent = `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                document.getElementById('next-run-time').textContent = `${minutes}m`;
            } else {
                document.getElementById('next-run-time').textContent = 'Soon';
            }
        } else {
            document.getElementById('next-run-time').textContent = '--';
        }
    }

    /**
     * Refresh all data
     */
    refreshData() {
        this.updateSchedulesList();
        this.updateConditionsDisplay();
        this.updateStatsDisplay();
        this.logActivity('🔄', 'Data refreshed');
    }

    /**
     * Log activity
     */
    logActivity(icon, message) {
        const container = document.getElementById('schedule-activity-log');
        if (!container) return;

        // Remove placeholder if it exists
        const placeholder = container.querySelector('.activity-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        const time = new Date().toLocaleTimeString();
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        activityItem.innerHTML = `
            <span class="activity-time">${time}</span>
            <span class="activity-icon">${icon}</span>
            <span class="activity-message">${message}</span>
        `;

        container.insertBefore(activityItem, container.firstChild);

        // Keep only last 20 items
        const items = container.querySelectorAll('.activity-item');
        if (items.length > 20) {
            items[items.length - 1].remove();
        }
    }

    /**
     * Show schedule evaluation
     */
    showScheduleEvaluation(data) {
        this.logActivity('🔍', `Evaluating schedule "${data.schedule.name}"`);
    }

    /**
     * Show backup starting
     */
    showBackupStarting(schedule) {
        this.logActivity('▶️', `Starting scheduled backup: "${schedule.name}"`);
    }

    /**
     * Show backup completed
     */
    showBackupCompleted(data) {
        const { schedule, result } = data;
        this.logActivity('✅', `Backup completed: "${schedule.name}" (${result.filesBackedUp} files, ${result.dataSize}MB)`);
    }

    /**
     * Show backup failed
     */
    showBackupFailed(data) {
        const { schedule, error } = data;
        this.logActivity('❌', `Backup failed: "${schedule.name}" - ${error}`);
    }

    /**
     * Show conditions not met
     */
    showConditionsNotMet(data) {
        const { schedule, failedConditions } = data;
        const reasons = failedConditions.map(c => c.condition).join(', ');
        this.logActivity('⏸️', `Schedule "${schedule.name}" postponed: ${reasons}`);
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.ScheduleConsole = ScheduleConsole;
}