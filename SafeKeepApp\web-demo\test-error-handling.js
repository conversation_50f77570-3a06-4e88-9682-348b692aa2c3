/**
 * Test Suite for Advanced Error Handling and Recovery System
 */

class ErrorHandlingTestSuite {
    constructor() {
        this.testResults = [];
        this.testCount = 0;
        this.passCount = 0;
        this.failCount = 0;
    }

    async runAllTests() {
        console.log('🧪 Starting Error Handling Test Suite...');
        
        // Test error handler initialization
        await this.testErrorHandlerInitialization();
        
        // Test error categorization
        await this.testErrorCategorization();
        
        // Test retry logic
        await this.testRetryLogic();
        
        // Test recovery strategies
        await this.testRecoveryStrategies();
        
        // Test network queue manager
        await this.testNetworkQueueManager();
        
        // Test offline manager
        await this.testOfflineManager();
        
        // Test demo state manager
        await this.testDemoStateManager();
        
        // Test user notifications
        await this.testUserNotifications();
        
        // Test graceful degradation
        await this.testGracefulDegradation();
        
        // Test demo reset functionality
        await this.testDemoReset();
        
        this.printResults();
        return this.generateReport();
    }

    async testErrorHandlerInitialization() {
        console.log('Testing error handler initialization...');
        
        try {
            this.assert(window.errorHandler instanceof SafeKeepErrorHandler, 'Error handler should be initialized');
            this.assert(typeof window.errorHandler.handleError === 'function', 'handleError method should exist');
            this.assert(window.errorHandler.errorCategories.AUTHENTICATION, 'Error categories should be defined');
            this.assert(window.errorHandler.recoveryStrategies.size > 0, 'Recovery strategies should be initialized');
            
            this.pass('Error handler initialization');
        } catch (error) {
            this.fail('Error handler initialization', error.message);
        }
    }

    async testErrorCategorization() {
        console.log('Testing error categorization...');
        
        try {
            // Test authentication error
            const authError = new Error('Authentication failed');
            const authResult = await window.errorHandler.handleError(authError, 'AUTHENTICATION', {
                operation: 'signin',
                email: '<EMAIL>'
            });
            
            this.assert(authResult.errorId, 'Error should have an ID');
            this.assert(!authResult.success, 'Auth error should not be successful');
            
            // Test network error
            const networkError = new Error('Network timeout');
            const networkResult = await window.errorHandler.handleError(networkError, 'NETWORK', {
                operation: 'api_call',
                url: '/api/test'
            });
            
            this.assert(networkResult.errorId, 'Network error should have an ID');
            
            // Check error log
            const errorLog = window.errorHandler.getErrorLog();
            this.assert(errorLog.length >= 2, 'Error log should contain test errors');
            
            this.pass('Error categorization');
        } catch (error) {
            this.fail('Error categorization', error.message);
        }
    }

    async testRetryLogic() {
        console.log('Testing retry logic...');
        
        try {
            // Mock a retryable operation
            let attemptCount = 0;
            window.errorHandler.retryNetworkOperation = async (context) => {
                attemptCount++;
                if (attemptCount < 3) {
                    throw new Error('Simulated failure');
                }
                return { success: true, attempt: attemptCount };
            };
            
            const error = new Error('Network failure');
            const result = await window.errorHandler.handleError(error, 'NETWORK', {
                operation: 'api_call',
                url: '/api/retry-test'
            });
            
            this.assert(attemptCount >= 2, 'Should have attempted retries');
            
            this.pass('Retry logic');
        } catch (error) {
            this.fail('Retry logic', error.message);
        }
    }

    async testRecoveryStrategies() {
        console.log('Testing recovery strategies...');
        
        try {
            // Test authentication recovery
            const authStrategies = window.errorHandler.recoveryStrategies.get('AUTHENTICATION');
            this.assert(authStrategies, 'Auth recovery strategies should exist');
            this.assert(typeof authStrategies.immediate === 'function', 'Immediate recovery should be a function');
            this.assert(typeof authStrategies.graceful === 'function', 'Graceful recovery should be a function');
            this.assert(typeof authStrategies.reset === 'function', 'Reset recovery should be a function');
            
            // Test network recovery
            const networkStrategies = window.errorHandler.recoveryStrategies.get('NETWORK');
            this.assert(networkStrategies, 'Network recovery strategies should exist');
            
            this.pass('Recovery strategies');
        } catch (error) {
            this.fail('Recovery strategies', error.message);
        }
    }

    async testNetworkQueueManager() {
        console.log('Testing network queue manager...');
        
        try {
            this.assert(window.networkQueue instanceof NetworkQueueManager, 'Network queue should be initialized');
            
            // Test queueing an API call
            const queueId = window.networkQueue.queueApiCall('/api/test', {
                method: 'POST',
                body: JSON.stringify({ test: true })
            });
            
            this.assert(queueId, 'Queue operation should return an ID');
            
            // Test queue status
            const status = window.networkQueue.getQueueStatus();
            this.assert(typeof status.queueLength === 'number', 'Queue status should include length');
            this.assert(typeof status.isOnline === 'boolean', 'Queue status should include online status');
            
            this.pass('Network queue manager');
        } catch (error) {
            this.fail('Network queue manager', error.message);
        }
    }

    async testOfflineManager() {
        console.log('Testing offline manager...');
        
        try {
            this.assert(window.offlineManager instanceof OfflineManager, 'Offline manager should be initialized');
            
            // Test feature availability
            const isDemoAvailable = window.offlineManager.isFeatureAvailable('demo_state');
            this.assert(typeof isDemoAvailable === 'boolean', 'Feature availability should return boolean');
            
            // Test caching
            window.offlineManager.cacheData('test_key', { test: 'data' });
            const cachedData = window.offlineManager.getCachedData('test_key');
            this.assert(cachedData && cachedData.test === 'data', 'Data should be cached and retrievable');
            
            // Test offline status
            const status = window.offlineManager.getStatus();
            this.assert(typeof status.isOffline === 'boolean', 'Status should include offline flag');
            
            this.pass('Offline manager');
        } catch (error) {
            this.fail('Offline manager', error.message);
        }
    }

    async testDemoStateManager() {
        console.log('Testing demo state manager...');
        
        try {
            this.assert(window.demoManager instanceof DemoStateManager, 'Demo state manager should be initialized');
            
            // Test state updates
            window.demoManager.updateState('test.value', 'test_data');
            const retrievedValue = window.demoManager.getState('test.value');
            this.assert(retrievedValue === 'test_data', 'State should be updatable and retrievable');
            
            // Test demo-specific methods
            window.demoManager.setDemoStep(5);
            const currentStep = window.demoManager.getState('demo.currentStep');
            this.assert(currentStep === 5, 'Demo step should be settable');
            
            // Test feature exploration tracking
            window.demoManager.addFeatureExplored('backup');
            const explored = window.demoManager.getState('demo.featuresExplored');
            this.assert(explored.includes('backup'), 'Feature exploration should be tracked');
            
            // Test stats
            const stats = window.demoManager.getStats();
            this.assert(typeof stats.sessionCount === 'number', 'Stats should include session count');
            
            this.pass('Demo state manager');
        } catch (error) {
            this.fail('Demo state manager', error.message);
        }
    }

    async testUserNotifications() {
        console.log('Testing user notifications...');
        
        try {
            // Test notification display
            window.errorHandler.showUserNotification({
                type: 'info',
                title: 'Test Notification',
                message: 'This is a test notification',
                autoHide: false
            });
            
            // Check if notification was added to DOM
            await new Promise(resolve => setTimeout(resolve, 100)); // Wait for DOM update
            const notification = document.querySelector('.safekeep-notification');
            this.assert(notification, 'Notification should be added to DOM');
            
            // Clean up
            if (notification) {
                notification.remove();
            }
            
            this.pass('User notifications');
        } catch (error) {
            this.fail('User notifications', error.message);
        }
    }

    async testGracefulDegradation() {
        console.log('Testing graceful degradation...');
        
        try {
            // Simulate network failure
            const originalFetch = window.fetch;
            window.fetch = () => Promise.reject(new Error('Network error'));
            
            // Test that error handler enables offline mode
            const networkError = new Error('Connection failed');
            await window.errorHandler.handleError(networkError, 'NETWORK', {
                operation: 'api_call'
            });
            
            // Restore fetch
            window.fetch = originalFetch;
            
            // Test that offline features are available
            const offlineStatus = window.offlineManager.getStatus();
            this.assert(Array.isArray(offlineStatus.availableFeatures), 'Offline features should be available');
            
            this.pass('Graceful degradation');
        } catch (error) {
            this.fail('Graceful degradation', error.message);
        }
    }

    async testDemoReset() {
        console.log('Testing demo reset functionality...');
        
        try {
            // Set some demo state
            window.demoManager.setDemoStep(10);
            window.demoManager.addFeatureExplored('encryption');
            
            // Test reset
            const resetResult = await window.demoManager.reset();
            this.assert(resetResult, 'Demo reset should succeed');
            
            // Verify state was reset
            const currentStep = window.demoManager.getState('demo.currentStep');
            this.assert(currentStep === 0, 'Demo step should be reset to 0');
            
            this.pass('Demo reset functionality');
        } catch (error) {
            this.fail('Demo reset functionality', error.message);
        }
    }

    // Test utility methods
    assert(condition, message) {
        if (!condition) {
            throw new Error(message);
        }
    }

    pass(testName) {
        this.testCount++;
        this.passCount++;
        this.testResults.push({
            name: testName,
            status: 'PASS',
            message: 'Test passed successfully'
        });
        console.log(`✅ ${testName}`);
    }

    fail(testName, message) {
        this.testCount++;
        this.failCount++;
        this.testResults.push({
            name: testName,
            status: 'FAIL',
            message: message
        });
        console.log(`❌ ${testName}: ${message}`);
    }

    printResults() {
        console.log('\n📊 Test Results Summary:');
        console.log(`Total Tests: ${this.testCount}`);
        console.log(`Passed: ${this.passCount}`);
        console.log(`Failed: ${this.failCount}`);
        console.log(`Success Rate: ${((this.passCount / this.testCount) * 100).toFixed(1)}%`);
        
        if (this.failCount > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(result => result.status === 'FAIL')
                .forEach(result => {
                    console.log(`  - ${result.name}: ${result.message}`);
                });
        }
    }

    generateReport() {
        return {
            timestamp: new Date().toISOString(),
            totalTests: this.testCount,
            passed: this.passCount,
            failed: this.failCount,
            successRate: (this.passCount / this.testCount) * 100,
            results: this.testResults,
            summary: {
                errorHandlerInitialized: window.errorHandler instanceof SafeKeepErrorHandler,
                networkQueueInitialized: window.networkQueue instanceof NetworkQueueManager,
                offlineManagerInitialized: window.offlineManager instanceof OfflineManager,
                demoStateManagerInitialized: window.demoManager instanceof DemoStateManager,
                errorCategoriesCount: Object.keys(window.errorHandler.errorCategories).length,
                recoveryStrategiesCount: window.errorHandler.recoveryStrategies.size
            }
        };
    }
}

// Function to run tests
async function testErrorHandling() {
    const testSuite = new ErrorHandlingTestSuite();
    return await testSuite.runAllTests();
}

// Auto-run tests if this script is loaded directly
if (typeof window !== 'undefined') {
    window.testErrorHandling = testErrorHandling;
    window.ErrorHandlingTestSuite = ErrorHandlingTestSuite;
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testErrorHandling, ErrorHandlingTestSuite };
}