/**
 * SafeKeep - Personal Data Backup App
 * Secure backup for photos, contacts, and messages
 *
 * @format
 */

import React, { useEffect } from 'react';
import { StatusBar, StyleSheet, Alert } from 'react-native';
import { Provider } from 'react-redux';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { PaperProvider, MD3LightTheme } from 'react-native-paper';

import AppNavigator from './src/navigation/AppNavigator';
import { store } from './src/store';
import { COLORS } from './src/utils/constants';
import StripeWrapper from './src/components/StripeWrapper';
import { validateSupabaseConfig } from './src/config/supabase';
import { testSupabaseConnection } from './src/utils/testSupabaseConnection';

const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: COLORS.primary,
    secondary: COLORS.secondary,
    surface: COLORS.surface,
    background: COLORS.background,
  },
};

function App() {
  return (
    <Provider store={store}>
      <PaperProvider theme={theme}>
        <SafeAreaProvider>
          <StripeWrapper>
            <StatusBar
              barStyle="dark-content"
              backgroundColor={COLORS.background}
            />
            <AppNavigator />
          </StripeWrapper>
        </SafeAreaProvider>
      </PaperProvider>
    </Provider>
  );
}

export default App;
