// SafeKeep Backend API Example - Modular Pricing Integration
// This file shows how to safely handle Stripe payments with modular pricing support
// IMPORTANT: This should run on your server, NOT in the React Native app

const express = require('express');
const cors = require('cors');
const Stripe = require('stripe');
require('dotenv').config(); // Load environment variables from .env file

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Initialize Stripe with secret key (ONLY on backend!)
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// Middleware
app.use(cors());
app.use(express.json());

// Webhook middleware for raw body (needed for Stripe webhook verification)
app.use('/webhook', express.raw({ type: 'application/json' }));

// SafeKeep modular pricing structure
const MODULAR_PRICING = {
  SERVICES: {
    CONTACTS: {
      id: 'contacts',
      name: 'Contacts Backup',
      price: 199, // $1.99/month in cents
      description: 'Secure backup of your contacts'
    },
    MESSAGES: {
      id: 'messages',
      name: 'Messages Backup',
      price: 299, // $2.99/month in cents
      description: 'Secure backup of your messages'
    },
    PHOTOS: {
      id: 'photos',
      name: 'Photos Backup',
      price: 499, // $4.99/month in cents
      description: 'Secure backup of your photos'
    }
  },
  COMBINATIONS: {
    CONTACTS_MESSAGES: {
      id: 'contacts_messages',
      name: 'Contacts + Messages',
      price: 399, // $3.99/month (save $0.99)
      services: ['contacts', 'messages'],
      savingsCents: 99,
      storageGb: 5,
      isPopular: false
    },
    CONTACTS_PHOTOS: {
      id: 'contacts_photos',
      name: 'Contacts + Photos',
      price: 599, // $5.99/month (save $0.99)
      services: ['contacts', 'photos'],
      savingsCents: 99,
      storageGb: 50,
      isPopular: false
    },
    MESSAGES_PHOTOS: {
      id: 'messages_photos',
      name: 'Messages + Photos',
      price: 699, // $6.99/month (save $0.99)
      services: ['messages', 'photos'],
      savingsCents: 99,
      storageGb: 50,
      isPopular: true
    },
    ALL_SERVICES: {
      id: 'all_services',
      name: 'Complete Backup',
      price: 799, // $7.99/month (save $1.97)
      services: ['contacts', 'messages', 'photos'],
      savingsCents: 197,
      storageGb: 100,
      isPopular: true
    }
  }
};

// API Routes

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    service: 'SafeKeep Payment API',
    timestamp: new Date().toISOString()
  });
});

// Create Payment Intent with modular pricing support
app.post('/api/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency = 'usd', customerId, description, serviceIds, userId, planId, planName } = req.body;

    // Validate input
    if (!amount || amount < 50) { // Minimum $0.50
      return res.status(400).json({ 
        error: 'Invalid amount. Minimum $0.50 required.' 
      });
    }

    if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
      return res.status(400).json({ 
        error: 'Service IDs array is required and cannot be empty' 
      });
    }

    if (!userId || typeof userId !== 'string') {
      return res.status(400).json({ 
        error: 'Valid User ID is required' 
      });
    }

    console.log('💳 Creating payment intent for modular services:', { 
      amount, 
      currency, 
      serviceIds, 
      userId, 
      planId, 
      planName 
    });

    // Create payment intent with service combination metadata
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount), // Ensure integer
      currency,
      customer: customerId,
      description: description || `SafeKeep ${planName || 'Custom Plan'}`,
      metadata: {
        app: 'SafeKeep',
        environment: process.env.NODE_ENV || 'development',
        created_at: new Date().toISOString(),
        userId: userId,
        serviceIds: JSON.stringify(serviceIds),
        planId: planId || 'custom',
        planName: planName || 'Custom Plan'
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });

    console.log('✅ Payment intent created for services:', paymentIntent.id, serviceIds);

    // Return client secret to frontend (safe to send)
    res.json({
      success: true,
      data: {
        client_secret: paymentIntent.client_secret,
        payment_intent_id: paymentIntent.id,
        amount: amount,
        serviceIds: serviceIds,
        planName: planName || 'Custom Plan'
      }
    });

  } catch (error) {
    console.error('❌ Payment intent creation failed:', error);
    res.status(500).json({ 
      success: false,
      error: {
        code: 'PAYMENT_INTENT_ERROR',
        message: 'Payment intent creation failed',
        details: { message: error.message },
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Create Customer
app.post('/api/create-customer', async (req, res) => {
  try {
    const { email, name } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    console.log('👤 Creating customer:', { email, name });

    // Check if customer already exists
    const existingCustomers = await stripe.customers.list({
      email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      console.log('✅ Customer already exists:', existingCustomers.data[0].id);
      return res.json({ customer: existingCustomers.data[0] });
    }

    // Create new customer
    const customer = await stripe.customers.create({
      email,
      name,
      metadata: {
        app: 'SafeKeep',
        created_at: new Date().toISOString(),
      },
    });

    console.log('✅ Customer created:', customer.id);
    res.json({ customer });

  } catch (error) {
    console.error('❌ Customer creation failed:', error);
    res.status(500).json({ 
      error: 'Customer creation failed',
      message: error.message 
    });
  }
});

// Get available service combinations with pricing
app.get('/api/pricing/combinations', (req, res) => {
  try {
    const combinations = Object.values(MODULAR_PRICING.COMBINATIONS).map(combo => ({
      planId: combo.id,
      planName: combo.name,
      priceCents: combo.price,
      services: combo.services,
      storageGb: combo.storageGb,
      isPopular: combo.isPopular,
      savingsCents: combo.savingsCents || 0
    }));

    res.json({
      success: true,
      data: combinations
    });
  } catch (error) {
    console.error('❌ Error getting service combinations:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PRICING_ERROR',
        message: 'Failed to retrieve service combinations',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Calculate optimal pricing for selected services
app.post('/api/pricing/calculate', (req, res) => {
  try {
    const { serviceIds } = req.body;

    // Input validation
    if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: new Date().toISOString()
        }
      });
    }

    // Calculate individual total
    let individualTotal = 0;
    const validServices = [];
    
    for (const serviceId of serviceIds) {
      const service = Object.values(MODULAR_PRICING.SERVICES).find(s => s.id === serviceId);
      if (service) {
        individualTotal += service.price;
        validServices.push(serviceId);
      }
    }

    if (validServices.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SERVICES',
          message: 'No valid services found in the provided service IDs',
          timestamp: new Date().toISOString()
        }
      });
    }

    // Find best combination plan
    let bestPlan = null;
    let maxSavings = 0;

    for (const combo of Object.values(MODULAR_PRICING.COMBINATIONS)) {
      // Check if combination covers all requested services
      const coversAllServices = validServices.every(serviceId => combo.services.includes(serviceId));
      const hasExtraServices = combo.services.some(serviceId => !validServices.includes(serviceId));
      
      if (coversAllServices && !hasExtraServices) {
        const savings = individualTotal - combo.price;
        if (savings > maxSavings) {
          maxSavings = savings;
          bestPlan = combo;
        }
      }
    }

    // If no combination plan is better, recommend individual services
    if (!bestPlan || maxSavings <= 0) {
      res.json({
        success: true,
        data: {
          recommendedPlanId: 'individual',
          recommendedPlanName: 'Individual Services',
          priceCents: individualTotal,
          savingsCents: 0,
          individualTotalCents: individualTotal
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          recommendedPlanId: bestPlan.id,
          recommendedPlanName: bestPlan.name,
          priceCents: bestPlan.price,
          savingsCents: maxSavings,
          individualTotalCents: individualTotal
        }
      });
    }

  } catch (error) {
    console.error('❌ Error calculating optimal pricing:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PRICING_CALCULATION_ERROR',
        message: 'Failed to calculate optimal pricing',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Validate service combination
app.post('/api/services/validate', (req, res) => {
  try {
    const { serviceIds } = req.body;

    // Input validation
    if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: new Date().toISOString()
        }
      });
    }

    const errors = [];
    const warnings = [];
    const validServices = Object.values(MODULAR_PRICING.SERVICES).map(s => s.id);

    // Check for invalid services
    const invalidServices = serviceIds.filter(id => !validServices.includes(id));
    if (invalidServices.length > 0) {
      errors.push(`Invalid service IDs: ${invalidServices.join(', ')}`);
    }

    // Check for duplicates
    const duplicates = serviceIds.filter((id, index) => serviceIds.indexOf(id) !== index);
    if (duplicates.length > 0) {
      errors.push(`Duplicate service IDs: ${duplicates.join(', ')}`);
    }

    // Check if combination plan would be better
    const validServiceIds = serviceIds.filter(id => validServices.includes(id));
    if (validServiceIds.length > 1) {
      const combo = Object.values(MODULAR_PRICING.COMBINATIONS).find(c => 
        c.services.length === validServiceIds.length && 
        validServiceIds.every(id => c.services.includes(id))
      );
      if (combo) {
        warnings.push(`Consider the ${combo.name} plan for better value`);
      }
    }

    res.json({
      success: true,
      data: {
        isValid: errors.length === 0,
        errors,
        warnings
      }
    });

  } catch (error) {
    console.error('❌ Error validating service combination:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Failed to validate service combination',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Get individual services
app.get('/api/services', (req, res) => {
  try {
    const services = Object.values(MODULAR_PRICING.SERVICES).map(service => ({
      id: service.id,
      name: service.name,
      priceCents: service.price,
      description: service.description
    }));

    res.json({
      success: true,
      data: services
    });
  } catch (error) {
    console.error('❌ Error getting services:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVICES_ERROR',
        message: 'Failed to retrieve services',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Create subscription with service combination
app.post('/api/subscriptions', async (req, res) => {
  try {
    const { userId, serviceIds, paymentMethodId, customerId } = req.body;

    // Input validation
    if (!userId || typeof userId !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: new Date().toISOString()
        }
      });
    }

    if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Service IDs array is required and cannot be empty',
          timestamp: new Date().toISOString()
        }
      });
    }

    // Validate services exist
    const validServices = Object.values(MODULAR_PRICING.SERVICES).map(s => s.id);
    const invalidServices = serviceIds.filter(id => !validServices.includes(id));
    if (invalidServices.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SERVICES',
          message: `Invalid service IDs: ${invalidServices.join(', ')}`,
          timestamp: new Date().toISOString()
        }
      });
    }

    // Calculate optimal pricing
    const pricingResponse = await calculateOptimalPricing(serviceIds);
    
    console.log('🔄 Creating subscription for user:', userId, 'with services:', serviceIds);
    console.log('💰 Calculated pricing:', pricingResponse);

    // Create subscription record (in a real app, this would be stored in database)
    const subscription = {
      id: `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: userId,
      planId: pricingResponse.recommendedPlanId,
      serviceIds: serviceIds,
      totalPriceCents: pricingResponse.priceCents,
      status: 'active',
      stripeCustomerId: customerId,
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // TODO: Store subscription in database
    // Example: await database.subscriptions.create(subscription);

    res.status(201).json({
      success: true,
      data: subscription
    });

  } catch (error) {
    console.error('❌ Error creating subscription:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SUBSCRIPTION_CREATION_ERROR',
        message: 'Failed to create subscription',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Get user's subscription details
app.get('/api/subscriptions/:userId', (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId || typeof userId !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Valid User ID is required',
          timestamp: new Date().toISOString()
        }
      });
    }

    // TODO: Retrieve from database
    // For now, return mock data
    const subscriptionDetails = {
      subscriptionId: `sub_${userId}_mock`,
      planId: 'messages_photos',
      planName: 'Messages + Photos',
      currentPriceCents: 699,
      status: 'active',
      services: ['messages', 'photos'],
      storageQuotaGb: 50,
      storageUsedGb: 12.5,
      nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)
    };

    res.json({
      success: true,
      data: subscriptionDetails
    });

  } catch (error) {
    console.error('❌ Error getting subscription details:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SUBSCRIPTION_RETRIEVAL_ERROR',
        message: 'Failed to retrieve subscription details',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Helper function to calculate optimal pricing
async function calculateOptimalPricing(serviceIds) {
  // Calculate individual total
  let individualTotal = 0;
  const validServices = [];
  
  for (const serviceId of serviceIds) {
    const service = Object.values(MODULAR_PRICING.SERVICES).find(s => s.id === serviceId);
    if (service) {
      individualTotal += service.price;
      validServices.push(serviceId);
    }
  }

  // Find best combination plan
  let bestPlan = null;
  let maxSavings = 0;

  for (const combo of Object.values(MODULAR_PRICING.COMBINATIONS)) {
    // Check if combination covers all requested services
    const coversAllServices = validServices.every(serviceId => combo.services.includes(serviceId));
    const hasExtraServices = combo.services.some(serviceId => !validServices.includes(serviceId));
    
    if (coversAllServices && !hasExtraServices) {
      const savings = individualTotal - combo.price;
      if (savings > maxSavings) {
        maxSavings = savings;
        bestPlan = combo;
      }
    }
  }

  // Return optimal pricing
  if (!bestPlan || maxSavings <= 0) {
    return {
      recommendedPlanId: 'individual',
      recommendedPlanName: 'Individual Services',
      priceCents: individualTotal,
      savingsCents: 0,
      individualTotalCents: individualTotal
    };
  } else {
    return {
      recommendedPlanId: bestPlan.id,
      recommendedPlanName: bestPlan.name,
      priceCents: bestPlan.price,
      savingsCents: maxSavings,
      individualTotalCents: individualTotal
    };
  }
}

// Get legacy plans (for backward compatibility)
app.get('/api/plans', (req, res) => {
  res.json({ 
    plans: MODULAR_PRICING.COMBINATIONS,
    services: MODULAR_PRICING.SERVICES,
    message: 'Legacy endpoint - use /api/pricing/combinations for modular pricing'
  });
});

// Stripe Webhook Handler with modular subscription support
app.post('/webhook', (req, res) => {
  const sig = req.headers['stripe-signature'];
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!webhookSecret) {
    console.error('❌ Webhook secret not configured');
    return res.status(500).send('Webhook secret not configured');
  }

  let event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    console.log('✅ Webhook signature verified:', event.type);
  } catch (err) {
    console.error('❌ Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('💰 Payment succeeded for modular services:', paymentIntent.id);
      
      // Extract service information from metadata
      const { userId, serviceIds, planId, planName } = paymentIntent.metadata;
      console.log('📋 Service details:', { userId, serviceIds, planId, planName });
      
      // TODO: Activate user's selected services in database
      // Example: activateUserServices(userId, JSON.parse(serviceIds || '[]'), planId);
      
      // TODO: Send confirmation email with service details
      // Example: sendServiceActivationEmail(userId, serviceIds, planName);
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('❌ Payment failed for modular services:', failedPayment.id);
      
      const failedMetadata = failedPayment.metadata;
      console.log('📋 Failed payment details:', failedMetadata);
      
      // TODO: Handle failed payment, notify user about specific services
      // Example: notifyPaymentFailure(failedMetadata.userId, failedMetadata.serviceIds);
      break;

    case 'customer.subscription.created':
      const subscription = event.data.object;
      console.log('🔄 Modular subscription created:', subscription.id);
      
      // Extract service information from subscription metadata
      const subMetadata = subscription.metadata;
      console.log('📋 Subscription services:', subMetadata);
      
      // TODO: Activate user's subscription with selected services
      // Example: createModularSubscription(subscription.customer, subMetadata);
      break;

    case 'customer.subscription.updated':
      const updatedSubscription = event.data.object;
      console.log('🔄 Modular subscription updated:', updatedSubscription.id);
      
      // Handle service changes in subscription
      const updatedMetadata = updatedSubscription.metadata;
      console.log('📋 Updated subscription services:', updatedMetadata);
      
      // TODO: Update user's active services based on subscription changes
      // Example: updateUserServices(updatedSubscription.customer, updatedMetadata);
      break;

    case 'customer.subscription.deleted':
      const canceledSubscription = event.data.object;
      console.log('❌ Modular subscription canceled:', canceledSubscription.id);
      
      const canceledMetadata = canceledSubscription.metadata;
      console.log('📋 Canceled subscription services:', canceledMetadata);
      
      // TODO: Deactivate all user's subscription services
      // Example: deactivateUserServices(canceledSubscription.customer, canceledMetadata);
      break;

    case 'invoice.payment_succeeded':
      const invoice = event.data.object;
      console.log('💰 Invoice payment succeeded for modular subscription:', invoice.id);
      
      // TODO: Extend service access period
      // Example: extendServiceAccess(invoice.customer, invoice.subscription);
      break;

    case 'invoice.payment_failed':
      const failedInvoice = event.data.object;
      console.log('❌ Invoice payment failed for modular subscription:', failedInvoice.id);
      
      // TODO: Handle failed recurring payment, possibly suspend services
      // Example: suspendServices(failedInvoice.customer, failedInvoice.subscription);
      break;

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  res.json({ 
    received: true,
    processed: true,
    eventType: event.type,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 SafeKeep Modular Pricing API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`💳 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`\n📋 Available Endpoints:`);
  console.log(`   GET  /api/pricing/combinations - Get service combinations`);
  console.log(`   POST /api/pricing/calculate - Calculate optimal pricing`);
  console.log(`   POST /api/services/validate - Validate service combination`);
  console.log(`   GET  /api/services - Get individual services`);
  console.log(`   POST /api/subscriptions - Create subscription`);
  console.log(`   GET  /api/subscriptions/:userId - Get subscription details`);
  console.log(`   POST /api/create-payment-intent - Create payment intent`);
  console.log(`   POST /webhook - Stripe webhook handler`);
  
  // Validate Stripe configuration
  if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY.includes('PLACEHOLDER')) {
    console.warn('⚠️ WARNING: Stripe secret key not properly configured!');
  } else {
    console.log('✅ Stripe configuration loaded');
  }
  
  console.log(`\n🎯 Modular Pricing Structure:`);
  console.log(`   Services: ${Object.keys(MODULAR_PRICING.SERVICES).length} individual services`);
  console.log(`   Combinations: ${Object.keys(MODULAR_PRICING.COMBINATIONS).length} combination plans`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Server shutting down gracefully...');
  process.exit(0);
});

module.exports = app;
