/* ModularPricingUI Main Component Styles */

.modular-pricing-ui {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px;
}

.modular-pricing-ui.theme-dark {
    background: #1a1a1a;
    color: #ffffff;
}

/* Container */
.pricing-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.theme-dark .pricing-container {
    background: #2d2d2d;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Header */
.pricing-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
}

.pricing-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 12px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pricing-subtitle {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 400;
}

/* Main content layout */
.pricing-main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    padding: 40px 30px;
    min-height: 600px;
}

.pricing-left-column,
.pricing-right-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* Pricing section */
.pricing-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Actions section */
.pricing-actions {
    background: #f8f9fa;
    padding: 30px;
    border-top: 2px solid #e9ecef;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.theme-dark .pricing-actions {
    background: #1a1a1a;
    border-top-color: #444;
}

.action-summary {
    color: #666;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    text-align: center;
}

.theme-dark .action-summary {
    color: #ccc;
}

.continue-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    padding: 16px 40px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.continue-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.continue-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.continue-btn.ready {
    animation: readyPulse 2s infinite;
}

@keyframes readyPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(79, 172, 254, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
    }
}

/* Loading states */
.modular-pricing-ui.loading {
    pointer-events: none;
}

.modular-pricing-ui.loading::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* Animation states */
.modular-pricing-ui.updating {
    transition: opacity 0.3s ease;
}

.modular-pricing-ui.updating .pricing-main-content {
    opacity: 0.7;
}

/* Mobile layout */
.modular-pricing-ui.mobile-layout .pricing-main-content {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 24px 20px;
}

.modular-pricing-ui.mobile-layout .pricing-header {
    padding: 30px 20px;
}

.modular-pricing-ui.mobile-layout .pricing-title {
    font-size: 2rem;
}

.modular-pricing-ui.mobile-layout .pricing-subtitle {
    font-size: 1rem;
}

.modular-pricing-ui.mobile-layout .pricing-actions {
    padding: 20px;
}

/* Responsive breakpoints */
@media (max-width: 1024px) {
    .pricing-main-content {
        gap: 30px;
        padding: 30px 24px;
    }
    
    .pricing-container {
        margin: 0 10px;
    }
}

@media (max-width: 768px) {
    .modular-pricing-ui {
        padding: 10px;
    }
    
    .pricing-container {
        border-radius: 16px;
        margin: 0;
    }
    
    .pricing-main-content {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }
    
    .pricing-header {
        padding: 24px 20px;
    }
    
    .pricing-title {
        font-size: 1.8rem;
    }
    
    .pricing-subtitle {
        font-size: 1rem;
    }
    
    .pricing-actions {
        padding: 20px;
    }
    
    .continue-btn {
        padding: 14px 32px;
        font-size: 1rem;
        min-width: 180px;
    }
}

@media (max-width: 480px) {
    .modular-pricing-ui {
        padding: 5px;
    }
    
    .pricing-container {
        border-radius: 12px;
    }
    
    .pricing-main-content {
        padding: 16px;
        gap: 16px;
    }
    
    .pricing-header {
        padding: 20px 16px;
    }
    
    .pricing-title {
        font-size: 1.6rem;
    }
    
    .pricing-subtitle {
        font-size: 0.9rem;
    }
    
    .pricing-left-column,
    .pricing-right-column {
        gap: 16px;
    }
    
    .pricing-actions {
        padding: 16px;
    }
    
    .continue-btn {
        padding: 12px 24px;
        font-size: 0.95rem;
        min-width: 160px;
    }
    
    .action-summary {
        font-size: 0.9rem;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .pricing-container {
        border: 3px solid #000;
    }
    
    .pricing-header {
        background: #000;
        color: #fff;
    }
    
    .pricing-actions {
        border-top: 3px solid #000;
    }
    
    .continue-btn {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }
    
    .continue-btn:disabled {
        background: #666;
        border-color: #666;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .modular-pricing-ui,
    .continue-btn,
    .pricing-main-content {
        transition: none;
    }
    
    .continue-btn:hover:not(:disabled) {
        transform: none;
    }
    
    .continue-btn.ready {
        animation: none;
    }
}

/* Focus management */
.modular-pricing-ui:focus-within {
    outline: 2px solid #4facfe;
    outline-offset: 4px;
}

.continue-btn:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .modular-pricing-ui {
        background: #fff;
        padding: 0;
    }
    
    .pricing-container {
        box-shadow: none;
        border: 2px solid #000;
    }
    
    .pricing-header {
        background: #f0f0f0;
        color: #000;
    }
    
    .pricing-actions {
        display: none;
    }
    
    .pricing-main-content {
        break-inside: avoid;
    }
}

/* Accessibility improvements */
.modular-pricing-ui[aria-busy="true"] {
    cursor: wait;
}

.modular-pricing-ui[aria-busy="true"] * {
    pointer-events: none;
}

/* Error states */
.modular-pricing-ui.error {
    border: 2px solid #dc3545;
}

.modular-pricing-ui.error .pricing-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* Success states */
.modular-pricing-ui.success {
    border: 2px solid #28a745;
}

.modular-pricing-ui.success .continue-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

/* Custom scrollbar for overflow areas */
.modular-pricing-ui ::-webkit-scrollbar {
    width: 8px;
}

.modular-pricing-ui ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modular-pricing-ui ::-webkit-scrollbar-thumb {
    background: #4facfe;
    border-radius: 4px;
}

.modular-pricing-ui ::-webkit-scrollbar-thumb:hover {
    background: #2196f3;
}