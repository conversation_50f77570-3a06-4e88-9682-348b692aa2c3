-- Update file_metadata table to support tags and organization features

-- Add tags column as a string array
ALTER TABLE file_metadata ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';

-- Add sync_status column
ALTER TABLE file_metadata ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'synced' 
  CHECK (sync_status IN ('synced', 'pending', 'failed'));

-- Create index on hash for faster deduplication lookups
CREATE INDEX IF NOT EXISTS idx_file_metadata_hash ON file_metadata(hash);

-- Create index on tags for faster tag-based searches
CREATE INDEX IF NOT EXISTS idx_file_metadata_tags ON file_metadata USING GIN(tags);

-- Create index on storage_path for faster folder-based lookups
CREATE INDEX IF NOT EXISTS idx_file_metadata_storage_path ON file_metadata(storage_path text_pattern_ops);

-- Create a function to find duplicate files
CREATE OR REPLACE FUNCTION find_duplicate_files(user_id_param UUID)
RETURNS TABLE (
  hash TEXT,
  files JSONB
) AS $$
BEGIN
  RETURN QUERY
  WITH duplicate_hashes AS (
    SELECT 
      hash,
      COUNT(*) as file_count
    FROM 
      file_metadata
    WHERE 
      user_id = user_id_param
    GROUP BY 
      hash
    HAVING 
      COUNT(*) > 1
  )
  SELECT 
    dh.hash,
    jsonb_agg(
      jsonb_build_object(
        'id', fm.id,
        'original_name', fm.original_name,
        'size', fm.size,
        'category', fm.category,
        'uploaded_at', fm.uploaded_at,
        'storage_path', fm.storage_path
      )
    ) as files
  FROM 
    duplicate_hashes dh
  JOIN 
    file_metadata fm ON dh.hash = fm.hash
  WHERE 
    fm.user_id = user_id_param
  GROUP BY 
    dh.hash;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION find_duplicate_files(UUID) TO authenticated;

-- Create a function to add a virtual folder to user preferences
CREATE OR REPLACE FUNCTION add_virtual_folder(user_id_param UUID, folder_path_param TEXT)
RETURNS VOID AS $$
DECLARE
  current_folders TEXT[];
BEGIN
  -- Get current folders from user preferences
  SELECT COALESCE(preferences->'virtual_folders', '[]'::jsonb)::TEXT[] 
  INTO current_folders 
  FROM users 
  WHERE id = user_id_param;
  
  -- Add new folder if it doesn't exist
  IF NOT folder_path_param = ANY(current_folders) THEN
    -- Update user preferences
    UPDATE users 
    SET preferences = jsonb_set(
      COALESCE(preferences, '{}'::jsonb),
      '{virtual_folders}',
      COALESCE(preferences->'virtual_folders', '[]'::jsonb) || to_jsonb(folder_path_param)
    )
    WHERE id = user_id_param;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION add_virtual_folder(UUID, TEXT) TO authenticated;

-- Create a function to get virtual folders
CREATE OR REPLACE FUNCTION get_virtual_folders(user_id_param UUID)
RETURNS TABLE (
  id TEXT,
  name TEXT,
  path TEXT,
  parent_id TEXT,
  file_count INTEGER
) AS $$
DECLARE
  folder_paths TEXT[];
  folder_record RECORD;
  folder_id TEXT;
  folder_name TEXT;
  parent_path TEXT;
  parent_id TEXT;
  file_count INTEGER;
  i INTEGER := 1;
BEGIN
  -- Get folder paths from user preferences
  SELECT COALESCE(preferences->'virtual_folders', '[]'::jsonb)::TEXT[] 
  INTO folder_paths 
  FROM users 
  WHERE id = user_id_param;
  
  -- Process each folder
  FOREACH folder_path IN ARRAY folder_paths LOOP
    -- Extract folder name (last part of path)
    folder_name := split_part(folder_path, '/', array_length(string_to_array(folder_path, '/'), 1));
    
    -- Calculate parent path
    parent_path := substring(folder_path from 1 for length(folder_path) - length(folder_name) - 1);
    
    -- Generate IDs
    folder_id := 'folder-' || i;
    IF parent_path = '' THEN
      parent_id := NULL;
    ELSE
      -- Find parent ID (simplified approach)
      parent_id := 'folder-' || (i - 1);
    END IF;
    
    -- Count files in this folder
    SELECT COUNT(*) INTO file_count 
    FROM file_metadata 
    WHERE user_id = user_id_param 
    AND storage_path LIKE folder_path || '/%';
    
    -- Return the folder record
    id := folder_id;
    name := folder_name;
    path := folder_path;
    RETURN NEXT;
    
    i := i + 1;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_virtual_folders(UUID) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION find_duplicate_files(UUID) IS 'Finds files with duplicate hashes for a specific user';
COMMENT ON FUNCTION add_virtual_folder(UUID, TEXT) IS 'Adds a virtual folder to user preferences';
COMMENT ON FUNCTION get_virtual_folders(UUID) IS 'Gets all virtual folders for a user';