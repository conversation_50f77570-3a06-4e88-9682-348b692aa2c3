import BackupManager from '../../src/services/BackupManager';
import ContactBackupService from '../../src/services/ContactBackupService';
import MessageBackupService from '../../src/services/MessageBackupService';
import PhotoBackupService from '../../src/services/PhotoBackupService';
import BackupRecoveryService from '../../src/services/BackupRecoveryService';
import AuthService from '../../src/services/AuthService';
import PermissionService from '../../src/services/PermissionService';
import NotificationService from '../../src/services/NotificationService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupConfiguration, BackupRecoveryState } from '../../src/types/backup';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage');
jest.mock('../../src/utils/helpers', () => ({
  isWiFiConnected: jest.fn().mockResolvedValue(true),
  hasSufficientBattery: jest.fn().mockResolvedValue(true),
  generateId: jest.fn().mockReturnValue('test-session-id'),
  delay: jest.fn().mockResolvedValue(undefined)
}));
jest.mock('react-native', () => ({
  Platform: { OS: 'android' },
  PermissionsAndroid: {
    request: jest.fn().mockResolvedValue('granted'),
    PERMISSIONS: { READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE' },
    RESULTS: { GRANTED: 'granted' }
  }
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('Backup Interruption and Resume Integration Tests', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    aud: 'authenticated',
    role: 'authenticated',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    app_metadata: {},
    user_metadata: {}
  };

  const defaultConfiguration: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium'
  };

  const mockContacts = Array.from({ length: 5 }, (_, i) => ({
    recordID: `${i}`,
    displayName: `Contact ${i}`,
    phoneNumbers: [{ label: 'mobile', number: `+123456789${i}` }],
    emailAddresses: []
  }));

  const mockMessages = Array.from({ length: 3 }, (_, i) => ({
    id: `${i}`,
    body: `Message ${i}`,
    address: '+1234567890',
    date: Date.now() - i * 1000
  }));

  const mockPhotos = Array.from({ length: 4 }, (_, i) => ({
    uri: `file://photo${i}.jpg`,
    filename: `photo${i}.jpg`,
    type: 'image/jpeg',
    fileSize: 1024000,
    timestamp: Date.now() - i * 1000,
    width: 1920,
    height: 1080
  }));

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Setup basic mocks
    jest.spyOn(AuthService, 'getCurrentUser').mockReturnValue(mockUser);
    jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
      contacts: { granted: true, denied: false, blocked: false, unavailable: false },
      sms: { granted: true, denied: false, blocked: false, unavailable: false },
      photos: { granted: true, denied: false, blocked: false, unavailable: false }
    });
    
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    
    // Setup notification service
    jest.spyOn(NotificationService, 'showResumeNotification').mockResolvedValue();
    jest.spyOn(NotificationService, 'showErrorNotification').mockResolvedValue();
    
    // Setup recovery service
    jest.spyOn(BackupRecoveryService, 'checkForResumableSession').mockResolvedValue(null);
    jest.spyOn(BackupRecoveryService, 'saveRecoveryState').mockResolvedValue();
    jest.spyOn(BackupRecoveryService, 'clearRecoveryState').mockResolvedValue();
    jest.spyOn(BackupRecoveryService, 'resumeBackup').mockResolvedValue({
      success: true,
      recoveredItems: 0,
      errors: []
    });
    
    // Setup backup services with default successful responses
    jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);
    jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue(mockMessages as any);
    jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
      photos: mockPhotos,
      excludedVideos: [],
      totalScanned: 4
    });
    
    jest.spyOn(ContactBackupService, 'pauseBackup').mockImplementation(() => {});
    jest.spyOn(ContactBackupService, 'resumeBackup').mockImplementation(() => {});
    jest.spyOn(MessageBackupService, 'pauseBackup').mockImplementation(() => {});
    jest.spyOn(MessageBackupService, 'resumeBackup').mockImplementation(() => {});
    jest.spyOn(PhotoBackupService, 'pauseBackup').mockImplementation(() => {});
    jest.spyOn(PhotoBackupService, 'resumeBackup').mockImplementation(() => {});
    
    await BackupManager.initialize();
  });

  describe('Backup Interruption Scenarios', () => {
    it('should handle user-initiated backup pause', async () => {
      let backupPaused = false;
      
      // Mock contact backup that can be paused
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async (contacts, onProgress) => {
        for (let i = 0; i < contacts.length; i++) {
          if (backupPaused) {
            break;
          }
          
          if (onProgress) {
            onProgress({
              totalContacts: contacts.length,
              processedContacts: i + 1,
              currentContact: contacts[i].displayName,
              percentage: Math.round(((i + 1) / contacts.length) * 100),
              status: 'processing'
            });
          }
          
          // Simulate processing time
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        return {
          success: !backupPaused,
          itemsProcessed: backupPaused ? 2 : contacts.length,
          errors: backupPaused ? [{
            id: 'pause_error',
            type: 'platform',
            message: 'Backup paused by user',
            timestamp: new Date(),
            retryable: true
          }] : []
        };
      });

      // Start backup
      const backupPromise = BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      // Pause after short delay
      setTimeout(() => {
        backupPaused = true;
        BackupManager.pauseBackup();
      }, 25);

      const result = await backupPromise;

      expect(BackupManager.isPaused()).toBe(true);
      expect(ContactBackupService.pauseBackup).toHaveBeenCalled();
      
      // Should have processed some but not all items
      expect(result.itemsProcessed).toBeLessThan(mockContacts.length);
    });

    it('should handle backup cancellation', async () => {
      let backupCancelled = false;
      
      // Mock photo backup that can be cancelled
      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async (photos, onProgress) => {
        for (let i = 0; i < photos.length; i++) {
          if (backupCancelled) {
            break;
          }
          
          if (onProgress) {
            onProgress({
              totalPhotos: photos.length,
              processedPhotos: i + 1,
              currentPhoto: photos[i].filename,
              bytesUploaded: (i + 1) * 1024000,
              totalBytes: photos.length * 1024000,
              percentage: Math.round(((i + 1) / photos.length) * 100),
              status: 'uploading'
            });
          }
          
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        return {
          success: !backupCancelled,
          itemsProcessed: backupCancelled ? 1 : photos.length,
          errors: backupCancelled ? [{
            id: 'cancel_error',
            type: 'platform',
            message: 'Backup cancelled by user',
            timestamp: new Date(),
            retryable: false
          }] : []
        };
      });

      // Start backup
      const backupPromise = BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      // Cancel after short delay
      setTimeout(() => {
        backupCancelled = true;
        BackupManager.cancelBackup();
      }, 25);

      const result = await backupPromise;

      expect(BackupManager.isRunning()).toBe(false);
      expect(result.itemsProcessed).toBeLessThan(mockPhotos.length);
    });

    it('should handle system interruption (app backgrounding)', async () => {
      let systemInterrupted = false;
      
      // Mock message backup that gets interrupted
      jest.spyOn(MessageBackupService, 'backupMessages').mockImplementation(async (messages, onProgress) => {
        for (let i = 0; i < messages.length; i++) {
          if (systemInterrupted) {
            // Save recovery state before interruption
            await BackupRecoveryService.saveRecoveryState({
              sessionId: 'interrupted-session',
              resumable: true,
              lastCheckpoint: {
                dataType: 'messages',
                itemIndex: i,
                timestamp: new Date()
              },
              failedItems: [],
              retryableErrors: []
            });
            
            throw new Error('System interrupted backup');
          }
          
          if (onProgress) {
            onProgress({
              totalMessages: messages.length,
              processedMessages: i + 1,
              currentMessage: `Message ${i + 1}`,
              percentage: Math.round(((i + 1) / messages.length) * 100),
              status: 'processing'
            });
          }
          
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        return {
          success: true,
          itemsProcessed: messages.length,
          errors: []
        };
      });

      // Start backup
      const backupPromise = BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includePhotos: false
      });

      // Simulate system interruption
      setTimeout(() => {
        systemInterrupted = true;
      }, 15);

      const result = await backupPromise;

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('interrupted'))).toBe(true);
      expect(BackupRecoveryService.saveRecoveryState).toHaveBeenCalled();
    });

    it('should handle network disconnection during backup', async () => {
      let networkDisconnected = false;
      
      // Mock contact backup with network failure
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async (contacts, onProgress) => {
        const processedContacts = [];
        const errors: any[] = [];
        
        for (let i = 0; i < contacts.length; i++) {
          if (networkDisconnected && i >= 2) {
            errors.push({
              id: `network_error_${i}`,
              type: 'network',
              message: 'Network disconnected during backup',
              timestamp: new Date(),
              retryable: true,
              itemId: contacts[i].recordID
            });
            break;
          }
          
          processedContacts.push(contacts[i]);
          
          if (onProgress) {
            onProgress({
              totalContacts: contacts.length,
              processedContacts: i + 1,
              currentContact: contacts[i].displayName,
              percentage: Math.round(((i + 1) / contacts.length) * 100),
              status: 'processing'
            });
          }
          
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        return {
          success: errors.length === 0,
          itemsProcessed: processedContacts.length,
          errors
        };
      });

      // Start backup
      const backupPromise = BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      // Simulate network disconnection
      setTimeout(() => {
        networkDisconnected = true;
      }, 25);

      const result = await backupPromise;

      expect(result.success).toBe(false);
      expect(result.itemsProcessed).toBe(2); // Should have processed 2 contacts before disconnection
      expect(result.errors.some(e => e.type === 'network')).toBe(true);
      expect(result.errors.some(e => e.retryable)).toBe(true);
    });

    it('should handle low battery interruption', async () => {
      const { hasSufficientBattery } = require('../../src/utils/helpers');
      
      // Start with sufficient battery
      hasSufficientBattery.mockResolvedValue(true);
      
      let batteryLow = false;
      
      // Mock photo backup that checks battery
      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async (photos, onProgress) => {
        for (let i = 0; i < photos.length; i++) {
          // Check battery before each photo
          const sufficientBattery = await hasSufficientBattery();
          if (!sufficientBattery || batteryLow) {
            return {
              success: false,
              itemsProcessed: i,
              errors: [{
                id: 'battery_low_error',
                type: 'platform',
                message: 'Backup paused due to low battery',
                timestamp: new Date(),
                retryable: true
              }]
            };
          }
          
          if (onProgress) {
            onProgress({
              totalPhotos: photos.length,
              processedPhotos: i + 1,
              currentPhoto: photos[i].filename,
              bytesUploaded: (i + 1) * 1024000,
              totalBytes: photos.length * 1024000,
              percentage: Math.round(((i + 1) / photos.length) * 100),
              status: 'uploading'
            });
          }
          
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        return {
          success: true,
          itemsProcessed: photos.length,
          errors: []
        };
      });

      // Start backup
      const backupPromise = BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      // Simulate battery getting low
      setTimeout(() => {
        batteryLow = true;
        hasSufficientBattery.mockResolvedValue(false);
      }, 25);

      const result = await backupPromise;

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('battery'))).toBe(true);
    });
  });

  describe('Backup Resume Functionality', () => {
    it('should detect resumable backup session on initialization', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'resumable-session-123',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 2,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'checkForResumableSession').mockResolvedValue(mockRecoveryState);

      // Re-initialize to trigger resumable session detection
      await BackupManager.initialize();

      expect(BackupRecoveryService.checkForResumableSession).toHaveBeenCalled();
      expect(NotificationService.showResumeNotification).toHaveBeenCalledWith(
        mockRecoveryState.sessionId,
        mockRecoveryState.lastCheckpoint.dataType,
        0, // progress
        100, // total
        expect.any(Function), // resume callback
        expect.any(Function)  // restart callback
      );
    });

    it('should resume backup from last checkpoint', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'resume-session-456',
        resumable: true,
        lastCheckpoint: {
          dataType: 'contacts',
          itemIndex: 3,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'resumeBackup').mockResolvedValue({
        success: true,
        recoveredItems: 2, // Remaining contacts
        errors: []
      });

      const result = await BackupManager.resumeBackupFromRecovery(mockRecoveryState);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2);
      expect(BackupRecoveryService.resumeBackup).toHaveBeenCalledWith(
        mockRecoveryState,
        expect.any(Object), // configuration
        expect.any(Function) // progress callback
      );
    });

    it('should handle resume failure gracefully', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'failed-resume-session',
        resumable: true,
        lastCheckpoint: {
          dataType: 'messages',
          itemIndex: 1,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'resumeBackup').mockResolvedValue({
        success: false,
        recoveredItems: 0,
        errors: [{
          id: 'resume_error',
          type: 'platform',
          message: 'Failed to resume backup',
          timestamp: new Date(),
          retryable: true
        }]
      });

      const result = await BackupManager.resumeBackupFromRecovery(mockRecoveryState);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toContain('Failed to resume');
    });

    it('should clear recovery state after successful resume', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'clear-session-789',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 1,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'resumeBackup').mockResolvedValue({
        success: true,
        recoveredItems: 3,
        errors: []
      });

      await BackupManager.resumeBackupFromRecovery(mockRecoveryState);

      // Should clear recovery state after successful resume
      expect(BackupRecoveryService.clearRecoveryState).toHaveBeenCalledWith(mockRecoveryState.sessionId);
    });

    it('should handle partial resume with some failed items', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'partial-resume-session',
        resumable: true,
        lastCheckpoint: {
          dataType: 'contacts',
          itemIndex: 2,
          timestamp: new Date()
        },
        failedItems: [{
          id: 'failed_contact_1',
          type: 'network',
          message: 'Network error during contact backup',
          timestamp: new Date(),
          retryable: true,
          itemId: 'contact_1'
        }],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'resumeBackup').mockResolvedValue({
        success: false,
        recoveredItems: 2,
        errors: [{
          id: 'partial_resume_error',
          type: 'network',
          message: 'Some items could not be recovered',
          timestamp: new Date(),
          retryable: true
        }]
      });

      const result = await BackupManager.resumeBackupFromRecovery(mockRecoveryState);

      expect(result.success).toBe(false);
      expect(result.itemsProcessed).toBe(2);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].retryable).toBe(true);
    });
  });

  describe('Recovery State Management', () => {
    it('should save recovery state during interruption', async () => {
      let saveRecoveryStateCalled = false;
      
      jest.spyOn(BackupRecoveryService, 'saveRecoveryState').mockImplementation(async (state) => {
        saveRecoveryStateCalled = true;
        expect(state.sessionId).toBeDefined();
        expect(state.resumable).toBe(true);
        expect(state.lastCheckpoint).toBeDefined();
      });

      // Mock contact backup that saves state before interruption
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async (contacts, onProgress) => {
        for (let i = 0; i < contacts.length; i++) {
          if (i === 2) {
            // Save recovery state before interruption
            await BackupRecoveryService.saveRecoveryState({
              sessionId: 'interrupted-session',
              resumable: true,
              lastCheckpoint: {
                dataType: 'contacts',
                itemIndex: i,
                timestamp: new Date()
              },
              failedItems: [],
              retryableErrors: []
            });
            
            throw new Error('Backup interrupted');
          }
          
          if (onProgress) {
            onProgress({
              totalContacts: contacts.length,
              processedContacts: i + 1,
              currentContact: contacts[i].displayName,
              percentage: Math.round(((i + 1) / contacts.length) * 100),
              status: 'processing'
            });
          }
          
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        return {
          success: true,
          itemsProcessed: contacts.length,
          errors: []
        };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(saveRecoveryStateCalled).toBe(true);
    });

    it('should handle recovery state corruption', async () => {
      // Mock corrupted recovery state
      jest.spyOn(BackupRecoveryService, 'checkForResumableSession').mockRejectedValue(
        new Error('Recovery state corrupted')
      );

      // Should not throw error during initialization
      await expect(BackupManager.initialize()).resolves.not.toThrow();
      
      // Should show error notification about corrupted state
      expect(NotificationService.showErrorNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('initialize backup manager'),
          retryable: true
        })
      );
    });

    it('should clean up old recovery states', async () => {
      const oldRecoveryState: BackupRecoveryState = {
        sessionId: 'old-session',
        resumable: false, // Not resumable anymore
        lastCheckpoint: {
          dataType: 'contacts',
          itemIndex: 0,
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
        },
        failedItems: [],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'checkForResumableSession').mockResolvedValue(oldRecoveryState);

      await BackupManager.initialize();

      // Should clear old non-resumable state
      expect(BackupRecoveryService.clearRecoveryState).toHaveBeenCalledWith(oldRecoveryState.sessionId);
    });
  });

  describe('User Interaction During Resume', () => {
    it('should allow user to choose between resume and restart', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'user-choice-session',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 2,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      let resumeCallback: (() => Promise<void>) | undefined;
      let restartCallback: (() => Promise<void>) | undefined;

      jest.spyOn(NotificationService, 'showResumeNotification').mockImplementation(
        async (sessionId, dataType, progress, total, onResume, onRestart) => {
          resumeCallback = onResume;
          restartCallback = onRestart;
        }
      );

      jest.spyOn(BackupRecoveryService, 'checkForResumableSession').mockResolvedValue(mockRecoveryState);

      await BackupManager.initialize();

      expect(resumeCallback).toBeDefined();
      expect(restartCallback).toBeDefined();

      // Test resume callback
      if (resumeCallback) {
        await resumeCallback();
        expect(BackupRecoveryService.resumeBackup).toHaveBeenCalled();
      }

      // Test restart callback
      if (restartCallback) {
        await restartCallback();
        expect(BackupRecoveryService.clearRecoveryState).toHaveBeenCalledWith(mockRecoveryState.sessionId);
      }
    });

    it('should handle user dismissing resume notification', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'dismissed-session',
        resumable: true,
        lastCheckpoint: {
          dataType: 'messages',
          itemIndex: 1,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'checkForResumableSession').mockResolvedValue(mockRecoveryState);

      await BackupManager.initialize();

      // User dismisses notification - recovery state should remain
      expect(BackupRecoveryService.clearRecoveryState).not.toHaveBeenCalled();
    });
  });

  describe('Multi-Data Type Resume', () => {
    it('should resume from correct data type checkpoint', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'multi-type-resume',
        resumable: true,
        lastCheckpoint: {
          dataType: 'messages', // Resume from messages
          itemIndex: 1,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'resumeBackup').mockImplementation(async (state, config, onProgress) => {
        // Should resume from messages, not contacts
        expect(state.lastCheckpoint.dataType).toBe('messages');
        
        if (onProgress) {
          onProgress({
            session: {
              id: state.sessionId,
              userId: mockUser.id,
              startTime: new Date(),
              status: 'in_progress',
              progress: {
                contacts: { total: 5, completed: 5, failed: 0, status: 'completed', errors: [] }, // Already completed
                messages: { total: 3, completed: 1, failed: 0, status: 'in_progress', errors: [] }, // Resume from here
                photos: { total: 4, completed: 0, failed: 0, status: 'pending', errors: [] }
              },
              totalItems: 12,
              completedItems: 6,
              configuration: config
            },
            currentDataType: 'messages',
            overallProgress: 50
          });
        }
        
        return {
          success: true,
          recoveredItems: 6, // 2 remaining messages + 4 photos
          errors: []
        };
      });

      const result = await BackupManager.resumeBackupFromRecovery(mockRecoveryState);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(6);
    });

    it('should handle resume with mixed success across data types', async () => {
      const mockRecoveryState: BackupRecoveryState = {
        sessionId: 'mixed-success-resume',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 0,
          timestamp: new Date()
        },
        failedItems: [{
          id: 'failed_message_1',
          type: 'network',
          message: 'Message backup failed',
          timestamp: new Date(),
          retryable: true,
          itemId: 'message_1'
        }],
        retryableErrors: []
      };

      jest.spyOn(BackupRecoveryService, 'resumeBackup').mockResolvedValue({
        success: false,
        recoveredItems: 3, // Some photos succeeded
        errors: [{
          id: 'resume_partial_error',
          type: 'network',
          message: 'Some items failed during resume',
          timestamp: new Date(),
          retryable: true
        }]
      });

      const result = await BackupManager.resumeBackupFromRecovery(mockRecoveryState);

      expect(result.success).toBe(false);
      expect(result.itemsProcessed).toBe(3);
      expect(result.errors).toHaveLength(1);
    });
  });
});