import ContactBackupService, { ContactData } from '../../src/services/ContactBackupService';
import AuthService from '../../src/services/AuthService';
import EncryptionService from '../../src/services/EncryptionService';
import CloudStorageService from '../../src/services/CloudStorageService';
import { PermissionsAndroid } from 'react-native';

// Mock dependencies
jest.mock('../../src/services/AuthService');
jest.mock('../../src/services/EncryptionService');
jest.mock('../../src/services/CloudStorageService');
jest.mock('react-native-contacts');
jest.mock('react-native', () => ({
  Platform: { OS: 'android' },
  PermissionsAndroid: {
    request: jest.fn(),
    PERMISSIONS: { READ_CONTACTS: 'android.permission.READ_CONTACTS' },
    RESULTS: { GRANTED: 'granted' }
  }
}));

const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;
const mockEncryptionService = EncryptionService as jest.Mocked<typeof EncryptionService>;
const mockCloudStorageService = CloudStorageService as jest.Mocked<typeof CloudStorageService>;
const mockPermissionsAndroid = PermissionsAndroid as jest.Mocked<typeof PermissionsAndroid>;

describe('ContactBackupService', () => {
  const mockContacts: ContactData[] = [
    {
      recordID: '1',
      displayName: 'John Doe',
      givenName: 'John',
      familyName: 'Doe',
      phoneNumbers: [{ label: 'mobile', number: '+1234567890' }],
      emailAddresses: [{ label: 'work', email: '<EMAIL>' }],
      postalAddresses: [],
      company: 'Test Corp',
      jobTitle: 'Developer',
      note: 'Test contact',
      hasThumbnail: false,
      isStarred: false,
      lastModified: Date.now()
    },
    {
      recordID: '2',
      displayName: 'Jane Smith',
      givenName: 'Jane',
      familyName: 'Smith',
      phoneNumbers: [{ label: 'mobile', number: '+0987654321' }],
      emailAddresses: [],
      postalAddresses: [],
      company: '',
      jobTitle: '',
      note: '',
      hasThumbnail: false,
      isStarred: true,
      lastModified: Date.now()
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockAuthService.getCurrentUser.mockReturnValue({ id: 'user123', email: '<EMAIL>' });
    mockPermissionsAndroid.request.mockResolvedValue('granted');
  });

  describe('requestContactsPermission', () => {
    it('should request Android contacts permission successfully', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('granted');
      
      const result = await ContactBackupService.requestContactsPermission();
      
      expect(result).toBe(true);
      expect(mockPermissionsAndroid.request).toHaveBeenCalledWith(
        'android.permission.READ_CONTACTS',
        expect.objectContaining({
          title: '📞 Contact Access Permission'
        })
      );
    });

    it('should handle permission denial', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');
      
      const result = await ContactBackupService.requestContactsPermission();
      
      expect(result).toBe(false);
    });

    it('should handle permission request errors', async () => {
      mockPermissionsAndroid.request.mockRejectedValue(new Error('Permission error'));
      
      const result = await ContactBackupService.requestContactsPermission();
      
      expect(result).toBe(false);
    });
  });

  describe('detectDuplicates', () => {
    it('should remove duplicate contacts based on phone numbers', () => {
      const contactsWithDuplicates = [
        ...mockContacts,
        {
          ...mockContacts[0],
          recordID: '3',
          displayName: 'John Duplicate'
        }
      ];

      const result = ContactBackupService.detectDuplicates(contactsWithDuplicates);
      
      expect(result).toHaveLength(2);
      expect(result.find(c => c.recordID === '1')).toBeDefined();
      expect(result.find(c => c.recordID === '2')).toBeDefined();
      expect(result.find(c => c.recordID === '3')).toBeUndefined();
    });

    it('should remove duplicate contacts based on email addresses', () => {
      const contactsWithDuplicates = [
        ...mockContacts,
        {
          ...mockContacts[0],
          recordID: '4',
          displayName: 'John Email Duplicate',
          phoneNumbers: []
        }
      ];

      const result = ContactBackupService.detectDuplicates(contactsWithDuplicates);
      
      expect(result).toHaveLength(2);
    });

    it('should handle contacts with no phone or email using name', () => {
      const contactsWithNameOnly = [
        {
          recordID: '5',
          displayName: 'Name Only Contact',
          givenName: 'Name',
          familyName: 'Only',
          phoneNumbers: [],
          emailAddresses: [],
          postalAddresses: [],
          company: '',
          jobTitle: '',
          note: '',
          hasThumbnail: false,
          isStarred: false,
          lastModified: Date.now()
        },
        {
          recordID: '6',
          displayName: 'Name Only Contact',
          givenName: 'Name',
          familyName: 'Only',
          phoneNumbers: [],
          emailAddresses: [],
          postalAddresses: [],
          company: '',
          jobTitle: '',
          note: '',
          hasThumbnail: false,
          isStarred: false,
          lastModified: Date.now()
        }
      ];

      const result = ContactBackupService.detectDuplicates(contactsWithNameOnly);
      
      expect(result).toHaveLength(1);
    });
  });

  describe('backupContacts', () => {
    it('should successfully backup contacts with encryption', async () => {
      mockEncryptionService.encrypt.mockResolvedValue({
        success: true,
        data: 'encrypted_data'
      });
      mockCloudStorageService.uploadFile.mockResolvedValue({
        success: true,
        fileId: 'file123'
      });

      const progressCallback = jest.fn();
      const result = await ContactBackupService.backupContacts(mockContacts, progressCallback);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2);
      expect(result.errors).toHaveLength(0);
      expect(mockEncryptionService.encrypt).toHaveBeenCalled();
      expect(mockCloudStorageService.uploadFile).toHaveBeenCalled();
      expect(progressCallback).toHaveBeenCalledTimes(6); // Multiple progress updates
    });

    it('should handle authentication failure', async () => {
      mockAuthService.getCurrentUser.mockReturnValue(null);

      const result = await ContactBackupService.backupContacts(mockContacts);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('authentication required');
    });

    it('should handle permission denial', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');

      const result = await ContactBackupService.backupContacts(mockContacts);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('permission denied');
    });

    it('should handle encryption failure', async () => {
      mockEncryptionService.encrypt.mockResolvedValue({
        success: false,
        error: 'Encryption failed'
      });

      const result = await ContactBackupService.backupContacts(mockContacts);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('encryption');
      expect(result.errors[0].message).toContain('Failed to encrypt');
    });

    it('should handle upload failure', async () => {
      mockEncryptionService.encrypt.mockResolvedValue({
        success: true,
        data: 'encrypted_data'
      });
      mockCloudStorageService.uploadFile.mockResolvedValue({
        success: false,
        error: 'Upload failed'
      });

      const result = await ContactBackupService.backupContacts(mockContacts);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('network');
      expect(result.errors[0].message).toContain('Failed to upload');
    });

    it('should validate contact data and filter invalid contacts', async () => {
      const invalidContacts = [
        ...mockContacts,
        {
          recordID: '999',
          displayName: '',
          givenName: '',
          familyName: '',
          phoneNumbers: [],
          emailAddresses: [],
          postalAddresses: [],
          company: '',
          jobTitle: '',
          note: '',
          hasThumbnail: false,
          isStarred: false,
          lastModified: Date.now()
        }
      ];

      mockEncryptionService.encrypt.mockResolvedValue({
        success: true,
        data: 'encrypted_data'
      });
      mockCloudStorageService.uploadFile.mockResolvedValue({
        success: true,
        fileId: 'file123'
      });

      const result = await ContactBackupService.backupContacts(invalidContacts);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2); // Only valid contacts
    });

    it('should handle general errors gracefully', async () => {
      mockEncryptionService.encrypt.mockRejectedValue(new Error('Unexpected error'));

      const result = await ContactBackupService.backupContacts(mockContacts);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
      expect(result.errors[0].message).toContain('Unexpected error');
    });

    it('should prevent concurrent backup operations', async () => {
      // Start first backup
      const firstBackup = ContactBackupService.backupContacts(mockContacts);
      
      // Try to start second backup while first is running
      await expect(ContactBackupService.backupContacts(mockContacts))
        .rejects.toThrow('Contact backup is already running');
      
      // Wait for first backup to complete
      await firstBackup;
    });
  });

  describe('validateContactData', () => {
    it('should filter out contacts with no name, phone, or email', () => {
      const mixedContacts = [
        ...mockContacts,
        {
          recordID: '999',
          displayName: '',
          givenName: '',
          familyName: '',
          phoneNumbers: [],
          emailAddresses: [],
          postalAddresses: [],
          company: '',
          jobTitle: '',
          note: '',
          hasThumbnail: false,
          isStarred: false,
          lastModified: Date.now()
        }
      ];

      // Access private method through service instance
      const service = ContactBackupService as any;
      const result = service.validateContactData(mixedContacts);

      expect(result).toHaveLength(2); // Only valid contacts
      expect(result.every((c: ContactData) => 
        c.displayName.trim().length > 0 || 
        c.phoneNumbers.length > 0 || 
        c.emailAddresses.length > 0
      )).toBe(true);
    });
  });

  describe('control functions', () => {
    it('should track running state correctly', () => {
      expect(ContactBackupService.isRunning()).toBe(false);
    });

    it('should handle pause and resume', () => {
      ContactBackupService.pauseBackup();
      ContactBackupService.resumeBackup();
      // These are void functions, just ensure they don't throw
      expect(true).toBe(true);
    });
  });
});