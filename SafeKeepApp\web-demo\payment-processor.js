/**
 * Payment Processor for SafeKeep Web Demo
 * Handles secure payment processing with Stripe Elements
 */

class PaymentProcessor {
    constructor(stripeManager, subscriptionManager) {
        this.stripeManager = stripeManager;
        this.subscriptionManager = subscriptionManager;
        this.stripe = null;
        this.elements = null;
        this.cardElement = null;
        this.paymentElement = null;
        this.currentPaymentIntent = null;
        this.billingAddress = {};
        this.taxCalculator = new TaxCalculator();
        
        this.setupEventListeners();
    }

    /**
     * Initialize payment processor
     */
    async initialize() {
        try {
            await this.stripeManager.initialize();
            this.stripe = this.stripeManager.stripe;
            this.elements = this.stripeManager.elements;
            
            this.setupPaymentElements();
            console.log('✅ Payment Processor initialized');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Payment Processor:', error);
            throw error;
        }
    }

    /**
     * Setup Stripe Elements
     */
    setupPaymentElements() {
        // Create modern Payment Element (recommended)
        this.paymentElement = this.elements.create('payment', {
            layout: {
                type: 'tabs',
                defaultCollapsed: false,
                radios: false,
                spacedAccordionItems: false
            },
            fields: {
                billingDetails: {
                    name: 'auto',
                    email: 'auto',
                    phone: 'auto',
                    address: {
                        country: 'auto',
                        line1: 'auto',
                        line2: 'auto',
                        city: 'auto',
                        state: 'auto',
                        postalCode: 'auto'
                    }
                }
            }
        });

        // Fallback to Card Element for compatibility
        this.cardElement = this.elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                    iconColor: '#666EE8',
                    fontWeight: '500',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    fontSmoothing: 'antialiased',
                },
                invalid: {
                    color: '#9e2146',
                    iconColor: '#fa755a'
                }
            },
            hidePostalCode: false
        });
    }

    /**
     * Mount payment elements to DOM
     */
    mountPaymentElements(containerId, useModernElements = true) {
        const container = document.getElementById(containerId);
        if (!container) {
            throw new Error(`Payment container ${containerId} not found`);
        }

        // Clear existing content
        container.innerHTML = '';

        if (useModernElements && this.paymentElement) {
            // Use modern Payment Element
            const paymentContainer = document.createElement('div');
            paymentContainer.id = 'payment-element';
            container.appendChild(paymentContainer);
            
            this.paymentElement.mount('#payment-element');
            
            this.paymentElement.on('change', (event) => {
                this.handlePaymentElementChange(event);
            });
        } else {
            // Use legacy Card Element
            const cardContainer = document.createElement('div');
            cardContainer.id = 'card-element';
            cardContainer.style.cssText = `
                padding: 12px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background: white;
                margin-bottom: 16px;
            `;
            container.appendChild(cardContainer);
            
            this.cardElement.mount('#card-element');
            
            this.cardElement.on('change', (event) => {
                this.handleCardElementChange(event);
            });
        }

        // Add error display
        const errorContainer = document.createElement('div');
        errorContainer.id = 'payment-errors';
        errorContainer.style.cssText = `
            color: #fa755a;
            font-size: 14px;
            margin-top: 8px;
            min-height: 20px;
        `;
        container.appendChild(errorContainer);
    }

    /**
     * Handle Payment Element changes
     */
    handlePaymentElementChange(event) {
        const errorContainer = document.getElementById('payment-errors');
        if (errorContainer) {
            if (event.error) {
                errorContainer.textContent = event.error.message;
            } else {
                errorContainer.textContent = '';
            }
        }

        // Update billing address if available
        if (event.value && event.value.billingDetails) {
            this.billingAddress = event.value.billingDetails;
        }

        // Trigger custom event
        this.dispatchPaymentEvent('payment-change', {
            complete: event.complete,
            error: event.error,
            billingDetails: this.billingAddress
        });
    }

    /**
     * Handle Card Element changes
     */
    handleCardElementChange(event) {
        const errorContainer = document.getElementById('payment-errors');
        if (errorContainer) {
            if (event.error) {
                errorContainer.textContent = event.error.message;
            } else {
                errorContainer.textContent = '';
            }
        }

        // Trigger custom event
        this.dispatchPaymentEvent('payment-change', {
            complete: event.complete,
            error: event.error
        });
    }

    /**
     * Create payment intent for subscription
     */
    async createPaymentIntent(subscriptionData) {
        try {
            const { tierId, billingAddress, taxInfo } = subscriptionData;
            const tier = this.stripeManager.getSubscriptionTier(tierId);
            
            if (!tier) {
                throw new Error(`Invalid subscription tier: ${tierId}`);
            }

            // Calculate tax if applicable
            let taxAmount = 0;
            if (billingAddress && billingAddress.country) {
                taxAmount = await this.taxCalculator.calculateTax(tier.price, billingAddress);
            }

            const totalAmount = tier.price + taxAmount;

            // Create payment intent
            const paymentIntent = await this.stripeManager.createPaymentIntent({
                amount: totalAmount,
                currency: tier.currency || 'usd',
                description: `SafeKeep ${tier.name} Subscription`,
                metadata: {
                    userId: this.subscriptionManager.currentUser,
                    tierId: tierId,
                    taxAmount: taxAmount.toString(),
                    billingCountry: billingAddress?.country || 'US'
                },
                automatic_payment_methods: {
                    enabled: true
                }
            });

            this.currentPaymentIntent = paymentIntent;
            
            return {
                ...paymentIntent,
                taxAmount,
                totalAmount,
                tier
            };
        } catch (error) {
            console.error('❌ Failed to create payment intent:', error);
            throw error;
        }
    }

    /**
     * Process payment with modern Payment Element
     */
    async processPaymentWithPaymentElement(paymentIntentData) {
        try {
            const { error, paymentIntent } = await this.stripe.confirmPayment({
                elements: this.elements,
                confirmParams: {
                    return_url: `${window.location.origin}/payment-success`,
                    payment_method_data: {
                        billing_details: this.billingAddress
                    }
                },
                redirect: 'if_required'
            });

            if (error) {
                throw new Error(error.message);
            }

            return {
                success: true,
                paymentIntent,
                requiresAction: paymentIntent.status === 'requires_action'
            };
        } catch (error) {
            console.error('❌ Payment processing failed:', error);
            throw error;
        }
    }

    /**
     * Process payment with legacy Card Element
     */
    async processPaymentWithCardElement(paymentIntentData) {
        try {
            const { error, paymentIntent } = await this.stripe.confirmCardPayment(
                paymentIntentData.client_secret,
                {
                    payment_method: {
                        card: this.cardElement,
                        billing_details: this.billingAddress
                    }
                }
            );

            if (error) {
                throw new Error(error.message);
            }

            return {
                success: true,
                paymentIntent,
                requiresAction: paymentIntent.status === 'requires_action'
            };
        } catch (error) {
            console.error('❌ Payment processing failed:', error);
            throw error;
        }
    }

    /**
     * Process subscription payment
     */
    async processSubscriptionPayment(tierId, billingAddress = {}) {
        try {
            this.dispatchPaymentEvent('payment-start', { tierId });

            // Create payment intent
            const paymentIntentData = await this.createPaymentIntent({
                tierId,
                billingAddress
            });

            // Process payment based on available elements
            let result;
            if (this.paymentElement) {
                result = await this.processPaymentWithPaymentElement(paymentIntentData);
            } else {
                result = await this.processPaymentWithCardElement(paymentIntentData);
            }

            if (result.success && !result.requiresAction) {
                // Payment succeeded, update subscription
                await this.subscriptionManager.upgradeSubscription(tierId);
                
                this.dispatchPaymentEvent('payment-success', {
                    tierId,
                    paymentIntent: result.paymentIntent,
                    amount: paymentIntentData.totalAmount
                });

                return {
                    success: true,
                    message: `Successfully upgraded to ${paymentIntentData.tier.name} plan!`,
                    paymentIntent: result.paymentIntent
                };
            } else if (result.requiresAction) {
                this.dispatchPaymentEvent('payment-requires-action', {
                    paymentIntent: result.paymentIntent
                });

                return {
                    success: false,
                    requiresAction: true,
                    message: 'Payment requires additional authentication',
                    paymentIntent: result.paymentIntent
                };
            }
        } catch (error) {
            this.dispatchPaymentEvent('payment-error', {
                error: error.message,
                tierId
            });

            return {
                success: false,
                error: error.message,
                message: `Payment failed: ${error.message}`
            };
        }
    }

    /**
     * Setup billing address collection
     */
    createBillingAddressForm(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="billing-address-form">
                <h4>Billing Address</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="billing-name">Full Name *</label>
                        <input type="text" id="billing-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="billing-email">Email *</label>
                        <input type="email" id="billing-email" name="email" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="billing-address-line1">Address Line 1 *</label>
                    <input type="text" id="billing-address-line1" name="line1" required>
                </div>
                <div class="form-group">
                    <label for="billing-address-line2">Address Line 2</label>
                    <input type="text" id="billing-address-line2" name="line2">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="billing-city">City *</label>
                        <input type="text" id="billing-city" name="city" required>
                    </div>
                    <div class="form-group">
                        <label for="billing-state">State/Province</label>
                        <input type="text" id="billing-state" name="state">
                    </div>
                    <div class="form-group">
                        <label for="billing-postal-code">Postal Code *</label>
                        <input type="text" id="billing-postal-code" name="postal_code" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="billing-country">Country *</label>
                    <select id="billing-country" name="country" required>
                        <option value="">Select Country</option>
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="GB">United Kingdom</option>
                        <option value="AU">Australia</option>
                        <option value="DE">Germany</option>
                        <option value="FR">France</option>
                        <option value="JP">Japan</option>
                        <!-- Add more countries as needed -->
                    </select>
                </div>
            </div>
        `;

        // Add event listeners for address validation
        this.setupBillingAddressValidation();
    }

    /**
     * Setup billing address validation
     */
    setupBillingAddressValidation() {
        const form = document.querySelector('.billing-address-form');
        if (!form) return;

        const inputs = form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.validateBillingAddress();
            });
        });
    }

    /**
     * Validate billing address
     */
    validateBillingAddress() {
        const form = document.querySelector('.billing-address-form');
        if (!form) return false;

        const formData = new FormData(form);
        const address = Object.fromEntries(formData.entries());

        // Basic validation
        const required = ['name', 'email', 'line1', 'city', 'postal_code', 'country'];
        const missing = required.filter(field => !address[field] || address[field].trim() === '');

        if (missing.length > 0) {
            this.showValidationError(`Please fill in required fields: ${missing.join(', ')}`);
            return false;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(address.email)) {
            this.showValidationError('Please enter a valid email address');
            return false;
        }

        // Store valid address
        this.billingAddress = address;
        this.clearValidationError();
        
        // Trigger tax calculation
        this.calculateAndDisplayTax();
        
        return true;
    }

    /**
     * Calculate and display tax
     */
    async calculateAndDisplayTax() {
        if (!this.billingAddress.country) return;

        try {
            const currentTier = this.getCurrentSelectedTier();
            if (!currentTier) return;

            const taxAmount = await this.taxCalculator.calculateTax(
                currentTier.price, 
                this.billingAddress
            );

            this.displayTaxInformation(currentTier.price, taxAmount);
        } catch (error) {
            console.error('Tax calculation failed:', error);
        }
    }

    /**
     * Display tax information
     */
    displayTaxInformation(subtotal, taxAmount) {
        let taxDisplay = document.getElementById('tax-display');
        if (!taxDisplay) {
            taxDisplay = document.createElement('div');
            taxDisplay.id = 'tax-display';
            taxDisplay.className = 'tax-information';
            
            const billingForm = document.querySelector('.billing-address-form');
            if (billingForm) {
                billingForm.appendChild(taxDisplay);
            }
        }

        const total = subtotal + taxAmount;
        
        taxDisplay.innerHTML = `
            <div class="tax-breakdown">
                <div class="tax-line">
                    <span>Subtotal:</span>
                    <span>$${(subtotal / 100).toFixed(2)}</span>
                </div>
                ${taxAmount > 0 ? `
                    <div class="tax-line">
                        <span>Tax:</span>
                        <span>$${(taxAmount / 100).toFixed(2)}</span>
                    </div>
                ` : ''}
                <div class="tax-line total">
                    <span><strong>Total:</strong></span>
                    <span><strong>$${(total / 100).toFixed(2)}</strong></span>
                </div>
            </div>
        `;
    }

    /**
     * Get currently selected tier
     */
    getCurrentSelectedTier() {
        // This would be implemented based on your UI structure
        // For now, return null as placeholder
        return null;
    }

    /**
     * Show validation error
     */
    showValidationError(message) {
        let errorDiv = document.getElementById('billing-validation-error');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.id = 'billing-validation-error';
            errorDiv.className = 'validation-error';
            
            const form = document.querySelector('.billing-address-form');
            if (form) {
                form.insertBefore(errorDiv, form.firstChild);
            }
        }
        
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    /**
     * Clear validation error
     */
    clearValidationError() {
        const errorDiv = document.getElementById('billing-validation-error');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for subscription manager events
        document.addEventListener('subscription-change', (event) => {
            this.handleSubscriptionChange(event.detail);
        });
    }

    /**
     * Handle subscription changes
     */
    handleSubscriptionChange(data) {
        // Update payment processor state based on subscription changes
        console.log('Subscription changed:', data);
    }

    /**
     * Dispatch payment events
     */
    dispatchPaymentEvent(eventType, data) {
        const event = new CustomEvent(eventType, {
            detail: data,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    /**
     * Get payment methods for customer
     */
    async getPaymentMethods(customerId) {
        try {
            return await this.stripeManager.getCustomerPaymentMethods(customerId);
        } catch (error) {
            console.error('Failed to get payment methods:', error);
            return [];
        }
    }

    /**
     * Save payment method for future use
     */
    async savePaymentMethod(paymentMethodId, customerId) {
        try {
            return await this.stripeManager.addPaymentMethod(customerId, {
                id: paymentMethodId
            });
        } catch (error) {
            console.error('Failed to save payment method:', error);
            throw error;
        }
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.paymentElement) {
            this.paymentElement.destroy();
        }
        if (this.cardElement) {
            this.cardElement.destroy();
        }
        console.log('🧹 Payment Processor cleaned up');
    }
}

/**
 * Tax Calculator for payment processing
 */
class TaxCalculator {
    constructor() {
        this.taxRates = {
            'US': {
                'CA': 0.0725, // California
                'NY': 0.08,   // New York
                'TX': 0.0625, // Texas
                'FL': 0.06,   // Florida
                'default': 0.05
            },
            'CA': 0.13,   // Canada GST/HST
            'GB': 0.20,   // UK VAT
            'DE': 0.19,   // Germany VAT
            'FR': 0.20,   // France VAT
            'AU': 0.10,   // Australia GST
            'default': 0
        };
    }

    /**
     * Calculate tax for given amount and address
     */
    async calculateTax(amount, billingAddress) {
        try {
            const { country, state } = billingAddress;
            
            if (!country) return 0;
            
            let taxRate = 0;
            
            if (country === 'US' && state) {
                const usRates = this.taxRates.US;
                taxRate = usRates[state] || usRates.default;
            } else {
                taxRate = this.taxRates[country] || this.taxRates.default;
            }
            
            return Math.round(amount * taxRate);
        } catch (error) {
            console.error('Tax calculation error:', error);
            return 0;
        }
    }

    /**
     * Get tax information for display
     */
    getTaxInfo(country, state = null) {
        if (country === 'US' && state) {
            const rate = this.taxRates.US[state] || this.taxRates.US.default;
            return {
                rate: rate,
                description: `${state} Sales Tax`,
                percentage: `${(rate * 100).toFixed(2)}%`
            };
        } else if (this.taxRates[country]) {
            const rate = this.taxRates[country];
            const descriptions = {
                'CA': 'GST/HST',
                'GB': 'VAT',
                'DE': 'VAT',
                'FR': 'VAT',
                'AU': 'GST'
            };
            
            return {
                rate: rate,
                description: descriptions[country] || 'Tax',
                percentage: `${(rate * 100).toFixed(2)}%`
            };
        }
        
        return {
            rate: 0,
            description: 'No tax applicable',
            percentage: '0%'
        };
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.PaymentProcessor = PaymentProcessor;
    window.TaxCalculator = TaxCalculator;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PaymentProcessor, TaxCalculator };
}