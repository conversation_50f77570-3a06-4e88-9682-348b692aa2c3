/**
 * Performance Benchmark Tests
 * Tests performance metrics and benchmarks for the web demo
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class PerformanceBenchmarkTests {
    constructor() {
        this.browser = null;
        this.testResults = [];
        this.baseUrl = 'http://localhost:3000';
        this.startTime = Date.now();
        this.performanceThresholds = {
            pageLoadTime: 3000, // 3 seconds
            firstContentfulPaint: 1500, // 1.5 seconds
            largestContentfulPaint: 2500, // 2.5 seconds
            cumulativeLayoutShift: 0.1,
            firstInputDelay: 100, // 100ms
            memoryUsage: 100 * 1024 * 1024, // 100MB
            backupProgressUpdate: 100, // 100ms
            encryptionSpeed: 1000, // 1 second for 1MB
            restoreSpeed: 2000, // 2 seconds for restore
            chartRenderTime: 500 // 500ms for chart rendering
        };
    }

    async initialize() {
        console.log('⚡ Initializing Performance Benchmark Tests...');
        
        this.browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox', 
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-gpu'
            ]
        });

        // Ensure test reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
    }

    async runAllTests() {
        await this.initialize();

        const testSuites = [
            this.testPageLoadPerformance,
            this.testRealTimeProgressPerformance,
            this.testEncryptionPerformance,
            this.testRestorePerformance,
            this.testChartRenderingPerformance,
            this.testMemoryUsagePerformance,
            this.testNetworkPerformance,
            this.testConcurrentOperationsPerformance,
            this.testMobilePerformance,
            this.testStressTestPerformance
        ];

        console.log('⚡ Running Performance Benchmark Tests...');
        console.log('========================================');

        for (const testSuite of testSuites) {
            await this.runTestSuite(testSuite);
        }

        await this.cleanup();
        this.generateReport();
    }

    async runTestSuite(testFunction) {
        const testName = testFunction.name;
        console.log(`\n🧪 Running ${testName}...`);

        const startTime = Date.now();
        let page = null;

        try {
            page = await this.browser.newPage();
            await page.setViewport({ width: 1280, height: 720 });
            
            const result = await testFunction.call(this, page);
            const duration = Date.now() - startTime;

            this.testResults.push({
                test: testName,
                status: result.success ? 'PASSED' : 'FAILED',
                duration,
                details: result.details,
                metrics: result.metrics || {},
                errors: result.errors || []
            });

            console.log(`${result.success ? '✅' : '❌'} ${testName} - ${duration}ms`);

        } catch (error) {
            const duration = Date.now() - startTime;
            console.error(`❌ ${testName} failed:`, error.message);
            
            this.testResults.push({
                test: testName,
                status: 'ERROR',
                duration,
                details: 'Test execution failed',
                metrics: {},
                errors: [error.message]
            });
        } finally {
            if (page) {
                await page.close();
            }
        }
    }

    async testPageLoadPerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            // Enable performance monitoring
            await page.setCacheEnabled(false);
            
            const startTime = Date.now();
            
            // Navigate and measure load time
            await page.goto(this.baseUrl, { waitUntil: 'networkidle0' });
            const loadTime = Date.now() - startTime;
            
            metrics.pageLoadTime = loadTime;
            steps.push(`✅ Page loaded in ${loadTime}ms`);

            // Get Web Vitals
            const webVitals = await page.evaluate(() => {
                return new Promise((resolve) => {
                    const vitals = {};
                    
                    // First Contentful Paint
                    new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach((entry) => {
                            if (entry.name === 'first-contentful-paint') {
                                vitals.firstContentfulPaint = entry.startTime;
                            }
                        });
                    }).observe({ entryTypes: ['paint'] });

                    // Largest Contentful Paint
                    new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        const lastEntry = entries[entries.length - 1];
                        vitals.largestContentfulPaint = lastEntry.startTime;
                    }).observe({ entryTypes: ['largest-contentful-paint'] });

                    // Cumulative Layout Shift
                    let clsValue = 0;
                    new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            if (!entry.hadRecentInput) {
                                clsValue += entry.value;
                            }
                        }
                        vitals.cumulativeLayoutShift = clsValue;
                    }).observe({ entryTypes: ['layout-shift'] });

                    setTimeout(() => resolve(vitals), 2000);
                });
            });

            Object.assign(metrics, webVitals);
            
            // Check thresholds
            const loadPassed = loadTime <= this.performanceThresholds.pageLoadTime;
            const fcpPassed = !webVitals.firstContentfulPaint || 
                             webVitals.firstContentfulPaint <= this.performanceThresholds.firstContentfulPaint;
            const lcpPassed = !webVitals.largestContentfulPaint || 
                             webVitals.largestContentfulPaint <= this.performanceThresholds.largestContentfulPaint;
            const clsPassed = !webVitals.cumulativeLayoutShift || 
                             webVitals.cumulativeLayoutShift <= this.performanceThresholds.cumulativeLayoutShift;

            steps.push(`${loadPassed ? '✅' : '❌'} Page load time: ${loadTime}ms (threshold: ${this.performanceThresholds.pageLoadTime}ms)`);
            
            if (webVitals.firstContentfulPaint) {
                steps.push(`${fcpPassed ? '✅' : '❌'} First Contentful Paint: ${webVitals.firstContentfulPaint.toFixed(2)}ms`);
            }
            
            if (webVitals.largestContentfulPaint) {
                steps.push(`${lcpPassed ? '✅' : '❌'} Largest Contentful Paint: ${webVitals.largestContentfulPaint.toFixed(2)}ms`);
            }
            
            if (webVitals.cumulativeLayoutShift !== undefined) {
                steps.push(`${clsPassed ? '✅' : '❌'} Cumulative Layout Shift: ${webVitals.cumulativeLayoutShift.toFixed(3)}`);
            }

            const allPassed = loadPassed && fcpPassed && lcpPassed && clsPassed;

            return {
                success: allPassed,
                details: steps.join('\n'),
                metrics
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testRealTimeProgressPerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Start backup and measure progress update frequency
            const progressUpdates = [];
            
            await page.evaluateOnNewDocument(() => {
                window.progressUpdateTimes = [];
                const originalUpdate = window.updateProgress || function() {};
                window.updateProgress = function(...args) {
                    window.progressUpdateTimes.push(Date.now());
                    return originalUpdate.apply(this, args);
                };
            });

            const startTime = Date.now();
            await page.click('#start-backup-btn');
            
            // Monitor progress updates for 10 seconds
            await page.waitForTimeout(10000);
            
            const updateTimes = await page.evaluate(() => window.progressUpdateTimes || []);
            
            if (updateTimes.length > 0) {
                const intervals = [];
                for (let i = 1; i < updateTimes.length; i++) {
                    intervals.push(updateTimes[i] - updateTimes[i-1]);
                }
                
                const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
                const maxInterval = Math.max(...intervals);
                const minInterval = Math.min(...intervals);
                
                metrics.averageUpdateInterval = avgInterval;
                metrics.maxUpdateInterval = maxInterval;
                metrics.minUpdateInterval = minInterval;
                metrics.totalUpdates = updateTimes.length;
                
                steps.push(`✅ Progress updates: ${updateTimes.length} in 10 seconds`);
                steps.push(`✅ Average interval: ${avgInterval.toFixed(2)}ms`);
                steps.push(`✅ Max interval: ${maxInterval}ms`);
                steps.push(`✅ Min interval: ${minInterval}ms`);
                
                const performancePassed = avgInterval <= this.performanceThresholds.backupProgressUpdate;
                steps.push(`${performancePassed ? '✅' : '❌'} Performance threshold: ${performancePassed ? 'PASSED' : 'FAILED'}`);
                
                return {
                    success: performancePassed,
                    details: steps.join('\n'),
                    metrics
                };
            } else {
                throw new Error('No progress updates detected');
            }

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testEncryptionPerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to encryption demo
            await page.click('#encryption-demo-tab');
            await page.waitForSelector('#encryption-demo-container', { timeout: 3000 });

            // Measure encryption performance for different data sizes
            const dataSizes = [1024, 10240, 102400, 1048576]; // 1KB, 10KB, 100KB, 1MB
            
            for (const size of dataSizes) {
                const startTime = Date.now();
                
                // Set data size and start encryption
                await page.evaluate((dataSize) => {
                    if (window.setEncryptionDataSize) {
                        window.setEncryptionDataSize(dataSize);
                    }
                }, size);
                
                await page.click('#start-encryption-btn');
                await page.waitForSelector('#encryption-completed', { timeout: 10000 });
                
                const encryptionTime = Date.now() - startTime;
                const throughput = (size / encryptionTime) * 1000; // bytes per second
                
                metrics[`encryption_${size}_bytes`] = {
                    time: encryptionTime,
                    throughput: throughput
                };
                
                steps.push(`✅ ${size} bytes encrypted in ${encryptionTime}ms (${(throughput / 1024).toFixed(2)} KB/s)`);
                
                // Reset for next test
                await page.click('#reset-encryption-btn');
                await page.waitForTimeout(500);
            }

            // Check if 1MB encryption meets threshold
            const oneMBTime = metrics['encryption_1048576_bytes']?.time || Infinity;
            const performancePassed = oneMBTime <= this.performanceThresholds.encryptionSpeed;
            
            steps.push(`${performancePassed ? '✅' : '❌'} 1MB encryption performance: ${performancePassed ? 'PASSED' : 'FAILED'}`);

            return {
                success: performancePassed,
                details: steps.join('\n'),
                metrics
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testRestorePerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to restore simulation
            await page.click('#restore-simulation-tab');
            await page.waitForSelector('#restore-container', { timeout: 3000 });

            // Test restore performance for different backup sizes
            const backupSizes = ['small', 'medium', 'large'];
            
            for (const size of backupSizes) {
                await page.select('#backup-size-select', size);
                
                const startTime = Date.now();
                await page.click('#start-restore-btn');
                await page.waitForSelector('#restore-completed', { timeout: 15000 });
                const restoreTime = Date.now() - startTime;
                
                metrics[`restore_${size}`] = restoreTime;
                steps.push(`✅ ${size} backup restored in ${restoreTime}ms`);
                
                // Reset for next test
                await page.click('#reset-restore-btn');
                await page.waitForTimeout(500);
            }

            // Check performance threshold (using medium backup as benchmark)
            const mediumRestoreTime = metrics['restore_medium'] || Infinity;
            const performancePassed = mediumRestoreTime <= this.performanceThresholds.restoreSpeed;
            
            steps.push(`${performancePassed ? '✅' : '❌'} Restore performance: ${performancePassed ? 'PASSED' : 'FAILED'}`);

            return {
                success: performancePassed,
                details: steps.join('\n'),
                metrics
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testChartRenderingPerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Navigate to analytics dashboard
            await page.click('#analytics-tab');
            await page.waitForSelector('#analytics-dashboard', { timeout: 3000 });

            // Measure chart rendering times
            const chartTypes = ['backup-success-chart', 'storage-usage-chart', 'performance-chart'];
            
            for (const chartId of chartTypes) {
                const startTime = Date.now();
                
                // Trigger chart rendering
                await page.click(`#render-${chartId}`);
                await page.waitForSelector(`#${chartId} canvas`, { timeout: 5000 });
                
                const renderTime = Date.now() - startTime;
                metrics[chartId] = renderTime;
                
                steps.push(`✅ ${chartId} rendered in ${renderTime}ms`);
                
                const performancePassed = renderTime <= this.performanceThresholds.chartRenderTime;
                steps.push(`${performancePassed ? '✅' : '❌'} Chart render performance: ${performancePassed ? 'PASSED' : 'FAILED'}`);
            }

            // Test chart interaction performance
            const interactionStartTime = Date.now();
            await page.hover('#backup-success-chart canvas');
            await page.waitForSelector('#chart-tooltip', { timeout: 2000 });
            const interactionTime = Date.now() - interactionStartTime;
            
            metrics.chartInteractionTime = interactionTime;
            steps.push(`✅ Chart interaction response: ${interactionTime}ms`);

            const allChartsPassed = Object.values(metrics).every(time => 
                time <= this.performanceThresholds.chartRenderTime
            );

            return {
                success: allChartsPassed,
                details: steps.join('\n'),
                metrics
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testMemoryUsagePerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Get initial memory usage
            const initialMemory = await page.evaluate(() => {
                if (performance.memory) {
                    return {
                        used: performance.memory.usedJSHeapSize,
                        total: performance.memory.totalJSHeapSize,
                        limit: performance.memory.jsHeapSizeLimit
                    };
                }
                return null;
            });

            if (initialMemory) {
                metrics.initialMemoryUsage = initialMemory.used;
                steps.push(`✅ Initial memory usage: ${(initialMemory.used / 1024 / 1024).toFixed(2)} MB`);
            }

            // Perform memory-intensive operations
            await page.click('#start-backup-btn');
            await page.waitForTimeout(5000);
            
            await page.click('#encryption-demo-tab');
            await page.click('#start-encryption-btn');
            await page.waitForTimeout(3000);
            
            await page.click('#analytics-tab');
            await page.waitForTimeout(2000);

            // Get memory usage after operations
            const finalMemory = await page.evaluate(() => {
                if (performance.memory) {
                    return {
                        used: performance.memory.usedJSHeapSize,
                        total: performance.memory.totalJSHeapSize,
                        limit: performance.memory.jsHeapSizeLimit
                    };
                }
                return null;
            });

            if (finalMemory && initialMemory) {
                const memoryIncrease = finalMemory.used - initialMemory.used;
                metrics.finalMemoryUsage = finalMemory.used;
                metrics.memoryIncrease = memoryIncrease;
                
                steps.push(`✅ Final memory usage: ${(finalMemory.used / 1024 / 1024).toFixed(2)} MB`);
                steps.push(`✅ Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB`);
                
                const memoryPassed = finalMemory.used <= this.performanceThresholds.memoryUsage;
                steps.push(`${memoryPassed ? '✅' : '❌'} Memory usage threshold: ${memoryPassed ? 'PASSED' : 'FAILED'}`);
                
                return {
                    success: memoryPassed,
                    details: steps.join('\n'),
                    metrics
                };
            } else {
                steps.push('⚠️ Memory API not available in this browser');
                return {
                    success: true,
                    details: steps.join('\n'),
                    metrics
                };
            }

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testNetworkPerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            // Enable network monitoring
            await page.setRequestInterception(true);
            
            const networkRequests = [];
            page.on('request', request => {
                networkRequests.push({
                    url: request.url(),
                    method: request.method(),
                    startTime: Date.now()
                });
                request.continue();
            });

            page.on('response', response => {
                const request = networkRequests.find(req => req.url === response.url());
                if (request) {
                    request.endTime = Date.now();
                    request.duration = request.endTime - request.startTime;
                    request.status = response.status();
                    request.size = response.headers()['content-length'] || 0;
                }
            });

            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Perform various network-intensive operations
            await page.click('#start-backup-btn');
            await page.waitForTimeout(3000);
            
            await page.click('#analytics-tab');
            await page.waitForTimeout(2000);

            // Analyze network performance
            const completedRequests = networkRequests.filter(req => req.endTime);
            
            if (completedRequests.length > 0) {
                const totalRequests = completedRequests.length;
                const avgResponseTime = completedRequests.reduce((sum, req) => sum + req.duration, 0) / totalRequests;
                const maxResponseTime = Math.max(...completedRequests.map(req => req.duration));
                const failedRequests = completedRequests.filter(req => req.status >= 400).length;
                
                metrics.totalNetworkRequests = totalRequests;
                metrics.averageResponseTime = avgResponseTime;
                metrics.maxResponseTime = maxResponseTime;
                metrics.failedRequests = failedRequests;
                metrics.successRate = ((totalRequests - failedRequests) / totalRequests) * 100;
                
                steps.push(`✅ Total network requests: ${totalRequests}`);
                steps.push(`✅ Average response time: ${avgResponseTime.toFixed(2)}ms`);
                steps.push(`✅ Max response time: ${maxResponseTime}ms`);
                steps.push(`✅ Failed requests: ${failedRequests}`);
                steps.push(`✅ Success rate: ${metrics.successRate.toFixed(1)}%`);
                
                const performancePassed = avgResponseTime <= 1000 && metrics.successRate >= 95;
                steps.push(`${performancePassed ? '✅' : '❌'} Network performance: ${performancePassed ? 'PASSED' : 'FAILED'}`);
                
                return {
                    success: performancePassed,
                    details: steps.join('\n'),
                    metrics
                };
            } else {
                throw new Error('No network requests captured');
            }

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testConcurrentOperationsPerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Test concurrent backup and encryption
            const startTime = Date.now();
            
            // Start multiple operations simultaneously
            await Promise.all([
                page.click('#start-backup-btn'),
                page.evaluate(() => {
                    // Simulate clicking encryption demo in another tab
                    const encryptionTab = document.querySelector('#encryption-demo-tab');
                    if (encryptionTab) encryptionTab.click();
                }),
                page.evaluate(() => {
                    // Simulate starting analytics
                    setTimeout(() => {
                        const analyticsTab = document.querySelector('#analytics-tab');
                        if (analyticsTab) analyticsTab.click();
                    }, 100);
                })
            ]);

            // Wait for operations to complete
            await page.waitForTimeout(10000);
            
            const totalTime = Date.now() - startTime;
            metrics.concurrentOperationTime = totalTime;
            
            steps.push(`✅ Concurrent operations completed in ${totalTime}ms`);

            // Check if UI remains responsive
            const responsiveTest = await page.evaluate(() => {
                const button = document.querySelector('#start-backup-btn');
                if (button) {
                    const startTime = Date.now();
                    button.click();
                    return Date.now() - startTime;
                }
                return 0;
            });

            metrics.uiResponsiveness = responsiveTest;
            steps.push(`✅ UI responsiveness: ${responsiveTest}ms`);
            
            const performancePassed = responsiveTest <= 200 && totalTime <= 15000;
            steps.push(`${performancePassed ? '✅' : '❌'} Concurrent operations performance: ${performancePassed ? 'PASSED' : 'FAILED'}`);

            return {
                success: performancePassed,
                details: steps.join('\n'),
                metrics
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testMobilePerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            // Set mobile viewport
            await page.setViewport({ width: 375, height: 667 });
            
            const startTime = Date.now();
            await page.goto(this.baseUrl, { waitUntil: 'networkidle0' });
            const mobileLoadTime = Date.now() - startTime;
            
            metrics.mobileLoadTime = mobileLoadTime;
            steps.push(`✅ Mobile page load: ${mobileLoadTime}ms`);

            // Test mobile interactions
            const touchStartTime = Date.now();
            await page.tap('#start-backup-btn');
            await page.waitForSelector('#backup-progress-container', { timeout: 5000 });
            const touchResponseTime = Date.now() - touchStartTime;
            
            metrics.touchResponseTime = touchResponseTime;
            steps.push(`✅ Touch response time: ${touchResponseTime}ms`);

            // Test mobile scrolling performance
            const scrollStartTime = Date.now();
            await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            await page.waitForTimeout(500);
            const scrollTime = Date.now() - scrollStartTime;
            
            metrics.scrollPerformance = scrollTime;
            steps.push(`✅ Scroll performance: ${scrollTime}ms`);

            const mobilePerformancePassed = mobileLoadTime <= 4000 && touchResponseTime <= 300;
            steps.push(`${mobilePerformancePassed ? '✅' : '❌'} Mobile performance: ${mobilePerformancePassed ? 'PASSED' : 'FAILED'}`);

            return {
                success: mobilePerformancePassed,
                details: steps.join('\n'),
                metrics
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async testStressTestPerformance(page) {
        const steps = [];
        const metrics = {};

        try {
            await page.goto(this.baseUrl);
            await page.waitForSelector('#demo-container', { timeout: 5000 });

            // Stress test: Rapid operations
            const operationCount = 50;
            const startTime = Date.now();
            
            for (let i = 0; i < operationCount; i++) {
                await page.click('#start-backup-btn');
                await page.waitForTimeout(50);
                await page.click('#stop-backup-btn');
                await page.waitForTimeout(50);
            }
            
            const stressTestTime = Date.now() - startTime;
            metrics.stressTestTime = stressTestTime;
            metrics.operationsPerSecond = (operationCount / stressTestTime) * 1000;
            
            steps.push(`✅ Stress test: ${operationCount} operations in ${stressTestTime}ms`);
            steps.push(`✅ Operations per second: ${metrics.operationsPerSecond.toFixed(2)}`);

            // Check memory after stress test
            const memoryAfterStress = await page.evaluate(() => {
                if (performance.memory) {
                    return performance.memory.usedJSHeapSize;
                }
                return null;
            });

            if (memoryAfterStress) {
                metrics.memoryAfterStress = memoryAfterStress;
                steps.push(`✅ Memory after stress test: ${(memoryAfterStress / 1024 / 1024).toFixed(2)} MB`);
            }

            // Test UI responsiveness after stress
            const responsivenessTest = await page.evaluate(() => {
                const startTime = Date.now();
                const button = document.querySelector('#analytics-tab');
                if (button) button.click();
                return Date.now() - startTime;
            });

            metrics.responsivenessAfterStress = responsivenessTest;
            steps.push(`✅ UI responsiveness after stress: ${responsivenessTest}ms`);

            const stressPassed = responsivenessTest <= 500 && metrics.operationsPerSecond >= 10;
            steps.push(`${stressPassed ? '✅' : '❌'} Stress test performance: ${stressPassed ? 'PASSED' : 'FAILED'}`);

            return {
                success: stressPassed,
                details: steps.join('\n'),
                metrics
            };

        } catch (error) {
            return {
                success: false,
                details: steps.join('\n'),
                metrics,
                errors: [error.message]
            };
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    generateReport() {
        const totalDuration = Date.now() - this.startTime;
        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const failed = this.testResults.filter(r => r.status === 'FAILED').length;
        const errors = this.testResults.filter(r => r.status === 'ERROR').length;
        const total = this.testResults.length;

        console.log('\n⚡ Performance Benchmark Test Results');
        console.log('====================================');
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed} ✅`);
        console.log(`Failed: ${failed} ❌`);
        console.log(`Errors: ${errors} 💥`);
        console.log(`Total Duration: ${totalDuration}ms`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        // Performance summary
        console.log('\n📊 Performance Summary:');
        this.testResults.forEach(result => {
            if (result.metrics && Object.keys(result.metrics).length > 0) {
                console.log(`\n${result.test}:`);
                Object.entries(result.metrics).forEach(([key, value]) => {
                    if (typeof value === 'number') {
                        console.log(`  ${key}: ${value.toFixed(2)}${key.includes('Time') || key.includes('Interval') ? 'ms' : ''}`);
                    } else {
                        console.log(`  ${key}: ${JSON.stringify(value)}`);
                    }
                });
            }
        });

        // Generate detailed report
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total,
                passed,
                failed,
                errors,
                successRate: ((passed / total) * 100).toFixed(1),
                totalDuration
            },
            thresholds: this.performanceThresholds,
            results: this.testResults
        };

        // Ensure reports directory exists
        const reportsDir = path.join(__dirname, '../test-reports');
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        fs.writeFileSync(
            path.join(reportsDir, 'performance-benchmark-test-report.json'),
            JSON.stringify(report, null, 2)
        );

        console.log('\n📄 Report saved to test-reports/performance-benchmark-test-report.json');
    }
}

// Run tests if called directly
if (require.main === module) {
    const runner = new PerformanceBenchmarkTests();
    runner.runAllTests().catch(console.error);
}

module.exports = PerformanceBenchmarkTests;