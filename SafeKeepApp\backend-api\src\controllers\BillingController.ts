import { Request, Response } from 'express';
import { ApiResponse, BillingMetadata, PaymentResult, WebhookResult } from '../types/modular-pricing';
import { BillingIntegration } from '../services/BillingIntegration';

export class BillingController {
  private billingIntegration: BillingIntegration;

  constructor() {
    this.billingIntegration = new BillingIntegration();
  }
  /**
   * POST /api/billing/payment-intent
   * Body: { amount: number, serviceIds: string[], userId: string, planId?: string, planName?: string }
   * Creates Stripe payment intent for service combination
   */
  async createPaymentIntent(req: Request, res: Response): Promise<void> {
    try {
      const { amount, serviceIds, userId, planId, planName } = req.body;
      
      // Input validation
      if (!amount || typeof amount !== 'number' || amount <= 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid amount (positive number) is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Service IDs array is required and cannot be empty',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid User ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Validate service IDs are strings
      if (!serviceIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'All service IDs must be non-empty strings',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const metadata: BillingMetadata = {
        userId: userId.trim(),
        serviceIds,
        planId: planId || 'custom',
        planName: planName || 'Custom Plan'
      };

      const paymentResult = await this.billingIntegration.createPaymentIntent(amount, metadata);
      
      const response: ApiResponse<PaymentResult> = {
        success: true,
        data: paymentResult
      };
      
      res.json(response);
    } catch (error) {
      console.error('BillingController.createPaymentIntent error:', error);
      
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'PAYMENT_INTENT_ERROR',
          message: 'Failed to create payment intent',
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * POST /api/billing/webhook
   * Handles Stripe webhook events
   */
  async handleWebhook(req: Request, res: Response): Promise<void> {
    try {
      const signature = req.headers['stripe-signature'] as string;
      const payload = req.body;
      
      // Input validation
      if (!signature || typeof signature !== 'string') {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_SIGNATURE',
            message: 'Stripe signature is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      if (!payload) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_PAYLOAD',
            message: 'Webhook payload is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Verify webhook signature and process event
      const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET || '';
      const event = this.billingIntegration.verifyWebhookSignature(payload, signature, endpointSecret);
      const webhookResult = await this.billingIntegration.handleWebhook(event);
      
      const response: ApiResponse<WebhookResult> = {
        success: true,
        data: webhookResult
      };
      
      res.json(response);
    } catch (error) {
      console.error('BillingController.handleWebhook error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'WEBHOOK_ERROR';
      let errorMessage = 'Failed to process webhook';

      if (error instanceof Error) {
        if (error.message.includes('Invalid webhook signature')) {
          statusCode = 400;
          errorCode = 'INVALID_SIGNATURE';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }

  /**
   * GET /api/billing/status/:userId
   * Returns billing status and next payment date
   */
  async getBillingStatus(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      
      // Input validation
      if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid User ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Get customer information from Stripe
      const customer = await this.billingIntegration.getCustomerByUserId(userId.trim());
      
      if (!customer) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'CUSTOMER_NOT_FOUND',
            message: 'No billing information found for this user',
            timestamp: new Date().toISOString()
          }
        };
        res.status(404).json(response);
        return;
      }

      // For now, return basic customer information
      // In a full implementation, you would get subscription details, payment history, etc.
      const billingStatus = {
        userId: userId.trim(),
        customerId: customer.id,
        email: customer.email,
        status: 'active', // This would come from subscription status
        nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        lastPaymentDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        currentPriceCents: 0 // This would come from current subscription
      };
      
      const response: ApiResponse<typeof billingStatus> = {
        success: true,
        data: billingStatus
      };
      
      res.json(response);
    } catch (error) {
      console.error('BillingController.getBillingStatus error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'BILLING_STATUS_ERROR';
      let errorMessage = 'Failed to retrieve billing status';

      if (error instanceof Error) {
        if (error.message.includes('User ID is required')) {
          statusCode = 400;
          errorCode = 'INVALID_INPUT';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }
}