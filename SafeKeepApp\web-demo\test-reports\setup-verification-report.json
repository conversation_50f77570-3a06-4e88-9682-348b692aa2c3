{"timestamp": "2025-07-28T21:56:29.925Z", "summary": {"totalChecks": 19, "errors": 2, "warnings": 5, "passed": false}, "checks": [{"status": "✅ Node.js version", "message": "v22.16.0 (supported)"}, {"status": "✅ Dependency", "message": "puppeteer@^21.0.0"}, {"status": "✅ Dependency", "message": "playwright@^1.40.0"}, {"status": "✅ Dependency", "message": "jest@^29.7.0"}, {"status": "✅ Dependency", "message": "supertest@^6.3.3"}, {"status": "✅ Dependency", "message": "stripe@^14.0.0"}, {"status": "✅ Test file", "message": "integration-test-runner.js"}, {"status": "✅ Test file", "message": "e2e-test-runner.js"}, {"status": "✅ Test file", "message": "stripe-integration-tests.js"}, {"status": "✅ Test file", "message": "subscription-lifecycle-tests.js"}, {"status": "✅ Test file", "message": "performance-benchmark-tests.js"}, {"status": "✅ Test file", "message": "cross-browser-tests.js"}, {"status": "✅ Test file", "message": "user-journey-tests.js"}, {"status": "✅ Test file", "message": "run-all-tests.js"}, {"status": "✅ Reports directory", "message": "Created test-reports directory"}, {"status": "✅ Reports directory writable", "message": "Write permissions verified"}, {"status": "✅ Browser support", "message": "chromium (assumed available)"}, {"status": "✅ Browser support", "message": "firefox (assumed available)"}, {"status": "✅ Browser support", "message": "webkit (assumed available)"}], "errors": [{"status": "❌ Dependencies not installed", "message": "Run npm install"}, {"status": "❌ Server unavailable", "message": "Demo Server - Start with 'npm run start:both'"}], "warnings": [{"status": "⚠️ Server unavailable", "message": "WebSocket Server - Some tests may fail"}, {"status": "ℹ️ Browser installation", "message": "Run \"npx playwright install\" if browsers are missing"}, {"status": "⚠️ Optional env var not set", "message": "STRIPE_SECRET_KEY: For Stripe integration tests"}, {"status": "⚠️ Optional env var not set", "message": "STRIPE_PUBLISHABLE_KEY: For Stripe integration tests"}, {"status": "⚠️ Optional env var not set", "message": "NODE_ENV: Environment setting"}]}