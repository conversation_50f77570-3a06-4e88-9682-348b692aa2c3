# 🔐 SafeKeep Stripe Integration Setup Guide

This guide will help you safely integrate Stripe payments into your SafeKeep project.

## 📋 Prerequisites

1. **Stripe Account**: Create a free account at [stripe.com](https://stripe.com)
2. **Node.js**: Ensure you have Node.js installed
3. **React Native Environment**: Your SafeKeep app should be set up

## 🔑 Step 1: Get Your Stripe API Keys

1. **Log into Stripe Dashboard**: https://dashboard.stripe.com
2. **Navigate to API Keys**: Developers → API keys
3. **Copy your keys**:
   - **Publishable key**: `pk_test_...` (safe for frontend)
   - **Secret key**: `sk_test_...` (NEVER expose to frontend!)
   - **Webhook secret**: `whsec_...` (for webhook verification)

## 🔧 Step 2: Configure Environment Variables

1. **Open the `.env` file** in your project root
2. **Replace the placeholder values** with your actual Stripe keys:

```env
# Replace these with your actual Stripe keys from the dashboard
STRIPE_SECRET_KEY=sk_test_YOUR_ACTUAL_SECRET_KEY_HERE
STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_ACTUAL_PUBLISHABLE_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_ACTUAL_WEBHOOK_SECRET_HERE
```

⚠️ **CRITICAL**: Never commit the `.env` file to version control!

## 🛡️ Step 3: Verify Security Setup

✅ **Check that `.env` is in `.gitignore`**:
```bash
# Should be in your .gitignore file
.env
.env.local
.env.*.local
```

✅ **Verify packages are installed**:
```bash
cd SafeKeepApp
npm list stripe @stripe/stripe-react-native dotenv
```

## 🏗️ Step 4: Backend Setup (Required for Production)

### Option A: Use the provided example
1. **Copy `backend-api-example.js`** to your backend server
2. **Install dependencies**:
```bash
npm install express cors stripe dotenv
```
3. **Run the server**:
```bash
node backend-api-example.js
```

### Option B: Integrate with existing backend
Add the payment endpoints from `backend-api-example.js` to your existing server.

## 📱 Step 5: Frontend Integration

### Initialize Stripe in your App
```tsx
// In your main App.tsx or App.js
import StripeWrapper from './src/components/StripeWrapper';

export default function App() {
  return (
    <StripeWrapper>
      {/* Your existing app components */}
    </StripeWrapper>
  );
}
```

### Use the Payment Component
```tsx
import StripePayment from './src/components/StripePayment';

// In any screen where you want payments
<StripePayment
  selectedPlan="PREMIUM"
  onPaymentSuccess={(paymentIntent) => {
    console.log('Payment successful!', paymentIntent);
    // Navigate to success screen, update user subscription, etc.
  }}
  onPaymentError={(error) => {
    console.error('Payment failed:', error);
    // Show error message, retry options, etc.
  }}
/>
```

## 🧪 Step 6: Testing

### Test Card Numbers (Development Only)
- **Visa**: `4242 4242 4242 4242`
- **Mastercard**: `5555 5555 5555 4444`
- **Declined**: `4000 0000 0000 0002`

Use any future expiry date and any 3-digit CVC.

### Test the Integration
1. **Start your backend server**
2. **Run your React Native app**
3. **Navigate to payment screen**
4. **Enter test card details**
5. **Complete payment flow**

## 🔍 Step 7: Webhook Setup (Production)

1. **In Stripe Dashboard**: Developers → Webhooks
2. **Add endpoint**: `https://your-domain.com/webhook`
3. **Select events**:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `customer.subscription.created`
   - `customer.subscription.deleted`
4. **Copy webhook secret** to your `.env` file

## 🚀 Step 8: Production Deployment

### Environment Variables
Replace test keys with live keys:
```env
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY
```

### Security Checklist
- ✅ `.env` file is in `.gitignore`
- ✅ Secret keys are only on backend server
- ✅ Webhook signature verification is enabled
- ✅ HTTPS is enabled for webhook endpoints
- ✅ Input validation on all payment endpoints

## 🔧 Troubleshooting

### Common Issues

**"Stripe publishable key not found"**
- Check that `.env` file exists in project root
- Verify the key starts with `pk_test_` or `pk_live_`
- Restart your development server

**"Payment intent creation failed"**
- Verify backend server is running
- Check that secret key is correctly configured
- Ensure API endpoint URLs are correct

**"Webhook signature verification failed"**
- Verify webhook secret in `.env` file
- Check that webhook URL is accessible
- Ensure raw body parsing for webhook endpoint

### Debug Mode
Add this to see configuration status:
```tsx
import environmentConfig from './src/config/environment';
console.log('Stripe config:', {
  hasPublishableKey: !!environmentConfig.STRIPE_PUBLISHABLE_KEY,
  hasSecretKey: !!environmentConfig.STRIPE_SECRET_KEY,
  environment: environmentConfig.NODE_ENV,
});
```

## 📞 Support

- **Stripe Documentation**: https://stripe.com/docs
- **React Native Stripe**: https://github.com/stripe/stripe-react-native
- **SafeKeep Issues**: Check your project's issue tracker

## 🎉 You're Ready!

Your SafeKeep app now has secure Stripe payment integration! 

Remember:
- 🔐 Keep secret keys secure
- 🧪 Test thoroughly before production
- 📊 Monitor payments in Stripe Dashboard
- 🔄 Handle webhooks for reliable payment processing
