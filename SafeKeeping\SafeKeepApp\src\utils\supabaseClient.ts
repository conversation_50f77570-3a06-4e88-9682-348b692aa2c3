import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define User and Session types based on Supabase v2 structure
export type User = {
  id: string;
  app_metadata: {
    provider?: string;
    [key: string]: any;
  };
  user_metadata: {
    [key: string]: any;
  };
  aud: string;
  email?: string;
  phone?: string;
  created_at: string;
  confirmed_at?: string;
  last_sign_in_at?: string;
  role?: string;
  updated_at?: string;
};

export type Session = {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at?: number;
  token_type: string;
  user: User;
};

// Get Supabase URL and anon key from environment variables
// For development, we'll use hardcoded values that will be replaced by CI/CD
const supabaseUrl = 'https://your-supabase-project-url.supabase.co';
const supabaseAnonKey = 'your-supabase-anon-key';

// Create a simple Database type for now
// This can be expanded later with proper table definitions
export type Database = {
  public: {
    Tables: {
      users: any;
      file_metadata: any;
      backup_sessions: any;
      storage_usage: any;
    };
  };
};

// Create Supabase client with AsyncStorage for session persistence
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    storage: AsyncStorage,
    storageKey: 'safekeep-auth-token',
  },
  global: {
    headers: {
      'x-app-version': '1.0.0',
    },
  },
});

// Export a function to get a fresh client with the latest session
export const getSupabaseClient = () => supabase;

// Helper function to handle Supabase errors consistently
export const handleSupabaseError = (error: any, context: string): string => {
  console.error(`Supabase error in ${context}:`, error);
  
  const errorMappings = {
    'JWT expired': 'Your session has expired. Please sign in again.',
    'Row Level Security': 'Access denied. You can only access your own data.',
    'duplicate key': 'This item already exists.',
    'foreign key': 'Invalid reference. Please try again.',
    'not found': 'The requested item was not found.',
    'storage quota exceeded': 'Storage limit reached. Please upgrade your plan.'
  };
  
  for (const [key, message] of Object.entries(errorMappings)) {
    if (error?.message?.includes(key)) {
      return message;
    }
  }
  
  return error?.message || 'An unexpected error occurred. Please try again.';
};