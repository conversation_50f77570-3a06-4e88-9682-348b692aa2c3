import { configureStore } from '@reduxjs/toolkit';
import BackupProgressService from '../../src/services/BackupProgressService';
import backupReducer from '../../src/store/slices/backupSlice';
import { BackupConfiguration } from '../../src/types/backup';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock the store
const mockStore = configureStore({
  reducer: {
    backup: backupReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'backup/startBackupSession',
          'backup/completeBackupSession',
          'backup/addProgressError',
        ],
        ignoredPaths: [
          'backup.currentSession.startTime',
          'backup.currentSession.endTime',
          'backup.backupHistory',
          'backup.realTimeProgress.contacts.errors',
          'backup.realTimeProgress.messages.errors',
          'backup.realTimeProgress.photos.errors',
          'backup.errors',
        ],
      },
    }),
});

// Mock the store import
jest.mock('../../src/store', () => ({
  get store() {
    return mockStore;
  },
}));

describe('BackupProgressService', () => {
  let service: BackupProgressService;
  const mockConfiguration: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium',
  };

  beforeEach(() => {
    service = BackupProgressService.getInstance();
    // Reset store state
    mockStore.dispatch({ type: 'backup/resetBackupState' });
  });

  afterEach(() => {
    service.reset();
  });

  describe('singleton pattern', () => {
    it('should return the same instance', () => {
      const instance1 = BackupProgressService.getInstance();
      const instance2 = BackupProgressService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('session initialization', () => {
    it('should initialize a new backup session', () => {
      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      const session = service.initializeSession('user-123', mockConfiguration, totalCounts);

      expect(session.id).toBeDefined();
      expect(session.userId).toBe('user-123');
      expect(session.status).toBe('in_progress');
      expect(session.totalItems).toBe(100);
      expect(session.completedItems).toBe(0);
      expect(session.configuration).toEqual(mockConfiguration);
      
      // Check progress initialization
      expect(session.progress.contacts.total).toBe(50);
      expect(session.progress.messages.total).toBe(30);
      expect(session.progress.photos.total).toBe(20);
      
      // Check store state
      const storeState = mockStore.getState().backup;
      expect(storeState.currentSession).toEqual(session);
      expect(storeState.isBackupInProgress).toBe(true);
    });

    it('should handle disabled data types', () => {
      const configWithDisabledTypes: BackupConfiguration = {
        ...mockConfiguration,
        includeMessages: false,
        includePhotos: false,
      };
      
      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      const session = service.initializeSession('user-123', configWithDisabledTypes, totalCounts);

      expect(session.progress.contacts.status).toBe('pending');
      expect(session.progress.messages.status).toBe('completed');
      expect(session.progress.photos.status).toBe('completed');
    });
  });

  describe('progress updates', () => {
    beforeEach(() => {
      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      service.initializeSession('user-123', mockConfiguration, totalCounts);
    });

    it('should update progress for a data type', () => {
      service.updateProgress('contacts', {
        completed: 25,
        status: 'in_progress',
      });

      const storeState = mockStore.getState().backup;
      expect(storeState.realTimeProgress.contacts.completed).toBe(25);
      expect(storeState.realTimeProgress.contacts.status).toBe('in_progress');
      expect(storeState.currentSession?.completedItems).toBe(25);
    });

    it('should increment progress', () => {
      service.incrementProgress('contacts', 'completed');
      service.incrementProgress('contacts', 'completed');
      service.incrementProgress('contacts', 'failed');

      const storeState = mockStore.getState().backup;
      expect(storeState.realTimeProgress.contacts.completed).toBe(2);
      expect(storeState.realTimeProgress.contacts.failed).toBe(1);
    });

    it('should not exceed total items', () => {
      service.updateProgress('contacts', { completed: 100 }); // More than total (50)

      const storeState = mockStore.getState().backup;
      expect(storeState.realTimeProgress.contacts.completed).toBe(50); // Should be capped at total
    });

    it('should auto-complete when all items are processed', () => {
      service.updateProgress('contacts', { completed: 45, failed: 5 });

      const storeState = mockStore.getState().backup;
      expect(storeState.realTimeProgress.contacts.status).toBe('failed'); // Has failures
    });

    it('should set data type status', () => {
      service.setDataTypeStatus('messages', 'completed');

      const storeState = mockStore.getState().backup;
      expect(storeState.realTimeProgress.messages.status).toBe('completed');
    });
  });

  describe('error handling', () => {
    beforeEach(() => {
      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      service.initializeSession('user-123', mockConfiguration, totalCounts);
    });

    it('should add errors with auto-generated ID and timestamp', () => {
      service.addError('photos', {
        type: 'network',
        message: 'Connection failed',
        retryable: true,
      });

      const storeState = mockStore.getState().backup;
      const photoErrors = storeState.realTimeProgress.photos.errors;
      
      expect(photoErrors).toHaveLength(1);
      expect(photoErrors[0].id).toBeDefined();
      expect(photoErrors[0].timestamp).toBeDefined();
      expect(photoErrors[0].type).toBe('network');
      expect(photoErrors[0].message).toBe('Connection failed');
      expect(photoErrors[0].retryable).toBe(true);
      
      // Should also increment failed count
      expect(storeState.realTimeProgress.photos.failed).toBe(1);
    });
  });

  describe('session completion', () => {
    beforeEach(() => {
      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      service.initializeSession('user-123', mockConfiguration, totalCounts);
    });

    it('should complete session successfully', async () => {
      await service.completeSession('completed');

      const storeState = mockStore.getState().backup;
      expect(storeState.currentSession?.status).toBe('completed');
      expect(storeState.currentSession?.endTime).toBeDefined();
      expect(storeState.isBackupInProgress).toBe(false);
      expect(storeState.backupHistory).toHaveLength(1);
    });

    it('should cancel session', async () => {
      await service.cancelSession();

      const storeState = mockStore.getState().backup;
      expect(storeState.currentSession?.status).toBe('cancelled');
      expect(storeState.isBackupInProgress).toBe(false);
    });
  });

  describe('progress summary', () => {
    beforeEach(() => {
      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      service.initializeSession('user-123', mockConfiguration, totalCounts);
    });

    it('should calculate progress summary correctly', () => {
      // Update some progress
      service.updateProgress('contacts', { completed: 25 });
      service.updateProgress('messages', { completed: 15 });
      service.updateProgress('photos', { completed: 10 });

      const summary = service.getProgressSummary();

      expect(summary.overallProgress).toBe(50); // 50 out of 100 items
      expect(summary.dataTypeProgress.contacts.progress).toBe(50); // 25 out of 50
      expect(summary.dataTypeProgress.messages.progress).toBe(50); // 15 out of 30
      expect(summary.dataTypeProgress.photos.progress).toBe(50); // 10 out of 20
    });

    it('should calculate estimated time remaining', () => {
      // Simulate some progress after a delay
      const startTime = Date.now() - 10000; // 10 seconds ago
      jest.spyOn(Date, 'now').mockReturnValue(startTime + 10000);
      
      service.updateProgress('contacts', { completed: 25 });
      
      const summary = service.getProgressSummary();
      expect(summary.estimatedTimeRemaining).toBeGreaterThan(0);
    });
  });

  describe('utility methods', () => {
    it('should check if backup is in progress', () => {
      expect(service.isBackupInProgress()).toBe(false);

      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      service.initializeSession('user-123', mockConfiguration, totalCounts);
      
      expect(service.isBackupInProgress()).toBe(true);
    });

    it('should get current session ID', () => {
      expect(service.getCurrentSessionId()).toBeNull();

      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      const session = service.initializeSession('user-123', mockConfiguration, totalCounts);
      
      expect(service.getCurrentSessionId()).toBe(session.id);
    });
  });

  describe('history statistics', () => {
    it('should calculate history statistics', async () => {
      // Create and complete a few sessions
      const totalCounts = { contacts: 50, messages: 30, photos: 20 };
      
      // Session 1 - successful
      service.initializeSession('user-123', mockConfiguration, totalCounts);
      service.updateProgress('contacts', { completed: 50 });
      service.updateProgress('messages', { completed: 30 });
      service.updateProgress('photos', { completed: 20 });
      await service.completeSession('completed');
      
      // Session 2 - failed
      service.initializeSession('user-123', mockConfiguration, totalCounts);
      service.updateProgress('contacts', { completed: 25, failed: 25 });
      await service.completeSession('failed');

      const stats = service.getHistoryStatistics();
      
      expect(stats.totalSessions).toBe(2);
      expect(stats.successfulSessions).toBe(1);
      expect(stats.failedSessions).toBe(1);
      expect(stats.totalItemsBackedUp).toBe(100); // Only from successful session
      expect(stats.averageSessionDuration).toBeGreaterThan(0);
    });
  });
});