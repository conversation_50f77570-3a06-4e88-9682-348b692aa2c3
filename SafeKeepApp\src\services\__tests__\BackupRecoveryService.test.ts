import AsyncStorage from '@react-native-async-storage/async-storage';
import BackupRecoveryService from '../BackupRecoveryService';
import { BackupDatabaseService } from '../BackupDatabaseService';
import NotificationService from '../NotificationService';
import { BackupRecoveryState, BackupConfiguration } from '../../types/backup';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  getAllKeys: jest.fn()
}));

// Mock BackupDatabaseService
jest.mock('../BackupDatabaseService', () => ({
  BackupDatabaseService: {
    getBackupSession: jest.fn()
  }
}));

// Mock NotificationService
jest.mock('../NotificationService', () => ({
  showResumeNotification: jest.fn(),
  showInfoNotification: jest.fn()
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockBackupDatabaseService = BackupDatabaseService as jest.Mocked<typeof BackupDatabaseService>;
const mockNotificationService = NotificationService as jest.Mocked<typeof NotificationService>;

describe('BackupRecoveryService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('saveCheckpoint', () => {
    it('should save recovery checkpoint to AsyncStorage', async () => {
      mockAsyncStorage.setItem.mockResolvedValue();

      await BackupRecoveryService.saveCheckpoint(
        'session-123',
        'photos',
        50,
        []
      );

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'backup_recovery_state_session-123',
        expect.stringContaining('"sessionId":"session-123"')
      );

      const savedData = JSON.parse(mockAsyncStorage.setItem.mock.calls[0][1]);
      expect(savedData.sessionId).toBe('session-123');
      expect(savedData.lastCheckpoint.dataType).toBe('photos');
      expect(savedData.lastCheckpoint.itemIndex).toBe(50);
      expect(savedData.resumable).toBe(true);
    });

    it('should handle save errors gracefully', async () => {
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage error'));

      // Should not throw
      await expect(BackupRecoveryService.saveCheckpoint('session-123', 'contacts', 10))
        .resolves.toBeUndefined();
    });
  });

  describe('checkForResumableSession', () => {
    it('should return null when no recovery states exist', async () => {
      mockAsyncStorage.getAllKeys.mockResolvedValue([]);

      const result = await BackupRecoveryService.checkForResumableSession();

      expect(result).toBeNull();
    });

    it('should return the most recent resumable session', async () => {
      const oldRecoveryState: BackupRecoveryState = {
        sessionId: 'old-session',
        resumable: true,
        lastCheckpoint: {
          dataType: 'contacts',
          itemIndex: 10,
          timestamp: new Date('2023-01-01')
        },
        failedItems: [],
        retryableErrors: []
      };

      const recentRecoveryState: BackupRecoveryState = {
        sessionId: 'recent-session',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 50,
          timestamp: new Date('2023-01-02')
        },
        failedItems: [],
        retryableErrors: []
      };

      mockAsyncStorage.getAllKeys.mockResolvedValue([
        'backup_recovery_state_old-session',
        'backup_recovery_state_recent-session',
        'other_key'
      ]);

      mockAsyncStorage.getItem
        .mockResolvedValueOnce(JSON.stringify(oldRecoveryState))
        .mockResolvedValueOnce(JSON.stringify(recentRecoveryState));

      const result = await BackupRecoveryService.checkForResumableSession();

      expect(result).toEqual(recentRecoveryState);
    });

    it('should filter out non-resumable sessions', async () => {
      const nonResumableState: BackupRecoveryState = {
        sessionId: 'non-resumable',
        resumable: false,
        lastCheckpoint: {
          dataType: 'contacts',
          itemIndex: 10,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      mockAsyncStorage.getAllKeys.mockResolvedValue(['backup_recovery_state_non-resumable']);
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(nonResumableState));

      const result = await BackupRecoveryService.checkForResumableSession();

      expect(result).toBeNull();
    });
  });

  describe('resumeBackup', () => {
    const mockConfiguration: BackupConfiguration = {
      autoBackup: false,
      wifiOnly: true,
      includeContacts: true,
      includeMessages: true,
      includePhotos: true,
      compressionLevel: 'medium'
    };

    const mockRecoveryState: BackupRecoveryState = {
      sessionId: 'session-123',
      resumable: true,
      lastCheckpoint: {
        dataType: 'photos',
        itemIndex: 50,
        timestamp: new Date()
      },
      failedItems: [],
      retryableErrors: []
    };

    it('should resume backup successfully', async () => {
      const mockSession = {
        id: 'session-123',
        user_id: 'user-123',
        start_time: new Date().toISOString(),
        status: 'in_progress' as const,
        total_items: 100,
        completed_items: 50,
        configuration: mockConfiguration,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockBackupDatabaseService.getBackupSession.mockResolvedValue(mockSession);
      mockNotificationService.showResumeNotification.mockResolvedValue();

      const result = await BackupRecoveryService.resumeBackup(
        mockRecoveryState,
        mockConfiguration
      );

      expect(result.success).toBe(true);
      expect(mockBackupDatabaseService.getBackupSession).toHaveBeenCalledWith('session-123');
      expect(mockNotificationService.showResumeNotification).toHaveBeenCalled();
    });

    it('should fail when session is not found', async () => {
      mockBackupDatabaseService.getBackupSession.mockResolvedValue(null);

      const result = await BackupRecoveryService.resumeBackup(
        mockRecoveryState,
        mockConfiguration
      );

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Original backup session not found');
    });

    it('should handle resume errors', async () => {
      mockBackupDatabaseService.getBackupSession.mockRejectedValue(new Error('Database error'));

      const result = await BackupRecoveryService.resumeBackup(
        mockRecoveryState,
        mockConfiguration
      );

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
    });
  });

  describe('retryFailedItems', () => {
    const mockConfiguration: BackupConfiguration = {
      autoBackup: false,
      wifiOnly: true,
      includeContacts: true,
      includeMessages: true,
      includePhotos: true,
      compressionLevel: 'medium'
    };

    it('should return success when no retryable errors exist', async () => {
      const recoveryState: BackupRecoveryState = {
        sessionId: 'session-123',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 50,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(recoveryState));

      const result = await BackupRecoveryService.retryFailedItems(
        'session-123',
        mockConfiguration
      );

      expect(result.success).toBe(true);
      expect(result.recoveredItems).toBe(0);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail when no recovery state exists', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const result = await BackupRecoveryService.retryFailedItems(
        'session-123',
        mockConfiguration
      );

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('No recovery information found for this session');
    });
  });

  describe('continuePartialBackup', () => {
    const mockConfiguration: BackupConfiguration = {
      autoBackup: false,
      wifiOnly: true,
      includeContacts: true,
      includeMessages: true,
      includePhotos: true,
      compressionLevel: 'medium'
    };

    it('should show info notification about partial backup continuation', async () => {
      const recoveryState: BackupRecoveryState = {
        sessionId: 'session-123',
        resumable: true,
        lastCheckpoint: {
          dataType: 'contacts',
          itemIndex: 50,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(recoveryState));
      mockNotificationService.showInfoNotification.mockResolvedValue();

      const result = await BackupRecoveryService.continuePartialBackup(
        'session-123',
        'contacts',
        mockConfiguration
      );

      expect(mockNotificationService.showInfoNotification).toHaveBeenCalledWith(
        'Continuing Backup',
        expect.stringContaining('Skipping contacts backup')
      );
    });

    it('should return success when no remaining data types', async () => {
      const recoveryState: BackupRecoveryState = {
        sessionId: 'session-123',
        resumable: true,
        lastCheckpoint: {
          dataType: 'photos', // Last data type
          itemIndex: 50,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(recoveryState));

      const result = await BackupRecoveryService.continuePartialBackup(
        'session-123',
        'photos',
        mockConfiguration
      );

      expect(result.success).toBe(true);
      expect(result.recoveredItems).toBe(0);
    });
  });

  describe('clearRecoveryState', () => {
    it('should remove recovery state from AsyncStorage', async () => {
      mockAsyncStorage.removeItem.mockResolvedValue();

      await BackupRecoveryService.clearRecoveryState('session-123');

      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith(
        'backup_recovery_state_session-123'
      );
    });

    it('should handle removal errors gracefully', async () => {
      mockAsyncStorage.removeItem.mockRejectedValue(new Error('Storage error'));

      // Should not throw
      await expect(BackupRecoveryService.clearRecoveryState('session-123'))
        .resolves.toBeUndefined();
    });
  });

  describe('getAllRecoveryStates', () => {
    it('should return all recovery states', async () => {
      const recoveryState1: BackupRecoveryState = {
        sessionId: 'session-1',
        resumable: true,
        lastCheckpoint: {
          dataType: 'contacts',
          itemIndex: 10,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      const recoveryState2: BackupRecoveryState = {
        sessionId: 'session-2',
        resumable: false,
        lastCheckpoint: {
          dataType: 'photos',
          itemIndex: 50,
          timestamp: new Date()
        },
        failedItems: [],
        retryableErrors: []
      };

      mockAsyncStorage.getAllKeys.mockResolvedValue([
        'backup_recovery_state_session-1',
        'backup_recovery_state_session-2',
        'other_key'
      ]);

      mockAsyncStorage.getItem
        .mockResolvedValueOnce(JSON.stringify(recoveryState1))
        .mockResolvedValueOnce(JSON.stringify(recoveryState2));

      const result = await BackupRecoveryService.getAllRecoveryStates();

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(recoveryState1);
      expect(result[1]).toEqual(recoveryState2);
    });

    it('should handle errors and return empty array', async () => {
      mockAsyncStorage.getAllKeys.mockRejectedValue(new Error('Storage error'));

      const result = await BackupRecoveryService.getAllRecoveryStates();

      expect(result).toEqual([]);
    });
  });
});