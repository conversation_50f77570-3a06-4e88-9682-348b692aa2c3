import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import { 
  Button, 
  Card, 
  Text, 
  ProgressBar, 
  Chip, 
  List,
  ActivityIndicator 
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import PhotoBackupService, { PhotoAsset, BackupProgress, BackupResult } from '../../services/PhotoBackupService';
import CloudStorageService from '../../services/CloudStorageService';
import { COLORS, SPACING } from '../../utils/constants';

const { width } = Dimensions.get('window');

const PhotoBackupScreen = () => {
  const [photos, setPhotos] = useState<PhotoAsset[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [backupProgress, setBackupProgress] = useState<BackupProgress | null>(null);
  const [backupResult, setBackupResult] = useState<BackupResult | null>(null);
  const [backupStats, setBackupStats] = useState<any>(null);
  const [scanComplete, setScanComplete] = useState(false);

  useEffect(() => {
    // Auto-scan photos when component mounts
    loadBackupStats();
    handleScanPhotos();
  }, []);

  const loadBackupStats = async () => {
    try {
      console.log('📸 Loading backup stats...');
      const stats = await PhotoBackupService.getBackupStatsFromCloud();
      console.log('📸 Backup stats loaded:', stats);
      setBackupStats(stats);
    } catch (error) {
      console.error('📸 Failed to load backup stats:', error);
    }
  };

  const handleScanPhotos = async () => {
    setIsScanning(true);
    setScanComplete(false);
    setBackupResult(null);
    
    try {
      console.log('📷 Starting photo scan...');
      const scannedPhotos = await PhotoBackupService.scanPhotoLibrary(500); // Limit for demo
      setPhotos(scannedPhotos);
      setScanComplete(true);
      
      Alert.alert(
        '📷 Photos Found!',
        `Found ${scannedPhotos.length} photos in your library.\n\nReady to backup your precious memories?`,
        [{ text: 'Great!' }]
      );
    } catch (error) {
      console.error('Photo scan error:', error);
      Alert.alert(
        'Scan Error',
        'Could not access your photos. Please check permissions and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Try Again', onPress: handleScanPhotos }
        ]
      );
    } finally {
      setIsScanning(false);
    }
  };

  const handleStartBackup = async () => {
    if (photos.length === 0) {
      Alert.alert(
        'No Photos Found',
        'Please scan for photos first before starting backup.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      '🚀 Start Photo Backup?',
      `This will backup ${photos.length} photos to the cloud.\n\n• Duplicates will be automatically detected\n• Your photos will be encrypted for security\n• You can pause anytime\n\nStart backup now?`,
      [
        { text: 'Not Now', style: 'cancel' },
        { text: 'Start Backup', onPress: startBackupProcess }
      ]
    );
  };

  const startBackupProcess = async () => {
    setIsBackingUp(true);
    setBackupResult(null);
    
    try {
      const result = await PhotoBackupService.backupPhotos(
        photos,
        (progress: BackupProgress) => {
          setBackupProgress(progress);
        },
        3 // Max retries
      );
      
      setBackupResult(result);
      await loadBackupStats(); // Refresh stats
      showBackupCompleteDialog(result);
    } catch (error) {
      console.error('Backup error:', error);
      Alert.alert(
        'Backup Error',
        'Something went wrong during backup. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsBackingUp(false);
      setBackupProgress(null);
    }
  };

  const showBackupCompleteDialog = (result: BackupResult) => {
    const minutes = Math.round(result.duration / 60000);
    const seconds = Math.round((result.duration % 60000) / 1000);
    const timeString = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;

    if (result.success) {
      Alert.alert(
        '🎉 Backup Complete!',
        `Successfully backed up ${result.backedUpPhotos} photos in ${timeString}!\n\n` +
        `• ${result.duplicatesFound} duplicates were skipped\n` +
        `• All your photos are now safely stored in the cloud\n` +
        `• Your memories are protected forever!`,
        [{ text: 'Wonderful!' }]
      );
    } else {
      Alert.alert(
        '⚠️ Backup Completed with Issues',
        `Backed up ${result.backedUpPhotos} of ${result.totalPhotos} photos.\n\n` +
        `• ${result.skippedPhotos} photos had errors\n` +
        `• ${result.duplicatesFound} duplicates were skipped\n` +
        `• Time taken: ${timeString}\n\n` +
        `Would you like to retry the failed photos?`,
        [
          { text: 'Later', style: 'cancel' },
          { text: 'Retry Failed', onPress: handleStartBackup }
        ]
      );
    }
  };

  const handlePauseResume = () => {
    if (backupProgress?.status === 'paused') {
      PhotoBackupService.resumeBackup();
    } else {
      PhotoBackupService.pauseBackup();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const handleRestorePhotos = async () => {
    Alert.alert(
      '📸 Restore Photos?',
      'This will download your latest photo backup from the cloud.\n\nNote: Photos will be restored to your camera roll for review.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Restore', onPress: performRestore }
      ]
    );
  };

  const performRestore = async () => {
    try {
      console.log('📸 Starting photo restore process...');
      const result = await PhotoBackupService.restorePhotos();
      console.log('📸 Restore result:', result);

      if (result.success) {
        Alert.alert(
          '✅ Restore Complete!',
          `Successfully restored ${result.photos?.length || 0} photos from your backup.\n\nYour photos are now available for review and can be saved to your camera roll.`,
          [{ text: 'Great!' }]
        );
      } else {
        Alert.alert(
          'Restore Failed',
          result.error || 'Could not restore photos from backup.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('📸 Restore error:', error);
      Alert.alert(
        'Restore Error',
        `Something went wrong during restore: ${error.message}`,
        [{ text: 'OK' }]
      );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return COLORS.success;
      case 'error': return COLORS.error;
      case 'paused': return COLORS.warning;
      default: return COLORS.primary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scanning': return 'magnify';
      case 'processing': return 'cog';
      case 'uploading': return 'cloud-upload';
      case 'completed': return 'check-circle';
      case 'error': return 'alert-circle';
      case 'paused': return 'pause-circle';
      default: return 'information';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Photo Backup',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        {/* Photo Library Status */}
        <Card style={styles.statusCard}>
          <Card.Content>
            <View style={styles.statusHeader}>
              <Icon name="camera" size={32} color={COLORS.primary} />
              <Text variant="titleLarge" style={styles.statusTitle}>
                Photo Library
              </Text>
            </View>

            {isScanning ? (
              <View style={styles.scanningContainer}>
                <ActivityIndicator size="large" color={COLORS.primary} />
                <Text style={styles.scanningText}>Scanning your photos...</Text>
              </View>
            ) : (
              <View style={styles.photoStats}>
                <View style={styles.statRow}>
                  <Text style={styles.statLabel}>Photos Found:</Text>
                  <Chip style={styles.statChip}>{photos.length}</Chip>
                </View>
                
                {scanComplete && (
                  <>
                    <View style={styles.statRow}>
                      <Text style={styles.statLabel}>Total Size:</Text>
                      <Text style={styles.statValue}>
                        {formatFileSize(photos.reduce((sum, photo) => sum + photo.fileSize, 0))}
                      </Text>
                    </View>
                    
                    <Button
                      mode="outlined"
                      onPress={handleScanPhotos}
                      style={styles.rescanButton}
                      icon="refresh"
                    >
                      Rescan Photos
                    </Button>
                  </>
                )}
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Backup Progress */}
        {(isBackingUp || backupProgress) && (
          <Card style={styles.progressCard}>
            <Card.Content>
              <View style={styles.progressHeader}>
                <Icon 
                  name={getStatusIcon(backupProgress?.status || 'uploading')} 
                  size={24} 
                  color={getStatusColor(backupProgress?.status || 'uploading')} 
                />
                <Text variant="titleMedium" style={styles.progressTitle}>
                  {backupProgress?.status === 'scanning' && 'Scanning Photos...'}
                  {backupProgress?.status === 'processing' && 'Processing Photos...'}
                  {backupProgress?.status === 'uploading' && 'Uploading Photos...'}
                  {backupProgress?.status === 'completed' && 'Backup Complete!'}
                  {backupProgress?.status === 'error' && 'Backup Error'}
                  {backupProgress?.status === 'paused' && 'Backup Paused'}
                </Text>
              </View>

              {backupProgress && (
                <>
                  <Text style={styles.currentPhoto}>
                    {backupProgress.currentPhoto}
                  </Text>

                  <ProgressBar 
                    progress={backupProgress.percentage / 100} 
                    color={getStatusColor(backupProgress.status)}
                    style={styles.progressBar}
                  />

                  <View style={styles.progressStats}>
                    <Text style={styles.progressText}>
                      {backupProgress.processedPhotos} of {backupProgress.totalPhotos} photos
                    </Text>
                    <Text style={styles.progressText}>
                      {backupProgress.percentage}%
                    </Text>
                  </View>

                  {backupProgress.totalBytes > 0 && (
                    <Text style={styles.progressText}>
                      {formatFileSize(backupProgress.bytesUploaded)} of {formatFileSize(backupProgress.totalBytes)}
                    </Text>
                  )}

                  {isBackingUp && (
                    <View style={styles.controlButtons}>
                      <Button
                        mode="outlined"
                        onPress={handlePauseResume}
                        style={styles.controlButton}
                        icon={backupProgress.status === 'paused' ? 'play' : 'pause'}
                      >
                        {backupProgress.status === 'paused' ? 'Resume' : 'Pause'}
                      </Button>
                    </View>
                  )}
                </>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Backup Results */}
        {backupResult && (
          <Card style={styles.resultCard}>
            <Card.Content>
              <View style={styles.resultHeader}>
                <Icon 
                  name={backupResult.success ? 'check-circle' : 'alert-circle'} 
                  size={32} 
                  color={backupResult.success ? COLORS.success : COLORS.warning} 
                />
                <Text variant="titleLarge" style={styles.resultTitle}>
                  Backup {backupResult.success ? 'Successful' : 'Completed'}
                </Text>
              </View>

              <View style={styles.resultStats}>
                <List.Item
                  title="Photos Backed Up"
                  description={`${backupResult.backedUpPhotos} photos`}
                  left={() => <Icon name="check" size={24} color={COLORS.success} />}
                />
                <List.Item
                  title="Duplicates Skipped"
                  description={`${backupResult.duplicatesFound} duplicates`}
                  left={() => <Icon name="content-duplicate" size={24} color={COLORS.warning} />}
                />
                {backupResult.skippedPhotos > 0 && (
                  <List.Item
                    title="Photos Skipped"
                    description={`${backupResult.skippedPhotos} errors`}
                    left={() => <Icon name="alert" size={24} color={COLORS.error} />}
                  />
                )}
                <List.Item
                  title="Time Taken"
                  description={`${Math.round(backupResult.duration / 1000)} seconds`}
                  left={() => <Icon name="clock" size={24} color={COLORS.primary} />}
                />
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Backup Statistics */}
        {backupStats && backupStats.totalBackups > 0 && (
          <Card style={styles.statsCard}>
            <Card.Content>
              <View style={styles.statsHeader}>
                <Icon name="chart-line" size={24} color={COLORS.primary} />
                <Text variant="titleMedium" style={styles.statsTitle}>
                  Backup History
                </Text>
              </View>

              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{backupStats.totalBackups}</Text>
                  <Text style={styles.statLabel}>Total Backups</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{backupStats.totalPhotos}</Text>
                  <Text style={styles.statLabel}>Photos Backed Up</Text>
                </View>
              </View>

              {backupStats.lastBackupDate && (
                <Text style={styles.lastBackupText}>
                  Last backup: {new Date(backupStats.lastBackupDate).toLocaleDateString()}
                </Text>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Action Buttons */}
        <Card style={styles.actionCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.actionTitle}>
              Ready to Backup Your Photos?
            </Text>
            
            <Text style={styles.actionDescription}>
              Your photos will be safely encrypted and stored in the cloud. 
              Duplicates are automatically detected and skipped.
            </Text>

            <View style={styles.actionButtons}>
              {!isBackingUp && scanComplete && (
                <Button
                  mode="contained"
                  onPress={handleStartBackup}
                  disabled={photos.length === 0}
                  style={styles.primaryButton}
                  icon="cloud-upload"
                >
                  Start Photo Backup
                </Button>
              )}

              {/* Always show restore button - it should be available at all times */}
              <Button
                mode="outlined"
                onPress={handleRestorePhotos}
                style={styles.secondaryButton}
                icon="cloud-download"
              >
                Restore from Backup
              </Button>

              {!scanComplete && (
                <Button
                  mode="outlined"
                  onPress={handleScanPhotos}
                  disabled={isScanning}
                  loading={isScanning}
                  style={styles.secondaryButton}
                  icon="magnify"
                >
                  Scan for Photos
                </Button>
              )}
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  statusCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statusTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  scanningContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  scanningText: {
    marginTop: SPACING.sm,
    color: COLORS.textSecondary,
  },
  photoStats: {
    gap: SPACING.sm,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    color: COLORS.text,
    fontSize: 16,
  },
  statValue: {
    color: COLORS.textSecondary,
    fontSize: 16,
  },
  statChip: {
    backgroundColor: COLORS.primary,
  },
  rescanButton: {
    marginTop: SPACING.sm,
    borderColor: COLORS.primary,
  },
  progressCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  progressTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  currentPhoto: {
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    fontSize: 14,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: SPACING.sm,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  progressText: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  controlButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: SPACING.md,
  },
  controlButton: {
    borderColor: COLORS.primary,
  },
  resultCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  resultTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  resultStats: {
    gap: SPACING.xs,
  },
  statsCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statsTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: 4,
  },
  lastBackupText: {
    textAlign: 'center',
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  actionCard: {
    elevation: 4,
  },
  actionTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  actionDescription: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  actionButtons: {
    gap: SPACING.sm,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    borderColor: COLORS.primary,
  },
});

export default PhotoBackupScreen;
