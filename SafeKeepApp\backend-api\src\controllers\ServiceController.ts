import { Request, Response } from 'express';
import { ApiResponse, UserService, ValidationResult, AccessResult } from '../types/modular-pricing';
import { ServiceValidator } from '../services/ServiceValidator';

export class ServiceController {
  private serviceValidator: ServiceValidator;

  constructor() {
    this.serviceValidator = new ServiceValidator();
  }
  /**
   * GET /api/services/user/:userId
   * Returns user's active services
   */
  async getUserServices(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      
      // Input validation
      if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid User ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const userServices = await this.serviceValidator.getUserServices(userId.trim());
      
      const response: ApiResponse<UserService[]> = {
        success: true,
        data: userServices
      };
      
      res.json(response);
    } catch (error) {
      console.error('ServiceController.getUserServices error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'USER_SERVICES_ERROR';
      let errorMessage = 'Failed to retrieve user services';

      if (error instanceof Error) {
        if (error.message.includes('User ID is required')) {
          statusCode = 400;
          errorCode = 'INVALID_INPUT';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }

  /**
   * POST /api/services/validate
   * Body: { serviceIds: string[] }
   * Validates service combination
   */
  async validateServiceCombination(req: Request, res: Response): Promise<void> {
    try {
      const { serviceIds } = req.body;
      
      // Input validation
      if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Service IDs array is required and cannot be empty',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      // Validate service IDs are strings
      if (!serviceIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'All service IDs must be non-empty strings',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const validationResult = await this.serviceValidator.validateServiceCombination(serviceIds);
      
      const response: ApiResponse<ValidationResult> = {
        success: true,
        data: validationResult
      };
      
      res.json(response);
    } catch (error) {
      console.error('ServiceController.validateServiceCombination error:', error);
      
      const response: ApiResponse = {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Failed to validate service combination',
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(500).json(response);
    }
  }

  /**
   * GET /api/services/access/:userId/:serviceType
   * Checks if user has access to specific service
   */
  async checkServiceAccess(req: Request, res: Response): Promise<void> {
    try {
      const { userId, serviceType } = req.params;
      
      // Input validation
      if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid User ID is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      if (!serviceType || typeof serviceType !== 'string' || serviceType.trim().length === 0) {
        const response: ApiResponse = {
          success: false,
          error: {
            code: 'INVALID_INPUT',
            message: 'Valid service type is required',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(response);
        return;
      }

      const accessResult = await this.serviceValidator.checkServiceAccess(userId.trim(), serviceType.trim());
      
      const response: ApiResponse<AccessResult> = {
        success: true,
        data: accessResult
      };
      
      res.json(response);
    } catch (error) {
      console.error('ServiceController.checkServiceAccess error:', error);
      
      // Handle specific error types
      let statusCode = 500;
      let errorCode = 'ACCESS_CHECK_ERROR';
      let errorMessage = 'Failed to check service access';

      if (error instanceof Error) {
        if (error.message.includes('User ID is required') || error.message.includes('Service type is required')) {
          statusCode = 400;
          errorCode = 'INVALID_INPUT';
          errorMessage = error.message;
        }
      }

      const response: ApiResponse = {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: error instanceof Error ? { message: error.message } : undefined,
          timestamp: new Date().toISOString()
        }
      };
      res.status(statusCode).json(response);
    }
  }
}