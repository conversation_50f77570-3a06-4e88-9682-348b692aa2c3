/**
 * Data Export/Import Verification Script
 * Tests all functionality of the DataExportImportManager
 */

const fs = require('fs');
const path = require('path');

// Mock Supabase client for testing
const mockSupabase = {
    from: (table) => ({
        select: (fields) => ({
            eq: (field, value) => ({
                single: () => Promise.resolve({
                    data: generateMockBackupSession(),
                    error: null
                })
            })
        })
    })
};

// Import the manager
const imported = require('./data-export-import-manager.js');
console.log('Imported:', imported);
console.log('Keys:', Object.keys(imported));
const DataExportImportManager = imported.DataExportImportManager || imported;

class DataExportImportVerifier {
    constructor() {
        this.manager = new DataExportImportManager(mockSupabase, mockSupabase);
        this.testResults = [];
        this.setupMockData();
    }
    
    setupMockData() {
        // Override the getBackupSessionData method with mock data
        this.manager.getBackupSessionData = async function(sessionId) {
            return {
                session: {
                    id: sessionId,
                    user_id: 'test-user',
                    status: 'completed',
                    started_at: new Date().toISOString(),
                    completed_at: new Date().toISOString()
                },
                contacts: [
                    {
                        id: 'contact_1',
                        data_type: 'contacts',
                        data: {
                            name: 'John Doe',
                            phone: '******-0001',
                            email: '<EMAIL>'
                        }
                    },
                    {
                        id: 'contact_2',
                        data_type: 'contacts',
                        data: {
                            name: 'Jane Smith',
                            phone: '******-0002',
                            email: '<EMAIL>'
                        }
                    }
                ],
                messages: [
                    {
                        id: 'message_1',
                        data_type: 'messages',
                        data: {
                            content: 'Hello, this is a test message',
                            sender: 'John Doe',
                            timestamp: new Date().toISOString()
                        }
                    }
                ],
                photos: [
                    {
                        id: 'photo_1',
                        data_type: 'photos',
                        data: {
                            filename: 'test.jpg',
                            data: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
                            metadata: {
                                size: 1024000,
                                width: 1920,
                                height: 1080
                            }
                        }
                    }
                ],
                metadata: {
                    exportedAt: new Date().toISOString(),
                    version: '1.0',
                    totalItems: 4,
                    dataTypes: ['contacts', 'messages', 'photos']
                }
            };
        };
    }
    
    async runAllTests() {
        console.log('🔄 Starting Data Export/Import Verification Tests...\n');
        
        try {
            await this.testExportFormats();
            await this.testImportFormats();
            await this.testDataValidation();
            await this.testCrossPlatformCompatibility();
            await this.testErrorHandling();
            await this.testDataIntegrity();
            
            this.printResults();
            
        } catch (error) {
            console.error('❌ Test suite failed:', error);
            process.exit(1);
        }
    }
    
    async testExportFormats() {
        console.log('📤 Testing Export Formats...');
        
        const formats = ['json', 'csv', 'xml', 'zip', 'backup'];
        
        for (const format of formats) {
            try {
                // Mock the downloadFile method to capture the data instead of downloading
                let exportedData = null;
                const originalDownload = this.manager.downloadFile;
                this.manager.downloadFile = async (content, filename, format) => {
                    exportedData = content;
                    return Promise.resolve();
                };
                
                await this.manager.exportBackupData('test-session', format, { pretty: true });
                
                // Restore original method
                this.manager.downloadFile = originalDownload;
                
                if (exportedData) {
                    this.addTestResult(`Export ${format.toUpperCase()}`, true, `Successfully exported data in ${format} format`);
                    
                    // Verify format-specific content
                    await this.verifyExportFormat(format, exportedData);
                } else {
                    this.addTestResult(`Export ${format.toUpperCase()}`, false, 'No data was exported');
                }
                
            } catch (error) {
                this.addTestResult(`Export ${format.toUpperCase()}`, false, error.message);
            }
        }
    }
    
    async verifyExportFormat(format, data) {
        try {
            switch (format) {
                case 'json':
                    const jsonData = JSON.parse(data);
                    if (jsonData.contacts && jsonData.messages && jsonData.photos) {
                        this.addTestResult(`JSON Format Validation`, true, 'JSON structure is valid');
                    } else {
                        this.addTestResult(`JSON Format Validation`, false, 'Missing required data types');
                    }
                    break;
                    
                case 'csv':
                    if (data.includes('name,phone,email') || data.includes('ZIP_ARCHIVE:')) {
                        this.addTestResult(`CSV Format Validation`, true, 'CSV format is valid');
                    } else {
                        this.addTestResult(`CSV Format Validation`, false, 'Invalid CSV format');
                    }
                    break;
                    
                case 'xml':
                    if (data.includes('<?xml') && data.includes('<SafeKeepBackup>')) {
                        this.addTestResult(`XML Format Validation`, true, 'XML format is valid');
                    } else {
                        this.addTestResult(`XML Format Validation`, false, 'Invalid XML format');
                    }
                    break;
                    
                case 'zip':
                    if (data.startsWith('ZIP_ARCHIVE:')) {
                        this.addTestResult(`ZIP Format Validation`, true, 'ZIP format is valid');
                    } else {
                        this.addTestResult(`ZIP Format Validation`, false, 'Invalid ZIP format');
                    }
                    break;
                    
                case 'backup':
                    if (data.includes('SafeKeep Backup v1.0') || data.startsWith('ENCRYPTED:')) {
                        this.addTestResult(`Backup Format Validation`, true, 'Backup format is valid');
                    } else {
                        this.addTestResult(`Backup Format Validation`, false, 'Invalid backup format');
                    }
                    break;
            }
        } catch (error) {
            this.addTestResult(`${format.toUpperCase()} Format Validation`, false, error.message);
        }
    }
    
    async testImportFormats() {
        console.log('📥 Testing Import Formats...');
        
        // Test JSON import
        await this.testJSONImport();
        await this.testCSVImport();
        await this.testBackupImport();
    }
    
    async testJSONImport() {
        try {
            const jsonData = {
                contacts: [
                    { name: 'Test Contact', phone: '******-0000', email: '<EMAIL>' }
                ],
                messages: [
                    { content: 'Test message', sender: 'Test Contact', timestamp: new Date().toISOString() }
                ],
                photos: [],
                metadata: {
                    importedAt: new Date().toISOString(),
                    version: '1.0'
                }
            };
            
            const mockFile = {
                name: 'test.json',
                size: JSON.stringify(jsonData).length
            };
            
            // Mock file reading
            const originalReadFile = this.manager.readFile;
            this.manager.readFile = async () => JSON.stringify(jsonData);
            
            const result = await this.manager.importBackupData(mockFile);
            
            // Restore original method
            this.manager.readFile = originalReadFile;
            
            if (result.success && result.itemCount > 0) {
                this.addTestResult('JSON Import', true, `Successfully imported ${result.itemCount} items`);
            } else {
                this.addTestResult('JSON Import', false, 'Import failed or no items imported');
            }
            
        } catch (error) {
            this.addTestResult('JSON Import', false, error.message);
        }
    }
    
    async testCSVImport() {
        try {
            const csvData = 'name,phone,email\nTest Contact,******-0000,<EMAIL>\nAnother Contact,******-0001,<EMAIL>';
            
            const mockFile = {
                name: 'test.csv',
                size: csvData.length
            };
            
            // Mock file reading
            const originalReadFile = this.manager.readFile;
            this.manager.readFile = async () => csvData;
            
            const result = await this.manager.importBackupData(mockFile);
            
            // Restore original method
            this.manager.readFile = originalReadFile;
            
            if (result.success && result.itemCount > 0) {
                this.addTestResult('CSV Import', true, `Successfully imported ${result.itemCount} items`);
            } else {
                this.addTestResult('CSV Import', false, 'Import failed or no items imported');
            }
            
        } catch (error) {
            this.addTestResult('CSV Import', false, error.message);
        }
    }
    
    async testBackupImport() {
        try {
            const backupData = {
                header: {
                    format: 'SafeKeep Backup v1.0',
                    created: new Date().toISOString(),
                    encrypted: false,
                    compressed: true
                },
                data: {
                    contacts: [
                        { name: 'Backup Contact', phone: '******-0000', email: '<EMAIL>' }
                    ],
                    messages: [],
                    photos: [],
                    metadata: { version: '1.0' }
                },
                checksum: 'abc123'
            };
            
            const mockFile = {
                name: 'test.skb',
                size: JSON.stringify(backupData).length
            };
            
            // Mock file reading
            const originalReadFile = this.manager.readFile;
            this.manager.readFile = async () => JSON.stringify(backupData);
            
            const result = await this.manager.importBackupData(mockFile);
            
            // Restore original method
            this.manager.readFile = originalReadFile;
            
            if (result.success) {
                this.addTestResult('Backup Import', true, `Successfully imported backup format`);
            } else {
                this.addTestResult('Backup Import', false, 'Backup import failed');
            }
            
        } catch (error) {
            this.addTestResult('Backup Import', false, error.message);
        }
    }
    
    async testDataValidation() {
        console.log('✅ Testing Data Validation...');
        
        // Test valid data
        const validData = {
            contacts: [
                { name: 'Valid Contact', phone: '******-0000', email: '<EMAIL>' }
            ],
            messages: [
                { content: 'Valid message', timestamp: new Date().toISOString() }
            ],
            photos: [
                { filename: 'valid.jpg', data: 'base64data' }
            ]
        };
        
        try {
            const validResult = await this.manager.validateImportData(validData);
            this.addTestResult('Valid Data Validation', validResult.valid, 
                validResult.valid ? 'Valid data passed validation' : `Validation failed: ${validResult.errors.join(', ')}`);
        } catch (error) {
            this.addTestResult('Valid Data Validation', false, error.message);
        }
        
        // Test invalid data
        const invalidData = {
            contacts: [
                { phone: '******-0000', email: '<EMAIL>' } // Missing required 'name' field
            ],
            messages: [
                { timestamp: new Date().toISOString() } // Missing required 'content' field
            ]
        };
        
        try {
            const invalidResult = await this.manager.validateImportData(invalidData);
            this.addTestResult('Invalid Data Validation', !invalidResult.valid, 
                !invalidResult.valid ? 'Invalid data correctly rejected' : 'Invalid data incorrectly accepted');
        } catch (error) {
            this.addTestResult('Invalid Data Validation', false, error.message);
        }
    }
    
    async testCrossPlatformCompatibility() {
        console.log('🌐 Testing Cross-Platform Compatibility...');
        
        try {
            const testData = await this.manager.getBackupSessionData('test-session');
            const compatibilityResults = await this.manager.testCrossPlatformCompatibility(testData);
            
            const platforms = Object.keys(compatibilityResults);
            const allCompatible = platforms.every(platform => compatibilityResults[platform].compatible);
            
            this.addTestResult('Cross-Platform Compatibility', true, 
                `Tested ${platforms.length} platforms: ${platforms.join(', ')}`);
                
            if (allCompatible) {
                this.addTestResult('Platform Compatibility Status', true, 'All platforms are compatible');
            } else {
                const incompatible = platforms.filter(p => !compatibilityResults[p].compatible);
                this.addTestResult('Platform Compatibility Status', false, 
                    `Incompatible platforms: ${incompatible.join(', ')}`);
            }
            
        } catch (error) {
            this.addTestResult('Cross-Platform Compatibility', false, error.message);
        }
    }
    
    async testErrorHandling() {
        console.log('🚨 Testing Error Handling...');
        
        // Test export with invalid session ID
        try {
            await this.manager.exportBackupData('invalid-session-id', 'json');
            this.addTestResult('Invalid Session Export', false, 'Should have thrown an error');
        } catch (error) {
            this.addTestResult('Invalid Session Export', true, 'Correctly handled invalid session ID');
        }
        
        // Test import with invalid format
        try {
            const mockFile = { name: 'test.invalid', size: 100 };
            this.manager.readFile = async () => 'invalid data format';
            
            await this.manager.importBackupData(mockFile);
            this.addTestResult('Invalid Format Import', false, 'Should have thrown an error');
        } catch (error) {
            this.addTestResult('Invalid Format Import', true, 'Correctly handled invalid format');
        }
        
        // Test unsupported export format
        try {
            await this.manager.exportBackupData('test-session', 'unsupported');
            this.addTestResult('Unsupported Export Format', false, 'Should have thrown an error');
        } catch (error) {
            this.addTestResult('Unsupported Export Format', true, 'Correctly handled unsupported format');
        }
    }
    
    async testDataIntegrity() {
        console.log('🔒 Testing Data Integrity...');
        
        try {
            const testData = { test: 'data', number: 123, array: [1, 2, 3] };
            const checksum = this.manager.calculateChecksum(JSON.stringify(testData));
            
            // Test with same data
            const sameChecksum = this.manager.calculateChecksum(JSON.stringify(testData));
            this.addTestResult('Checksum Consistency', checksum === sameChecksum, 
                'Checksum calculation is consistent');
            
            // Test with different data
            const differentData = { test: 'different', number: 456, array: [4, 5, 6] };
            const differentChecksum = this.manager.calculateChecksum(JSON.stringify(differentData));
            this.addTestResult('Checksum Uniqueness', checksum !== differentChecksum, 
                'Different data produces different checksums');
            
            // Test integrity verification
            const integrityResult = await this.manager.verifyDataIntegrity(testData, checksum);
            this.addTestResult('Data Integrity Verification', integrityResult.valid, 
                integrityResult.valid ? 'Data integrity verified' : `Integrity issues: ${integrityResult.issues.join(', ')}`);
            
        } catch (error) {
            this.addTestResult('Data Integrity', false, error.message);
        }
    }
    
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message,
            timestamp: new Date().toISOString()
        });
        
        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}: ${message}`);
    }
    
    printResults() {
        console.log('\n📊 Test Results Summary');
        console.log('=' .repeat(50));
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests} ✅`);
        console.log(`Failed: ${failedTests} ❌`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults.filter(r => !r.passed).forEach(test => {
                console.log(`  - ${test.name}: ${test.message}`);
            });
        }
        
        console.log('\n🎉 Data Export/Import verification completed!');
        
        // Exit with appropriate code
        process.exit(failedTests > 0 ? 1 : 0);
    }
}

// Helper function to generate mock backup session
function generateMockBackupSession() {
    return {
        id: 'test-session',
        user_id: 'test-user',
        status: 'completed',
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        total_items: 100,
        completed_items: 100,
        failed_items: 0
    };
}

// Run verification if this script is executed directly
if (require.main === module) {
    const verifier = new DataExportImportVerifier();
    verifier.runAllTests().catch(error => {
        console.error('Verification failed:', error);
        process.exit(1);
    });
}

module.exports = DataExportImportVerifier;