#!/usr/bin/env node

/**
 * Startup script for SafeKeep Stripe Server
 * Starts the Stripe integration server for the web demo
 */

const StripeConfig = require('./stripe-config.js');
const StripeServer = require('./stripe-server.js');

async function startStripeServer() {
    console.log('🚀 Starting SafeKeep Stripe Server...');
    console.log('=' .repeat(50));
    
    try {
        // Initialize configuration
        const config = new StripeConfig();
        const validation = config.validateConfig();
        
        if (!validation.valid) {
            console.error('❌ Configuration validation failed:');
            validation.errors.forEach(error => {
                console.error(`  • ${error}`);
            });
            process.exit(1);
        }
        
        console.log('✅ Configuration validated successfully');
        
        // Initialize and start server
        const server = new StripeServer(config);
        const httpServer = server.start(3001);
        
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Shutting down Stripe Server...');
            server.stop();
            process.exit(0);
        });
        
        process.on('SIGTERM', () => {
            console.log('\n🛑 Shutting down Stripe Server...');
            server.stop();
            process.exit(0);
        });
        
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error('💥 Uncaught Exception:', error);
            server.stop();
            process.exit(1);
        });
        
        process.on('unhandledRejection', (reason, promise) => {
            console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
            server.stop();
            process.exit(1);
        });
        
        console.log('\n🎯 Stripe Server Features:');
        console.log('• Customer management');
        console.log('• Payment intent processing');
        console.log('• Subscription management');
        console.log('• Webhook event handling');
        console.log('• Demo data simulation');
        console.log('\n📝 Activity will be logged here...\n');
        
    } catch (error) {
        console.error('❌ Failed to start Stripe Server:', error.message);
        process.exit(1);
    }
}

// Handle command line arguments
const args = process.argv.slice(2);
const portIndex = args.indexOf('--port');
let port = 3001;

if (portIndex !== -1 && args[portIndex + 1]) {
    const customPort = parseInt(args[portIndex + 1]);
    if (!isNaN(customPort)) {
        port = customPort;
    }
}

// Start the server
startStripeServer().catch(error => {
    console.error('💥 Startup failed:', error);
    process.exit(1);
});