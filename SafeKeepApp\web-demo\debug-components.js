/**
 * Debug Component Loading
 * Check which components are loaded and working
 */

function debugComponents() {
    console.log('🔍 Debugging Component Loading...');
    
    const components = [
        'AuthManager',
        'RealtimeProgressManager', 
        'BackupConsole',
        'EncryptionDemo',
        'BackupHistoryManager',
        'BackupHistoryDashboard',
        'RestoreManager',
        'RestoreSimulation',
        'ScheduleManager',
        'ScheduleConsole'
    ];
    
    console.log('\n📦 Checking component classes:');
    components.forEach(component => {
        const available = typeof window[component] !== 'undefined';
        console.log(`   ${component}: ${available ? '✅' : '❌'}`);
        
        if (!available) {
            console.log(`      Missing: window.${component}`);
        }
    });
    
    console.log('\n🏗️ Checking DOM containers:');
    const containers = [
        'backup-console-container',
        'encryption-demo-container', 
        'backup-history-dashboard-container',
        'restore-simulation-container',
        'schedule-console-container'
    ];
    
    containers.forEach(containerId => {
        const element = document.getElementById(containerId);
        console.log(`   ${containerId}: ${element ? '✅' : '❌'}`);
        
        if (element) {
            console.log(`      Content: ${element.innerHTML.length > 0 ? 'Has content' : 'Empty'}`);
        }
    });
    
    console.log('\n🔗 Checking global variables:');
    const globals = ['supabase', 'adminSupabase', 'authManager', 'backupConsole', 'encryptionDemo', 'backupHistoryDashboard', 'restoreSimulation', 'scheduleConsole'];
    
    globals.forEach(global => {
        const available = typeof window[global] !== 'undefined';
        console.log(`   ${global}: ${available ? '✅' : '❌'}`);
    });
    
    console.log('\n🚨 Checking for JavaScript errors:');
    const originalError = window.onerror;
    const errors = [];
    
    window.onerror = function(message, source, lineno, colno, error) {
        errors.push({ message, source, lineno, colno, error });
        if (originalError) originalError.apply(this, arguments);
    };
    
    setTimeout(() => {
        if (errors.length > 0) {
            console.log('   Found errors:');
            errors.forEach(err => {
                console.log(`   ❌ ${err.message} at ${err.source}:${err.lineno}`);
            });
        } else {
            console.log('   ✅ No JavaScript errors detected');
        }
    }, 1000);
    
    console.log('\n🧪 Testing component initialization:');
    
    // Test if we can create instances
    try {
        if (window.ScheduleManager && typeof supabase !== 'undefined') {
            const testManager = new window.ScheduleManager(supabase, adminSupabase);
            console.log('   ✅ ScheduleManager can be instantiated');
        } else {
            console.log('   ❌ ScheduleManager cannot be instantiated');
        }
    } catch (error) {
        console.log('   ❌ ScheduleManager error:', error.message);
    }
    
    try {
        if (window.ScheduleConsole) {
            const testConsole = new window.ScheduleConsole('schedule-console-container');
            console.log('   ✅ ScheduleConsole can be instantiated');
        } else {
            console.log('   ❌ ScheduleConsole cannot be instantiated');
        }
    } catch (error) {
        console.log('   ❌ ScheduleConsole error:', error.message);
    }
    
    console.log('\n📋 Summary:');
    console.log('   If components show ❌, check script loading order');
    console.log('   If containers show ❌, check HTML structure');
    console.log('   If globals show ❌, check initialization sequence');
    console.log('   If errors are found, fix JavaScript issues first');
}

// Auto-run debug when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(debugComponents, 2000);
    });
} else {
    setTimeout(debugComponents, 2000);
}

// Export for manual use
window.debugComponents = debugComponents;