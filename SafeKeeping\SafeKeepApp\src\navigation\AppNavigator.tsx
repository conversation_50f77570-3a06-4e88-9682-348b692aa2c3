import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { OnboardingScreen, DashboardScreen, SettingsScreen, BackupScreen } from '../screens';
import PermissionSetupScreen from '../screens/Permissions/PermissionSetupScreen';
import AuthScreen from '../screens/Auth/AuthScreen';
import SubscriptionScreen from '../screens/Subscription/SubscriptionScreen';
import WelcomeScreen from '../screens/Welcome/WelcomeScreen';
import AuthService from '../services/AuthService';
import { COLORS } from '../utils/constants';

export type RootStackParamList = {
  Welcome: undefined;
  Auth: undefined;
  Onboarding: undefined;
  PermissionSetup: undefined;
  Main: undefined;
  Subscription: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Backup: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Backup':
              iconName = 'backup';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.primary,
        tabBarInactiveTintColor: COLORS.textSecondary,
        headerShown: false,
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} />
      <Tab.Screen name="Backup" component={BackupScreen} />
      <Tab.Screen name="Settings" component={SettingsScreen} />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeApp();

    // Listen for auth state changes
    AuthService.addAuthStateListener((user) => {
      setIsAuthenticated(!!user);
      console.log('Auth state changed:', !!user);
    });
  }, []);

  const initializeApp = async () => {
    try {
      console.log('🚀 Initializing SafeKeep app...');

      // Check onboarding status
      const hasCompletedOnboarding = await AsyncStorage.getItem('hasCompletedOnboarding');
      setIsOnboardingComplete(!!hasCompletedOnboarding);

      // Check authentication status
      const currentUser = AuthService.getCurrentUser();
      setIsAuthenticated(!!currentUser);

      console.log('📊 App state:', {
        onboardingComplete: !!hasCompletedOnboarding,
        authenticated: !!currentUser
      });
    } catch (error) {
      console.error('❌ Error initializing app:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading screen while initializing
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Loading SafeKeep...</Text>
      </View>
    );
  }

  // Determine initial route based on app state
  const getInitialRouteName = (): keyof RootStackParamList => {
    if (!isAuthenticated) {
      return 'Welcome';
    }
    if (!isOnboardingComplete) {
      return 'Onboarding';
    }
    return 'Main';
  };

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{ headerShown: false }}
        initialRouteName={getInitialRouteName()}
      >
        <Stack.Screen name="Welcome" component={WelcomeScreen} />
        <Stack.Screen name="Auth" component={AuthScreen} />
        <Stack.Screen name="Onboarding" component={OnboardingScreen} />
        <Stack.Screen name="PermissionSetup" component={PermissionSetupScreen} />
        <Stack.Screen name="Subscription" component={SubscriptionScreen} />
        <Stack.Screen name="Main" component={MainTabNavigator} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.text,
  },
});

export default AppNavigator;
