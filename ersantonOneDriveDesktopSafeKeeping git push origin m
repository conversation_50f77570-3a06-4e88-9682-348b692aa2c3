warning: in the working copy of 'SafeKeepApp/src/components/backup/index.ts', LF will be replaced by CR<PERSON> the next time Git touches it
warning: in the working copy of 'SafeKeepApp/src/screens/Backup/BackupScreen.tsx', LF will be replaced by CR<PERSON> the next time Git touches it
warning: in the working copy of 'SafeKeepApp/src/services/ContactBackupService.ts', LF will be replaced by CR<PERSON> the next time Git touches it
warning: in the working copy of 'SafeKeepApp/src/services/MessageBackupService.ts', LF will be replaced by CR<PERSON> the next time Git touches it
warning: in the working copy of 'SafeKeepApp/src/services/PhotoBackupService.ts', LF will be replaced by CR<PERSON> the next time Git touches it
warning: in the working copy of 'SafeKeepApp/src/services/index.ts', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SafeKeepApp/src/store/slices/backupSlice.ts', LF will be replaced by <PERSON><PERSON> the next time Git touches it
warning: in the working copy of 'SafeKeepApp/src/utils/types.ts', <PERSON><PERSON> will be replaced by <PERSON><PERSON> the next time Git touches it
[1mdiff --git a/SafeKeepApp/src/components/backup/index.ts b/SafeKeepApp/src/components/backup/index.ts[m
[1mindex 0321aa8..e285836 100644[m
[1m--- a/SafeKeepApp/src/components/backup/index.ts[m
[1m+++ b/SafeKeepApp/src/components/backup/index.ts[m
[36m@@ -2,3 +2,5 @@[m
 export { default as BackupProgress } from './BackupProgress';[m
 export { default as PhotoGrid } from './PhotoGrid';[m
 export { default as BackupSettings } from './BackupSettings';[m
[32m+[m[32mexport { default as BackupDashboard } from './BackupDashboard';[m
[32m+[m[32mexport { default as BackupProgressScreen } from './BackupProgressScreen';[m
[1mdiff --git a/SafeKeepApp/src/screens/Backup/BackupScreen.tsx b/SafeKeepApp/src/screens/Backup/BackupScreen.tsx[m
[1mindex 89378aa..2d1f146 100644[m
[1m--- a/SafeKeepApp/src/screens/Backup/BackupScreen.tsx[m
[1m+++ b/SafeKeepApp/src/screens/Backup/BackupScreen.tsx[m
[36m@@ -13,16 +13,34 @@[m [mimport { COLORS, FONTS, SPACING } from '../../utils/constants';[m
 import PhotoBackupScreen from './PhotoBackupScreen';[m
 import ContactBackupScreen from './ContactBackupScreen';[m
 import MessageBackupScreen from './MessageBackupScreen';[m
[32m+[m[32mimport { BackupDashboard, BackupProgressScreen } from '../../components/backup';[m
[32m+[m[32mimport { useIsBackupInProgress } from '../../store/hooks/backupHooks';[m
 [m
 const BackupScreen = () => {[m
[31m-  const [selectedTab, setSelectedTab] = useState('photos');[m
[32m+[m[32m  const [selectedTab, setSelectedTab] = useState('dashboard');[m
[32m+[m[32m  const [showProgressScreen, setShowProgressScreen] = useState(false);[m
   const dispatch = useDispatch();[m
   const { status, photos, contacts } = useSelector([m
     (state: RootState) => state.backup[m
   );[m
[32m+[m[32m  const isBackupInProgress = useIsBackupInProgress();[m
[32m+[m
[32m+[m[32m  const handleNavigateToProgress = () => {[m
[32m+[m[32m    setShowProgressScreen(true);[m
[32m+[m[32m  };[m
[32m+[m
[32m+[m[32m  const handleNavigateBack = () => {[m
[32m+[m[32m    setShowProgressScreen(false);[m
[32m+[m[32m  };[m
 [m
   const renderTabContent = () => {[m
[32m+[m[32m    if (showProgressScreen) {[m
[32m+[m[32m      return <BackupProgressScreen onNavigateBack={handleNavigateBack} />;[m
[32m+[m[32m    }[m
[32m+[m
     switch (selectedTab) {[m
[32m+[m[32m      case 'dashboard':[m
[32m+[m[32m        return <BackupDashboard onNavigateToProgress={handleNavigateToProgress} />;[m
       case 'photos':[m
         return <PhotoBackupScreen />;[m
       case 'contacts':[m
[36m@@ -30,7 +48,7 @@[m [mconst BackupScreen = () => {[m
       case 'messages':[m
         return <MessageBackupScreen />;[m
       default:[m
[31m-        return <PhotoBackupScreen />;[m
[32m+[m[32m        return <BackupDashboard onNavigateToProgress={handleNavigateToProgress} />;[m
     }[m
   };[m
 [m
[36m@@ -49,6 +67,11 @@[m [mconst BackupScreen = () => {[m
           value={selectedTab}[m
           onValueChange={setSelectedTab}[m
           buttons={[[m
[32m+[m[32m            {[m
[32m+[m[32m              value: 'dashboard',[m
[32m+[m[32m              label: 'Dashboard',[m
[32m+[m[32m              icon: 'view-dashboard',[m
[32m+[m[32m            },[m
             {[m
               value: 'photos',[m
               label: 'Photos',[m
[1mdiff --git a/SafeKeepApp/src/services/ContactBackupService.ts b/SafeKeepApp/src/services/ContactBackupService.ts[m
[1mindex e38bae6..7afcd3f 100644[m
[1m--- a/SafeKeepApp/src/services/ContactBackupService.ts[m
[1m+++ b/SafeKeepApp/src/services/ContactBackupService.ts[m
[36m@@ -2,6 +2,8 @@[m [mimport { Platform, PermissionsAndroid } from 'react-native';[m
 import Contacts from 'react-native-contacts';[m
 import CloudStorageService from './CloudStorageService';[m
 import AuthService from './AuthService';[m
[32m+[m[32mimport EncryptionService from './EncryptionService';[m
[32m+[m[32mimport { BackupServiceResult, BackupError } from '../types/backup';[m
 [m
 export interface ContactData {[m
   recordID: string;[m
[36m@@ -171,11 +173,11 @@[m [mclass ContactBackupService {[m
     return uniqueContacts;[m
   }[m
 [m
[31m-  // Backup contacts to cloud storage[m
[32m+[m[32m  // Enhanced backup contacts with encryption and improved error handling[m
   async backupContacts([m
     contacts: ContactData[],[m
     onProgress?: (progress: ContactBackupProgress) => void[m
[31m-  ): Promise<ContactBackupResult> {[m
[32m+[m[32m  ): Promise<BackupServiceResult> {[m
     if (this.isBackupRunning) {[m
       throw new Error('Contact backup is already running');[m
     }[m
[36m@@ -183,7 +185,14 @@[m [mclass ContactBackupService {[m
     // Check authentication[m
     const user = AuthService.getCurrentUser();[m
     if (!user) {[m
[31m-      throw new Error('User authentication required for backup');[m
[32m+[m[32m      const error: BackupError = {[m
[32m+[m[32m        id: `contact_auth_${Date.now()}`,[m
[32m+[m[32m        type: 'permission',[m
[32m+[m[32m        message: 'User authentication required for backup',[m
[32m+[m[32m        timestamp: new Date(),[m
[32m+[m[32m        retryable: true[m
[32m+[m[32m      };[m
[32m+[m[32m      return { success: false, itemsProcessed: 0, errors: [error] };[m
     }[m
 [m
     this.isBackupRunning = true;[m
[36m@@ -192,19 +201,31 @@[m [mclass ContactBackupService {[m
 [m
     const startTime = Date.now();[m
     let backedUpContacts = 0;[m
[31m-    let skippedContacts = 0;[m
[31m-    const errors: string[] = [];[m
[32m+[m[32m    const errors: BackupError[] = [];[m
 [m
     try {[m
       console.log(`🚀 Starting backup of ${contacts.length} contacts...`);[m
 [m
[31m-      // Step 1: Detect duplicates[m
[32m+[m[32m      // Step 1: Request permissions[m
[32m+[m[32m      const hasPermission = await this.requestContactsPermission();[m
[32m+[m[32m      if (!hasPermission) {[m
[32m+[m[32m        const error: BackupError = {[m
[32m+[m[32m          id: `contact_permission_${Date.now()}`,[m
[32m+[m[32m          type: 'permission',[m
[32m+[m[32m          message: 'Contacts permission denied by user',[m
[32m+[m[32m          timestamp: new Date(),[m
[32m+[m[32m          retryable: true[m
[32m+[m[32m        };[m
[32m+[m[32m        return { success: false, itemsProcessed: 0, errors: [error] };[m
[32m+[m[32m      }[m
[32m+[m
[32m+[m[32m      // Step 2: Detect duplicates[m
       if (onProgress) {[m
         onProgress({[m
           totalContacts: contacts.length,[m
           processedContacts: 0,[m
           currentContact: 'Detecting duplicates...',[m
[31m-          percentage: 0,[m
[32m+[m[32m          percentage: 10,[m
           status: 'processing'[m
         });[m
       }[m
[36m@@ -212,40 +233,83 @@[m [mclass ContactBackupService {[m
       const uniqueContacts = this.detectDuplicates(contacts);[m
       const 