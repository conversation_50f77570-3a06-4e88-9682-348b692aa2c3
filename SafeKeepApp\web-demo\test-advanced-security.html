<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep - Advanced Security Demonstrations</title>
    <link rel="stylesheet" href="advanced-security-styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 3rem;
            margin: 0 0 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin: 0;
        }

        .demo-navigation {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .demo-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #4facfe;
        }

        .demo-card h3 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.3rem;
        }

        .demo-card p {
            color: #666;
            margin: 0;
            line-height: 1.5;
        }

        .demo-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .demo-content {
            display: none;
            margin-top: 20px;
        }

        .demo-content.active {
            display: block;
        }

        .security-dashboard {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .dashboard-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .dashboard-title h2 {
            color: #333;
            font-size: 2rem;
            margin: 0 0 10px 0;
        }

        .dashboard-title p {
            color: #666;
            margin: 0;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ SafeKeep Security Center</h1>
            <p>Advanced Security Demonstrations & Education Platform</p>
        </div>

        <div class="security-dashboard">
            <div class="dashboard-title">
                <h2>Security Overview</h2>
                <p>Real-time security metrics and system status</p>
            </div>
            <div id="security-dashboard">
                <!-- Security metrics will be populated here -->
            </div>
        </div>

        <div class="demo-navigation">
            <div class="demo-card" data-demo="zero-knowledge">
                <span class="demo-icon">🔐</span>
                <h3>Zero-Knowledge Encryption</h3>
                <p>Experience how SafeKeep encrypts your data without ever seeing it. Learn about client-side encryption and key management.</p>
            </div>

            <div class="demo-card" data-demo="audit-trail">
                <span class="demo-icon">🔍</span>
                <h3>Security Audit Trail</h3>
                <p>Explore comprehensive security event tracking and visualization. See how SafeKeep monitors and logs all security activities.</p>
            </div>

            <div class="demo-card" data-demo="penetration-test">
                <span class="demo-icon">🎯</span>
                <h3>Penetration Testing</h3>
                <p>Watch SafeKeep's security withstand simulated attacks. See how our defenses protect against real-world threats.</p>
            </div>

            <div class="demo-card" data-demo="compliance-report">
                <span class="demo-icon">📋</span>
                <h3>Compliance Reporting</h3>
                <p>Generate compliance reports for GDPR, HIPAA, SOC 2, and other frameworks. Understand regulatory requirements.</p>
            </div>

            <div class="demo-card" data-demo="security-education">
                <span class="demo-icon">🎓</span>
                <h3>Security Education</h3>
                <p>Learn security best practices through interactive tutorials and hands-on exercises. Build your security knowledge.</p>
            </div>
        </div>

        <!-- Demo Content Areas -->
        <div id="zero-knowledge-demo" class="demo-content">
            <!-- Zero-knowledge demo will be populated here -->
        </div>

        <div id="audit-trail-demo" class="demo-content">
            <!-- Audit trail demo will be populated here -->
        </div>

        <div id="penetration-test-demo" class="demo-content">
            <!-- Penetration test demo will be populated here -->
        </div>

        <div id="compliance-report-demo" class="demo-content">
            <!-- Compliance report demo will be populated here -->
        </div>

        <div id="security-education-demo" class="demo-content">
            <!-- Security education demo will be populated here -->
        </div>

        <div class="footer">
            <p>&copy; 2024 SafeKeep. Advanced Security Demonstrations. All rights reserved.</p>
        </div>
    </div>

    <!-- Include all the security demo modules -->
    <script src="zero-knowledge-crypto.js"></script>
    <script src="security-audit-trail.js"></script>
    <script src="penetration-test-simulator.js"></script>
    <script src="compliance-reporter.js"></script>
    <script src="security-education.js"></script>
    <script src="advanced-security-demo.js"></script>

    <script>
        // Initialize the demo when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Advanced Security Demonstrations loaded successfully');
            
            // Set up demo card interactions
            document.querySelectorAll('.demo-card').forEach(card => {
                card.addEventListener('click', function() {
                    // Hide all demo content
                    document.querySelectorAll('.demo-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // Show selected demo content
                    const demoType = this.dataset.demo;
                    const demoContent = document.getElementById(demoType + '-demo');
                    if (demoContent) {
                        demoContent.classList.add('active');
                        demoContent.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // Add compliance report type buttons
            document.addEventListener('click', function(e) {
                if (e.target.matches('[data-demo="compliance-report"]')) {
                    // Add compliance type selection
                    const complianceDemo = document.getElementById('compliance-report-demo');
                    if (complianceDemo && !complianceDemo.querySelector('.compliance-selector')) {
                        complianceDemo.innerHTML = `
                            <div class="compliance-selector" style="text-align: center; margin: 20px 0;">
                                <h3>Select Compliance Framework</h3>
                                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin-top: 20px;">
                                    <button class="btn-demo" data-demo="compliance-report" data-type="gdpr">GDPR Report</button>
                                    <button class="btn-demo" data-demo="compliance-report" data-type="hipaa">HIPAA Report</button>
                                    <button class="btn-demo" data-demo="compliance-report" data-type="soc2">SOC 2 Report</button>
                                    <button class="btn-demo" data-demo="compliance-report" data-type="iso27001">ISO 27001 Report</button>
                                </div>
                            </div>
                        `;
                    }
                }
            });

            // Test the advanced security demo initialization
            if (typeof advancedSecurityDemo !== 'undefined') {
                console.log('Advanced Security Demo initialized successfully');
            } else {
                console.error('Failed to initialize Advanced Security Demo');
            }
        });

        // Add some demo data for testing
        window.addEventListener('load', function() {
            // Simulate some demo interactions for testing
            setTimeout(() => {
                console.log('Running security demo tests...');
                
                // Test zero-knowledge crypto
                if (typeof ZeroKnowledgeCrypto !== 'undefined') {
                    const crypto = new ZeroKnowledgeCrypto();
                    console.log('Zero-Knowledge Crypto module loaded');
                }
                
                // Test audit trail
                if (typeof SecurityAuditTrail !== 'undefined') {
                    const audit = new SecurityAuditTrail();
                    console.log('Security Audit Trail module loaded');
                }
                
                // Test penetration testing
                if (typeof PenetrationTestSimulator !== 'undefined') {
                    const pentest = new PenetrationTestSimulator();
                    console.log('Penetration Test Simulator module loaded');
                }
                
                // Test compliance reporter
                if (typeof ComplianceReporter !== 'undefined') {
                    const compliance = new ComplianceReporter();
                    console.log('Compliance Reporter module loaded');
                }
                
                // Test security education
                if (typeof SecurityEducation !== 'undefined') {
                    const education = new SecurityEducation();
                    console.log('Security Education module loaded');
                }
                
                console.log('All security demo modules loaded successfully!');
            }, 1000);
        });
    </script>
</body>
</html>