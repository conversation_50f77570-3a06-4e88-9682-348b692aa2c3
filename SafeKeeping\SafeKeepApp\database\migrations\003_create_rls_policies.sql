-- Migration script to create comprehensive RLS policies for all tables
-- This implements the performance best practice of using subqueries for auth functions

-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storage_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sync_status ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Users can delete own profile" ON public.users;

DROP POLICY IF EXISTS "Users can view own files" ON public.file_metadata;
DROP POLICY IF EXISTS "Users can insert own files" ON public.file_metadata;
DROP POLICY IF EXISTS "Users can update own files" ON public.file_metadata;
DROP POLICY IF EXISTS "Users can delete own files" ON public.file_metadata;

DROP POLICY IF EXISTS "Users can view own backup sessions" ON public.backup_sessions;
DROP POLICY IF EXISTS "Users can insert own backup sessions" ON public.backup_sessions;
DROP POLICY IF EXISTS "Users can update own backup sessions" ON public.backup_sessions;
DROP POLICY IF EXISTS "Users can delete own backup sessions" ON public.backup_sessions;

DROP POLICY IF EXISTS "Users can view own storage usage" ON public.storage_usage;
DROP POLICY IF EXISTS "Users can insert own storage usage" ON public.storage_usage;
DROP POLICY IF EXISTS "Users can update own storage usage" ON public.storage_usage;
DROP POLICY IF EXISTS "Users can delete own storage usage" ON public.storage_usage;

DROP POLICY IF EXISTS "Users can view own sync status" ON public.sync_status;
DROP POLICY IF EXISTS "Users can insert own sync status" ON public.sync_status;
DROP POLICY IF EXISTS "Users can update own sync status" ON public.sync_status;
DROP POLICY IF EXISTS "Users can delete own sync status" ON public.sync_status;

-- Create optimized policies for users table
-- Note: In the users table, the primary key column is 'id', not 'user_id'
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (id = (SELECT auth.uid()));

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (id = (SELECT auth.uid()));

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (id = (SELECT auth.uid()));

CREATE POLICY "Users can delete own profile" ON public.users
    FOR DELETE USING (id = (SELECT auth.uid()));

-- Create optimized policies for file_metadata table
CREATE POLICY "Users can view own files" ON public.file_metadata
    FOR SELECT USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can insert own files" ON public.file_metadata
    FOR INSERT WITH CHECK (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can update own files" ON public.file_metadata
    FOR UPDATE USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can delete own files" ON public.file_metadata
    FOR DELETE USING (user_id = (SELECT auth.uid()));

-- Create optimized policies for backup_sessions table
CREATE POLICY "Users can view own backup sessions" ON public.backup_sessions
    FOR SELECT USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can insert own backup sessions" ON public.backup_sessions
    FOR INSERT WITH CHECK (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can update own backup sessions" ON public.backup_sessions
    FOR UPDATE USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can delete own backup sessions" ON public.backup_sessions
    FOR DELETE USING (user_id = (SELECT auth.uid()));

-- Create optimized policies for storage_usage table
CREATE POLICY "Users can view own storage usage" ON public.storage_usage
    FOR SELECT USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can insert own storage usage" ON public.storage_usage
    FOR INSERT WITH CHECK (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can update own storage usage" ON public.storage_usage
    FOR UPDATE USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can delete own storage usage" ON public.storage_usage
    FOR DELETE USING (user_id = (SELECT auth.uid()));

-- Create optimized policies for sync_status table
CREATE POLICY "Users can view own sync status" ON public.sync_status
    FOR SELECT USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can insert own sync status" ON public.sync_status
    FOR INSERT WITH CHECK (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can update own sync status" ON public.sync_status
    FOR UPDATE USING (user_id = (SELECT auth.uid()));

CREATE POLICY "Users can delete own sync status" ON public.sync_status
    FOR DELETE USING (user_id = (SELECT auth.uid()));

-- Create special policies for admin access (if needed)
-- These policies would allow designated admin users to access all data
-- Uncomment and modify as needed for admin functionality

/*
-- Create admin role if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'admin') THEN
    CREATE ROLE admin;
  END IF;
END $$;

-- Admin policies for users table
CREATE POLICY "Admins can view all profiles" ON public.users
    FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');

-- Admin policies for file_metadata table
CREATE POLICY "Admins can view all files" ON public.file_metadata
    FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');

-- Admin policies for backup_sessions table
CREATE POLICY "Admins can view all backup sessions" ON public.backup_sessions
    FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');

-- Admin policies for storage_usage table
CREATE POLICY "Admins can view all storage usage" ON public.storage_usage
    FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');

-- Admin policies for sync_status table
CREATE POLICY "Admins can view all sync status" ON public.sync_status
    FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');
*/

-- Create policies for the user_storage_summary view
CREATE POLICY "Users can view own storage summary" ON public.user_storage_summary
    FOR SELECT USING (id = (SELECT auth.uid()));

-- Add comments explaining the optimization
COMMENT ON TABLE public.users IS 'User profiles with optimized RLS policies';
COMMENT ON TABLE public.file_metadata IS 'File metadata with optimized RLS policies';
COMMENT ON TABLE public.backup_sessions IS 'Backup sessions with optimized RLS policies';
COMMENT ON TABLE public.storage_usage IS 'Storage usage tracking with optimized RLS policies';
COMMENT ON TABLE public.sync_status IS 'Sync status tracking with optimized RLS policies';

-- Log policy creation for auditing
DO $$ 
BEGIN
    RAISE NOTICE 'RLS policies created successfully for all tables';
    RAISE NOTICE 'Policies use optimized subquery format for better performance';
END $$;