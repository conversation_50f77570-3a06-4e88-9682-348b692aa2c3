-- S<PERSON> functions for file metadata management

-- Function to find duplicate files based on hash comparison
CREATE OR REPLACE FUNCTION find_duplicate_files(user_id_param UUID)
RETURNS TABLE (
  hash TEXT,
  files JSONB
) AS $
BEGIN
  RETURN QUERY
  WITH duplicate_hashes AS (
    SELECT 
      hash,
      COUNT(*) as file_count
    FROM 
      file_metadata
    WHERE 
      user_id = user_id_param
    GROUP BY 
      hash
    HAVING 
      COUNT(*) > 1
  )
  SELECT 
    dh.hash,
    jsonb_agg(
      jsonb_build_object(
        'id', fm.id,
        'original_name', fm.original_name,
        'size', fm.size,
        'category', fm.category,
        'uploaded_at', fm.uploaded_at,
        'storage_path', fm.storage_path
      )
    ) as files
  FROM 
    duplicate_hashes dh
  JOIN 
    file_metadata fm ON dh.hash = fm.hash
  WHERE 
    fm.user_id = user_id_param
  GROUP BY 
    dh.hash;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION find_duplicate_files(UUID) TO authenticated;

-- Function to find duplicate files by category
CREATE OR REPLACE FUNCTION find_duplicate_files_by_category(
  user_id_param UUID,
  category_param TEXT
)
RETURNS TABLE (
  hash TEXT,
  files JSONB
) AS $
BEGIN
  RETURN QUERY
  WITH duplicate_hashes AS (
    SELECT 
      hash,
      COUNT(*) as file_count
    FROM 
      file_metadata
    WHERE 
      user_id = user_id_param
      AND category = category_param
    GROUP BY 
      hash
    HAVING 
      COUNT(*) > 1
  )
  SELECT 
    dh.hash,
    jsonb_agg(
      jsonb_build_object(
        'id', fm.id,
        'original_name', fm.original_name,
        'size', fm.size,
        'category', fm.category,
        'uploaded_at', fm.uploaded_at,
        'storage_path', fm.storage_path
      )
    ) as files
  FROM 
    duplicate_hashes dh
  JOIN 
    file_metadata fm ON dh.hash = fm.hash
  WHERE 
    fm.user_id = user_id_param
    AND fm.category = category_param
  GROUP BY 
    dh.hash;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION find_duplicate_files_by_category(UUID, TEXT) TO authenticated;

-- Function to get file statistics by category
CREATE OR REPLACE FUNCTION get_file_statistics(user_id_param UUID)
RETURNS JSONB AS $
DECLARE
  result JSONB;
BEGIN
  SELECT 
    jsonb_build_object(
      'totalFiles', COUNT(*),
      'totalSize', SUM(size),
      'byCategory', jsonb_object_agg(
        category, 
        jsonb_build_object(
          'count', COUNT(*),
          'size', SUM(size)
        )
      ),
      'byDate', jsonb_object_agg(
        TO_CHAR(uploaded_at, 'YYYY-MM-DD'),
        jsonb_build_object(
          'count', COUNT(*),
          'size', SUM(size)
        )
      )
    ) INTO result
  FROM (
    SELECT 
      category,
      size,
      uploaded_at
    FROM 
      file_metadata
    WHERE 
      user_id = user_id_param
  ) AS stats
  GROUP BY category, TO_CHAR(uploaded_at, 'YYYY-MM-DD');
  
  RETURN result;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_file_statistics(UUID) TO authenticated;

-- Function to add a virtual folder to user preferences
CREATE OR REPLACE FUNCTION add_virtual_folder(user_id_param UUID, folder_path_param TEXT)
RETURNS VOID AS $
DECLARE
  current_folders TEXT[];
BEGIN
  -- Get current folders from user preferences
  SELECT COALESCE(preferences->'virtual_folders', '[]'::jsonb)::TEXT[] 
  INTO current_folders 
  FROM users 
  WHERE id = user_id_param;
  
  -- Add new folder if it doesn't exist
  IF NOT folder_path_param = ANY(current_folders) THEN
    -- Update user preferences
    UPDATE users 
    SET preferences = jsonb_set(
      COALESCE(preferences, '{}'::jsonb),
      '{virtual_folders}',
      COALESCE(preferences->'virtual_folders', '[]'::jsonb) || to_jsonb(folder_path_param)
    )
    WHERE id = user_id_param;
  END IF;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION add_virtual_folder(UUID, TEXT) TO authenticated;

-- Function to get virtual folders
CREATE OR REPLACE FUNCTION get_virtual_folders(user_id_param UUID)
RETURNS TABLE (
  id TEXT,
  name TEXT,
  path TEXT,
  parent_id TEXT,
  file_count INTEGER
) AS $
DECLARE
  folder_paths TEXT[];
  folder_record RECORD;
  folder_id TEXT;
  folder_name TEXT;
  parent_path TEXT;
  parent_id TEXT;
  file_count INTEGER;
  i INTEGER := 1;
BEGIN
  -- Get folder paths from user preferences
  SELECT COALESCE(preferences->'virtual_folders', '[]'::jsonb)::TEXT[] 
  INTO folder_paths 
  FROM users 
  WHERE id = user_id_param;
  
  -- Process each folder
  FOREACH folder_path IN ARRAY folder_paths LOOP
    -- Extract folder name (last part of path)
    folder_name := split_part(folder_path, '/', array_length(string_to_array(folder_path, '/'), 1));
    
    -- Calculate parent path
    parent_path := substring(folder_path from 1 for length(folder_path) - length(folder_name) - 1);
    
    -- Generate IDs
    folder_id := 'folder-' || i;
    IF parent_path = '' THEN
      parent_id := NULL;
    ELSE
      -- Find parent ID (simplified approach)
      parent_id := 'folder-' || (i - 1);
    END IF;
    
    -- Count files in this folder
    SELECT COUNT(*) INTO file_count 
    FROM file_metadata 
    WHERE user_id = user_id_param 
    AND storage_path LIKE folder_path || '/%';
    
    -- Return the folder record
    id := folder_id;
    name := folder_name;
    path := folder_path;
    RETURN NEXT;
    
    i := i + 1;
  END LOOP;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_virtual_folders(UUID) TO authenticated;

-- Function to search files with advanced filters
CREATE OR REPLACE FUNCTION search_files(
  user_id_param UUID,
  category_param TEXT DEFAULT NULL,
  search_term TEXT DEFAULT NULL,
  min_size BIGINT DEFAULT NULL,
  max_size BIGINT DEFAULT NULL,
  start_date TIMESTAMPTZ DEFAULT NULL,
  end_date TIMESTAMPTZ DEFAULT NULL,
  tags TEXT[] DEFAULT NULL,
  is_favorite BOOLEAN DEFAULT NULL,
  limit_param INTEGER DEFAULT 20,
  offset_param INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  original_name TEXT,
  encrypted_name TEXT,
  mime_type TEXT,
  size BIGINT,
  encrypted_size BIGINT,
  category TEXT,
  hash TEXT,
  storage_path TEXT,
  uploaded_at TIMESTAMPTZ,
  last_modified TIMESTAMPTZ,
  tags TEXT[],
  favorite BOOLEAN,
  total_count BIGINT
) AS $
DECLARE
  query_text TEXT;
  count_query_text TEXT;
  total_count_var BIGINT;
BEGIN
  -- Build the base query
  query_text := 'SELECT id, original_name, encrypted_name, mime_type, size, encrypted_size, 
                category, hash, storage_path, uploaded_at, last_modified, tags, favorite
                FROM file_metadata
                WHERE user_id = $1';
                
  -- Build the count query
  count_query_text := 'SELECT COUNT(*) FROM file_metadata WHERE user_id = $1';
  
  -- Add filters to both queries
  IF category_param IS NOT NULL THEN
    query_text := query_text || ' AND category = $2';
    count_query_text := count_query_text || ' AND category = $2';
  END IF;
  
  IF search_term IS NOT NULL THEN
    query_text := query_text || ' AND (original_name ILIKE $3 OR storage_path ILIKE $3)';
    count_query_text := count_query_text || ' AND (original_name ILIKE $3 OR storage_path ILIKE $3)';
  END IF;
  
  IF min_size IS NOT NULL THEN
    query_text := query_text || ' AND size >= $4';
    count_query_text := count_query_text || ' AND size >= $4';
  END IF;
  
  IF max_size IS NOT NULL THEN
    query_text := query_text || ' AND size <= $5';
    count_query_text := count_query_text || ' AND size <= $5';
  END IF;
  
  IF start_date IS NOT NULL THEN
    query_text := query_text || ' AND uploaded_at >= $6';
    count_query_text := count_query_text || ' AND uploaded_at >= $6';
  END IF;
  
  IF end_date IS NOT NULL THEN
    query_text := query_text || ' AND uploaded_at <= $7';
    count_query_text := count_query_text || ' AND uploaded_at <= $7';
  END IF;
  
  IF tags IS NOT NULL AND array_length(tags, 1) > 0 THEN
    query_text := query_text || ' AND tags @> $8';
    count_query_text := count_query_text || ' AND tags @> $8';
  END IF;
  
  IF is_favorite IS NOT NULL THEN
    query_text := query_text || ' AND favorite = $9';
    count_query_text := count_query_text || ' AND favorite = $9';
  END IF;
  
  -- Add sorting and pagination
  query_text := query_text || ' ORDER BY uploaded_at DESC LIMIT $10 OFFSET $11';
  
  -- Execute the count query
  EXECUTE count_query_text 
  INTO total_count_var
  USING 
    user_id_param,
    category_param,
    CASE WHEN search_term IS NOT NULL THEN '%' || search_term || '%' ELSE NULL END,
    min_size,
    max_size,
    start_date,
    end_date,
    tags,
    is_favorite;
  
  -- Execute the main query and return results with total count
  RETURN QUERY EXECUTE query_text 
  USING 
    user_id_param,
    category_param,
    CASE WHEN search_term IS NOT NULL THEN '%' || search_term || '%' ELSE NULL END,
    min_size,
    max_size,
    start_date,
    end_date,
    tags,
    is_favorite,
    limit_param,
    offset_param;
    
  -- Add total count to each row
  total_count := total_count_var;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION search_files(UUID, TEXT, TEXT, BIGINT, BIGINT, TIMESTAMPTZ, TIMESTAMPTZ, TEXT[], BOOLEAN, INTEGER, INTEGER) TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION find_duplicate_files(UUID) IS 'Finds files with duplicate hashes for a specific user';
COMMENT ON FUNCTION find_duplicate_files_by_category(UUID, TEXT) IS 'Finds files with duplicate hashes for a specific user and category';
COMMENT ON FUNCTION get_file_statistics(UUID) IS 'Gets file statistics by category and date for a user';
COMMENT ON FUNCTION add_virtual_folder(UUID, TEXT) IS 'Adds a virtual folder to user preferences';
COMMENT ON FUNCTION get_virtual_folders(UUID) IS 'Gets all virtual folders for a user';
COMMENT ON FUNCTION search_files(UUID, TEXT, TEXT, BIGINT, BIGINT, TIMESTAMPTZ, TIMESTAMPTZ, TEXT[], BOOLEAN, INTEGER, INTEGER) IS 'Searches files with advanced filters';