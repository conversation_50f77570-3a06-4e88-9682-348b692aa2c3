const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://babgywcvqyclvxdkckkd.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzMDc1MSwiZXhwIjoyMDY3MjA2NzUxfQ.DUKouq8tLc1QwyWKw9pze3tHXIiN-L9SNpdvhEQw3cs';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDatabaseFix() {
  console.log('🧪 Testing Database Fix...\n');

  try {
    console.log('Verifying database setup...');
    
    // Test the fixed approach - direct table queries
    const requiredTables = ['users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status'];
    const tableResults = {};
    
    for (const tableName of requiredTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);

        if (error) {
          tableResults[tableName] = `❌ Error: ${error.message}`;
        } else {
          tableResults[tableName] = `✅ Accessible (${data.length} records found)`;
        }
      } catch (err) {
        tableResults[tableName] = `❌ Not accessible: ${err.message}`;
      }
    }

    console.log('📊 Table Access Results:');
    for (const [table, result] of Object.entries(tableResults)) {
      console.log(`   ${table}: ${result}`);
    }

    // Test alternative system query approach using RPC
    console.log('\n🔍 Testing system table query via RPC...');
    try {
      const { data: systemData, error: systemError } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status')
          ORDER BY table_name;
        `
      });

      if (systemError) {
        console.log('❌ RPC system query failed:', systemError.message);
        console.log('ℹ️  This is expected - using direct table queries instead');
      } else {
        console.log('✅ RPC system query worked:', systemData);
      }
    } catch (rpcErr) {
      console.log('❌ RPC not available:', rpcErr.message);
      console.log('ℹ️  This is normal - direct table queries are more reliable');
    }

    // Check storage buckets
    console.log('\n🗄️  Checking storage buckets...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error checking buckets:', bucketsError);
    } else {
      console.log('📦 Existing buckets:', buckets.map(b => b.name));
      
      if (buckets.find(b => b.name === 'user-backups')) {
        console.log('✅ user-backups bucket exists');
      }
      if (buckets.find(b => b.name === 'user-data')) {
        console.log('✅ user-data bucket exists');  
      }
      if (buckets.find(b => b.name === 'thumbnails')) {
        console.log('✅ thumbnails bucket exists');
      }
    }

    // Test authentication
    console.log('\n🔐 Testing authentication...');
    
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.log('ℹ️  No active session (expected for service role)');
    } else {
      console.log('✅ Auth service accessible');
    }

    console.log('\n🎉 Database setup completed!');
    
    const workingTables = Object.values(tableResults).filter(result => result.includes('✅')).length;
    console.log(`📋 Summary: ${workingTables}/${requiredTables.length} tables accessible`);
    
    if (workingTables === requiredTables.length) {
      console.log('✅ All tables are working correctly!');
      console.log('✅ The information_schema error has been resolved!');
    } else {
      console.log('⚠️  Some tables may need to be created');
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

testDatabaseFix();