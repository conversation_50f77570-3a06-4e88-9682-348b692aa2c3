#!/usr/bin/env node

/**
 * Stripe Integration Test Runner
 * 
 * This script runs the Stripe integration tests with proper environment setup.
 * Make sure to set your Stripe test keys before running.
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Running Stripe Integration Tests...\n');

// Check for required environment variables
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY environment variable is required');
  console.log('   Set it to your Stripe test key (sk_test_...)');
  process.exit(1);
}

if (!process.env.STRIPE_SECRET_KEY.includes('sk_test_')) {
  console.error('❌ Please use a Stripe test key (sk_test_...) for integration tests');
  process.exit(1);
}

try {
  // Run the specific Stripe integration test
  const command = 'npx jest src/__tests__/stripe-integration.test.ts --verbose --detectOpenHandles';
  
  console.log('Running command:', command);
  console.log('Working directory:', process.cwd());
  console.log('');
  
  execSync(command, {
    stdio: 'inherit',
    cwd: __dirname,
    env: {
      ...process.env,
      NODE_ENV: 'test'
    }
  });
  
  console.log('\n✅ Stripe integration tests completed successfully!');
  
} catch (error) {
  console.error('\n❌ Stripe integration tests failed:');
  console.error(error.message);
  process.exit(1);
}