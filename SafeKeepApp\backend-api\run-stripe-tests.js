#!/usr/bin/env node

/**
 * Simple test runner for Stripe integration tests
 * This script runs the Stripe integration tests with proper configuration
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Running Stripe Integration Tests...\n');

try {
  // Set environment variables for testing
  process.env.NODE_ENV = 'test';
  process.env.STRIPE_SECRET_KEY = 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP';
  process.env.SUPABASE_URL = 'https://test.supabase.co';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test_service_role_key';

  // Run the specific test file
  const command = 'npx jest src/__tests__/stripe-integration.test.ts --verbose --runInBand --no-coverage';
  
  console.log(`Executing: ${command}\n`);
  
  const result = execSync(command, {
    stdio: 'inherit',
    cwd: __dirname,
    env: { ...process.env }
  });

  console.log('\n✅ Stripe integration tests completed successfully!');
  
} catch (error) {
  console.error('\n❌ Stripe integration tests failed:');
  console.error(error.message);
  process.exit(1);
}