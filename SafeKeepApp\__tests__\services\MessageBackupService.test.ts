import MessageBackupService, { MessageData, MessageThread } from '../../src/services/MessageBackupService';
import AuthService from '../../src/services/AuthService';
import EncryptionService from '../../src/services/EncryptionService';
import CloudStorageService from '../../src/services/CloudStorageService';
import { Platform, PermissionsAndroid } from 'react-native';

// Mock dependencies
jest.mock('../../src/services/AuthService');
jest.mock('../../src/services/EncryptionService');
jest.mock('../../src/services/CloudStorageService');
jest.mock('react-native', () => ({
  Platform: { OS: 'android' },
  PermissionsAndroid: {
    request: jest.fn(),
    PERMISSIONS: { READ_SMS: 'android.permission.READ_SMS' },
    RESULTS: { GRANTED: 'granted' }
  }
}));

const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;
const mockEncryptionService = EncryptionService as jest.Mocked<typeof EncryptionService>;
const mockCloudStorageService = CloudStorageService as jest.Mocked<typeof CloudStorageService>;
const mockPermissionsAndroid = PermissionsAndroid as jest.Mocked<typeof PermissionsAndroid>;

describe('MessageBackupService', () => {
  const mockMessages: MessageData[] = [
    {
      id: '1',
      threadId: '1',
      address: '+1234567890',
      person: 'John Doe',
      date: Date.now() - 1000,
      dateReceived: Date.now() - 1000,
      protocol: 0,
      read: true,
      status: 0,
      type: 1, // received
      body: 'Hello, how are you?',
      locked: false
    },
    {
      id: '2',
      threadId: '1',
      address: '+1234567890',
      person: 'John Doe',
      date: Date.now(),
      dateReceived: Date.now(),
      protocol: 0,
      read: true,
      status: 0,
      type: 2, // sent
      body: 'I am doing well, thanks!',
      locked: false
    },
    {
      id: '3',
      threadId: '2',
      address: '+0987654321',
      person: 'Jane Smith',
      date: Date.now() - 2000,
      dateReceived: Date.now() - 2000,
      protocol: 0,
      read: false,
      status: 0,
      type: 1, // received
      body: 'Meeting at 3pm today',
      locked: false
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockAuthService.getCurrentUser.mockReturnValue({ id: 'user123', email: '<EMAIL>' });
    mockPermissionsAndroid.request.mockResolvedValue('granted');
    (Platform as any).OS = 'android';
  });

  describe('requestSMSPermission', () => {
    it('should request Android SMS permission successfully', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('granted');
      
      const result = await MessageBackupService.requestSMSPermission();
      
      expect(result).toBe(true);
      expect(mockPermissionsAndroid.request).toHaveBeenCalledWith(
        'android.permission.READ_SMS',
        expect.objectContaining({
          title: '💬 SMS Access Permission'
        })
      );
    });

    it('should handle permission denial on Android', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');
      
      const result = await MessageBackupService.requestSMSPermission();
      
      expect(result).toBe(false);
    });

    it('should return false for iOS platform', async () => {
      (Platform as any).OS = 'ios';
      
      const result = await MessageBackupService.requestSMSPermission();
      
      expect(result).toBe(false);
      expect(mockPermissionsAndroid.request).not.toHaveBeenCalled();
    });

    it('should handle permission request errors', async () => {
      mockPermissionsAndroid.request.mockRejectedValue(new Error('Permission error'));
      
      const result = await MessageBackupService.requestSMSPermission();
      
      expect(result).toBe(false);
    });
  });

  describe('groupMessagesByThread', () => {
    it('should group messages by thread ID correctly', () => {
      const result = MessageBackupService.groupMessagesByThread(mockMessages);
      
      expect(result).toHaveLength(2);
      
      const thread1 = result.find(t => t.threadId === '1');
      const thread2 = result.find(t => t.threadId === '2');
      
      expect(thread1).toBeDefined();
      expect(thread1!.messages).toHaveLength(2);
      expect(thread1!.contactName).toBe('John Doe');
      expect(thread1!.messageCount).toBe(2);
      
      expect(thread2).toBeDefined();
      expect(thread2!.messages).toHaveLength(1);
      expect(thread2!.contactName).toBe('Jane Smith');
      expect(thread2!.messageCount).toBe(1);
    });

    it('should sort threads by last message date', () => {
      const result = MessageBackupService.groupMessagesByThread(mockMessages);
      
      // Thread 1 should be first (has newer messages)
      expect(result[0].threadId).toBe('1');
      expect(result[1].threadId).toBe('2');
    });

    it('should handle empty message array', () => {
      const result = MessageBackupService.groupMessagesByThread([]);
      
      expect(result).toHaveLength(0);
    });

    it('should use address as contact name when person is not available', () => {
      const messagesWithoutPerson = mockMessages.map(msg => ({
        ...msg,
        person: ''
      }));
      
      const result = MessageBackupService.groupMessagesByThread(messagesWithoutPerson);
      
      expect(result[0].contactName).toBe('+1234567890');
    });
  });

  describe('backupMessages', () => {
    beforeEach(() => {
      mockEncryptionService.encrypt.mockResolvedValue({
        success: true,
        data: 'encrypted_data'
      });
      mockCloudStorageService.uploadFile.mockResolvedValue({
        success: true,
        fileId: 'file123'
      });
    });

    it('should successfully backup messages with encryption on Android', async () => {
      const progressCallback = jest.fn();
      const result = await MessageBackupService.backupMessages(mockMessages, progressCallback);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(3);
      expect(result.errors).toHaveLength(0);
      expect(mockEncryptionService.encrypt).toHaveBeenCalled();
      expect(mockCloudStorageService.uploadFile).toHaveBeenCalled();
      expect(progressCallback).toHaveBeenCalledTimes(6); // Multiple progress updates
    });

    it('should handle iOS platform limitation', async () => {
      (Platform as any).OS = 'ios';
      
      const result = await MessageBackupService.backupMessages(mockMessages);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
      expect(result.errors[0].message).toContain('iOS does not allow SMS access');
      expect(result.errors[0].retryable).toBe(false);
    });

    it('should handle authentication failure', async () => {
      mockAuthService.getCurrentUser.mockReturnValue(null);

      const result = await MessageBackupService.backupMessages(mockMessages);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('authentication required');
    });

    it('should handle permission denial', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');

      const result = await MessageBackupService.backupMessages(mockMessages);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('permission');
      expect(result.errors[0].message).toContain('permission denied');
    });

    it('should handle encryption failure', async () => {
      mockEncryptionService.encrypt.mockResolvedValue({
        success: false,
        error: 'Encryption failed'
      });

      const result = await MessageBackupService.backupMessages(mockMessages);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('encryption');
      expect(result.errors[0].message).toContain('Failed to encrypt');
    });

    it('should handle upload failure', async () => {
      mockCloudStorageService.uploadFile.mockResolvedValue({
        success: false,
        error: 'Upload failed'
      });

      const result = await MessageBackupService.backupMessages(mockMessages);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('network');
      expect(result.errors[0].message).toContain('Failed to upload');
    });

    it('should validate message data and filter invalid messages', async () => {
      const invalidMessages = [
        ...mockMessages,
        {
          id: '',
          threadId: '999',
          address: '',
          person: '',
          date: 0,
          dateReceived: 0,
          protocol: 0,
          read: false,
          status: 0,
          type: 1,
          body: '',
          locked: false
        }
      ];

      const result = await MessageBackupService.backupMessages(invalidMessages);

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(3); // Only valid messages
    });

    it('should handle no valid messages scenario', async () => {
      const invalidMessages = [
        {
          id: '',
          threadId: '999',
          address: '',
          person: '',
          date: 0,
          dateReceived: 0,
          protocol: 0,
          read: false,
          status: 0,
          type: 1,
          body: '',
          locked: false
        }
      ];

      const result = await MessageBackupService.backupMessages(invalidMessages);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
      expect(result.errors[0].message).toContain('No valid messages found');
    });

    it('should handle general errors gracefully', async () => {
      mockEncryptionService.encrypt.mockRejectedValue(new Error('Unexpected error'));

      const result = await MessageBackupService.backupMessages(mockMessages);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('platform');
      expect(result.errors[0].message).toContain('Unexpected error');
    });

    it('should prevent concurrent backup operations', async () => {
      // Start first backup
      const firstBackup = MessageBackupService.backupMessages(mockMessages);
      
      // Try to start second backup while first is running
      await expect(MessageBackupService.backupMessages(mockMessages))
        .rejects.toThrow('Message backup is already running');
      
      // Wait for first backup to complete
      await firstBackup;
    });

    it('should track progress correctly during backup', async () => {
      const progressCallback = jest.fn();
      
      await MessageBackupService.backupMessages(mockMessages, progressCallback);

      // Verify progress updates
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentThread: 'Validating message data...',
          percentage: 10,
          status: 'processing'
        })
      );

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentThread: 'Organizing conversations...',
          percentage: 20,
          status: 'processing'
        })
      );

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentThread: 'Encrypting message data...',
          percentage: 40,
          status: 'processing'
        })
      );

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          currentThread: 'Backup completed!',
          percentage: 100,
          status: 'completed'
        })
      );
    });
  });

  describe('validateMessageData', () => {
    it('should filter out messages with missing required fields', () => {
      const mixedMessages = [
        ...mockMessages,
        {
          id: '',
          threadId: '999',
          address: '+1111111111',
          person: 'Invalid',
          date: Date.now(),
          dateReceived: Date.now(),
          protocol: 0,
          read: false,
          status: 0,
          type: 1,
          body: 'Valid body',
          locked: false
        },
        {
          id: '999',
          threadId: '999',
          address: '',
          person: 'Invalid',
          date: Date.now(),
          dateReceived: Date.now(),
          protocol: 0,
          read: false,
          status: 0,
          type: 1,
          body: 'Valid body',
          locked: false
        }
      ];

      // Access private method through service instance
      const service = MessageBackupService as any;
      const result = service.validateMessageData(mixedMessages);

      expect(result).toHaveLength(3); // Only original valid messages
      expect(result.every((m: MessageData) => 
        m.id.trim().length > 0 && 
        m.address.trim().length > 0 && 
        m.body.trim().length > 0 &&
        m.date > 0
      )).toBe(true);
    });
  });

  describe('scanMessages', () => {
    it('should scan messages successfully with permission', async () => {
      const result = await MessageBackupService.scanMessages();

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });

    it('should throw error when permission is denied', async () => {
      mockPermissionsAndroid.request.mockResolvedValue('denied');

      await expect(MessageBackupService.scanMessages())
        .rejects.toThrow('SMS permission denied');
    });
  });

  describe('control functions', () => {
    it('should track running state correctly', () => {
      expect(MessageBackupService.isRunning()).toBe(false);
    });

    it('should handle pause and resume', () => {
      MessageBackupService.pauseBackup();
      MessageBackupService.resumeBackup();
      // These are void functions, just ensure they don't throw
      expect(true).toBe(true);
    });
  });

  describe('restoreMessages', () => {
    it('should restore messages successfully', async () => {
      const mockFiles = [
        {
          id: 'file1',
          uploaded_at: '2023-01-01T00:00:00Z'
        }
      ];
      
      const mockBackupData = {
        threads: [
          {
            threadId: '1',
            contactName: 'John Doe',
            address: '+1234567890',
            messageCount: 2,
            lastMessage: 'Hello',
            lastMessageDate: Date.now(),
            messages: mockMessages.slice(0, 2)
          }
        ]
      };

      mockCloudStorageService.getUserFiles.mockResolvedValue(mockFiles);
      mockCloudStorageService.downloadFile.mockResolvedValue({
        success: true,
        data: JSON.stringify(mockBackupData)
      });

      const result = await MessageBackupService.restoreMessages();

      expect(result.success).toBe(true);
      expect(result.threads).toBeDefined();
      expect(result.threads!).toHaveLength(1);
    });

    it('should handle no backup files found', async () => {
      mockCloudStorageService.getUserFiles.mockResolvedValue([]);

      const result = await MessageBackupService.restoreMessages();

      expect(result.success).toBe(false);
      expect(result.error).toBe('No message backup found');
    });
  });

  describe('getBackupStats', () => {
    it('should return backup statistics', async () => {
      const mockFiles = [
        {
          id: 'file1',
          uploaded_at: '2023-01-01T00:00:00Z'
        }
      ];
      
      const mockBackupData = {
        totalMessages: 10,
        totalThreads: 3
      };

      mockCloudStorageService.getUserFiles.mockResolvedValue(mockFiles);
      mockCloudStorageService.downloadFile.mockResolvedValue({
        success: true,
        data: JSON.stringify(mockBackupData)
      });

      const result = await MessageBackupService.getBackupStats();

      expect(result.totalBackups).toBe(1);
      expect(result.totalMessages).toBe(10);
      expect(result.totalThreads).toBe(3);
      expect(result.lastBackupDate).toBeDefined();
    });

    it('should handle no backup files', async () => {
      mockCloudStorageService.getUserFiles.mockResolvedValue([]);

      const result = await MessageBackupService.getBackupStats();

      expect(result.totalBackups).toBe(0);
      expect(result.totalMessages).toBe(0);
      expect(result.totalThreads).toBe(0);
    });
  });
});