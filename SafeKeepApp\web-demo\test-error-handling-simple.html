<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Handling Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #4facfe;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-controls {
            margin: 20px 0;
        }
        .btn {
            background: #4facfe;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #3a8bfe;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.success {
            background: #28a745;
        }
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🧪 Error Handling System Test</h1>
        <p>Testing the advanced error handling and recovery system</p>
    </div>

    <div class="test-container">
        <h2>Test Controls</h2>
        <div class="test-controls">
            <button class="btn" onclick="runAllTests()">Run All Tests</button>
            <button class="btn" onclick="testErrorHandling()">Test Error Handling</button>
            <button class="btn" onclick="testNetworkQueue()">Test Network Queue</button>
            <button class="btn" onclick="testOfflineManager()">Test Offline Manager</button>
            <button class="btn" onclick="testDemoState()">Test Demo State</button>
            <button class="btn danger" onclick="triggerTestError()">Trigger Test Error</button>
            <button class="btn success" onclick="showTestNotification()">Show Notification</button>
            <button class="btn" onclick="clearResults()">Clear Results</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>System Status</h2>
        <div id="system-status"></div>
    </div>

    <div class="test-container">
        <h2>Console Log</h2>
        <div id="console-log" class="log-container"></div>
    </div>

    <!-- Load Error Handling System -->
    <link rel="stylesheet" href="error-handler-styles.css">
    <script src="error-handler.js"></script>
    <script src="network-queue-manager.js"></script>
    <script src="offline-manager.js"></script>
    <script src="demo-state-manager.js"></script>
    <script src="error-handling-integration.js"></script>
    <script src="test-error-handling.js"></script>
    <script src="verify-error-handling.js"></script>

    <script>
        // Test logging
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToPage(message, type = 'log') {
            const logContainer = document.getElementById('console-log');
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logEntry.style.color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#ffd93d' : '#00ff00';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToPage(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToPage(args.join(' '), 'warn');
        };

        // Test functions
        function addTestResult(name, status, details) {
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${status.toLowerCase()}`;
            resultDiv.innerHTML = `<strong>${name}:</strong> ${status} - ${details}`;
            resultsContainer.appendChild(resultDiv);
        }

        function updateSystemStatus() {
            const statusContainer = document.getElementById('system-status');
            const status = {
                errorHandler: !!window.errorHandler,
                networkQueue: !!window.networkQueue,
                offlineManager: !!window.offlineManager,
                demoManager: !!window.demoManager,
                integration: !!window.errorHandlingIntegration
            };

            statusContainer.innerHTML = `
                <div class="test-result test-${status.errorHandler ? 'pass' : 'fail'}">
                    Error Handler: ${status.errorHandler ? 'Loaded' : 'Not Loaded'}
                </div>
                <div class="test-result test-${status.networkQueue ? 'pass' : 'fail'}">
                    Network Queue: ${status.networkQueue ? 'Loaded' : 'Not Loaded'}
                </div>
                <div class="test-result test-${status.offlineManager ? 'pass' : 'fail'}">
                    Offline Manager: ${status.offlineManager ? 'Loaded' : 'Not Loaded'}
                </div>
                <div class="test-result test-${status.demoManager ? 'pass' : 'fail'}">
                    Demo Manager: ${status.demoManager ? 'Loaded' : 'Not Loaded'}
                </div>
                <div class="test-result test-${status.integration ? 'pass' : 'fail'}">
                    Integration: ${status.integration ? 'Loaded' : 'Not Loaded'}
                </div>
            `;
        }

        async function runAllTests() {
            clearResults();
            console.log('🚀 Starting comprehensive error handling tests...');
            
            try {
                // Run the comprehensive test suite
                if (window.testErrorHandling) {
                    const results = await window.testErrorHandling();
                    
                    // Display results
                    results.results.forEach(result => {
                        addTestResult(result.name, result.status, result.message);
                    });
                    
                    addTestResult('Overall Test Suite', results.successRate > 80 ? 'PASS' : 'FAIL', 
                        `${results.passed}/${results.totalTests} tests passed (${results.successRate.toFixed(1)}%)`);
                } else {
                    addTestResult('Test Suite', 'FAIL', 'Test suite not available');
                }
                
                // Run verification
                if (window.verifyErrorHandling) {
                    const verifyResults = await window.verifyErrorHandling();
                    addTestResult('System Verification', verifyResults.overall, 
                        `Components: ${Object.keys(verifyResults.components).length}, Integration: ${Object.keys(verifyResults.integration).length}`);
                }
                
            } catch (error) {
                addTestResult('Test Execution', 'FAIL', error.message);
                console.error('Test execution failed:', error);
            }
        }

        function testErrorHandling() {
            console.log('Testing error handling...');
            
            if (!window.errorHandler) {
                addTestResult('Error Handler', 'FAIL', 'Not initialized');
                return;
            }
            
            try {
                // Test error handling
                const testError = new Error('Test error for demonstration');
                window.errorHandler.handleError(testError, 'DEMO_STATE', {
                    operation: 'manual_test',
                    testId: Date.now()
                });
                
                addTestResult('Error Handler', 'PASS', 'Successfully handled test error');
                
                // Check error log
                const errorLog = window.errorHandler.getErrorLog();
                addTestResult('Error Logging', 'PASS', `${errorLog.length} errors logged`);
                
            } catch (error) {
                addTestResult('Error Handler', 'FAIL', error.message);
            }
        }

        function testNetworkQueue() {
            console.log('Testing network queue...');
            
            if (!window.networkQueue) {
                addTestResult('Network Queue', 'FAIL', 'Not initialized');
                return;
            }
            
            try {
                // Test queueing
                const queueId = window.networkQueue.queueApiCall('/api/test', {
                    method: 'GET'
                });
                
                addTestResult('Network Queue', 'PASS', `Queued operation: ${queueId}`);
                
                // Check status
                const status = window.networkQueue.getQueueStatus();
                addTestResult('Queue Status', 'PASS', 
                    `Queue length: ${status.queueLength}, Online: ${status.isOnline}`);
                
            } catch (error) {
                addTestResult('Network Queue', 'FAIL', error.message);
            }
        }

        function testOfflineManager() {
            console.log('Testing offline manager...');
            
            if (!window.offlineManager) {
                addTestResult('Offline Manager', 'FAIL', 'Not initialized');
                return;
            }
            
            try {
                // Test caching
                const testData = { test: true, timestamp: Date.now() };
                window.offlineManager.cacheData('test_key', testData);
                
                const cachedData = window.offlineManager.getCachedData('test_key');
                
                if (cachedData && cachedData.test) {
                    addTestResult('Offline Caching', 'PASS', 'Data cached and retrieved successfully');
                } else {
                    addTestResult('Offline Caching', 'FAIL', 'Failed to cache or retrieve data');
                }
                
                // Check status
                const status = window.offlineManager.getStatus();
                addTestResult('Offline Status', 'PASS', 
                    `Features: ${status.availableFeatures.length}, Cached: ${status.cachedDataCount}`);
                
            } catch (error) {
                addTestResult('Offline Manager', 'FAIL', error.message);
            }
        }

        function testDemoState() {
            console.log('Testing demo state manager...');
            
            if (!window.demoManager) {
                addTestResult('Demo State', 'FAIL', 'Not initialized');
                return;
            }
            
            try {
                // Test state updates
                const testValue = `test_${Date.now()}`;
                window.demoManager.updateState('test.manual', testValue);
                
                const retrievedValue = window.demoManager.getState('test.manual');
                
                if (retrievedValue === testValue) {
                    addTestResult('Demo State Update', 'PASS', 'State updated and retrieved successfully');
                } else {
                    addTestResult('Demo State Update', 'FAIL', 'State update failed');
                }
                
                // Test demo features
                window.demoManager.setDemoStep(99);
                const currentStep = window.demoManager.getState('demo.currentStep');
                
                if (currentStep === 99) {
                    addTestResult('Demo Step Tracking', 'PASS', 'Demo step set correctly');
                } else {
                    addTestResult('Demo Step Tracking', 'FAIL', 'Demo step not set correctly');
                }
                
                // Get stats
                const stats = window.demoManager.getStats();
                addTestResult('Demo Stats', 'PASS', 
                    `Sessions: ${stats.sessionCount}, Features: ${stats.featuresExplored}`);
                
            } catch (error) {
                addTestResult('Demo State', 'FAIL', error.message);
            }
        }

        function triggerTestError() {
            console.log('Triggering test error...');
            
            if (window.errorHandler) {
                const testError = new Error('Manual test error triggered by user');
                window.errorHandler.handleError(testError, 'SYSTEM', {
                    operation: 'manual_trigger',
                    userTriggered: true,
                    timestamp: Date.now()
                });
                
                addTestResult('Manual Error', 'PASS', 'Test error triggered successfully');
            } else {
                addTestResult('Manual Error', 'FAIL', 'Error handler not available');
            }
        }

        function showTestNotification() {
            console.log('Showing test notification...');
            
            if (window.errorHandler) {
                window.errorHandler.showUserNotification({
                    type: 'info',
                    title: 'Test Notification',
                    message: 'This is a test notification to verify the notification system is working.',
                    autoHide: true,
                    duration: 3000
                });
                
                addTestResult('Test Notification', 'PASS', 'Notification displayed');
            } else {
                addTestResult('Test Notification', 'FAIL', 'Error handler not available');
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('console-log').innerHTML = '';
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            console.log('🔧 Error Handling Test Page Loaded');
            updateSystemStatus();
            
            // Update status every 2 seconds
            setInterval(updateSystemStatus, 2000);
            
            // Auto-run basic tests after components load
            setTimeout(() => {
                if (window.errorHandler && window.networkQueue && window.offlineManager && window.demoManager) {
                    console.log('✅ All components loaded, running basic verification...');
                    testErrorHandling();
                    testNetworkQueue();
                    testOfflineManager();
                    testDemoState();
                } else {
                    console.log('⚠️ Some components not loaded yet');
                }
            }, 1000);
        });
    </script>
</body>
</html>