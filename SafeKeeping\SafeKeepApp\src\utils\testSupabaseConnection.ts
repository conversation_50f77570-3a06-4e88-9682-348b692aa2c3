import { supabase } from './supabaseClient';

/**
 * Tests the connection to Supabase by performing a simple query
 * @returns Object with success status and message
 */
export async function testSupabaseConnection(): Promise<{ 
  success: boolean; 
  message: string;
  details?: any;
}> {
  try {
    console.log('Testing Supabase connection...');
    
    // Get the current timestamp from the server
    const { data, error } = await supabase.rpc('get_server_timestamp');
    
    if (error) {
      // If the function doesn't exist, try a simple query instead
      if (error.message.includes('does not exist')) {
        console.log('get_server_timestamp function not found, trying simple query...');
        
        const { data: simpleData, error: simpleError } = await supabase
          .from('_rpc')
          .select('*')
          .limit(1);
        
        if (simpleError) {
          console.error('Simple query failed:', simpleError);
          return { 
            success: false, 
            message: `Connection failed: ${simpleError.message}` 
          };
        }
        
        return { 
          success: true, 
          message: 'Connection successful using simple query',
          details: { method: 'simple_query' }
        };
      }
      
      console.error('Connection test failed:', error);
      return { 
        success: false, 
        message: `Connection failed: ${error.message}` 
      };
    }
    
    // Create the function if it doesn't exist
    if (!data) {
      console.log('Creating get_server_timestamp function...');
      
      const createFunctionSql = `
        CREATE OR REPLACE FUNCTION get_server_timestamp()
        RETURNS TIMESTAMPTZ AS $$
        BEGIN
          RETURN NOW();
        END;
        $$ LANGUAGE plpgsql;
        
        -- Grant execute permission to authenticated users
        GRANT EXECUTE ON FUNCTION get_server_timestamp() TO authenticated;
        
        COMMENT ON FUNCTION get_server_timestamp() IS 'Returns the current server timestamp';
      `;
      
      try {
        await supabase.rpc('exec_sql', { sql_query: createFunctionSql });
        
        // Test the function again
        const { data: retestData, error: retestError } = await supabase.rpc('get_server_timestamp');
        
        if (retestError) {
          console.error('Function creation succeeded but retest failed:', retestError);
          return { 
            success: false, 
            message: `Function created but test failed: ${retestError.message}` 
          };
        }
        
        return { 
          success: true, 
          message: 'Connection successful and helper function created',
          details: { 
            timestamp: retestData,
            method: 'function_created'
          }
        };
      } catch (funcError) {
        console.error('Failed to create function:', funcError);
        return { 
          success: false, 
          message: `Failed to create helper function: ${funcError instanceof Error ? funcError.message : String(funcError)}` 
        };
      }
    }
    
    return { 
      success: true, 
      message: 'Connection successful',
      details: { 
        timestamp: data,
        method: 'function_call'
      }
    };
  } catch (error) {
    console.error('Unexpected error testing connection:', error);
    return { 
      success: false, 
      message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}