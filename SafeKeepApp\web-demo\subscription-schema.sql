-- Subscription Tier Management Database Schema
-- For SafeKeep Web Demo

-- Subscription Tiers Table
CREATE TABLE subscription_tiers (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price_cents INTEGER NOT NULL DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'usd',
    billing_interval VARCHAR(20) NOT NULL DEFAULT 'month',
    max_storage_gb INTEGER NOT NULL,
    max_backups_per_month INTEGER NOT NULL DEFAULT -1, -- -1 for unlimited
    max_restores_per_month INTEGER NOT NULL DEFAULT -1, -- -1 for unlimited
    backup_frequencies TEXT NOT NULL, -- JSON array of allowed frequencies
    priority_support BOOLEAN NOT NULL DEFAULT FALSE,
    advanced_encryption BOOLEAN NOT NULL DEFAULT FALSE,
    multi_device_sync BOOLEAN NOT NULL DEFAULT FALSE,
    backup_history_days INTEGER NOT NULL DEFAULT 30,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Subscriptions Table
CREATE TABLE user_subscriptions (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    tier_id VARCHAR(50) NOT NULL,
    stripe_subscription_id VARCHAR(100),
    stripe_customer_id VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, canceled, past_due, unpaid
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    cancel_at_period_end BOOLEAN NOT NULL DEFAULT FALSE,
    canceled_at TIMESTAMP NULL,
    trial_start TIMESTAMP NULL,
    trial_end TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tier_id) REFERENCES subscription_tiers(id)
);

-- Usage Tracking Table
CREATE TABLE usage_tracking (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    subscription_id VARCHAR(50) NOT NULL,
    tracking_period_start TIMESTAMP NOT NULL,
    tracking_period_end TIMESTAMP NOT NULL,
    storage_used_gb DECIMAL(10,2) NOT NULL DEFAULT 0,
    backup_count INTEGER NOT NULL DEFAULT 0,
    restore_count INTEGER NOT NULL DEFAULT 0,
    api_calls_count INTEGER NOT NULL DEFAULT 0,
    bandwidth_used_gb DECIMAL(10,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id)
);

-- Payment Methods Table
CREATE TABLE payment_methods (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    stripe_payment_method_id VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'card',
    card_brand VARCHAR(20),
    card_last4 VARCHAR(4),
    card_exp_month INTEGER,
    card_exp_year INTEGER,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subscription Events Log Table
CREATE TABLE subscription_events (
    id SERIAL PRIMARY KEY,
    subscription_id VARCHAR(50) NOT NULL,
    event_type VARCHAR(50) NOT NULL, -- created, updated, canceled, payment_succeeded, payment_failed
    event_data TEXT, -- JSON data
    stripe_event_id VARCHAR(100),
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id)
);

-- Feature Access Control Table
CREATE TABLE feature_permissions (
    id SERIAL PRIMARY KEY,
    tier_id VARCHAR(50) NOT NULL,
    feature_key VARCHAR(100) NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    limit_value INTEGER, -- For features with limits
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tier_id) REFERENCES subscription_tiers(id),
    UNIQUE(tier_id, feature_key)
);

-- Insert default subscription tiers
INSERT INTO subscription_tiers (id, name, price_cents, max_storage_gb, max_backups_per_month, max_restores_per_month, backup_frequencies, priority_support, advanced_encryption, multi_device_sync, backup_history_days) VALUES
('free', 'Free', 0, 1, 5, 2, '["manual"]', FALSE, FALSE, FALSE, 7),
('basic', 'Basic', 299, 10, 50, 20, '["manual", "daily"]', FALSE, TRUE, FALSE, 30),
('premium', 'Premium', 999, 100, -1, -1, '["manual", "daily", "weekly"]', TRUE, TRUE, TRUE, 90);

-- Insert default feature permissions
INSERT INTO feature_permissions (tier_id, feature_key, is_enabled, limit_value) VALUES
-- Free tier features
('free', 'backup_encryption', TRUE, NULL),
('free', 'backup_scheduling', FALSE, NULL),
('free', 'priority_support', FALSE, NULL),
('free', 'advanced_encryption', FALSE, NULL),
('free', 'multi_device_sync', FALSE, NULL),
('free', 'backup_history_extended', FALSE, NULL),

-- Basic tier features
('basic', 'backup_encryption', TRUE, NULL),
('basic', 'backup_scheduling', TRUE, NULL),
('basic', 'priority_support', FALSE, NULL),
('basic', 'advanced_encryption', TRUE, NULL),
('basic', 'multi_device_sync', FALSE, NULL),
('basic', 'backup_history_extended', TRUE, NULL),

-- Premium tier features
('premium', 'backup_encryption', TRUE, NULL),
('premium', 'backup_scheduling', TRUE, NULL),
('premium', 'priority_support', TRUE, NULL),
('premium', 'advanced_encryption', TRUE, NULL),
('premium', 'multi_device_sync', TRUE, NULL),
('premium', 'backup_history_extended', TRUE, NULL);

-- Create indexes for better performance
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_tier_id ON user_subscriptions(tier_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_usage_tracking_user_id ON usage_tracking(user_id);
CREATE INDEX idx_usage_tracking_period ON usage_tracking(tracking_period_start, tracking_period_end);
CREATE INDEX idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX idx_subscription_events_subscription_id ON subscription_events(subscription_id);
CREATE INDEX idx_subscription_events_type ON subscription_events(event_type);
CREATE INDEX idx_feature_permissions_tier_id ON feature_permissions(tier_id);