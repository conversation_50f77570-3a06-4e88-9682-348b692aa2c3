import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Switch, List, Divider } from 'react-native-paper';

interface BackupSettingsProps {
  settings: {
    contactsEnabled: boolean;
    messagesEnabled: boolean;
    photosEnabled: boolean;
    wifiOnlyEnabled: boolean;
    autoBackupEnabled: boolean;
  };
  onSettingChange: (setting: string, value: boolean) => void;
}

const BackupSettings: React.FC<BackupSettingsProps> = ({
  settings,
  onSettingChange,
}) => {
  return (
    <View style={styles.container}>
      <List.Section>
        <List.Subheader>Data Types</List.Subheader>
        
        <List.Item
          title="Backup Contacts"
          description="Sync your contacts to the cloud"
          right={() => (
            <Switch
              value={settings.contactsEnabled}
              onValueChange={(value) => onSettingChange('contactsEnabled', value)}
            />
          )}
        />
        
        <List.Item
          title="Backup Messages"
          description="Sync your SMS messages"
          right={() => (
            <Switch
              value={settings.messagesEnabled}
              onValueChange={(value) => onSettingChange('messagesEnabled', value)}
            />
          )}
        />
        
        <List.Item
          title="Backup Photos"
          description="Sync photos from camera roll (videos excluded)"
          right={() => (
            <Switch
              value={settings.photosEnabled}
              onValueChange={(value) => onSettingChange('photosEnabled', value)}
            />
          )}
        />
        
        <Divider />
        
        <List.Subheader>Network & Automation</List.Subheader>
        
        <List.Item
          title="WiFi Only"
          description="Only backup when connected to WiFi"
          right={() => (
            <Switch
              value={settings.wifiOnlyEnabled}
              onValueChange={(value) => onSettingChange('wifiOnlyEnabled', value)}
            />
          )}
        />
        
        <List.Item
          title="Automatic Backup"
          description="Enable scheduled automatic backups"
          right={() => (
            <Switch
              value={settings.autoBackupEnabled}
              onValueChange={(value) => onSettingChange('autoBackupEnabled', value)}
            />
          )}
        />
      </List.Section>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default BackupSettings;