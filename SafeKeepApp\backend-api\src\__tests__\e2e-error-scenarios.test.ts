import request from 'supertest';
import app from '../app';

describe('End-to-End Error <PERSON> and Edge Cases', () => {
  let testUserId: string;
  let authToken: string;

  beforeAll(async () => {
    testUserId = 'test-user-errors-' + Date.now();
    authToken = 'Bearer test-jwt-token';

    // Mock authentication middleware
    jest.spyOn(require('../middleware/auth'), 'authenticateToken').mockImplementation((req: any, res: any, next: any) => {
      req.user = { id: testUserId, email: '<EMAIL>' };
      next();
    });
  });

  describe('Invalid Service Combinations and Validation Errors', () => {
    it('should reject empty service selection', async () => {
      const response = await request(app)
        .post('/api/services/validate')
        .set('Authorization', authToken)
        .send({ serviceIds: [] })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error.message).toContain('at least one service');
    });

    it('should reject invalid service IDs', async () => {
      const response = await request(app)
        .post('/api/services/validate')
        .set('Authorization', authToken)
        .send({ serviceIds: ['invalid-service', 'another-invalid'] })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error.message).toContain('invalid service');
    });

    it('should reject duplicate service selections', async () => {
      const response = await request(app)
        .post('/api/services/validate')
        .set('Authorization', authToken)
        .send({ serviceIds: ['contacts', 'contacts', 'messages'] })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
      expect(response.body.error.message).toContain('duplicate');
    });

    it('should handle pricing calculation with invalid services', async () => {
      const response = await request(app)
        .post('/api/pricing/calculate')
        .set('Authorization', authToken)
        .send({ serviceIds: ['nonexistent-service'] })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });

    it('should reject subscription creation with invalid service combination', async () => {
      const response = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: ['invalid-service'],
          paymentMethodId: 'pm_card_visa'
        })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });
  });

  describe('Payment Failures and Subscription Rollback', () => {
    it('should handle payment method declined scenario', async () => {
      // Mock Stripe to simulate declined payment
      const mockStripe = require('stripe');
      jest.spyOn(mockStripe.prototype.paymentIntents, 'create').mockRejectedValueOnce({
        type: 'card_error',
        code: 'card_declined',
        message: 'Your card was declined.'
      });

      const response = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: ['contacts', 'messages'],
          paymentMethodId: 'pm_card_chargeDeclined'
        })
        .expect(402);

      expect(response.body.error).toHaveProperty('code', 'PAYMENT_FAILED');
      expect(response.body.error.message).toContain('declined');
    });

    it('should handle insufficient funds scenario', async () => {
      const mockStripe = require('stripe');
      jest.spyOn(mockStripe.prototype.paymentIntents, 'create').mockRejectedValueOnce({
        type: 'card_error',
        code: 'insufficient_funds',
        message: 'Your card has insufficient funds.'
      });

      const response = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: ['contacts', 'messages', 'photos'],
          paymentMethodId: 'pm_card_insufficientFunds'
        })
        .expect(402);

      expect(response.body.error).toHaveProperty('code', 'PAYMENT_FAILED');
      expect(response.body.error.message).toContain('insufficient funds');
    });

    it('should rollback subscription on payment failure', async () => {
      // Mock database to simulate successful subscription creation but payment failure
      const mockSupabase = require('../utils/database').supabase;
      jest.spyOn(mockSupabase, 'from').mockReturnValueOnce({
        insert: jest.fn().mockResolvedValue({ data: { id: 'temp-sub-id' }, error: null }),
        delete: jest.fn().mockResolvedValue({ data: null, error: null })
      });

      const mockStripe = require('stripe');
      jest.spyOn(mockStripe.prototype.paymentIntents, 'create').mockRejectedValueOnce({
        type: 'card_error',
        code: 'card_declined'
      });

      const response = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: ['contacts'],
          paymentMethodId: 'pm_card_chargeDeclined'
        })
        .expect(402);

      expect(response.body.error).toHaveProperty('code', 'PAYMENT_FAILED');

      // Verify subscription was not created by checking user subscriptions
      const subscriptionsResponse = await request(app)
        .get(`/api/subscriptions/${testUserId}`)
        .set('Authorization', authToken)
        .expect(404);

      expect(subscriptionsResponse.body.error).toHaveProperty('code', 'SUBSCRIPTION_NOT_FOUND');
    });

    it('should handle Stripe API timeout errors', async () => {
      const mockStripe = require('stripe');
      jest.spyOn(mockStripe.prototype.paymentIntents, 'create').mockRejectedValueOnce({
        type: 'api_error',
        message: 'Request timeout'
      });

      const response = await request(app)
        .post('/api/billing/payment-intent')
        .set('Authorization', authToken)
        .send({
          serviceIds: ['contacts'],
          amount: 999
        })
        .expect(503);

      expect(response.body.error).toHaveProperty('code', 'EXTERNAL_SERVICE_ERROR');
      expect(response.body.error.message).toContain('timeout');
    });
  });

  describe('Service Access Denial for Inactive Subscriptions', () => {
    it('should deny access when user has no subscription', async () => {
      const response = await request(app)
        .get(`/api/services/access/${testUserId}/contacts`)
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.hasAccess).toBe(false);
      expect(response.body).not.toHaveProperty('expiresAt');
    });

    it('should deny access when subscription is cancelled', async () => {
      // First create a subscription
      const subscriptionResponse = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', authToken)
        .send({
          serviceIds: ['contacts'],
          paymentMethodId: 'pm_card_visa'
        })
        .expect(201);

      const subscriptionId = subscriptionResponse.body.subscriptionId;

      // Cancel the subscription
      await request(app)
        .delete(`/api/subscriptions/${subscriptionId}`)
        .set('Authorization', authToken)
        .expect(200);

      // Verify access is denied
      const accessResponse = await request(app)
        .get(`/api/services/access/${testUserId}/contacts`)
        .set('Authorization', authToken)
        .expect(200);

      expect(accessResponse.body.hasAccess).toBe(false);
    });

    it('should deny access when subscription is past due', async () => {
      // Mock database to return past due subscription
      const mockSupabase = require('../utils/database').supabase;
      jest.spyOn(mockSupabase, 'from').mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: {
                id: 'past-due-sub',
                status: 'past_due',
                services: ['contacts'],
                current_period_end: new Date(Date.now() - 86400000) // Yesterday
              },
              error: null
            })
          })
        })
      });

      const response = await request(app)
        .get(`/api/services/access/${testUserId}/contacts`)
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.hasAccess).toBe(false);
    });

    it('should return empty services list for user with no active subscription', async () => {
      const response = await request(app)
        .get(`/api/services/user/${testUserId}`)
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.services).toEqual([]);
    });
  });

  describe('Webhook Failure Handling and Retry Logic', () => {
    it('should handle invalid webhook signatures', async () => {
      const response = await request(app)
        .post('/api/billing/webhook')
        .set('stripe-signature', 'invalid-signature')
        .send({
          type: 'invoice.payment_succeeded',
          data: { object: { subscription: 'sub_test' } }
        })
        .expect(400);

      expect(response.body.error).toHaveProperty('code', 'INVALID_WEBHOOK_SIGNATURE');
    });

    it('should handle unknown webhook event types gracefully', async () => {
      // Mock valid signature verification
      const mockStripe = require('stripe');
      jest.spyOn(mockStripe.prototype.webhooks, 'constructEvent').mockReturnValueOnce({
        type: 'unknown.event.type',
        data: { object: {} }
      });

      const response = await request(app)
        .post('/api/billing/webhook')
        .set('stripe-signature', 'valid-signature')
        .send({
          type: 'unknown.event.type',
          data: { object: {} }
        })
        .expect(200);

      expect(response.body.message).toContain('ignored');
    });

    it('should handle database errors during webhook processing', async () => {
      // Mock valid webhook event
      const mockStripe = require('stripe');
      jest.spyOn(mockStripe.prototype.webhooks, 'constructEvent').mockReturnValueOnce({
        type: 'invoice.payment_succeeded',
        data: {
          object: {
            subscription: 'sub_test',
            customer: 'cus_test'
          }
        }
      });

      // Mock database error
      const mockSupabase = require('../utils/database').supabase;
      jest.spyOn(mockSupabase, 'from').mockReturnValueOnce({
        update: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'Database connection failed' }
        })
      });

      const response = await request(app)
        .post('/api/billing/webhook')
        .set('stripe-signature', 'valid-signature')
        .send({
          type: 'invoice.payment_succeeded',
          data: {
            object: {
              subscription: 'sub_test',
              customer: 'cus_test'
            }
          }
        })
        .expect(500);

      expect(response.body.error).toHaveProperty('code', 'WEBHOOK_PROCESSING_ERROR');
    });

    it('should implement retry logic for failed webhook processing', async () => {
      // This test verifies that webhook failures are logged for retry
      const mockStripe = require('stripe');
      jest.spyOn(mockStripe.prototype.webhooks, 'constructEvent').mockReturnValueOnce({
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_test',
            status: 'active',
            metadata: { serviceIds: 'contacts,messages' }
          }
        }
      });

      // Mock temporary database failure
      const mockSupabase = require('../utils/database').supabase;
      jest.spyOn(mockSupabase, 'from').mockReturnValueOnce({
        update: jest.fn().mockRejectedValue(new Error('Temporary database error'))
      });

      const response = await request(app)
        .post('/api/billing/webhook')
        .set('stripe-signature', 'valid-signature')
        .send({
          type: 'customer.subscription.updated',
          data: {
            object: {
              id: 'sub_test',
              status: 'active',
              metadata: { serviceIds: 'contacts,messages' }
            }
          }
        })
        .expect(500);

      expect(response.body.error).toHaveProperty('code', 'WEBHOOK_PROCESSING_ERROR');
    });
  });

  describe('Authentication and Authorization Errors', () => {
    it('should reject requests without authentication token', async () => {
      const response = await request(app)
        .get('/api/pricing/combinations')
        .expect(401);

      expect(response.body.error).toHaveProperty('code', 'AUTHENTICATION_REQUIRED');
    });

    it('should reject requests with invalid authentication token', async () => {
      const response = await request(app)
        .get('/api/pricing/combinations')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.error).toHaveProperty('code', 'INVALID_TOKEN');
    });

    it('should prevent users from accessing other users\' data', async () => {
      const otherUserId = 'other-user-id';
      
      const response = await request(app)
        .get(`/api/subscriptions/${otherUserId}`)
        .set('Authorization', authToken)
        .expect(403);

      expect(response.body.error).toHaveProperty('code', 'ACCESS_DENIED');
    });

    it('should prevent unauthorized subscription modifications', async () => {
      const response = await request(app)
        .put('/api/subscriptions/unauthorized-sub-id')
        .set('Authorization', authToken)
        .send({ serviceIds: ['contacts'] })
        .expect(403);

      expect(response.body.error).toHaveProperty('code', 'ACCESS_DENIED');
    });
  });

  describe('Rate Limiting and Abuse Prevention', () => {
    it('should enforce rate limits on pricing calculation endpoints', async () => {
      // Make multiple rapid requests to trigger rate limiting
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/pricing/calculate')
          .set('Authorization', authToken)
          .send({ serviceIds: ['contacts'] })
      );

      const responses = await Promise.all(requests);
      
      // At least one request should be rate limited
      const rateLimitedResponses = responses.filter((r: any) => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
      
      if (rateLimitedResponses.length > 0) {
        expect(rateLimitedResponses[0].body.error).toHaveProperty('code', 'RATE_LIMIT_EXCEEDED');
      }
    });

    it('should handle concurrent subscription creation attempts', async () => {
      // Attempt to create multiple subscriptions simultaneously
      const requests = Array(3).fill(null).map(() =>
        request(app)
          .post('/api/subscriptions')
          .set('Authorization', authToken)
          .send({
            serviceIds: ['contacts'],
            paymentMethodId: 'pm_card_visa'
          })
      );

      const responses = await Promise.all(requests);
      
      // Only one should succeed, others should fail with conflict error
      const successfulResponses = responses.filter((r: any) => r.status === 201);
      const conflictResponses = responses.filter((r: any) => r.status === 409);
      
      expect(successfulResponses.length).toBe(1);
      expect(conflictResponses.length).toBeGreaterThan(0);
      
      if (conflictResponses.length > 0) {
        expect(conflictResponses[0].body.error).toHaveProperty('code', 'SUBSCRIPTION_EXISTS');
      }

      // Cleanup successful subscription
      if (successfulResponses.length > 0) {
        const subscriptionId = successfulResponses[0].body.subscriptionId;
        await request(app)
          .delete(`/api/subscriptions/${subscriptionId}`)
          .set('Authorization', authToken);
      }
    });
  });
});