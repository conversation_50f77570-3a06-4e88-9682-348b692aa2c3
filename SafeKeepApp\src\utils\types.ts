// Core types for SafeKeep app

export interface Photo {
  id: string;
  uri: string;
  fileName: string;
  fileSize: number;
  timestamp: number;
  isBackedUp: boolean;
  hash?: string;
}

export interface Contact {
  id: string;
  displayName: string;
  phoneNumbers: string[];
  emailAddresses: string[];
  isBackedUp: boolean;
}

export interface SmsMessage {
  id: string;
  threadId: string;
  address: string;
  body: string;
  date: number;
  type: 'sent' | 'received';
  isBackedUp: boolean;
}

export interface BackupStatus {
  isRunning: boolean;
  progress: number;
  totalItems: number;
  completedItems: number;
  currentItem?: string;
  error?: string;
}

export interface User {
  id: string;
  email: string;
  isAuthenticated: boolean;
  storageUsed: number;
  storageLimit: number;
}

export interface AppSettings {
  autoBackup: boolean;
  wifiOnly: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly' | 'manual';
  notifications: boolean;
}

export type BackupType = 'photos' | 'contacts' | 'sms';

export interface BackupItem {
  id: string;
  type: BackupType;
  data: Photo | Contact | SmsMessage;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  metadata: {
    size: number;
    timestamp: Date;
    checksum: string;
  };
}

export interface BackupSession {
  id: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  progress: {
    contacts: BackupProgress;
    messages: BackupProgress;
    photos: BackupProgress;
  };
  totalItems: number;
  completedItems: number;
  configuration: BackupConfiguration;
}

export interface BackupProgress {
  total: number;
  completed: number;
  failed: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  errors: BackupError[];
}

export interface BackupConfiguration {
  autoBackup: boolean;
  wifiOnly: boolean;
  includeContacts: boolean;
  includeMessages: boolean;
  includePhotos: boolean;
  compressionLevel: 'none' | 'low' | 'medium' | 'high';
}

export interface BackupError {
  id: string;
  type: 'permission' | 'network' | 'storage' | 'encryption' | 'platform';
  message: string;
  timestamp: Date;
  itemId?: string;
  retryable: boolean;
}

// Video exclusion constants
export const VIDEO_MIME_TYPES = [
  'video/mp4',
  'video/quicktime',
  'video/avi',
  'video/mov',
  'video/wmv',
  'video/flv',
  'video/webm',
  'video/mkv',
  'video/3gp',
  'video/m4v'
] as const;

export const VIDEO_FILE_EXTENSIONS = [
  '.mp4',
  '.mov',
  '.avi',
  '.wmv',
  '.flv',
  '.webm',
  '.mkv',
  '.3gp',
  '.m4v',
  '.qt'
] as const;

export type VideoMimeType = typeof VIDEO_MIME_TYPES[number];
export type VideoFileExtension = typeof VIDEO_FILE_EXTENSIONS[number];
