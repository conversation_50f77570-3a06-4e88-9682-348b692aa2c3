import BackupManager from '../../src/services/BackupManager';
import ContactBackupService from '../../src/services/ContactBackupService';
import MessageBackupService from '../../src/services/MessageBackupService';
import PhotoBackupService from '../../src/services/PhotoBackupService';
import AuthService from '../../src/services/AuthService';
import PermissionService from '../../src/services/PermissionService';
import CloudStorageService from '../../src/services/CloudStorageService';
import NotificationService from '../../src/services/NotificationService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupConfiguration } from '../../src/types/backup';

// Mock network utilities
jest.mock('../../src/utils/helpers', () => ({
  isWiFiConnected: jest.fn(),
  hasSufficientBattery: jest.fn().mockResolvedValue(true),
  generateId: jest.fn().mockReturnValue('test-session-id'),
  delay: jest.fn().mockResolvedValue(undefined)
}));

// Mock other dependencies
jest.mock('@react-native-async-storage/async-storage');
jest.mock('react-native', () => ({
  Platform: { OS: 'android' },
  PermissionsAndroid: {
    request: jest.fn().mockResolvedValue('granted'),
    PERMISSIONS: { READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE' },
    RESULTS: { GRANTED: 'granted' }
  }
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('Network Condition Integration Tests', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    aud: 'authenticated',
    role: 'authenticated',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    app_metadata: {},
    user_metadata: {}
  };

  const defaultConfiguration: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium'
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Setup basic mocks
    jest.spyOn(AuthService, 'getCurrentUser').mockReturnValue(mockUser);
    jest.spyOn(PermissionService, 'checkAllPermissions').mockResolvedValue({
      contacts: { granted: true, denied: false, blocked: false, unavailable: false },
      sms: { granted: true, denied: false, blocked: false, unavailable: false },
      photos: { granted: true, denied: false, blocked: false, unavailable: false }
    });
    
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    
    // Setup successful backup services
    jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([]);
    jest.spyOn(ContactBackupService, 'backupContacts').mockResolvedValue({
      success: true,
      itemsProcessed: 0,
      errors: []
    });
    
    jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue([]);
    jest.spyOn(MessageBackupService, 'backupMessages').mockResolvedValue({
      success: true,
      itemsProcessed: 0,
      errors: []
    });
    
    jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
      photos: [],
      excludedVideos: [],
      totalScanned: 0
    });
    jest.spyOn(PhotoBackupService, 'backupPhotos').mockResolvedValue({
      success: true,
      itemsProcessed: 0,
      errors: []
    });
    
    jest.spyOn(CloudStorageService, 'uploadFile').mockResolvedValue({
      success: true,
      fileId: 'uploaded_file_id'
    });
    
    jest.spyOn(NotificationService, 'showErrorNotification').mockResolvedValue();
    jest.spyOn(NotificationService, 'showWarningNotification').mockResolvedValue();
    
    await BackupManager.initialize();
  });

  describe('WiFi-Only Configuration', () => {
    it('should prevent backup when WiFi-only is enabled but device is on cellular', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('network');
      expect(result.errors[0].message).toContain('WiFi-only backup is enabled');
      expect(result.errors[0].message).toContain('not connected to WiFi');
    });

    it('should allow backup when WiFi-only is enabled and device is on WiFi', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(true);

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should show warning but allow backup on cellular when WiFi-only is disabled', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      const cellularConfig: BackupConfiguration = {
        ...defaultConfiguration,
        wifiOnly: false
      };

      const result = await BackupManager.startBackup(cellularConfig);

      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle network check failures gracefully', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockRejectedValue(new Error('Network check failed'));

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('network');
      expect(result.errors[0].message).toContain('Unable to check network conditions');
    });
  });

  describe('Network Interruption During Backup', () => {
    it('should handle network failure during contact backup', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(true);

      // Mock contacts to backup
      const mockContacts = [
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ];
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);
      
      // Mock network failure during contact backup
      jest.spyOn(ContactBackupService, 'backupContacts').mockResolvedValue({
        success: false,
        itemsProcessed: 0,
        errors: [{
          id: 'network_error_1',
          type: 'network',
          message: 'Network connection lost during backup',
          timestamp: new Date(),
          retryable: true
        }]
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.type === 'network')).toBe(true);
      expect(result.errors.some(e => e.retryable)).toBe(true);
    });

    it('should handle intermittent network issues with retry logic', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(true);

      // Mock photos to backup
      const mockPhotos = [
        { uri: 'file://photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now(), width: 100, height: 100 }
      ];
      
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: mockPhotos,
        excludedVideos: [],
        totalScanned: 1
      });

      // Mock network failure then success (simulating retry)
      let uploadAttempts = 0;
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async () => {
        uploadAttempts++;
        if (uploadAttempts === 1) {
          return { success: false, error: 'Network timeout' };
        }
        return { success: true, fileId: 'uploaded_after_retry' };
      });

      // Mock photo backup with retry logic
      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async (photos, onProgress, maxRetries = 3) => {
        let successfulUploads = 0;
        const errors: any[] = [];

        for (const photo of photos) {
          let success = false;
          let retryCount = 0;

          while (!success && retryCount < maxRetries) {
            try {
              const uploadResult = await CloudStorageService.uploadFile(
                'mock_data',
                photo.filename,
                photo.type,
                'photo'
              );
              
              if (uploadResult.success) {
                success = true;
                successfulUploads++;
              } else {
                retryCount++;
                if (retryCount >= maxRetries) {
                  errors.push({
                    id: `upload_error_${Date.now()}`,
                    type: 'network',
                    message: `Failed to upload ${photo.filename} after ${maxRetries} retries`,
                    timestamp: new Date(),
                    retryable: true
                  });
                }
              }
            } catch (error) {
              retryCount++;
            }
          }
        }

        return {
          success: errors.length === 0,
          itemsProcessed: successfulUploads,
          errors
        };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(1);
      expect(uploadAttempts).toBe(2); // Should have retried once
    });

    it('should continue with other data types when network fails for one type', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(true);

      // Mock data for all types
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue([
        { recordID: '1', displayName: 'John Doe' }
      ] as any);
      jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue([
        { id: '1', body: 'Test message' }
      ] as any);
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: [{ uri: 'file://photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now() }],
        excludedVideos: [],
        totalScanned: 1
      });

      // Make contacts fail due to network
      jest.spyOn(ContactBackupService, 'backupContacts').mockResolvedValue({
        success: false,
        itemsProcessed: 0,
        errors: [{
          id: 'contact_network_error',
          type: 'network',
          message: 'Network error during contact backup',
          timestamp: new Date(),
          retryable: true
        }]
      });

      // Messages and photos succeed
      jest.spyOn(MessageBackupService, 'backupMessages').mockResolvedValue({
        success: true,
        itemsProcessed: 1,
        errors: []
      });
      jest.spyOn(PhotoBackupService, 'backupPhotos').mockResolvedValue({
        success: true,
        itemsProcessed: 1,
        errors: []
      });

      const result = await BackupManager.startBackup(defaultConfiguration);

      expect(result.success).toBe(false); // Overall failed due to contact errors
      expect(result.itemsProcessed).toBe(2); // Messages and photos succeeded
      expect(result.errors.length).toBe(1); // Only contact error
      expect(result.errors[0].type).toBe('network');
    });
  });

  describe('Network Quality and Bandwidth', () => {
    it('should handle slow network conditions', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(true);

      // Mock slow upload
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async (data, filename, mimeType, category, onProgress) => {
        // Simulate slow upload with progress updates
        if (onProgress) {
          onProgress({ bytesTransferred: 0, totalBytes: 1000, percentage: 0 });
          await new Promise(resolve => setTimeout(resolve, 100));
          onProgress({ bytesTransferred: 500, totalBytes: 1000, percentage: 50 });
          await new Promise(resolve => setTimeout(resolve, 100));
          onProgress({ bytesTransferred: 1000, totalBytes: 1000, percentage: 100 });
        }
        return { success: true, fileId: 'slow_upload_id' };
      });

      const mockPhotos = [
        { uri: 'file://photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now(), width: 100, height: 100 }
      ];
      
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: mockPhotos,
        excludedVideos: [],
        totalScanned: 1
      });

      const progressUpdates: any[] = [];
      const progressCallback = jest.fn((progress) => {
        progressUpdates.push(progress);
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      }, progressCallback);

      expect(result.success).toBe(true);
      expect(progressUpdates.length).toBeGreaterThan(0);
      
      // Should have received progress updates during slow upload
      expect(progressCallback).toHaveBeenCalled();
    });

    it('should handle network timeout scenarios', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(true);

      // Mock network timeout
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        throw new Error('Network timeout');
      });

      const mockContacts = [
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ];
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);
      
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('mock_data', 'contact.json', 'application/json', 'contact');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'timeout_error',
              type: 'network',
              message: error instanceof Error ? error.message : 'Network timeout',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('timeout'))).toBe(true);
    });
  });

  describe('Network State Changes During Backup', () => {
    it('should handle WiFi to cellular transition during backup', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      
      // Start on WiFi
      isWiFiConnected.mockResolvedValue(true);

      const mockPhotos = [
        { uri: 'file://photo1.jpg', filename: 'photo1.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now(), width: 100, height: 100 },
        { uri: 'file://photo2.jpg', filename: 'photo2.jpg', type: 'image/jpeg', fileSize: 1000, timestamp: Date.now(), width: 100, height: 100 }
      ];
      
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: mockPhotos,
        excludedVideos: [],
        totalScanned: 2
      });

      // Mock photo backup that detects network change
      jest.spyOn(PhotoBackupService, 'backupPhotos').mockImplementation(async (photos, onProgress) => {
        let processedPhotos = 0;
        const errors: any[] = [];

        for (let i = 0; i < photos.length; i++) {
          // Simulate network change after first photo
          if (i === 1) {
            isWiFiConnected.mockResolvedValue(false);
            
            // Check if WiFi-only is enabled
            const isWiFi = await isWiFiConnected();
            if (!isWiFi && defaultConfiguration.wifiOnly) {
              errors.push({
                id: `network_change_${i}`,
                type: 'network',
                message: 'Network changed from WiFi to cellular during backup',
                timestamp: new Date(),
                retryable: true
              });
              break;
            }
          }

          processedPhotos++;
          if (onProgress) {
            onProgress({
              totalPhotos: photos.length,
              processedPhotos,
              currentPhoto: photos[i].filename,
              bytesUploaded: processedPhotos * 1000,
              totalBytes: photos.length * 1000,
              percentage: Math.round((processedPhotos / photos.length) * 100),
              status: 'uploading'
            });
          }
        }

        return {
          success: errors.length === 0,
          itemsProcessed: processedPhotos,
          errors
        };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeContacts: false,
        includeMessages: false
      });

      expect(result.success).toBe(false);
      expect(result.itemsProcessed).toBe(1); // Only first photo processed
      expect(result.errors.some(e => e.message.includes('Network changed'))).toBe(true);
    });

    it('should handle complete network loss during backup', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(true);

      // Mock complete network loss
      jest.spyOn(CloudStorageService, 'uploadFile').mockRejectedValue(new Error('Network unreachable'));

      const mockContacts = [
        { recordID: '1', displayName: 'John Doe', phoneNumbers: [], emailAddresses: [] }
      ];
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);
      
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async () => {
        try {
          await CloudStorageService.uploadFile('mock_data', 'contact.json', 'application/json', 'contact');
          return { success: true, itemsProcessed: 1, errors: [] };
        } catch (error) {
          return {
            success: false,
            itemsProcessed: 0,
            errors: [{
              id: 'network_unreachable',
              type: 'network',
              message: 'Network unreachable',
              timestamp: new Date(),
              retryable: true
            }]
          };
        }
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('unreachable'))).toBe(true);
    });
  });

  describe('Data Usage Warnings', () => {
    it('should provide data usage warnings on cellular networks', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      const cellularConfig: BackupConfiguration = {
        ...defaultConfiguration,
        wifiOnly: false
      };

      // Mock large photo dataset
      const largePhotos = Array.from({ length: 10 }, (_, i) => ({
        uri: `file://photo${i}.jpg`,
        filename: `photo${i}.jpg`,
        type: 'image/jpeg',
        fileSize: 5 * 1024 * 1024, // 5MB each
        timestamp: Date.now(),
        width: 1920,
        height: 1080
      }));

      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: largePhotos,
        excludedVideos: [],
        totalScanned: 10
      });

      const result = await BackupManager.startBackup({
        ...cellularConfig,
        includeContacts: false,
        includeMessages: false
      });

      // Should succeed but may show warnings about data usage
      expect(result.success).toBe(true);
      
      // Verify that warning notification was shown
      expect(NotificationService.showWarningNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          message: expect.stringContaining('cellular data')
        })
      );
    });

    it('should estimate data usage for backup preview', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      isWiFiConnected.mockResolvedValue(false);

      // Mock data with known sizes
      const mockContacts = Array.from({ length: 100 }, (_, i) => ({
        recordID: `${i}`,
        displayName: `Contact ${i}`,
        phoneNumbers: [{ label: 'mobile', number: `+123456789${i}` }],
        emailAddresses: []
      }));

      const mockMessages = Array.from({ length: 50 }, (_, i) => ({
        id: `${i}`,
        body: `Message ${i}`,
        address: '+1234567890',
        date: Date.now() - i * 1000
      }));

      const mockPhotos = Array.from({ length: 20 }, (_, i) => ({
        uri: `file://photo${i}.jpg`,
        filename: `photo${i}.jpg`,
        type: 'image/jpeg',
        fileSize: 2 * 1024 * 1024, // 2MB each
        timestamp: Date.now() - i * 1000
      }));

      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);
      jest.spyOn(MessageBackupService, 'scanMessages').mockResolvedValue(mockMessages as any);
      jest.spyOn(PhotoBackupService, 'scanPhotoLibrary').mockResolvedValue({
        photos: mockPhotos,
        excludedVideos: [],
        totalScanned: 20
      });

      const cellularConfig: BackupConfiguration = {
        ...defaultConfiguration,
        wifiOnly: false
      };

      const result = await BackupManager.startBackup(cellularConfig);

      expect(result.success).toBe(true);
      
      // Should have processed all items
      expect(result.itemsProcessed).toBe(170); // 100 contacts + 50 messages + 20 photos
      
      // Should have shown data usage warning
      expect(NotificationService.showWarningNotification).toHaveBeenCalled();
    });
  });

  describe('Network Recovery and Resilience', () => {
    it('should resume backup when network is restored', async () => {
      const { isWiFiConnected } = require('../../src/utils/helpers');
      
      // Start with network available
      isWiFiConnected.mockResolvedValue(true);

      let networkFailureSimulated = false;
      
      // Mock intermittent network failure
      jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async () => {
        if (!networkFailureSimulated) {
          networkFailureSimulated = true;
          throw new Error('Network temporarily unavailable');
        }
        return { success: true, fileId: 'recovered_upload' };
      });

      const mockContacts = [
        { recordID: '1', displayName: 'John Doe' },
        { recordID: '2', displayName: 'Jane Smith' }
      ];
      jest.spyOn(ContactBackupService, 'scanContacts').mockResolvedValue(mockContacts as any);
      
      // Mock backup with retry logic
      jest.spyOn(ContactBackupService, 'backupContacts').mockImplementation(async (contacts) => {
        let successfulBackups = 0;
        const errors: any[] = [];

        for (const contact of contacts) {
          try {
            await CloudStorageService.uploadFile('contact_data', `${contact.recordID}.json`, 'application/json', 'contact');
            successfulBackups++;
          } catch (error) {
            // Retry once
            try {
              await CloudStorageService.uploadFile('contact_data', `${contact.recordID}.json`, 'application/json', 'contact');
              successfulBackups++;
            } catch (retryError) {
              errors.push({
                id: `contact_error_${contact.recordID}`,
                type: 'network',
                message: retryError instanceof Error ? retryError.message : 'Network error',
                timestamp: new Date(),
                retryable: true
              });
            }
          }
        }

        return {
          success: errors.length === 0,
          itemsProcessed: successfulBackups,
          errors
        };
      });

      const result = await BackupManager.startBackup({
        ...defaultConfiguration,
        includeMessages: false,
        includePhotos: false
      });

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(2); // Both contacts should succeed after retry
    });
  });
});