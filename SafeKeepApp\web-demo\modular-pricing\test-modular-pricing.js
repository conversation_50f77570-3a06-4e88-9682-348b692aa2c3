/**
 * Test Runner for Modular Pricing UI Components
 * Simple test suite to verify component functionality
 */

class ModularPricingTestRunner {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0
        };
    }

    // Test registration
    test(name, testFn) {
        this.tests.push({ name, testFn });
    }

    // Assertion helpers
    assert(condition, message) {
        if (!condition) {
            throw new Error(message || 'Assertion failed');
        }
    }

    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `Expected ${expected}, got ${actual}`);
        }
    }

    assertNotNull(value, message) {
        if (value === null || value === undefined) {
            throw new Error(message || 'Value should not be null or undefined');
        }
    }

    // Run all tests
    async runTests() {
        console.log('🧪 Starting Modular Pricing UI Tests...\n');
        
        this.results = { passed: 0, failed: 0, total: this.tests.length };

        for (const test of this.tests) {
            try {
                console.log(`Running: ${test.name}`);
                await test.testFn();
                console.log(`✅ PASSED: ${test.name}`);
                this.results.passed++;
            } catch (error) {
                console.error(`❌ FAILED: ${test.name}`);
                console.error(`   Error: ${error.message}`);
                this.results.failed++;
            }
            console.log('');
        }

        this.printResults();
    }

    printResults() {
        console.log('📊 Test Results:');
        console.log(`   Total: ${this.results.total}`);
        console.log(`   Passed: ${this.results.passed}`);
        console.log(`   Failed: ${this.results.failed}`);
        console.log(`   Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
        
        if (this.results.failed === 0) {
            console.log('🎉 All tests passed!');
        } else {
            console.log('⚠️  Some tests failed. Check the output above.');
        }
    }
}

// Create test runner instance
const testRunner = new ModularPricingTestRunner();

// Test: Configuration Loading
testRunner.test('Configuration should load correctly', () => {
    testRunner.assertNotNull(window.ModularPricingConfig, 'Config should be available');
    testRunner.assertNotNull(window.ModularPricingConfig.SERVICE_CONFIG, 'Service config should exist');
    testRunner.assertNotNull(window.ModularPricingConfig.PRICING_PLANS, 'Pricing plans should exist');
    
    const services = Object.keys(window.ModularPricingConfig.SERVICE_CONFIG);
    testRunner.assertEqual(services.length, 3, 'Should have 3 services');
    testRunner.assert(services.includes('contacts'), 'Should include contacts service');
    testRunner.assert(services.includes('messages'), 'Should include messages service');
    testRunner.assert(services.includes('photos'), 'Should include photos service');
});

// Test: Utility Functions
testRunner.test('Utility functions should work correctly', () => {
    testRunner.assertNotNull(window.ModularPricingUtils, 'Utils should be available');
    
    // Test price formatting
    const formattedPrice = window.ModularPricingUtils.formatPrice(999);
    testRunner.assertEqual(formattedPrice, '$9.99', 'Price formatting should work');
    
    // Test savings calculation
    const savings = window.ModularPricingUtils.calculateSavingsPercentage(1000, 800);
    testRunner.assertEqual(savings, 20, 'Savings calculation should work');
    
    // Test validation
    const validation = window.ModularPricingUtils.validateServiceCombination(['contacts', 'messages']);
    testRunner.assert(validation.isValid, 'Valid service combination should pass');
    
    const invalidValidation = window.ModularPricingUtils.validateServiceCombination(['invalid']);
    testRunner.assert(!invalidValidation.isValid, 'Invalid service combination should fail');
});

// Test: ServiceCheckbox Component
testRunner.test('ServiceCheckbox component should initialize', () => {
    const service = window.ModularPricingConfig.SERVICE_CONFIG.contacts;
    
    const checkbox = new window.ServiceCheckbox({
        service: service,
        checked: false,
        onChange: () => {},
        disabled: false
    });
    
    testRunner.assertNotNull(checkbox.getElement(), 'Checkbox element should exist');
    testRunner.assert(checkbox.getElement().classList.contains('service-checkbox'), 'Should have correct class');
    
    // Test state update
    checkbox.updateProps({ checked: true });
    testRunner.assert(checkbox.getElement().classList.contains('checked'), 'Should update checked state');
    
    checkbox.destroy();
});

// Test: ServiceSelector Component
testRunner.test('ServiceSelector component should manage multiple services', () => {
    const services = Object.values(window.ModularPricingConfig.SERVICE_CONFIG);
    
    const selector = new window.ServiceSelector({
        services: services,
        selectedServices: ['contacts'],
        onServiceToggle: () => {},
        onServiceDetailsToggle: () => {},
        showDetails: null,
        disabled: false
    });
    
    testRunner.assertNotNull(selector.getElement(), 'Selector element should exist');
    testRunner.assert(selector.getElement().classList.contains('service-selector'), 'Should have correct class');
    
    // Test selected services
    const selectedServices = selector.getSelectedServices();
    testRunner.assertEqual(selectedServices.length, 1, 'Should have one selected service');
    testRunner.assertEqual(selectedServices[0].id, 'contacts', 'Should have contacts selected');
    
    selector.destroy();
});

// Test: PricingCalculator Component
testRunner.test('PricingCalculator should calculate prices correctly', async () => {
    const calculator = new window.PricingCalculator({
        selectedServices: ['contacts', 'messages'],
        onPricingUpdate: () => {},
        animateChanges: false
    });
    
    testRunner.assertNotNull(calculator.getElement(), 'Calculator element should exist');
    testRunner.assert(calculator.getElement().classList.contains('pricing-calculator'), 'Should have correct class');
    
    // Wait for initial calculation
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const pricing = calculator.getCurrentPricing();
    testRunner.assertNotNull(pricing, 'Should have pricing result');
    testRunner.assert(pricing.totalPrice > 0, 'Should have positive total price');
    
    calculator.destroy();
});

// Test: SavingsDisplay Component
testRunner.test('SavingsDisplay should show savings correctly', () => {
    const savingsDisplay = new window.SavingsDisplay({
        savings: 100,
        individualTotal: 800,
        bundlePrice: 700,
        currency: 'USD',
        showComparison: true,
        animate: false
    });
    
    testRunner.assertNotNull(savingsDisplay.getElement(), 'Savings display element should exist');
    testRunner.assert(savingsDisplay.getElement().classList.contains('savings-display'), 'Should have correct class');
    testRunner.assert(savingsDisplay.getElement().classList.contains('has-savings'), 'Should show has-savings state');
    
    // Test no savings state
    savingsDisplay.updateProps({ savings: 0 });
    testRunner.assert(savingsDisplay.getElement().classList.contains('no-savings'), 'Should show no-savings state');
    
    savingsDisplay.destroy();
});

// Test: PlanRecommendations Component
testRunner.test('PlanRecommendations should display plans correctly', () => {
    const recommendations = new window.PlanRecommendations({
        plans: [],
        selectedServices: ['contacts', 'messages', 'photos'],
        onPlanSelect: () => {},
        highlightPopular: true
    });
    
    testRunner.assertNotNull(recommendations.getElement(), 'Recommendations element should exist');
    testRunner.assert(recommendations.getElement().classList.contains('plan-recommendations'), 'Should have correct class');
    
    const recommendedPlan = recommendations.getRecommendedPlan();
    testRunner.assertNotNull(recommendedPlan, 'Should have a recommended plan');
    
    recommendations.destroy();
});

// Test: ModularPricingUI Main Component
testRunner.test('ModularPricingUI should integrate all components', async () => {
    const pricingUI = new window.ModularPricingUI({
        userId: 'test-user',
        onSubscriptionSelect: () => {},
        onPriceUpdate: () => {},
        initialServices: ['contacts'],
        theme: 'light',
        showRecommendations: true
    });
    
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 200));
    
    testRunner.assertNotNull(pricingUI.getElement(), 'Main UI element should exist');
    testRunner.assert(pricingUI.getElement().classList.contains('modular-pricing-ui'), 'Should have correct class');
    
    // Test initial state
    const selectedServices = pricingUI.getSelectedServices();
    testRunner.assertEqual(selectedServices.length, 1, 'Should have initial service selected');
    testRunner.assertEqual(selectedServices[0], 'contacts', 'Should have contacts selected');
    
    // Test service selection
    pricingUI.selectServices(['contacts', 'messages']);
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const updatedServices = pricingUI.getSelectedServices();
    testRunner.assertEqual(updatedServices.length, 2, 'Should have two services selected');
    
    // Test pricing
    const pricing = pricingUI.getCurrentPricing();
    testRunner.assertNotNull(pricing, 'Should have pricing data');
    
    // Test theme change
    pricingUI.setTheme('dark');
    testRunner.assert(pricingUI.getElement().classList.contains('theme-dark'), 'Should apply dark theme');
    
    pricingUI.destroy();
});

// Test: Error Handling
testRunner.test('Components should handle errors gracefully', () => {
    // Test with invalid service data
    try {
        const invalidCheckbox = new window.ServiceCheckbox({
            service: null,
            checked: false,
            onChange: () => {}
        });
        // Should not throw, but handle gracefully
        testRunner.assertNotNull(invalidCheckbox, 'Should handle invalid service data');
        invalidCheckbox.destroy();
    } catch (error) {
        // This is acceptable - component should validate input
        console.log('Component correctly validates input:', error.message);
    }
    
    // Test validation function
    const validation = window.ModularPricingUtils.validateServiceCombination(null);
    testRunner.assert(!validation.isValid, 'Should handle null input');
    testRunner.assert(validation.errors.length > 0, 'Should provide error messages');
});

// Export test runner for manual execution
if (typeof window !== 'undefined') {
    window.ModularPricingTestRunner = testRunner;
}

// Auto-run tests if this script is loaded directly
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        // Only run tests if we're in a test environment
        if (window.location.pathname.includes('test') || window.location.search.includes('test=true')) {
            setTimeout(() => {
                testRunner.runTests();
            }, 500); // Wait for all components to load
        }
    });
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModularPricingTestRunner;
}