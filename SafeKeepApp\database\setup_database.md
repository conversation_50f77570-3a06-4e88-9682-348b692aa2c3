# SafeKeep Database Setup Guide

This guide will help you set up the complete database schema for your SafeKeep application in Supabase.

## Prerequisites

1. Access to your Supabase project dashboard
2. SQL Editor access in Supabase
3. Admin privileges on your Supabase project

## Setup Steps

### Step 1: Run Core Tables Migration

1. **Open Supabase Dashboard** → Go to your SafeKeep project
2. **Navigate to SQL Editor** (left sidebar)
3. **Create a new query** and copy the contents of `migrations/001_create_core_tables.sql`
4. **Execute the query** to create all core tables

This will create:
- `users` table (extended user profiles)
- `file_metadata` table (encrypted file information)
- `backup_sessions` table (backup operation tracking)
- `storage_usage` table (detailed usage by category)
- `sync_status` table (device synchronization)
- `user_storage_summary` view (comprehensive storage overview)

### Step 2: Run Functions and Triggers Migration

1. **Create another new query** in SQL Editor
2. **Copy the contents** of `migrations/002_create_functions_and_triggers.sql`
3. **Execute the query** to create all functions and triggers

This will create:
- Storage usage calculation functions
- User management triggers
- Quota validation functions
- Backup progress tracking functions
- Cleanup utilities

### Step 3: Verify Installation

Run this verification query to ensure everything is set up correctly:

```sql
-- Verify tables exist
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status');

-- Verify functions exist
SELECT routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
  AND routine_name LIKE '%storage%' OR routine_name LIKE '%backup%';

-- Verify triggers exist
SELECT trigger_name, event_object_table
FROM information_schema.triggers
WHERE trigger_schema = 'public';
```

Expected results:
- 5 tables should be listed
- Several functions with 'storage' or 'backup' in the name
- 3 triggers should be active

### Step 4: Test Basic Functionality

Create a test user and verify the system works:

```sql
-- This will be handled automatically when users sign up through Supabase Auth
-- But you can test the storage calculation function:
SELECT public.get_subscription_limits('00000000-0000-0000-0000-000000000000'::UUID);
```

## Table Relationships

```
auth.users (Supabase Auth)
    ↓ (triggers create)
public.users
    ↓ (references)
├── file_metadata
├── backup_sessions
├── storage_usage
└── sync_status
```

## Storage Quotas by Subscription Tier

- **Basic**: 5GB storage, 1 device, daily backups
- **Premium**: 50GB storage, 3 devices, hourly backups
- **Family**: 200GB storage, 6 devices, hourly backups

## Automatic Features

Once set up, the database will automatically:

1. **Create user profiles** when users sign up through Supabase Auth
2. **Calculate storage usage** when files are uploaded/deleted
3. **Enforce storage quotas** before allowing uploads
4. **Track backup progress** in real-time
5. **Maintain data consistency** across all tables

## Security Features

- **Row Level Security (RLS)** will be configured in the next step
- **User data isolation** - users can only access their own data
- **Quota enforcement** - prevents storage abuse
- **Audit trails** - all operations are logged with timestamps

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure you're running queries as a Supabase admin
2. **Extension Missing**: The `uuid-ossp` extension should be enabled automatically
3. **Trigger Failures**: Check that the `auth.users` table exists (it's created by Supabase)

### Verification Queries

```sql
-- Check if RLS is enabled (will be set up in next step)
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Check storage usage calculation
SELECT * FROM public.user_storage_summary LIMIT 5;

-- Test function access
SELECT public.cleanup_orphaned_files();
```

## Next Steps

After completing this setup:

1. **Configure Row Level Security** (Task 2.2)
2. **Set up Storage Buckets** (Task 3.1)
3. **Configure Authentication** (Task 4.1)
4. **Test the complete system** (Task 10.1)

The database is now ready to support your SafeKeep application with secure, scalable data storage and automatic maintenance features.