import { supabase } from '../src/config/supabase';
import * as fileSearch from '../src/utils/fileSearch';
import * as supabaseHelpers from '../src/utils/supabaseHelpers';

// Mock the supabase client
jest.mock('../src/config/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    contains: jest.fn().mockReturnThis(),
    not: jest.fn().mockReturnThis(),
    or: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis()
  }
}));

// Mock the getCurrentUserId function
jest.mock('../src/utils/supabaseHelpers', () => ({
  getCurrentUserId: jest.fn()
}));

describe('File Search', () => {
  const mockUserId = 'test-user-id';
  
  beforeEach(() => {
    jest.clearAllMocks();
    (supabaseHelpers.getCurrentUserId as jest.Mock).mockResolvedValue(mockUserId);
  });

  describe('advancedSearch', () => {
    it('should perform advanced search with multiple filters', async () => {
      const mockFiles = [
        { id: 'file1', original_name: 'test1.jpg', category: 'photo' },
        { id: 'file2', original_name: 'test2.jpg', category: 'photo' }
      ];

      // Mock count query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.gte as jest.Mock).mockReturnThis();
      (supabase.lte as jest.Mock).mockReturnThis();
      (supabase.contains as jest.Mock).mockResolvedValueOnce({
        count: 2,
        error: null
      });

      // Mock main query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.gte as jest.Mock).mockReturnThis();
      (supabase.lte as jest.Mock).mockReturnThis();
      (supabase.contains as jest.Mock).mockReturnThis();
      (supabase.order as jest.Mock).mockReturnThis();
      (supabase.limit as jest.Mock).mockReturnThis();
      (supabase.range as jest.Mock).mockResolvedValueOnce({
        data: mockFiles,
        error: null
      });

      const searchParams = {
        category: 'photo' as const,
        minSize: 1000,
        maxSize: 5000000,
        tags: ['vacation', 'family'],
        isFavorite: true,
        limit: 10,
        offset: 0,
        sortBy: 'uploaded_at' as const,
        sortDirection: 'desc' as const
      };

      const result = await fileSearch.advancedSearch(searchParams);

      expect(result).toEqual({
        data: mockFiles,
        totalCount: 2,
        error: null
      });
      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(supabase.eq).toHaveBeenCalledWith('user_id', mockUserId);
      expect(supabase.eq).toHaveBeenCalledWith('category', 'photo');
      expect(supabase.gte).toHaveBeenCalledWith('size', 1000);
      expect(supabase.lte).toHaveBeenCalledWith('size', 5000000);
      expect(supabase.contains).toHaveBeenCalledWith('tags', ['vacation', 'family']);
      expect(supabase.eq).toHaveBeenCalledWith('favorite', true);
      expect(supabase.order).toHaveBeenCalledWith('uploaded_at', { ascending: false });
      expect(supabase.limit).toHaveBeenCalledWith(10);
    });
  });

  describe('searchByMimeType', () => {
    it('should search files by mime type', async () => {
      const mockFiles = [
        { id: 'file1', original_name: 'test1.jpg', mime_type: 'image/jpeg' },
        { id: 'file2', original_name: 'test2.jpg', mime_type: 'image/png' }
      ];

      // Mock count query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.ilike as jest.Mock).mockResolvedValueOnce({
        count: 2,
        error: null
      });

      // Mock main query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.ilike as jest.Mock).mockReturnThis();
      (supabase.order as jest.Mock).mockReturnThis();
      (supabase.range as jest.Mock).mockResolvedValueOnce({
        data: mockFiles,
        error: null
      });

      const result = await fileSearch.searchByMimeType('image');

      expect(result).toEqual({
        data: mockFiles,
        totalCount: 2,
        error: null
      });
      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(supabase.eq).toHaveBeenCalledWith('user_id', mockUserId);
      expect(supabase.ilike).toHaveBeenCalledWith('mime_type', 'image%');
      expect(supabase.order).toHaveBeenCalledWith('uploaded_at', { ascending: false });
    });
  });

  describe('searchByDateRange', () => {
    it('should search files by date range', async () => {
      const mockFiles = [
        { id: 'file1', original_name: 'test1.jpg', uploaded_at: '2023-01-01T00:00:00Z' },
        { id: 'file2', original_name: 'test2.jpg', uploaded_at: '2023-01-02T00:00:00Z' }
      ];

      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');

      // Mock count query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.gte as jest.Mock).mockReturnThis();
      (supabase.lte as jest.Mock).mockResolvedValueOnce({
        count: 2,
        error: null
      });

      // Mock main query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockReturnThis();
      (supabase.gte as jest.Mock).mockReturnThis();
      (supabase.lte as jest.Mock).mockReturnThis();
      (supabase.order as jest.Mock).mockReturnThis();
      (supabase.range as jest.Mock).mockResolvedValueOnce({
        data: mockFiles,
        error: null
      });

      const result = await fileSearch.searchByDateRange(startDate, endDate);

      expect(result).toEqual({
        data: mockFiles,
        totalCount: 2,
        error: null
      });
      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(supabase.eq).toHaveBeenCalledWith('user_id', mockUserId);
      expect(supabase.gte).toHaveBeenCalledWith('uploaded_at', startDate.toISOString());
      expect(supabase.lte).toHaveBeenCalledWith('uploaded_at', endDate.toISOString());
      expect(supabase.order).toHaveBeenCalledWith('uploaded_at', { ascending: false });
    });
  });

  describe('getFileMetadataStats', () => {
    it('should return file metadata statistics', async () => {
      const mockFiles = [
        { 
          id: 'file1', 
          original_name: 'test1.jpg', 
          category: 'photo', 
          size: 1000, 
          mime_type: 'image/jpeg',
          uploaded_at: '2023-01-01T00:00:00Z'
        },
        { 
          id: 'file2', 
          original_name: 'test2.jpg', 
          category: 'photo', 
          size: 2000, 
          mime_type: 'image/png',
          uploaded_at: '2023-01-15T00:00:00Z'
        },
        { 
          id: 'file3', 
          original_name: 'contact.vcf', 
          category: 'contact', 
          size: 500, 
          mime_type: 'text/vcard',
          uploaded_at: '2023-02-01T00:00:00Z'
        }
      ];

      // Mock query
      (supabase.from as jest.Mock).mockReturnThis();
      (supabase.select as jest.Mock).mockReturnThis();
      (supabase.eq as jest.Mock).mockResolvedValueOnce({
        data: mockFiles,
        error: null
      });

      const result = await fileSearch.getFileMetadataStats();

      expect(result.totalFiles).toBe(3);
      expect(result.totalSize).toBe(3500);
      expect(result.filesByCategory).toEqual({ photo: 2, contact: 1 });
      expect(result.sizeByCategory).toEqual({ photo: 3000, contact: 500 });
      expect(result.filesByMimeType).toEqual({ image: 2, text: 1 });
      expect(result.filesByMonth).toEqual({ '2023-01': 2, '2023-02': 1 });
      expect(result.error).toBeNull();
      expect(supabase.from).toHaveBeenCalledWith('file_metadata');
      expect(supabase.eq).toHaveBeenCalledWith('user_id', mockUserId);
    });
  });
});