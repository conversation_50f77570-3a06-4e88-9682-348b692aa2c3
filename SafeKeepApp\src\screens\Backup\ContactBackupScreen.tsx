import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { 
  <PERSON>ton, 
  Card, 
  Text, 
  ProgressBar, 
  Chip, 
  List,
  ActivityIndicator 
} from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import ContactBackupService, { ContactData, ContactBackupProgress, ContactBackupResult } from '../../services/ContactBackupService';
import { COLORS, SPACING } from '../../utils/constants';

const ContactBackupScreen = () => {
  const [contacts, setContacts] = useState<ContactData[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [backupProgress, setBackupProgress] = useState<ContactBackupProgress | null>(null);
  const [backupResult, setBackupResult] = useState<ContactBackupResult | null>(null);
  const [backupStats, setBackupStats] = useState<any>(null);
  const [scanComplete, setScanComplete] = useState(false);

  useEffect(() => {
    loadBackupStats();
    handleScanContacts();
  }, []);

  const loadBackupStats = async () => {
    try {
      const stats = await ContactBackupService.getBackupStats();
      setBackupStats(stats);
    } catch (error) {
      console.error('Failed to load backup stats:', error);
    }
  };

  const handleScanContacts = async () => {
    setIsScanning(true);
    setScanComplete(false);
    setBackupResult(null);
    
    try {
      console.log('📞 Starting contact scan...');
      const scannedContacts = await ContactBackupService.scanContacts();
      setContacts(scannedContacts);
      setScanComplete(true);
      
      Alert.alert(
        '📞 Contacts Found!',
        `Found ${scannedContacts.length} contacts in your phone.\n\nReady to backup your important contact information?`,
        [{ text: 'Great!' }]
      );
    } catch (error) {
      console.error('Contact scan error:', error);
      Alert.alert(
        'Scan Error',
        'Could not access your contacts. Please check permissions and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Try Again', onPress: handleScanContacts }
        ]
      );
    } finally {
      setIsScanning(false);
    }
  };

  const handleStartBackup = async () => {
    if (contacts.length === 0) {
      Alert.alert(
        'No Contacts Found',
        'Please scan for contacts first before starting backup.',
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      '🚀 Start Contact Backup?',
      `This will backup ${contacts.length} contacts to the cloud.\n\n• Duplicates will be automatically detected\n• Your contacts will be encrypted for security\n• All phone numbers and emails included\n• You can restore on any device\n\nStart backup now?`,
      [
        { text: 'Not Now', style: 'cancel' },
        { text: 'Start Backup', onPress: startBackupProcess }
      ]
    );
  };

  const startBackupProcess = async () => {
    setIsBackingUp(true);
    setBackupResult(null);
    
    try {
      const result = await ContactBackupService.backupContacts(
        contacts,
        (progress: ContactBackupProgress) => {
          setBackupProgress(progress);
        }
      );
      
      setBackupResult(result);
      await loadBackupStats(); // Refresh stats
      showBackupCompleteDialog(result);
    } catch (error) {
      console.error('Backup error:', error);
      Alert.alert(
        'Backup Error',
        'Something went wrong during backup. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsBackingUp(false);
      setBackupProgress(null);
    }
  };

  const showBackupCompleteDialog = (result: ContactBackupResult) => {
    const minutes = Math.round(result.duration / 60000);
    const seconds = Math.round((result.duration % 60000) / 1000);
    const timeString = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;

    if (result.success) {
      Alert.alert(
        '🎉 Backup Complete!',
        `Successfully backed up ${result.backedUpContacts} contacts in ${timeString}!\n\n` +
        `• ${result.duplicatesFound} duplicates were skipped\n` +
        `• All your contacts are now safely stored in the cloud\n` +
        `• You can restore them on any device!`,
        [{ text: 'Wonderful!' }]
      );
    } else {
      Alert.alert(
        '⚠️ Backup Had Issues',
        `Backup completed with some problems.\n\n` +
        `• ${result.skippedContacts} contacts had errors\n` +
        `• ${result.duplicatesFound} duplicates were skipped\n` +
        `• Time taken: ${timeString}\n\n` +
        `Would you like to try again?`,
        [
          { text: 'Later', style: 'cancel' },
          { text: 'Try Again', onPress: handleStartBackup }
        ]
      );
    }
  };

  const handleRestoreContacts = async () => {
    Alert.alert(
      '📞 Restore Contacts?',
      'This will download your latest contact backup from the cloud.\n\nNote: This will not automatically add contacts to your phone - you can review them first.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Restore', onPress: performRestore }
      ]
    );
  };

  const performRestore = async () => {
    try {
      const result = await ContactBackupService.restoreContacts();
      
      if (result.success) {
        Alert.alert(
          '✅ Restore Complete!',
          `Successfully restored ${result.contacts?.length || 0} contacts from your backup.\n\nYou can now review and import them to your phone.`,
          [{ text: 'Great!' }]
        );
      } else {
        Alert.alert(
          'Restore Failed',
          result.error || 'Could not restore contacts from backup.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Restore Error',
        'Something went wrong during restore. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return COLORS.success;
      case 'error': return COLORS.error;
      default: return COLORS.primary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scanning': return 'magnify';
      case 'processing': return 'cog';
      case 'uploading': return 'cloud-upload';
      case 'completed': return 'check-circle';
      case 'error': return 'alert-circle';
      default: return 'information';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Contact Backup',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.content}>
        {/* Contact Library Status */}
        <Card style={styles.statusCard}>
          <Card.Content>
            <View style={styles.statusHeader}>
              <Icon name="contacts" size={32} color={COLORS.primary} />
              <Text variant="titleLarge" style={styles.statusTitle}>
                Contact Library
              </Text>
            </View>

            {isScanning ? (
              <View style={styles.scanningContainer}>
                <ActivityIndicator size="large" color={COLORS.primary} />
                <Text style={styles.scanningText}>Scanning your contacts...</Text>
              </View>
            ) : (
              <View style={styles.contactStats}>
                <View style={styles.statRow}>
                  <Text style={styles.statLabel}>Contacts Found:</Text>
                  <Chip style={styles.statChip}>{contacts.length}</Chip>
                </View>
                
                {scanComplete && (
                  <>
                    <View style={styles.statRow}>
                      <Text style={styles.statLabel}>With Phone Numbers:</Text>
                      <Text style={styles.statValue}>
                        {contacts.filter(c => c.phoneNumbers.length > 0).length}
                      </Text>
                    </View>
                    
                    <View style={styles.statRow}>
                      <Text style={styles.statLabel}>With Email Addresses:</Text>
                      <Text style={styles.statValue}>
                        {contacts.filter(c => c.emailAddresses.length > 0).length}
                      </Text>
                    </View>
                    
                    <Button
                      mode="outlined"
                      onPress={handleScanContacts}
                      style={styles.rescanButton}
                      icon="refresh"
                    >
                      Rescan Contacts
                    </Button>
                  </>
                )}
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Backup Progress */}
        {(isBackingUp || backupProgress) && (
          <Card style={styles.progressCard}>
            <Card.Content>
              <View style={styles.progressHeader}>
                <Icon 
                  name={getStatusIcon(backupProgress?.status || 'uploading')} 
                  size={24} 
                  color={getStatusColor(backupProgress?.status || 'uploading')} 
                />
                <Text variant="titleMedium" style={styles.progressTitle}>
                  {backupProgress?.status === 'scanning' && 'Scanning Contacts...'}
                  {backupProgress?.status === 'processing' && 'Processing Contacts...'}
                  {backupProgress?.status === 'uploading' && 'Uploading Contacts...'}
                  {backupProgress?.status === 'completed' && 'Backup Complete!'}
                  {backupProgress?.status === 'error' && 'Backup Error'}
                </Text>
              </View>

              {backupProgress && (
                <>
                  <Text style={styles.currentContact}>
                    {backupProgress.currentContact}
                  </Text>

                  <ProgressBar 
                    progress={backupProgress.percentage / 100} 
                    color={getStatusColor(backupProgress.status)}
                    style={styles.progressBar}
                  />

                  <View style={styles.progressStats}>
                    <Text style={styles.progressText}>
                      {backupProgress.processedContacts} of {backupProgress.totalContacts} contacts
                    </Text>
                    <Text style={styles.progressText}>
                      {backupProgress.percentage}%
                    </Text>
                  </View>
                </>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Backup Statistics */}
        {backupStats && backupStats.totalBackups > 0 && (
          <Card style={styles.statsCard}>
            <Card.Content>
              <View style={styles.statsHeader}>
                <Icon name="chart-line" size={24} color={COLORS.primary} />
                <Text variant="titleMedium" style={styles.statsTitle}>
                  Backup History
                </Text>
              </View>

              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{backupStats.totalBackups}</Text>
                  <Text style={styles.statLabel}>Total Backups</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{backupStats.totalContacts}</Text>
                  <Text style={styles.statLabel}>Contacts Backed Up</Text>
                </View>
              </View>

              {backupStats.lastBackupDate && (
                <Text style={styles.lastBackupText}>
                  Last backup: {new Date(backupStats.lastBackupDate).toLocaleDateString()}
                </Text>
              )}
            </Card.Content>
          </Card>
        )}

        {/* Action Buttons */}
        <Card style={styles.actionCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.actionTitle}>
              Ready to Backup Your Contacts?
            </Text>
            
            <Text style={styles.actionDescription}>
              Your contacts will be safely encrypted and stored in the cloud. 
              You can restore them on any device anytime.
            </Text>

            <View style={styles.actionButtons}>
              {!isBackingUp && scanComplete && (
                <Button
                  mode="contained"
                  onPress={handleStartBackup}
                  disabled={contacts.length === 0}
                  style={styles.primaryButton}
                  icon="cloud-upload"
                >
                  Start Contact Backup
                </Button>
              )}

              {/* Always show restore button - it should be available at all times */}
              <Button
                mode="outlined"
                onPress={handleRestoreContacts}
                style={styles.secondaryButton}
                icon="cloud-download"
              >
                Restore from Backup
              </Button>

              {!scanComplete && (
                <Button
                  mode="outlined"
                  onPress={handleScanContacts}
                  disabled={isScanning}
                  loading={isScanning}
                  style={styles.secondaryButton}
                  icon="magnify"
                >
                  Scan for Contacts
                </Button>
              )}
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  statusCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statusTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  scanningContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.lg,
  },
  scanningText: {
    marginTop: SPACING.sm,
    color: COLORS.textSecondary,
  },
  contactStats: {
    gap: SPACING.sm,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    color: COLORS.text,
    fontSize: 16,
  },
  statValue: {
    color: COLORS.textSecondary,
    fontSize: 16,
  },
  statChip: {
    backgroundColor: COLORS.primary,
  },
  rescanButton: {
    marginTop: SPACING.sm,
    borderColor: COLORS.primary,
  },
  progressCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  progressTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  currentContact: {
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
    fontSize: 14,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: SPACING.sm,
  },
  progressStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressText: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  statsCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  statsTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  lastBackupText: {
    textAlign: 'center',
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  actionCard: {
    elevation: 4,
  },
  actionTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  actionDescription: {
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.lg,
  },
  actionButtons: {
    gap: SPACING.sm,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    borderColor: COLORS.primary,
  },
});

export default ContactBackupScreen;
