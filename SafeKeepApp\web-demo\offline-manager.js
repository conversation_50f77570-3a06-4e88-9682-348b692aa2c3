/**
 * Offline Manager
 * Handles offline functionality and graceful degradation
 */

class OfflineManager {
    constructor() {
        this.isOffline = !navigator.onLine;
        this.offlineFeatures = new Set();
        this.cachedData = new Map();
        this.offlineQueue = [];
        this.syncInProgress = false;
        
        this.setupOfflineListeners();
        this.initializeOfflineFeatures();
        this.loadOfflineData();
    }

    setupOfflineListeners() {
        window.addEventListener('online', () => {
            this.handleOnline();
        });

        window.addEventListener('offline', () => {
            this.handleOffline();
        });

        // Listen for visibility changes to sync when app becomes visible
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && navigator.onLine && !this.syncInProgress) {
                this.syncOfflineData();
            }
        });
    }

    initializeOfflineFeatures() {
        // Define which features work offline
        this.offlineCapabilities = {
            'demo_state': {
                enabled: true,
                storage: 'localStorage',
                syncRequired: false
            },
            'user_preferences': {
                enabled: true,
                storage: 'localStorage',
                syncRequired: true
            },
            'backup_history_view': {
                enabled: true,
                storage: 'indexedDB',
                syncRequired: false
            },
            'encryption_demo': {
                enabled: true,
                storage: 'memory',
                syncRequired: false
            },
            'tutorial_progress': {
                enabled: true,
                storage: 'localStorage',
                syncRequired: true
            },
            'error_logs': {
                enabled: true,
                storage: 'localStorage',
                syncRequired: true
            },
            'backup_operations': {
                enabled: false,
                storage: null,
                syncRequired: true,
                fallback: 'queue_for_online'
            },
            'restore_operations': {
                enabled: false,
                storage: null,
                syncRequired: true,
                fallback: 'queue_for_online'
            },
            'payment_processing': {
                enabled: false,
                storage: null,
                syncRequired: true,
                fallback: 'show_offline_message'
            },
            'realtime_updates': {
                enabled: false,
                storage: null,
                syncRequired: true,
                fallback: 'use_cached_data'
            }
        };
    }

    loadOfflineData() {
        try {
            // Load cached data from localStorage
            const cachedDataStr = localStorage.getItem('safekeep_offline_cache');
            if (cachedDataStr) {
                const cachedData = JSON.parse(cachedDataStr);
                Object.entries(cachedData).forEach(([key, value]) => {
                    this.cachedData.set(key, value);
                });
            }

            // Load offline queue
            const queueStr = localStorage.getItem('safekeep_offline_queue');
            if (queueStr) {
                this.offlineQueue = JSON.parse(queueStr);
            }
        } catch (error) {
            console.warn('Failed to load offline data:', error);
        }
    }

    saveOfflineData() {
        try {
            // Save cached data
            const cacheObj = {};
            this.cachedData.forEach((value, key) => {
                cacheObj[key] = value;
            });
            localStorage.setItem('safekeep_offline_cache', JSON.stringify(cacheObj));

            // Save offline queue
            localStorage.setItem('safekeep_offline_queue', JSON.stringify(this.offlineQueue));
        } catch (error) {
            console.warn('Failed to save offline data:', error);
        }
    }

    handleOffline() {
        this.isOffline = true;
        
        // Show offline indicator
        this.showOfflineIndicator();
        
        // Enable offline features
        this.enableOfflineFeatures();
        
        // Notify error handler
        if (window.errorHandler) {
            window.errorHandler.handleNetworkLoss();
        }
        
        // Notify user
        this.notifyOfflineMode();
    }

    handleOnline() {
        this.isOffline = false;
        
        // Hide offline indicator
        this.hideOfflineIndicator();
        
        // Sync offline data
        this.syncOfflineData();
        
        // Notify error handler
        if (window.errorHandler) {
            window.errorHandler.handleNetworkRecovery();
        }
        
        // Notify user
        this.notifyOnlineMode();
    }

    showOfflineIndicator() {
        let indicator = document.querySelector('.safekeep-offline-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'safekeep-offline-indicator';
            indicator.innerHTML = 'Working offline - changes will sync when connection is restored';
            document.body.appendChild(indicator);
        }
        indicator.style.display = 'flex';
    }

    hideOfflineIndicator() {
        const indicator = document.querySelector('.safekeep-offline-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    enableOfflineFeatures() {
        Object.entries(this.offlineCapabilities).forEach(([feature, config]) => {
            if (config.enabled) {
                this.offlineFeatures.add(feature);
            }
        });
    }

    notifyOfflineMode() {
        if (window.errorHandler) {
            window.errorHandler.showUserNotification({
                type: 'warning',
                title: 'Offline Mode',
                message: 'You\'re working offline. Some features are limited, but you can still explore the demo.',
                persistent: true,
                actions: [
                    {
                        label: 'View Offline Features',
                        action: () => this.showOfflineFeatures()
                    }
                ]
            });
        }
    }

    notifyOnlineMode() {
        if (window.errorHandler) {
            window.errorHandler.showUserNotification({
                type: 'success',
                title: 'Back Online',
                message: 'Connection restored. Syncing your data...',
                autoHide: true
            });
        }
    }

    showOfflineFeatures() {
        const availableFeatures = Object.entries(this.offlineCapabilities)
            .filter(([_, config]) => config.enabled)
            .map(([feature, _]) => this.getFeatureDisplayName(feature));

        if (window.errorHandler) {
            window.errorHandler.showUserNotification({
                type: 'info',
                title: 'Available Offline Features',
                message: `You can still use: ${availableFeatures.join(', ')}`,
                autoHide: false
            });
        }
    }

    getFeatureDisplayName(feature) {
        const displayNames = {
            'demo_state': 'Demo Navigation',
            'user_preferences': 'Settings',
            'backup_history_view': 'Backup History',
            'encryption_demo': 'Encryption Demo',
            'tutorial_progress': 'Tutorial',
            'error_logs': 'Error Logs'
        };
        return displayNames[feature] || feature;
    }

    // Feature availability checking
    isFeatureAvailable(feature) {
        if (!this.isOffline) return true;
        
        const config = this.offlineCapabilities[feature];
        return config ? config.enabled : false;
    }

    getFeatureFallback(feature) {
        if (!this.isOffline) return null;
        
        const config = this.offlineCapabilities[feature];
        return config ? config.fallback : null;
    }

    // Data caching methods
    cacheData(key, data, ttl = 3600000) { // 1 hour default TTL
        this.cachedData.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
        this.saveOfflineData();
    }

    getCachedData(key) {
        const cached = this.cachedData.get(key);
        if (!cached) return null;
        
        // Check if data has expired
        if (Date.now() - cached.timestamp > cached.ttl) {
            this.cachedData.delete(key);
            this.saveOfflineData();
            return null;
        }
        
        return cached.data;
    }

    clearCache(key = null) {
        if (key) {
            this.cachedData.delete(key);
        } else {
            this.cachedData.clear();
        }
        this.saveOfflineData();
    }

    // Offline queue management
    queueForSync(operation) {
        this.offlineQueue.push({
            id: this.generateId(),
            timestamp: Date.now(),
            operation
        });
        this.saveOfflineData();
    }

    async syncOfflineData() {
        if (this.syncInProgress || this.isOffline || this.offlineQueue.length === 0) {
            return;
        }

        this.syncInProgress = true;
        
        try {
            const itemsToSync = [...this.offlineQueue];
            let syncedCount = 0;
            
            for (const item of itemsToSync) {
                try {
                    await this.syncItem(item);
                    
                    // Remove synced item from queue
                    this.offlineQueue = this.offlineQueue.filter(q => q.id !== item.id);
                    syncedCount++;
                    
                } catch (error) {
                    console.warn('Failed to sync item:', item.id, error);
                    
                    // Keep item in queue for retry, but limit retries
                    const queueIndex = this.offlineQueue.findIndex(q => q.id === item.id);
                    if (queueIndex !== -1) {
                        this.offlineQueue[queueIndex].retries = (this.offlineQueue[queueIndex].retries || 0) + 1;
                        
                        // Remove if too many retries
                        if (this.offlineQueue[queueIndex].retries > 3) {
                            this.offlineQueue.splice(queueIndex, 1);
                        }
                    }
                }
            }
            
            this.saveOfflineData();
            
            if (syncedCount > 0) {
                this.notifySyncComplete(syncedCount);
            }
            
        } finally {
            this.syncInProgress = false;
        }
    }

    async syncItem(item) {
        const { operation } = item;
        
        switch (operation.type) {
            case 'user_preferences':
                return await this.syncUserPreferences(operation.data);
            case 'tutorial_progress':
                return await this.syncTutorialProgress(operation.data);
            case 'error_logs':
                return await this.syncErrorLogs(operation.data);
            default:
                throw new Error(`Unknown sync operation: ${operation.type}`);
        }
    }

    async syncUserPreferences(data) {
        // Simulate API call to sync user preferences
        if (window.authManager && window.authManager.currentUser) {
            // In a real app, this would make an API call
            console.log('Syncing user preferences:', data);
            return true;
        }
        throw new Error('No authenticated user for preferences sync');
    }

    async syncTutorialProgress(data) {
        // Simulate API call to sync tutorial progress
        console.log('Syncing tutorial progress:', data);
        return true;
    }

    async syncErrorLogs(data) {
        // Simulate API call to sync error logs
        console.log('Syncing error logs:', data);
        return true;
    }

    notifySyncComplete(count) {
        if (window.errorHandler) {
            window.errorHandler.showUserNotification({
                type: 'success',
                title: 'Sync Complete',
                message: `${count} item${count > 1 ? 's' : ''} synced successfully.`,
                autoHide: true
            });
        }
    }

    // Offline-specific UI adaptations
    adaptUIForOffline() {
        // Disable online-only buttons
        const onlineOnlyElements = document.querySelectorAll('[data-requires-online]');
        onlineOnlyElements.forEach(element => {
            element.disabled = true;
            element.title = 'This feature requires an internet connection';
        });

        // Show offline alternatives
        const offlineAlternatives = document.querySelectorAll('[data-offline-alternative]');
        offlineAlternatives.forEach(element => {
            element.style.display = 'block';
        });

        // Update status indicators
        const statusIndicators = document.querySelectorAll('[data-connection-status]');
        statusIndicators.forEach(element => {
            element.textContent = 'Offline';
            element.className = 'status-offline';
        });
    }

    adaptUIForOnline() {
        // Re-enable online-only buttons
        const onlineOnlyElements = document.querySelectorAll('[data-requires-online]');
        onlineOnlyElements.forEach(element => {
            element.disabled = false;
            element.title = '';
        });

        // Hide offline alternatives
        const offlineAlternatives = document.querySelectorAll('[data-offline-alternative]');
        offlineAlternatives.forEach(element => {
            element.style.display = 'none';
        });

        // Update status indicators
        const statusIndicators = document.querySelectorAll('[data-connection-status]');
        statusIndicators.forEach(element => {
            element.textContent = 'Online';
            element.className = 'status-online';
        });
    }

    // Public API methods
    enable() {
        this.enableOfflineFeatures();
        this.adaptUIForOffline();
    }

    disable() {
        this.offlineFeatures.clear();
        this.adaptUIForOnline();
    }

    getStatus() {
        return {
            isOffline: this.isOffline,
            availableFeatures: Array.from(this.offlineFeatures),
            cachedDataCount: this.cachedData.size,
            queuedOperations: this.offlineQueue.length,
            syncInProgress: this.syncInProgress
        };
    }

    // Utility methods
    generateId() {
        return `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Force sync (for testing)
    forcSync() {
        if (!this.isOffline) {
            this.syncOfflineData();
        }
    }
}

// Initialize global offline manager
window.OfflineManager = OfflineManager;
window.offlineManager = new OfflineManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OfflineManager;
}