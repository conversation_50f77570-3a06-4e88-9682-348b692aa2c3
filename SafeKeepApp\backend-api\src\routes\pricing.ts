import { Router } from 'express';
import { PricingController } from '../controllers/PricingController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const pricingController = new PricingController();

/**
 * Pricing routes for modular pricing system
 * All routes require authentication
 */

// GET /api/pricing/combinations - Returns available service combinations with pricing
router.get('/combinations', authenticateToken, (req, res) => {
  pricingController.getServiceCombinations(req, res);
});

// POST /api/pricing/calculate - Returns optimal pricing for selected services
router.post('/calculate', authenticateToken, (req, res) => {
  pricingController.calculateOptimalPrice(req, res);
});

// GET /api/pricing/recommendations/:userId - Returns upgrade/downgrade recommendations for user
router.get('/recommendations/:userId', authenticateToken, (req, res) => {
  pricingController.getPlanRecommendations(req, res);
});

export default router;