-- SafeKeep Database Functions and Triggers
-- Automated storage calculation, user management, and data consistency

-- Function to calculate and update storage usage for a user
CREATE OR REPLACE FUNCTION public.update_user_storage_usage(target_user_id UUID)
RETURNS void AS $$
DECLARE
  total_storage BIGINT := 0;
  category_record RECORD;
BEGIN
  -- Calculate total storage used across all categories
  SELECT COALESCE(SUM(encrypted_size), 0) INTO total_storage
  FROM public.file_metadata
  WHERE user_id = target_user_id AND is_backed_up = true;
  
  -- Update user's total storage usage
  UPDATE public.users 
  SET storage_used = total_storage,
      last_login_at = CASE WHEN last_login_at < NOW() - INTERVAL '1 hour' THEN NOW() ELSE last_login_at END
  WHERE id = target_user_id;
  
  -- Update detailed storage usage by category
  FOR category_record IN 
    SELECT 
      category,
      COALESCE(SUM(encrypted_size), 0) as bytes_used,
      COUNT(*) as file_count
    FROM public.file_metadata 
    WHERE user_id = target_user_id AND is_backed_up = true
    GROUP BY category
  LOOP
    INSERT INTO public.storage_usage (user_id, category, bytes_used, file_count, last_updated)
    VALUES (target_user_id, category_record.category, category_record.bytes_used, category_record.file_count, NOW())
    ON CONFLICT (user_id, category) 
    DO UPDATE SET 
      bytes_used = EXCLUDED.bytes_used,
      file_count = EXCLUDED.file_count,
      last_updated = NOW();
  END LOOP;
  
  -- Ensure all categories exist (even with 0 usage)
  INSERT INTO public.storage_usage (user_id, category, bytes_used, file_count, last_updated)
  SELECT target_user_id, category, 0, 0, NOW()
  FROM (VALUES ('photo'), ('contact'), ('message'), ('other')) AS categories(category)
  ON CONFLICT (user_id, category) DO NOTHING;
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  -- Create user profile when auth.users record is created
  INSERT INTO public.users (id, email, display_name, created_at, last_login_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email),
    NOW(),
    NOW()
  );
  
  -- Initialize storage usage records
  PERFORM public.update_user_storage_usage(NEW.id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate storage quota before file upload
CREATE OR REPLACE FUNCTION public.check_storage_quota()
RETURNS trigger AS $$
DECLARE
  current_usage BIGINT;
  user_quota BIGINT;
BEGIN
  -- Get current usage and quota
  SELECT storage_used, storage_quota INTO current_usage, user_quota
  FROM public.users WHERE id = NEW.user_id;
  
  -- Check if adding this file would exceed quota
  IF (current_usage + NEW.encrypted_size) > user_quota THEN
    RAISE EXCEPTION 'Storage quota exceeded. Current: % bytes, Quota: % bytes, Attempting to add: % bytes', 
      current_usage, user_quota, NEW.encrypted_size;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update backup session progress
CREATE OR REPLACE FUNCTION public.update_backup_progress(
  session_id UUID,
  new_processed_files INTEGER DEFAULT NULL,
  new_failed_files INTEGER DEFAULT NULL,
  new_processed_bytes BIGINT DEFAULT NULL,
  new_status TEXT DEFAULT NULL,
  error_msg TEXT DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  UPDATE public.backup_sessions
  SET 
    processed_files = COALESCE(new_processed_files, processed_files),
    failed_files = COALESCE(new_failed_files, failed_files),
    processed_bytes = COALESCE(new_processed_bytes, processed_bytes),
    status = COALESCE(new_status, status),
    error_message = COALESCE(error_msg, error_message),
    completed_at = CASE 
      WHEN new_status IN ('completed', 'failed', 'cancelled') THEN NOW()
      ELSE completed_at
    END
  WHERE id = session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up orphaned files
CREATE OR REPLACE FUNCTION public.cleanup_orphaned_files()
RETURNS INTEGER AS $$
DECLARE
  cleanup_count INTEGER := 0;
BEGIN
  -- Delete file metadata for files that no longer exist in storage
  -- This would typically be called by a scheduled job
  DELETE FROM public.file_metadata
  WHERE uploaded_at < NOW() - INTERVAL '30 days'
    AND is_backed_up = false;
  
  GET DIAGNOSTICS cleanup_count = ROW_COUNT;
  
  RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user subscription limits
CREATE OR REPLACE FUNCTION public.get_subscription_limits(user_id UUID)
RETURNS TABLE(
  storage_quota BIGINT,
  max_devices INTEGER,
  backup_frequency TEXT,
  has_premium_features BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    CASE u.subscription_tier
      WHEN 'basic' THEN 5368709120::BIGINT     -- 5GB
      WHEN 'premium' THEN 53687091200::BIGINT  -- 50GB
      WHEN 'family' THEN ************::BIGINT  -- 200GB
      ELSE 5368709120::BIGINT
    END as storage_quota,
    CASE u.subscription_tier
      WHEN 'basic' THEN 1
      WHEN 'premium' THEN 3
      WHEN 'family' THEN 6
      ELSE 1
    END as max_devices,
    CASE u.subscription_tier
      WHEN 'basic' THEN 'daily'
      WHEN 'premium' THEN 'hourly'
      WHEN 'family' THEN 'hourly'
      ELSE 'daily'
    END as backup_frequency,
    CASE u.subscription_tier
      WHEN 'basic' THEN false
      ELSE true
    END as has_premium_features
  FROM public.users u
  WHERE u.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger: Update storage usage when files are added/removed
CREATE OR REPLACE FUNCTION public.trigger_update_storage_usage()
RETURNS trigger AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM public.update_user_storage_usage(NEW.user_id);
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    PERFORM public.update_user_storage_usage(NEW.user_id);
    IF OLD.user_id != NEW.user_id THEN
      PERFORM public.update_user_storage_usage(OLD.user_id);
    END IF;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM public.update_user_storage_usage(OLD.user_id);
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

DROP TRIGGER IF EXISTS check_quota_before_insert ON public.file_metadata;
CREATE TRIGGER check_quota_before_insert
  BEFORE INSERT ON public.file_metadata
  FOR EACH ROW EXECUTE FUNCTION public.check_storage_quota();

DROP TRIGGER IF EXISTS update_storage_on_file_change ON public.file_metadata;
CREATE TRIGGER update_storage_on_file_change
  AFTER INSERT OR UPDATE OR DELETE ON public.file_metadata
  FOR EACH ROW EXECUTE FUNCTION public.trigger_update_storage_usage();

-- Create RPC functions for client access
CREATE OR REPLACE FUNCTION public.refresh_storage_usage(user_id UUID)
RETURNS void AS $$
BEGIN
  PERFORM public.update_user_storage_usage(user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's backup history
CREATE OR REPLACE FUNCTION public.get_backup_history(
  user_id UUID,
  limit_count INTEGER DEFAULT 10
)
RETURNS TABLE(
  id UUID,
  session_type TEXT,
  status TEXT,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  total_files INTEGER,
  processed_files INTEGER,
  failed_files INTEGER,
  total_bytes BIGINT,
  processed_bytes BIGINT,
  duration_minutes INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    bs.id,
    bs.session_type,
    bs.status,
    bs.started_at,
    bs.completed_at,
    bs.total_files,
    bs.processed_files,
    bs.failed_files,
    bs.total_bytes,
    bs.processed_bytes,
    CASE 
      WHEN bs.completed_at IS NOT NULL THEN 
        EXTRACT(EPOCH FROM (bs.completed_at - bs.started_at))::INTEGER / 60
      ELSE NULL
    END as duration_minutes
  FROM public.backup_sessions bs
  WHERE bs.user_id = get_backup_history.user_id
  ORDER BY bs.started_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add helpful comments
COMMENT ON FUNCTION public.update_user_storage_usage(UUID) IS 'Recalculates and updates storage usage for a specific user';
COMMENT ON FUNCTION public.handle_new_user() IS 'Trigger function to initialize new user profiles';
COMMENT ON FUNCTION public.check_storage_quota() IS 'Validates storage quota before file uploads';
COMMENT ON FUNCTION public.get_subscription_limits(UUID) IS 'Returns subscription limits and features for a user';
COMMENT ON FUNCTION public.refresh_storage_usage(UUID) IS 'Client-accessible function to refresh storage calculations';