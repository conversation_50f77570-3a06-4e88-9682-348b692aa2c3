-- SafeKeep Pricing Calculation Functions
-- This script contains all the database functions needed for modular pricing calculations
-- Run this AFTER the migration scripts

-- ============================================================================
-- PRICING CALCULATION FUNCTIONS
-- ============================================================================

-- Function to get available service combinations for a user
CREATE OR REPLACE FUNCTION public.get_available_service_combinations()
RETURNS TABLE (
    plan_id VARCHAR(50),
    plan_name VARCHAR(100),
    price_cents INTEGER,
    services TEXT[],
    storage_gb INTEGER,
    is_popular BOOLEAN
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        sp.id,
        sp.name,
        sp.price_cents,
        ARRAY_AGG(ps.service_type_id ORDER BY ps.service_type_id) as services,
        sp.total_storage_gb,
        sp.is_popular
    FROM public.subscription_plans sp
    JOIN public.plan_services ps ON sp.id = ps.plan_id
    WHERE sp.is_active = TRUE AND ps.is_included = TRUE
    GROUP BY sp.id, sp.name, sp.price_cents, sp.total_storage_gb, sp.is_popular
    ORDER BY sp.price_cents;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's current service selections
CREATE OR REPLACE FUNCTION public.get_user_services(user_uuid UUID)
RETURNS TABLE (
    service_id VARCHAR(50),
    service_name VARCHAR(100),
    is_active BOOLEAN,
    activated_at TIMESTAMP WITH TIME ZONE
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        st.id,
        st.name,
        uss.is_active,
        uss.activated_at
    FROM public.user_service_selections uss
    JOIN public.service_types st ON uss.service_type_id = st.id
    WHERE uss.user_id = user_uuid
    ORDER BY st.name;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate optimal pricing for selected services
CREATE OR REPLACE FUNCTION public.calculate_optimal_price(service_ids TEXT[])
RETURNS TABLE (
    recommended_plan_id VARCHAR(50),
    recommended_plan_name VARCHAR(100),
    price_cents INTEGER,
    savings_cents INTEGER,
    individual_total_cents INTEGER
) AS $
DECLARE
    individual_total INTEGER := 0;
    best_plan_price INTEGER := 999999;
    best_plan_id VARCHAR(50);
    best_plan_name VARCHAR(100);
    savings INTEGER := 0;
BEGIN
    -- Calculate individual service total
    SELECT COALESCE(SUM(st.base_price_cents), 0) INTO individual_total
    FROM public.service_types st
    WHERE st.id = ANY(service_ids);
    
    -- Find the best combination plan that includes all requested services
    SELECT sp.id, sp.name, sp.price_cents
    INTO best_plan_id, best_plan_name, best_plan_price
    FROM public.subscription_plans sp
    WHERE sp.is_active = TRUE
    AND (
        SELECT COUNT(*)
        FROM public.plan_services ps
        WHERE ps.plan_id = sp.id 
        AND ps.service_type_id = ANY(service_ids)
        AND ps.is_included = TRUE
    ) = array_length(service_ids, 1)
    AND (
        SELECT COUNT(*)
        FROM public.plan_services ps
        WHERE ps.plan_id = sp.id 
        AND ps.is_included = TRUE
    ) = array_length(service_ids, 1)
    ORDER BY sp.price_cents ASC
    LIMIT 1;
    
    -- If no exact match found, find the cheapest plan that includes all services
    IF best_plan_id IS NULL THEN
        SELECT sp.id, sp.name, sp.price_cents
        INTO best_plan_id, best_plan_name, best_plan_price
        FROM public.subscription_plans sp
        WHERE sp.is_active = TRUE
        AND (
            SELECT COUNT(*)
            FROM public.plan_services ps
            WHERE ps.plan_id = sp.id 
            AND ps.service_type_id = ANY(service_ids)
            AND ps.is_included = TRUE
        ) = array_length(service_ids, 1)
        ORDER BY sp.price_cents ASC
        LIMIT 1;
    END IF;
    
    -- Calculate savings
    savings := GREATEST(individual_total - COALESCE(best_plan_price, individual_total), 0);
    
    RETURN QUERY SELECT 
        best_plan_id,
        best_plan_name,
        COALESCE(best_plan_price, individual_total),
        savings,
        individual_total;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user's service selections
CREATE OR REPLACE FUNCTION public.update_user_services(
    user_uuid UUID,
    new_service_ids TEXT[]
)
RETURNS BOOLEAN AS $
DECLARE
    service_id TEXT;
BEGIN
    -- Deactivate all current services
    UPDATE public.user_service_selections
    SET 
        is_active = FALSE,
        deactivated_at = NOW(),
        updated_at = NOW()
    WHERE user_id = user_uuid AND is_active = TRUE;
    
    -- Activate new services
    FOREACH service_id IN ARRAY new_service_ids
    LOOP
        INSERT INTO public.user_service_selections (user_id, service_type_id, is_active, activated_at)
        VALUES (user_uuid, service_id, TRUE, NOW())
        ON CONFLICT (user_id, service_type_id) 
        DO UPDATE SET 
            is_active = TRUE,
            activated_at = NOW(),
            deactivated_at = NULL,
            updated_at = NOW();
    END LOOP;
    
    RETURN TRUE;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's current subscription with pricing details
CREATE OR REPLACE FUNCTION public.get_user_subscription_details(user_uuid UUID)
RETURNS TABLE (
    subscription_id VARCHAR(50),
    plan_id VARCHAR(50),
    plan_name VARCHAR(100),
    current_price_cents INTEGER,
    status VARCHAR(20),
    services TEXT[],
    storage_quota_gb INTEGER,
    storage_used_gb DECIMAL(10,2),
    next_billing_date TIMESTAMP WITH TIME ZONE
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        us.id,
        us.plan_id,
        sp.name,
        us.total_price_cents,
        us.status,
        ARRAY_AGG(ps.service_type_id ORDER BY ps.service_type_id) as services,
        sp.total_storage_gb,
        ROUND((u.storage_used::DECIMAL / 1073741824), 2) as storage_used_gb, -- Convert bytes to GB
        us.current_period_end
    FROM public.user_subscriptions us
    JOIN public.subscription_plans sp ON us.plan_id = sp.id
    JOIN public.plan_services ps ON sp.id = ps.plan_id AND ps.is_included = TRUE
    JOIN public.users u ON us.user_id = u.id
    WHERE us.user_id = user_uuid 
    AND us.status = 'active'
    GROUP BY us.id, us.plan_id, sp.name, us.total_price_cents, us.status, 
             sp.total_storage_gb, u.storage_used, us.current_period_end
    ORDER BY us.created_at DESC
    LIMIT 1;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get usage statistics by service type
CREATE OR REPLACE FUNCTION public.get_user_usage_by_service(user_uuid UUID)
RETURNS TABLE (
    service_type VARCHAR(50),
    file_count INTEGER,
    size_bytes BIGINT,
    size_gb DECIMAL(10,2),
    last_backup TIMESTAMP WITH TIME ZONE
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        'contacts'::VARCHAR(50) as service_type,
        su.contacts_count,
        su.contacts_size_bytes,
        ROUND((su.contacts_size_bytes::DECIMAL / 1073741824), 2) as size_gb,
        (SELECT MAX(uploaded_at) FROM public.file_metadata WHERE user_id = user_uuid AND category = 'contact') as last_backup
    FROM public.storage_usage su
    WHERE su.user_id = user_uuid AND su.category = 'contact'
    
    UNION ALL
    
    SELECT 
        'messages'::VARCHAR(50) as service_type,
        su.messages_count,
        su.messages_size_bytes,
        ROUND((su.messages_size_bytes::DECIMAL / 1073741824), 2) as size_gb,
        (SELECT MAX(uploaded_at) FROM public.file_metadata WHERE user_id = user_uuid AND category = 'message') as last_backup
    FROM public.storage_usage su
    WHERE su.user_id = user_uuid AND su.category = 'message'
    
    UNION ALL
    
    SELECT 
        'photos'::VARCHAR(50) as service_type,
        su.photos_count,
        su.photos_size_bytes,
        ROUND((su.photos_size_bytes::DECIMAL / 1073741824), 2) as size_gb,
        (SELECT MAX(uploaded_at) FROM public.file_metadata WHERE user_id = user_uuid AND category = 'photo') as last_backup
    FROM public.storage_usage su
    WHERE su.user_id = user_uuid AND su.category = 'photo';
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate service access for a user
CREATE OR REPLACE FUNCTION public.user_has_service_access(
    user_uuid UUID,
    service_type_id VARCHAR(50)
)
RETURNS BOOLEAN AS $
DECLARE
    has_access BOOLEAN := FALSE;
BEGIN
    SELECT EXISTS(
        SELECT 1 
        FROM public.user_service_selections uss
        WHERE uss.user_id = user_uuid 
        AND uss.service_type_id = service_type_id 
        AND uss.is_active = TRUE
    ) INTO has_access;
    
    RETURN has_access;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get upgrade/downgrade recommendations
CREATE OR REPLACE FUNCTION public.get_plan_recommendations(user_uuid UUID)
RETURNS TABLE (
    recommendation_type VARCHAR(20), -- 'upgrade', 'downgrade', 'alternative'
    plan_id VARCHAR(50),
    plan_name VARCHAR(100),
    price_cents INTEGER,
    price_difference_cents INTEGER,
    reason TEXT
) AS $
DECLARE
    current_plan_id VARCHAR(50);
    current_price INTEGER;
    user_services TEXT[];
BEGIN
    -- Get user's current plan and services
    SELECT us.plan_id, us.total_price_cents
    INTO current_plan_id, current_price
    FROM public.user_subscriptions us
    WHERE us.user_id = user_uuid AND us.status = 'active'
    ORDER BY us.created_at DESC
    LIMIT 1;
    
    -- Get user's active services
    SELECT ARRAY_AGG(uss.service_type_id)
    INTO user_services
    FROM public.user_service_selections uss
    WHERE uss.user_id = user_uuid AND uss.is_active = TRUE;
    
    -- Return upgrade options (higher price, more features)
    RETURN QUERY
    SELECT 
        'upgrade'::VARCHAR(20),
        sp.id,
        sp.name,
        sp.price_cents,
        sp.price_cents - current_price,
        'More features and storage' as reason
    FROM public.subscription_plans sp
    WHERE sp.is_active = TRUE 
    AND sp.price_cents > current_price
    AND sp.id != current_plan_id
    ORDER BY sp.price_cents ASC
    LIMIT 3;
    
    -- Return downgrade options (lower price, fewer features)
    RETURN QUERY
    SELECT 
        'downgrade'::VARCHAR(20),
        sp.id,
        sp.name,
        sp.price_cents,
        sp.price_cents - current_price,
        'Save money with essential features' as reason
    FROM public.subscription_plans sp
    WHERE sp.is_active = TRUE 
    AND sp.price_cents < current_price
    AND sp.id != current_plan_id
    ORDER BY sp.price_cents DESC
    LIMIT 2;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

GRANT EXECUTE ON FUNCTION public.get_available_service_combinations() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_services(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.calculate_optimal_price(TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_services(UUID, TEXT[]) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_subscription_details(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_usage_by_service(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.user_has_service_access(UUID, VARCHAR(50)) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_plan_recommendations(UUID) TO authenticated;

SELECT 'Pricing Calculation Functions Created Successfully!' as status;