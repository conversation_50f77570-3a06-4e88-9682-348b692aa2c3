// File Search Utility for SafeKeep
// Provides advanced search capabilities for file metadata

import { supabase } from '../config/supabase';
import { getCurrentUserId } from './supabaseHelpers';
import { FileMetadata, FileSearchParams } from './fileMetadataManager';

export interface AdvancedSearchParams extends FileSearchParams {
  minSize?: number;
  maxSize?: number;
  uploadedBefore?: Date;
  uploadedAfter?: Date;
  modifiedBefore?: Date;
  modifiedAfter?: Date;
  isFavorite?: boolean;
  isBackedUp?: boolean;
  syncStatus?: 'synced' | 'pending' | 'failed';
  deviceId?: string;
  excludeTags?: string[];
  includeDescription?: boolean;
}

/**
 * Perform advanced search on file metadata
 */
export const advancedSearch = async (
  params: AdvancedSearchParams
): Promise<{ 
  data: FileMetadata[]; 
  totalCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], totalCount: 0, error: 'User not authenticated' };
    }

    // First get total count for pagination info
    const countQuery = supabase
      .from('file_metadata')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);
      
    // Apply all filters to count query
    applySearchFilters(countQuery, params);
    
    const { count: totalCount, error: countError } = await countQuery;
    
    if (countError) {
      return { data: [], totalCount: 0, error: countError.message };
    }
    
    // Now build the main query
    let query = supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId);

    // Apply all filters
    applySearchFilters(query, params);

    // Apply sorting
    const sortField = params.sortBy || 'uploaded_at';
    const sortDirection = params.sortDirection === 'asc' ? true : false;
    query = query.order(sortField, { ascending: sortDirection });

    // Apply pagination
    if (params.limit) {
      query = query.limit(params.limit);
    }

    if (params.offset) {
      query = query.range(params.offset, params.offset + (params.limit || 20) - 1);
    }

    const { data, error } = await query;

    if (error) {
      return { data: [], totalCount: 0, error: error.message };
    }

    return { 
      data: data as FileMetadata[], 
      totalCount: totalCount || 0,
      error: null 
    };
  } catch (error) {
    console.error('Error in advanced search:', error);
    return { data: [], totalCount: 0, error: (error as Error).message };
  }
};

/**
 * Helper function to apply search filters to a query
 */
const applySearchFilters = (query: any, params: AdvancedSearchParams): void => {
  // Basic filters
  if (params.category) {
    query = query.eq('category', params.category);
  }

  if (params.startDate) {
    query = query.gte('uploaded_at', params.startDate.toISOString());
  }

  if (params.endDate) {
    query = query.lte('uploaded_at', params.endDate.toISOString());
  }

  // Advanced filters
  if (params.minSize !== undefined) {
    query = query.gte('size', params.minSize);
  }

  if (params.maxSize !== undefined) {
    query = query.lte('size', params.maxSize);
  }

  if (params.uploadedBefore) {
    query = query.lt('uploaded_at', params.uploadedBefore.toISOString());
  }

  if (params.uploadedAfter) {
    query = query.gt('uploaded_at', params.uploadedAfter.toISOString());
  }

  if (params.modifiedBefore) {
    query = query.lt('last_modified', params.modifiedBefore.toISOString());
  }

  if (params.modifiedAfter) {
    query = query.gt('last_modified', params.modifiedAfter.toISOString());
  }

  if (params.isFavorite !== undefined) {
    query = query.eq('favorite', params.isFavorite);
  }

  if (params.isBackedUp !== undefined) {
    query = query.eq('is_backed_up', params.isBackedUp);
  }

  if (params.syncStatus) {
    query = query.eq('sync_status', params.syncStatus);
  }

  if (params.deviceId) {
    query = query.eq('device_id', params.deviceId);
  }

  // Tag filters
  if (params.tags && params.tags.length > 0) {
    // Search for files that contain any of the specified tags
    query = query.contains('tags', params.tags);
  }

  if (params.excludeTags && params.excludeTags.length > 0) {
    // Exclude files that contain any of the specified tags
    for (const tag of params.excludeTags) {
      query = query.not('tags', 'cs', `{${tag}}`);
    }
  }

  // Text search
  if (params.searchTerm) {
    if (params.includeDescription) {
      // Search in original name, storage path, and description
      query = query.or(`original_name.ilike.%${params.searchTerm}%,storage_path.ilike.%${params.searchTerm}%,description.ilike.%${params.searchTerm}%`);
    } else {
      // Search in original name and storage path only
      query = query.or(`original_name.ilike.%${params.searchTerm}%,storage_path.ilike.%${params.searchTerm}%`);
    }
  }
};

/**
 * Search files by content type
 */
export const searchByMimeType = async (
  mimeType: string,
  limit: number = 20,
  offset: number = 0
): Promise<{ 
  data: FileMetadata[]; 
  totalCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], totalCount: 0, error: 'User not authenticated' };
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('file_metadata')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .ilike('mime_type', `${mimeType}%`);
      
    if (countError) {
      return { data: [], totalCount: 0, error: countError.message };
    }
    
    // Get files with pagination
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .ilike('mime_type', `${mimeType}%`)
      .order('uploaded_at', { ascending: false })
      .range(offset, offset + limit - 1);
      
    if (error) {
      return { data: [], totalCount: 0, error: error.message };
    }
    
    return { 
      data: data as FileMetadata[], 
      totalCount: count || 0, 
      error: null 
    };
  } catch (error) {
    console.error('Error searching by mime type:', error);
    return { data: [], totalCount: 0, error: (error as Error).message };
  }
};

/**
 * Search files by date range
 */
export const searchByDateRange = async (
  startDate: Date,
  endDate: Date,
  dateField: 'uploaded_at' | 'last_modified' = 'uploaded_at',
  limit: number = 20,
  offset: number = 0
): Promise<{ 
  data: FileMetadata[]; 
  totalCount: number;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { data: [], totalCount: 0, error: 'User not authenticated' };
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('file_metadata')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .gte(dateField, startDate.toISOString())
      .lte(dateField, endDate.toISOString());
      
    if (countError) {
      return { data: [], totalCount: 0, error: countError.message };
    }
    
    // Get files with pagination
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId)
      .gte(dateField, startDate.toISOString())
      .lte(dateField, endDate.toISOString())
      .order(dateField, { ascending: false })
      .range(offset, offset + limit - 1);
      
    if (error) {
      return { data: [], totalCount: 0, error: error.message };
    }
    
    return { 
      data: data as FileMetadata[], 
      totalCount: count || 0, 
      error: null 
    };
  } catch (error) {
    console.error('Error searching by date range:', error);
    return { data: [], totalCount: 0, error: (error as Error).message };
  }
};

/**
 * Get file metadata statistics
 */
export const getFileMetadataStats = async (): Promise<{ 
  totalFiles: number;
  totalSize: number;
  filesByCategory: Record<string, number>;
  sizeByCategory: Record<string, number>;
  filesByMimeType: Record<string, number>;
  filesByMonth: Record<string, number>;
  error: string | null 
}> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return { 
        totalFiles: 0, 
        totalSize: 0, 
        filesByCategory: {}, 
        sizeByCategory: {}, 
        filesByMimeType: {}, 
        filesByMonth: {}, 
        error: 'User not authenticated' 
      };
    }

    // Get all file metadata
    const { data, error } = await supabase
      .from('file_metadata')
      .select('*')
      .eq('user_id', userId);
      
    if (error) {
      return { 
        totalFiles: 0, 
        totalSize: 0, 
        filesByCategory: {}, 
        sizeByCategory: {}, 
        filesByMimeType: {}, 
        filesByMonth: {}, 
        error: error.message 
      };
    }
    
    // Calculate statistics
    const totalFiles = data.length;
    const totalSize = data.reduce((sum, file) => sum + file.size, 0);
    
    // Group by category
    const filesByCategory: Record<string, number> = {};
    const sizeByCategory: Record<string, number> = {};
    
    // Group by mime type
    const filesByMimeType: Record<string, number> = {};
    
    // Group by month
    const filesByMonth: Record<string, number> = {};
    
    data.forEach(file => {
      // Category stats
      if (!filesByCategory[file.category]) {
        filesByCategory[file.category] = 0;
        sizeByCategory[file.category] = 0;
      }
      filesByCategory[file.category]++;
      sizeByCategory[file.category] += file.size;
      
      // Mime type stats
      const mimeType = file.mime_type.split('/')[0]; // Get main type (image, video, etc.)
      if (!filesByMimeType[mimeType]) {
        filesByMimeType[mimeType] = 0;
      }
      filesByMimeType[mimeType]++;
      
      // Month stats
      const date = new Date(file.uploaded_at);
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      if (!filesByMonth[monthKey]) {
        filesByMonth[monthKey] = 0;
      }
      filesByMonth[monthKey]++;
    });
    
    return {
      totalFiles,
      totalSize,
      filesByCategory,
      sizeByCategory,
      filesByMimeType,
      filesByMonth,
      error: null
    };
  } catch (error) {
    console.error('Error getting file metadata stats:', error);
    return { 
      totalFiles: 0, 
      totalSize: 0, 
      filesByCategory: {}, 
      sizeByCategory: {}, 
      filesByMimeType: {}, 
      filesByMonth: {}, 
      error: (error as Error).message 
    };
  }
};