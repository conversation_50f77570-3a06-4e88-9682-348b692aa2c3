import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from '../../utils/types';

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isLoading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.error = null;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    logout: (state) => {
      state.user = null;
      state.error = null;
      state.isLoading = false;
    },
    updateStorageUsage: (state, action: PayloadAction<{ used: number; limit: number }>) => {
      if (state.user) {
        state.user.storageUsed = action.payload.used;
        state.user.storageLimit = action.payload.limit;
      }
    },
  },
});

export const {
  setLoading,
  setUser,
  setError,
  logout,
  updateStorageUsage,
} = authSlice.actions;

export default authSlice.reducer;
