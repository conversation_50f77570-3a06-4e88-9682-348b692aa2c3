/**
 * Quick Test for Advanced Backup Scheduling System
 * Simple verification that the scheduling system works
 */

async function quickTestSchedule() {
    console.log('🚀 Quick Test: Advanced Backup Scheduling System');
    
    try {
        // Check if classes are available
        if (!window.ScheduleManager) {
            throw new Error('ScheduleManager class not found');
        }
        
        if (!window.ScheduleConsole) {
            throw new Error('ScheduleConsole class not found');
        }
        
        console.log('✅ Classes loaded successfully');
        
        // Initialize schedule manager
        const scheduleManager = new window.ScheduleManager(supabase, adminSupabase);
        await scheduleManager.initialize();
        console.log('✅ ScheduleManager initialized');
        
        // Create a test schedule
        const testSchedule = await scheduleManager.createSchedule({
            name: 'Test Daily Backup',
            frequency: 'daily',
            time: '14:00',
            days: [1, 2, 3, 4, 5], // Weekdays
            dataTypes: ['contacts', 'messages'],
            conditions: {
                wifiOnly: true,
                minBatteryLevel: 30,
                maxStorageUsage: 80,
                requireCharging: false,
                requireIdle: false
            }
        });
        
        console.log('✅ Test schedule created:', testSchedule.name);
        console.log('   Schedule ID:', testSchedule.id);
        console.log('   Next run:', testSchedule.nextRun?.toLocaleString());
        
        // Test condition evaluation
        scheduleManager.updateSimulatedConditions({
            networkType: 'wifi',
            batteryLevel: 85,
            storageUsage: 45,
            isCharging: false,
            deviceIdle: true
        });
        
        const conditionTest = scheduleManager.testSchedule(testSchedule);
        console.log('✅ Condition test result:', conditionTest.canRunNow ? 'CAN RUN' : 'BLOCKED');
        
        // Get statistics
        const stats = scheduleManager.getScheduleStats();
        console.log('✅ Schedule statistics:');
        console.log('   Total schedules:', stats.totalSchedules);
        console.log('   Enabled schedules:', stats.enabledSchedules);
        console.log('   Success rate:', stats.successRate.toFixed(1) + '%');
        
        // Test schedule console initialization
        const scheduleConsole = new window.ScheduleConsole('test-container');
        scheduleConsole.setScheduleManager(scheduleManager);
        console.log('✅ ScheduleConsole initialized');
        
        // Simulate backup execution
        const backupResult = await scheduleManager.simulateBackupExecution(testSchedule);
        console.log('✅ Backup simulation:', backupResult.success ? 'SUCCESS' : 'FAILED');
        if (backupResult.success) {
            console.log('   Files backed up:', backupResult.filesBackedUp);
            console.log('   Data size:', backupResult.dataSize + 'MB');
        }
        
        console.log('\n🎉 Quick test completed successfully!');
        console.log('📋 Summary:');
        console.log('   ✅ ScheduleManager working');
        console.log('   ✅ ScheduleConsole working');
        console.log('   ✅ Schedule creation working');
        console.log('   ✅ Condition evaluation working');
        console.log('   ✅ Backup simulation working');
        
        return {
            success: true,
            scheduleManager,
            scheduleConsole,
            testSchedule
        };
        
    } catch (error) {
        console.error('❌ Quick test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Export for use
if (typeof window !== 'undefined') {
    window.quickTestSchedule = quickTestSchedule;
}