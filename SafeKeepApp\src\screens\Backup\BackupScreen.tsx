import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { SegmentedButtons, Text } from 'react-native-paper';
import { Header } from 'react-native-elements';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// Removed unused import
import { COLORS, FONTS, SPACING } from '../../utils/constants';
import PhotoBackupScreen from './PhotoBackupScreen';
import ContactBackupScreen from './ContactBackupScreen';
import MessageBackupScreen from './MessageBackupScreen';
import { BackupDashboard, BackupProgressScreen } from '../../components/backup';
import { useIsBackupInProgress } from '../../store/hooks/backupHooks';
import { RootStackParamList } from '../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<RootStackParamList>;

const BackupScreen = () => {
  const [selectedTab, setSelectedTab] = useState('dashboard');
  const [showProgressScreen, setShowProgressScreen] = useState(false);
  const navigation = useNavigation<NavigationProp>();
  const isBackupInProgress = useIsBackupInProgress();

  const handleNavigateToProgress = () => {
    setShowProgressScreen(true);
  };

  const handleNavigateBack = () => {
    setShowProgressScreen(false);
  };
  
  const handleNavigateToHistory = () => {
    navigation.navigate('BackupHistory');
  };
  
  const handleNavigateToSettings = () => {
    navigation.navigate('BackupSettings', {});
  };

  const renderTabContent = () => {
    if (showProgressScreen) {
      return <BackupProgressScreen onNavigateBack={handleNavigateBack} />;
    }

    switch (selectedTab) {
      case 'dashboard':
        return <BackupDashboard 
          onNavigateToProgress={handleNavigateToProgress}
          onNavigateToSettings={handleNavigateToSettings}
          onNavigateToHistory={handleNavigateToHistory}
        />;
      case 'photos':
        return <PhotoBackupScreen />;
      case 'contacts':
        return <ContactBackupScreen />;
      case 'messages':
        return <MessageBackupScreen />;
      default:
        return <BackupDashboard 
          onNavigateToProgress={handleNavigateToProgress}
          onNavigateToSettings={handleNavigateToSettings}
          onNavigateToHistory={handleNavigateToHistory}
        />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Backup Center',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <View style={styles.tabContainer}>
        <SegmentedButtons
          value={selectedTab}
          onValueChange={setSelectedTab}
          buttons={[
            {
              value: 'dashboard',
              label: 'Dashboard',
              icon: 'view-dashboard',
            },
            {
              value: 'photos',
              label: 'Photos',
              icon: 'camera',
            },
            {
              value: 'contacts',
              label: 'Contacts',
              icon: 'contacts',
            },
            {
              value: 'messages',
              label: 'Messages',
              icon: 'message-text',
            },
          ]}
          style={styles.segmentedButtons}
        />
      </View>

      <View style={styles.contentContainer}>
        {renderTabContent()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  tabContainer: {
    padding: SPACING.md,
    backgroundColor: COLORS.surface,
    elevation: 2,
  },
  segmentedButtons: {
    backgroundColor: COLORS.background,
  },
  contentContainer: {
    flex: 1,
  },
  comingSoon: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  comingSoonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  comingSoonSubtext: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});

export default BackupScreen;
