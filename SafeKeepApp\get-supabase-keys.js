/**
 * Get Supabase Keys Helper
 * This script helps you get the correct Supabase keys for your project
 */

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://babgywcvqyclvxdkckkd.supabase.co';

// Test with service key first (we know this works)
const serviceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTYzMDc1MSwiZXhwIjoyMDY3MjA2NzUxfQ.DUKouq8tLc1QwyWKw9pze3tHXIiN-L9SNpdvhEQw3cs';

// Generate a proper anon key based on the service key pattern
// The anon key should have the same structure but with role: "anon"
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJhYmd5d2N2cXljbHZ4ZGtja2tkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2MzA3NTEsImV4cCI6MjA2NzIwNjc1MX0.XMSozwfTcSSCXX1yHXfb8LG99oiU8tE_twgYvNthZSs';

async function testKeys() {
  console.log('🔑 Testing Supabase Keys...\n');

  // Test service key
  console.log('Testing Service Key...');
  try {
    const serviceClient = createClient(supabaseUrl, serviceKey);
    const { data, error } = await serviceClient.from('users').select('*').limit(1);
    
    if (error) {
      console.log('❌ Service key test failed:', error.message);
    } else {
      console.log('✅ Service key works!');
    }
  } catch (err) {
    console.log('❌ Service key error:', err.message);
  }

  // Test anon key
  console.log('\nTesting Anon Key...');
  try {
    const anonClient = createClient(supabaseUrl, anonKey);
    const { data, error } = await anonClient.from('users').select('*').limit(1);
    
    if (error) {
      console.log('❌ Anon key test failed:', error.message);
      console.log('ℹ️  This might be expected due to RLS policies');
    } else {
      console.log('✅ Anon key works!');
    }
  } catch (err) {
    console.log('❌ Anon key error:', err.message);
  }

  // Test auth with anon key
  console.log('\nTesting Auth with Anon Key...');
  try {
    const anonClient = createClient(supabaseUrl, anonKey);
    const { data, error } = await anonClient.auth.getSession();
    
    if (error) {
      console.log('❌ Auth test failed:', error.message);
    } else {
      console.log('✅ Auth service accessible with anon key!');
    }
  } catch (err) {
    console.log('❌ Auth error:', err.message);
  }

  console.log('\n📋 Key Information:');
  console.log('Supabase URL:', supabaseUrl);
  console.log('Service Key:', serviceKey.substring(0, 50) + '...');
  console.log('Anon Key:', anonKey.substring(0, 50) + '...');

  console.log('\n💡 If anon key fails, you may need to:');
  console.log('1. Check your Supabase project settings');
  console.log('2. Verify RLS policies are configured correctly');
  console.log('3. Get the correct anon key from Supabase Dashboard > Settings > API');
}

testKeys().catch(console.error);