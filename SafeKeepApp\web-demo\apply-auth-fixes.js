/**
 * Apply Authentication Fixes
 * This script helps apply the enhanced user authentication system
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config({ path: '../.env' });

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const serviceKey = process.env.REACT_APP_SUPABASE_SERVICE_KEY;

console.log('Environment check:');
console.log('Supabase URL:', supabaseUrl ? 'Found' : 'Missing');
console.log('Service Key:', serviceKey ? 'Found' : 'Missing');

const supabase = createClient(supabaseUrl, serviceKey);

async function applyAuthFixes() {
  console.log('🔧 Applying Enhanced Authentication Fixes...\n');

  try {
    // Test current user creation
    console.log('🧪 Testing current user creation system...');
    
    const testEmail = `auth-test-${Date.now()}@safekeep.test`;
    const testPassword = 'TestPassword123!';
    
    const { data: signupData, error: signupError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword
    });

    if (signupError) {
      console.log('❌ Current auth system has issues:', signupError.message);
      console.log('📋 Manual fix required in Supabase Dashboard:\n');
      
      // Read and display the SQL fix
      const sqlFix = fs.readFileSync('./enhanced-user-trigger.sql', 'utf8');
      console.log('Copy and paste this SQL into Supabase SQL Editor:');
      console.log('=' .repeat(60));
      console.log(sqlFix);
      console.log('=' .repeat(60));
      
    } else {
      console.log('✅ Auth signup working!');
      
      // Check if user record was created
      setTimeout(async () => {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', signupData.user.id);

        if (userError || !userData || userData.length === 0) {
          console.log('⚠️  User record not created by trigger');
          console.log('📋 Trigger needs to be fixed in Supabase Dashboard');
        } else {
          console.log('✅ User record created successfully by trigger');
          console.log('🎉 Authentication system is working properly!');
        }

        // Clean up test user
        try {
          await supabase.auth.admin.deleteUser(signupData.user.id);
          console.log('🧹 Test user cleaned up');
        } catch (cleanupError) {
          console.log('⚠️  Could not clean up test user:', cleanupError.message);
        }
      }, 3000);
    }

    console.log('\n📋 Next Steps:');
    console.log('1. If auth is failing, execute the SQL fix in Supabase Dashboard');
    console.log('2. Refresh your web demo at http://localhost:3000');
    console.log('3. Try the "Create Demo User" button');
    console.log('4. Test signup with a real email address');
    console.log('5. Check the activity log for detailed feedback');

  } catch (error) {
    console.error('💥 Error applying auth fixes:', error.message);
  }
}

applyAuthFixes().catch(console.error);