import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Badge, ProgressBar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { COLORS, SPACING } from '../../utils/constants';
import { 
  useIsBackupInProgress, 
  useOverallBackupProgress, 
  useBackupErrors 
} from '../../store/hooks/backupHooks';

type NavigationProp = StackNavigationProp<RootStackParamList>;

interface BackupStatusIndicatorProps {
  compact?: boolean;
  showProgress?: boolean;
  onPress?: () => void;
}

const BackupStatusIndicator: React.FC<BackupStatusIndicatorProps> = ({ 
  compact = false,
  showProgress = true,
  onPress
}) => {
  const navigation = useNavigation<NavigationProp>();
  const isBackupInProgress = useIsBackupInProgress();
  const overallProgress = useOverallBackupProgress();
  const backupErrors = useBackupErrors();
  const hasErrors = backupErrors.length > 0;

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      navigation.navigate('BackupProgress');
    }
  };

  const getStatusIcon = () => {
    if (isBackupInProgress) {
      return 'sync';
    } else if (hasErrors) {
      return 'alert-circle';
    } else {
      return 'check-circle';
    }
  };

  const getStatusColor = () => {
    if (isBackupInProgress) {
      return COLORS.warning;
    } else if (hasErrors) {
      return COLORS.error;
    } else {
      return COLORS.success;
    }
  };

  const getStatusText = () => {
    if (isBackupInProgress) {
      return `Backup in progress (${overallProgress.percentage}%)`;
    } else if (hasErrors) {
      return `${backupErrors.length} backup error${backupErrors.length !== 1 ? 's' : ''}`;
    } else {
      return 'Backup complete';
    }
  };

  if (compact) {
    return (
      <TouchableOpacity style={styles.compactContainer} onPress={handlePress}>
        <Icon name={getStatusIcon()} size={16} color={getStatusColor()} />
        {isBackupInProgress && (
          <Badge
            size={12}
            style={[styles.badge, { backgroundColor: getStatusColor() }]}
          >
            {overallProgress.percentage}%
          </Badge>
        )}
        {hasErrors && !isBackupInProgress && (
          <Badge
            size={12}
            style={[styles.badge, { backgroundColor: getStatusColor() }]}
          >
            {backupErrors.length}
          </Badge>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <View style={styles.header}>
        <Icon name={getStatusIcon()} size={20} color={getStatusColor()} />
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
      </View>
      
      {showProgress && isBackupInProgress && (
        <View style={styles.progressContainer}>
          <ProgressBar
            progress={overallProgress.percentage / 100}
            color={getStatusColor()}
            style={styles.progressBar}
          />
          <Text style={styles.progressText}>
            {overallProgress.completedItems} of {overallProgress.totalItems} items
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: SPACING.sm,
    borderRadius: 8,
    backgroundColor: COLORS.surface,
  },
  compactContainer: {
    position: 'relative',
    padding: SPACING.xs,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    marginLeft: SPACING.xs,
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    marginTop: SPACING.xs,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: 2,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
  },
});

export default BackupStatusIndicator;