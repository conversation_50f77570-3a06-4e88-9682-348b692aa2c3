import { supabase } from '../config/supabase';
import {
  BackupSession,
  BackupItem,
  BackupProgress,
  BackupSessionSummary,
  BackupConfiguration,
  InitializeBackupProgressParams,
  UpdateBackupProgressParams,
  BACKUP_TABLES,
  BACKUP_FUNCTIONS,
} from '../types/backup';

/**
 * Service for managing backup tracking data in Supabase
 * Provides methods for creating, updating, and querying backup sessions, items, and progress
 */
export class BackupDatabaseService {
  /**
   * Creates a new backup session
   */
  static async createBackupSession(
    totalItems: number,
    configuration: BackupConfiguration
  ): Promise<BackupSession> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.SESSIONS)
      .insert({
        total_items: totalItems,
        configuration,
        status: 'pending',
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create backup session: ${error.message}`);
    }

    return data;
  }

  /**
   * Updates a backup session status and metadata
   */
  static async updateBackupSession(
    sessionId: string,
    updates: Partial<Pick<BackupSession, 'status' | 'end_time' | 'completed_items' | 'error_message'>>
  ): Promise<BackupSession> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.SESSIONS)
      .update(updates)
      .eq('id', sessionId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update backup session: ${error.message}`);
    }

    return data;
  }

  /**
   * Gets a backup session by ID
   */
  static async getBackupSession(sessionId: string): Promise<BackupSession | null> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.SESSIONS)
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to get backup session: ${error.message}`);
    }

    return data;
  }

  /**
   * Gets all backup sessions for the current user
   */
  static async getUserBackupSessions(
    limit: number = 50,
    offset: number = 0
  ): Promise<BackupSession[]> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.SESSIONS)
      .select('*')
      .order('start_time', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Failed to get user backup sessions: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Creates a backup item within a session
   */
  static async createBackupItem(
    sessionId: string,
    itemType: 'contact' | 'message' | 'photo',
    originalId: string,
    encryptedData: Uint8Array,
    metadata: Record<string, any> = {},
    filePath?: string,
    checksum?: string,
    sizeBytes?: number
  ): Promise<BackupItem> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.ITEMS)
      .insert({
        session_id: sessionId,
        item_type: itemType,
        original_id: originalId,
        encrypted_data: encryptedData,
        metadata,
        file_path: filePath,
        checksum,
        size_bytes: sizeBytes,
        status: 'pending',
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create backup item: ${error.message}`);
    }

    return data;
  }

  /**
   * Updates a backup item status
   */
  static async updateBackupItem(
    itemId: string,
    updates: Partial<Pick<BackupItem, 'status' | 'error_message' | 'file_path' | 'checksum'>>
  ): Promise<BackupItem> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.ITEMS)
      .update(updates)
      .eq('id', itemId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update backup item: ${error.message}`);
    }

    return data;
  }

  /**
   * Gets backup items for a session
   */
  static async getSessionBackupItems(
    sessionId: string,
    itemType?: 'contact' | 'message' | 'photo',
    status?: 'pending' | 'completed' | 'failed'
  ): Promise<BackupItem[]> {
    let query = supabase
      .from(BACKUP_TABLES.ITEMS)
      .select('*')
      .eq('session_id', sessionId);

    if (itemType) {
      query = query.eq('item_type', itemType);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query.order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to get session backup items: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Initializes backup progress for a session
   */
  static async initializeBackupProgress(params: InitializeBackupProgressParams): Promise<void> {
    const { error } = await supabase.rpc(BACKUP_FUNCTIONS.INITIALIZE_PROGRESS, {
      p_session_id: params.session_id,
      p_contacts_total: params.contacts_total || 0,
      p_messages_total: params.messages_total || 0,
      p_photos_total: params.photos_total || 0,
    });

    if (error) {
      throw new Error(`Failed to initialize backup progress: ${error.message}`);
    }
  }

  /**
   * Updates backup progress for a specific item type
   */
  static async updateBackupProgress(params: UpdateBackupProgressParams): Promise<void> {
    const { error } = await supabase.rpc(BACKUP_FUNCTIONS.UPDATE_PROGRESS, {
      p_session_id: params.session_id,
      p_item_type: params.item_type,
      p_completed_increment: params.completed_increment || 1,
      p_failed_increment: params.failed_increment || 0,
    });

    if (error) {
      throw new Error(`Failed to update backup progress: ${error.message}`);
    }
  }

  /**
   * Gets backup progress for a session
   */
  static async getBackupProgress(sessionId: string): Promise<BackupProgress[]> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.PROGRESS)
      .select('*')
      .eq('session_id', sessionId);

    if (error) {
      throw new Error(`Failed to get backup progress: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Gets comprehensive backup session summary
   */
  static async getBackupSessionSummary(sessionId: string): Promise<BackupSessionSummary | null> {
    const { data, error } = await supabase.rpc(BACKUP_FUNCTIONS.GET_SESSION_SUMMARY, {
      p_session_id: sessionId,
    });

    if (error) {
      throw new Error(`Failed to get backup session summary: ${error.message}`);
    }

    return data?.[0] || null;
  }

  /**
   * Cancels a backup session and marks it as cancelled
   */
  static async cancelBackupSession(sessionId: string): Promise<BackupSession> {
    return this.updateBackupSession(sessionId, {
      status: 'cancelled',
      end_time: new Date().toISOString(),
    });
  }

  /**
   * Marks a backup session as completed
   */
  static async completeBackupSession(sessionId: string): Promise<BackupSession> {
    return this.updateBackupSession(sessionId, {
      status: 'completed',
      end_time: new Date().toISOString(),
    });
  }

  /**
   * Marks a backup session as failed with error message
   */
  static async failBackupSession(sessionId: string, errorMessage: string): Promise<BackupSession> {
    return this.updateBackupSession(sessionId, {
      status: 'failed',
      end_time: new Date().toISOString(),
      error_message: errorMessage,
    });
  }

  /**
   * Gets failed backup items for retry logic
   */
  static async getFailedBackupItems(sessionId: string): Promise<BackupItem[]> {
    return this.getSessionBackupItems(sessionId, undefined, 'failed');
  }

  /**
   * Cleans up old backup sessions (calls database function)
   */
  static async cleanupOldBackupSessions(): Promise<number> {
    const { data, error } = await supabase.rpc(BACKUP_FUNCTIONS.CLEANUP_OLD_SESSIONS);

    if (error) {
      throw new Error(`Failed to cleanup old backup sessions: ${error.message}`);
    }

    return data || 0;
  }

  /**
   * Gets backup statistics for the current user
   */
  static async getBackupStatistics(): Promise<{
    totalSessions: number;
    successfulSessions: number;
    failedSessions: number;
    lastBackupTime?: string;
  }> {
    const { data, error } = await supabase
      .from(BACKUP_TABLES.SESSIONS)
      .select('status, start_time')
      .order('start_time', { ascending: false });

    if (error) {
      throw new Error(`Failed to get backup statistics: ${error.message}`);
    }

    const sessions = data || [];
    const totalSessions = sessions.length;
    const successfulSessions = sessions.filter(s => s.status === 'completed').length;
    const failedSessions = sessions.filter(s => s.status === 'failed').length;
    const lastBackupTime = sessions.length > 0 ? sessions[0].start_time : undefined;

    return {
      totalSessions,
      successfulSessions,
      failedSessions,
      lastBackupTime,
    };
  }

  /**
   * Subscribes to real-time backup progress updates
   */
  static subscribeToBackupProgress(
    sessionId: string,
    callback: (progress: BackupProgress[]) => void
  ) {
    return supabase
      .channel(`backup_progress_${sessionId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: BACKUP_TABLES.PROGRESS,
          filter: `session_id=eq.${sessionId}`,
        },
        async () => {
          // Fetch updated progress when changes occur
          try {
            const progress = await this.getBackupProgress(sessionId);
            callback(progress);
          } catch (error) {
            console.error('Error fetching backup progress:', error);
          }
        }
      )
      .subscribe();
  }

  /**
   * Subscribes to backup session status changes
   */
  static subscribeToBackupSession(
    sessionId: string,
    callback: (session: BackupSession) => void
  ) {
    return supabase
      .channel(`backup_session_${sessionId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: BACKUP_TABLES.SESSIONS,
          filter: `id=eq.${sessionId}`,
        },
        async () => {
          // Fetch updated session when changes occur
          try {
            const session = await this.getBackupSession(sessionId);
            if (session) {
              callback(session);
            }
          } catch (error) {
            console.error('Error fetching backup session:', error);
          }
        }
      )
      .subscribe();
  }
}