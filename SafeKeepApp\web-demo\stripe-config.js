/**
 * Stripe Configuration for SafeKeep Web Demo
 * Centralized configuration for Stripe integration
 */

class StripeConfig {
    constructor() {
        // Stripe API Configuration
        this.config = {
            // Test keys for demo - replace with your actual keys
            publishableKey: 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            secretKey: 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            
            // Webhook configuration
            webhookSecret: 'whsec_demo_webhook_secret_for_safekeep_demo',
            webhookEndpoint: '/api/stripe/webhook',
            
            // API endpoints
            endpoints: {
                createPaymentIntent: '/api/stripe/create-payment-intent',
                createCustomer: '/api/stripe/create-customer',
                createSubscription: '/api/stripe/create-subscription',
                updateSubscription: '/api/stripe/update-subscription',
                cancelSubscription: '/api/stripe/cancel-subscription',
                addPaymentMethod: '/api/stripe/add-payment-method',
                removePaymentMethod: '/api/stripe/remove-payment-method',
                getCustomer: '/api/stripe/customer',
                getSubscription: '/api/stripe/subscription',
                getPaymentMethods: '/api/stripe/payment-methods'
            },
            
            // Currency and locale settings
            currency: 'usd',
            locale: 'en',
            
            // Demo mode settings
            demoMode: true,
            simulateNetworkDelay: true,
            networkDelayMs: 1000
        };
        
        // Subscription tier configuration - Modular Pricing Structure
        this.subscriptionTiers = {
            // Individual Services
            contacts_only: {
                id: 'contacts_only',
                name: 'Contacts Only',
                description: 'Secure backup for your contacts',
                price: 99, // $0.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_contacts_safekeep',
                stripePriceId: 'price_demo_contacts_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: false,
                    photosBackup: false,
                    storageLimit: 1, // 1GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10
                },
                limits: {
                    maxStorageGB: 1,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10,
                    maxDevices: 1
                }
            },
            messages_only: {
                id: 'messages_only',
                name: 'Messages Only',
                description: 'Secure backup for your messages',
                price: 199, // $1.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_messages_safekeep',
                stripePriceId: 'price_demo_messages_monthly',
                features: {
                    contactsBackup: false,
                    messagesBackup: true,
                    photosBackup: false,
                    storageLimit: 5, // 5GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10
                },
                limits: {
                    maxStorageGB: 5,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10,
                    maxDevices: 1
                }
            },
            photos_only: {
                id: 'photos_only',
                name: 'Photos Only',
                description: 'Secure backup for your photos',
                price: 499, // $4.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_photos_safekeep',
                stripePriceId: 'price_demo_photos_monthly',
                features: {
                    contactsBackup: false,
                    messagesBackup: false,
                    photosBackup: true,
                    storageLimit: 50, // 50GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: false,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10
                },
                limits: {
                    maxStorageGB: 50,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 10,
                    maxDevices: 1
                }
            },
            // Combination Services
            contacts_messages: {
                id: 'contacts_messages',
                name: 'Contacts + Messages',
                description: 'Backup contacts and messages together',
                price: 249, // $2.49 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_contacts_messages_safekeep',
                stripePriceId: 'price_demo_contacts_messages_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: true,
                    photosBackup: false,
                    storageLimit: 10, // 10GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15
                },
                limits: {
                    maxStorageGB: 10,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15,
                    maxDevices: 2
                }
            },
            contacts_photos: {
                id: 'contacts_photos',
                name: 'Contacts + Photos',
                description: 'Backup contacts and photos together',
                price: 549, // $5.49 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_contacts_photos_safekeep',
                stripePriceId: 'price_demo_contacts_photos_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: false,
                    photosBackup: true,
                    storageLimit: 60, // 60GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15
                },
                limits: {
                    maxStorageGB: 60,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15,
                    maxDevices: 2
                }
            },
            messages_photos: {
                id: 'messages_photos',
                name: 'Messages + Photos',
                description: 'Backup messages and photos together',
                price: 649, // $6.49 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_messages_photos_safekeep',
                stripePriceId: 'price_demo_messages_photos_monthly',
                features: {
                    contactsBackup: false,
                    messagesBackup: true,
                    photosBackup: true,
                    storageLimit: 70, // 70GB
                    backupFrequency: ['manual', 'daily'],
                    prioritySupport: false,
                    advancedEncryption: true,
                    multiDeviceSync: false,
                    backupHistory: 30, // days
                    restoreSpeed: 'standard',
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15
                },
                limits: {
                    maxStorageGB: 70,
                    maxBackupsPerMonth: 50,
                    maxRestoresPerMonth: 15,
                    maxDevices: 2
                }
            },
            complete_backup: {
                id: 'complete_backup',
                name: 'Complete Backup',
                description: 'Full backup solution for all your data',
                price: 699, // $6.99 in cents
                currency: 'usd',
                interval: 'month',
                stripeProductId: 'prod_demo_complete_safekeep',
                stripePriceId: 'price_demo_complete_monthly',
                features: {
                    contactsBackup: true,
                    messagesBackup: true,
                    photosBackup: true,
                    storageLimit: 100, // 100GB
                    backupFrequency: ['manual', 'daily', 'weekly'],
                    prioritySupport: true,
                    advancedEncryption: true,
                    multiDeviceSync: true,
                    backupHistory: 90, // days
                    restoreSpeed: 'priority',
                    maxBackupsPerMonth: -1, // unlimited
                    maxRestoresPerMonth: -1 // unlimited
                },
                limits: {
                    maxStorageGB: 100,
                    maxBackupsPerMonth: -1, // unlimited
                    maxRestoresPerMonth: -1, // unlimited
                    maxDevices: 6
                }
            }
        };
        
        // Test card numbers for demo
        this.testCards = {
            success: {
                number: '****************',
                exp_month: 12,
                exp_year: 2025,
                cvc: '123',
                description: 'Successful payment'
            },
            declined: {
                number: '****************',
                exp_month: 12,
                exp_year: 2025,
                cvc: '123',
                description: 'Card declined'
            },
            expired: {
                number: '****************',
                exp_month: 12,
                exp_year: 2020,
                cvc: '123',
                description: 'Expired card'
            },
            incorrectCvc: {
                number: '****************',
                exp_month: 12,
                exp_year: 2025,
                cvc: '999',
                description: 'Incorrect CVC'
            },
            insufficientFunds: {
                number: '****************',
                exp_month: 12,
                exp_year: 2025,
                cvc: '123',
                description: 'Insufficient funds'
            }
        };
        
        // Webhook event types to handle
        this.webhookEvents = [
            'customer.created',
            'customer.updated',
            'customer.deleted',
            'customer.subscription.created',
            'customer.subscription.updated',
            'customer.subscription.deleted',
            'payment_intent.succeeded',
            'payment_intent.payment_failed',
            'payment_method.attached',
            'payment_method.detached',
            'invoice.payment_succeeded',
            'invoice.payment_failed',
            'invoice.upcoming'
        ];
    }
    
    /**
     * Get Stripe configuration
     */
    getConfig() {
        return this.config;
    }
    
    /**
     * Get publishable key for client-side
     */
    getPublishableKey() {
        return this.config.publishableKey;
    }
    
    /**
     * Get secret key for server-side (demo only)
     */
    getSecretKey() {
        return this.config.secretKey;
    }
    
    /**
     * Get webhook secret
     */
    getWebhookSecret() {
        return this.config.webhookSecret;
    }
    
    /**
     * Get subscription tiers
     */
    getSubscriptionTiers() {
        return this.subscriptionTiers;
    }
    
    /**
     * Get subscription tier by ID
     */
    getSubscriptionTier(tierId) {
        return this.subscriptionTiers[tierId];
    }
    
    /**
     * Get test cards for demo
     */
    getTestCards() {
        return this.testCards;
    }
    
    /**
     * Get webhook events to handle
     */
    getWebhookEvents() {
        return this.webhookEvents;
    }
    
    /**
     * Validate configuration
     */
    validateConfig() {
        const errors = [];
        
        if (!this.config.publishableKey || !this.config.publishableKey.startsWith('pk_')) {
            errors.push('Invalid or missing publishable key');
        }
        
        if (!this.config.secretKey || !this.config.secretKey.startsWith('sk_')) {
            errors.push('Invalid or missing secret key');
        }
        
        if (!this.config.webhookSecret) {
            errors.push('Missing webhook secret');
        }
        
        // Validate subscription tiers
        Object.values(this.subscriptionTiers).forEach(tier => {
            if (!tier.id || !tier.name || tier.price === undefined) {
                errors.push(`Invalid subscription tier: ${tier.id}`);
            }
        });
        
        return {
            valid: errors.length === 0,
            errors: errors
        };
    }
    
    /**
     * Get demo configuration status
     */
    getDemoStatus() {
        const validation = this.validateConfig();
        
        return {
            configured: validation.valid,
            demoMode: this.config.demoMode,
            publishableKeyConfigured: !!this.config.publishableKey,
            secretKeyConfigured: !!this.config.secretKey,
            webhookConfigured: !!this.config.webhookSecret,
            tiersConfigured: Object.keys(this.subscriptionTiers).length > 0,
            errors: validation.errors
        };
    }
    
    /**
     * Update configuration (for demo purposes)
     */
    updateConfig(updates) {
        Object.assign(this.config, updates);
        console.log('✅ Stripe configuration updated');
    }
    
    /**
     * Reset to default configuration
     */
    resetConfig() {
        this.config = {
            publishableKey: 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            secretKey: 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP',
            webhookSecret: 'whsec_demo_webhook_secret_for_safekeep_demo',
            demoMode: true
        };
        console.log('🔄 Stripe configuration reset to defaults');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.StripeConfig = StripeConfig;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StripeConfig;
}