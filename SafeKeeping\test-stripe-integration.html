<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeKeep Stripe Integration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .success {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .error {
            background-color: #ffebee;
            border-color: #f44336;
        }
        .button {
            background-color: #4A90E2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #357abd;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            background-color: #f9f9f9;
            border-left: 4px solid #4A90E2;
        }
        .code {
            font-family: 'Courier New', monospace;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
            overflow-x: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background-color: #4caf50;
            color: white;
        }
        .status.error {
            background-color: #f44336;
            color: white;
        }
        .status.pending {
            background-color: #ff9800;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 SafeKeep Stripe Integration Test</h1>
            <p>Test your Stripe payment integration before using it in the React Native app</p>
        </div>

        <!-- Backend Connection Test -->
        <div class="test-section" id="backend-test">
            <h3>1. Backend Server Connection</h3>
            <p>Testing connection to your local test server...</p>
            <button class="button" onclick="testBackend()">Test Backend Connection</button>
            <div id="backend-result"></div>
        </div>

        <!-- Stripe Configuration Test -->
        <div class="test-section" id="stripe-test">
            <h3>2. Stripe Configuration</h3>
            <p>Verifying that your Stripe keys are properly configured...</p>
            <button class="button" onclick="testStripeConfig()">Test Stripe Config</button>
            <div id="stripe-result"></div>
        </div>

        <!-- Payment Intent Test -->
        <div class="test-section" id="payment-test">
            <h3>3. Payment Intent Creation</h3>
            <p>Testing payment intent creation (mock for now)...</p>
            <button class="button" onclick="testPaymentIntent()">Test Payment Intent</button>
            <div id="payment-result"></div>
        </div>

        <!-- Instructions -->
        <div class="test-section">
            <h3>📋 Next Steps</h3>
            <p>Once all tests pass:</p>
            <ol>
                <li><strong>Start your React Native app:</strong> <code class="code">cd SafeKeepApp && npm start</code></li>
                <li><strong>Open Settings:</strong> Navigate to Settings in your app</li>
                <li><strong>Test Stripe Integration:</strong> Tap "Test Stripe Integration" in the Development section</li>
                <li><strong>Use test card:</strong> 4242 4242 4242 4242 with any future expiry and CVC</li>
            </ol>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<span class="status pending">Testing...</span>';

            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                if (data.status === 'OK') {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <span class="status success">✅ SUCCESS</span>
                            <h4>Backend server is running!</h4>
                            <div class="code">${JSON.stringify(data, null, 2)}</div>
                        </div>
                    `;
                    document.getElementById('backend-test').className = 'test-section success';
                } else {
                    throw new Error('Unexpected response');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result">
                        <span class="status error">❌ FAILED</span>
                        <h4>Cannot connect to backend server</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p><strong>Solution:</strong> Make sure you run <code>node test-server.js</code> in the SafeKeeping directory</p>
                    </div>
                `;
                document.getElementById('backend-test').className = 'test-section error';
            }
        }

        async function testStripeConfig() {
            const resultDiv = document.getElementById('stripe-result');
            resultDiv.innerHTML = '<span class="status pending">Testing...</span>';

            try {
                const response = await fetch(`${API_BASE}/api/stripe-status`);
                const data = await response.json();

                if (data.configured) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <span class="status success">✅ SUCCESS</span>
                            <h4>Stripe is properly configured!</h4>
                            <div class="code">${JSON.stringify(data, null, 2)}</div>
                        </div>
                    `;
                    document.getElementById('stripe-test').className = 'test-section success';
                } else {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <span class="status error">❌ FAILED</span>
                            <h4>Stripe configuration incomplete</h4>
                            <p><strong>Secret Key:</strong> ${data.secret_key_configured ? '✅' : '❌'}</p>
                            <p><strong>Publishable Key:</strong> ${data.publishable_key_configured ? '✅' : '❌'}</p>
                            <p><strong>Solution:</strong> Update your .env file with actual Stripe keys</p>
                        </div>
                    `;
                    document.getElementById('stripe-test').className = 'test-section error';
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result">
                        <span class="status error">❌ FAILED</span>
                        <h4>Cannot test Stripe configuration</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                document.getElementById('stripe-test').className = 'test-section error';
            }
        }

        async function testPaymentIntent() {
            const resultDiv = document.getElementById('payment-result');
            resultDiv.innerHTML = '<span class="status pending">Testing...</span>';

            try {
                const response = await fetch(`${API_BASE}/api/create-payment-intent`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: 500,
                        currency: 'usd',
                        description: 'SafeKeep Test Payment - $5.00'
                    })
                });

                const data = await response.json();

                if (response.ok && data.client_secret) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <span class="status success">✅ SUCCESS</span>
                            <h4>Payment intent created successfully!</h4>
                            <p><strong>Amount:</strong> $${(data.amount / 100).toFixed(2)}</p>
                            <p><strong>Currency:</strong> ${data.currency.toUpperCase()}</p>
                            <p><strong>Payment Intent ID:</strong> ${data.payment_intent_id}</p>
                            ${data.message ? `<p><strong>Note:</strong> ${data.message}</p>` : ''}
                        </div>
                    `;
                    document.getElementById('payment-test').className = 'test-section success';
                } else {
                    throw new Error(data.message || 'Payment intent creation failed');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result">
                        <span class="status error">❌ FAILED</span>
                        <h4>Payment intent creation failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                document.getElementById('payment-test').className = 'test-section error';
            }
        }

        // Auto-run tests when page loads
        window.onload = function() {
            setTimeout(testBackend, 500);
            setTimeout(testStripeConfig, 1000);
            setTimeout(testPaymentIntent, 1500);
        };
    </script>
</body>
</html>
