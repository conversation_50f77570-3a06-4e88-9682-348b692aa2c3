# Backup Integration Tests

This directory contains comprehensive integration tests for the SafeKeep backup functionality, covering end-to-end flows, network conditions, interruption/resume scenarios, and error recovery mechanisms.

## Overview

The integration tests validate the complete backup flow from UI interaction to cloud storage, ensuring that all components work together correctly under various conditions and scenarios.

## Test Suites

### 1. BackupFlowIntegration.test.ts
**Purpose**: Tests the complete end-to-end backup process from UI to storage.

**Key Test Scenarios**:
- ✅ Complete full backup process from UI to storage
- ✅ Video exclusion functionality with mixed media libraries  
- ✅ Partial backup when some data types are disabled
- ✅ Continuation with available data types when one fails
- ✅ Progress tracking and statistics throughout backup
- ✅ Platform-specific behavior (iOS/Android differences)
- ✅ Data integrity and validation
- ✅ Memory and performance with large datasets

**Requirements Covered**: 3.2, 5.2, 5.4, 6.3

### 2. NetworkConditionIntegration.test.ts
**Purpose**: Tests network condition handling and user warnings.

**Key Test Scenarios**:
- ✅ WiFi-only configuration enforcement
- ✅ Network interruption during backup
- ✅ Intermittent network issues with retry logic
- ✅ Network quality and bandwidth handling
- ✅ Network state changes during backup (WiFi to cellular)
- ✅ Complete network loss scenarios
- ✅ Data usage warnings on cellular networks
- ✅ Network recovery and resilience

**Requirements Covered**: 5.2, 5.4

### 3. BackupInterruptionIntegration.test.ts
**Purpose**: Tests backup interruption and resume functionality.

**Key Test Scenarios**:
- ✅ User-initiated backup pause
- ✅ Backup cancellation
- ✅ System interruption (app backgrounding)
- ✅ Network disconnection during backup
- ✅ Low battery interruption
- ✅ Detection of resumable backup sessions
- ✅ Resume backup from last checkpoint
- ✅ Recovery state management
- ✅ User interaction during resume (choose resume vs restart)
- ✅ Multi-data type resume from correct checkpoint

**Requirements Covered**: 6.3

### 4. ErrorRecoveryIntegration.test.ts
**Purpose**: Tests error scenarios and recovery mechanisms.

**Key Test Scenarios**:
- ✅ Authentication and authorization errors
- ✅ Network and connectivity errors with retry logic
- ✅ Storage and quota errors
- ✅ Encryption and security errors
- ✅ Platform-specific errors (iOS/Android limitations)
- ✅ Recovery mechanisms and retry functionality
- ✅ Actionable error notifications
- ✅ Partial recovery scenarios
- ✅ Error aggregation and reporting

**Requirements Covered**: 3.2, 5.2, 5.4, 6.3

## Running the Tests

### Prerequisites
- Node.js and npm installed
- React Native development environment set up
- Jest testing framework installed

### Run All Integration Tests
```bash
# From the SafeKeepApp directory
npm test -- __tests__/integration/

# Or run the custom test runner
node __tests__/integration/runIntegrationTests.js
```

### Run Individual Test Suites
```bash
# End-to-end backup flow tests
npm test -- __tests__/integration/BackupFlowIntegration.test.ts

# Network condition tests
npm test -- __tests__/integration/NetworkConditionIntegration.test.ts

# Backup interruption and resume tests
npm test -- __tests__/integration/BackupInterruptionIntegration.test.ts

# Error recovery tests
npm test -- __tests__/integration/ErrorRecoveryIntegration.test.ts
```

### Run with Coverage
```bash
npm test -- __tests__/integration/ --coverage
```

## Test Architecture

### Mocking Strategy
The integration tests use comprehensive mocking to simulate:
- **React Native APIs**: Platform, PermissionsAndroid, AsyncStorage
- **External Libraries**: Camera Roll, React Native FS, Supabase
- **Network Conditions**: WiFi/cellular connectivity, network failures
- **Device States**: Battery levels, storage availability
- **User Interactions**: Permission grants/denials, backup interruptions

### Test Data
Each test suite includes realistic mock data:
- **Contacts**: Multiple contacts with various field combinations
- **Messages**: SMS messages with different types and timestamps
- **Photos**: Image files with metadata, excluding video files
- **Mixed Media**: Libraries containing both photos and videos for exclusion testing

### Progress Tracking
Tests validate that progress callbacks are called correctly:
- Real-time progress updates for each data type
- Overall progress calculation
- Estimated time remaining
- Current item being processed

## Key Testing Patterns

### 1. End-to-End Flow Testing
```typescript
it('should complete full backup process from UI to storage', async () => {
  const progressUpdates: any[] = [];
  const progressCallback = jest.fn((progress) => {
    progressUpdates.push(progress);
  });

  const result = await BackupManager.startBackup(defaultConfiguration, progressCallback);

  // Verify backup completed successfully
  expect(result.success).toBe(true);
  expect(result.itemsProcessed).toBe(6); // 2 contacts + 2 messages + 2 photos
  
  // Verify all services were called
  expect(ContactBackupService.backupContacts).toHaveBeenCalled();
  expect(MessageBackupService.backupMessages).toHaveBeenCalled();
  expect(PhotoBackupService.backupPhotos).toHaveBeenCalled();
  
  // Verify progress updates
  expect(progressCallback).toHaveBeenCalled();
  expect(progressUpdates[progressUpdates.length - 1].overallProgress).toBe(100);
});
```

### 2. Video Exclusion Testing
```typescript
it('should exclude videos from photo backup in mixed media libraries', async () => {
  const result = await BackupManager.startBackup({
    ...defaultConfiguration,
    includeContacts: false,
    includeMessages: false
  });

  expect(result.success).toBe(true);
  expect(result.itemsProcessed).toBe(2); // Only 2 photos, video excluded

  // Verify PhotoBackupService received only photos
  expect(PhotoBackupService.backupPhotos).toHaveBeenCalledWith(
    expect.arrayContaining([
      expect.objectContaining({ type: 'image/jpeg' }),
      expect.objectContaining({ type: 'image/png' })
    ]),
    expect.any(Function)
  );
});
```

### 3. Network Condition Testing
```typescript
it('should prevent backup when WiFi-only is enabled but not on WiFi', async () => {
  const { isWiFiConnected } = require('../../src/utils/helpers');
  isWiFiConnected.mockResolvedValue(false);

  const result = await BackupManager.startBackup(defaultConfiguration);

  expect(result.success).toBe(false);
  expect(result.errors[0].type).toBe('network');
  expect(result.errors[0].message).toContain('WiFi-only');
});
```

### 4. Interruption and Resume Testing
```typescript
it('should handle backup interruption gracefully', async () => {
  const backupPromise = BackupManager.startBackup(defaultConfiguration);

  // Simulate interruption after a short delay
  setTimeout(() => {
    BackupManager.pauseBackup();
  }, 50);

  const result = await backupPromise;

  expect(BackupManager.isPaused()).toBe(true);
  expect(result.itemsProcessed).toBeLessThan(totalExpectedItems);
});
```

### 5. Error Recovery Testing
```typescript
it('should handle network timeout with retry logic', async () => {
  let attemptCount = 0;
  
  jest.spyOn(CloudStorageService, 'uploadFile').mockImplementation(async () => {
    attemptCount++;
    if (attemptCount <= 2) {
      throw new Error('Network timeout');
    }
    return { success: true, fileId: 'success_after_retry' };
  });

  const result = await BackupManager.startBackup(configuration);

  expect(result.success).toBe(true);
  expect(attemptCount).toBe(3); // Should have retried twice
});
```

## Test Coverage

The integration tests provide comprehensive coverage of:

### Functional Requirements
- ✅ **Requirement 3.2**: Video exclusion functionality
- ✅ **Requirement 5.2**: Network condition handling
- ✅ **Requirement 5.4**: Battery level monitoring
- ✅ **Requirement 6.3**: Error handling and recovery

### Technical Scenarios
- ✅ **Happy Path**: Complete successful backup
- ✅ **Partial Success**: Some data types succeed, others fail
- ✅ **Network Issues**: Various network failure scenarios
- ✅ **Permission Issues**: Permission denial and revocation
- ✅ **Storage Issues**: Quota exceeded, insufficient space
- ✅ **Platform Issues**: iOS/Android specific limitations
- ✅ **Interruption**: User and system-initiated interruptions
- ✅ **Resume**: Backup continuation from checkpoints
- ✅ **Error Recovery**: Retry mechanisms and user notifications

### Edge Cases
- ✅ **Large Datasets**: Performance with thousands of items
- ✅ **Mixed Media**: Photos and videos in same library
- ✅ **Corrupted Data**: Handling of corrupted files
- ✅ **Concurrent Operations**: Multiple backup attempts
- ✅ **State Corruption**: Recovery from corrupted state
- ✅ **Memory Constraints**: Efficient processing of large files

## Continuous Integration

These integration tests are designed to run in CI/CD pipelines:

### GitHub Actions Example
```yaml
- name: Run Integration Tests
  run: |
    cd SafeKeepApp
    npm ci
    node __tests__/integration/runIntegrationTests.js
```

### Test Reporting
The test runner generates:
- **Console Output**: Detailed test results with colors
- **Coverage Reports**: HTML and LCOV formats
- **Exit Codes**: 0 for success, 1 for failures
- **Test Metrics**: Pass/fail counts and success rates

## Troubleshooting

### Common Issues

1. **Test Timeouts**
   - Increase Jest timeout in `jest.config.js`
   - Check for infinite loops in mock implementations

2. **Mock Conflicts**
   - Clear mocks between tests using `jest.clearAllMocks()`
   - Ensure mock implementations are consistent

3. **Async Issues**
   - Use `await` for all async operations
   - Properly handle Promise rejections

4. **Platform-Specific Failures**
   - Check Platform.OS mocking
   - Verify platform-specific API mocks

### Debug Mode
Run tests with additional logging:
```bash
DEBUG=true npm test -- __tests__/integration/
```

## Contributing

When adding new integration tests:

1. **Follow Naming Convention**: `[Feature]Integration.test.ts`
2. **Include Comprehensive Scenarios**: Happy path, error cases, edge cases
3. **Mock External Dependencies**: Keep tests isolated and fast
4. **Document Test Purpose**: Clear descriptions of what each test validates
5. **Update This README**: Add new test scenarios to the documentation

## Performance Considerations

- Tests run with 30-second timeout for complex scenarios
- Mocks are optimized to avoid unnecessary delays
- Large datasets use realistic but manageable sizes
- Memory usage is monitored for leak detection

## Security Testing

Integration tests include security-focused scenarios:
- Encryption failure handling
- Authentication token expiration
- Permission revocation during backup
- Data integrity validation
- Secure transmission verification