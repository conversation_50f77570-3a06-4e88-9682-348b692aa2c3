// Simple test server for Stripe integration
// Run this with: node test-server.js

const http = require('http');
const url = require('url');
const querystring = require('querystring');

// Load environment variables manually
const fs = require('fs');
const path = require('path');

// Simple .env loader
function loadEnv() {
  try {
    const envPath = path.join(__dirname, '.env');
    const envFile = fs.readFileSync(envPath, 'utf8');
    const lines = envFile.split('\n');

    lines.forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.log('No .env file found or error reading it');
  }
}

loadEnv();

const PORT = 3000;

// Simple CORS headers
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
}

// Simple JSON response helper
function sendJSON(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const method = req.method;
  const pathname = parsedUrl.pathname;

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // Health check endpoint
  if (pathname === '/health' && method === 'GET') {
    sendJSON(res, 200, {
      status: 'OK',
      message: 'SafeKeep Test Server Running',
      timestamp: new Date().toISOString(),
      stripe_configured: !!process.env.STRIPE_SECRET_KEY && !process.env.STRIPE_SECRET_KEY.includes('PLACEHOLDER')
    });
    return;
  }

  // Test endpoint for payment intent creation
  if (pathname === '/api/create-payment-intent' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', async () => {
      try {
        const requestData = JSON.parse(body);
        const { amount = 500, currency = 'usd', description = 'SafeKeep Test Payment' } = requestData;

        console.log('💳 Creating test payment intent:', { amount, currency, description });

        // Check if Stripe is properly configured
        if (!process.env.STRIPE_SECRET_KEY || process.env.STRIPE_SECRET_KEY.includes('PLACEHOLDER')) {
          sendJSON(res, 500, {
            error: 'Stripe not configured',
            message: 'Please add your Stripe secret key to the .env file'
          });
          return;
        }

        // Try to use real Stripe if available, otherwise mock
        try {
          // Check if we can load Stripe from the SafeKeepApp directory
          const Stripe = require('./SafeKeepApp/node_modules/stripe');
          const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

          // Create real payment intent
          const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount),
            currency,
            description,
            metadata: {
              app: 'SafeKeep',
              test: 'true',
              environment: 'development',
            },
            automatic_payment_methods: {
              enabled: true,
            },
          });

          console.log('✅ Real payment intent created:', paymentIntent.id);

          sendJSON(res, 200, {
            client_secret: paymentIntent.client_secret,
            payment_intent_id: paymentIntent.id,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
          });

        } catch (stripeError) {
          console.log('⚠️ Stripe not available, using mock response:', stripeError.message);

          // Fallback to mock response
          sendJSON(res, 200, {
            client_secret: 'pi_test_mock_client_secret_for_testing',
            payment_intent_id: 'pi_test_mock_' + Date.now(),
            amount: Math.round(amount),
            currency: currency,
            message: 'Mock response - Stripe package not available. Install with: npm install stripe'
          });
        }

      } catch (error) {
        console.error('❌ Payment intent creation failed:', error);
        sendJSON(res, 500, {
          error: 'Payment intent creation failed',
          message: error.message
        });
      }
    });
    return;
  }

  // Test endpoint to verify Stripe configuration
  if (pathname === '/api/stripe-status' && method === 'GET') {
    const hasSecretKey = !!process.env.STRIPE_SECRET_KEY && !process.env.STRIPE_SECRET_KEY.includes('PLACEHOLDER');
    const hasPublishableKey = !!process.env.STRIPE_PUBLISHABLE_KEY && !process.env.STRIPE_PUBLISHABLE_KEY.includes('PLACEHOLDER');

    sendJSON(res, 200, {
      configured: hasSecretKey && hasPublishableKey,
      secret_key_configured: hasSecretKey,
      publishable_key_configured: hasPublishableKey,
      environment: process.env.NODE_ENV || 'development',
    });
    return;
  }

  // 404 for unknown routes
  sendJSON(res, 404, { error: 'Not found' });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 SafeKeep Test Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 Stripe status: http://localhost:${PORT}/api/stripe-status`);
  
  // Check configuration
  const hasStripeKeys = process.env.STRIPE_SECRET_KEY && 
                       process.env.STRIPE_PUBLISHABLE_KEY && 
                       !process.env.STRIPE_SECRET_KEY.includes('PLACEHOLDER') &&
                       !process.env.STRIPE_PUBLISHABLE_KEY.includes('PLACEHOLDER');

  if (hasStripeKeys) {
    console.log('✅ Stripe keys configured - ready for testing!');
  } else {
    console.log('⚠️ WARNING: Stripe keys not configured. Please update your .env file.');
  }
});

module.exports = server;
