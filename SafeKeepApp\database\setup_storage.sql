-- SafeKeep Storage Policies Setup
-- Run this in Supabase SQL Editor AFTER creating your storage buckets

-- Note: You must first create the buckets in the Supabase Dashboard:
-- 1. Go to Storage in your Supabase Dashboard
-- 2. Create bucket "user-data" (private, 50MB limit, application/octet-stream)
-- 3. Create bucket "thumbnails" (public, 5MB limit, image/jpeg,image/png,image/webp)

-- Storage policies for user-data bucket (private encrypted files)
CREATE POLICY "Users can upload own encrypted files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'user-data' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can view own encrypted files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'user-data' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can update own encrypted files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'user-data' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can delete own encrypted files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'user-data' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

-- Storage policies for thumbnails bucket (public thumbnails)
CREATE POLICY "Users can upload own thumbnails" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'thumbnails' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can view own thumbnails" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'thumbnails' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can update own thumbnails" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'thumbnails' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can delete own thumbnails" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'thumbnails' 
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

-- Verify storage policies
SELECT 'Storage Policies Created:' as status,
       policyname,
       cmd as operation,
       qual as condition
FROM pg_policies 
WHERE schemaname = 'storage'
AND tablename = 'objects'
ORDER BY policyname;