import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useSelector } from 'react-redux';
import { Card, Text, ProgressBar, Chip, Button, FAB } from 'react-native-paper';
import { Header } from 'react-native-elements';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation, CompositeNavigationProp } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootState } from '../../store';
import { COLORS, SPACING } from '../../utils/constants';
import PermissionStatus from '../../components/common/PermissionStatus';
import CloudStorageService from '../../services/CloudStorageService';
import AuthService from '../../services/AuthService';
import { MainTabParamList, RootStackParamList } from '../../navigation/AppNavigator';
import { BackupQuickActions } from '../../components/backup';
import {
  useIsBackupInProgress,
  useOverallBackupProgress,
  useLastSuccessfulBackup,
  useBackupErrors,
  useFormattedBackupStatistics
} from '../../store/hooks/backupHooks';

type DashboardScreenNavigationProp = CompositeNavigationProp<
  BottomTabNavigationProp<MainTabParamList>,
  StackNavigationProp<RootStackParamList>
>;

const DashboardScreen = () => {
  const navigation = useNavigation<DashboardScreenNavigationProp>();
  const [storageQuota, setStorageQuota] = useState<any>(null);

  // New backup hooks
  const isBackupInProgress = useIsBackupInProgress();
  const overallProgress = useOverallBackupProgress();
  const lastSuccessfulBackup = useLastSuccessfulBackup();
  const backupErrors = useBackupErrors();
  const backupStatistics = useFormattedBackupStatistics();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const quota = await CloudStorageService.getStorageQuota();
      setStorageQuota(quota);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };
  const { user } = useSelector((state: RootState) => state.auth);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  const getStoragePercentage = () => {
    if (!user) return 0;
    return (user.storageUsed / user.storageLimit) * 100;
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        centerComponent={{
          text: 'Dashboard',
          style: { color: '#fff', fontSize: 20, fontWeight: 'bold' }
        }}
        backgroundColor={COLORS.primary}
      />

      <ScrollView style={styles.scrollView}>
        <PermissionStatus />

        {/* Storage Quota Card */}
        {storageQuota && (
          <Card style={styles.statusCard}>
            <Card.Content>
              <View style={styles.cardHeader}>
                <Icon name="cloud-outline" size={24} color={COLORS.primary} />
                <Text variant="titleMedium" style={styles.cardTitle}>
                  Cloud Storage
                </Text>
              </View>

              <View style={styles.storageInfo}>
                <Text variant="bodyLarge" style={styles.storageText}>
                  {formatBytes(storageQuota.used)} of {formatBytes(storageQuota.total)} used
                </Text>

                <ProgressBar
                  progress={storageQuota.percentage / 100}
                  color={storageQuota.percentage > 90 ? COLORS.error : COLORS.primary}
                  style={styles.storageProgress}
                />

                <View style={styles.storageDetails}>
                  <Text style={styles.storageLabel}>
                    {storageQuota.percentage}% used
                  </Text>
                  <Text style={styles.storageLabel}>
                    {formatBytes(storageQuota.remaining)} remaining
                  </Text>
                </View>

                {storageQuota.percentage > 80 && (
                  <Chip
                    icon="alert"
                    style={[styles.warningChip, {
                      backgroundColor: storageQuota.percentage > 90 ? COLORS.error : COLORS.warning
                    }]}
                    textStyle={{ color: '#fff' }}
                  >
                    {storageQuota.percentage > 90 ? 'Storage Almost Full' : 'Storage Getting Full'}
                  </Chip>
                )}
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Enhanced Backup Status Card */}
        <Card style={styles.statusCard}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Icon
                name={isBackupInProgress ? "sync" : backupErrors.length > 0 ? "alert-circle" : "check-circle"}
                size={24}
                color={isBackupInProgress ? COLORS.warning : backupErrors.length > 0 ? COLORS.error : COLORS.success}
              />
              <Text variant="titleMedium" style={styles.cardTitle}>
                Backup Status
              </Text>
              <TouchableOpacity
                style={styles.settingsIcon}
                onPress={() => navigation.navigate('BackupSettings', {})}
              >
                <Icon name="cog" size={20} color={COLORS.textSecondary} />
              </TouchableOpacity>
            </View>

            {isBackupInProgress ? (
              <View style={styles.progressContainer}>
                <Chip
                  icon={() => <Icon name="sync" size={16} color="#fff" />}
                  style={[styles.statusChip, { backgroundColor: COLORS.warning }]}
                  textStyle={{ color: '#fff' }}
                >
                  Backup in progress... {overallProgress.percentage}%
                </Chip>

                <ProgressBar
                  progress={overallProgress.percentage / 100}
                  color={COLORS.warning}
                  style={styles.backupProgress}
                />

                <Text variant="bodySmall" style={styles.progressText}>
                  {overallProgress.completedItems} of {overallProgress.totalItems} items completed
                </Text>

                <Button
                  mode="outlined"
                  onPress={() => navigation.navigate('BackupProgress', {})}
                  style={styles.viewProgressButton}
                  compact
                >
                  View Details
                </Button>
              </View>
            ) : backupErrors.length > 0 ? (
              <View>
                <Chip
                  icon={() => <Icon name="alert" size={16} color="#fff" />}
                  style={[styles.statusChip, { backgroundColor: COLORS.error }]}
                  textStyle={{ color: '#fff' }}
                >
                  {backupErrors.length} backup error{backupErrors.length > 1 ? 's' : ''}
                </Chip>

                <Text variant="bodySmall" style={styles.errorText}>
                  Some items failed to backup. Tap to review and retry.
                </Text>

                <Button
                  mode="outlined"
                  onPress={() => navigation.navigate('BackupProgress', {})}
                  style={styles.viewProgressButton}
                  compact
                >
                  Review Errors
                </Button>
              </View>
            ) : (
              <View>
                <Chip
                  icon={() => <Icon name="check" size={16} color="#fff" />}
                  style={[styles.statusChip, { backgroundColor: COLORS.success }]}
                  textStyle={{ color: '#fff' }}
                >
                  All backed up
                </Chip>

                <Text variant="bodySmall" style={styles.lastBackup}>
                  Last backup: {lastSuccessfulBackup ? formatDate(lastSuccessfulBackup) : 'Never'}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        <View style={styles.statsContainer}>
          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Icon name="camera" size={32} color={COLORS.primary} />
              <Text variant="headlineMedium" style={styles.statNumber}>
                {backupStatistics.dataTypeBreakdown.photos.count}
              </Text>
              <Text variant="bodyMedium" style={styles.statLabel}>
                Photos
              </Text>
            </Card.Content>
          </Card>

          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Icon name="contacts" size={32} color={COLORS.primary} />
              <Text variant="headlineMedium" style={styles.statNumber}>
                {backupStatistics.dataTypeBreakdown.contacts.count}
              </Text>
              <Text variant="bodyMedium" style={styles.statLabel}>
                Contacts
              </Text>
            </Card.Content>
          </Card>
        </View>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <Icon name="message-text" size={32} color={COLORS.primary} />
            <Text variant="headlineMedium" style={styles.statNumber}>
              156
            </Text>
            <Text variant="bodyMedium" style={styles.statLabel}>
              Text Messages
            </Text>
          </Card.Content>
        </Card>

        {/* Backup Quick Actions */}
        <BackupQuickActions />

        {user && (
          <Card style={styles.storageCard}>
            <Card.Content>
              <View style={styles.cardHeader}>
                <Icon name="harddisk" size={24} color={COLORS.primary} />
                <Text variant="titleMedium" style={styles.cardTitle}>
                  Storage Usage
                </Text>
              </View>

              <ProgressBar
                progress={getStoragePercentage() / 100}
                color={COLORS.primary}
                style={styles.progressBar}
              />

              <Text variant="bodyMedium" style={styles.storageText}>
                {(user.storageUsed / 1024 / 1024).toFixed(1)} MB of{' '}
                {(user.storageLimit / 1024 / 1024).toFixed(0)} MB used
              </Text>
            </Card.Content>
          </Card>
        )}
      </ScrollView>

      {/* Quick Backup FAB */}
      {!isBackupInProgress && (
        <FAB
          icon="backup-restore"
          label="Quick Backup"
          onPress={() => navigation.navigate('Backup')}
          style={styles.fab}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
    padding: SPACING.lg,
  },
  statusCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  cardTitle: {
    marginLeft: SPACING.sm,
    color: COLORS.text,
  },
  storageInfo: {
    gap: SPACING.sm,
  },
  storageText: {
    color: COLORS.text,
    textAlign: 'center',
  },
  storageProgress: {
    height: 8,
    borderRadius: 4,
  },
  storageDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  storageLabel: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  warningChip: {
    alignSelf: 'center',
    marginTop: SPACING.sm,
  },
  statusChip: {
    alignSelf: 'flex-start',
    marginBottom: SPACING.sm,
  },
  lastBackup: {
    color: COLORS.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.lg,
  },
  statCard: {
    flex: 0.48,
    elevation: 4,
    marginBottom: SPACING.lg,
  },
  statContent: {
    alignItems: 'center',
  },
  statNumber: {
    color: COLORS.primary,
    marginVertical: SPACING.xs,
  },
  statLabel: {
    color: COLORS.textSecondary,
  },
  storageCard: {
    elevation: 4,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginVertical: SPACING.md,
  },
  settingsIcon: {
    marginLeft: 'auto',
    padding: SPACING.xs,
  },
  progressContainer: {
    gap: SPACING.sm,
  },
  backupProgress: {
    height: 6,
    borderRadius: 3,
  },
  progressText: {
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  errorText: {
    color: COLORS.textSecondary,
    marginBottom: SPACING.sm,
  },
  viewProgressButton: {
    marginTop: SPACING.xs,
    borderColor: COLORS.primary,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: COLORS.primary,
  },
});

export default DashboardScreen;
