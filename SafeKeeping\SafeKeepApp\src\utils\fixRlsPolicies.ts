import { executeRlsFix } from './executeRlsFix';

/**
 * Utility function to fix RLS policies
 * This addresses the syntax error with CREATE POLICY IF NOT EXISTS
 */
export async function fixRlsPolicies(): Promise<{ success: boolean; message: string }> {
    try {
        console.log('Starting RLS policy fix...');

        // Use the executeRlsFix function to fix RLS policies
        const result = await executeRlsFix();

        if (!result.success) {
            console.error('Error fixing RLS policies:', result.message);
            return result;
        }

        console.log('RLS policy fix completed successfully');
        return {
            success: true,
            message: 'RLS policies have been fixed successfully'
        };
    } catch (error) {
        console.error('Unexpected error fixing RLS policies:', error);
        return {
            success: false,
            message: `Unexpected error: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}