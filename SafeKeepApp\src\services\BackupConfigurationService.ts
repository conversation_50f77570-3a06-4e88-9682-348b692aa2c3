import AsyncStorage from '@react-native-async-storage/async-storage';
import { BackupConfiguration } from '../types/backup';

const BACKUP_SETTINGS_KEY = '@SafeKeep:backupConfiguration';

export class BackupConfigurationService {
  private static defaultConfiguration: BackupConfiguration = {
    autoBackup: false,
    wifiOnly: true,
    includeContacts: true,
    includeMessages: true,
    includePhotos: true,
    compressionLevel: 'medium',
  };

  /**
   * Load backup configuration from AsyncStorage
   */
  static async loadConfiguration(): Promise<BackupConfiguration> {
    try {
      const stored = await AsyncStorage.getItem(BACKUP_SETTINGS_KEY);
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        // Merge with defaults to ensure all properties exist
        return { ...this.defaultConfiguration, ...parsedConfig };
      }
      return this.defaultConfiguration;
    } catch (error) {
      console.error('Failed to load backup configuration:', error);
      return this.defaultConfiguration;
    }
  }

  /**
   * Save backup configuration to AsyncStorage
   */
  static async saveConfiguration(configuration: BackupConfiguration): Promise<void> {
    try {
      await AsyncStorage.setItem(BACKUP_SETTINGS_KEY, JSON.stringify(configuration));
    } catch (error) {
      console.error('Failed to save backup configuration:', error);
      throw new Error('Failed to save backup settings');
    }
  }

  /**
   * Update specific configuration setting
   */
  static async updateSetting<K extends keyof BackupConfiguration>(
    key: K,
    value: BackupConfiguration[K]
  ): Promise<BackupConfiguration> {
    const currentConfig = await this.loadConfiguration();
    const newConfig = { ...currentConfig, [key]: value };
    await this.saveConfiguration(newConfig);
    return newConfig;
  }

  /**
   * Reset configuration to defaults
   */
  static async resetConfiguration(): Promise<BackupConfiguration> {
    await this.saveConfiguration(this.defaultConfiguration);
    return this.defaultConfiguration;
  }

  /**
   * Get data usage estimate based on configuration
   */
  static getDataUsageEstimate(configuration: BackupConfiguration): {
    contacts: string;
    messages: string;
    photos: string;
    total: string;
  } {
    const estimates = {
      contacts: configuration.includeContacts ? '~1-5 MB' : '0 MB',
      messages: configuration.includeMessages ? '~5-50 MB' : '0 MB',
      photos: configuration.includePhotos 
        ? configuration.compressionLevel === 'high' 
          ? '~50-200 MB' 
          : configuration.compressionLevel === 'medium'
          ? '~100-500 MB'
          : '~200-1000 MB'
        : '0 MB',
      total: 'Varies by data amount'
    };

    return estimates;
  }

  /**
   * Validate configuration settings
   */
  static validateConfiguration(configuration: BackupConfiguration): {
    isValid: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];
    
    // Check if at least one data type is selected
    if (!configuration.includeContacts && !configuration.includeMessages && !configuration.includePhotos) {
      warnings.push('At least one data type must be selected for backup');
    }

    // Warn about cellular data usage
    if (!configuration.wifiOnly) {
      warnings.push('Cellular backup may consume significant data allowance');
    }

    // Warn about auto backup without WiFi-only
    if (configuration.autoBackup && !configuration.wifiOnly) {
      warnings.push('Automatic backup over cellular may result in unexpected data charges');
    }

    return {
      isValid: warnings.length === 0 || warnings.every(w => !w.includes('must be selected')),
      warnings
    };
  }
}