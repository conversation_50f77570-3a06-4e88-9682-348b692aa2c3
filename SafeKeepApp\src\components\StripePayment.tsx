import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {
  Button,
  Card,
  Text,
  ActivityIndicator,
} from 'react-native-paper';
import {
  useStripe,
  useConfirmPayment,
  CardField,
  CardFieldInput,
} from '@stripe/stripe-react-native';

import { COLORS, SPACING } from '../utils/constants';
import { SAFEKEEP_PLANS } from '../services/StripeService';

interface StripePaymentProps {
  selectedPlan?: keyof typeof SAFEKEEP_PLANS;
  onPaymentSuccess?: (paymentIntent: any) => void;
  onPaymentError?: (error: string) => void;
}

const StripePayment: React.FC<StripePaymentProps> = ({
  selectedPlan = 'PREMIUM',
  onPaymentSuccess,
  onPaymentError,
}) => {
  const { confirmPayment } = useConfirmPayment();
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardDetails, setCardDetails] = useState<CardFieldInput.Details | null>(null);

  const plan = SAFEKEEP_PLANS[selectedPlan];

  // Handle payment submission
  const handlePayment = async () => {
    if (!cardDetails?.complete) {
      Alert.alert('Incomplete Card', 'Please enter complete card details.');
      return;
    }

    setIsProcessing(true);

    try {
      // Step 1: Create payment intent on your backend
      // This should be an API call to your server, not directly to Stripe
      const paymentIntentResponse = await createPaymentIntentOnBackend({
        amount: plan.price,
        currency: plan.currency,
        description: `SafeKeep ${plan.name} Subscription`,
      });

      if (!paymentIntentResponse.client_secret) {
        throw new Error('Failed to create payment intent');
      }

      // Step 2: Confirm payment with Stripe
      const { error, paymentIntent } = await confirmPayment(
        paymentIntentResponse.client_secret,
        {
          paymentMethodType: 'Card',
          paymentMethodData: {
            billingDetails: {
              // Add customer billing details here
              email: '<EMAIL>', // Replace with actual customer email
            },
          },
        }
      );

      if (error) {
        console.error('Payment confirmation error:', error);
        const errorMessage = error.message || 'Payment failed. Please try again.';
        Alert.alert('Payment Failed', errorMessage);
        onPaymentError?.(errorMessage);
      } else if (paymentIntent) {
        console.log('Payment successful:', paymentIntent);
        Alert.alert(
          'Payment Successful!',
          `Your ${plan.name} subscription has been activated. Welcome to SafeKeep!`
        );
        onPaymentSuccess?.(paymentIntent);
      }
    } catch (error) {
      console.error('Payment error:', error);
      const errorMessage = error.message || 'An unexpected error occurred.';
      Alert.alert('Payment Error', errorMessage);
      onPaymentError?.(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // This function should call your backend API, not Stripe directly
  // IMPORTANT: Never call Stripe's secret key from the frontend!
  const createPaymentIntentOnBackend = async (data: {
    amount: number;
    currency: string;
    description: string;
  }) => {
    // TODO: Replace this with your actual backend API endpoint
    // Example: POST /api/create-payment-intent
    
    console.log('🚨 IMPORTANT: This should call your backend API, not Stripe directly!');
    console.log('Backend API call data:', data);
    
    // For demo purposes, we'll simulate a backend response
    // In production, this should be a real API call to your server
    return {
      client_secret: 'pi_demo_client_secret_placeholder',
      // Your backend should return the client_secret from Stripe PaymentIntent
    };
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Plan Information */}
        <Card style={styles.planCard}>
          <Card.Content>
            <Text variant="headlineSmall" style={styles.planTitle}>
              {plan.name}
            </Text>
            <Text variant="headlineMedium" style={styles.planPrice}>
              ${(plan.price / 100).toFixed(2)}
              <Text variant="bodyMedium" style={styles.planInterval}>
                /{plan.interval}
              </Text>
            </Text>
            
            <View style={styles.featuresContainer}>
              {plan.features.map((feature, index) => (
                <View key={index} style={styles.featureRow}>
                  <Text style={styles.featureIcon}>✓</Text>
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Payment Form */}
        <Card style={styles.paymentCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.paymentTitle}>
              Payment Information
            </Text>
            
            <Text style={styles.securityNote}>
              🔒 Your payment information is encrypted and secure
            </Text>

            {/* Stripe Card Input */}
            <View style={styles.cardFieldContainer}>
              <CardField
                postalCodeEnabled={true}
                placeholders={{
                  number: '4242 4242 4242 4242',
                  expiration: 'MM/YY',
                  cvc: 'CVC',
                  postalCode: 'ZIP',
                }}
                cardStyle={{
                  backgroundColor: '#FFFFFF',
                  textColor: '#000000',
                  fontSize: 16,
                  placeholderColor: '#999999',
                }}
                style={styles.cardField}
                onCardChange={(cardDetails) => {
                  setCardDetails(cardDetails);
                }}
              />
            </View>

            {/* Payment Button */}
            <Button
              mode="contained"
              onPress={handlePayment}
              disabled={!cardDetails?.complete || isProcessing}
              style={styles.paymentButton}
              contentStyle={styles.paymentButtonContent}
            >
              {isProcessing ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                `Subscribe for $${(plan.price / 100).toFixed(2)}/${plan.interval}`
              )}
            </Button>

            <Text style={styles.disclaimer}>
              By subscribing, you agree to SafeKeep's Terms of Service and Privacy Policy.
              You can cancel anytime from your account settings.
            </Text>
          </Card.Content>
        </Card>

        {/* Test Card Information */}
        <Card style={styles.testCard}>
          <Card.Content>
            <Text variant="titleSmall" style={styles.testTitle}>
              Test Card Numbers (Development Only)
            </Text>
            <Text style={styles.testCardNumber}>4242 4242 4242 4242 - Visa</Text>
            <Text style={styles.testCardNumber}>5555 5555 5555 4444 - Mastercard</Text>
            <Text style={styles.testNote}>
              Use any future expiry date and any 3-digit CVC
            </Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  planCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  planTitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  planPrice: {
    color: COLORS.primary,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: SPACING.md,
  },
  planInterval: {
    color: COLORS.textSecondary,
    fontWeight: 'normal',
  },
  featuresContainer: {
    marginTop: SPACING.md,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  featureIcon: {
    color: COLORS.success,
    marginRight: SPACING.sm,
    fontSize: 16,
    fontWeight: 'bold',
  },
  featureText: {
    color: COLORS.text,
    flex: 1,
  },
  paymentCard: {
    marginBottom: SPACING.lg,
    elevation: 4,
  },
  paymentTitle: {
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  securityNote: {
    color: COLORS.success,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  cardFieldContainer: {
    marginBottom: SPACING.lg,
  },
  cardField: {
    width: '100%',
    height: 50,
    marginVertical: SPACING.sm,
  },
  paymentButton: {
    backgroundColor: COLORS.primary,
    marginBottom: SPACING.md,
  },
  paymentButtonContent: {
    height: 50,
  },
  disclaimer: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  testCard: {
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
    elevation: 2,
  },
  testTitle: {
    color: COLORS.warning,
    marginBottom: SPACING.sm,
    fontWeight: 'bold',
  },
  testCardNumber: {
    fontFamily: 'monospace',
    fontSize: 14,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  testNote: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginTop: SPACING.sm,
  },
});

export default StripePayment;
