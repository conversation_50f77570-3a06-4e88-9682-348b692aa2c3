/**
 * A/B Testing Framework - Framework for running A/B tests on demo features
 * Implements A/B testing capabilities for performance monitoring
 */

class ABTestingFramework {
    constructor(performanceMonitor) {
        this.performanceMonitor = performanceMonitor;
        this.tests = new Map();
        this.userVariants = new Map();
        this.events = [];
        this.userId = this.getUserId();
        
        this.config = {
            persistVariants: true,
            trackingEnabled: true,
            maxEvents: 1000,
            defaultTrafficSplit: 0.5
        };
        
        this.init();
    }

    init() {
        this.loadStoredVariants();
        this.setupDefaultTests();
        console.log('A/B Testing Framework initialized for user:', this.userId);
    }

    getUserId() {
        let userId = localStorage.getItem('ab_test_user_id');
        if (!userId) {
            userId = 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            localStorage.setItem('ab_test_user_id', userId);
        }
        return userId;
    }

    loadStoredVariants() {
        if (this.config.persistVariants) {
            const stored = localStorage.getItem('ab_test_variants');
            if (stored) {
                try {
                    const variants = JSON.parse(stored);
                    Object.entries(variants).forEach(([testName, variant]) => {
                        this.userVariants.set(testName, variant);
                    });
                } catch (e) {
                    console.warn('Failed to load stored A/B test variants:', e);
                }
            }
        }
    }

    saveVariants() {
        if (this.config.persistVariants) {
            const variants = Object.fromEntries(this.userVariants);
            localStorage.setItem('ab_test_variants', JSON.stringify(variants));
        }
    }

    setupDefaultTests() {
        // Backup button color test
        this.createTest('backup_button_color', {
            name: 'Backup Button Color Test',
            description: 'Test different colors for the backup button',
            variants: {
                'blue': { weight: 0.5, config: { color: '#007bff', label: 'Blue Button' } },
                'green': { weight: 0.5, config: { color: '#28a745', label: 'Green Button' } }
            },
            goals: ['click', 'conversion'],
            active: true
        });

        // Progress bar style test
        this.createTest('progress_bar_style', {
            name: 'Progress Bar Style Test',
            description: 'Test different progress bar styles',
            variants: {
                'linear': { weight: 0.5, config: { style: 'linear', animation: 'smooth' } },
                'circular': { weight: 0.5, config: { style: 'circular', animation: 'pulse' } }
            },
            goals: ['engagement', 'completion'],
            active: true
        });

        // Dashboard layout test
        this.createTest('dashboard_layout', {
            name: 'Dashboard Layout Test',
            description: 'Test different dashboard layouts',
            variants: {
                'grid': { weight: 0.5, config: { layout: 'grid', columns: 3 } },
                'list': { weight: 0.5, config: { layout: 'list', density: 'compact' } }
            },
            goals: ['navigation', 'task_completion'],
            active: true
        });

        // Notification style test
        this.createTest('notification_style', {
            name: 'Notification Style Test',
            description: 'Test different notification styles',
            variants: {
                'toast': { weight: 0.5, config: { type: 'toast', position: 'top-right' } },
                'banner': { weight: 0.5, config: { type: 'banner', position: 'top' } }
            },
            goals: ['attention', 'action'],
            active: true
        });

        // Encryption demo complexity test
        this.createTest('encryption_demo_complexity', {
            name: 'Encryption Demo Complexity Test',
            description: 'Test different levels of encryption demo complexity',
            variants: {
                'simple': { weight: 0.5, config: { level: 'basic', steps: 3 } },
                'detailed': { weight: 0.5, config: { level: 'advanced', steps: 7 } }
            },
            goals: ['understanding', 'engagement'],
            active: true
        });
    }

    createTest(testName, testConfig) {
        const test = {
            name: testConfig.name,
            description: testConfig.description,
            variants: testConfig.variants,
            goals: testConfig.goals || [],
            active: testConfig.active !== false,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            ...testConfig
        };

        this.tests.set(testName, test);
        return test;
    }

    getVariant(testName) {
        // Return existing variant if user already assigned
        if (this.userVariants.has(testName)) {
            return this.userVariants.get(testName);
        }

        const test = this.tests.get(testName);
        if (!test || !test.active) {
            return null;
        }

        // Assign new variant based on weights
        const variant = this.assignVariant(testName, test.variants);
        this.userVariants.set(testName, variant);
        this.saveVariants();

        // Track assignment event
        this.trackEvent(testName, variant, 'assigned');

        return variant;
    }

    assignVariant(testName, variants) {
        const variantNames = Object.keys(variants);
        const weights = variantNames.map(name => variants[name].weight || 1);
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

        // Use deterministic assignment based on user ID for consistency
        const hash = this.hashString(this.userId + testName);
        const normalizedHash = hash / 0xffffffff; // Normalize to 0-1
        
        let cumulativeWeight = 0;
        for (let i = 0; i < variantNames.length; i++) {
            cumulativeWeight += weights[i] / totalWeight;
            if (normalizedHash <= cumulativeWeight) {
                return variantNames[i];
            }
        }

        // Fallback to first variant
        return variantNames[0];
    }

    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    getVariantConfig(testName) {
        const variant = this.getVariant(testName);
        if (!variant) return null;

        const test = this.tests.get(testName);
        return test?.variants[variant]?.config || null;
    }

    trackEvent(testName, variant, eventType, eventData = {}) {
        if (!this.config.trackingEnabled) return;

        const event = {
            testName,
            variant: variant || this.getVariant(testName),
            eventType,
            eventData,
            userId: this.userId,
            timestamp: Date.now(),
            page: window.location.pathname,
            url: window.location.href,
            userAgent: navigator.userAgent,
            sessionId: this.getSessionId()
        };

        this.events.push(event);

        // Report to performance monitor
        if (this.performanceMonitor) {
            this.performanceMonitor.recordABTestEvent(testName, event.variant, eventType, eventData);
        }

        // Cleanup old events
        if (this.events.length > this.config.maxEvents) {
            this.events = this.events.slice(-this.config.maxEvents);
        }

        console.log('A/B Test Event:', event);
    }

    getSessionId() {
        let sessionId = sessionStorage.getItem('ab_test_session_id');
        if (!sessionId) {
            sessionId = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            sessionStorage.setItem('ab_test_session_id', sessionId);
        }
        return sessionId;
    }

    // Convenience methods for common events
    trackClick(testName, element, additionalData = {}) {
        this.trackEvent(testName, null, 'click', {
            element: element.tagName,
            elementId: element.id,
            elementClass: element.className,
            ...additionalData
        });
    }

    trackConversion(testName, conversionType, value = null) {
        this.trackEvent(testName, null, 'conversion', {
            conversionType,
            value
        });
    }

    trackEngagement(testName, engagementType, duration = null) {
        this.trackEvent(testName, null, 'engagement', {
            engagementType,
            duration
        });
    }

    trackPageView(testName) {
        this.trackEvent(testName, null, 'page_view');
    }

    // Test management methods
    activateTest(testName) {
        const test = this.tests.get(testName);
        if (test) {
            test.active = true;
            test.updatedAt = Date.now();
        }
    }

    deactivateTest(testName) {
        const test = this.tests.get(testName);
        if (test) {
            test.active = false;
            test.updatedAt = Date.now();
        }
    }

    updateTestConfig(testName, config) {
        const test = this.tests.get(testName);
        if (test) {
            Object.assign(test, config);
            test.updatedAt = Date.now();
        }
    }

    // Analytics and reporting methods
    getTestResults(testName, timeRange = null) {
        let events = this.events.filter(e => e.testName === testName);
        
        if (timeRange) {
            const cutoff = Date.now() - timeRange;
            events = events.filter(e => e.timestamp > cutoff);
        }

        const variants = {};
        const test = this.tests.get(testName);

        if (!test) return null;

        // Initialize variant stats
        Object.keys(test.variants).forEach(variant => {
            variants[variant] = {
                assignments: 0,
                clicks: 0,
                conversions: 0,
                engagements: 0,
                pageViews: 0,
                events: []
            };
        });

        // Process events
        events.forEach(event => {
            if (variants[event.variant]) {
                variants[event.variant].events.push(event);
                
                switch (event.eventType) {
                    case 'assigned':
                        variants[event.variant].assignments++;
                        break;
                    case 'click':
                        variants[event.variant].clicks++;
                        break;
                    case 'conversion':
                        variants[event.variant].conversions++;
                        break;
                    case 'engagement':
                        variants[event.variant].engagements++;
                        break;
                    case 'page_view':
                        variants[event.variant].pageViews++;
                        break;
                }
            }
        });

        // Calculate conversion rates and other metrics
        Object.keys(variants).forEach(variant => {
            const stats = variants[variant];
            stats.conversionRate = stats.assignments > 0 ? stats.conversions / stats.assignments : 0;
            stats.clickRate = stats.assignments > 0 ? stats.clicks / stats.assignments : 0;
            stats.engagementRate = stats.assignments > 0 ? stats.engagements / stats.assignments : 0;
        });

        return {
            testName,
            test,
            variants,
            totalEvents: events.length,
            timeRange,
            generatedAt: Date.now()
        };
    }

    getAllTestResults(timeRange = null) {
        const results = {};
        
        this.tests.forEach((test, testName) => {
            results[testName] = this.getTestResults(testName, timeRange);
        });

        return results;
    }

    getWinningVariant(testName, metric = 'conversionRate') {
        const results = this.getTestResults(testName);
        if (!results) return null;

        let winner = null;
        let bestValue = -1;

        Object.entries(results.variants).forEach(([variant, stats]) => {
            if (stats[metric] > bestValue) {
                bestValue = stats[metric];
                winner = variant;
            }
        });

        return {
            variant: winner,
            value: bestValue,
            metric,
            confidence: this.calculateConfidence(results, winner, metric)
        };
    }

    calculateConfidence(results, winningVariant, metric) {
        // Simple confidence calculation (in a real implementation, you'd use proper statistical tests)
        const winnerStats = results.variants[winningVariant];
        const otherVariants = Object.entries(results.variants).filter(([v]) => v !== winningVariant);
        
        if (otherVariants.length === 0 || winnerStats.assignments < 30) {
            return 0; // Not enough data
        }

        let totalDifference = 0;
        let comparisons = 0;

        otherVariants.forEach(([variant, stats]) => {
            if (stats.assignments >= 30) {
                const difference = Math.abs(winnerStats[metric] - stats[metric]);
                totalDifference += difference;
                comparisons++;
            }
        });

        if (comparisons === 0) return 0;

        const avgDifference = totalDifference / comparisons;
        return Math.min(avgDifference * 100, 95); // Cap at 95% confidence
    }

    // Utility methods for UI integration
    applyVariant(testName, element, styleProperty, variantStyles) {
        const variant = this.getVariant(testName);
        if (variant && variantStyles[variant]) {
            if (typeof variantStyles[variant] === 'object') {
                Object.assign(element.style, variantStyles[variant]);
            } else {
                element.style[styleProperty] = variantStyles[variant];
            }
        }
    }

    getVariantClass(testName, classPrefix = '') {
        const variant = this.getVariant(testName);
        return variant ? `${classPrefix}${variant}` : '';
    }

    // Export methods
    exportTestData() {
        return {
            tests: Object.fromEntries(this.tests),
            userVariants: Object.fromEntries(this.userVariants),
            events: this.events,
            userId: this.userId,
            exportTime: Date.now()
        };
    }

    exportTestResults(testName = null) {
        if (testName) {
            return this.getTestResults(testName);
        } else {
            return this.getAllTestResults();
        }
    }

    // Reset methods
    resetUserVariants() {
        this.userVariants.clear();
        localStorage.removeItem('ab_test_variants');
        console.log('User A/B test variants reset');
    }

    clearEvents() {
        this.events = [];
        console.log('A/B test events cleared');
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ABTestingFramework;
}