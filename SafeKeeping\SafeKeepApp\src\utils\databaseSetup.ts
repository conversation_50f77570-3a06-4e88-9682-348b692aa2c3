// Database Setup Utility for SafeKeep
// This will automatically create all necessary tables and functions in Supabase

import { supabase } from '../config/supabase';

// Core tables SQL
const createCoreTablesSQL = `
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY DEFAULT auth.uid(),
  email TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  last_login_at TIMESTAMPTZ DEFAULT NOW(),
  storage_used BIGINT DEFAULT 0,
  storage_quota BIGINT DEFAULT 5368709120,
  encryption_key_id TEXT,
  subscription_tier TEXT DEFAULT 'basic' CHECK (subscription_tier IN ('basic', 'premium', 'family')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'expired', 'past_due')),
  subscription_expires_at TIMESTAMPTZ,
  stripe_customer_id TEXT UNIQUE,
  backup_settings JSONB DEFAULT '{"auto_backup": true, "wifi_only": true, "frequency": "daily"}'::jsonb,
  
  CONSTRAINT valid_storage_quota CHECK (storage_quota > 0),
  CONSTRAINT valid_storage_used CHECK (storage_used >= 0),
  CONSTRAINT storage_within_quota CHECK (storage_used <= storage_quota)
);

CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_subscription_tier ON public.users(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_users_stripe_customer_id ON public.users(stripe_customer_id);

-- Create file_metadata table
CREATE TABLE IF NOT EXISTS public.file_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  original_name TEXT NOT NULL,
  encrypted_name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT NOT NULL CHECK (size > 0),
  encrypted_size BIGINT NOT NULL CHECK (encrypted_size > 0),
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  last_modified TIMESTAMPTZ DEFAULT NOW(),
  category TEXT NOT NULL CHECK (category IN ('photo', 'contact', 'message')),
  hash TEXT NOT NULL,
  encryption_iv TEXT NOT NULL,
  encryption_salt TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  is_backed_up BOOLEAN DEFAULT true,
  device_id TEXT,
  sync_status TEXT DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'failed')),
  
  UNIQUE(user_id, hash)
);

CREATE INDEX IF NOT EXISTS idx_file_metadata_user_id ON public.file_metadata(user_id);
CREATE INDEX IF NOT EXISTS idx_file_metadata_category ON public.file_metadata(category);
CREATE INDEX IF NOT EXISTS idx_file_metadata_uploaded_at ON public.file_metadata(uploaded_at DESC);
CREATE INDEX IF NOT EXISTS idx_file_metadata_hash ON public.file_metadata(hash);
CREATE INDEX IF NOT EXISTS idx_file_metadata_sync_status ON public.file_metadata(sync_status);

-- Create backup_sessions table
CREATE TABLE IF NOT EXISTS public.backup_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  session_type TEXT NOT NULL CHECK (session_type IN ('manual', 'automatic')),
  status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  total_files INTEGER DEFAULT 0 CHECK (total_files >= 0),
  processed_files INTEGER DEFAULT 0 CHECK (processed_files >= 0),
  failed_files INTEGER DEFAULT 0 CHECK (failed_files >= 0),
  total_bytes BIGINT DEFAULT 0 CHECK (total_bytes >= 0),
  processed_bytes BIGINT DEFAULT 0 CHECK (processed_bytes >= 0),
  error_message TEXT,
  device_id TEXT,
  
  CONSTRAINT valid_processed_files CHECK (processed_files <= total_files),
  CONSTRAINT valid_failed_files CHECK (failed_files <= total_files),
  CONSTRAINT valid_processed_bytes CHECK (processed_bytes <= total_bytes),
  CONSTRAINT completed_sessions_have_end_time CHECK (
    (status IN ('completed', 'failed', 'cancelled') AND completed_at IS NOT NULL) OR
    (status = 'running' AND completed_at IS NULL)
  )
);

CREATE INDEX IF NOT EXISTS idx_backup_sessions_user_id ON public.backup_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_backup_sessions_status ON public.backup_sessions(status);
CREATE INDEX IF NOT EXISTS idx_backup_sessions_started_at ON public.backup_sessions(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_backup_sessions_type ON public.backup_sessions(session_type);

-- Create storage_usage table
CREATE TABLE IF NOT EXISTS public.storage_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  category TEXT NOT NULL CHECK (category IN ('photo', 'contact', 'message', 'other')),
  bytes_used BIGINT DEFAULT 0 CHECK (bytes_used >= 0),
  file_count INTEGER DEFAULT 0 CHECK (file_count >= 0),
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, category)
);

CREATE INDEX IF NOT EXISTS idx_storage_usage_user_id ON public.storage_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_storage_usage_category ON public.storage_usage(category);

-- Create sync_status table
CREATE TABLE IF NOT EXISTS public.sync_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  device_name TEXT,
  last_sync_at TIMESTAMPTZ DEFAULT NOW(),
  sync_version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT true,
  
  UNIQUE(user_id, device_id)
);

CREATE INDEX IF NOT EXISTS idx_sync_status_user_id ON public.sync_status(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_status_device_id ON public.sync_status(device_id);
CREATE INDEX IF NOT EXISTS idx_sync_status_active ON public.sync_status(is_active);
`;

// Database functions SQL
const createFunctionsSQL = `
-- Function to calculate and update storage usage for a user
CREATE OR REPLACE FUNCTION public.update_user_storage_usage(target_user_id UUID)
RETURNS void AS $$
DECLARE
  total_storage BIGINT := 0;
  category_record RECORD;
BEGIN
  SELECT COALESCE(SUM(encrypted_size), 0) INTO total_storage
  FROM public.file_metadata
  WHERE user_id = target_user_id AND is_backed_up = true;
  
  UPDATE public.users 
  SET storage_used = total_storage,
      last_login_at = CASE WHEN last_login_at < NOW() - INTERVAL '1 hour' THEN NOW() ELSE last_login_at END
  WHERE id = target_user_id;
  
  FOR category_record IN 
    SELECT 
      category,
      COALESCE(SUM(encrypted_size), 0) as bytes_used,
      COUNT(*) as file_count
    FROM public.file_metadata 
    WHERE user_id = target_user_id AND is_backed_up = true
    GROUP BY category
  LOOP
    INSERT INTO public.storage_usage (user_id, category, bytes_used, file_count, last_updated)
    VALUES (target_user_id, category_record.category, category_record.bytes_used, category_record.file_count, NOW())
    ON CONFLICT (user_id, category) 
    DO UPDATE SET 
      bytes_used = EXCLUDED.bytes_used,
      file_count = EXCLUDED.file_count,
      last_updated = NOW();
  END LOOP;
  
  INSERT INTO public.storage_usage (user_id, category, bytes_used, file_count, last_updated)
  SELECT target_user_id, category, 0, 0, NOW()
  FROM (VALUES ('photo'), ('contact'), ('message'), ('other')) AS categories(category)
  ON CONFLICT (user_id, category) DO NOTHING;
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.users (id, email, display_name, created_at, last_login_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email),
    NOW(),
    NOW()
  );
  
  PERFORM public.update_user_storage_usage(NEW.id);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RPC function for client access
CREATE OR REPLACE FUNCTION public.refresh_storage_usage(user_id UUID)
RETURNS void AS $$
BEGIN
  PERFORM public.update_user_storage_usage(user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
`;

// RLS policies SQL
const createRlsPoliciesSQL = `
-- Enable Row-Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storage_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sync_status ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
-- Users can only view and update their own profile
CREATE POLICY "Users can view own profile" 
  ON public.users 
  FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
  ON public.users 
  FOR UPDATE 
  USING (auth.uid() = id);

-- Create policies for file_metadata table
-- Users can only access their own files
CREATE POLICY "Users can select own files" 
  ON public.file_metadata 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own files" 
  ON public.file_metadata 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own files" 
  ON public.file_metadata 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own files" 
  ON public.file_metadata 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Create policies for backup_sessions table
-- Users can only access their own backup sessions
CREATE POLICY "Users can select own backup sessions" 
  ON public.backup_sessions 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own backup sessions" 
  ON public.backup_sessions 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own backup sessions" 
  ON public.backup_sessions 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own backup sessions" 
  ON public.backup_sessions 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Create policies for storage_usage table
-- Users can only view their own storage usage
CREATE POLICY "Users can select own storage usage" 
  ON public.storage_usage 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Storage usage is updated by triggers, not directly by users
CREATE POLICY "System can update storage usage" 
  ON public.storage_usage 
  FOR UPDATE 
  USING (true);

CREATE POLICY "System can insert storage usage" 
  ON public.storage_usage 
  FOR INSERT 
  WITH CHECK (true);

-- Create policies for sync_status table
-- Users can only access their own sync status
CREATE POLICY "Users can select own sync status" 
  ON public.sync_status 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sync status" 
  ON public.sync_status 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sync status" 
  ON public.sync_status 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own sync status" 
  ON public.sync_status 
  FOR DELETE 
  USING (auth.uid() = user_id);
`;

// Setup function
export const setupDatabase = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    console.log('🚀 Starting database setup...');

    // Step 1: Create core tables
    console.log('📋 Creating core tables...');
    const { error: tablesError } = await supabase.rpc('exec_sql', { 
      sql: createCoreTablesSQL 
    });

    if (tablesError) {
      // Try alternative method - direct SQL execution
      const { error: directError } = await supabase
        .from('_supabase_migrations')
        .select('*')
        .limit(1);
      
      if (directError && !directError.message.includes('does not exist')) {
        throw new Error(`Failed to create tables: ${tablesError.message}`);
      }
    }

    // Step 2: Create functions
    console.log('⚙️ Creating database functions...');
    const { error: functionsError } = await supabase.rpc('exec_sql', { 
      sql: createFunctionsSQL 
    });

    if (functionsError) {
      console.warn('Functions creation may have failed:', functionsError);
    }
    
    // Step 3: Create RLS policies
    console.log('🔒 Setting up Row-Level Security policies...');
    const { error: rlsError } = await supabase.rpc('exec_sql', { 
      sql: createRlsPoliciesSQL 
    });

    if (rlsError) {
      console.warn('RLS policy creation may have failed:', rlsError);
    }

    // Step 3: Verify setup
    console.log('✅ Verifying database setup...');
    const { data: tables, error: verifyError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['users', 'file_metadata', 'backup_sessions', 'storage_usage', 'sync_status']);

    if (verifyError) {
      console.warn('Verification failed, but setup may still be successful');
    }

    console.log('🎉 Database setup completed successfully!');
    
    return {
      success: true,
      message: 'Database setup completed successfully',
      details: {
        tablesCreated: tables?.length || 'Unknown',
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    return {
      success: false,
      message: 'Database setup failed',
      details: error
    };
  }
};

// Auto-setup in development (optional)
export const autoSetupIfNeeded = async (): Promise<void> => {
  if (!__DEV__) return;

  try {
    // Check if tables exist
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error && error.message.includes('does not exist')) {
      console.log('🔧 Tables not found, running auto-setup...');
      const result = await setupDatabase();
      
      if (result.success) {
        console.log('✅ Auto-setup completed successfully!');
      } else {
        console.error('❌ Auto-setup failed:', result.message);
      }
    } else {
      console.log('✅ Database tables already exist');
    }
  } catch (error) {
    console.log('🔧 Database setup check failed, may need manual setup');
  }
};