/**
 * Accessibility Manager for SafeKeep Web Demo
 * Handles ARIA labels, keyboard navigation, screen reader support, and WCAG compliance
 */

class AccessibilityManager {
    constructor() {
        this.announcements = [];
        this.focusHistory = [];
        this.keyboardTrapStack = [];
        
        this.init();
    }
    
    init() {
        this.createAriaLiveRegions();
        this.setupKeyboardNavigation();
        this.setupFocusManagement();
        this.enhanceFormAccessibility();
        this.setupProgressAnnouncements();
        this.addAriaLabelsToComponents();
        this.setupReducedMotionSupport();
        this.createAccessibilityToolbar();
        
        // Monitor for dynamic content changes
        this.setupMutationObserver();
    }
    
    createAriaLiveRegions() {
        // Polite announcements (non-interrupting)
        this.politeRegion = this.createLiveRegion('polite-announcements', 'polite');
        
        // Assertive announcements (interrupting)
        this.assertiveRegion = this.createLiveRegion('assertive-announcements', 'assertive');
        
        // Status updates
        this.statusRegion = this.createLiveRegion('status-announcements', 'polite');
        this.statusRegion.setAttribute('role', 'status');
    }
    
    createLiveRegion(id, politeness) {
        let region = document.getElementById(id);
        if (!region) {
            region = document.createElement('div');
            region.id = id;
            region.setAttribute('aria-live', politeness);
            region.setAttribute('aria-atomic', 'true');
            region.className = 'sr-only';
            document.body.appendChild(region);
        }
        return region;
    }
    
    announce(message, type = 'polite', delay = 0) {
        setTimeout(() => {
            const region = type === 'assertive' ? this.assertiveRegion : this.politeRegion;
            
            // Clear previous message
            region.textContent = '';
            
            // Add new message after a brief delay to ensure screen readers pick it up
            setTimeout(() => {
                region.textContent = message;
                
                // Clear after announcement
                setTimeout(() => {
                    region.textContent = '';
                }, 1000);
            }, 100);
            
            // Keep history of announcements
            this.announcements.push({
                message,
                type,
                timestamp: new Date()
            });
            
            // Limit history size
            if (this.announcements.length > 50) {
                this.announcements.shift();
            }
        }, delay);
    }
    
    announceStatus(message) {
        this.statusRegion.textContent = '';
        setTimeout(() => {
            this.statusRegion.textContent = message;
        }, 100);
    }
    
    setupKeyboardNavigation() {
        // Global keyboard event handler
        document.addEventListener('keydown', (e) => {
            this.handleGlobalKeydown(e);
        });
        
        // Escape key handler for modals and overlays
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.handleEscapeKey(e);
            }
        });
        
        // Tab trapping for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.handleTabKey(e);
            }
        });
    }
    
    handleGlobalKeydown(e) {
        // Skip to main content (Alt + M)
        if (e.altKey && e.key === 'm') {
            e.preventDefault();
            this.skipToMainContent();
        }
        
        // Skip to navigation (Alt + N)
        if (e.altKey && e.key === 'n') {
            e.preventDefault();
            this.skipToNavigation();
        }
        
        // Open accessibility menu (Alt + A)
        if (e.altKey && e.key === 'a') {
            e.preventDefault();
            this.openAccessibilityMenu();
        }
        
        // Announce current focus (Alt + F)
        if (e.altKey && e.key === 'f') {
            e.preventDefault();
            this.announceCurrentFocus();
        }
    }
    
    handleEscapeKey(e) {
        // Close any open modals or overlays
        const modal = document.querySelector('.modal.show, [role="dialog"][aria-hidden="false"]');
        if (modal) {
            this.closeModal(modal);
            return;
        }
        
        // Close expanded navigation
        const expandedNav = document.querySelector('.mobile-nav.nav-expanded');
        if (expandedNav) {
            const toggle = document.querySelector('.mobile-nav-toggle');
            if (toggle) {
                toggle.click();
            }
            return;
        }
        
        // Close any dropdowns
        const openDropdown = document.querySelector('.dropdown.show');
        if (openDropdown) {
            openDropdown.classList.remove('show');
            return;
        }
    }
    
    handleTabKey(e) {
        // Handle tab trapping in modals
        if (this.keyboardTrapStack.length > 0) {
            const currentTrap = this.keyboardTrapStack[this.keyboardTrapStack.length - 1];
            this.trapFocus(e, currentTrap);
        }
    }
    
    trapFocus(e, container) {
        const focusableElements = this.getFocusableElements(container);
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            // Tab
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }
    
    getFocusableElements(container) {
        const selector = [
            'a[href]',
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable="true"]'
        ].join(', ');
        
        return Array.from(container.querySelectorAll(selector))
            .filter(el => !el.hasAttribute('hidden') && el.offsetParent !== null);
    }
    
    setupFocusManagement() {
        // Track focus changes
        document.addEventListener('focusin', (e) => {
            this.focusHistory.push({
                element: e.target,
                timestamp: new Date()
            });
            
            // Limit history size
            if (this.focusHistory.length > 20) {
                this.focusHistory.shift();
            }
        });
        
        // Enhance focus visibility
        document.addEventListener('focusin', (e) => {
            e.target.classList.add('has-focus');
        });
        
        document.addEventListener('focusout', (e) => {
            e.target.classList.remove('has-focus');
        });
    }
    
    enhanceFormAccessibility() {
        // Add proper labels and descriptions to form controls
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            this.enhanceForm(form);
        });
        
        // Monitor for new forms
        this.observeNewForms();
    }
    
    enhanceForm(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            this.enhanceFormControl(input);
        });
        
        // Add form submission feedback
        form.addEventListener('submit', (e) => {
            this.announce('Form submitted', 'polite');
        });
        
        // Add validation feedback
        form.addEventListener('invalid', (e) => {
            const field = e.target;
            const label = this.getFieldLabel(field);
            this.announce(`${label} is invalid: ${field.validationMessage}`, 'assertive');
        }, true);
    }
    
    enhanceFormControl(input) {
        // Ensure proper labeling
        if (!input.hasAttribute('aria-label') && !input.hasAttribute('aria-labelledby')) {
            const label = this.findLabelForInput(input);
            if (label) {
                const labelId = label.id || this.generateId('label');
                label.id = labelId;
                input.setAttribute('aria-labelledby', labelId);
            }
        }
        
        // Add descriptions for validation
        const description = this.createFieldDescription(input);
        if (description) {
            input.setAttribute('aria-describedby', description.id);
        }
        
        // Add required indicator
        if (input.hasAttribute('required')) {
            input.setAttribute('aria-required', 'true');
        }
        
        // Add input type announcements
        input.addEventListener('focus', () => {
            this.announceInputType(input);
        });
    }
    
    findLabelForInput(input) {
        // Look for explicit label
        if (input.id) {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (label) return label;
        }
        
        // Look for implicit label (input inside label)
        const parentLabel = input.closest('label');
        if (parentLabel) return parentLabel;
        
        // Look for nearby label
        const prevLabel = input.previousElementSibling;
        if (prevLabel && prevLabel.tagName === 'LABEL') return prevLabel;
        
        return null;
    }
    
    createFieldDescription(input) {
        const existingDesc = input.getAttribute('aria-describedby');
        if (existingDesc) return document.getElementById(existingDesc);
        
        const description = document.createElement('div');
        description.id = this.generateId('field-desc');
        description.className = 'sr-only field-description';
        
        let descText = '';
        
        if (input.type === 'password') {
            descText = 'Password field. Your input will be hidden.';
        } else if (input.type === 'email') {
            descText = 'Email field. Enter a valid email address.';
        } else if (input.type === 'tel') {
            descText = 'Phone number field.';
        } else if (input.type === 'url') {
            descText = 'URL field. Enter a valid web address.';
        }
        
        if (input.hasAttribute('required')) {
            descText += ' This field is required.';
        }
        
        if (input.hasAttribute('pattern')) {
            descText += ' Input must match the required format.';
        }
        
        if (descText) {
            description.textContent = descText;
            input.parentNode.insertBefore(description, input.nextSibling);
            return description;
        }
        
        return null;
    }
    
    announceInputType(input) {
        const label = this.getFieldLabel(input);
        let announcement = label;
        
        if (input.type === 'password') {
            announcement += ', password field';
        } else if (input.type === 'search') {
            announcement += ', search field';
        } else if (input.tagName === 'SELECT') {
            announcement += ', dropdown menu';
        } else if (input.tagName === 'TEXTAREA') {
            announcement += ', text area';
        }
        
        if (input.hasAttribute('required')) {
            announcement += ', required';
        }
        
        // Don't announce every time, only on first focus
        if (!input.hasAttribute('data-announced')) {
            this.announce(announcement, 'polite');
            input.setAttribute('data-announced', 'true');
        }
    }
    
    getFieldLabel(field) {
        const label = this.findLabelForInput(field);
        if (label) return label.textContent.trim();
        
        if (field.hasAttribute('aria-label')) {
            return field.getAttribute('aria-label');
        }
        
        if (field.hasAttribute('placeholder')) {
            return field.getAttribute('placeholder');
        }
        
        return field.name || 'Input field';
    }
    
    setupProgressAnnouncements() {
        // Monitor progress bars and announce changes
        const progressBars = document.querySelectorAll('.progress-bar, [role="progressbar"]');
        
        progressBars.forEach(bar => {
            this.enhanceProgressBar(bar);
        });
    }
    
    enhanceProgressBar(progressBar) {
        // Ensure proper ARIA attributes
        if (!progressBar.hasAttribute('role')) {
            progressBar.setAttribute('role', 'progressbar');
        }
        
        // Set up value monitoring
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'attributes' && 
                    (mutation.attributeName === 'aria-valuenow' || 
                     mutation.attributeName === 'style')) {
                    this.announceProgressChange(progressBar);
                }
            });
        });
        
        observer.observe(progressBar, {
            attributes: true,
            attributeFilter: ['aria-valuenow', 'style']
        });
        
        // Store observer for cleanup
        progressBar._accessibilityObserver = observer;
    }
    
    announceProgressChange(progressBar) {
        const value = progressBar.getAttribute('aria-valuenow') || 
                     this.extractProgressFromStyle(progressBar);
        const label = progressBar.getAttribute('aria-label') || 'Progress';
        
        if (value !== null) {
            // Throttle announcements to avoid spam
            const now = Date.now();
            const lastAnnouncement = progressBar._lastProgressAnnouncement || 0;
            
            if (now - lastAnnouncement > 2000) { // 2 second throttle
                this.announce(`${label}: ${value}%`, 'polite');
                progressBar._lastProgressAnnouncement = now;
            }
        }
    }
    
    extractProgressFromStyle(progressBar) {
        const style = progressBar.style.width || progressBar.querySelector('.progress-fill')?.style.width;
        if (style && style.includes('%')) {
            return parseInt(style);
        }
        return null;
    }
    
    addAriaLabelsToComponents() {
        // Add ARIA labels to common components
        this.labelButtons();
        this.labelCards();
        this.labelSections();
        this.labelTables();
        this.labelCharts();
    }
    
    labelButtons() {
        const buttons = document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])');
        
        buttons.forEach(button => {
            if (!button.textContent.trim()) {
                // Button with no text content needs a label
                const icon = button.querySelector('svg, i, .icon');
                if (icon) {
                    button.setAttribute('aria-label', this.guessButtonPurpose(button));
                }
            }
        });
    }
    
    guessButtonPurpose(button) {
        const className = button.className.toLowerCase();
        const id = button.id.toLowerCase();
        
        if (className.includes('close') || className.includes('dismiss')) return 'Close';
        if (className.includes('menu') || className.includes('hamburger')) return 'Menu';
        if (className.includes('search')) return 'Search';
        if (className.includes('submit')) return 'Submit';
        if (className.includes('cancel')) return 'Cancel';
        if (className.includes('edit')) return 'Edit';
        if (className.includes('delete')) return 'Delete';
        if (className.includes('save')) return 'Save';
        if (className.includes('play')) return 'Play';
        if (className.includes('pause')) return 'Pause';
        if (className.includes('stop')) return 'Stop';
        
        if (id.includes('theme')) return 'Toggle theme';
        
        return 'Button';
    }
    
    labelCards() {
        const cards = document.querySelectorAll('.card, .demo-card');
        
        cards.forEach(card => {
            const title = card.querySelector('h1, h2, h3, h4, h5, h6, .card-title');
            if (title && !card.hasAttribute('aria-labelledby')) {
                const titleId = title.id || this.generateId('card-title');
                title.id = titleId;
                card.setAttribute('aria-labelledby', titleId);
            }
        });
    }
    
    labelSections() {
        const sections = document.querySelectorAll('section, .demo-section');
        
        sections.forEach(section => {
            const heading = section.querySelector('h1, h2, h3, h4, h5, h6');
            if (heading && !section.hasAttribute('aria-labelledby')) {
                const headingId = heading.id || this.generateId('section-heading');
                heading.id = headingId;
                section.setAttribute('aria-labelledby', headingId);
            }
        });
    }
    
    labelTables() {
        const tables = document.querySelectorAll('table');
        
        tables.forEach(table => {
            if (!table.hasAttribute('aria-label') && !table.hasAttribute('aria-labelledby')) {
                const caption = table.querySelector('caption');
                const prevHeading = table.previousElementSibling;
                
                if (caption) {
                    const captionId = caption.id || this.generateId('table-caption');
                    caption.id = captionId;
                    table.setAttribute('aria-labelledby', captionId);
                } else if (prevHeading && /^H[1-6]$/.test(prevHeading.tagName)) {
                    const headingId = prevHeading.id || this.generateId('table-heading');
                    prevHeading.id = headingId;
                    table.setAttribute('aria-labelledby', headingId);
                } else {
                    table.setAttribute('aria-label', 'Data table');
                }
            }
        });
    }
    
    labelCharts() {
        const charts = document.querySelectorAll('canvas[role="img"], .chart-container');
        
        charts.forEach(chart => {
            if (!chart.hasAttribute('aria-label')) {
                chart.setAttribute('aria-label', 'Chart visualization');
                chart.setAttribute('role', 'img');
            }
        });
    }
    
    setupReducedMotionSupport() {
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        
        const handleReducedMotion = (e) => {
            if (e.matches) {
                document.body.classList.add('reduced-motion');
                this.announce('Reduced motion mode enabled', 'polite');
            } else {
                document.body.classList.remove('reduced-motion');
            }
        };
        
        handleReducedMotion(mediaQuery);
        mediaQuery.addEventListener('change', handleReducedMotion);
    }
    
    createAccessibilityToolbar() {
        const toolbar = document.createElement('div');
        toolbar.id = 'accessibility-toolbar';
        toolbar.className = 'accessibility-toolbar';
        toolbar.setAttribute('role', 'toolbar');
        toolbar.setAttribute('aria-label', 'Accessibility tools');
        
        toolbar.innerHTML = `
            <button class="btn btn-sm" id="increase-font-size" aria-label="Increase font size">A+</button>
            <button class="btn btn-sm" id="decrease-font-size" aria-label="Decrease font size">A-</button>
            <button class="btn btn-sm" id="toggle-high-contrast" aria-label="Toggle high contrast">◐</button>
            <button class="btn btn-sm" id="toggle-focus-indicators" aria-label="Toggle focus indicators">⊙</button>
            <button class="btn btn-sm" id="read-page" aria-label="Read page aloud">🔊</button>
        `;
        
        // Position toolbar
        toolbar.style.cssText = `
            position: fixed;
            top: 60px;
            right: 20px;
            z-index: 1000;
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            display: none;
            gap: var(--spacing-xs);
            box-shadow: var(--shadow-lg);
        `;
        
        document.body.appendChild(toolbar);
        
        // Add event listeners
        this.setupAccessibilityToolbarEvents(toolbar);
        
        this.accessibilityToolbar = toolbar;
    }
    
    setupAccessibilityToolbarEvents(toolbar) {
        toolbar.querySelector('#increase-font-size').addEventListener('click', () => {
            this.adjustFontSize(1.1);
        });
        
        toolbar.querySelector('#decrease-font-size').addEventListener('click', () => {
            this.adjustFontSize(0.9);
        });
        
        toolbar.querySelector('#toggle-high-contrast').addEventListener('click', () => {
            this.toggleHighContrast();
        });
        
        toolbar.querySelector('#toggle-focus-indicators').addEventListener('click', () => {
            this.toggleFocusIndicators();
        });
        
        toolbar.querySelector('#read-page').addEventListener('click', () => {
            this.readPageAloud();
        });
    }
    
    adjustFontSize(multiplier) {
        const currentSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
        const newSize = Math.max(12, Math.min(24, currentSize * multiplier));
        document.documentElement.style.fontSize = `${newSize}px`;
        
        this.announce(`Font size ${multiplier > 1 ? 'increased' : 'decreased'} to ${newSize} pixels`, 'polite');
    }
    
    toggleHighContrast() {
        const isHighContrast = document.documentElement.getAttribute('data-theme') === 'high-contrast';
        
        if (isHighContrast) {
            document.documentElement.removeAttribute('data-theme');
            this.announce('High contrast mode disabled', 'polite');
        } else {
            document.documentElement.setAttribute('data-theme', 'high-contrast');
            this.announce('High contrast mode enabled', 'polite');
        }
    }
    
    toggleFocusIndicators() {
        const hasEnhanced = document.body.classList.contains('enhanced-focus');
        
        if (hasEnhanced) {
            document.body.classList.remove('enhanced-focus');
            this.announce('Enhanced focus indicators disabled', 'polite');
        } else {
            document.body.classList.add('enhanced-focus');
            this.announce('Enhanced focus indicators enabled', 'polite');
        }
    }
    
    readPageAloud() {
        if ('speechSynthesis' in window) {
            const text = this.extractReadableText();
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = 0.8;
            utterance.pitch = 1;
            
            speechSynthesis.speak(utterance);
            this.announce('Reading page aloud', 'polite');
        } else {
            this.announce('Speech synthesis not supported in this browser', 'assertive');
        }
    }
    
    extractReadableText() {
        const main = document.querySelector('main, .main-content, .container');
        if (!main) return document.body.textContent;
        
        // Remove non-essential elements
        const clone = main.cloneNode(true);
        const elementsToRemove = clone.querySelectorAll('script, style, .sr-only, [aria-hidden="true"]');
        elementsToRemove.forEach(el => el.remove());
        
        return clone.textContent.replace(/\s+/g, ' ').trim();
    }
    
    setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.enhanceNewElement(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        this.mutationObserver = observer;
    }
    
    enhanceNewElement(element) {
        // Enhance forms
        if (element.tagName === 'FORM' || element.querySelector('form')) {
            const forms = element.tagName === 'FORM' ? [element] : element.querySelectorAll('form');
            forms.forEach(form => this.enhanceForm(form));
        }
        
        // Enhance buttons
        const buttons = element.querySelectorAll ? element.querySelectorAll('button') : [];
        buttons.forEach(button => {
            if (!button.hasAttribute('aria-label') && !button.textContent.trim()) {
                button.setAttribute('aria-label', this.guessButtonPurpose(button));
            }
        });
        
        // Enhance progress bars
        const progressBars = element.querySelectorAll ? element.querySelectorAll('.progress-bar, [role="progressbar"]') : [];
        progressBars.forEach(bar => this.enhanceProgressBar(bar));
    }
    
    // Utility methods
    skipToMainContent() {
        const main = document.querySelector('#main-content, main, .main-content');
        if (main) {
            main.focus();
            main.scrollIntoView();
            this.announce('Skipped to main content', 'polite');
        }
    }
    
    skipToNavigation() {
        const nav = document.querySelector('nav, .navigation, .main-nav');
        if (nav) {
            const firstLink = nav.querySelector('a, button');
            if (firstLink) {
                firstLink.focus();
                this.announce('Skipped to navigation', 'polite');
            }
        }
    }
    
    openAccessibilityMenu() {
        if (this.accessibilityToolbar) {
            const isVisible = this.accessibilityToolbar.style.display !== 'none';
            this.accessibilityToolbar.style.display = isVisible ? 'none' : 'flex';
            
            if (!isVisible) {
                this.accessibilityToolbar.querySelector('button').focus();
                this.announce('Accessibility toolbar opened', 'polite');
            }
        }
    }
    
    announceCurrentFocus() {
        const focused = document.activeElement;
        if (focused && focused !== document.body) {
            const label = this.getElementLabel(focused);
            this.announce(`Currently focused on: ${label}`, 'assertive');
        } else {
            this.announce('No element currently focused', 'assertive');
        }
    }
    
    getElementLabel(element) {
        if (element.hasAttribute('aria-label')) {
            return element.getAttribute('aria-label');
        }
        
        if (element.hasAttribute('aria-labelledby')) {
            const labelElement = document.getElementById(element.getAttribute('aria-labelledby'));
            if (labelElement) return labelElement.textContent.trim();
        }
        
        if (element.textContent.trim()) {
            return element.textContent.trim();
        }
        
        return element.tagName.toLowerCase();
    }
    
    generateId(prefix = 'a11y') {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // Cleanup method
    destroy() {
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }
        
        // Clean up progress bar observers
        document.querySelectorAll('[data-accessibility-observer]').forEach(el => {
            if (el._accessibilityObserver) {
                el._accessibilityObserver.disconnect();
            }
        });
    }
}

// Add accessibility-specific CSS
const accessibilityStyles = `
<style>
/* Enhanced focus indicators */
.enhanced-focus *:focus {
    outline: 4px solid #ffbf47 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 6px rgba(255, 191, 71, 0.3) !important;
}

/* High contrast theme enhancements */
[data-theme="high-contrast"] {
    --primary-color: #0000ff !important;
    --bg-primary: #ffffff !important;
    --bg-secondary: #ffffff !important;
    --text-primary: #000000 !important;
    --text-secondary: #000000 !important;
    --border-light: #000000 !important;
}

[data-theme="high-contrast"] .btn {
    border: 2px solid #000000 !important;
    background: #ffffff !important;
    color: #000000 !important;
}

[data-theme="high-contrast"] .btn:hover {
    background: #000000 !important;
    color: #ffffff !important;
}

/* Reduced motion support */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
}

/* Screen reader only content */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Focus management */
.has-focus {
    position: relative;
}

.has-focus::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid var(--primary-color);
    border-radius: inherit;
    pointer-events: none;
}

/* Accessibility toolbar */
.accessibility-toolbar {
    font-size: 14px;
}

.accessibility-toolbar button {
    min-width: 32px;
    min-height: 32px;
}

/* Skip link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Form accessibility enhancements */
.form-control:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control:valid {
    border-color: #28a745;
}

/* Progress bar accessibility */
[role="progressbar"] {
    position: relative;
}

[role="progressbar"]::after {
    content: attr(aria-valuenow) '%';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
}

/* Table accessibility */
table caption {
    caption-side: top;
    text-align: left;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

th[scope="col"] {
    background-color: var(--bg-secondary);
}

th[scope="row"] {
    background-color: var(--bg-tertiary);
}

/* Button accessibility */
button:disabled,
.btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* Link accessibility */
a:not([class]) {
    text-decoration: underline;
}

a:not([class]):hover,
a:not([class]):focus {
    text-decoration: none;
}

/* Error message accessibility */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

/* Loading state accessibility */
[aria-busy="true"] {
    cursor: wait;
}

[aria-busy="true"]::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Modal accessibility */
[role="dialog"] {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    max-height: 90vh;
    overflow-y: auto;
}

[role="dialog"][aria-hidden="true"] {
    display: none;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
}
</style>
`;

// Inject accessibility styles
document.head.insertAdjacentHTML('beforeend', accessibilityStyles);

// Initialize accessibility manager when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.accessibilityManager = new AccessibilityManager();
    });
} else {
    window.accessibilityManager = new AccessibilityManager();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AccessibilityManager;
}