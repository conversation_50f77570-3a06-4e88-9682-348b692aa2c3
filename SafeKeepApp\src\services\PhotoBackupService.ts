import { Platform, PermissionsAndroid } from 'react-native';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import CryptoJS from 'crypto-js';
import CloudStorageService from './CloudStorageService';
import ChunkedUploadService from './ChunkedUploadService';
import PerformanceMonitoringService from './PerformanceMonitoringService';
import BackupPerformanceLogger from './BackupPerformanceLogger';
import AuthService from './AuthService';
import EncryptionService from './EncryptionService';
import { BackupServiceResult, BackupError, VIDEO_MIME_TYPES, VIDEO_FILE_EXTENSIONS } from '../types/backup';
import type { MediaFile, FilteredMediaResult } from '../types/backup';

export interface PhotoAsset {
  id?: string;
  uri: string;
  filename: string;
  timestamp: number;
  type: string;
  fileSize: number;
  width: number;
  height: number;
  hash?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
}

export interface BackupProgress {
  totalPhotos: number;
  processedPhotos: number;
  currentPhoto: string;
  bytesUploaded: number;
  totalBytes: number;
  percentage: number;
  status: 'scanning' | 'processing' | 'uploading' | 'completed' | 'error' | 'paused';
  error?: string;
}

export interface BackupResult {
  success: boolean;
  totalPhotos: number;
  backedUpPhotos: number;
  skippedPhotos: number;
  duplicatesFound: number;
  errors: string[];
  duration: number;
}

class PhotoBackupService {
  private isBackupRunning = false;
  private shouldPauseBackup = false;
  private progressCallback?: (progress: BackupProgress) => void;
  private duplicateHashes = new Set<string>();
  private readonly CHUNK_SIZE = 1024 * 1024; // 1MB chunks for upload

  // Request photo library permissions
  async requestPhotoPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          {
            title: '📸 Photo Library Access Permission',
            message: 'SafeKeep needs access to your photo library to back up your photos safely.\n\nThis helps you:\n• Never lose precious memories\n• Restore photos if you get a new phone\n• Keep family photos safe and secure',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'Allow',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        // iOS permission is handled by react-native-camera-roll
        return true;
      }
    } catch (error) {
      console.error('Error requesting photo permission:', error);
      return false;
    }
  }

  // Check if file is a video based on MIME type and extension
  private isVideoFile(filename: string, mimeType: string): boolean {
    // Check MIME type first
    if (VIDEO_MIME_TYPES.includes(mimeType as any)) {
      return true;
    }

    // Check file extension as fallback
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    return VIDEO_FILE_EXTENSIONS.includes(extension as any);
  }

  // Scan device photo library with video exclusion
  async scanPhotoLibrary(limit: number = 1000): Promise<FilteredMediaResult> {
    try {
      console.log('📷 Starting photo library scan with video exclusion...');

      // Check permission first
      const hasPermission = await this.requestPhotoPermission();
      if (!hasPermission) {
        throw new Error('Photo library permission denied');
      }

      // Get all media (photos and videos)
      const allMedia = await CameraRoll.getPhotos({
        first: limit,
        assetType: 'All', // Get both photos and videos to filter
        include: ['filename', 'fileSize', 'imageSize', 'location'],
      });

      const photos: MediaFile[] = [];
      const excludedVideos: MediaFile[] = [];

      allMedia.edges.forEach(edge => {
        const mediaFile: MediaFile = {
          uri: edge.node.image.uri,
          filename: edge.node.image.filename || `media_${Date.now()}`,
          type: edge.node.type,
          fileSize: edge.node.image.fileSize || 0,
          timestamp: new Date(edge.node.timestamp).getTime(),
        };

        // Filter out videos
        if (this.isVideoFile(mediaFile.filename, mediaFile.type)) {
          excludedVideos.push(mediaFile);
          console.log(`🎥 Excluding video: ${mediaFile.filename}`);
        } else {
          photos.push(mediaFile);
        }
      });

      const result: FilteredMediaResult = {
        photos: photos,
        excludedVideos: excludedVideos,
        totalScanned: allMedia.edges.length
      };

      console.log(`📷 Scan complete: ${photos.length} photos found, ${excludedVideos.length} videos excluded`);
      return result;
    } catch (error) {
      console.error('Error scanning photo library:', error);
      throw new Error('Failed to scan photo library. Please check permissions.');
    }
  }

  // Convert MediaFile to PhotoAsset for backward compatibility
  private convertToPhotoAssets(mediaFiles: MediaFile[]): PhotoAsset[] {
    return mediaFiles.map(media => ({
      uri: media.uri,
      filename: media.filename,
      timestamp: media.timestamp,
      type: media.type,
      fileSize: media.fileSize,
      width: 0, // Will be populated if needed
      height: 0, // Will be populated if needed
    }));
  }

  // Generate hash for duplicate detection
  async generatePhotoHash(photoUri: string): Promise<string> {
    try {
      // For React Native, we'll use file stats and a portion of the file for hashing
      const fileInfo = await RNFS.stat(photoUri);
      const fileSize = fileInfo.size;
      const modificationTime = fileInfo.mtime;

      // Read first 1KB of file for content-based hashing
      const sampleData = await RNFS.read(photoUri, 1024, 0, 'base64');

      // Create hash from file size, modification time, and sample content
      const hashInput = `${fileSize}_${modificationTime}_${sampleData}`;
      const hash = CryptoJS.SHA256(hashInput).toString();

      return hash;
    } catch (error) {
      console.error('Error generating photo hash:', error);
      // Fallback to filename + size based hash
      const fallbackHash = CryptoJS.SHA256(`${photoUri}_${Date.now()}`).toString();
      return fallbackHash;
    }
  }

  // Detect duplicate photos
  async detectDuplicates(photos: PhotoAsset[]): Promise<PhotoAsset[]> {
    console.log('🔍 Starting duplicate detection...');
    const uniquePhotos: PhotoAsset[] = [];
    const duplicateHashes = new Set<string>();

    for (let i = 0; i < photos.length; i++) {
      const photo = photos[i];

      try {
        const hash = await this.generatePhotoHash(photo.uri);
        photo.hash = hash;

        if (!duplicateHashes.has(hash)) {
          duplicateHashes.add(hash);
          uniquePhotos.push(photo);
        } else {
          console.log(`📷 Duplicate found: ${photo.filename}`);
        }

        // Update progress for duplicate detection
        if (this.progressCallback) {
          this.progressCallback({
            totalPhotos: photos.length,
            processedPhotos: i + 1,
            currentPhoto: photo.filename,
            bytesUploaded: 0,
            totalBytes: 0,
            percentage: Math.round(((i + 1) / photos.length) * 100),
            status: 'processing'
          });
        }
      } catch (error) {
        console.error(`Error processing photo ${photo.filename}:`, error);
        // Include photo anyway if hash generation fails
        uniquePhotos.push(photo);
      }
    }

    this.duplicateHashes = duplicateHashes;
    console.log(`🔍 Duplicate detection complete. ${uniquePhotos.length} unique photos found.`);
    return uniquePhotos;
  }

  // Upload photo using CloudStorageService
  async uploadPhoto(photo: PhotoAsset): Promise<boolean> {
    try {
      if (this.shouldPauseBackup) {
        throw new Error('Backup paused by user');
      }

      console.log(`📤 Starting upload for ${photo.filename}`);

      // Read file data
      const fileData = await RNFS.readFile(photo.uri, 'base64');

      // Upload to cloud storage with encryption
      const result = await CloudStorageService.uploadFile(
        fileData,
        photo.filename,
        photo.type,
        'photo',
        (progress) => {
          // Update progress callback
          if (this.progressCallback) {
            this.progressCallback({
              totalPhotos: 1,
              processedPhotos: 0,
              currentPhoto: photo.filename,
              bytesUploaded: progress.bytesTransferred,
              totalBytes: progress.totalBytes,
              percentage: progress.percentage,
              status: 'uploading'
            });
          }
        }
      );

      if (result.success) {
        console.log(`✅ Successfully uploaded ${photo.filename}`);
        return true;
      } else {
        console.error(`❌ Failed to upload ${photo.filename}: ${result.error}`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Failed to upload ${photo.filename}:`, error);
      return false;
    }
  }

  // Check if user is authenticated before backup
  private async checkAuthentication(): Promise<boolean> {
    const user = AuthService.getCurrentUser();
    if (!user) {
      console.error('❌ User not authenticated');
      return false;
    }
    return true;
  }

  // Enhanced backup function with video exclusion and encryption
  async backupPhotos(
    photos: PhotoAsset[],
    onProgress?: (progress: BackupProgress) => void,
    maxRetries: number = 3
  ): Promise<BackupServiceResult> {
    if (this.isBackupRunning) {
      throw new Error('Photo backup is already running');
    }

    // Check authentication
    const user = AuthService.getCurrentUser();
    if (!user) {
      const error: BackupError = {
        id: `photo_auth_${Date.now()}`,
        type: 'permission',
        message: 'User authentication required for backup',
        timestamp: new Date(),
        retryable: true
      };
      return { success: false, itemsProcessed: 0, errors: [error] };
    }

    this.isBackupRunning = true;
    this.shouldPauseBackup = false;
    this.progressCallback = onProgress;

    const startTime = Date.now();
    let backedUpPhotos = 0;
    const errors: BackupError[] = [];

    try {
      console.log(`🚀 Starting backup of ${photos.length} photos...`);

      // Step 1: Request permissions
      const hasPermission = await this.requestPhotoPermission();
      if (!hasPermission) {
        const error: BackupError = {
          id: `photo_permission_${Date.now()}`,
          type: 'permission',
          message: 'Photo library permission denied by user',
          timestamp: new Date(),
          retryable: true
        };
        return { success: false, itemsProcessed: 0, errors: [error] };
      }

      // Step 2: Validate photo data (ensure no videos)
      if (onProgress) {
        onProgress({
          totalPhotos: photos.length,
          processedPhotos: 0,
          currentPhoto: 'Validating photo data...',
          bytesUploaded: 0,
          totalBytes: 0,
          percentage: 10,
          status: 'processing'
        });
      }

      const validatedPhotos = this.validatePhotoData(photos);
      if (validatedPhotos.length === 0) {
        const error: BackupError = {
          id: `photo_validation_${Date.now()}`,
          type: 'platform',
          message: 'No valid photos found to backup',
          timestamp: new Date(),
          retryable: false
        };
        return { success: false, itemsProcessed: 0, errors: [error] };
      }

      // Step 3: Detect duplicates
      if (onProgress) {
        onProgress({
          totalPhotos: validatedPhotos.length,
          processedPhotos: 0,
          currentPhoto: 'Detecting duplicates...',
          bytesUploaded: 0,
          totalBytes: 0,
          percentage: 20,
          status: 'processing'
        });
      }

      const uniquePhotos = await this.detectDuplicates(validatedPhotos);
      const duplicatesFound = validatedPhotos.length - uniquePhotos.length;

      // Step 4: Upload unique photos with encryption
      const totalBytes = uniquePhotos.reduce((sum, photo) => sum + photo.fileSize, 0);
      let processedBytes = 0;

      for (let i = 0; i < uniquePhotos.length; i++) {
        if (this.shouldPauseBackup) {
          break;
        }

        const photo = uniquePhotos[i];
        let success = false;
        let retryCount = 0;

        // Retry logic
        while (!success && retryCount < maxRetries) {
          try {
            if (onProgress) {
              onProgress({
                totalPhotos: uniquePhotos.length,
                processedPhotos: i,
                currentPhoto: photo.filename,
                bytesUploaded: processedBytes,
                totalBytes: totalBytes,
                percentage: 30 + Math.round(((i / uniquePhotos.length) * 70)),
                status: 'uploading'
              });
            }

            success = await this.uploadPhotoWithEncryption(photo);

            if (success) {
              backedUpPhotos++;
              processedBytes += photo.fileSize;
            }
          } catch (error: unknown) {
            retryCount++;
            console.error(`Retry ${retryCount}/${maxRetries} for ${photo.filename}:`, error);

            if (retryCount >= maxRetries) {
              const backupError: BackupError = {
                id: `photo_upload_${Date.now()}_${i}`,
                type: 'network',
                message: `Failed to backup ${photo.filename}: ${error instanceof Error ? error.message : String(error)}`,
                timestamp: new Date(),
                item_id: photo.filename,
                retryable: true
              };
              errors.push(backupError);
            } else {
              // Wait before retry
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
          }
        }
      }

      // Final progress update
      if (onProgress) {
        onProgress({
          totalPhotos: uniquePhotos.length,
          processedPhotos: uniquePhotos.length,
          currentPhoto: 'Backup completed!',
          bytesUploaded: processedBytes,
          totalBytes: totalBytes,
          percentage: 100,
          status: 'completed'
        });
      }

      const result: BackupServiceResult = {
        success: errors.length === 0,
        itemsProcessed: backedUpPhotos,
        errors,
        data: {
          totalPhotos: photos.length,
          backedUpPhotos,
          duplicatesFound,
          duration: Date.now() - startTime
        }
      };

      console.log('🎉 Photo backup completed:', result);
      return result;

    } catch (error: unknown) {
      console.error('❌ Photo backup failed:', error);

      const backupError: BackupError = {
        id: `photo_general_${Date.now()}`,
        type: 'platform',
        message: error instanceof Error ? error.message : 'Unknown error during photo backup',
        timestamp: new Date(),
        retryable: true
      };

      if (onProgress) {
        onProgress({
          totalPhotos: photos.length,
          processedPhotos: 0,
          currentPhoto: '',
          bytesUploaded: 0,
          totalBytes: 0,
          percentage: 0,
          status: 'error',
          error: backupError.message
        });
      }

      return {
        success: false,
        itemsProcessed: backedUpPhotos,
        errors: [backupError]
      };
    } finally {
      this.isBackupRunning = false;
      this.progressCallback = undefined;
    }
  }

  // Validate photo data and exclude videos
  private validatePhotoData(photos: PhotoAsset[]): PhotoAsset[] {
    return photos.filter(photo => {
      // Ensure photo has required fields
      const hasUri = photo.uri && photo.uri.trim().length > 0;
      const hasFilename = photo.filename && photo.filename.trim().length > 0;
      const hasValidTimestamp = photo.timestamp && photo.timestamp > 0;
      const hasValidSize = photo.fileSize && photo.fileSize > 0;
      
      if (!hasUri || !hasFilename || !hasValidTimestamp || !hasValidSize) {
        console.warn(`Skipping invalid photo: ${photo.filename}`);
        return false;
      }

      // Double-check that this is not a video file
      if (this.isVideoFile(photo.filename, photo.type)) {
        console.warn(`Excluding video file from photo backup: ${photo.filename}`);
        return false;
      }
      
      return true;
    });
  }

  // Upload photo with encryption and performance optimization
  private async uploadPhotoWithEncryption(photo: PhotoAsset): Promise<boolean> {
    const itemStartTime = Date.now();
    
    try {
      if (this.shouldPauseBackup) {
        throw new Error('Backup paused by user');
      }

      console.log(`📤 Starting optimized encrypted upload for ${photo.filename} (${Math.round(photo.fileSize / 1024)}KB)`);

      // Check battery level before processing large files
      const batteryStatus = await PerformanceMonitoringService.monitorBatteryConsumption();
      if (batteryStatus.shouldPause) {
        await BackupPerformanceLogger.logPause(batteryStatus.warning || 'Low battery');
        throw new Error(batteryStatus.warning || 'Battery too low for backup');
      }

      // Log item processing start
      await BackupPerformanceLogger.logItemProcessed('photos', photo.filename, photo.fileSize, 0, false);

      // Read file data
      const fileData = await RNFS.readFile(photo.uri, 'base64');

      // Determine upload strategy based on file size
      const LARGE_FILE_THRESHOLD = 5 * 1024 * 1024; // 5MB
      let result;

      if (photo.fileSize > LARGE_FILE_THRESHOLD) {
        console.log(`📦 Using chunked upload for large file: ${photo.filename}`);
        
        // Use chunked upload for large files
        result = await ChunkedUploadService.uploadLargeFile(
          fileData,
          photo.filename,
          photo.type,
          'photo',
          (chunkProgress) => {
            // Update progress callback with chunked upload progress
            if (this.progressCallback) {
              this.progressCallback({
                totalPhotos: 1,
                processedPhotos: 0,
                currentPhoto: `${photo.filename} (chunk ${chunkProgress.chunkIndex}/${chunkProgress.totalChunks})`,
                bytesUploaded: chunkProgress.bytesUploaded,
                totalBytes: chunkProgress.totalBytes,
                percentage: chunkProgress.percentage,
                status: 'uploading'
              });
            }
          }
        );
      } else {
        // Use standard upload for smaller files
        const encryptedData = await EncryptionService.encryptFile(fileData, photo.filename);

        result = await CloudStorageService.uploadFile(
          encryptedData.encryptedData,
          encryptedData.fileName,
          photo.type,
          'photo',
          (progress) => {
            // Update progress callback
            if (this.progressCallback) {
              this.progressCallback({
                totalPhotos: 1,
                processedPhotos: 0,
                currentPhoto: photo.filename,
                bytesUploaded: progress.bytesTransferred,
                totalBytes: progress.totalBytes,
                percentage: progress.percentage,
                status: 'uploading'
              });
            }
          }
        );
      }

      const processingTime = Date.now() - itemStartTime;

      if (result.success) {
        console.log(`✅ Successfully uploaded ${photo.filename} in ${Math.round(processingTime / 1000)}s`);
        
        // Log successful item processing
        await BackupPerformanceLogger.logItemProcessed('photos', photo.filename, photo.fileSize, processingTime, true);
        
        return true;
      } else {
        console.error(`❌ Failed to upload ${photo.filename}: ${result.error}`);
        
        // Log failed item processing
        await BackupPerformanceLogger.logError('photos', result.error || 'Upload failed', photo.filename, true);
        await BackupPerformanceLogger.logItemProcessed('photos', photo.filename, photo.fileSize, processingTime, false);
        
        return false;
      }
    } catch (error) {
      const processingTime = Date.now() - itemStartTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ Failed to upload ${photo.filename}:`, error);
      
      // Log error and failed processing
      await BackupPerformanceLogger.logError('photos', errorMessage, photo.filename, true);
      await BackupPerformanceLogger.logItemProcessed('photos', photo.filename, photo.fileSize, processingTime, false);
      
      return false;
    }
  }

  // Control functions
  pauseBackup(): void {
    this.shouldPauseBackup = true;
    console.log('⏸️ Backup paused by user');
  }

  resumeBackup(): void {
    this.shouldPauseBackup = false;
    console.log('▶️ Backup resumed by user');
  }

  isRunning(): boolean {
    return this.isBackupRunning;
  }

  // Restore photos from backup
  async restorePhotos(): Promise<{ success: boolean; photos?: PhotoAsset[]; error?: string }> {
    try {
      console.log('📸 Starting photo restore...');

      // Get user's photo backup files
      const files = await CloudStorageService.getUserFiles('photo');

      if (files.length === 0) {
        return { success: false, error: 'No photo backup found. Please create a backup first by using the "Start Photo Backup" button.' };
      }

      console.log(`📸 Found ${files.length} photo backups`);

      // For demo purposes, we'll simulate photo restoration
      // In a real implementation, this would download and decrypt each photo
      const restoredPhotos: PhotoAsset[] = files.map((file, index) => ({
        id: file.id,
        uri: `restored_photo_${index}`, // Placeholder URI
        filename: file.original_name,
        width: 1920, // Placeholder dimensions
        height: 1080,
        fileSize: file.size,
        timestamp: new Date(file.uploaded_at).getTime(),
        type: file.mime_type
      }));

      console.log(`✅ Successfully restored ${restoredPhotos.length} photos`);
      return { success: true, photos: restoredPhotos };

    } catch (error: unknown) {
      console.error('❌ Photo restore failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  // Get comprehensive backup statistics
  async getBackupStatsFromCloud(): Promise<{
    totalBackups: number;
    lastBackupDate?: Date;
    totalPhotos: number;
  }> {
    try {
      const files = await CloudStorageService.getUserFiles('photo');

      if (files.length === 0) {
        return { totalBackups: 0, totalPhotos: 0 };
      }

      const latestBackup = files.sort((a, b) =>
        new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
      )[0];

      return {
        totalBackups: files.length,
        lastBackupDate: new Date(latestBackup.uploaded_at),
        totalPhotos: files.length
      };
    } catch (error: unknown) {
      console.error('Failed to get backup stats:', error);
      return { totalBackups: 0, totalPhotos: 0 };
    }
  }

  // Get backup statistics
  getBackupStats(): { duplicateHashes: number } {
    return {
      duplicateHashes: this.duplicateHashes.size
    };
  }
}

export default new PhotoBackupService();
