declare module 'react-native-progress/Bar' {
  import * as React from 'react';
  import { ViewStyle, StyleProp } from 'react-native';

  export interface ProgressBarProps {
    progress?: number;
    width?: number | null;
    height?: number;
    color?: string;
    unfilledColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    borderColor?: string;
    animationType?: 'decay' | 'spring' | 'timing';
    animated?: boolean;
    indeterminate?: boolean;
    indeterminateAnimationDuration?: number;
    useNativeDriver?: boolean;
    style?: StyleProp<ViewStyle>;
    children?: React.ReactNode;
  }

  const ProgressBar: React.ComponentType<ProgressBarProps>;
  export default ProgressBar;
}
