/**
 * Locale-Specific Formatter
 * Handles locale-specific formatting for dates, numbers, currencies, and text
 */

class LocaleFormatter {
    constructor() {
        this.formatters = new Map();
        this.localeData = new Map();
        this.rtlLanguages = new Set(['ar', 'he', 'fa', 'ur', 'yi', 'ji', 'iw', 'ku', 'ps']);
        
        this.initializeLocaleData();
        this.initializeFormatters();
    }

    initializeLocaleData() {
        // Comprehensive locale data for supported languages
        this.localeData.set('en', {
            code: 'en-US',
            name: 'English',
            nativeName: 'English',
            direction: 'ltr',
            dateFormat: {
                short: 'M/d/yyyy',
                medium: 'MMM d, yyyy',
                long: 'MMMM d, yyyy',
                full: 'EEEE, MMMM d, yyyy'
            },
            timeFormat: {
                short: 'h:mm a',
                medium: 'h:mm:ss a',
                long: 'h:mm:ss a z'
            },
            numberFormat: {
                decimal: '.',
                thousands: ',',
                grouping: [3],
                currency: {
                    symbol: '$',
                    position: 'before',
                    code: 'USD'
                }
            },
            pluralRules: this.getEnglishPluralRules(),
            weekStart: 0, // Sunday
            calendar: 'gregorian'
        });

        this.localeData.set('es', {
            code: 'es-ES',
            name: 'Spanish',
            nativeName: 'Español',
            direction: 'ltr',
            dateFormat: {
                short: 'd/M/yyyy',
                medium: 'd MMM yyyy',
                long: 'd \'de\' MMMM \'de\' yyyy',
                full: 'EEEE, d \'de\' MMMM \'de\' yyyy'
            },
            timeFormat: {
                short: 'H:mm',
                medium: 'H:mm:ss',
                long: 'H:mm:ss z'
            },
            numberFormat: {
                decimal: ',',
                thousands: '.',
                grouping: [3],
                currency: {
                    symbol: '€',
                    position: 'after',
                    code: 'EUR'
                }
            },
            pluralRules: this.getSpanishPluralRules(),
            weekStart: 1, // Monday
            calendar: 'gregorian'
        });

        this.localeData.set('fr', {
            code: 'fr-FR',
            name: 'French',
            nativeName: 'Français',
            direction: 'ltr',
            dateFormat: {
                short: 'dd/MM/yyyy',
                medium: 'd MMM yyyy',
                long: 'd MMMM yyyy',
                full: 'EEEE d MMMM yyyy'
            },
            timeFormat: {
                short: 'HH:mm',
                medium: 'HH:mm:ss',
                long: 'HH:mm:ss z'
            },
            numberFormat: {
                decimal: ',',
                thousands: ' ',
                grouping: [3],
                currency: {
                    symbol: '€',
                    position: 'after',
                    code: 'EUR'
                }
            },
            pluralRules: this.getFrenchPluralRules(),
            weekStart: 1, // Monday
            calendar: 'gregorian'
        });

        this.localeData.set('de', {
            code: 'de-DE',
            name: 'German',
            nativeName: 'Deutsch',
            direction: 'ltr',
            dateFormat: {
                short: 'dd.MM.yyyy',
                medium: 'd. MMM yyyy',
                long: 'd. MMMM yyyy',
                full: 'EEEE, d. MMMM yyyy'
            },
            timeFormat: {
                short: 'HH:mm',
                medium: 'HH:mm:ss',
                long: 'HH:mm:ss z'
            },
            numberFormat: {
                decimal: ',',
                thousands: '.',
                grouping: [3],
                currency: {
                    symbol: '€',
                    position: 'after',
                    code: 'EUR'
                }
            },
            pluralRules: this.getGermanPluralRules(),
            weekStart: 1, // Monday
            calendar: 'gregorian'
        });

        this.localeData.set('ar', {
            code: 'ar-SA',
            name: 'Arabic',
            nativeName: 'العربية',
            direction: 'rtl',
            dateFormat: {
                short: 'dd/MM/yyyy',
                medium: 'd MMM yyyy',
                long: 'd MMMM yyyy',
                full: 'EEEE، d MMMM yyyy'
            },
            timeFormat: {
                short: 'h:mm a',
                medium: 'h:mm:ss a',
                long: 'h:mm:ss a z'
            },
            numberFormat: {
                decimal: '.',
                thousands: ',',
                grouping: [3],
                currency: {
                    symbol: 'ر.س',
                    position: 'after',
                    code: 'SAR'
                }
            },
            pluralRules: this.getArabicPluralRules(),
            weekStart: 6, // Saturday
            calendar: 'gregorian',
            numerals: ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩']
        });

        this.localeData.set('zh', {
            code: 'zh-CN',
            name: 'Chinese',
            nativeName: '中文',
            direction: 'ltr',
            dateFormat: {
                short: 'yyyy/M/d',
                medium: 'yyyy年M月d日',
                long: 'yyyy年M月d日',
                full: 'yyyy年M月d日EEEE'
            },
            timeFormat: {
                short: 'ah:mm',
                medium: 'ah:mm:ss',
                long: 'ah:mm:ss z'
            },
            numberFormat: {
                decimal: '.',
                thousands: ',',
                grouping: [3],
                currency: {
                    symbol: '¥',
                    position: 'before',
                    code: 'CNY'
                }
            },
            pluralRules: this.getChinesePluralRules(),
            weekStart: 1, // Monday
            calendar: 'gregorian'
        });

        this.localeData.set('ja', {
            code: 'ja-JP',
            name: 'Japanese',
            nativeName: '日本語',
            direction: 'ltr',
            dateFormat: {
                short: 'yyyy/MM/dd',
                medium: 'yyyy年M月d日',
                long: 'yyyy年M月d日',
                full: 'yyyy年M月d日EEEE'
            },
            timeFormat: {
                short: 'H:mm',
                medium: 'H:mm:ss',
                long: 'H:mm:ss z'
            },
            numberFormat: {
                decimal: '.',
                thousands: ',',
                grouping: [3],
                currency: {
                    symbol: '¥',
                    position: 'before',
                    code: 'JPY'
                }
            },
            pluralRules: this.getJapanesePluralRules(),
            weekStart: 0, // Sunday
            calendar: 'gregorian'
        });

        this.localeData.set('ko', {
            code: 'ko-KR',
            name: 'Korean',
            nativeName: '한국어',
            direction: 'ltr',
            dateFormat: {
                short: 'yyyy. M. d.',
                medium: 'yyyy년 M월 d일',
                long: 'yyyy년 M월 d일',
                full: 'yyyy년 M월 d일 EEEE'
            },
            timeFormat: {
                short: 'a h:mm',
                medium: 'a h:mm:ss',
                long: 'a h:mm:ss z'
            },
            numberFormat: {
                decimal: '.',
                thousands: ',',
                grouping: [3],
                currency: {
                    symbol: '₩',
                    position: 'before',
                    code: 'KRW'
                }
            },
            pluralRules: this.getKoreanPluralRules(),
            weekStart: 0, // Sunday
            calendar: 'gregorian'
        });

        this.localeData.set('hi', {
            code: 'hi-IN',
            name: 'Hindi',
            nativeName: 'हिन्दी',
            direction: 'ltr',
            dateFormat: {
                short: 'd/M/yyyy',
                medium: 'd MMM yyyy',
                long: 'd MMMM yyyy',
                full: 'EEEE, d MMMM yyyy'
            },
            timeFormat: {
                short: 'h:mm a',
                medium: 'h:mm:ss a',
                long: 'h:mm:ss a z'
            },
            numberFormat: {
                decimal: '.',
                thousands: ',',
                grouping: [3, 2], // Indian numbering system
                currency: {
                    symbol: '₹',
                    position: 'before',
                    code: 'INR'
                }
            },
            pluralRules: this.getHindiPluralRules(),
            weekStart: 0, // Sunday
            calendar: 'gregorian',
            numerals: ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९']
        });

        this.localeData.set('ru', {
            code: 'ru-RU',
            name: 'Russian',
            nativeName: 'Русский',
            direction: 'ltr',
            dateFormat: {
                short: 'dd.MM.yyyy',
                medium: 'd MMM yyyy \'г\'.',
                long: 'd MMMM yyyy \'г\'.',
                full: 'EEEE, d MMMM yyyy \'г\'.'
            },
            timeFormat: {
                short: 'H:mm',
                medium: 'H:mm:ss',
                long: 'H:mm:ss z'
            },
            numberFormat: {
                decimal: ',',
                thousands: ' ',
                grouping: [3],
                currency: {
                    symbol: '₽',
                    position: 'after',
                    code: 'RUB'
                }
            },
            pluralRules: this.getRussianPluralRules(),
            weekStart: 1, // Monday
            calendar: 'gregorian'
        });
    }

    initializeFormatters() {
        // Initialize Intl formatters for each locale
        this.localeData.forEach((localeInfo, langCode) => {
            try {
                // Number formatter
                this.formatters.set(`number-${langCode}`, new Intl.NumberFormat(localeInfo.code));
                
                // Currency formatter
                this.formatters.set(`currency-${langCode}`, new Intl.NumberFormat(localeInfo.code, {
                    style: 'currency',
                    currency: localeInfo.numberFormat.currency.code
                }));
                
                // Date formatter
                this.formatters.set(`date-${langCode}`, new Intl.DateTimeFormat(localeInfo.code));
                
                // Time formatter
                this.formatters.set(`time-${langCode}`, new Intl.DateTimeFormat(localeInfo.code, {
                    hour: '2-digit',
                    minute: '2-digit'
                }));
                
                // Relative time formatter (if supported)
                if (Intl.RelativeTimeFormat) {
                    this.formatters.set(`relative-${langCode}`, new Intl.RelativeTimeFormat(localeInfo.code, {
                        numeric: 'auto'
                    }));
                }
                
                // List formatter (if supported)
                if (Intl.ListFormat) {
                    this.formatters.set(`list-${langCode}`, new Intl.ListFormat(localeInfo.code, {
                        style: 'long',
                        type: 'conjunction'
                    }));
                }
            } catch (error) {
                console.warn(`Failed to initialize formatters for ${langCode}:`, error);
            }
        });
    }

    // Plural rules for different languages
    getEnglishPluralRules() {
        return (n) => {
            if (n === 1) return 'one';
            return 'other';
        };
    }

    getSpanishPluralRules() {
        return (n) => {
            if (n === 1) return 'one';
            return 'other';
        };
    }

    getFrenchPluralRules() {
        return (n) => {
            if (n === 0 || n === 1) return 'one';
            return 'other';
        };
    }

    getGermanPluralRules() {
        return (n) => {
            if (n === 1) return 'one';
            return 'other';
        };
    }

    getArabicPluralRules() {
        return (n) => {
            if (n === 0) return 'zero';
            if (n === 1) return 'one';
            if (n === 2) return 'two';
            if (n >= 3 && n <= 10) return 'few';
            if (n >= 11 && n <= 99) return 'many';
            return 'other';
        };
    }

    getChinesePluralRules() {
        return (n) => 'other'; // Chinese doesn't have plural forms
    }

    getJapanesePluralRules() {
        return (n) => 'other'; // Japanese doesn't have plural forms
    }

    getKoreanPluralRules() {
        return (n) => 'other'; // Korean doesn't have plural forms
    }

    getHindiPluralRules() {
        return (n) => {
            if (n === 0 || n === 1) return 'one';
            return 'other';
        };
    }

    getRussianPluralRules() {
        return (n) => {
            const mod10 = n % 10;
            const mod100 = n % 100;
            
            if (mod10 === 1 && mod100 !== 11) return 'one';
            if (mod10 >= 2 && mod10 <= 4 && (mod100 < 12 || mod100 > 14)) return 'few';
            return 'many';
        };
    }

    // Formatting methods
    formatNumber(number, locale = 'en', options = {}) {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo) {
            console.warn(`Unsupported locale: ${locale}`);
            return number.toString();
        }

        try {
            const formatter = new Intl.NumberFormat(localeInfo.code, options);
            let formatted = formatter.format(number);
            
            // Apply custom numerals if available
            if (localeInfo.numerals) {
                formatted = this.convertToLocalNumerals(formatted, localeInfo.numerals);
            }
            
            return formatted;
        } catch (error) {
            console.warn('Number formatting failed:', error);
            return number.toString();
        }
    }

    formatCurrency(amount, locale = 'en', currency = null, options = {}) {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo) {
            console.warn(`Unsupported locale: ${locale}`);
            return amount.toString();
        }

        const currencyCode = currency || localeInfo.numberFormat.currency.code;
        
        try {
            const formatter = new Intl.NumberFormat(localeInfo.code, {
                style: 'currency',
                currency: currencyCode,
                ...options
            });
            
            let formatted = formatter.format(amount);
            
            // Apply custom numerals if available
            if (localeInfo.numerals) {
                formatted = this.convertToLocalNumerals(formatted, localeInfo.numerals);
            }
            
            return formatted;
        } catch (error) {
            console.warn('Currency formatting failed:', error);
            const symbol = localeInfo.numberFormat.currency.symbol;
            return `${symbol}${amount}`;
        }
    }

    formatDate(date, locale = 'en', style = 'medium', options = {}) {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo) {
            console.warn(`Unsupported locale: ${locale}`);
            return date.toLocaleDateString();
        }

        try {
            const formatOptions = {
                dateStyle: style,
                ...options
            };
            
            const formatter = new Intl.DateTimeFormat(localeInfo.code, formatOptions);
            return formatter.format(date);
        } catch (error) {
            console.warn('Date formatting failed:', error);
            return date.toLocaleDateString();
        }
    }

    formatTime(date, locale = 'en', style = 'short', options = {}) {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo) {
            console.warn(`Unsupported locale: ${locale}`);
            return date.toLocaleTimeString();
        }

        try {
            const formatOptions = {
                timeStyle: style,
                ...options
            };
            
            const formatter = new Intl.DateTimeFormat(localeInfo.code, formatOptions);
            return formatter.format(date);
        } catch (error) {
            console.warn('Time formatting failed:', error);
            return date.toLocaleTimeString();
        }
    }

    formatRelativeTime(date, locale = 'en', options = {}) {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo) {
            console.warn(`Unsupported locale: ${locale}`);
            return date.toString();
        }

        const now = new Date();
        const diffInSeconds = Math.floor((date - now) / 1000);
        
        try {
            if (Intl.RelativeTimeFormat) {
                const formatter = new Intl.RelativeTimeFormat(localeInfo.code, {
                    numeric: 'auto',
                    ...options
                });
                
                if (Math.abs(diffInSeconds) < 60) {
                    return formatter.format(diffInSeconds, 'second');
                } else if (Math.abs(diffInSeconds) < 3600) {
                    return formatter.format(Math.floor(diffInSeconds / 60), 'minute');
                } else if (Math.abs(diffInSeconds) < 86400) {
                    return formatter.format(Math.floor(diffInSeconds / 3600), 'hour');
                } else {
                    return formatter.format(Math.floor(diffInSeconds / 86400), 'day');
                }
            } else {
                // Fallback for browsers without RelativeTimeFormat
                return this.formatRelativeTimeFallback(diffInSeconds, locale);
            }
        } catch (error) {
            console.warn('Relative time formatting failed:', error);
            return date.toLocaleDateString();
        }
    }

    formatRelativeTimeFallback(diffInSeconds, locale) {
        const absSeconds = Math.abs(diffInSeconds);
        const isPast = diffInSeconds < 0;
        
        // Basic relative time strings (would be better to use actual translations)
        const timeStrings = {
            en: {
                now: 'now',
                secondsAgo: 'seconds ago',
                minutesAgo: 'minutes ago',
                hoursAgo: 'hours ago',
                daysAgo: 'days ago',
                inSeconds: 'in seconds',
                inMinutes: 'in minutes',
                inHours: 'in hours',
                inDays: 'in days'
            },
            es: {
                now: 'ahora',
                secondsAgo: 'hace segundos',
                minutesAgo: 'hace minutos',
                hoursAgo: 'hace horas',
                daysAgo: 'hace días',
                inSeconds: 'en segundos',
                inMinutes: 'en minutos',
                inHours: 'en horas',
                inDays: 'en días'
            }
            // Add more languages as needed
        };
        
        const strings = timeStrings[locale] || timeStrings.en;
        
        if (absSeconds < 60) {
            return strings.now;
        } else if (absSeconds < 3600) {
            const minutes = Math.floor(absSeconds / 60);
            return isPast ? `${minutes} ${strings.minutesAgo}` : `${strings.inMinutes} ${minutes}`;
        } else if (absSeconds < 86400) {
            const hours = Math.floor(absSeconds / 3600);
            return isPast ? `${hours} ${strings.hoursAgo}` : `${strings.inHours} ${hours}`;
        } else {
            const days = Math.floor(absSeconds / 86400);
            return isPast ? `${days} ${strings.daysAgo}` : `${strings.inDays} ${days}`;
        }
    }

    formatList(items, locale = 'en', options = {}) {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo) {
            console.warn(`Unsupported locale: ${locale}`);
            return items.join(', ');
        }

        try {
            if (Intl.ListFormat) {
                const formatter = new Intl.ListFormat(localeInfo.code, {
                    style: 'long',
                    type: 'conjunction',
                    ...options
                });
                return formatter.format(items);
            } else {
                // Fallback for browsers without ListFormat
                return this.formatListFallback(items, locale);
            }
        } catch (error) {
            console.warn('List formatting failed:', error);
            return items.join(', ');
        }
    }

    formatListFallback(items, locale) {
        if (items.length === 0) return '';
        if (items.length === 1) return items[0];
        if (items.length === 2) {
            const conjunctions = {
                en: 'and',
                es: 'y',
                fr: 'et',
                de: 'und',
                ar: 'و',
                zh: '和',
                ja: 'と',
                ko: '그리고',
                hi: 'और',
                ru: 'и'
            };
            const conjunction = conjunctions[locale] || conjunctions.en;
            return `${items[0]} ${conjunction} ${items[1]}`;
        }
        
        const lastItem = items[items.length - 1];
        const otherItems = items.slice(0, -1);
        const conjunctions = {
            en: ', and ',
            es: ', y ',
            fr: ', et ',
            de: ', und ',
            ar: '، و',
            zh: '，和',
            ja: '、と',
            ko: ', 그리고 ',
            hi: ', और ',
            ru: ', и '
        };
        const conjunction = conjunctions[locale] || conjunctions.en;
        
        return otherItems.join(', ') + conjunction + lastItem;
    }

    formatPlural(count, translations, locale = 'en') {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo || !localeInfo.pluralRules) {
            console.warn(`No plural rules for locale: ${locale}`);
            return translations.other || translations.one || '';
        }

        const pluralForm = localeInfo.pluralRules(count);
        return translations[pluralForm] || translations.other || translations.one || '';
    }

    convertToLocalNumerals(text, numerals) {
        if (!numerals || numerals.length !== 10) return text;
        
        return text.replace(/[0-9]/g, (digit) => {
            return numerals[parseInt(digit)];
        });
    }

    // RTL support methods
    isRTL(locale) {
        const localeInfo = this.localeData.get(locale);
        return localeInfo ? localeInfo.direction === 'rtl' : this.rtlLanguages.has(locale);
    }

    getTextDirection(locale) {
        return this.isRTL(locale) ? 'rtl' : 'ltr';
    }

    applyTextDirection(element, locale) {
        const direction = this.getTextDirection(locale);
        element.dir = direction;
        element.style.direction = direction;
        
        // Add CSS classes for styling
        element.classList.toggle('rtl', direction === 'rtl');
        element.classList.toggle('ltr', direction === 'ltr');
    }

    // Utility methods
    getLocaleInfo(locale) {
        return this.localeData.get(locale);
    }

    getSupportedLocales() {
        return Array.from(this.localeData.keys());
    }

    getWeekStart(locale) {
        const localeInfo = this.localeData.get(locale);
        return localeInfo ? localeInfo.weekStart : 0;
    }

    getCalendarType(locale) {
        const localeInfo = this.localeData.get(locale);
        return localeInfo ? localeInfo.calendar : 'gregorian';
    }

    // Address formatting
    formatAddress(address, locale = 'en') {
        const localeInfo = this.localeData.get(locale);
        if (!localeInfo) return this.formatAddressFallback(address);

        // Different address formats for different locales
        const formats = {
            'en': '{street}\n{city}, {state} {postalCode}\n{country}',
            'es': '{street}\n{postalCode} {city}, {state}\n{country}',
            'fr': '{street}\n{postalCode} {city}\n{country}',
            'de': '{street}\n{postalCode} {city}\n{country}',
            'ar': '{street}\n{city} {postalCode}\n{country}',
            'zh': '{country} {state} {city}\n{street} {postalCode}',
            'ja': '〒{postalCode}\n{state}{city}\n{street}\n{country}',
            'ko': '{country} {state} {city}\n{street}\n{postalCode}'
        };

        const format = formats[locale] || formats['en'];
        
        return format.replace(/\{(\w+)\}/g, (match, key) => {
            return address[key] || '';
        }).replace(/\n\s*\n/g, '\n').trim();
    }

    formatAddressFallback(address) {
        const parts = [
            address.street,
            [address.city, address.state].filter(Boolean).join(', '),
            address.postalCode,
            address.country
        ].filter(Boolean);
        
        return parts.join('\n');
    }

    // Phone number formatting
    formatPhoneNumber(phoneNumber, locale = 'en') {
        // Basic phone number formatting based on locale
        const formats = {
            'en': this.formatUSPhoneNumber,
            'es': this.formatEuropeanPhoneNumber,
            'fr': this.formatEuropeanPhoneNumber,
            'de': this.formatEuropeanPhoneNumber,
            'ar': this.formatInternationalPhoneNumber,
            'zh': this.formatInternationalPhoneNumber,
            'ja': this.formatInternationalPhoneNumber,
            'ko': this.formatInternationalPhoneNumber,
            'hi': this.formatIndianPhoneNumber,
            'ru': this.formatRussianPhoneNumber
        };

        const formatter = formats[locale] || this.formatInternationalPhoneNumber;
        return formatter.call(this, phoneNumber);
    }

    formatUSPhoneNumber(phoneNumber) {
        const cleaned = phoneNumber.replace(/\D/g, '');
        if (cleaned.length === 10) {
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        } else if (cleaned.length === 11 && cleaned[0] === '1') {
            return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
        }
        return phoneNumber;
    }

    formatEuropeanPhoneNumber(phoneNumber) {
        const cleaned = phoneNumber.replace(/\D/g, '');
        if (cleaned.length >= 8) {
            return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 4)} ${cleaned.slice(4, 6)} ${cleaned.slice(6, 8)} ${cleaned.slice(8)}`.trim();
        }
        return phoneNumber;
    }

    formatIndianPhoneNumber(phoneNumber) {
        const cleaned = phoneNumber.replace(/\D/g, '');
        if (cleaned.length === 10) {
            return `${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
        } else if (cleaned.length === 12 && cleaned.slice(0, 2) === '91') {
            return `+91 ${cleaned.slice(2, 7)} ${cleaned.slice(7)}`;
        }
        return phoneNumber;
    }

    formatRussianPhoneNumber(phoneNumber) {
        const cleaned = phoneNumber.replace(/\D/g, '');
        if (cleaned.length === 11 && cleaned[0] === '7') {
            return `+7 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7, 9)}-${cleaned.slice(9)}`;
        }
        return phoneNumber;
    }

    formatInternationalPhoneNumber(phoneNumber) {
        // Basic international format
        const cleaned = phoneNumber.replace(/\D/g, '');
        if (cleaned.length > 7) {
            return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`.trim();
        }
        return phoneNumber;
    }

    // File size formatting
    formatFileSize(bytes, locale = 'en', binary = false) {
        const base = binary ? 1024 : 1000;
        const units = binary 
            ? ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB']
            : ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        if (bytes === 0) return `0 ${units[0]}`;

        const exponent = Math.floor(Math.log(bytes) / Math.log(base));
        const value = bytes / Math.pow(base, exponent);
        const formattedValue = this.formatNumber(value, locale, {
            minimumFractionDigits: 0,
            maximumFractionDigits: exponent === 0 ? 0 : 1
        });

        return `${formattedValue} ${units[exponent]}`;
    }
}

// Create global instance
const localeFormatter = new LocaleFormatter();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LocaleFormatter;
}

// Make available globally
window.localeFormatter = localeFormatter;