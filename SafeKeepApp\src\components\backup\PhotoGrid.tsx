import React from 'react';
import { View, FlatList, Image, StyleSheet, Dimensions } from 'react-native';

interface Photo {
  id: string;
  uri: string;
  timestamp?: number;
}

interface PhotoGridProps {
  photos: Photo[];
  numColumns?: number;
}

const PhotoGrid: React.FC<PhotoGridProps> = ({ photos, numColumns = 3 }) => {
  const screenWidth = Dimensions.get('window').width;
  const itemSize = (screenWidth - (numColumns + 1) * 10) / numColumns;

  const renderPhoto = ({ item }: { item: Photo }) => (
    <View style={[styles.photoContainer, { width: itemSize, height: itemSize }]}>
      <Image source={{ uri: item.uri }} style={styles.photo} />
    </View>
  );

  return (
    <FlatList
      data={photos}
      renderItem={renderPhoto}
      numColumns={numColumns}
      keyExtractor={(item) => item.id}
      contentContainerStyle={styles.container}
      columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 5,
  },
  row: {
    justifyContent: 'space-around',
  },
  photoContainer: {
    margin: 5,
    borderRadius: 8,
    overflow: 'hidden',
  },
  photo: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
});

export default PhotoGrid;