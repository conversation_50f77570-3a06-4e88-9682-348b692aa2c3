/* Payment Processing Interface Styles */

/* Payment Modal Styles */
.payment-modal, .subscription-flow-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.payment-modal .modal-content,
.subscription-flow-modal .modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.subscription-flow-modal .modal-content.large {
    max-width: 800px;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
}

.modal-header {
    padding: 24px 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 24px;
}

.modal-header h3,
.modal-header h4 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background: #f8f9fa;
}

.modal-body {
    padding: 0 24px 24px;
}

.modal-footer {
    padding: 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

/* Payment Elements Styles */
#payment-element,
#card-element {
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background: white;
    margin-bottom: 16px;
    transition: border-color 0.2s;
}

#payment-element:focus-within,
#card-element:focus-within {
    border-color: #4facfe;
    box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
}

#payment-errors {
    color: #fa755a;
    font-size: 14px;
    margin-top: 8px;
    min-height: 20px;
}

/* Billing Address Form Styles */
.billing-address-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.billing-address-form h4 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 1.1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
}

.validation-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    border: 1px solid #f5c6cb;
    font-size: 14px;
}

/* Tax Information Styles */
.tax-information,
.tax-breakdown {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.tax-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.tax-line:last-child {
    border-bottom: none;
}

.tax-line.total {
    border-top: 2px solid #e9ecef;
    margin-top: 8px;
    padding-top: 12px;
    font-size: 1.1rem;
}

/* Payment Methods Manager Styles */
.payment-methods-manager {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
}

.payment-methods-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e9ecef;
}

.payment-methods-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.4rem;
}

.payment-methods-list {
    display: grid;
    gap: 16px;
}

.payment-method-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
}

.payment-method-card:hover {
    border-color: #4facfe;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.15);
}

.payment-method-card.default {
    border-color: #28a745;
    background: #f8fff9;
}

.payment-method-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.payment-method-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
}

.payment-method-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.payment-method-primary {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    color: #333;
}

.payment-method-secondary {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #666;
}

.default-badge {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.payment-method-actions {
    display: flex;
    gap: 8px;
}

.btn-icon {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

.btn-icon:hover {
    background: #f8f9fa;
}

.btn-icon.danger:hover {
    background: #f8d7da;
}

/* Empty State Styles */
.payment-methods-empty {
    text-align: center;
    padding: 60px 20px;
}

.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-state h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 1.2rem;
}

.empty-state p {
    margin: 0 0 24px 0;
    color: #666;
    line-height: 1.5;
}

/* Subscription Flow Styles */
.upgrade-flow-steps {
    position: relative;
}

.flow-step {
    display: none;
    padding: 20px 0;
}

.flow-step.active {
    display: block;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #4facfe;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 18px;
}

.step-header h4 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.step-content {
    padding: 0 56px;
}

/* Plan Comparison Styles */
.plan-comparison-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 24px;
    align-items: center;
    margin-bottom: 32px;
}

.plan-column {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
}

.plan-column.current {
    border-color: #6c757d;
}

.plan-column.target {
    border-color: #4facfe;
    background: #f8f9ff;
}

.plan-header {
    margin-bottom: 20px;
}

.plan-header h5 {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.plan-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.plan-price {
    font-size: 1.2rem;
    color: #4facfe;
    font-weight: 500;
}

.plan-arrow {
    font-size: 24px;
    color: #4facfe;
    font-weight: bold;
}

.plan-features {
    text-align: left;
}

.feature-list {
    display: grid;
    gap: 12px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
}

.feature-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* Upgrade Benefits Styles */
.upgrade-benefits {
    background: #e8f5e8;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #28a745;
}

.upgrade-benefits h5 {
    margin: 0 0 16px 0;
    color: #155724;
    font-size: 1.1rem;
}

.benefits-list {
    display: grid;
    gap: 8px;
}

.benefit-item {
    color: #155724;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Payment Method Selection Styles */
.payment-method-options {
    display: grid;
    gap: 24px;
}

.existing-payment-methods h5 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 1.1rem;
}

.payment-method-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 12px;
}

.payment-method-option:hover {
    border-color: #4facfe;
    background: #f8f9ff;
}

.payment-method-option input[type="radio"] {
    margin: 0;
}

.payment-method-display {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.card-info {
    font-weight: 500;
    color: #333;
}

.expiry {
    font-size: 14px;
    color: #666;
}

.new-method-text {
    font-weight: 500;
    color: #4facfe;
}

.payment-method-divider {
    text-align: center;
    position: relative;
    margin: 20px 0;
}

.payment-method-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.payment-method-divider span {
    background: white;
    padding: 0 16px;
    color: #666;
    font-size: 14px;
}

.new-payment-form {
    margin-top: 16px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Flow Navigation Styles */
.flow-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.flow-progress {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* Downgrade Flow Styles */
.downgrade-warning {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
}

.warning-icon {
    font-size: 24px;
    color: #856404;
}

.warning-content h4 {
    margin: 0 0 8px 0;
    color: #856404;
    font-size: 1.1rem;
}

.warning-content p {
    margin: 0;
    color: #856404;
    line-height: 1.5;
}

.downgrade-impact {
    display: grid;
    gap: 24px;
}

.lost-features,
.reduced-limits,
.downgrade-timing {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #dc3545;
}

.lost-features h5,
.reduced-limits h5,
.downgrade-timing h5 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 1.1rem;
}

.lost-features ul,
.reduced-limits ul {
    margin: 0;
    padding-left: 20px;
    color: #666;
}

.lost-features li,
.reduced-limits li {
    margin-bottom: 8px;
    line-height: 1.4;
}

.downgrade-timing {
    border-left-color: #17a2b8;
}

.downgrade-timing p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

/* Review Step Styles */
.upgrade-review {
    display: grid;
    gap: 24px;
}

.review-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.review-section h5 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 1.1rem;
    padding-bottom: 8px;
    border-bottom: 1px solid #f8f9fa;
}

.plan-change-summary {
    display: grid;
    gap: 12px;
}

.change-from,
.change-to {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.change-to {
    background: #e8f5e8;
}

.label {
    font-weight: 500;
    color: #666;
    min-width: 50px;
}

.plan-name {
    font-weight: 600;
    color: #333;
}

.plan-price {
    color: #4facfe;
    font-weight: 500;
}

.payment-method-summary,
.billing-summary {
    font-size: 14px;
}

.billing-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.billing-line:last-child {
    border-bottom: none;
}

.billing-line.total {
    border-top: 2px solid #e9ecef;
    margin-top: 8px;
    padding-top: 12px;
    font-size: 1.1rem;
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: #4facfe;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #4facfe;
    border-color: #4facfe;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Confirmation Styles */
.confirmation-success,
.confirmation-error {
    text-align: center;
    padding: 40px 20px;
}

.confirmation-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.confirmation-success h3 {
    color: #28a745;
    margin: 0 0 12px 0;
    font-size: 1.4rem;
}

.confirmation-error h3 {
    color: #dc3545;
    margin: 0 0 12px 0;
    font-size: 1.4rem;
}

.confirmation-success p,
.confirmation-error p {
    margin: 0 0 20px 0;
    color: #666;
    line-height: 1.5;
}

.confirmation-details {
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
    padding: 0;
    list-style: none;
}

.confirmation-details li {
    padding: 8px 0;
    color: #666;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.confirmation-details li::before {
    content: '✓';
    color: #28a745;
    font-weight: bold;
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    line-height: 1;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn.primary {
    background: #4facfe;
    color: white;
}

.btn.primary:hover:not(:disabled) {
    background: #2196f3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 172, 254, 0.3);
}

.btn.secondary {
    background: #6c757d;
    color: white;
}

.btn.secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn.danger {
    background: #dc3545;
    color: white;
}

.btn.danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-icon {
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-modal .modal-content,
    .subscription-flow-modal .modal-content {
        width: 95%;
        margin: 20px;
        max-height: calc(100vh - 40px);
    }
    
    .plan-comparison-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .plan-arrow {
        transform: rotate(90deg);
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .step-content {
        padding: 0 20px;
    }
    
    .flow-navigation {
        flex-direction: column;
        gap: 16px;
    }
    
    .payment-method-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .payment-method-actions {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }
    
    .payment-methods-manager {
        padding: 16px;
    }
    
    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .step-content {
        padding: 0;
    }
}