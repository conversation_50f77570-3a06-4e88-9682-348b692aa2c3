import { ServiceValidator } from '../ServiceValidator';
import { supabase } from '../../utils/database';
import { SERVICE_TYPES } from '../../types/modular-pricing';

// Mock the database module
jest.mock('../../utils/database', () => ({
  supabase: {
    from: jest.fn()
  }
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('ServiceValidator', () => {
  let serviceValidator: ServiceValidator;

  beforeEach(() => {
    serviceValidator = new ServiceValidator();
    jest.clearAllMocks();
  });

  describe('validateServiceCombination', () => {
    const mockActiveServices = [
      { id: 'contacts', name: 'Contacts Backup', is_active: true },
      { id: 'messages', name: 'Messages Backup', is_active: true },
      { id: 'photos', name: 'Photos Backup', is_active: true }
    ];

    beforeEach(() => {
      // Default mock setup for successful database query
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          in: jest.fn().mockResolvedValue({
            data: mockActiveServices,
            error: null
          })
        })
      } as any);
    });

    it('should validate a single valid service', async () => {
      const result = await serviceValidator.validateServiceCombination(['contacts']);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toContain('Consider combining services for better value');
    });

    it('should validate multiple valid services', async () => {
      const result = await serviceValidator.validateServiceCombination(['contacts', 'messages']);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should validate all three services with warning', async () => {
      const result = await serviceValidator.validateServiceCombination(['contacts', 'messages', 'photos']);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toContain('Complete backup package available with potential savings');
    });

    it('should reject empty service array', async () => {
      const result = await serviceValidator.validateServiceCombination([]);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('At least one service must be selected');
    });

    it('should reject null or undefined service array', async () => {
      const result = await serviceValidator.validateServiceCombination(null as any);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('At least one service must be selected');
    });

    it('should reject duplicate services', async () => {
      const result = await serviceValidator.validateServiceCombination(['contacts', 'contacts', 'messages']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate services are not allowed');
    });

    it('should reject non-existent services', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          in: jest.fn().mockResolvedValue({
            data: [mockActiveServices[0]], // Only return contacts
            error: null
          })
        })
      } as any);

      const result = await serviceValidator.validateServiceCombination(['contacts', 'invalid-service']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid service(s): invalid-service');
    });

    it('should reject inactive services', async () => {
      const inactiveService = { id: 'messages', name: 'Messages Backup', is_active: false };
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          in: jest.fn().mockResolvedValue({
            data: [mockActiveServices[0], inactiveService],
            error: null
          })
        })
      } as any);

      const result = await serviceValidator.validateServiceCombination(['contacts', 'messages']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Inactive service(s): messages');
    });

    it('should reject unknown service types', async () => {
      const result = await serviceValidator.validateServiceCombination(['unknown-type']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Unknown service type(s): unknown-type');
    });

    it('should handle database errors gracefully', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          in: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Database connection failed' }
          })
        })
      } as any);

      const result = await serviceValidator.validateServiceCombination(['contacts']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Service validation failed due to system error');
    });

    it('should handle multiple validation errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          in: jest.fn().mockResolvedValue({
            data: [],
            error: null
          })
        })
      } as any);

      const result = await serviceValidator.validateServiceCombination(['contacts', 'contacts', 'invalid-service']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate services are not allowed');
      expect(result.errors).toContain('Invalid service(s): contacts, invalid-service');
    });

    it('should validate all known service types', async () => {
      const serviceIds = Object.values(SERVICE_TYPES);
      const result = await serviceValidator.validateServiceCombination(serviceIds);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toContain('Complete backup package available with potential savings');
    });

    it('should call database with correct parameters', async () => {
      const serviceIds = ['contacts', 'messages'];
      const mockSelect = jest.fn().mockReturnValue({
        in: jest.fn().mockResolvedValue({
          data: mockActiveServices,
          error: null
        })
      });
      
      mockSupabase.from.mockReturnValue({
        select: mockSelect
      } as any);

      await serviceValidator.validateServiceCombination(serviceIds);

      expect(mockSupabase.from).toHaveBeenCalledWith('service_types');
      expect(mockSelect).toHaveBeenCalledWith('id, name, is_active');
    });
  });

  describe('checkServiceAccess', () => {
    it('should return access granted for user with active service', async () => {
      // Mock the RPC call for service access check
      mockSupabase.from.mockReturnValue({
        rpc: jest.fn().mockResolvedValue({
          data: true,
          error: null
        })
      } as any);

      // Mock supabase.rpc directly
      (mockSupabase as any).rpc = jest.fn()
        .mockResolvedValueOnce({ data: true, error: null }) // user_has_service_access
        .mockResolvedValueOnce({ // get_user_subscription_details
          data: [{
            subscription_id: 'sub_123',
            plan_name: 'Contacts Only',
            next_billing_date: '2024-02-01T00:00:00Z'
          }],
          error: null
        });

      const result = await serviceValidator.checkServiceAccess('user-123', 'contacts');

      expect(result.hasAccess).toBe(true);
      expect(result.planName).toBe('Contacts Only');
      expect(result.expiresAt).toEqual(new Date('2024-02-01T00:00:00Z'));
    });

    it('should return access denied for user without service', async () => {
      (mockSupabase as any).rpc = jest.fn().mockResolvedValue({
        data: false,
        error: null
      });

      const result = await serviceValidator.checkServiceAccess('user-123', 'contacts');

      expect(result.hasAccess).toBe(false);
      expect(result.expiresAt).toBeUndefined();
      expect(result.planName).toBeUndefined();
    });

    it('should handle database errors gracefully', async () => {
      (mockSupabase as any).rpc = jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' }
      });

      const result = await serviceValidator.checkServiceAccess('user-123', 'contacts');

      expect(result.hasAccess).toBe(false);
    });

    it('should return access without expiration when subscription details unavailable', async () => {
      (mockSupabase as any).rpc = jest.fn()
        .mockResolvedValueOnce({ data: true, error: null }) // user_has_service_access
        .mockResolvedValueOnce({ data: null, error: { message: 'No subscription found' } }); // get_user_subscription_details

      const result = await serviceValidator.checkServiceAccess('user-123', 'contacts');

      expect(result.hasAccess).toBe(true);
      expect(result.expiresAt).toBeUndefined();
      expect(result.planName).toBeUndefined();
    });
  });

  describe('getUserServices', () => {
    it('should return user services successfully', async () => {
      const mockUserServices = [
        {
          service_id: 'contacts',
          service_name: 'Contacts Backup',
          is_active: true,
          activated_at: '2024-01-01T00:00:00Z'
        },
        {
          service_id: 'messages',
          service_name: 'Messages Backup',
          is_active: true,
          activated_at: '2024-01-01T00:00:00Z'
        }
      ];

      (mockSupabase as any).rpc = jest.fn().mockResolvedValue({
        data: mockUserServices,
        error: null
      });

      const result = await serviceValidator.getUserServices('user-123');

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        serviceId: 'contacts',
        serviceName: 'Contacts Backup',
        isActive: true,
        activatedAt: new Date('2024-01-01T00:00:00Z'),
        expiresAt: undefined
      });
      expect(result[1]).toEqual({
        serviceId: 'messages',
        serviceName: 'Messages Backup',
        isActive: true,
        activatedAt: new Date('2024-01-01T00:00:00Z'),
        expiresAt: undefined
      });
    });

    it('should return empty array when user has no services', async () => {
      (mockSupabase as any).rpc = jest.fn().mockResolvedValue({
        data: [],
        error: null
      });

      const result = await serviceValidator.getUserServices('user-123');

      expect(result).toEqual([]);
    });

    it('should handle database errors gracefully', async () => {
      (mockSupabase as any).rpc = jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'Database connection failed' }
      });

      const result = await serviceValidator.getUserServices('user-123');

      expect(result).toEqual([]);
    });

    it('should handle null data response', async () => {
      (mockSupabase as any).rpc = jest.fn().mockResolvedValue({
        data: null,
        error: null
      });

      const result = await serviceValidator.getUserServices('user-123');

      expect(result).toEqual([]);
    });

    it('should call database function with correct parameters', async () => {
      (mockSupabase as any).rpc = jest.fn().mockResolvedValue({
        data: [],
        error: null
      });

      await serviceValidator.getUserServices('user-123');

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_user_services', {
        user_uuid: 'user-123'
      });
    });
  });
});