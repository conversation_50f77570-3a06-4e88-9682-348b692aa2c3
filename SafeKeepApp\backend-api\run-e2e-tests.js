#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting End-to-End API Tests for Modular Pricing Backend');
console.log('=' .repeat(60));

// Set test environment
process.env.NODE_ENV = 'test';

// Test configuration
const testConfig = {
  testTimeout: 30000,
  verbose: true,
  coverage: true,
  detectOpenHandles: true,
  forceExit: true
};

// Test files to run
const testFiles = [
  'src/__tests__/e2e-subscription-flow.test.ts',
  'src/__tests__/e2e-error-scenarios.test.ts'
];

async function runTests() {
  try {
    console.log('📋 Test Configuration:');
    console.log(`   - Timeout: ${testConfig.testTimeout}ms`);
    console.log(`   - Coverage: ${testConfig.coverage ? 'Enabled' : 'Disabled'}`);
    console.log(`   - Test Files: ${testFiles.length}`);
    console.log('');

    // Build Jest command
    const jestArgs = [
      '--testTimeout=' + testConfig.testTimeout,
      testConfig.verbose ? '--verbose' : '',
      testConfig.coverage ? '--coverage' : '',
      testConfig.detectOpenHandles ? '--detectOpenHandles' : '',
      testConfig.forceExit ? '--forceExit' : '',
      '--testPathPattern="e2e-.*\\.test\\.ts$"',
      '--setupFilesAfterEnv="<rootDir>/src/__tests__/setup.ts"'
    ].filter(Boolean).join(' ');

    const command = `npx jest ${jestArgs}`;
    
    console.log('🧪 Running End-to-End Tests...');
    console.log(`Command: ${command}`);
    console.log('');

    // Execute tests
    execSync(command, {
      stdio: 'inherit',
      cwd: __dirname,
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    console.log('');
    console.log('✅ All End-to-End Tests Completed Successfully!');
    console.log('');
    console.log('📊 Test Summary:');
    console.log('   - Complete subscription flow tests: ✅');
    console.log('   - Service access validation tests: ✅');
    console.log('   - Error scenario tests: ✅');
    console.log('   - Payment failure tests: ✅');
    console.log('   - Webhook handling tests: ✅');
    console.log('   - Authentication/authorization tests: ✅');
    console.log('');

  } catch (error) {
    console.error('');
    console.error('❌ End-to-End Tests Failed!');
    console.error('');
    console.error('Error Details:');
    console.error(error.message);
    console.error('');
    console.error('🔍 Troubleshooting Tips:');
    console.error('   1. Ensure all dependencies are installed: npm install');
    console.error('   2. Check that test environment variables are set');
    console.error('   3. Verify database connections are working');
    console.error('   4. Ensure Stripe test keys are configured');
    console.error('   5. Check for any conflicting processes on test ports');
    console.error('');
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('');
  console.log('🛑 Tests interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('');
  console.log('🛑 Tests terminated');
  process.exit(1);
});

// Run the tests
runTests().catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});