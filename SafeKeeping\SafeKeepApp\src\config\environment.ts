// Environment Configuration for SafeKeep
// This file safely loads and validates environment variables

import { Platform } from 'react-native';
import {
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  STRIPE_SECRET_KEY,
  STRIPE_PUBLISHABLE_KEY,
  STRIPE_WEBHOOK_SECRET,
  NODE_ENV,
  APP_NAME,
  API_BASE_URL
} from '@env';

// Load environment variables
// Note: In React Native, you need to use a library like react-native-dotenv
// or manually configure environment variables in your build process

interface EnvironmentConfig {
  // Stripe Configuration
  STRIPE_SECRET_KEY: string;
  STRIPE_PUBLISHABLE_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  
  // App Configuration
  NODE_ENV: 'development' | 'production' | 'test';
  APP_NAME: string;
  
  // API Configuration
  API_BASE_URL: string;
  
  // Supabase Configuration (if using)
  SUPABASE_URL?: string;
  SUPABASE_ANON_KEY?: string;
}

// Default configuration for development
const defaultConfig: Partial<EnvironmentConfig> = {
  NODE_ENV: 'development',
  APP_NAME: 'SafeKeep Test',
  API_BASE_URL: Platform.OS === 'ios' ? 'http://localhost:3000' : 'http://********:3000',
};

// Load environment variables with fallbacks
const loadEnvironmentConfig = (): EnvironmentConfig => {
  const config: EnvironmentConfig = {
    // Stripe Keys - loaded from @env
    STRIPE_SECRET_KEY: STRIPE_SECRET_KEY || 'sk_test_PLACEHOLDER_PASTE_YOUR_SECRET_KEY_HERE',
    STRIPE_PUBLISHABLE_KEY: STRIPE_PUBLISHABLE_KEY || 'pk_test_PLACEHOLDER_PASTE_YOUR_PUBLISHABLE_KEY_HERE',
    STRIPE_WEBHOOK_SECRET: STRIPE_WEBHOOK_SECRET || 'whsec_PLACEHOLDER_PASTE_YOUR_WEBHOOK_SECRET_HERE',
    
    // App Configuration
    NODE_ENV: (NODE_ENV as any) || defaultConfig.NODE_ENV!,
    APP_NAME: APP_NAME || defaultConfig.APP_NAME!,
    
    // API Configuration
    API_BASE_URL: API_BASE_URL || defaultConfig.API_BASE_URL!,
    
    // Supabase Configuration - loaded from @env
    SUPABASE_URL: SUPABASE_URL,
    SUPABASE_ANON_KEY: SUPABASE_ANON_KEY,
  };

  return config;
};

// Validate required environment variables
const validateEnvironmentConfig = (config: EnvironmentConfig): void => {
  const requiredKeys: (keyof EnvironmentConfig)[] = [
    'STRIPE_SECRET_KEY',
    'STRIPE_PUBLISHABLE_KEY',
    'NODE_ENV',
    'APP_NAME',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
  ];

  const missingKeys: string[] = [];

  requiredKeys.forEach((key) => {
    if (!config[key] || config[key].includes('PLACEHOLDER')) {
      missingKeys.push(key);
    }
  });

  if (missingKeys.length > 0) {
    const errorMessage = `
❌ Missing or invalid environment variables:
${missingKeys.map(key => `   • ${key}`).join('\n')}

Please update your .env file with the correct values:

STRIPE_SECRET_KEY=sk_test_your_actual_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here
SUPABASE_URL=https://your_project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

You can find Stripe keys in your Stripe Dashboard:
https://dashboard.stripe.com/test/apikeys

You can find Supabase keys in your Supabase Dashboard:
https://app.supabase.com/project/your-project/settings/api
    `;

    if (__DEV__) {
      console.error(errorMessage);
      // In development, show a warning but don't crash
      console.warn('⚠️ Using placeholder Stripe keys - payments will not work');
    } else {
      // In production, this should be a hard error
      throw new Error(`Environment configuration error: ${missingKeys.join(', ')} are required`);
    }
  }
};

// Load and validate configuration
const environmentConfig = loadEnvironmentConfig();
validateEnvironmentConfig(environmentConfig);

// Log configuration status (without sensitive data)
console.log('🔧 Environment Configuration Loaded:');
console.log(`   • Environment: ${environmentConfig.NODE_ENV}`);
console.log(`   • App Name: ${environmentConfig.APP_NAME}`);
console.log(`   • API Base URL: ${environmentConfig.API_BASE_URL}`);
console.log(`   • Stripe Keys: ${environmentConfig.STRIPE_PUBLISHABLE_KEY.includes('PLACEHOLDER') ? '❌ Placeholder' : '✅ Configured'}`);
console.log(`   • Supabase: ${environmentConfig.SUPABASE_URL ? '✅ Configured' : '❌ Missing'}`);

export default environmentConfig;

// Export individual config values for convenience
export const {
  STRIPE_SECRET_KEY,
  STRIPE_PUBLISHABLE_KEY,
  STRIPE_WEBHOOK_SECRET,
  NODE_ENV,
  APP_NAME,
  API_BASE_URL,
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
} = environmentConfig;
