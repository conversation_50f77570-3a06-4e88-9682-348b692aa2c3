/**
 * Analytics Dashboard Styles
 * Styling for the performance monitoring and analytics dashboard
 */

.analytics-dashboard {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    overflow-y: auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.analytics-dashboard.hidden {
    display: none;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #1a1a1a;
    border-bottom: 1px solid #333;
    position: sticky;
    top: 0;
    z-index: 10001;
}

.dashboard-header h2 {
    color: #fff;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.dashboard-controls {
    display: flex;
    gap: 10px;
}

.dashboard-controls .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-close {
    background: #dc3545;
    color: white;
    font-size: 18px;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-close:hover {
    background: #c82333;
}

.dashboard-content {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #444;
}

.metric-card h3 {
    color: #fff;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
}

.metric-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.metric-label {
    color: #ccc;
    font-size: 14px;
}

.metric-value {
    color: #fff;
    font-weight: 600;
    font-size: 14px;
}

/* Error List */
.error-list {
    margin-top: 10px;
}

.error-item {
    background: #3a1f1f;
    border: 1px solid #5a2d2d;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 8px;
}

.error-message {
    color: #ff6b6b;
    font-size: 13px;
    margin-bottom: 4px;
}

.error-time {
    color: #999;
    font-size: 11px;
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #444;
}

.chart-container h3 {
    color: #fff;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
}

.chart-container canvas {
    width: 100%;
    height: auto;
    background: #1a1a1a;
    border-radius: 4px;
}

.heatmap-container {
    min-height: 200px;
    background: #1a1a1a;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ccc;
}

.heatmap-container canvas {
    background: transparent;
}

/* Recommendations Section */
.recommendations-section {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #444;
    margin-bottom: 30px;
}

.recommendations-section h3 {
    color: #fff;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation {
    background: #1a1a1a;
    border-radius: 6px;
    padding: 15px;
    border-left: 4px solid #007bff;
}

.recommendation-high {
    border-left-color: #dc3545;
}

.recommendation-medium {
    border-left-color: #ffc107;
}

.recommendation-low {
    border-left-color: #28a745;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.recommendation-type {
    color: #007bff;
    font-weight: 600;
    font-size: 12px;
}

.recommendation-severity {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    background: #444;
    color: #fff;
}

.recommendation-high .recommendation-severity {
    background: #dc3545;
}

.recommendation-medium .recommendation-severity {
    background: #ffc107;
    color: #212529;
}

.recommendation-low .recommendation-severity {
    background: #28a745;
}

.recommendation-message {
    color: #ccc;
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 1.4;
}

.recommendation-action {
    background: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.recommendation-action:hover {
    background: #0056b3;
}

/* A/B Testing Section */
.ab-testing-section {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #444;
}

.ab-testing-section h3 {
    color: #fff;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
}

.ab-testing-results {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ab-test-result {
    background: #1a1a1a;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #444;
}

.ab-test-result h4 {
    color: #fff;
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
}

.variant-result {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.variant-name {
    color: #ccc;
    font-size: 14px;
}

.variant-count {
    color: #007bff;
    font-weight: 600;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .dashboard-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }

    .chart-container canvas {
        height: 200px;
    }
}

@media (max-width: 480px) {
    .dashboard-content {
        padding: 10px;
    }

    .metric-card,
    .chart-container,
    .recommendations-section,
    .ab-testing-section {
        padding: 15px;
    }

    .dashboard-header h2 {
        font-size: 20px;
    }

    .dashboard-controls .btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Animation for smooth transitions */
.analytics-dashboard {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.metric-value {
    transition: color 0.2s ease;
}

.metric-value:hover {
    color: #007bff;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility improvements */
.analytics-dashboard:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .analytics-dashboard {
        background: #000;
    }
    
    .metric-card,
    .chart-container,
    .recommendations-section,
    .ab-testing-section {
        background: #111;
        border-color: #666;
    }
    
    .metric-label,
    .recommendation-message {
        color: #fff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .analytics-dashboard {
        animation: none;
    }
    
    .btn,
    .metric-value,
    .recommendation-action {
        transition: none;
    }
    
    .loading::after {
        animation: none;
    }
}