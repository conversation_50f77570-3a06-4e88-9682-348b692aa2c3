import Stripe from 'stripe';
import { StripeProvider, useStripe, useConfirmPayment, initStripe } from '@stripe/stripe-react-native';

// IMPORTANT: In React Native, environment variables work differently
// For production, use a secure config management solution
const STRIPE_SECRET_KEY = 'sk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP'; // This should be on your backend only
const STRIPE_PUBLISHABLE_KEY = 'pk_test_51RhVH5D5PtzUTHvm8bN33k1ZgHDg9sameanTCZycjq1vBQzDlFWYOX7BQBye6kvo2WbABwNLaQNBf3VhGWAvracq00kISuiuCP';

// Note: In production, the secret key should NEVER be in the mobile app
// It should only be on your secure backend server
console.log('⚠️ Using hardcoded Stripe keys for development. In production, use secure config management.');

// Note: Server-side Stripe operations should be handled by your backend
// This service focuses on React Native client-side operations

// Payment Intent interface for SafeKeep modular subscriptions
export interface PaymentIntentData {
  amount: number; // Amount in cents (e.g., 99 for $0.99, 699 for $6.99)
  currency: string; // e.g., 'usd'
  customerId?: string;
  description?: string;
  metadata?: Record<string, string>;
  // Modular pricing specific fields
  serviceIds: string[]; // Array of service IDs (contacts, messages, photos)
  userId: string; // User ID for tracking
  planId?: string; // Optimal plan ID
  planName?: string; // Optimal plan name
}

// Service combination metadata interface
export interface ServiceCombinationMetadata {
  userId: string;
  serviceIds: string[];
  planId: string;
  planName: string;
  individualTotalCents: number;
  savingsCents: number;
}

// Subscription request interface
export interface ModularSubscriptionRequest {
  userId: string;
  serviceIds: string[];
  paymentMethodId?: string;
  customerId?: string;
}

// Service access result interface
export interface ServiceAccessResult {
  hasAccess: boolean;
  expiresAt?: Date;
  planName?: string;
  serviceIds?: string[];
}

// Individual Services for Modular Pricing
export const SAFEKEEP_SERVICES = {
  CONTACTS: {
    id: 'contacts',
    name: 'Contacts Backup',
    price: 199, // $1.99/month in cents
    currency: 'usd',
    interval: 'month',
    description: 'Secure backup of your contacts',
    features: [
      '1GB Secure Storage',
      'Contact Backup',
      'Basic Encryption',
      'Manual & Daily Backup',
      'Standard Support'
    ],
  },
  MESSAGES: {
    id: 'messages',
    name: 'Messages Backup',
    price: 299, // $2.99/month in cents
    currency: 'usd',
    interval: 'month',
    description: 'Secure backup of your messages',
    features: [
      '5GB Secure Storage',
      'Message Backup',
      'Basic Encryption',
      'Manual & Daily Backup',
      'Standard Support'
    ],
  },
  PHOTOS: {
    id: 'photos',
    name: 'Photos Backup',
    price: 499, // $4.99/month in cents
    currency: 'usd',
    interval: 'month',
    description: 'Secure backup of your photos',
    features: [
      '50GB Secure Storage',
      'Photo Backup',
      'Basic Encryption',
      'Manual & Daily Backup',
      'Standard Support'
    ],
  },
} as const;

// Service Combination Plans for Modular Pricing
export const SAFEKEEP_COMBINATIONS = {
  CONTACTS_MESSAGES: {
    id: 'contacts_messages',
    name: 'Contacts + Messages',
    price: 399, // $3.99/month (save $0.99)
    currency: 'usd',
    interval: 'month',
    services: ['contacts', 'messages'],
    savingsCents: 99,
    storageGb: 5,
    isPopular: false,
    features: [
      '5GB Secure Storage',
      'Contact & Message Backup',
      'Advanced Encryption',
      'Extended Backup History',
      'Standard Support'
    ],
  },
  CONTACTS_PHOTOS: {
    id: 'contacts_photos',
    name: 'Contacts + Photos',
    price: 599, // $5.99/month (save $0.99)
    currency: 'usd',
    interval: 'month',
    services: ['contacts', 'photos'],
    savingsCents: 99,
    storageGb: 50,
    isPopular: false,
    features: [
      '50GB Secure Storage',
      'Contact & Photo Backup',
      'Advanced Encryption',
      'Extended Backup History',
      'Standard Support'
    ],
  },
  MESSAGES_PHOTOS: {
    id: 'messages_photos',
    name: 'Messages + Photos',
    price: 699, // $6.99/month (save $0.99)
    currency: 'usd',
    interval: 'month',
    services: ['messages', 'photos'],
    savingsCents: 99,
    storageGb: 50,
    isPopular: true,
    features: [
      '50GB Secure Storage',
      'Message & Photo Backup',
      'Advanced Encryption',
      'Extended Backup History',
      'Priority Support'
    ],
  },
  ALL_SERVICES: {
    id: 'all_services',
    name: 'Complete Backup',
    price: 799, // $7.99/month (save $1.97)
    currency: 'usd',
    interval: 'month',
    services: ['contacts', 'messages', 'photos'],
    savingsCents: 197,
    storageGb: 100,
    isPopular: true,
    features: [
      '100GB Secure Storage',
      'Contact, Message & Photo Backup',
      'Advanced Encryption',
      'Multi-Device Sync',
      'Priority Support',
      'Extended Backup History',
      'Unlimited Backups & Restores'
    ],
  },
} as const;

// Legacy plans for backward compatibility
export const SAFEKEEP_PLANS = {
  ...SAFEKEEP_SERVICES,
  ...SAFEKEEP_COMBINATIONS,
} as const;

// React Native Stripe Service - Client-side operations with modular pricing support
export const StripeService = {
  // Get publishable key for frontend use
  getPublishableKey: (): string => {
    return STRIPE_PUBLISHABLE_KEY;
  },

  // Get individual service by ID
  getService: (serviceId: string) => {
    return Object.values(SAFEKEEP_SERVICES).find(service => service.id === serviceId);
  },

  // Get all individual services
  getAllServices: () => {
    return SAFEKEEP_SERVICES;
  },

  // Get combination plan by ID
  getCombination: (combinationId: string) => {
    return Object.values(SAFEKEEP_COMBINATIONS).find(combo => combo.id === combinationId);
  },

  // Get all combination plans
  getAllCombinations: () => {
    return SAFEKEEP_COMBINATIONS;
  },

  // Get plan by ID (legacy support)
  getPlan: (planId: string) => {
    return Object.values(SAFEKEEP_PLANS).find(plan => plan.id === planId);
  },

  // Get all plans (legacy support)
  getAllPlans: () => {
    return SAFEKEEP_PLANS;
  },

  // Get available service combinations from backend
  getServiceCombinations: async () => {
    try {
      console.log('🔄 Fetching service combinations from backend...');

      const response = await fetch('http://localhost:3000/api/pricing/combinations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Service combinations fetched:', result.data?.length || 0, 'combinations');
      return result.data || [];
    } catch (error: unknown) {
      console.error('❌ Failed to fetch service combinations:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to fetch service combinations: ${String(error)}`);
      }
    }
  },

  // Calculate optimal pricing for service selection
  calculateOptimalPrice: async (serviceIds: string[]) => {
    try {
      console.log('🔄 Calculating optimal price for services:', serviceIds);

      const response = await fetch('http://localhost:3000/api/pricing/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ serviceIds }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Optimal pricing calculated:', result.data);
      return result.data;
    } catch (error: unknown) {
      console.error('❌ Failed to calculate optimal pricing:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to calculate optimal pricing: ${String(error)}`);
      }
    }
  },

  // Validate service combination
  validateServiceCombination: async (serviceIds: string[]) => {
    try {
      console.log('🔄 Validating service combination:', serviceIds);

      const response = await fetch('http://localhost:3000/api/services/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ serviceIds }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Service combination validated:', result.data);
      return result.data;
    } catch (error: unknown) {
      console.error('❌ Failed to validate service combination:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to validate service combination: ${String(error)}`);
      }
    }
  },

  // Create payment intent with service combination metadata
  createPaymentIntent: async (data: PaymentIntentData) => {
    try {
      console.log('🔄 Creating payment intent for modular services...', data);

      const response = await fetch('http://localhost:3000/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: data.amount,
          currency: data.currency,
          description: data.description,
          customerId: data.customerId,
          serviceIds: data.serviceIds,
          userId: data.userId,
          planId: data.planId,
          planName: data.planName,
          metadata: data.metadata,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const paymentIntent = await response.json();
      console.log('✅ Payment intent created for services:', paymentIntent.data?.payment_intent_id, data.serviceIds);
      return paymentIntent.data || paymentIntent;
    } catch (error: unknown) {
      console.error('❌ Failed to create payment intent:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to create payment intent: ${String(error)}`);
      }
    }
  },

  // Create modular subscription
  createModularSubscription: async (subscriptionRequest: ModularSubscriptionRequest) => {
    try {
      console.log('🔄 Creating modular subscription...', subscriptionRequest);

      const response = await fetch('http://localhost:3000/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscriptionRequest),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Modular subscription created:', result.data?.id);
      return result.data;
    } catch (error: unknown) {
      console.error('❌ Failed to create modular subscription:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to create modular subscription: ${String(error)}`);
      }
    }
  },

  // Get user's subscription details
  getUserSubscription: async (userId: string) => {
    try {
      console.log('🔄 Fetching user subscription details for:', userId);

      const response = await fetch(`http://localhost:3000/api/subscriptions/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ User subscription details fetched:', result.data?.subscriptionId);
      return result.data;
    } catch (error: unknown) {
      console.error('❌ Failed to fetch user subscription:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to fetch user subscription: ${String(error)}`);
      }
    }
  },

  // Check service access for user
  checkServiceAccess: async (userId: string, serviceType: string): Promise<ServiceAccessResult> => {
    try {
      console.log('🔄 Checking service access for user:', userId, 'service:', serviceType);

      const response = await fetch(`http://localhost:3000/api/services/access/${userId}/${serviceType}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Service access checked:', result.data);
      return result.data;
    } catch (error: unknown) {
      console.error('❌ Failed to check service access:', error);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to check service access: ${String(error)}`);
      }
    }
  },

  // Helper method to calculate individual service total
  calculateIndividualTotal: (serviceIds: string[]): number => {
    return serviceIds.reduce((total, serviceId) => {
      const service = Object.values(SAFEKEEP_SERVICES).find(s => s.id === serviceId);
      return total + (service?.price || 0);
    }, 0);
  },

  // Helper method to find best combination for services
  findBestCombination: (serviceIds: string[]) => {
    const individualTotal = StripeService.calculateIndividualTotal(serviceIds);
    let bestCombination = null;
    let maxSavings = 0;

    for (const combo of Object.values(SAFEKEEP_COMBINATIONS)) {
      // Check if combination covers all requested services exactly
      const coversAllServices = serviceIds.every(serviceId => combo.services.includes(serviceId));
      const hasExtraServices = combo.services.some(serviceId => !serviceIds.includes(serviceId));
      
      if (coversAllServices && !hasExtraServices) {
        const savings = individualTotal - combo.price;
        if (savings > maxSavings) {
          maxSavings = savings;
          bestCombination = combo;
        }
      }
    }

    return {
      combination: bestCombination,
      savings: maxSavings,
      individualTotal
    };
  },
};

// React Native Stripe initialization
export const initializeStripe = async () => {
  try {
    await initStripe({
      publishableKey: STRIPE_PUBLISHABLE_KEY,
      merchantIdentifier: 'merchant.com.safekeep.app', // For Apple Pay
      urlScheme: 'safekeep', // For redirects
    });
    console.log('✅ Stripe initialized successfully for React Native');
    return true;
  } catch (error: unknown) {
    console.error('❌ Failed to initialize Stripe:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Stripe initialization failed: ${errorMessage}`);
  }
};

// Export React Native Stripe components for frontend use
export { StripeProvider, useStripe, useConfirmPayment, initStripe };

// Export publishable key for frontend initialization
export const STRIPE_PUBLISHABLE_KEY_EXPORT = STRIPE_PUBLISHABLE_KEY;

// Export default service
export default StripeService;
