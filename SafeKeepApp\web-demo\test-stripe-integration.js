/**
 * Stripe Integration Test Suite for SafeKeep Web Demo
 * Tests all Stripe infrastructure components
 */

class StripeIntegrationTest {
    constructor() {
        this.stripeConfig = null;
        this.stripeManager = null;
        this.subscriptionManager = null;
        this.testResults = [];
        this.currentTest = null;
    }
    
    /**
     * Initialize test suite
     */
    async initialize() {
        try {
            console.log('🧪 Initializing Stripe Integration Test Suite...');
            
            // Initialize configuration
            this.stripeConfig = new StripeConfig();
            
            // Initialize Stripe manager
            this.stripeManager = new StripeManager(this.stripeConfig);
            await this.stripeManager.initialize();
            
            // Initialize subscription manager
            this.subscriptionManager = new SubscriptionManager(this.stripeManager);
            await this.subscriptionManager.initialize('test_user_123');
            
            console.log('✅ Test suite initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize test suite:', error);
            throw error;
        }
    }
    
    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting Stripe Integration Tests...');
        console.log('=' .repeat(60));
        
        this.testResults = [];
        
        // Configuration tests
        await this.testStripeConfiguration();
        await this.testSubscriptionTiers();
        
        // Customer management tests
        await this.testCustomerCreation();
        await this.testCustomerRetrieval();
        
        // Payment intent tests
        await this.testPaymentIntentCreation();
        await this.testPaymentIntentConfirmation();
        
        // Subscription tests
        await this.testSubscriptionCreation();
        await this.testSubscriptionUpdates();
        await this.testSubscriptionCancellation();
        
        // Payment method tests
        await this.testPaymentMethodManagement();
        
        // Webhook tests
        await this.testWebhookProcessing();
        
        // Server communication tests
        await this.testServerCommunication();
        
        // Integration tests
        await this.testFullPaymentFlow();
        await this.testSubscriptionUpgrade();
        
        this.printTestResults();
        return this.getTestSummary();
    }
    
    /**
     * Test Stripe configuration
     */
    async testStripeConfiguration() {
        this.currentTest = 'Stripe Configuration';
        console.log(`\n🔧 Testing ${this.currentTest}...`);
        
        try {
            const config = this.stripeConfig.getConfig();
            const validation = this.stripeConfig.validateConfig();
            const status = this.stripeConfig.getDemoStatus();
            
            this.assert(config.publishableKey, 'Publishable key should be configured');
            this.assert(config.secretKey, 'Secret key should be configured');
            this.assert(config.webhookSecret, 'Webhook secret should be configured');
            this.assert(validation.valid, 'Configuration should be valid');
            this.assert(status.configured, 'Demo status should show configured');
            
            console.log('  ✅ Configuration validation passed');
            console.log('  ✅ Demo status check passed');
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test subscription tiers
     */
    async testSubscriptionTiers() {
        this.currentTest = 'Subscription Tiers';
        console.log(`\n📋 Testing ${this.currentTest}...`);
        
        try {
            const tiers = this.stripeConfig.getSubscriptionTiers();
            
            this.assert(tiers.free, 'Free tier should exist');
            this.assert(tiers.basic, 'Basic tier should exist');
            this.assert(tiers.premium, 'Premium tier should exist');
            
            // Test tier properties
            this.assert(tiers.free.price === 0, 'Free tier should have zero price');
            this.assert(tiers.basic.price > 0, 'Basic tier should have positive price');
            this.assert(tiers.premium.price > tiers.basic.price, 'Premium should cost more than basic');
            
            // Test features
            this.assert(tiers.premium.features.prioritySupport, 'Premium should have priority support');
            this.assert(!tiers.free.features.prioritySupport, 'Free should not have priority support');
            
            console.log('  ✅ All subscription tiers configured correctly');
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test customer creation
     */
    async testCustomerCreation() {
        this.currentTest = 'Customer Creation';
        console.log(`\n👤 Testing ${this.currentTest}...`);
        
        try {
            const customerData = {
                email: '<EMAIL>',
                name: 'Test User',
                metadata: {
                    source: 'integration_test'
                }
            };
            
            const customer = await this.stripeManager.createCustomer(customerData);
            
            this.assert(customer.id, 'Customer should have an ID');
            this.assert(customer.email === customerData.email, 'Customer email should match');
            this.assert(customer.name === customerData.name, 'Customer name should match');
            this.assert(customer.created, 'Customer should have creation timestamp');
            
            console.log(`  ✅ Customer created: ${customer.id}`);
            
            // Store for later tests
            this.testCustomer = customer;
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test customer retrieval
     */
    async testCustomerRetrieval() {
        this.currentTest = 'Customer Retrieval';
        console.log(`\n🔍 Testing ${this.currentTest}...`);
        
        try {
            if (!this.testCustomer) {
                throw new Error('No test customer available');
            }
            
            const customer = await this.stripeManager.getCustomer(this.testCustomer.id);
            
            this.assert(customer.id === this.testCustomer.id, 'Retrieved customer ID should match');
            this.assert(customer.email === this.testCustomer.email, 'Retrieved customer email should match');
            
            console.log(`  ✅ Customer retrieved: ${customer.id}`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test payment intent creation
     */
    async testPaymentIntentCreation() {
        this.currentTest = 'Payment Intent Creation';
        console.log(`\n💳 Testing ${this.currentTest}...`);
        
        try {
            const paymentData = {
                amount: 999, // $9.99
                currency: 'usd',
                description: 'Test payment for SafeKeep Premium',
                metadata: {
                    test: 'integration_test'
                }
            };
            
            const paymentIntent = await this.stripeManager.createPaymentIntent(paymentData);
            
            this.assert(paymentIntent.id, 'Payment intent should have an ID');
            this.assert(paymentIntent.client_secret, 'Payment intent should have client secret');
            this.assert(paymentIntent.amount === paymentData.amount, 'Amount should match');
            this.assert(paymentIntent.currency === paymentData.currency, 'Currency should match');
            this.assert(paymentIntent.status === 'requires_payment_method', 'Status should be requires_payment_method');
            
            console.log(`  ✅ Payment intent created: ${paymentIntent.id}`);
            
            // Store for later tests
            this.testPaymentIntent = paymentIntent;
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test payment intent confirmation
     */
    async testPaymentIntentConfirmation() {
        this.currentTest = 'Payment Intent Confirmation';
        console.log(`\n✅ Testing ${this.currentTest}...`);
        
        try {
            if (!this.testPaymentIntent) {
                throw new Error('No test payment intent available');
            }
            
            const paymentMethodData = {
                card: {
                    number: '****************', // Test card
                    exp_month: 12,
                    exp_year: 2025,
                    cvc: '123'
                },
                amount: this.testPaymentIntent.amount
            };
            
            const result = await this.stripeManager.confirmPaymentIntent(
                this.testPaymentIntent.id,
                paymentMethodData
            );
            
            this.assert(result.paymentIntent, 'Result should contain payment intent');
            this.assert(result.paymentIntent.id === this.testPaymentIntent.id, 'Payment intent ID should match');
            
            if (result.paymentIntent.status === 'succeeded') {
                console.log(`  ✅ Payment confirmed successfully`);
            } else {
                console.log(`  ⚠️ Payment confirmation simulated (demo mode)`);
            }
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test subscription creation
     */
    async testSubscriptionCreation() {
        this.currentTest = 'Subscription Creation';
        console.log(`\n📋 Testing ${this.currentTest}...`);
        
        try {
            if (!this.testCustomer) {
                throw new Error('No test customer available');
            }
            
            const subscriptionData = {
                customerId: this.testCustomer.id,
                tierId: 'basic',
                metadata: {
                    test: 'integration_test'
                }
            };
            
            const subscription = await this.stripeManager.createSubscription(subscriptionData);
            
            this.assert(subscription.id, 'Subscription should have an ID');
            this.assert(subscription.customerId === this.testCustomer.id, 'Customer ID should match');
            this.assert(subscription.tierId === 'basic', 'Tier ID should match');
            this.assert(subscription.status === 'active', 'Subscription should be active');
            
            console.log(`  ✅ Subscription created: ${subscription.id}`);
            
            // Store for later tests
            this.testSubscription = subscription;
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test subscription updates
     */
    async testSubscriptionUpdates() {
        this.currentTest = 'Subscription Updates';
        console.log(`\n🔄 Testing ${this.currentTest}...`);
        
        try {
            if (!this.testSubscription) {
                throw new Error('No test subscription available');
            }
            
            const updateData = {
                tierId: 'premium',
                metadata: {
                    updated: 'true'
                }
            };
            
            const updatedSubscription = await this.stripeManager.updateSubscription(
                this.testSubscription.id,
                updateData
            );
            
            this.assert(updatedSubscription.id === this.testSubscription.id, 'Subscription ID should match');
            this.assert(updatedSubscription.tierId === 'premium', 'Tier should be updated to premium');
            
            console.log(`  ✅ Subscription updated to premium`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test subscription cancellation
     */
    async testSubscriptionCancellation() {
        this.currentTest = 'Subscription Cancellation';
        console.log(`\n❌ Testing ${this.currentTest}...`);
        
        try {
            if (!this.testSubscription) {
                throw new Error('No test subscription available');
            }
            
            const canceledSubscription = await this.stripeManager.cancelSubscription(
                this.testSubscription.id,
                true // cancel at period end
            );
            
            this.assert(canceledSubscription.id === this.testSubscription.id, 'Subscription ID should match');
            this.assert(canceledSubscription.cancel_at_period_end === true, 'Should be marked for cancellation');
            
            console.log(`  ✅ Subscription marked for cancellation`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test payment method management
     */
    async testPaymentMethodManagement() {
        this.currentTest = 'Payment Method Management';
        console.log(`\n💳 Testing ${this.currentTest}...`);
        
        try {
            if (!this.testCustomer) {
                throw new Error('No test customer available');
            }
            
            const paymentMethodData = {
                type: 'card',
                card: {
                    brand: 'visa',
                    last4: '4242',
                    exp_month: 12,
                    exp_year: 2025
                }
            };
            
            // Add payment method
            const paymentMethod = await this.stripeManager.addPaymentMethod(
                this.testCustomer.id,
                paymentMethodData
            );
            
            this.assert(paymentMethod.id, 'Payment method should have an ID');
            this.assert(paymentMethod.customerId === this.testCustomer.id, 'Customer ID should match');
            this.assert(paymentMethod.type === 'card', 'Type should be card');
            
            console.log(`  ✅ Payment method added: ${paymentMethod.id}`);
            
            // Get customer payment methods
            const paymentMethods = await this.stripeManager.getCustomerPaymentMethods(this.testCustomer.id);
            
            this.assert(Array.isArray(paymentMethods), 'Should return array of payment methods');
            this.assert(paymentMethods.length > 0, 'Should have at least one payment method');
            
            console.log(`  ✅ Retrieved ${paymentMethods.length} payment method(s)`);
            
            // Remove payment method
            const removeResult = await this.stripeManager.removePaymentMethod(paymentMethod.id);
            
            this.assert(removeResult.deleted === true, 'Payment method should be marked as deleted');
            
            console.log(`  ✅ Payment method removed`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test webhook processing
     */
    async testWebhookProcessing() {
        this.currentTest = 'Webhook Processing';
        console.log(`\n📨 Testing ${this.currentTest}...`);
        
        try {
            const testEvent = {
                type: 'customer.subscription.created',
                data: {
                    object: {
                        id: 'sub_test_123',
                        customer: 'cus_test_123',
                        status: 'active'
                    }
                }
            };
            
            const result = await this.stripeManager.processWebhook(testEvent);
            
            this.assert(result.received === true, 'Webhook should be marked as received');
            
            console.log(`  ✅ Webhook processed successfully`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test server communication
     */
    async testServerCommunication() {
        this.currentTest = 'Server Communication';
        console.log(`\n🌐 Testing ${this.currentTest}...`);
        
        try {
            // Test server status
            const status = await this.stripeManager.getServerStatus();
            
            this.assert(status.status === 'OK', 'Server should be running');
            this.assert(typeof status.stripe_configured === 'boolean', 'Should report Stripe configuration status');
            
            console.log(`  ✅ Server status check passed`);
            
            // Test webhook sending
            const testEvent = {
                type: 'payment_intent.succeeded',
                data: {
                    object: {
                        id: 'pi_test_123',
                        status: 'succeeded'
                    }
                }
            };
            
            const webhookResult = await this.stripeManager.sendWebhookToServer(testEvent);
            
            this.assert(webhookResult.received === true, 'Webhook should be received by server');
            
            console.log(`  ✅ Webhook communication test passed`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test full payment flow
     */
    async testFullPaymentFlow() {
        this.currentTest = 'Full Payment Flow';
        console.log(`\n🔄 Testing ${this.currentTest}...`);
        
        try {
            // Create customer
            const customer = await this.stripeManager.createCustomer({
                email: '<EMAIL>',
                name: 'Payment Test User'
            });
            
            // Create payment intent
            const paymentIntent = await this.stripeManager.createPaymentIntent({
                amount: 299, // $2.99 for basic plan
                currency: 'usd',
                description: 'SafeKeep Basic Subscription'
            });
            
            // Simulate payment confirmation
            const confirmResult = await this.stripeManager.confirmPaymentIntent(
                paymentIntent.id,
                {
                    card: {
                        number: '****************',
                        exp_month: 12,
                        exp_year: 2025,
                        cvc: '123'
                    },
                    amount: paymentIntent.amount
                }
            );
            
            // Create subscription after successful payment
            const subscription = await this.stripeManager.createSubscription({
                customerId: customer.id,
                tierId: 'basic'
            });
            
            this.assert(customer.id, 'Customer should be created');
            this.assert(paymentIntent.id, 'Payment intent should be created');
            this.assert(confirmResult.paymentIntent, 'Payment should be processed');
            this.assert(subscription.id, 'Subscription should be created');
            
            console.log(`  ✅ Full payment flow completed successfully`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Test subscription upgrade
     */
    async testSubscriptionUpgrade() {
        this.currentTest = 'Subscription Upgrade';
        console.log(`\n⬆️ Testing ${this.currentTest}...`);
        
        try {
            // Test subscription manager upgrade flow
            await this.subscriptionManager.selectPlan('premium');
            
            const currentSubscription = this.subscriptionManager.currentSubscription;
            
            this.assert(currentSubscription, 'Should have current subscription');
            
            console.log(`  ✅ Subscription upgrade flow tested`);
            
            this.recordTestResult(this.currentTest, true);
        } catch (error) {
            console.error(`  ❌ ${this.currentTest} failed:`, error.message);
            this.recordTestResult(this.currentTest, false, error.message);
        }
    }
    
    /**
     * Utility methods
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(message);
        }
    }
    
    recordTestResult(testName, passed, error = null) {
        this.testResults.push({
            name: testName,
            passed: passed,
            error: error,
            timestamp: new Date().toISOString()
        });
    }
    
    printTestResults() {
        console.log('\n' + '=' .repeat(60));
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('=' .repeat(60));
        
        const passed = this.testResults.filter(r => r.passed).length;
        const failed = this.testResults.filter(r => !r.passed).length;
        const total = this.testResults.length;
        
        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed} ✅`);
        console.log(`Failed: ${failed} ❌`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        
        if (failed > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(test => {
                    console.log(`  • ${test.name}: ${test.error}`);
                });
        }
        
        console.log('\n✅ PASSED TESTS:');
        this.testResults
            .filter(r => r.passed)
            .forEach(test => {
                console.log(`  • ${test.name}`);
            });
    }
    
    getTestSummary() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        return {
            total: total,
            passed: passed,
            failed: total - passed,
            successRate: (passed / total) * 100,
            results: this.testResults
        };
    }
    
    /**
     * Reset test data
     */
    resetTestData() {
        this.testResults = [];
        this.testCustomer = null;
        this.testPaymentIntent = null;
        this.testSubscription = null;
        
        // Reset demo data in managers
        if (this.stripeManager) {
            this.stripeManager.resetDemoData();
        }
        
        console.log('🔄 Test data reset');
    }
}

// Export for use in web demo
if (typeof window !== 'undefined') {
    window.StripeIntegrationTest = StripeIntegrationTest;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StripeIntegrationTest;
}