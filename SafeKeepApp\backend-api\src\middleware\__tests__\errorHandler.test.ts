import { Request, Response, NextFunction } from 'express';
import {
  errorHandler,
  ValidationError,
  ServiceCombinationError,
  StripeError,
  DatabaseError,
  BusinessLogicError,
  handleValidationError,
  handleStripeError,
  handleDatabaseError,
  handleBusinessLogicError,
  withRetry,
  asyncHandler,
  validateRequest,
  validateServiceCombination,
  notFoundHandler
} from '../errorHandler';

describe('Error Handler Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      method: 'POST',
      path: '/api/test',
      user: { id: 'user-123', email: '<EMAIL>' }
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();
    
    // Mock console.error to avoid noise in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('Custom Error Classes', () => {
    describe('ValidationError', () => {
      it('should create validation error with details', () => {
        const details = ['Field is required', 'Invalid format'];
        const error = new ValidationError('Validation failed', details);
        
        expect(error.name).toBe('ValidationError');
        expect(error.message).toBe('Validation failed');
        expect(error.details).toEqual(details);
      });
    });

    describe('ServiceCombinationError', () => {
      it('should create service combination error', () => {
        const details = ['Invalid service ID'];
        const error = new ServiceCombinationError('Invalid combination', details);
        
        expect(error.name).toBe('ServiceCombinationError');
        expect(error.message).toBe('Invalid combination');
        expect(error.details).toEqual(details);
      });
    });

    describe('StripeError', () => {
      it('should create stripe error with card error details', () => {
        const stripeError = {
          type: 'card_error',
          code: 'card_declined',
          decline_code: 'generic_decline',
          message: 'Your card was declined'
        };
        
        const error = new StripeError('Payment failed', stripeError);
        
        expect(error.name).toBe('StripeError');
        expect(error.message).toBe('Payment failed');
        expect(error.stripeType).toBe('card_error');
        expect(error.stripeCode).toBe('card_declined');
        expect(error.declineCode).toBe('generic_decline');
        expect(error.statusCode).toBe(402);
      });

      it('should handle different stripe error types with correct status codes', () => {
        const testCases = [
          { type: 'card_error', expectedStatus: 402 },
          { type: 'rate_limit_error', expectedStatus: 429 },
          { type: 'invalid_request_error', expectedStatus: 400 },
          { type: 'authentication_error', expectedStatus: 401 },
          { type: 'api_connection_error', expectedStatus: 502 },
          { type: 'api_error', expectedStatus: 502 }
        ];

        testCases.forEach(({ type, expectedStatus }) => {
          const stripeError = { type, message: 'Test error' };
          const error = new StripeError('Test', stripeError);
          expect(error.statusCode).toBe(expectedStatus);
        });
      });
    });

    describe('DatabaseError', () => {
      it('should create database error', () => {
        const error = new DatabaseError('Connection failed');
        
        expect(error.name).toBe('DatabaseError');
        expect(error.message).toBe('Connection failed');
        expect(error.statusCode).toBe(500);
      });
    });

    describe('BusinessLogicError', () => {
      it('should create business logic error with custom status code', () => {
        const error = new BusinessLogicError('Business rule violated', 422);
        
        expect(error.name).toBe('BusinessLogicError');
        expect(error.message).toBe('Business rule violated');
        expect(error.statusCode).toBe(422);
      });
    });
  });

  describe('errorHandler middleware', () => {
    it('should handle ValidationError correctly', () => {
      const error = new ValidationError('Validation failed', ['Field required']);
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: ['Field required'],
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle ServiceCombinationError correctly', () => {
      const error = new ServiceCombinationError('Invalid combination', ['Invalid service']);
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid combination',
          details: ['Invalid service'],
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle StripeError correctly', () => {
      const stripeError = { type: 'card_error', code: 'card_declined' };
      const error = new StripeError('Card declined', stripeError);
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(402);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'STRIPE_ERROR',
          message: 'Card declined',
          details: {
            type: 'card_error',
            code: 'card_declined',
            decline_code: undefined
          },
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle DatabaseError correctly', () => {
      const error = new DatabaseError('Connection failed');
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Database operation failed',
          details: undefined,
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle BusinessLogicError correctly', () => {
      const error = new BusinessLogicError('Rule violation', 422);
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(422);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'BUSINESS_LOGIC_ERROR',
          message: 'Rule violation',
          details: undefined,
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle generic errors correctly', () => {
      const error = new Error('Generic error');
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          details: undefined,
          timestamp: expect.any(String)
        }
      });
    });

    it('should handle connection errors correctly', () => {
      const error = new Error('Connection failed') as any;
      error.code = 'ECONNREFUSED';
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(503);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'SERVICE_UNAVAILABLE',
          message: 'External service unavailable',
          details: undefined,
          timestamp: expect.any(String)
        }
      });
    });

    it('should log error details', () => {
      const error = new Error('Test error');
      
      errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(console.error).toHaveBeenCalledWith(
        'Error in POST /api/test:',
        expect.objectContaining({
          message: 'Test error',
          user: 'user-123',
          timestamp: expect.any(String)
        })
      );
    });
  });

  describe('Error handler functions', () => {
    describe('handleValidationError', () => {
      it('should throw ServiceCombinationError', () => {
        const errors = ['Invalid service'];
        
        expect(() => handleValidationError(errors)).toThrow(ServiceCombinationError);
        expect(() => handleValidationError(errors)).toThrow('Invalid service combination');
      });
    });

    describe('handleStripeError', () => {
      it('should throw StripeError with card error message', () => {
        const stripeError = {
          type: 'card_error',
          message: 'Your card was declined'
        };
        
        expect(() => handleStripeError(stripeError)).toThrow(StripeError);
        expect(() => handleStripeError(stripeError)).toThrow('Your card was declined');
      });

      it('should handle different stripe error types with appropriate messages', () => {
        const testCases = [
          { type: 'rate_limit_error', expectedMessage: 'Too many requests. Please try again later' },
          { type: 'invalid_request_error', expectedMessage: 'Invalid payment request' },
          { type: 'authentication_error', expectedMessage: 'Payment authentication failed' },
          { type: 'api_connection_error', expectedMessage: 'Payment service temporarily unavailable' },
          { type: 'api_error', expectedMessage: 'Payment processing error' }
        ];

        testCases.forEach(({ type, expectedMessage }) => {
          const stripeError = { type };
          expect(() => handleStripeError(stripeError)).toThrow(expectedMessage);
        });
      });
    });

    describe('handleDatabaseError', () => {
      it('should throw DatabaseError', () => {
        const originalError = new Error('Connection failed');
        
        expect(() => handleDatabaseError(originalError, 'query')).toThrow(DatabaseError);
        expect(() => handleDatabaseError(originalError, 'query')).toThrow('Database query failed');
      });
    });

    describe('handleBusinessLogicError', () => {
      it('should throw BusinessLogicError', () => {
        expect(() => handleBusinessLogicError('Rule violated', 422)).toThrow(BusinessLogicError);
        expect(() => handleBusinessLogicError('Rule violated', 422)).toThrow('Rule violated');
      });
    });
  });

  describe('withRetry', () => {
    it('should succeed on first attempt', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      
      const result = await withRetry(operation);
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValue('success');
      
      const result = await withRetry(operation, 3, 10); // Short delay for testing
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should not retry validation errors', async () => {
      const operation = jest.fn().mockRejectedValue(new ValidationError('Invalid input'));
      
      await expect(withRetry(operation)).rejects.toThrow(ValidationError);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should not retry card errors', async () => {
      const stripeError = { type: 'card_error' };
      const operation = jest.fn().mockRejectedValue(new StripeError('Card declined', stripeError));
      
      await expect(withRetry(operation)).rejects.toThrow(StripeError);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should exhaust retries and throw last error', async () => {
      const error = new Error('Persistent failure');
      const operation = jest.fn().mockRejectedValue(error);
      
      await expect(withRetry(operation, 2, 10)).rejects.toThrow('Persistent failure');
      expect(operation).toHaveBeenCalledTimes(2);
    });
  });

  describe('asyncHandler', () => {
    it('should handle successful async function', async () => {
      const asyncFn = jest.fn().mockResolvedValue('success');
      const handler = asyncHandler(asyncFn);
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(asyncFn).toHaveBeenCalledWith(mockRequest, mockResponse, mockNext);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should catch async errors and pass to next', async () => {
      const error = new Error('Async error');
      const asyncFn = jest.fn().mockRejectedValue(error);
      const handler = asyncHandler(asyncFn);
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('validateRequest', () => {
    it('should pass validation for valid request', () => {
      const schema = {
        validate: jest.fn().mockReturnValue({ error: null })
      };
      const middleware = validateRequest(schema);
      
      mockRequest.body = { valid: 'data' };
      
      middleware(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(schema.validate).toHaveBeenCalledWith({ valid: 'data' }, { abortEarly: false });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should throw ValidationError for invalid request', () => {
      const schema = {
        validate: jest.fn().mockReturnValue({
          error: {
            details: [
              { message: 'Field is required' },
              { message: 'Invalid format' }
            ]
          }
        })
      };
      const middleware = validateRequest(schema);
      
      mockRequest.body = { invalid: 'data' };
      
      expect(() => middleware(mockRequest as Request, mockResponse as Response, mockNext))
        .toThrow(ValidationError);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('validateServiceCombination', () => {
    it('should pass validation for valid service combination', () => {
      expect(() => validateServiceCombination(['contacts', 'messages'])).not.toThrow();
    });

    it('should throw error for empty service array', () => {
      expect(() => validateServiceCombination([])).toThrow(ServiceCombinationError);
      
      try {
        validateServiceCombination([]);
      } catch (error: any) {
        expect(error.details).toContain('At least one service must be selected');
      }
    });

    it('should throw error for duplicate services', () => {
      expect(() => validateServiceCombination(['contacts', 'contacts'])).toThrow(ServiceCombinationError);
      
      try {
        validateServiceCombination(['contacts', 'contacts']);
      } catch (error: any) {
        expect(error.details).toContain('Duplicate services are not allowed');
      }
    });

    it('should throw error for invalid service IDs', () => {
      expect(() => validateServiceCombination(['invalid', 'contacts'])).toThrow(ServiceCombinationError);
      
      try {
        validateServiceCombination(['invalid', 'contacts']);
      } catch (error: any) {
        expect(error.details).toContain('Invalid service IDs: invalid');
      }
    });

    it('should throw error with multiple validation issues', () => {
      expect(() => validateServiceCombination(['invalid', 'contacts', 'contacts'])).toThrow(ServiceCombinationError);
    });
  });

  describe('notFoundHandler', () => {
    it('should return 404 response for undefined routes', () => {
      (mockRequest as any).method = 'GET';
      (mockRequest as any).path = '/api/nonexistent';
      
      notFoundHandler(mockRequest as Request, mockResponse as Response);
      
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'ROUTE_NOT_FOUND',
          message: 'Route GET /api/nonexistent not found',
          timestamp: expect.any(String)
        }
      });
    });
  });
});