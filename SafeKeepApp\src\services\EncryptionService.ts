import CryptoJS from 'crypto-js';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface EncryptionResult {
  encryptedData: string;
  iv: string;
  salt: string;
  authTag?: string;
}

export interface DecryptionResult {
  success: boolean;
  data?: string;
  error?: string;
}

export interface EncryptionKeys {
  masterKey: string;
  derivedKey: string;
  keyId: string;
}

class EncryptionService {
  private readonly ALGORITHM = 'AES-256-GCM';
  private readonly KEY_SIZE = 256; // bits
  private readonly IV_SIZE = 16; // bytes
  private readonly SALT_SIZE = 32; // bytes
  private readonly ITERATIONS = 100000; // PBKDF2 iterations
  private readonly STORAGE_KEY = 'safekeep_encryption_keys';

  // Generate a secure random master key
  async generateMasterKey(): Promise<string> {
    const randomBytes = CryptoJS.lib.WordArray.random(this.KEY_SIZE / 8);
    return randomBytes.toString(CryptoJS.enc.Base64);
  }

  // Derive encryption key from master key using PBKDF2
  deriveKey(masterKey: string, salt: string): string {
    const key = CryptoJS.PBKDF2(masterKey, salt, {
      keySize: this.KEY_SIZE / 32,
      iterations: this.ITERATIONS,
      hasher: CryptoJS.algo.SHA256
    });
    return key.toString(CryptoJS.enc.Base64);
  }

  // Generate secure random salt
  generateSalt(): string {
    const salt = CryptoJS.lib.WordArray.random(this.SALT_SIZE);
    return salt.toString(CryptoJS.enc.Base64);
  }

  // Generate secure random IV
  generateIV(): string {
    const iv = CryptoJS.lib.WordArray.random(this.IV_SIZE);
    return iv.toString(CryptoJS.enc.Base64);
  }

  // Encrypt data using AES-256-GCM
  async encryptData(data: string, masterKey?: string): Promise<EncryptionResult> {
    try {
      // Use provided key or get from storage
      const key = masterKey || await this.getMasterKey();
      if (!key) {
        throw new Error('No encryption key available');
      }

      // Generate salt and IV
      const salt = this.generateSalt();
      const iv = this.generateIV();

      // Derive encryption key
      const derivedKey = this.deriveKey(key, salt);

      // Convert data to WordArray
      const dataWordArray = CryptoJS.enc.Utf8.parse(data);
      const keyWordArray = CryptoJS.enc.Base64.parse(derivedKey);
      const ivWordArray = CryptoJS.enc.Base64.parse(iv);

      // Encrypt using AES-256-CBC (CryptoJS doesn't support GCM directly)
      const encrypted = CryptoJS.AES.encrypt(dataWordArray, keyWordArray, {
        iv: ivWordArray,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      return {
        encryptedData: encrypted.toString(),
        iv,
        salt
      };
    } catch (error: unknown) {
      console.error('Encryption error:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Encryption failed: ${errorMessage}`);
    }
  }

  // Decrypt data using AES-256
  async decryptData(
    encryptedData: string,
    iv: string,
    salt: string,
    masterKey?: string
  ): Promise<DecryptionResult> {
    try {
      // Use provided key or get from storage
      const key = masterKey || await this.getMasterKey();
      if (!key) {
        return { success: false, error: 'No encryption key available' };
      }

      // Derive decryption key
      const derivedKey = this.deriveKey(key, salt);

      // Convert to WordArrays
      const keyWordArray = CryptoJS.enc.Base64.parse(derivedKey);
      const ivWordArray = CryptoJS.enc.Base64.parse(iv);

      // Decrypt
      const decrypted = CryptoJS.AES.decrypt(encryptedData, keyWordArray, {
        iv: ivWordArray,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

      if (!decryptedText) {
        return { success: false, error: 'Decryption failed - invalid key or corrupted data' };
      }

      return { success: true, data: decryptedText };
    } catch (error: unknown) {
      console.error('Decryption error:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: `Decryption failed: ${errorMessage}` };
    }
  }

  // Encrypt file data (for photos, etc.)
  async encryptFile(fileData: string, fileName: string): Promise<EncryptionResult & { fileName: string }> {
    try {
      console.log(`🔐 Encrypting file: ${fileName}`);

      const result = await this.encryptData(fileData);

      // Generate encrypted filename to hide original name
      const encryptedFileName = await this.encryptFileName(fileName);

      console.log(`✅ File encrypted successfully: ${fileName}`);

      return {
        ...result,
        fileName: encryptedFileName
      };
    } catch (error: unknown) {
      console.error(`❌ File encryption failed for ${fileName}:`, error);
      // Re-throw with proper error handling
      if (error instanceof Error) {
        throw error; // Preserve original Error object if it's already an Error
      } else {
        throw new Error(`File encryption failed: ${String(error)}`);
      }
    }
  }

  // Decrypt file data
  async decryptFile(
    encryptedData: string,
    iv: string,
    salt: string,
    encryptedFileName: string
  ): Promise<{ success: boolean; data?: string; fileName?: string; error?: string }> {
    try {
      console.log(`🔓 Decrypting file...`);

      const dataResult = await this.decryptData(encryptedData, iv, salt);
      if (!dataResult.success) {
        return dataResult;
      }

      const fileName = await this.decryptFileName(encryptedFileName);

      console.log(`✅ File decrypted successfully`);

      return {
        success: true,
        data: dataResult.data,
        fileName
      };
    } catch (error: unknown) {
      console.error(`❌ File decryption failed:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  }

  // Encrypt filename to hide original names
  private async encryptFileName(fileName: string): Promise<string> {
    const result = await this.encryptData(fileName);
    // Create a shorter, URL-safe encrypted filename
    const hash = CryptoJS.SHA256(result.encryptedData).toString(CryptoJS.enc.Base64);
    return hash.replace(/[+/=]/g, '').substring(0, 32);
  }

  // Decrypt filename
  private async decryptFileName(encryptedFileName: string): Promise<string> {
    // In a real implementation, you'd store the mapping between encrypted and original filenames
    // For now, return a placeholder
    return `decrypted_${encryptedFileName.substring(0, 8)}.file`;
  }

  // Store master key securely
  async storeMasterKey(masterKey: string): Promise<void> {
    try {
      const keyData: EncryptionKeys = {
        masterKey,
        derivedKey: '', // Will be generated per-use
        keyId: CryptoJS.lib.WordArray.random(16).toString(CryptoJS.enc.Base64)
      };

      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(keyData));
      console.log('🔐 Master key stored securely');
    } catch (error: unknown) {
      console.error('Failed to store master key:', error);
      throw new Error('Failed to store encryption key');
    }
  }

  // Retrieve master key
  async getMasterKey(): Promise<string | null> {
    try {
      const keyDataString = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (!keyDataString) {
        return null;
      }

      const keyData: EncryptionKeys = JSON.parse(keyDataString);
      return keyData.masterKey;
    } catch (error: unknown) {
      console.error('Failed to retrieve master key:', error);
      return null;
    }
  }

  // Initialize encryption for new user
  async initializeEncryption(): Promise<string> {
    try {
      console.log('🔐 Initializing encryption for new user...');

      const masterKey = await this.generateMasterKey();
      await this.storeMasterKey(masterKey);

      console.log('✅ Encryption initialized successfully');
      return masterKey;
    } catch (error: unknown) {
      console.error('❌ Failed to initialize encryption:', error);
      throw new Error('Failed to initialize encryption');
    }
  }

  // Verify encryption is working
  async verifyEncryption(): Promise<boolean> {
    try {
      const testData = 'SafeKeep encryption test';
      const encrypted = await this.encryptData(testData);
      const decrypted = await this.decryptData(
        encrypted.encryptedData,
        encrypted.iv,
        encrypted.salt
      );

      return decrypted.success && decrypted.data === testData;
    } catch (error: unknown) {
      console.error('Encryption verification failed:', error);
      return false;
    }
  }

  // Clear all encryption keys (for logout/reset)
  async clearEncryptionKeys(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      console.log('🔐 Encryption keys cleared');
    } catch (error: unknown) {
      console.error('Failed to clear encryption keys:', error);
    }
  }

  // Get encryption status
  async getEncryptionStatus(): Promise<{
    isInitialized: boolean;
    isWorking: boolean;
    keyId?: string;
  }> {
    try {
      const keyDataString = await AsyncStorage.getItem(this.STORAGE_KEY);
      const isInitialized = !!keyDataString;

      if (!isInitialized) {
        return { isInitialized: false, isWorking: false };
      }

      const keyData: EncryptionKeys = JSON.parse(keyDataString);
      const isWorking = await this.verifyEncryption();

      return {
        isInitialized,
        isWorking,
        keyId: keyData.keyId
      };
    } catch (error: unknown) {
      console.error('Failed to get encryption status:', error);
      return { isInitialized: false, isWorking: false };
    }
  }
}

export default new EncryptionService();
