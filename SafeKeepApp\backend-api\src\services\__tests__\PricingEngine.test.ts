import { PricingEngine } from '../PricingEngine';

// Mock the database module
jest.mock('../../utils/database', () => ({
  supabase: {
    rpc: jest.fn()
  }
}));

// Import after mocking
const { supabase } = require('../../utils/database');
const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('PricingEngine', () => {
  let pricingEngine: PricingEngine;

  beforeEach(() => {
    pricingEngine = new PricingEngine();
    jest.clearAllMocks();
  });

  describe('calculateOptimalPrice', () => {
    it('should calculate optimal price for valid service IDs', async () => {
      const mockResult = [{
        recommended_plan_id: 'plan_contacts_messages',
        recommended_plan_name: 'Contacts + Messages',
        price_cents: 1999,
        savings_cents: 500,
        individual_total_cents: 2499
      }];

      mockSupabase.rpc.mockResolvedValue({ data: mockResult, error: null });

      const result = await pricingEngine.calculateOptimalPrice(['contacts', 'messages']);

      expect(mockSupabase.rpc).toHaveBeenCalledWith('calculate_optimal_price', {
        service_ids: ['contacts', 'messages']
      });

      expect(result).toEqual({
        recommendedPlanId: 'plan_contacts_messages',
        recommendedPlanName: 'Contacts + Messages',
        priceCents: 1999,
        savingsCents: 500,
        individualTotalCents: 2499
      });
    });

    it('should throw error for empty service IDs', async () => {
      await expect(pricingEngine.calculateOptimalPrice([])).rejects.toThrow('Service IDs are required');
    });

    it('should throw error for null service IDs', async () => {
      await expect(pricingEngine.calculateOptimalPrice(null as any)).rejects.toThrow('Service IDs are required');
    });

    it('should handle database errors', async () => {
      mockSupabase.rpc.mockResolvedValue({ 
        data: null, 
        error: { message: 'Database connection failed', details: '', hint: '', code: '500' } 
      });

      await expect(pricingEngine.calculateOptimalPrice(['contacts']))
        .rejects.toThrow('Failed to calculate optimal price: Database connection failed');
    });

    it('should handle empty database response', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: [], error: null });

      await expect(pricingEngine.calculateOptimalPrice(['contacts']))
        .rejects.toThrow('No pricing data found for the selected services');
    });

    it('should handle single service calculation', async () => {
      const mockResult = [{
        recommended_plan_id: 'plan_contacts_only',
        recommended_plan_name: 'Contacts Only',
        price_cents: 999,
        savings_cents: 0,
        individual_total_cents: 999
      }];

      mockSupabase.rpc.mockResolvedValue({ data: mockResult, error: null });

      const result = await pricingEngine.calculateOptimalPrice(['contacts']);

      expect(result).toEqual({
        recommendedPlanId: 'plan_contacts_only',
        recommendedPlanName: 'Contacts Only',
        priceCents: 999,
        savingsCents: 0,
        individualTotalCents: 999
      });
    });

    it('should handle all services calculation', async () => {
      const mockResult = [{
        recommended_plan_id: 'plan_complete',
        recommended_plan_name: 'Complete Backup',
        price_cents: 2999,
        savings_cents: 1000,
        individual_total_cents: 3999
      }];

      mockSupabase.rpc.mockResolvedValue({ data: mockResult, error: null });

      const result = await pricingEngine.calculateOptimalPrice(['contacts', 'messages', 'photos']);

      expect(result).toEqual({
        recommendedPlanId: 'plan_complete',
        recommendedPlanName: 'Complete Backup',
        priceCents: 2999,
        savingsCents: 1000,
        individualTotalCents: 3999
      });
    });
  });

  describe('getAvailableServiceCombinations', () => {
    it('should return available service combinations', async () => {
      const mockData = [
        {
          plan_id: 'plan_contacts_only',
          plan_name: 'Contacts Only',
          price_cents: 999,
          services: ['contacts'],
          storage_gb: 5,
          is_popular: false
        },
        {
          plan_id: 'plan_complete',
          plan_name: 'Complete Backup',
          price_cents: 2999,
          services: ['contacts', 'messages', 'photos'],
          storage_gb: 100,
          is_popular: true
        }
      ];

      mockSupabase.rpc.mockResolvedValue({ data: mockData, error: null });

      const result = await pricingEngine.getAvailableServiceCombinations();

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_available_service_combinations');

      expect(result).toEqual([
        {
          planId: 'plan_contacts_only',
          planName: 'Contacts Only',
          priceCents: 999,
          services: ['contacts'],
          storageGb: 5,
          isPopular: false
        },
        {
          planId: 'plan_complete',
          planName: 'Complete Backup',
          priceCents: 2999,
          services: ['contacts', 'messages', 'photos'],
          storageGb: 100,
          isPopular: true
        }
      ]);
    });

    it('should return empty array when no data available', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: null, error: null });

      const result = await pricingEngine.getAvailableServiceCombinations();

      expect(result).toEqual([]);
    });

    it('should handle database errors', async () => {
      mockSupabase.rpc.mockResolvedValue({ 
        data: null, 
        error: { message: 'Database connection failed', details: '', hint: '', code: '500' } 
      });

      await expect(pricingEngine.getAvailableServiceCombinations())
        .rejects.toThrow('Failed to get service combinations: Database connection failed');
    });

    it('should handle missing optional fields', async () => {
      const mockData = [
        {
          plan_id: 'plan_basic',
          plan_name: 'Basic Plan',
          price_cents: 1499,
          services: null,
          storage_gb: null,
          is_popular: null
        }
      ];

      mockSupabase.rpc.mockResolvedValue({ data: mockData, error: null });

      const result = await pricingEngine.getAvailableServiceCombinations();

      expect(result).toEqual([
        {
          planId: 'plan_basic',
          planName: 'Basic Plan',
          priceCents: 1499,
          services: [],
          storageGb: 0,
          isPopular: false
        }
      ]);
    });
  });

  describe('getPlanRecommendations', () => {
    it('should return plan recommendations for valid user ID', async () => {
      const mockData = [
        {
          recommendation_type: 'upgrade',
          plan_id: 'plan_premium',
          plan_name: 'Premium Plan',
          price_cents: 2999,
          price_difference_cents: 1000,
          reason: 'More features and storage'
        },
        {
          recommendation_type: 'downgrade',
          plan_id: 'plan_basic',
          plan_name: 'Basic Plan',
          price_cents: 999,
          price_difference_cents: -1000,
          reason: 'Save money with essential features'
        }
      ];

      mockSupabase.rpc.mockResolvedValue({ data: mockData, error: null });

      const result = await pricingEngine.getPlanRecommendations('user123');

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_plan_recommendations', {
        user_uuid: 'user123'
      });

      expect(result).toEqual([
        {
          planId: 'plan_premium',
          planName: 'Premium Plan',
          priceCents: 2999,
          reason: 'More features and storage',
          savingsCents: 1000
        },
        {
          planId: 'plan_basic',
          planName: 'Basic Plan',
          priceCents: 999,
          reason: 'Save money with essential features',
          savingsCents: 1000
        }
      ]);
    });

    it('should throw error for empty user ID', async () => {
      await expect(pricingEngine.getPlanRecommendations('')).rejects.toThrow('User ID is required');
    });

    it('should throw error for null user ID', async () => {
      await expect(pricingEngine.getPlanRecommendations(null as any)).rejects.toThrow('User ID is required');
    });

    it('should handle database errors', async () => {
      mockSupabase.rpc.mockResolvedValue({ 
        data: null, 
        error: { message: 'Database connection failed', details: '', hint: '', code: '500' } 
      });

      await expect(pricingEngine.getPlanRecommendations('user123'))
        .rejects.toThrow('Failed to get plan recommendations: Database connection failed');
    });

    it('should return empty array when no recommendations available', async () => {
      mockSupabase.rpc.mockResolvedValue({ data: null, error: null });

      const result = await pricingEngine.getPlanRecommendations('user123');

      expect(result).toEqual([]);
    });

    it('should handle missing optional fields', async () => {
      const mockData = [
        {
          recommendation_type: 'upgrade',
          plan_id: 'plan_premium',
          plan_name: 'Premium Plan',
          price_cents: 2999,
          price_difference_cents: null,
          reason: null
        }
      ];

      mockSupabase.rpc.mockResolvedValue({ data: mockData, error: null });

      const result = await pricingEngine.getPlanRecommendations('user123');

      expect(result).toEqual([
        {
          planId: 'plan_premium',
          planName: 'Premium Plan',
          priceCents: 2999,
          reason: '',
          savingsCents: 0
        }
      ]);
    });

    it('should handle upgrade recommendations', async () => {
      const mockData = [
        {
          recommendation_type: 'upgrade',
          plan_id: 'plan_complete',
          plan_name: 'Complete Backup',
          price_cents: 3999,
          price_difference_cents: 2000,
          reason: 'Unlock all features with unlimited storage'
        }
      ];

      mockSupabase.rpc.mockResolvedValue({ data: mockData, error: null });

      const result = await pricingEngine.getPlanRecommendations('user123');

      expect(result).toEqual([
        {
          planId: 'plan_complete',
          planName: 'Complete Backup',
          priceCents: 3999,
          reason: 'Unlock all features with unlimited storage',
          savingsCents: 2000
        }
      ]);
    });

    it('should handle downgrade recommendations', async () => {
      const mockData = [
        {
          recommendation_type: 'downgrade',
          plan_id: 'plan_starter',
          plan_name: 'Starter Plan',
          price_cents: 499,
          price_difference_cents: -1500,
          reason: 'Perfect for light usage'
        }
      ];

      mockSupabase.rpc.mockResolvedValue({ data: mockData, error: null });

      const result = await pricingEngine.getPlanRecommendations('user123');

      expect(result).toEqual([
        {
          planId: 'plan_starter',
          planName: 'Starter Plan',
          priceCents: 499,
          reason: 'Perfect for light usage',
          savingsCents: 1500
        }
      ]);
    });
  });
});